variables:
  # Tells git to fetch all the branches of the project, required by the
  # analysis task
  GIT_DEPTH: "0"
  # See
  # https://gitlab.com/gitlab-org/gitlab-ci-yml/blob/7cf61ae97efa37106c45f1a7ab2ffd8b6a366f36/Maven.gitlab-ci.yml
  # This will supress any download for dependencies and plugins or upload
  # messages which would clutter the console log. `showDateTime` will show the
  # passed time in milliseconds. You need to specify `--batch-mode` to make
  # this work.
  # yamllint disable-line rule:line-length
  MAVEN_OPTS: "-Dhttps.protocols=TLSv1.2 -Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=WARN -Dorg.slf4j.simpleLogger.showDateTime=true -Djava.awt.headless=true"
  MAVEN_CLI_OPTS: "--batch-mode --errors --fail-at-end --show-version"
  # TODO: Unused ?
  # yamllint disable-line rule:line-length
  DOCKER_HOST: tcp://docker:2376
  # kaniko default variables
  CONTEXT_PATH: ${CI_PROJECT_DIR}/
  DOCKERFILE: ./Dockerfile
  IMAGE_TAG: ${CI_COMMIT_REF_NAME}
  CI_REGISTRY: docker.io
  CI_REGISTRY_IMAGE: index.docker.io/erhgo
  CI_REGISTRY_USER: ${DOCKERHUB_USERNAME}
  CI_REGISTRY_PASSWORD: ${DOCKERHUB_PASSWORD}

### common
stages:
  - pre-build
  - build
  - dockerize
  - push
  - deploy
  - verify
  - test

include:
  - local: "api/.gitlab-ci.yml"

.getDockerSourceTag: &getDockerSourceTag |
  # We are looking for the docker tag of the image we want to deploy. If we
  # just merged into master, we actually tagged it using the hash of the tip of
  # the merge request. Thankfully we can find it: it's the second parent of the
  # current HEAD (which should be a merge commit). Just in case we're not on a
  # merge commit (for an emergency rollback to a previous version for exemple),
  # we fallback to the hash of the current HEAD.
  export DOCKER_SOURCE_TAG="$CI_REGISTRY_IMAGE/$APP_NAME:$(git log --pretty="%H" -n 1 HEAD^2 2>/dev/null || git log --pretty="%H" -n 1 HEAD)"

.getCurrentVersion: &getCurrentVersion |
  export CURRENT_VERSION="$(git log --pretty="%H" -n 1 HEAD^2 2>/dev/null || git log --pretty="%H" -n 1 HEAD)"

.checkVersion:
  stage: verify
  interruptible: true
  image: alpine
  when: delayed
  start_in: 5 minutes
  before_script:
    - apk add --no-cache bash git openssh curl
  script:
    - *getCurrentVersion

.tag: &tag
  stage: dockerize
  interruptible: true
  image: "docker:stable-git"
  tags:
    - docker
  services:
    - docker:dind
  script:
    - docker login -u "${DOCKERHUB_USERNAME}" -p "${DOCKERHUB_PASSWORD}"
    - *getDockerSourceTag
    - docker pull "$DOCKER_SOURCE_TAG"
    - docker tag "$DOCKER_SOURCE_TAG" "$CI_REGISTRY_IMAGE/$APP_NAME:$IMAGE_TAG"
    - docker push "$CI_REGISTRY_IMAGE/$APP_NAME:$IMAGE_TAG"

# Build and push docker image with BuildKit
.build: &build
  stage: build
  interruptible: true
  image: "docker:stable"
  tags:
    - docker
  services:
    - docker:dind
  before_script:
    - docker login -u "${DOCKERHUB_USERNAME}" -p "${DOCKERHUB_PASSWORD}"
  script:
    - docker pull ${CI_REGISTRY_IMAGE}/${APP_NAME}:testing || true
    - >
      docker build
      --cache-from ${CI_REGISTRY_IMAGE}/${APP_NAME}:testing
      --build-arg APP=${APP_NAME}
      --build-arg VERSION=$CI_COMMIT_SHA
      --file ${DOCKERFILE}
      --tag ${CI_REGISTRY_IMAGE}/${APP_NAME}:$CI_COMMIT_SHA
      --tag ${CI_REGISTRY_IMAGE}/${APP_NAME}:$IMAGE_TAG
      $CONTEXT_PATH/
    - docker push ${CI_REGISTRY_IMAGE}/${APP_NAME}:$CI_COMMIT_SHA
    - docker push ${CI_REGISTRY_IMAGE}/${APP_NAME}:$IMAGE_TAG

  # generic deployment script
.deploy-k8s: &deploy-k8s
  image:
    name: alpine/helm:3.16.3
    entrypoint: [ "" ]
  stage: deploy
  variables:
    HELM_PROJECT_DIR: dummy
    IMAGE_TAG: local
  script:
    - helm repo add bitnami https://charts.bitnami.com/bitnami
    - helm get values ${HELM_PROJECT} > values.yaml
    - helm dependency build ./charts/${HELM_PROJECT_DIR}/
    # yamllint disable-line rule:line-length
    - helm upgrade ${HELM_PROJECT} -f values.yaml --set image.tag=${IMAGE_TAG} ./charts/${HELM_PROJECT_DIR}/

### MR checks
generate api.yaml:
  only: [ merge_requests, "master" ]
  stage: pre-build
  interruptible: true
  image: jeanberu/swagger-cli
  script:
    # yamllint disable-line rule:line-length
    - swagger-cli bundle common/open-api-definition/api-split.yaml --outfile common/generated/api.yaml --type yaml
  artifacts:
    when: always
    expire_in: 3 days
    paths:
      - common/generated/api.yaml

### charts lint
lint chart.yaml:
  only: [ merge_requests ]
  stage: pre-build
  interruptible: true
  image:
    name: alpine/helm:3.16.3
    entrypoint: [ "" ]
  script:
    - cd charts
    - helm lint back-office
    - helm lint sourcing
    - helm dependency update api && helm lint api


### testing

tag testing back-office:
  <<: *tag
  only: [ "master", "/^fix-bo-testing-/", "/^testing-/" ]
  variables:
    IMAGE_TAG: testing
    APP_NAME: back-office
tag testing sourcing:
  <<: *tag
  only: [ "master", "/^fix-sourcing-testing-/", "/^testing-/" ]
  variables:
    IMAGE_TAG: testing
    APP_NAME: sourcing
    DOCKERFILE: ./sourcing/Dockerfile

deploy:testing:k8s:api:
  <<: *deploy-k8s
  needs:
    - "tag testing API"
  variables:
    IMAGE_TAG: testing
    HELM_PROJECT_DIR: api
  environment: "testing-k8s-api"
  only: [ "master", "/^fix-api-testing-/", "/^testing-/" ]
deploy:testing:k8s:back:
  <<: *deploy-k8s
  needs:
    - "tag testing back-office"
  variables:
    IMAGE_TAG: testing
    HELM_PROJECT_DIR: back-office
  environment: "testing-k8s-back"
  only: [ "master", "/^fix-bo-testing-/", "/^testing-/" ]
deploy:testing:k8s:sourcing:
  <<: *deploy-k8s
  needs:
    - "tag testing sourcing"
  variables:
    IMAGE_TAG: testing
    HELM_PROJECT_DIR: sourcing
  environment: "testing-k8s-sourcing"
  only: [ "master", "/^fix-sourcing-testing-/", "/^testing-/" ]

verify api testing deployment:
  extends: .checkVersion
  needs: [ "deploy:testing:k8s:api" ]
  only: [ "master", "/^fix-api-testing-/", "/^testing-/" ]
  when: delayed
  start_in: 5 minutes
  script:
    - *getCurrentVersion
    # yamllint disable-line rule:line-length
    - common/check_version.sh "$CURRENT_VERSION" "https://testing-api.jenesuispasuncv.fr/api/odas/public/version"

verify bo testing deployment:
  extends: .checkVersion
  needs: [ "deploy:testing:k8s:back" ]
  only: [ "master", "/^fix-bo-testing-/", "/^testing-/" ]
  when: delayed
  start_in: 5 minutes
  script:
    - *getCurrentVersion
    # yamllint disable-line rule:line-length
    - common/check_version.sh "$CURRENT_VERSION" "https://testing-bo.jenesuispasuncv.fr/version"

verify sourcing testing deployment:
  extends: .checkVersion
  needs: [ "deploy:testing:k8s:sourcing" ]
  only: [ "master", "/^fix-sourcing-testing-/", "/^testing-/" ]
  when: delayed
  start_in: 5 minutes
  script:
    - *getCurrentVersion
    # yamllint disable-line rule:line-length
    - common/check_version.sh "$CURRENT_VERSION" "https://testing-jerecrute.jenesuispasuncv.fr/version"


### staging

tag staging back-office:
  <<: *tag
  only: [ "/^fix-bo-staging-/", "/^staging-/" ]
  variables:
    IMAGE_TAG: staging
    APP_NAME: back-office
tag staging sourcing:
  <<: *tag
  only: [ "/^fix-sourcing-staging-/", "/^staging-/" ]
  variables:
    IMAGE_TAG: staging
    APP_NAME: sourcing
    DOCKERFILE: ./sourcing/Dockerfile
deploy:staging:k8s:api:
  <<: *deploy-k8s
  needs:
    - "tag staging API"
  variables:
    IMAGE_TAG: staging
    HELM_PROJECT_DIR: api
  environment: "staging-k8s-api"
  only: [ "/^fix-api-staging-/", "/^staging-/" ]
deploy:staging:k8s:back:
  <<: *deploy-k8s
  needs:
    - "tag staging back-office"
  variables:
    IMAGE_TAG: staging
    HELM_PROJECT_DIR: back-office
  environment: "staging-k8s-back"
  only: [ "/^fix-bo-staging-/", "/^staging-/" ]
deploy:staging:k8s:sourcing:
  <<: *deploy-k8s
  needs:
    - "tag staging sourcing"
  variables:
    IMAGE_TAG: staging
    HELM_PROJECT_DIR: sourcing
  environment: "staging-k8s-sourcing"
  only: [ "/^fix-sourcing-staging-/", "/^staging-/" ]
verify api staging deployment:
  extends: .checkVersion
  needs: [ "deploy:staging:k8s:api" ]
  only: [ "/^fix-api-staging-/", "/^staging-/" ]
  when: delayed
  start_in: 5 minutes
  script:
    - *getCurrentVersion
    # yamllint disable-line rule:line-length
    - common/check_version.sh "$CURRENT_VERSION" "https://staging-api.jenesuispasuncv.fr/api/odas/public/version"
verify bo staging deployment:
  extends: .checkVersion
  needs: [ "deploy:staging:k8s:back" ]
  only: [ "/^fix-bo-staging-/", "/^staging-/" ]
  when: delayed
  start_in: 5 minutes
  script:
    - *getCurrentVersion
    # yamllint disable-line rule:line-length
    - common/check_version.sh "$CURRENT_VERSION" "https://staging-bo.jenesuispasuncv.fr/version"
verify sourcing staging deployment:
  extends: .checkVersion
  needs: [ "deploy:staging:k8s:sourcing" ]
  only: [ "/^fix-sourcing-staging-/", "/^staging-/" ]
  when: delayed
  start_in: 5 minutes
  script:
    - *getCurrentVersion
    # yamllint disable-line rule:line-length
    - common/check_version.sh "$CURRENT_VERSION" "https://staging-jerecrute.jenesuispasuncv.fr/version"

### e2e
build e2e back-office:
  <<: *build
  needs:
    - "generate api.yaml"
  rules:
    - if: $CI_MERGE_REQUEST_ID
  variables:
    IMAGE_TAG: e2e
    APP_NAME: back-office

build e2e launcher:
  <<: *build
  rules:
    - if: $CI_MERGE_REQUEST_ID && $CI_COMMIT_MESSAGE !~ /SKIP_E2E/
  variables:
    IMAGE_TAG: e2e
    APP_NAME: test_e2e_launcher
    DOCKERFILE: ./tests/Dockerfile
    CONTEXT_PATH: ./tests

build sourcing:
  <<: *build
  needs:
    - "generate api.yaml"
  rules:
    - if: $CI_MERGE_REQUEST_ID
  variables:
    IMAGE_TAG: testing
    APP_NAME: sourcing
    DOCKERFILE: ./sourcing/Dockerfile

e2e tests:
  stage: test
  interruptible: true
  rules:
    - if: $CI_MERGE_REQUEST_ID && $CI_COMMIT_MESSAGE !~ /SKIP_E2E/
  image: docker/compose:latest
  tags:
    - erhgo-e2e
  services:
    - docker:dind
  # yamllint disable-line rule:line-length
  needs: [ "build e2e launcher", "build e2e back-office", "dockerize API" ]
  script:
    - docker login -u "${DOCKERHUB_USERNAME}" -p "${DOCKERHUB_PASSWORD}"
    - cd tests
    - ./deployE2e.sh
  retry: 2
  artifacts:
    expire_in: 24 hours
    when: on_failure
    paths:
      - tests/vrt/
      - tests/tests_output/

### prod
tag prod back-office:
  <<: *tag
  only: [ "/^fix-bo-prod-/", "/^prod-/" ]
  variables:
    IMAGE_TAG: prod
    APP_NAME: back-office
tag prod sourcing:
  <<: *tag
  only: [ "/^fix-sourcing-prod-/", "/^prod-/" ]
  variables:
    IMAGE_TAG: prod
    APP_NAME: sourcing
    DOCKERFILE: ./sourcing/Dockerfile
deploy:prod:k8s:api:
  <<: *deploy-k8s
  needs:
    - "tag prod API"
  variables:
    IMAGE_TAG: master
    HELM_PROJECT_DIR: api
  environment: "prod-k8s-api"
  only: [ "/^fix-api-prod-/", "/^prod-/" ]
deploy:prod:k8s:back:
  <<: *deploy-k8s
  needs:
    - "tag prod back-office"
  variables:
    IMAGE_TAG: prod
    HELM_PROJECT_DIR: back-office
  environment: "prod-k8s-back"
  only: [ "/^fix-bo-prod-/", "/^prod-/" ]
deploy:prod:k8s:sourcing:
  <<: *deploy-k8s
  needs:
    - "tag prod sourcing"
  variables:
    IMAGE_TAG: prod
    HELM_PROJECT_DIR: sourcing
  environment: "prod-k8s-sourcing"
  only: [ "/^fix-sourcing-prod-/", "/^prod-/" ]
verify api prod deployment:
  extends: .checkVersion
  needs: [ "deploy:prod:k8s:api" ]
  only: [ "/^fix-api-prod-/", "/^prod-/" ]
  when: delayed
  start_in: 5 minutes
  script:
    - *getCurrentVersion
    # yamllint disable-line rule:line-length
    - common/check_version.sh "$CURRENT_VERSION" "https://api.jenesuispasuncv.fr/api/odas/public/version"
verify bo prod deployment:
  extends: .checkVersion
  needs: [ "deploy:prod:k8s:back" ]
  only: [ "/^fix-bo-prod-/", "/^prod-/" ]
  when: delayed
  start_in: 5 minutes
  script:
    - *getCurrentVersion
    # yamllint disable-line rule:line-length
    - common/check_version.sh "$CURRENT_VERSION" "https://bo.jenesuispasuncv.fr/version"
verify sourcing prod deployment:
  extends: .checkVersion
  needs: [ "deploy:prod:k8s:sourcing" ]
  only: [ "/^fix-sourcing-prod-/", "/^prod-/" ]
  when: delayed
  start_in: 5 minutes
  script:
    - *getCurrentVersion
    # yamllint disable-line rule:line-length
    - common/check_version.sh "$CURRENT_VERSION" "https://jerecrute.jenesuispasuncv.fr/version"
