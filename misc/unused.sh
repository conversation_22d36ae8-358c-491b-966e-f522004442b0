#!/bin/zsh

# Définition de la liste de fonctions
fonctions=(
"getVersion"
"getPublicRecruitmentInfo"
"logEvent"
"getLandingPageByUrlKey"
"getAlgoliaSearchConfiguration"
"getAllConfigurableProperties"
"editConfigurableProperty"
"createRecruitment"
"updateRecruitment"
"refreshMatching"
"meetCandidature"
"getMatchingCandidature"
"getCandidatures"
"getCandidaturesForConsultant"
"getRecruitmentForMatching"
"getRecruitmentForUpdate"
"listRecruitments"
"switchRecruitmentState"
"listRecruitmentsForOrientation"
"listRecruitmentsForCandidateOrientation"
"listRecruitmentsForJob"
"sendCandidatureProposal"
"listUserRecruitmentReports"
"createNewContext"
"updateContext"
"getContext"
"listContexts"
"searchContexts"
"saveContextQuestion"
"getQuestionForContexts"
"listContextsQuestionsByContextId"
"listQuestionForContexts"
"getRecruitmentProfilesByContextQuestionAndContextId"
"saveCapacityRelatedQuestion"
"getCapacityRelatedQuestionDetails"
"getCapacityRelatedQuestionPage"
"reorderCapacityRelatedQuestions"
"getQuestionsForUser"
"saveAnswerToCapacityRelatedQuestion"
"getQuestionsSumupForUser"
"escoOccupationPage"
"getEscoOccupationCapacities"
"getEscoOccupation"
"searchEscoOccupation"
"getSkill"
"linkToActivity"
"deleteLinkToActivity"
"linkToBehavior"
"deleteLinkToBehavior"
"linkToContext"
"deleteLinkToContext"
"setSkillNoActivity"
"setSkillNoBehavior"
"setSkillNoContext"
"updateSkillDescription"
"romePage"
"createErhgoOccupation"
"reindexErhgoOccupations"
"denormalizeErhgoOccupations"
"qualifyOccupationState"
"unqualifyOccupationState"
"updateMasteryLevel"
"updateDescription"
"updateBehaviorsDescription"
"updateErhgoOccupationErhgoClassifications"
"updateWorkDomain"
"updateWorkEnvironments"
"updateSpecifications"
"getErhgoOccupation"
"getErhgoOccupationSumUp"
"getErhgoOccupationForCapacitor"
"erhgoOccupationPage"
"erhgoOccupationOTPage"
"getErhgoOccupationsByJobActivityLabel"
"linkRomeToErhgoOccupation"
"unlinkRomeFromErhgoOccupation"
"linkEscoOccupationToErhgoOccupation"
"unlinkEscoOccupationFromErhgoOccupation"
"addActivitiesToOccupation"
"updateCapacityFamiliesToOccupation"
"removeActivitiesFromOccupation"
"setOccupationActivityMandatoryState"
"addContextToOccupation"
"removeContextFromOccupation"
"setOccupationContextMandatoryState"
"addBehaviorToOccupation"
"removeBehaviorFromOccupation"
"editAlternativeLabels"
"searchOccupations"
"unlinkSkillFromErhgoOccupation"
"changeOccupationPriority"
"setOccupationBehaviorCategory"
"getErhgoOccupationCapacities"
"getErhgoOccupationDetailsPdf"
"mergeOccupations"
"updateErhgoOccupationCriteriaValues"
"getErhgoOccupationsWithLabels"
"createNewMission"
"updateMission"
"deleteMission"
"changeOccupationVisibilityForOrientation"
"saveJob"
"getJobPage"
"updateCriteriaForJob"
"addWorkingTimeTypeForJob"
"saveBehavior"
"publish"
"getPublishedJobs"
"reorderMissions"
"createJobForTemplate"
"getJob"
"deleteJob"
"getJobCapacities"
"setRecommendationForJob"
"listRecruitmentProfiles"
"saveRecruitmentProfile"
"getRecruitmentProfile"
"getProfileCapacities"
"deleteOptionals"
"endQualification"
"addOptionals"
"setContextQuestionForProfile"
"getUsersMatchingJob"
"getUsersMatchingJobExport"
"getJobPdfExport"
"getJobCandidates"
"reindexActivities"
"searchActivities"
"getActivity"
"isActivityLabelDeletable"
"listActivityLabels"
"saveActivity"
"mergeActivities"
"saveExperience"
"getExperience"
"deleteExperience"
"createUserFO"
"createUser"
"deleteUser"
"getUserCandidatures"
"getMembersOfGroups"
"getUserExperiences"
"getUserProfile"
"getUserProfileDetailWithCapacities"
"getUserCriterias"
"setUserCriterias"
"getUserWorkDomains"
"setUserWorkDomains"
"getUserErhgoClassifications"
"setUserErhgoClassification"
"getUserContactInfo"
"setUserContactInfo"
"setUserName"
"setUserCity"
"setUserSalary"
"setUserSituation"
"setUserOccupation"
"setUserPhoneNumber"
"setUserShouldBeContacted"
"tagNoExperienceForUser"
"searchFrontOfficeUser"
"exportUsers"
"searchFrontOfficeUserByGroups"
"getFrontOfficeUserByGroupExport"
"setFrontOfficeUserPassword"
"confirmFOUserFromBO"
"resendInitialMail"
"getBackOfficeUsers"
"getMatchingErhgoOccupation"
"getMatchingOccupationDetail"
"getMatchingErhgoOccupationExperiences"
"getMatchingErhgoOccupationsForOrientationBackOffice"
"getMatchingErhgoOccupationsForOrientationFrontOffice"
"getMatchingJobs"
"getUserJobsCandidatures"
"getMatchingRecruitments"
"getUserCapacities"
"getUserCapacityFamilies"
"getUserProfileProgress"
"getUserRegistrationState"
"getUserBehaviorDetails"
"updateUserBehaviors"
"getUserLevel"
"initializeProfile"
"updateUsersChannels"
"clearCaches"
"updateUserRegistrationStateStep"
"getUserJobOffersOptIn"
"updateUserJobOffersOptIn"
"getUserJobDatingNotifyOptIn"
"updateUserJobDatingNotifyOptIn"
"getUserProfilePdf"
"getUserProfileFOPdf"
"getUserNotes"
"saveUserNote"
"deleteUserNote"
"reindexAllUsers"
"indexUsersNow"
"setUserEmail"
"getUserNotifications"
"markAllNotificationsAsRead"
"markNotificationAsRead"
"saveUserMobileToken"
"sendMobileNotificationToUsers"
"countNotifiableUsers"
"sendNotificationsToUsersSelection"
"publishCandidature"
"saveCandidatureState"
"createOrRetrieveCandidature"
"getContextsToEvaluateReferencingExperiences"
"setContextsMet"
"saveCustomAnswer"
"getCandidatureDetail"
"getCandidaturesPage"
"saveCandidatureNote"
"markCandidatureAsRefused"
"generateCandidaturesOnRecruitments"
"markCandidaturesAsSeen"
"getCandidaturePreview"
"getAvailabilityForCandidature"
"updateAvailabilityForCandidature"
"initializeCandidatureData"
"getCandidatesStats"
"getTopCapacitiesForErhgoOccupations"
"getTopCapacitiesForJobs"
"getTopCapacitiesForUsers"
"getOccupationsStats"
"getTopOccupationsForExperiences"
"behaviorPage"
"saveLandingPage"
"getLandingPageById"
"getLandingPages"
"saveOrganization"
"getOrganization"
"getOrganizationsHavingLandingPage"
"getAllRecruitersForOrganization"
"getAllRecruiters"
"getOrganizationByCode"
"findOrganizationsPaginatedAndFilteredByProperty"
"getCriteria"
"editCriteria"
"listWorkDomains"
"listWorkEnvironments"
"executeAllHealthCheckQueries"
"listErhgoClassifications"
"updateErhgoClassificationsForSourcingRecruitment"
"countCandidates"
"getOccupationSumup"
"notifyNoOccupationFound"
"initializeSourcingAccount"
"updateSourcingJobContract"
"updateCriteriaForSourcingJob"
"updateSourcingRecruitmentQuestion"
"countCandidatures"
"getSourcingJobAndRecruitment"
"getSourcingRecruitments"
"getSourcingRecruiters"
"updateSourcingStep"
"getSourcingCandidaturesPage"
"listInvitationCodes"
"saveInvitationCode"
"updateSourcingCandidatureState"
"getSourcingCandidatureDetail"
"updateSourcingUser"
"getSourcingOrganization"
"updateSourcingOrganization"
"inviteToRecruitment"
"sendContactForm"
"createOrUpdateSourcingJobAndRecruitment"
"inviteAndCreateNewUser"
"publishSourcingRecruitment"
"simulateSourcingAlgoliaFilters"
"getSimilarRecruitments"
"getAllJobDatings"
"getJobDating"
"affectJobDatingSlot"
)

# Définition des répertoires à vérifier
repertoires=("/home/<USER>/workspace/erhgo/back-office/src" "/home/<USER>/workspace/erhgo/front-office/src" "/home/<USER>/workspace/erhgo/common/plugins/" "/home/<USER>/workspace/erhgo/sourcing/src" "/home/<USER>/workspace/jenesuispasuncv-mobile/lib")

# Pour chaque fonction dans la liste
for fonction in "${fonctions[@]}"; do
    # Pour chaque répertoire dans la liste
    for repertoire in "${repertoires[@]}"; do
        # Rechercher la fonction dans le répertoire (récursivement)
        result=$(find "$repertoire" -type f -exec grep -l "$fonction" {} \;)
        # Si la fonction est trouvée, passer à la prochaine fonction
        if [ -n "$result" ]; then
            continue 2
        fi
    done
    # Si la fonction n'est trouvée dans aucun des répertoires, l'afficher
    echo "La fonction '$fonction' n'est utilisée dans aucun des répertoires"
done
