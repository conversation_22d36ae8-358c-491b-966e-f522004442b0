# Suivi des logs

En cas de notification d'erreur sur Slack par Graylog :

- Visualise l'erreur sur Graylog en cliquant sur le lien
- Cf. ci-dessous quelques tips en fonction du format du message
- Regarder 5 minutes autour et s'assurer qu'il n'y a pas d'autres erreurs

# Erreur send in blue

- Pattern : `Exception during call to SendInBlue api` dans le message
- Action :
    - Identifier l'action en cause dans la stacktrace (ajout ou suppression d'un utilisateur à une liste)
    - Reprendre l'action manuellement dans [sendinblue](https://account.sendinblue.com/senders) si besoin
