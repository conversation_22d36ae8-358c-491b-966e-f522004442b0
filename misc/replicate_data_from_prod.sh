#!/bin/bash
set -euo pipefail

# Color codes for visual output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m' # No Color

# Logging functions with emojis and colors
log_step() {
    echo -e "${BLUE}🔄 $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}" >&2
}

log_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

die() {
    log_error "$@"
    exit 1
}

# Function to format elapsed time in human-readable format
format_elapsed_time() {
    local elapsed_seconds="$1"
    local hours=$((elapsed_seconds / 3600))
    local minutes=$(((elapsed_seconds % 3600) / 60))
    local seconds=$((elapsed_seconds % 60))

    if [[ $hours -gt 0 ]]; then
        printf "%dh %dm %ds" "$hours" "$minutes" "$seconds"
    elif [[ $minutes -gt 0 ]]; then
        printf "%dm %ds" "$minutes" "$seconds"
    else
        printf "%ds" "$seconds"
    fi
}

# Global variables for database passwords
MARIADB_PASSWORD=""
POSTGRES_PASSWORD=""

# Function to get MariaDB root password from pod environment
get_mariadb_password() {
    local pod_name="$1"
    local namespace="$2"

    log_step "Retrieving MariaDB root password from pod environment..."

    # Use MARIADB_ROOT_PASSWORD for root user
    # Capture only stdout (password) and ignore stderr (kubectl warnings)
    local password
    password=$(kubectl exec "$pod_name" -n "$namespace" -c mariadb -- printenv MARIADB_ROOT_PASSWORD 2>/dev/null)
    local exit_code=$?

    if [[ $exit_code -ne 0 ]]; then
        die "Failed to retrieve MARIADB_ROOT_PASSWORD from pod $pod_name (exit code: $exit_code)"
    fi

    # Clean the password by removing any potential kubectl warning lines
    password=$(echo "$password" | grep -v "Defaulted container" | tr -d '\n\r')

    if [[ -z "$password" ]]; then
        die "MARIADB_ROOT_PASSWORD is empty in pod $pod_name"
    fi

    # Set global variable
    MARIADB_PASSWORD="$password"
    log_success "MariaDB root password retrieved successfully (${#password} characters)"
}

# Function to get PostgreSQL password from pod environment
get_postgres_password() {
    local pod_name="$1"
    local namespace="$2"

    log_step "Retrieving PostgreSQL keycloak user password from pod environment..."

    # Use POSTGRES_PASSWORD for keycloak user
    # Capture only stdout (password) and ignore stderr (kubectl warnings)
    local password
    password=$(kubectl exec "$pod_name" -n "$namespace" -- printenv POSTGRES_PASSWORD 2>/dev/null)
    local exit_code=$?

    if [[ $exit_code -ne 0 ]]; then
        die "Failed to retrieve POSTGRES_PASSWORD from pod $pod_name (exit code: $exit_code)"
    fi

    # Clean the password by removing any potential kubectl warning lines
    password=$(echo "$password" | grep -v "Defaulted container" | tr -d '\n\r')

    if [[ -z "$password" ]]; then
        die "POSTGRES_PASSWORD is empty in pod $pod_name"
    fi

    # Set global variable
    POSTGRES_PASSWORD="$password"
    log_success "PostgreSQL keycloak password retrieved successfully (${#password} characters)"
}

# Function to test MariaDB connection
test_mariadb_connection() {
    local pod_name="$1"
    local namespace="$2"
    local password="$3"

    log_step "Testing MariaDB connection..."

    # Test connection without color codes in kubectl exec
    if kubectl exec "$pod_name" -n "$namespace" -- sh -c "mysql -u root -p'$password' -e 'SELECT 1;'" >/dev/null 2>&1; then
        log_success "MariaDB connection test passed"
        return 0
    else
        log_error "MariaDB connection test failed"
        return 1
    fi
}

# Function to test PostgreSQL connection
test_postgres_connection() {
    local pod_name="$1"
    local namespace="$2"
    local password="$3"

    log_step "Testing PostgreSQL connection..."

    # Test connection without color codes in kubectl exec
    if kubectl exec "$pod_name" -n "$namespace" -- sh -c "PGPASSWORD='$password' psql -U keycloak -d keycloak -c 'SELECT 1;'" >/dev/null 2>&1; then
        log_success "PostgreSQL connection test passed"
        return 0
    else
        log_error "PostgreSQL connection test failed"
        return 1
    fi
}

# Validate input parameters
if [[ ($# != 1) ]]; then
    die "Incorrect target environment. Accepted values are: erhgo-preprod, erhgo-staging\nSyntax: replicate_data_from_prod.sh <target-environment>"
fi

targetEnv="${1}"

if ! [[ "$targetEnv" =~ ^(erhgo-preprod|erhgo-staging)$ ]]; then
    die "Incorrect target environment. Accepted values are: erhgo-preprod, erhgo-staging"
fi

log_info "Starting data replication to environment: $targetEnv"

# Get credential values for keycloak admin user
log_step "Please enter credential.secret_data value for admin user on target env"
log_info "Tip: You can find it with: select * from credential where user_id='4c5356c6-1ef7-4bf6-8433-dd6e23f729b6'"
read -r credentialValue

log_step "Please enter credential.salt value for admin user on target env"
read -r credentialSalt

# dirty fix for keycloak deployment typo for preprod namespace
[[ $targetEnv == "erhgo-staging" ]] \
    && targetKeycloak="keycloak" \
    || targetKeycloak="keycloack"

log_step "Discovering pod names..."

prodApiPod=$(kubectl get pods -n erhgo-prod | grep production-api | awk '{print $1}' | head -n 1)
targetApiDatabase=$(kubectl get pods -n "$targetEnv" | grep mariadb | awk '{print $1}')
targetKeycloakDatabase=$(kubectl get pods -n "$targetEnv" | grep "$targetKeycloak"-postgresql | awk '{print $1}')

if [[ -z "$prodApiPod" ]]; then
    die "Could not find production API pod in erhgo-prod namespace"
fi

if [[ -z "$targetApiDatabase" ]]; then
    die "Could not find MariaDB pod in $targetEnv namespace"
fi

if [[ -z "$targetKeycloakDatabase" ]]; then
    die "Could not find PostgreSQL pod in $targetEnv namespace"
fi

log_success "Pod discovery completed"
log_info "Production API pod: $prodApiPod"
log_info "Target MariaDB pod: $targetApiDatabase"
log_info "Target PostgreSQL pod: $targetKeycloakDatabase"

# Get database passwords automatically
get_mariadb_password "$targetApiDatabase" "$targetEnv"
get_postgres_password "$targetKeycloakDatabase" "$targetEnv"

# Test database connections
test_mariadb_connection "$targetApiDatabase" "$targetEnv" "$MARIADB_PASSWORD" || die "MariaDB connection test failed"
test_postgres_connection "$targetKeycloakDatabase" "$targetEnv" "$POSTGRES_PASSWORD" || die "PostgreSQL connection test failed"

log_step "📥 Copying backup files from production to local..."

# Create temp directory if it doesn't exist
mkdir -p /tmp/"$targetEnv"

kubectl cp erhgo-prod/"$prodApiPod":backup/master/. /tmp/"$targetEnv" || die "Failed to copy API backup from production"
kubectl cp erhgo-prod/"$prodApiPod":backup/master-psql/. /tmp/"$targetEnv" || die "Failed to copy Keycloak backup from production"

log_success "Backup files copied to local successfully"

# Validate downloaded files
targetApiDump=$(find /tmp/"$targetEnv"/*erhgo* 2>/dev/null | head -n 1)
targetKeycloakDump=$(find /tmp/"$targetEnv"/*keycloak* 2>/dev/null | head -n 1)

if [[ -z "$targetApiDump" || ! -f "$targetApiDump" ]]; then
    die "API dump file not found or invalid: $targetApiDump"
fi

if [[ -z "$targetKeycloakDump" || ! -f "$targetKeycloakDump" ]]; then
    die "Keycloak dump file not found or invalid: $targetKeycloakDump"
fi

# Check file sizes
apiDumpSize=$(stat -f%z "$targetApiDump" 2>/dev/null || stat -c%s "$targetApiDump" 2>/dev/null)
keycloakDumpSize=$(stat -f%z "$targetKeycloakDump" 2>/dev/null || stat -c%s "$targetKeycloakDump" 2>/dev/null)

if [[ "$apiDumpSize" -lt 1000 ]]; then
    die "API dump file seems too small (${apiDumpSize} bytes): $targetApiDump"
fi

if [[ "$keycloakDumpSize" -lt 1000 ]]; then
    die "Keycloak dump file seems too small (${keycloakDumpSize} bytes): $targetKeycloakDump"
fi

log_success "File validation completed"
log_info "API dump: $targetApiDump ($(numfmt --to=iec $apiDumpSize))"
log_info "Keycloak dump: $targetKeycloakDump ($(numfmt --to=iec $keycloakDumpSize))"

log_step "📤 Copying backup files to target pods..."

kubectl cp "$targetApiDump" "$targetEnv"/"$targetApiDatabase":/tmp/mysql.sql.gz || die "Failed to copy API dump to target pod"
kubectl cp "$targetKeycloakDump" "$targetEnv"/"$targetKeycloakDatabase":/tmp/psql.sql.gz || die "Failed to copy Keycloak dump to target pod"

log_success "Backup files copied to target pods successfully"

log_step "🧹 Cleaning temporary files..."
rm -rf /tmp/"$targetEnv"
log_success "Temporary files cleaned"

log_step "🗄️  Importing API database (MariaDB)..."

# stop api pod just in case
targetApiDeployment="${targetEnv//erhgo-/}"
log_info "Scaling down API deployment to prevent conflicts..."
kubectl -n "$targetEnv" scale deployment "$targetApiDeployment-api" --replicas=0

log_warning "Database import will take approximately 15+ minutes. Please be patient..."

# Import database with automated password
log_step "Starting MariaDB import process..."
if kubectl exec -i "$targetApiDatabase" -n "$targetEnv" -c mariadb -- sh -c "zcat /tmp/mysql.sql.gz | mysql -u root -p'$MARIADB_PASSWORD' erhgo" 2>/dev/null; then
    log_success "API database import completed successfully"
else
    die "API database import failed"
fi

# Clean up dump file
kubectl exec -i "$targetApiDatabase" -n "$targetEnv" -c mariadb -- rm -f /tmp/mysql.sql.gz

# Reset tokens to avoid sending mobile notifications to real users
log_step "Resetting mobile tokens to prevent notifications to real users..."
if kubectl exec -i "$targetApiDatabase" -n "$targetEnv" -c mariadb -- sh -c "mysql -u root -p'$MARIADB_PASSWORD' erhgo -e 'TRUNCATE TABLE UserMobileToken'" 2>/dev/null; then
    log_success "Mobile tokens reset successfully"
else
    log_warning "Failed to reset mobile tokens - please do this manually"
fi

log_step "🔐 Importing Keycloak database (PostgreSQL)..."

# Import with suppressed verbose logs
if kubectl exec -i "$targetKeycloakDatabase" -n "$targetEnv" -c postgresql -- sh -c "zcat /tmp/psql.sql.gz | PGPASSWORD='$POSTGRES_PASSWORD' psql -U keycloak -d keycloak -q" 2>/dev/null; then
    log_success "Keycloak database import completed successfully"
else
    die "Keycloak database import failed"
fi

# Clean up dump file
kubectl exec -i "$targetKeycloakDatabase" -n "$targetEnv" -- rm -f /tmp/psql.sql.gz

[[ $targetEnv == "erhgo-staging" ]] \
    && currentEnv="staging" \
    || currentEnv="testing"

log_step "🔧 Updating Keycloak configuration for $currentEnv environment..."

# Update keycloak with environment-specific values (suppress verbose ALTER TABLE logs)
{
  echo "CREATE TEMPORARY VIEW credentialValue(val) AS VALUES ('${credentialValue}');"
  echo "CREATE TEMPORARY VIEW credentialSalt(val) AS VALUES ('${credentialSalt}');"
  echo "CREATE TEMPORARY VIEW currentEnv(val) AS VALUES ('${currentEnv}');"
  cat replication_keycloak_update.sql;
} | kubectl exec -i "$targetKeycloakDatabase" -n "$targetEnv" -- sh -c "PGPASSWORD='${POSTGRES_PASSWORD}' psql -U keycloak -d keycloak -q" 2>/dev/null

if [[ $? -eq 0 ]]; then
    log_success "Keycloak configuration updated successfully"
else
    log_warning "Keycloak configuration update may have had issues - please verify manually"
fi

# restart keycloak pod
log_step "🔄 Restarting Keycloak pod..."
targetKeycloakPod=$(kubectl get pods -n "$targetEnv" | grep "$targetKeycloak" | awk '{print $1}' | head -n 1)
if [[ -n "$targetKeycloakPod" ]]; then
    kubectl delete pod "$targetKeycloakPod" -n "$targetEnv"
    log_success "Keycloak pod restart initiated"
else
    log_warning "Could not find Keycloak pod to restart"
fi

# restart api pod
log_step "🚀 Scaling up API deployment..."
kubectl -n "$targetEnv" scale deployment "$targetApiDeployment-api" --replicas=1
log_success "API deployment scaled up"

log_success "🎉 Data replication completed successfully!"
log_info "📋 Next steps:"
log_info "   1. Go to /admin/indexing to re-index all users"

if [[ "$targetEnv" == "erhgo-preprod" ]]; then
    log_info "   2. Anonymize users using the admin dashboard"
fi

log_info "🔍 Please verify that both applications are running correctly"
