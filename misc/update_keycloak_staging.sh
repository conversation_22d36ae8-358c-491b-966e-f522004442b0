#!/bin/bash
set -e

read -p "Whitelister son IP auprès de Jetpulp"  -n1 -s
printf "\n"
read -p "Récupérer la clé privée permettant de se connecter à l'infra chez Jetpulp sur Gitlab dans ~/.ssh/id_rsa_deploy et positionner des droits assez stricts (600)" -n1 -s
printf "\n"
printf "\n"
echo "Pour cibler staging - Lancer le script :"
echo "ssh -ti ~/.ssh/id_rsa_deploy odas-e2e@************** \"sudo /usr/local/bin/import_prod_in_staging.sh\""
echo "Pour cibler demo - Lancer le script :"
read -p  "ssh -ti ~/.ssh/id_rsa_deploy odas-e2e@************** \"sudo /usr/local/bin/import_prod_in_demo.sh\"" -n1 -s
printf "\n"
printf "\n"
echo "Reindexation Algolia :"
read -p "- Se rendre sur https://staging-bo.jenesuispasuncv.fr/admin/indexing ou https://demo-bo.jenesuispasuncv.fr/admin/indexing" -n1 -s
printf "\n"
read -p "- Relancer les indexation des activités, métiers, métiers de l'orientation" -n1 -s
printf "\n"
