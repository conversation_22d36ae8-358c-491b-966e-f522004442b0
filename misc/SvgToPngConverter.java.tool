package com.erhgo.services.exporter;

import lombok.SneakyThrows;
import org.apache.batik.transcoder.SVGAbstractTranscoder;
import org.apache.batik.transcoder.TranscoderInput;
import org.apache.batik.transcoder.TranscoderOutput;
import org.apache.batik.transcoder.image.ImageTranscoder;
import org.apache.batik.transcoder.image.PNGTranscoder;

import java.awt.*;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.List;
import java.util.regex.Pattern;

/**
* Useful java script used to generate Bsase64 png encoded based on svg string.
* Move inside java app sources, rename to .java & add apache Batik dep, then put svg in svgInputs var and run it.
**/
public class SvgToPngConverter {

    public static String changeFillColor(String svg, String fillColor) {
        return svg.replaceAll("fill=\"[^\"]*\"", "").replaceAll("(?i)<(\\w+)([^>]*)>", "<$1 fill=\"%s\" $2>".formatted(fillColor));
    }

    public static float extractViewBoxRatio(String svg) {
        var pattern = Pattern.compile("viewBox=\"(\\d+\\s\\d+\\s\\d+\\s\\d+)\"");
        var matcher = pattern.matcher(svg);
        if (matcher.find()) {
            var viewBoxValues = matcher.group(1).split("\\s");
            var width = Float.parseFloat(viewBoxValues[2]);
            var height = Float.parseFloat(viewBoxValues[3]);
            return width / height;
        }
        return 1;
    }

    @SneakyThrows
    public static String convertSvgToPngBase64(String svgIn, String fillColor, int height) {
        var svg = changeFillColor(svgIn, fillColor);
        var ratio = extractViewBoxRatio(svg);
        var width = height * ratio;

        var transcoder = new PNGTranscoder();
        transcoder.addTranscodingHint(SVGAbstractTranscoder.KEY_HEIGHT, (float) height * 5);
        transcoder.addTranscodingHint(SVGAbstractTranscoder.KEY_WIDTH, width * 5);
        transcoder.addTranscodingHint(ImageTranscoder.KEY_BACKGROUND_COLOR, new Color(0, 0, 0, 0));
        transcoder.addTranscodingHint(SVGAbstractTranscoder.KEY_PIXEL_UNIT_TO_MILLIMETER, 0.09f);
        var input = new TranscoderInput(new ByteArrayInputStream(svg.getBytes()));
        try (var outputStream = new ByteArrayOutputStream()) {
            transcoder.transcode(input, new TranscoderOutput(outputStream));
            var pngBytes = outputStream.toByteArray();

            return "data:image/png;base64," + Base64.getEncoder().encodeToString(pngBytes);
        }

    }

    public static void main(String[] args) {
        var svgInputs = List.of(
                """
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">xxx SVG path GOES HERE</svg>
                        """
        );
        var color = "#00A88C";
        int height = 60;

        svgInputs.forEach(svgInput -> System.out.println(convertSvgToPngBase64(svgInput, color, height)));
    }

    @SneakyThrows
    public static String convertFileToBase64Uri(String filePath) {
        // Lire le fichier en tant que tableau de bytes
        byte[] fileContent = Files.readAllBytes(Paths.get(filePath));
        // Encoder le tableau de bytes en Base64
        String encodedString = Base64.getEncoder().encodeToString(fileContent);
        // Construire la Data URI
        return "data:image/png;base64," + encodedString;
    }

}
