const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs');
const http = require('http');
const csv = require('csv-parser');

// Constants
const CSV_FILE_PATH = path.join(__dirname, 'participants.csv');
const OUTPUT_DIR = path.join(__dirname, 'tests_output');
const SUCCESS_LOG_PATH = path.join(OUTPUT_DIR, 'success.log');
const ERROR_CSV_PATH = path.join(OUTPUT_DIR, 'error.csv');
const EVENTBRITE_LOGIN_URL = 'https://www.eventbrite.fr/signin/';
const EVENTBRITE_EVENT_URL = 'https://www.eventbrite.fr/attendees-add?eid=1299223766259'; // SET EVENT URL HERE
const CHROME_DEBUGGER_URL = 'http://localhost:9222';
const EVENTBRITE_EMAIL = '<EMAIL>';// SET LOGIN HERE
const EVENTBRITE_PASSWORD = ''; // SET PASSWORD HERE

// Créer le dossier de sortie s'il n'existe pas
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

// Fonction pour attendre un peu (utile pour les animations)
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Fonction pour vérifier si Chrome est disponible avec le débogage à distance
function checkChromeDebuggerAvailable() {
  return new Promise((resolve) => {
    const req = http.get(`${CHROME_DEBUGGER_URL}/json/version`, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve(true);
        } catch (e) {
          resolve(false);
        }
      });
    });

    req.on('error', () => {
      resolve(false);
    });

    req.end();
  });
}

// Fonction pour lire les participants depuis le fichier CSV
const readParticipantsFromCSV = () => {
  return new Promise((resolve, reject) => {
    const participants = [];
    fs.createReadStream(CSV_FILE_PATH)
      .pipe(csv({
        mapHeaders: ({ header }) => header.trim(),
        mapValues: ({ value }) => value.trim(),
      }))
      .on('data', (data) => {
        const participant = {
          firstName: data.firstName || '',
          lastName: data.lastName || '',
          email: data.email || '',
          flag: data.flag && data.flag.toLowerCase() === 'true',
        };
        participants.push(participant);
      })
      .on('end', () => {
        resolve(participants);
      })
      .on('error', (error) => {
        reject(error);
      });
  });
};

// Fonction pour logger les succès
const logSuccess = (email) => {
  fs.appendFileSync(SUCCESS_LOG_PATH, `${email}\n`);
  console.log(`Succès enregistré pour: ${email}`);
};

// Fonction pour logger les erreurs
const logError = (participant) => {
  // Vérifier si error.csv existe
  const fileExists = fs.existsSync(ERROR_CSV_PATH);

  // Créer l'en-tête CSV si le fichier n'existe pas
  if (!fileExists) {
    fs.appendFileSync(ERROR_CSV_PATH, 'firstName,lastName,email,flag\n');
  }

  // Ajouter les données du participant à error.csv
  const csvLine = `"${participant.firstName}","${participant.lastName}","${participant.email}","${participant.flag}"\n`;
  fs.appendFileSync(ERROR_CSV_PATH, csvLine);
  console.log(`Erreur enregistrée pour: ${participant.email}`);
};

// Fonction pour vérifier le succès
const verifySuccess = async (frame) => {
  try {
    // Attendre un peu pour s'assurer que la page a eu le temps de se mettre à jour
    await wait(2000);

    // Vérifier si le message de succès est présent dans la frame
    const isSuccess = await frame.waitForFunction(() => {
      const pageContent = document.body.innerText.toLowerCase();
      console.log('Contenu de la page pour vérification:', pageContent);
      return pageContent.includes('réussi') || pageContent.includes('success') || pageContent.includes('besoin de plus de détails');
    });

    console.log(`Vérification du succès: ${isSuccess ? 'RÉUSSI' : 'ÉCHEC'}`);
    return isSuccess;
  } catch (error) {
    console.error('Erreur lors de la vérification du succès:', error);
    return false;
  }
};

async function rejectCookies(page) {
  // rejeter les cookies si nécessaire
  try {
    const rejectButton = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      return buttons.find(button => button.textContent.trim().toLowerCase() === 'tout rejeter' || button.textContent.trim().toLowerCase() === 'reject all');
    });

    if (rejectButton) {
      console.log('Clic sur le bouton "Tout rejeter"...');
      await rejectButton.click();
      await wait(500);
    } else {
      console.log('Bouton "Tout rejeter" non trouvé');
    }
  } catch (e) {
    console.log('Pas de bannière de cookies ou erreur:', e.message);
  }
}

// Fonction pour se connecter à Eventbrite
const loginToEventbrite = async (page) => {
  console.log('Navigation vers Eventbrite...');
  await page.goto(EVENTBRITE_LOGIN_URL, { waitUntil: 'networkidle2' });

  console.log('Remplissage du formulaire de connexion...');
  try {
    // Attendre que le champ email soit visible
    const email = await page.waitForSelector('input[type="email"]', {
      visible: true,
      timeout: 5000,
    }).catch(() => null);

    // Si le champ email n'est pas trouvé, on suppose qu'on est déjà authentifié
    if (!email) {
      console.log("Champ email non trouvé, on suppose qu'on est déjà authentifié");
      await page.screenshot({ path: path.join(OUTPUT_DIR, 'eventbrite_already_authenticated.png'), fullPage: true });
      return true;
    }

    if (email) await email.type(EVENTBRITE_EMAIL);

    // Cliquer sur le bouton de connexion
    const submitButton = await page.$('button[type="submit"]');
    if (submitButton) {
      await submitButton.click();
      await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 10000 }).catch(() => {
        console.log('Timeout lors de la navigation après connexion, mais on continue...');
      });

      // Saisir le mot de passe
      await page.type('input[type="password"]', EVENTBRITE_PASSWORD);
      const submitButtonPwd = await page.$('button[type="submit"]');
      await submitButtonPwd.click();

      // Attendre un peu pour s'assurer que tout est chargé
      await wait(2000);
      await page.screenshot({ path: path.join(OUTPUT_DIR, 'eventbrite_authenticated.png'), fullPage: true });
      return true;
    } else {
      console.log('Bouton de soumission non trouvé');
      return false;
    }
  } catch (e) {
    console.log("Erreur lors du remplissage du formulaire d'auth:", e.message);
    // On ne lance pas d'exception ici pour rester tolérant aux erreurs d'authentification
    console.log("On continue malgré l'erreur d'authentification");
    return true;
  }
};

// Fonction pour naviguer vers le formulaire d'événement
const navigateToEventForm = async (page) => {
  await page.goto(EVENTBRITE_EVENT_URL, { waitUntil: 'networkidle2' });

  // Prendre une capture d'écran
  console.log("Prise de capture d'écran...");
  await page.screenshot({ path: path.join(OUTPUT_DIR, 'eventbrite_event.png'), fullPage: true });

  // Saisir la quantité
  const quantityInput = await page.waitForSelector('input[type="text"]:not([disabled])[id^="quant_"]');
  await quantityInput.type('1');

  // Cliquer sur Continuer
  const continueAtt = await page.waitForSelector('#continue-attendee', { visible: true, timeout: 5000 });
  await rejectCookies(page);
  await continueAtt.click();

  // Attendre que le texte "Registration Information" soit visible
  console.log('Attente du texte "Registration Information"...');
  await page.waitForFunction(() => {
    const elements = Array.from(document.querySelectorAll('h1'));
    return elements.some(el => el.textContent && el.textContent.trim().includes('Registration Information') && el.checkVisibility()) &&
      elements.some(el => el.textContent && el.textContent.trim().includes('Add Attendees') && !el.checkVisibility());
  }, { timeout: 30000 });

  console.log('Texte "Registration Information" trouvé, recherche des champs de formulaire...');
};

// Fonction pour trouver l'iframe de checkout
const findCheckoutFrame = async (page) => {
  // Attendre que l'iframe apparaisse
  console.log("Attente de l'iframe...");
  await page.waitForSelector('iframe', { visible: true, timeout: 10000 });

  // Attendre un peu pour s'assurer que tous les iframes sont chargés
  await wait(2000);

  // Obtenir tous les iframes et trouver celui qui contient "checkout-external" dans son URL
  console.log("Recherche de l'iframe checkout-external...");

  let frame = null;
  let attempts = 0;
  const maxAttempts = 60;

  while (!frame && attempts < maxAttempts) {
    const frames = page.frames();
    console.log(`Nombre d'iframes trouvés: ${frames.length}`);

    for (let i = 0; i < frames.length; i++) {
      const frameUrl = frames[i].url();
      console.log(`Frame ${i}: URL = ${frameUrl}`);
      if (frameUrl.includes('checkout-external')) {
        console.log(`Frame ${i} correspond à checkout-external`);
        frame = frames[i];
        break;
      }
    }

    if (!frame) {
      console.log(`Tentative ${attempts + 1}/${maxAttempts}: iframe checkout-external non trouvé, attente de 2 secondes...`);
      await wait(2000);
      attempts++;
    }
  }

  if (!frame) {
    console.log('Iframe checkout-external non trouvé après 60 tentatives');
    return null;
  }

  // Attendre que les champs de formulaire soient visibles dans l'iframe
  try {
    console.log("Attente des champs de texte dans l'iframe...");
    await frame.waitForFunction(() => {
      // Sélectionner tous les champs de texte qui ne sont pas désactivés
      const inputs = Array.from(document.querySelectorAll('input[type="text"]'))
        .filter(input => !input.disabled && !input.readOnly);

      // Vérifier qu'il y a au moins 3 champs actifs
      return inputs.length >= 3;
    }, { timeout: 30000 });

    console.log("Champs de texte trouvés dans l'iframe");
    return frame;
  } catch (e) {
    console.log("Erreur lors de l'attente des champs de texte:", e.message);

    // Si nous n'avons pas trouvé les champs, essayons de déboguer
    console.log("Débogage des éléments dans l'iframe...");
    await frame.evaluate(() => {
      const allInputs = Array.from(document.querySelectorAll('input'));
      console.log(`Nombre total d'inputs: ${allInputs.length}`);

      const textInputs = Array.from(document.querySelectorAll('input[type="text"]'));
      console.log(`Nombre d'inputs de type text: ${textInputs.length}`);
    });

    return null;
  }
};

// Fonction pour remplir le formulaire d'événement
const fillEventForm = async (page, frame, participant) => {
  // Remplir les champs dans l'iframe
  console.log("Remplissage des champs dans l'iframe...");
  await page.keyboard.type(participant.firstName);
  await page.keyboard.press('Tab');
  await page.keyboard.type(participant.lastName);
  await page.keyboard.press('Tab');
  await page.keyboard.type(participant.email);
  await page.keyboard.press('Tab');

  console.log('Formulaire rempli directement via les éléments DOM');
  await wait(1000);

  // Copier les informations
  await frame.evaluate(async () => {
    const labels = Array.from(document.querySelectorAll('label'));
    const copyInfoLabel = labels.find(el => el.textContent.trim().toLowerCase().includes('copier les informations'));
    if (copyInfoLabel) {
      copyInfoLabel.click();
      setTimeout(() => {
        console.log('Clic sur le bouton "Copier les informations"');
        copyInfoLabel.click();
      }, 500);
    }
  });
  await wait(1000);

  // Sélectionner l'option MASTERCLASS ou Visite Libre
  if (participant.flag) {
    await frame.evaluate(() => {
      const labels = Array.from(document.querySelectorAll('label'));
      const masterclassLabel = labels.find(el => el.textContent.trim().includes('MASTERCLASS'));
      if (masterclassLabel) masterclassLabel.click();
    });
  } else {
    await frame.evaluate(() => {
      const labels = Array.from(document.querySelectorAll('label'));
      const visitLibreLabel = labels.find(el => el.textContent.trim().includes('Visite Libre'));
      if (visitLibreLabel) visitLibreLabel.click();
    });
  }

  // Cliquer sur le bouton d'inscription
  await frame.evaluate(() => {
    const buttons = Array.from(document.querySelectorAll('button'));
    const confirmButton = buttons.find(el => el.textContent.trim().toLowerCase().includes('inscrire'));
    console.log('confirm btn found: ' + !!confirmButton);
    if (confirmButton) confirmButton.click();
  });

  // Attendre un peu pour que la soumission soit traitée
  await wait(3000);
};

// Fonction pour traiter un participant
async function processParticipant(page, participant) {
  console.log(`Traitement du participant: ${participant.email}`);

  try {
    // Navigation vers le formulaire d'événement
    await navigateToEventForm(page);

    // Trouver l'iframe de checkout
    const frame = await findCheckoutFrame(page);
    if (!frame) {
      console.log(`Iframe de checkout non trouvé pour ${participant.email}`);
      logError(participant);
      return;
    }

    // Remplir le formulaire
    await fillEventForm(page, frame, participant);

    // Vérifier le succès
    const isSuccess = await verifySuccess(frame);

    // Logger le résultat
    if (isSuccess) {
      logSuccess(participant.email);
    } else {
      logError(participant);
    }

    // Prendre une capture d'écran
    await page.screenshot({
      path: path.join(OUTPUT_DIR, `eventbrite_${participant.email.replace(/[^a-zA-Z0-9]/g, '_')}.png`),
      fullPage: true,
    });

  } catch (error) {
    console.error(`Erreur lors du traitement de ${participant.email}:`, error);
    logError(participant);
  } finally {
    // await page.close();
  }
}

// Fonction principale
async function run() {
  // Vérifier si Chrome est disponible avec le débogage à distance
  const chromeAvailable = await checkChromeDebuggerAvailable();
  if (!chromeAvailable) {
    console.error("Chrome n'est pas disponible avec le débogage à distance.");
    console.error('Veuillez fermer toutes les instances de Chrome et exécuter:');
    console.error('google-chrome --remote-debugging-port=9222');
    console.error('Puis relancez ce script.');
    process.exit(1);
  }

  console.log('Connexion à Chrome existant...');

  // Se connecter à l'instance existante de Chrome
  const browser = await puppeteer.connect({
    browserURL: CHROME_DEBUGGER_URL,
    defaultViewport: null,
  });

  try {
    // Lire les participants depuis le CSV
    const participants = await readParticipantsFromCSV();
    console.log(`${participants.length} participants trouvés dans le CSV`);
    const page = await browser.newPage();
    // Connexion
    const loginSuccess = await loginToEventbrite(page);
    if (!loginSuccess) {
      console.log(`Impossible de se connecter, mais on continue...`);
    }

    // Traiter chaque participant séquentiellement
    for (const participant of participants) {
      await processParticipant(page, participant);
    }

  } catch (error) {
    console.error('Une erreur est survenue:', error);
  } finally {
    console.log('Chrome deconnecté.');
  }
}

run().catch(console.error);
