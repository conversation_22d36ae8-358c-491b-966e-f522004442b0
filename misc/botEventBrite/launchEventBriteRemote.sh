#!/bin/bash
set -e

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
    echo "Node.js n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
fi

# Vérifier si Puppeteer est installé
if ! grep -q "puppeteer" package.json 2>/dev/null; then
    echo "Installation de Puppeteer..."
    yarn add puppeteer
fi

# Créer le dossier de sortie
mkdir -p tests_output

# Vérifier si Chrome est déjà en cours d'exécution avec le débogage à distance
if ! curl -s http://localhost:9222/json/version > /dev/null; then
    echo "Chrome n'est pas en cours d'exécution avec le débogage à distance."
    echo "Veuillez fermer toutes les instances de Chrome et exécuter la commande suivante dans un autre terminal (ajuster user data dir):"
    echo ""
    echo "google-chrome --remote-debugging-port=9222 --user-data-dir=/home/<USER>/chrome-for-bot"
    echo ""
    echo "Puis appuyez sur Entrée pour continuer..."
    read
fi

# Exécuter le script Node.js
echo "Lancement du script Puppeteer pour Eventbrite..."
node eventbrite-remote.js

echo "Script terminé. Vérifiez tests_output/eventbrite_authenticated.png pour la capture d'écran."
