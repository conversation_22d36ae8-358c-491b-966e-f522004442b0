#!/bin/bash
set -e
trap 'last_command=$current_command; current_command=$BASH_COMMAND' DEBUG
# Affichage d'un message relatif à la dernière commande ayant échoué
trap 'echo -e "\e[31m!!! ==> Export des stats Pole Emploi - Erreur - Commande \"${last_command}\" en erreur ; code retour : $?.\e[0m"' ERR

read -p "Aller sur https://pole-emploi.io/compte et se connecter en <NAME_EMAIL> puis appuyez sur Entrée" -n1 -s
printf "\n"
read -p "Aller sur https://pole-emploi.io/compte/applications/2817 et récupérer les identifiants indiqués puis appuyez sur Entrée" -n1 -s
printf "\n"
echo "Saisissez l'identifiant"
read id
echo "Saisissez la clé secrète"
read pwd

token=`curl -f -s --request POST --url 'https://entreprise.pole-emploi.fr/connexion/oauth2/access_token?realm=%2Fpartenaire' --header 'content-type: application/x-www-form-urlencoded' --data grant_type='client_credentials' --data client_id="$id" --data client_secret="$pwd" --data "scope=application_$id api_infotravailv1" | jq .access_token`
token=${token//\"}

echo "Token API généré ; export des stats Pole Emploi"
json=`curl -f -s --request GET --url 'https://api.emploi-store.fr/partenaire/infotravail/v1/datastore_search_sql?sql=SELECT%20*%20FROM%20%22266f691f-bce8-4443-808e-8e5aa125cf17%22%20WHERE%20%22AREA_TYPE_CODE%22%20%3D%20'\''D'\''%20AND%20%22AREA_CODE%22%20%3D%20'\''69'\''' --header "authorization: Bearer $token"`

echo "JSON exporté ; génération du CSV"
echo $json | jq -r '.result.records | (map(keys) | add | unique) as $cols | map(. as $row | $cols | map($row[.])) as $rows | $cols, $rows[] | @csv' > stats_pole_emploi.csv
echo 'Données exportées dans stats_pole_emploi.csv'
echo 'Se rendre sur le fichier contenant les statistiques https://docs.google.com/spreadsheets/d/1yY-JA4skROH5waDeW-1gxEtR9VPFelL5Olrf3jksH3E/edit?usp=drive_web&ouid=109369190235546189806'
echo "Importer le CSV : fichier > importer > insérer de nouvelles feuille de calcul"


