/**
 * Renseigner :
 * - La clé Brevo dans un fichier .env à la racine de ce dossier, contenant BREVO_API_KEY=<clé>
 * - Un fichier emails.txt contenant les emails à blacklister (1 par ligne)
 * - Les expéditeurs à blacklister, dans la constante BLACKLIST_SENDERS de ce fichier
 */
require('dotenv').config();
const axios = require('axios');
const fs = require('fs');

const sleep = async (ms) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Constante définissant les expéditeurs à blacklister pour chaque utilisateur
const BLACKLIST_SENDERS = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

// Fonction pour lire la liste d'emails depuis un fichier
function readEmailList(filePath) {
  try {
    const data = fs.readFileSync(filePath, 'utf-8');
    return data.split('\n')
      .map(email => email.trim())
      .filter(email => !!email);
  } catch (err) {
    console.error('Erreur lors de la lecture du fichier:', err);
    return [];
  }
}

// Fonction pour blacklister un email via l'API Brévo
async function blacklistEmail(email) {
  const config = {
    headers: {
      'accept': 'application/json',
      'api-key': process.env.BREVO_API_KEY,
      'content-type': 'application/json',
    },
  };

  const data = {
    smtpBlacklistSender: BLACKLIST_SENDERS,
    emailBlacklisted: true,
  };

  try {
    const response = await axios.put(`https://api.brevo.com/v3/contacts/${email}`, data, config);
    console.log(`Email blacklisted: ${email} - got result: ${response.status} / ${response.data}`);
    return true;
  } catch (error) {
    console.error(`Échec pour ${email}: ${error.message} - body:`, error.response?.data);
    return false;
  }
}

// Fonction principale pour blacklister tous les emails de la liste
async function blacklistEmails(filePath) {
  const emailList = readEmailList(filePath);
  const failedEmails = [];

  for (const email of emailList) {
    const success = await blacklistEmail(email);
    if (success) {
      await sleep(100);
    } else {
      await sleep(1000);
      failedEmails.push(email);
    }
  }

  // Récapitulatif des erreurs à la fin du programme
  if (failedEmails.length > 0) {
    console.log('\nEmails en échec:');
    failedEmails.forEach(email => console.log(email));
  } else {
    console.log('Tous les emails ont été blacklistés avec succès.');
  }
}

// Exécution du script avec le fichier d'emails
const filePath = 'emails.txt'; // Chemin vers le fichier contenant la liste des emails
blacklistEmails(filePath).then(() => console.log('Fin.')).catch(e => console.error(e));
