---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ include "api.fullname" . }}-mysql-optimize
  labels:
    {{- include "api.labels" . | nindent 4 }}
spec:
  schedule: "15 1 * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: worker
            imagePullPolicy: {{ .Values.mariadb.image.pullPolicy | quote }}
            image: {{ include "call-nested" (list . "mariadb" "mariadb.image") }}
            {{- if .Values.cronjob.mariadbOptimize.resources }}
            resources: {{ toYaml .Values.cronjob.mariadbOptimize.resources | nindent 14 }}
            {{- end }}
            args:
            - bash
            - -c
            - "mysqlcheck --check --auto-repair -h preprod-api-mariadb --all-databases -h$MARIADB_HOST -uroot -p$MARIADB_ROOT_PASSWORD && mysqlcheck --analyze --auto-repair --all-databases -h$MARIADB_HOST -uroot -p$MARIADB_ROOT_PASSWORD  && mysqlcheck --optimize --all-databases -h$MARIADB_HOST -uroot -p$MARIADB_ROOT_PASSWORD"
            env:
              - name: MARIADB_ROOT_PASSWORD
                valueFrom:
                  secretKeyRef:
                    name: {{ include "call-nested" (list . "mariadb" "mariadb.secretName" . ) }}
                    key: mariadb-root-password
              - name: MARIADB_HOST
                value: {{ include "call-nested" (list . "mariadb" "mariadb.primary.fullname") }}
          restartPolicy: OnFailure
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ include "api.fullname" . }}-mysql-dump
  labels:
    {{- include "api.labels" . | nindent 4 }}
spec:
  schedule: "15 0 * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: worker
            imagePullPolicy: {{ .Values.mariadb.image.pullPolicy | quote }}
            image: {{ include "call-nested" (list . "mariadb" "mariadb.image") }}
            {{- if .Values.cronjob.mariadbOptimize.resources }}
            resources: {{ toYaml .Values.cronjob.mariadbOptimize.resources | nindent 14 }}
            {{- end }}
            volumeMounts:
              - name: data
                mountPath: /backup
            args:
            - bash
            - -c
            - |
              mkdir -p /backup/${APP_ENV}
              rm /backup/${APP_ENV}/*.sql.gz
              for i in $( echo "show databases;" | mysql -h$MARIADB_HOST -uroot -p$MARIADB_ROOT_PASSWORD | grep -v '^Database' |grep -v '^information_schema' )
              do
                mysqldump --single-transaction --add-drop-table -h$MARIADB_HOST -uroot -p$MARIADB_ROOT_PASSWORD $i | gzip > /backup/${APP_ENV}/$i-$( date +%Y%m%d-%H%M ).sql.gz
              done
            env:
              - name: MARIADB_ROOT_PASSWORD
                valueFrom:
                  secretKeyRef:
                    name: {{ include "call-nested" (list . "mariadb" "mariadb.secretName" . ) }}
                    key: mariadb-root-password
              - name: MARIADB_HOST
                value: {{ include "call-nested" (list . "mariadb" "mariadb.primary.fullname") }}
              - name: APP_ENV
                value: {{ .Values.spring.profileActive | quote }}
          restartPolicy: OnFailure
          volumes:
            - name: data
              persistentVolumeClaim:
                claimName: {{ include "api.fullname" . }}-mysql-dump

