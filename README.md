# ODAS

## Application
Le projet est composé de 3 applications :

- l'[api](./api), en Java avec Spring Boot
- le [back-office](./back-office), en Javascript avec VueJS

L'ensemble des dépendances JS est géré par des [workspaces Yarn](https://yarnpkg.com/lang/en/docs/cli/workspaces/).

L'API est décrite en format openAPI v3 dans [api.yaml](./api.yaml). Cela permet la génération de DTOs, controllers, et
d'un client, autant en javascript qu'en Java. L'installation du plugin IntelliJ Lombok est nécessaire pour la génération
du code Java.

Pour générer le client JS :

```bash
(cd ./common && ./generateClient.sh)
```

La génération Java est gérée par Maven : `mvn clean compile`.

Plusieurs Dockerfile sont disponibles, mais ils sont destinés à la CI et pas au développement.

Les différents services nécessaires à l'exécution de l'application en local peuvent être lancés via docker-compose : `docker-compose up -d`
Il est nécessaire de connecter docker au registry de Gitlab
```
docker login registry.gitlab.com
```

- Traefik permet d'accéder aux services définis dans le [docker-compose.yml](./docker-compose.yml) en ajoutant `.localhost` au nom du service.

Si vous souhaitez avoir Graylog en plus, utilisez cette
commande : `docker-compose -f docker-compose.yml -f docker-compose.graylog.yml up -d`. Après son lancement,
sur [graylog.localhost](http://graylog.localhost/system/inputs) (identifiants admin/admin) : ajouter une input de
type `GELF UDP`

## Tests End-to-end

Les tests end-to-end se trouvent dans le dossier [`tests`](./tests) et peuvent être lancés avec cette
commande : `(cd tests && ./deployE2e.sh build)`.

Dans le détail, ce script :

- Build (API, BO, FO, test) ou récupère (Keycloak) les images docker adéquates
- Démarre chacun des services
- Démarre les tests

Les répertoires contenant les scénarios et toutes les captures sont montés au traver d'un volume `docker-compose`.

Pour lancer un test e2e unique :

- Désactiver la `trap` dans [le script de lancement des tests](./tests/deployE2e.sh)
- Lancer tous les tests
- Lancer la
  commande `docker exec "local_yarn_test_1" sh -c './node_modules/.bin/nightwatch -c conf/nightwatch.conf.js --test scenarios/XXX_nom_du_test.js'`

Si les tests échouent, le build échoue ; les captures d'écran sont accessibles dans les artifact du build correspondant.
Plus de détail ici : https://docs.gitlab.com/ee/user/project/pipelines/job_artifacts.html#browsing-artifacts, ou
directement sur `https://gitlab.com/erhgo/erhgo/-/jobs/<JOB_ID>/artifacts/browse/tests/vrt/`).

## Update BDD

Les scripts de mise à jour de la base de données sont réalisés à partir du plugin maven liquibase. Le processus est "
semi-automatique" :

- Supprimer s'il existe `src/main/resources/liquibase-diffChangeLog.mariadb.sql`
- Passer la conf `spring.jpa.hibernate.ddl-auto` à `'update'` dans `application.yml`
- Lancer les test d'intégration en debug avec un breakpoint sur un test
- docker ps pour récupérer le port utilisé par test container
- màj liquibase.properties
- Faire appel au plugin maven liquibase (depuis le répertoire `api`) : `mvn liquibase:diff`
- Le fichier `src/main/resources/liquibase-outputChangeLog.mariadb.sql` est créé
- Le déplacer en le renommant dans `src/main/resources/db/changelog/db.changelog-1.x.sql`
- L'inclure dans `src/main/resources/db/changelog/db.changelog-master.sql`
- Remplacer les auteurs par `odas` (conf malheureusement non prise en compte par le plugin)
- Reverter les changements dans la bdd, remettre la conf `spring.jpa.hibernate.ddl-auto` à `'none'`
  dans `application.yml` (ou ajouter manuellement la ligne adéquate à la table de métadonnées Liquibase)

## Écraser les données avec la prod

Pour écraser les données de staging avec celles de prod :

- Whitelister l'ip auprès de Jetpulp
- Récupérer la clé privée SSH permettant de se connecter à Rancher
- Staging : `ssh -ti ~/.ssh/id_rsa_deploy  odas-e2e@************** "sudo /usr/local/bin/import_prod_in_staging.sh"`
- Démo : `/usr/local/bin/import_prod_in_demo.sh`
- Post opérations :
  - script d'update SQL des clients & mot de passe
  - relancer les diverses indexation

## Problème de rate limit sur docker hub

Pour vérifier l'état de la limite de téléchargement d'images docker :

- `TOKEN=$(curl --user 'erhgo:************************************' "https://auth.docker.io/token?service=registry.docker.io&scope=repository:ratelimitpreview/test:pull" | jq -r .token)`
- `curl --head -H "Authorization: Bearer $TOKEN" https://registry-1.docker.io/v2/ratelimitpreview/test/manifests/latest`
- Source : https://docs.docker.com/docker-hub/download-rate-limit/

## Mode opératoire pour développer et déployer un patch

Mode op branche de fix :

- Supprimer localement et sur le remote la branchge fix (gir br -D fix & git push origin :fix)
- Récupérer la version à patcher en prod (exx pour api : https://api.jenesuispasuncv.fr/api/odas/public/version)
- `git co <id du commit= la version à patcher>`
- `git co -B fix`
- Faire les corrections
- `git pu` + créer MR (pour que la CI génère l'image à déployer) sans rebaser sur master (à condition de les faire
  passer en local, si urgence il y a, on peut ajouter un -DskipTests=true à la commande maven du .gitlab-ci de api)
- Une fois l'image buildée, à la racine du projet : ./deploy.sh fix staging api
- Tester sur staging
- Si OK =>  `./deploy.sh fix prod api`
- Une fois déploiement OK :
  - rebase sur master
  - Supprimer le commit skippant les tests
  - Merger sur master la branche fix comme une MR normale

## Intégration Trimoji

Pour tester, il faut 2 varenv :

- `TRIMOJI_API_KEY=...`: la la clé d'API, à récupérer

- `TRIMOJI_JSON_TEMPLATE=`: un json ; ajuster le `customer_token` à celui fourni
  dans https://panel.trimoji.fr/marketplace > `Copier ma clé d'API`

```json
{
  "candidate_email": "%USER_EMAIL%",
  "candidate_first_name": "%USER_FIRSTNAME%",
  "candidate_last_name": "%USER_LASTNAME%",
  "customer_token": "f3b06b20-a889-460b-b51d-7c5f415d6ded",
  "is_trimoji_sending": false,
  "callback_url": "%CALLBACK_URL%",
  "no_result": false,
  "test_duration": "short",
  "metadatas": {
    "candidate_id": "%USER_ID%"
  }
}

```

- Pour visualiser les icones donnant accès au pdf en local :

```sql 
update UserProfile
set softSkillsPdfUrl = 'https://api.teambuildr.fr/pdf/serve/personalityTest/95ced126-2ddb-4d2c-ab0c-7eefab75007f'
where endedDate IS NOT NULL
  AND softSkillsPdfUrl IS NULL; 
```
