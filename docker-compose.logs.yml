services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.17.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
      - http.host=0.0.0.0
    ports:
      - "9200:9200"
    networks:
      - local

  logstash:
    image: docker.elastic.co/logstash/logstash:8.17.0
    ports:
      - "5044:5044"
    networks:
      - local
    depends_on:
      - elasticsearch
    volumes:
      - ./logstash/pipeline:/usr/share/logstash/pipeline
    environment:
      - xpack.monitoring.enabled=false

  kibana:
    image: docker.elastic.co/kibana/kibana:8.17.0
    ports:
      - "5601:5601"
    networks:
      - local
      - webgateway
    depends_on:
      - elasticsearch
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:5601/api/status" ]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    labels:
      traefik.docker.network: "erhgo_webgateway"
      traefik.backend: kibana
      traefik.frontend.rule: 'Host:kibana.localhost'
      traefik.enable: 'true'
      traefik.port: 5601

  filebeat:
    image: docker.elastic.co/beats/filebeat:8.17.0
    command: ["filebeat", "-e", "--strict.perms=false"]
    volumes:
      - ./filebeat/config/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - ./logs:/var/log/app:ro
    networks:
      - local
    depends_on:
      - logstash
    user: root

networks:
  local:
  webgateway:
