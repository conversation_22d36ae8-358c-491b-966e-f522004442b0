version: '3.5'
services:
  keycloak-db:
    image: postgres:13
    container_name: erhgo_keycloak_db
    ports:
      - '5432:5432'
    volumes:
      - 'postgres_data:/var/lib/postgresql/data'
    environment:
      POSTGRES_DB: keycloak
      POSTGRES_USER: keycloak
      POSTGRES_PASSWORD: password
    networks:
      - local
    labels:
      traefik.enable: 'false'
    restart: always
  keycloak:
    image: erhgo/authentication:develop
    container_name: erhgo_keycloak
    hostname: auth.localhost
    entrypoint: /bin/bash
    command: "/opt/keycloak/bin/kc.sh start-dev"
    expose:
      - 8080
    environment:
      REALM_ID: odas.localhost.app
      env: develop
      KC_DB: postgres
      KC_HOSTNAME: auth.localhost
      KC_DB_URL: ********************************************
      KC_DB_USERNAME: keycloak
      KC_DB_PASSWORD: password
      KEYCLOAK_ADMIN: admin
      <PERSON>OA<PERSON>_ADMIN_PASSWORD: Pa55w0rd
      KC_LOG_LEVEL: INFO
      ROOT_LOGLEVEL: INFO
      #https://www.keycloak.org/server/reverseproxy
      # none est censé être la valeur par défaut, mais lorsqu'on le met manuellement le proxy n'est pas désactivé
      #KC_PROXY: none
    depends_on:
      - keycloak-db
    networks:
      - local
      - webgateway
    labels:
      traefik.docker.network: "erhgo_webgateway"
      traefik.backend: keycloak
      traefik.frontend.rule: 'Host:auth.localhost'
      traefik.enable: 'true'
      traefik.port: 8080
    restart: always
  mailhog:
    image: mailhog/mailhog
    container_name: erhgo_dev_mailer
    networks:
      - local
      - webgateway
    expose:
      - 1025
      - 8025
    labels:
      traefik.docker.network: "erhgo_webgateway"
      traefik.backend: mailhog
      traefik.frontend.rule: 'Host:mailhog.localhost'
      traefik.enable: 'true'
      traefik.port: 8025
    restart: always
  traefik:
    image: 'traefik:v1.7.18'
    container_name: traefik
    ports:
      - '80:80'
      - '8000:8080'
    command: '--web --docker --logLevel=INFO'
    volumes:
      - '/var/run/docker.sock:/var/run/docker.sock'
    networks:
      - webgateway
    restart: always
  mariadb:
    image: mariadb:11.4.4
    container_name: erhgo_dev_mariadb
    volumes:
      - 'mariadb_data:/var/lib/mysql'
    environment:
      MYSQL_USER: odas
      MYSQL_DATABASE: odas
      MYSQL_PASSWORD: password
      MYSQL_ROOT_PASSWORD: password
      MARIADB_AUTO_UPGRADE: 1
    networks:
      - local
      - webgateway
    ports:
      - '3306:3306'
    labels:
      traefik.docker.network: "erhgo_webgateway"
      traefik.backend: mariadb
      traefik.frontend.rule: 'Host:mariadb.localhost'
      traefik.enable: 'true'
      traefik.port: 3306
    command: ['mariadbd', '--character-set-server=utf8mb4', '--collation-server=utf8mb4_unicode_ci']
    restart: always
  adminer:
    image: adminer
    container_name: erhgo_dev_adminer
    networks:
      - local
      - webgateway
    ports:
      - 9876:8080
    labels:
      traefik.docker.network: "erhgo_webgateway"
      traefik.backend: adminer
      traefik.frontend.rule: 'Host:adminer.localhost'
      traefik.enable: 'true'
      traefik.port: 9876
networks:
  local:
  webgateway:
volumes:
  mariadb_data: null
  postgres_data: null
