summary: List activity labels
operationId: listActivityLabels
parameters:
  - $ref: '../../../parameters/path/ActivityType.yaml'
  - $ref: '../../../parameters/query/CapacityIds.yaml'
  - $ref: '../../../parameters/query/IsCapacityRecursive.yaml'
  - $ref: '../../../parameters/query/Filter.yaml'
  - $ref: '../../../parameters/query/UserFilter.yaml'
  - $ref: '../../../parameters/query/Page.yaml'
  - $ref: '../../../parameters/query/Size.yaml'
  - $ref: '../../../parameters/query/SortBy.yaml'
  - $ref: '../../../parameters/query/SortDirection.yaml'
responses:
  200:
    description: The activity labels' list
    content:
      application/json:
        schema:
          $ref: '../schemas/ActivityLabelPage.yaml'

