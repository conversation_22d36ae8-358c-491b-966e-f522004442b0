summary: Merge activities
operationId: mergeActivities
requestBody:
  required: true
  content:
    application/json:
      schema:
        title: mergeActivityCommand
        type: object
        required:
          - sourceActivities
          - mergedActivity
        properties:
          sourceActivities:
            type: array
            items:
              type: string
              format: uuid
              nullable: false
            minItems: 2
            maxItems: 6
          mergedActivity:
            $ref: '../schemas/SaveActivityCommand.yaml'
responses:
  204:
    description: Activity is created
