summary: Update an existing context
operationId: updateContext
parameters:
  - $ref: '../../../parameters/path/ContextId.yaml'
requestBody:
  required: true
  description: Context data
  content:
    application/json:
      schema:
        $ref: '../schemas/SaveContextCommand.yaml'
responses:
  200:
    description: Context was successfully updated
    content:
      application/json:
        schema:
          $ref: '../schemas/Context.yaml'
  400:
    description: Invalid data
  500:
    description: Context update failed
