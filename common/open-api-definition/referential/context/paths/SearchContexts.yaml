summary: List contexts, search in title
operationId: searchContexts
parameters:
  - in: query
    name: query
    schema:
      type: string
    required: true
  - $ref: '../../../parameters/query/CategoryCode.yaml'
responses:
  200:
    description: The contexts matching query
    content:
      application/json:
        schema:
          type: array
          items:
            $ref: '../schemas/Context.yaml'
