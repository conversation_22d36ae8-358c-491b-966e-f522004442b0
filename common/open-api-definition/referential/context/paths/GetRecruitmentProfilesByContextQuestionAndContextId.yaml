summary: Get list of recruitments profiles used in contextQuestion for given context id
operationId: getRecruitmentProfilesByContextQuestionAndContextId
parameters:
  - $ref: '../../../parameters/path/QuestionId.yaml'
  - $ref: '../../../parameters/path/ContextId.yaml'
responses:
  200:
    description: List of recruitments profiles
    content:
      application/json:
        schema:
          type: array
          items:
            $ref: '../../../recruitment/schemas/RecruitmentProfileSummary.yaml'
