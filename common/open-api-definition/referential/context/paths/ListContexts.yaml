summary: List contexts
operationId: listContexts
parameters:
  - $ref: '../../../parameters/query/Size.yaml'
  - $ref: '../../../parameters/query/Page.yaml'
  - $ref: '../../../parameters/query/SortBy.yaml'
  - $ref: '../../../parameters/query/SortDirection.yaml'
  - $ref: '../../../parameters/query/Filter.yaml'
  - $ref: '../../../parameters/query/UserFilter.yaml'
responses:
  200:
    description: The contexts' list
    content:
      application/json:
        schema:
          $ref: '../schemas/ContextsPage.yaml'
