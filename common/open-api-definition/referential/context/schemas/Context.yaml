allOf:
  - $ref: '../../../misc/schemas/Auditing.yaml'
  - type: object
    required:
      - id
      - code
      - title
      - description
      - categoryLevel
    properties:
      id:
        type: string
        format: uuid
      code:
        type: string
        minLength: 8
      title:
        type: string
        minLength: 1
        maxLength: 50
      description:
        type: string
        minLength: 1
        maxLength: 2000
      categoryLevel:
        $ref: './ContextCategoryLevel.yaml'
      origin:
        $ref: './ReferentialElementOrigin.yaml'
