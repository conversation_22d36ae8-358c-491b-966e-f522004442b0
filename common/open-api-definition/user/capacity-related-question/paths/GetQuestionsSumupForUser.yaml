summary: Get questions summary for authenticated user (FO)
operationId: getQuestionsSumupForUser
parameters:
  - $ref: '../../../parameters/path/UserId.yaml'
responses:
  200:
    description: The questions sumup for user
    content:
      application/json:
        schema:
          title: questionsSumupForUser
          type: object
          properties:
            totalQuestionsCount:
              type: integer
            userAnsweredQuestionsCount:
              type: integer
