summary: Get questions summary for authenticated user (FO)
operationId: getQuestionsForUser
parameters:
  - $ref: '../../../parameters/query/QuestionType.yaml'
  - $ref: '../../../parameters/query/OrganizationCodeQueryOptional.yaml'
responses:
  200:
    description: The questions for user
    content:
      application/json:
        schema:
          type: array
          items:
            $ref: '../schemas/QuestionSummaryForUser.yaml'
