type: object
required:
  - id
  - title
  - responses
properties:
  id:
    type: string
    format: uuid
  title:
    type: string
    minLength: 1
  responses:
    type: array
    items:
      title: CapacityRelatedQuestionResponseForUser
      type: object
      required:
        - id
        - title
        - selected
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
          minLength: 1
        selected:
          type: boolean
