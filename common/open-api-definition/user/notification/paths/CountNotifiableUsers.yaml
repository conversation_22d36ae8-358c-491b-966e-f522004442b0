summary: Count users matching the request and having notifiable smartphone
operationId: countNotifiableUsers
parameters:
  - in: query
    name: query
    schema:
      type: string
  - in: query
    name: filters
    schema:
      type: string
  - in: query
    name: aroundLatLng
    schema:
      type: string
  - in: query
    name: aroundRadius
    schema:
      type: integer
responses:
  200:
    description: users count
    content:
      application/json:
        schema:
          type: object
          title: NotifiableUsersCount
          properties:
            totalUsers:
              type: integer
            notifiableUsers:
              type: integer
