summary: Send a notification to every user matching selection
operationId: sendNotificationsToUsersSelection
requestBody:
  required: true
  content:
    application/json:
      schema:
        type: object
        title: sendNotificationsToUsersSelectionCommand
        required:
          - query
          - subject
          - content
        properties:
          usersSelectionQuery:
            $ref: "../schemas/AlgoliaQuery.yaml"
          subject:
            type: string
            minLength: 4
          content:
            type: string
            minLength: 4
          link:
            type: string
            format: uri
responses:
  204:
    description: Notifications sent
