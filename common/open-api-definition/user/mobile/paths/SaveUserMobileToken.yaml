summary: Create or update user mobile app token
operationId: saveUserMobileToken
requestBody:
  required: true
  content:
    application/json:
      schema:
        title: saveUserMobileTokenCommand
        type: object
        required:
          - token
          - userId
        properties:
          id:
            type: string
            format: uuid
          token:
            type: string
          userId:
            type: string
responses:
  204:
    description: The user mobile token was created

