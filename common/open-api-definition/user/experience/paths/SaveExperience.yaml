summary: Save the experience type for a user
operationId: saveExperience
requestBody:
  required: true
  content:
    application/json:
      schema:
        title: saveExperienceCommand
        type: object
        required:
          - userId
          - jobTitle
          - experienceType
        properties:
          userId:
            type: string
          experienceType:
            $ref: '../schemas/ExperienceType.yaml'
          durationInMonths:
            type: integer
            format: int32
            minimum: 0
            maximum: 600
          startDate:
            type: string
            format: date
          endDate:
            type: string
            format: date
          organizationName:
            type: string
          erhgoOccupationId:
            type: string
            format: uuid
          experienceId:
            type: string
            format: uuid
          jobTitle:
            type: string
responses:
  204:
    description: Saved
    content:
      application/json:
        schema:
          $ref: '../schemas/ExperienceDetails.yaml'
  400:
    description: Invalid job title
