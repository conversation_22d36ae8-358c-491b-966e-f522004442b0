type: object
properties:
  id:
    type: string
    format: uuid
  jobTitle:
    type: string
  organizationName:
    type: string
  experienceType:
    $ref: './ExperienceType.yaml'
  activities:
    type: array
    items:
      $ref: '../../../referential/activity/schemas/ActivityLabelWithCapacities.yaml'
  erhgoOccupationId:
    type: string
    format: uuid
  erhgoOccupationTitle:
    type: string
  erhgoOccupationTotalCapacities:
    type: integer
  erhgoOccupationMasteryLevel:
    $ref: '../../../classifications/erhgo-occupation/schemas/MasteryLevel.yaml'
  durationInMonths:
    type: integer
    format: int32
  startDate:
    type: string
    format: date
  endDate:
    type: string
    format: date
