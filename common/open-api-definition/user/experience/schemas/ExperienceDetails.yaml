type: object
required:
  - id
  - jobTitle
  - experienceType
properties:
  id:
    type: string
    format: uuid
  jobTitle:
    type: string
  erhgoOccupationId:
    type: string
    format: uuid
  erhgoOccupationTitle:
    type: string
  organizationName:
    type: string
  experienceType:
    $ref: './ExperienceType.yaml'
  durationInMonths:
    type: integer
    format: int32
  startDate:
    type: string
    format: date
  endDate:
    type: string
    format: date
  activities:
    type: array
    items:
      $ref: '../../../referential/activity/schemas/ActivityLabelSummary.yaml'
  isOccupationBlacklisted:
    type: boolean
    default: false
