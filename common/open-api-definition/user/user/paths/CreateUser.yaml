summary: create new user
operationId: createUser
requestBody:
  required: true
  content:
    application/json:
      schema:
        title: createUserCommand
        type: object
        required:
          - firstName
          - lastName
          - email
          - password
          - group
        properties:
          firstName:
            type: string
          lastName:
            type: string
          email:
            type: string
          password:
            type: string
            format: password

responses:
  201:
    description: User has been created
