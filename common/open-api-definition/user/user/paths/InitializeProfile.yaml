summary: Initialize user profile and assign user to channel
operationId: initializeProfile
requestBody:
  required: true
  content:
    application/json:
      schema:
        $ref: "../schemas/InitializeProfileCommand.yaml"
responses:
  200:
    description: Profile initialization and user assignation to channel succeed
    content:
      application/json:
        schema:
          type: object
          title: initializedProfile
          properties:
            correctEmailSuggestion:
              type: string
            confirmationRequired:
              type: boolean
            registrationStep:
              $ref: '../schemas/UserRegistrationStateStep.yaml'
            mandatoryIdentity:
              type: boolean
            shouldAskForMailOptIn:
              type: boolean

