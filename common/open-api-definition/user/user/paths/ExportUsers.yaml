summary: get front office users
operationId: exportUsers
requestBody:
  required: true
  content:
    application/json:
      schema:
        title: usersExportRequest
        type: object
        properties:
          usersId:
            type: array
            nullable: true
            items:
              type: string
          deanonymizedUser:
            type: boolean
            default: false
responses:
  200:
    description: Export of users
    content:
      text/csv:
        schema:
          type: string
