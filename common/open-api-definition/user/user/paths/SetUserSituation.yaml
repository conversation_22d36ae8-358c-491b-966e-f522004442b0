summary: Set user situation
operationId: setUserSituation
requestBody:
  required: true
  content:
    application/json:
      schema:
        type: object
        title: setUserSituationCommand
        required:
          - situation
        properties:
          situation:
            $ref: '../schemas/Situation.yaml'
          delayInMonth:
            type: integer
responses:
  204:
    description: user situation updated
  400:
    description: specified situation is incorrect
