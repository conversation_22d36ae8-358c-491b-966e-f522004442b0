summary: get user profile (anti cv) pdf export
operationId: getUserProfileFOPdf
parameters:
  - $ref: '../../../parameters/path/UserId.yaml'
  - $ref: '../../../parameters/path/ForceAnonymous.yaml'
  - in: query
    name: handicap
    required: false
    schema:
      type: boolean

responses:
  200:
    description: PDF export of user profile anti cv
    content:
      application/pdf:
        schema:
          type: string
          format: binary
  404:
    description: user not found for uri
