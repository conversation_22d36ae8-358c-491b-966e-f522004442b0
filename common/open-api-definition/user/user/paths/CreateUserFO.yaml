summary: create new user FO
operationId: createUserFO
requestBody:
  required: true
  content:
    application/json:
      schema:
        title: createUserFOCommand
        type: object
        required:
          - phoneNumber
        properties:
          firstName:
            type: string
          lastName:
            type: string
          email:
            type: string
          phoneNumber:
            type: string

responses:
  200:
    description: Created user id
    content:
      application/json:
        schema:
          type: object
          title: createUserFOResult
          required:
            - userId
          properties:
            userId:
              type: string
  409:
    description: User already exist
