summary: Deactivate userSourcing
operationId: deactivateUserSourcing
requestBody:
  required: true
  content:
    application/json:
      schema:
        title: deactivateUserSourcingCommand
        type: object
        required:
          - userId
        properties:
          userId:
            type: string
          replacementUserId:
            type: string
responses:
  204:
    description: The user sourcing was deactivated
