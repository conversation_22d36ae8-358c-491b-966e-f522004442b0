summary: Create or update user note
operationId: saveUserNote
requestBody:
  required: true
  content:
    application/json:
      schema:
        title: saveUserNoteCommand
        type: object
        required:
          - userId
          - text
        properties:
          id:
            type: string
            format: uuid
          userId:
            type: string
          text:
            type: string
          organizationId:
            type: integer
            format: int64
responses:
  204:
    description: The user note was created

