summary: Get user erhgo classifications
operationId: getUserErhgoClassifications
parameters:
  - $ref: '../../../parameters/path/UserId.yaml'
responses:
  200:
    description: User erhgo classifications
    content:
      application/json:
        schema:
          type: object
          title: UserErhgoClassifications
          required:
            - acceptedErhgoClassifications
            - refusedErhgoClassifications
          properties:
            acceptedErhgoClassifications:
              type: array
              items:
                $ref: '../../../classifications/erhgo/schemas/ErhgoClassification.yaml'
            refusedErhgoClassifications:
              type: array
              items:
                $ref: '../../../classifications/erhgo/schemas/ErhgoClassification.yaml'

