summary: Regenerate user hashtags while keeping selected ones
operationId: regenerateUserHashtags
parameters:
  - $ref: '../../../parameters/path/UserId.yaml'
requestBody:
  required: true
  content:
    application/json:
      schema:
        $ref: '../schemas/Hashtags.yaml'
responses:
  200:
    description: Successfully regenerated hashtags
    content:
      application/json:
        schema:
          $ref: '../schemas/Hashtags.yaml'
  400:
    description: Invalid request
  404:
    description: User not found
  500:
    description: Hashtag generation failed
