summary: Get user candidatures
operationId: getUserCandidatures
parameters:
  - $ref: '../../../parameters/path/UserId.yaml'
  - in: query
    name: includes-spontaneous
    schema:
      type: boolean
    required: false
responses:
  200:
    description: User candidatures
    content:
      application/json:
        schema:
          type: array
          items:
            $ref: '../../../candidature/common/schemas/Candidature.yaml'
