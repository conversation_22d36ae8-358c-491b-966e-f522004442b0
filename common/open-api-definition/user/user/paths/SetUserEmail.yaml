summary: Change the email of given user
operationId: setUserEmail
parameters:
  - $ref: '../../../parameters/path/UserId.yaml'
requestBody:
  required: true
  content:
    application/json:
      schema:
        title: SetUserEmailCommand
        type: object
        required:
          - email
        properties:
          email:
            type: string
responses:
  200:
    description: User email is up to date, retrieves email verification state
    content:
      application/json:
        schema:
          title: SetUserEmailResult
          type: object
          properties:
            confirmationRequired:
              type: boolean
            correctEmailSuggestion:
              type: string
