summary: Get list of matching recruitments for optional authenticated user
operationId: getRecruitments
parameters:
  - $ref: '../../../parameters/query/Page.yaml'
  - $ref: '../../../parameters/query/Size.yaml'
  - $ref: '../../../parameters/query/Query.yaml'

responses:
  200:
    description: The list of recruitments
    content:
      application/json:
        schema:
          $ref: '../schemas/RecruitmentPage.yaml'
