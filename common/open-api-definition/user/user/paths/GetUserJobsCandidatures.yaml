summary: Get list of user candidatures for given user ID
operationId: getUserJobsCandidatures
parameters:
  - $ref: '../../../parameters/path/UserId.yaml'
  - $ref: '../../../parameters/query/OrganizationCodeQueryOptional.yaml'

responses:
  200:
    description: The list of jobs
    content:
      application/json:
        schema:
          type: array
          items:
            $ref: '../schemas/UserJobCandidature.yaml'
