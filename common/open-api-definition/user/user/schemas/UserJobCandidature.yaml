type: object
required:
  - organizationCode
  - candidatureId
  - jobTitle
  - employerTitle
  - recruiterTitle
  - visibleForUser
properties:
  organizationCode:
    type: string
  candidatureId:
    type: integer
    format: int64
  jobTitle:
    type: string
  employerTitle:
    type: string
  recruiterTitle:
    type: string
  refusedCandidature:
    type: boolean
  lastCandidatureNoteDate:
    type: string
    format: date-time
  candidatureState:
    $ref: '../../../candidature/job/schemas/GlobalCandidatureState.yaml'
  visibleForUser:
    type: boolean
