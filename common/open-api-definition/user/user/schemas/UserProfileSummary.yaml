type: object
properties:
  firstName:
    type: string
  lastName:
    type: string
  email:
    type: string
  phoneNumber:
    type: string
  contactTime:
    $ref: './ContactTime.yaml'
  birthDate:
    type: string
    format: date
  salary:
    type: integer
  delayInMonth:
    type: integer
  situation:
    $ref: './Situation.yaml'
  location:
    $ref: '../../../misc/schemas/Location.yaml'
  isSourceInitialized:
    type: boolean
  lastCandidatures:
    type: array
    items:
      title: CandidatureForProfile
      type: object
      properties:
        title:
          type: string
        organization:
          type: string
        recruitmentCode:
          type: string
        state:
          $ref: './UserCandidatureState.yaml'
  lastExperiences:
    type: array
    items:
      $ref: '../../experience/schemas/ExperienceSummary.yaml'
  behaviors:
    type: array
    items:
      $ref: '../../../referential/behavior/schemas/Behavior.yaml'
  criteria:
    items:
      $ref: "../../../criteria/schemas/UserCriteriaValue.yaml"
  includesMenus:
    type: array
    items:
      $ref: './OptionalMenu.yaml'
  isExportable:
    type: boolean
  incompleteInformations:
    type: array
    items:
      $ref: './IncompleteInformation.yaml'
  unreadNotificationsCount:
    type: integer
  blacklistedOccupations:
    type: array
    items:
      $ref: "../../../classifications/erhgo-occupation/schemas/ErhgoOccupationMinimumInfo.yaml"
  softSkillsStatus:
    type: string
    title: SoftSKillsStatus
    enum:
      - NOT_STARTED
      - NOT_FINISHED
      - WAITING
      - AVAILABLE
