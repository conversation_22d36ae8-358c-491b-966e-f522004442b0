type: object
properties:
  email:
    type: string
  firstName:
    type: string
    maxLength: 255
  lastName:
    type: string
    maxLength: 255
  phoneNumber:
    type: string
    maxLength: 255
  contactTime:
    $ref: './ContactTime.yaml'
  location:
    $ref: '../../../misc/schemas/Location.yaml'
  birthDate:
    type: string
    format: date
  salary:
    type: integer
  situation:
    $ref: './Situation.yaml'
  receiveJobOfferEmails:
    type: boolean
  source:
    type: string
  delayInMonth:
    type: integer
