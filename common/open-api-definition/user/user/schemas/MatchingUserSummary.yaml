type: object
required:
  - id
  - contactInformation
  - userProfileProgress
properties:
  id:
    type: string
  matchingRateInPercent:
    type: integer
  candidatureId:
    type: integer
    format: int64
  contactInformation:
    $ref: './GeneralInformation.yaml'
  userProfileProgress:
    $ref: './UserProfileProgress.yaml'
  alreadyReceivedProposalEmail:
    type: boolean
  missingCriteria:
    type: array
    items:
      type: string
  unknownCriteria:
    type: array
    items:
      type: string
  anyRefusedCandidature:
    type: boolean
