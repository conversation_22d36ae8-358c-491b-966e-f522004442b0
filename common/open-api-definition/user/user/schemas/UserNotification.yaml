type: object
required:
  - id
  - userId
  - createdDate
  - type
  - state
properties:
  id:
    type: string
    format: uuid
  userId:
    type: string
  createdDate:
    type: string
    format: date-time
  type:
    type: string
  state:
    $ref: './UserNotificationState.yaml'
  entityId:
    type: string
  entityType:
    $ref: './UserNotificationEntityType.yaml'
  entityData:
    type: object
    additionalProperties:
      type: string
