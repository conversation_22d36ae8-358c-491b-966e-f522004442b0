type: object
required:
  - firstName
  - lastName
  - email
  - transactionalBlacklisted
properties:
  creationDate:
    type: string
    format: date-time
  lastConnectionDate:
    type: string
    format: date-time
  firstName:
    type: string
  lastName:
    type: string
  email:
    type: string
  transactionalBlacklisted:
    type: boolean
  phoneNumber:
    type: string
  location:
    $ref: '../../../misc/schemas/Location.yaml'
  contactTime:
    $ref: './ContactTime.yaml'
  channels:
    type: array
    items:
      type: string
  isPrivate:
    type: boolean
  birthDate:
    type: string
    format: date
  smsBlacklisted:
    type: boolean
  salary:
    type: integer
  situation:
    $ref: './Situation.yaml'
  source:
    type: string
  delayInMonth:
    type: integer
  lastMobileConnexionDate:
    type: string
    format: date-time
