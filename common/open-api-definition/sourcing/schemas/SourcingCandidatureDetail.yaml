allOf:
  - $ref: './SourcingCandidatureItem.yaml'
  - type: object
    required:
      - keywordsKeys
      - candidatureNotes
    properties:
      numberOfExperiences:
        type: integer
      contracts:
        type: array
        items:
          $ref: '../../job/job/schemas/TypeContractCategory.yaml'
      workingTimes:
        type: array
        items:
          $ref: '../../job/job/schemas/WorkingTime.yaml'
      hasDrivingLicense:
        type: boolean
      candidatureNotes:
        type: array
        items:
          type: object
          title: SourcingCandidatureNote
          properties:
            userFullname:
              type: string
            content:
              type: string
            noteDateTime:
              type: string
              format: date-time
      completionScore:
        type: integer
      lastConnectionDate:
        type: string
        format: date-time
      recruitment:
        type: object
        title: recruitmentSumUp
        required:
          - recruitmentId
          - title
        properties:
          customQuestion:
            type: string
          recruitmentState:
            $ref: '../../recruitment/schemas/RecruitmentState.yaml'
          recruitmentId:
            type: integer
            format: int64
          publicationDate:
            type: string
            format: date-time
          title:
            type: string
          location:
            $ref: '../../misc/schemas/Location.yaml'
          baseSalary:
            type: integer
          maxSalary:
            type: integer
      previousCandidatures:
        type: array
        items:
          type: object
          title: SourcingCandidatureSumUp
          properties:
            jobTitle:
              type: string
            submissionDate:
              type: string
              format: date-time
            state:
              $ref: './SourcingCandidatureState.yaml'
            id:
              type: integer
              format: int64
