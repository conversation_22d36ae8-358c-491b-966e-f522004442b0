type: object
required:
  - anonymousCode
  - color
  - state
  - candidatureId
  - sectors
properties:
  generated:
    type: boolean
  candidatureId:
    type: integer
    format: int64
  anonymousCode:
    type: string
  color:
    type: string
  situation:
    $ref: '../../user/user/schemas/Situation.yaml'
  firstName:
    type: string
  lastName:
    type: string
  phone:
    type: string
  email:
    type: string
  lastActionDate:
    type: string
    format: date-time
  submissionDate:
    type: string
    format: date-time
  answer:
    type: string
  state:
    $ref: './SourcingCandidatureState.yaml'
  lastNote:
    type: string
  userId:
    type: string
  numberOfOfferCandidatures:
    type: integer
  hasSoftSkillPdf:
    type: boolean
  location:
    $ref: '../../misc/schemas/Location.yaml'
  sectors:
    type: array
    items:
      $ref: '../../sector/schemas/Sector.yaml'
discriminator:
  propertyName: sourcingCandidatureType
