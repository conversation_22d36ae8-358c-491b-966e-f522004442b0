type: object
title: commonSourcingRecruitmentInfos
properties:
  recruitmentState:
    $ref: '../../recruitment/schemas/RecruitmentState.yaml'
  recruitmentId:
    type: integer
    format: int64
  jobId:
    type: string
    format: uuid
  publicationEndDate:
    type: string
    format: date-time
  location:
    $ref: '../../misc/schemas/Location.yaml'
  baseSalary:
    type: integer
  maxSalary:
    type: integer
  title:
    type: string
  manager:
    $ref: '../schemas/SourcingUser.yaml'
  publicationDate:
    type: string
    format: date-time
  lastProcessingDate:
    type: string
    format: date-time
  lastProcessingType:
    $ref: '../../recruitment/schemas/ProcessingType.yaml'
