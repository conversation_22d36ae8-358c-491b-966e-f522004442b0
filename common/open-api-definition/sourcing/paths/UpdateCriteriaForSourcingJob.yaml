summary: Save criteria for sourcing job
operationId: updateCriteriaForSourcingJob
parameters:
  - $ref: '../../parameters/path/JobId.yaml'
  - in: query
    name: restrictToSourcingStep
    schema:
      $ref: '../../criteria/schemas/SourcingCriteriaStep.yaml'
    required: true
requestBody:
  required: true
  content:
    application/json:
      schema:
        type: array
        items:
          type: string
responses:
  204:
    description: success
