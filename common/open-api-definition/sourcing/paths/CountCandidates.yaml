summary: Count candidates for occupation & location search
operationId: countCandidates
parameters:
  - $ref: '../../parameters/query/ErhgoOccupationId.yaml'
  - in: query
    name: longitude
    schema:
      type: number
      format: float
    required: true
  - in: query
    name: latitude
    schema:
      type: number
      format: float
    required: true
  - in: query
    name: typeContractCategory
    schema:
      type: string
    required: false
  - in: query
    name: workingTimeType
    schema:
      type: string
    required: false
  - in: query
    name: criteria
    schema:
      type: array
      items:
        type: string
    required: false
  - in: query
    name: salaryMin
    schema:
      type: integer
    required: false
  - in: query
    name: salaryMax
    schema:
      type: integer
    required: false
  - in: query
    name: classifications
    schema:
      type: array
      items:
        type: string
    required: false
responses:
  200:
    description: Candidates count
    content:
      application/json:
        schema:
          type: object
          title: candidatesCount
          required:
            - count
          properties:
            count:
              type: integer
              format: int64
