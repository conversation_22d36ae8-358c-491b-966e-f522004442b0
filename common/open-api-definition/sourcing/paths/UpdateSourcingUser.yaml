summary: Update sourcing user
operationId: updateSourcingUser
requestBody:
  required: true
  content:
    application/json:
      schema:
        type: object
        title: updateSourcingUserCommand
        required:
          - fullname
          - email
        properties:
          fullname:
            type: string
          email:
            type: string
          phone:
            type: string
          preferences:
            $ref: '../schemas/SourcingPreferences.yaml'
responses:
  204:
    description: User has been updated in keycloak
