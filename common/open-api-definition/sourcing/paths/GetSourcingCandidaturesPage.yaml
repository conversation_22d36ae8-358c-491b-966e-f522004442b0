summary: Get candidatures for recruitment
operationId: getSourcingCandidaturesPage
parameters:
  - $ref: '../../parameters/query/Size.yaml'
  - $ref: '../../parameters/query/Page.yaml'
  - in: query
    name: sourcingCandidatureSort
    schema:
      $ref: '../schemas/SourcingCandidatureSort.yaml'
    required: false
  - in: query
    name: direction
    schema:
      $ref: '../../misc/schemas/SortDirection.yaml'
    required: false
  - in: query
    name: candidatureState
    schema:
      type: array
      items:
        $ref: '../schemas/SourcingCandidatureState.yaml'
    required: false
  - in: query
    name: searchQuery
    schema:
      type: string
    required: false
  - in: query
    name: recruitmentId
    schema:
      type: integer
      format: int64
    required: false
responses:
  200:
    description: Candidatures page
    content:
      application/json:
        schema:
          $ref: '../schemas/SourcingCandidaturePage.yaml'
