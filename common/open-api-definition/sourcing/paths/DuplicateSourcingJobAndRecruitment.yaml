summary: Duplicate Sourcing Job And Recruitment
operationId: duplicateSourcingJobAndRecruitment
requestBody:
  required: true
  description: Recruitment ID
  content:
    application/json:
      schema:
        type: object
        title: DuplicateSourcingJobAndRecruitmentCommand
        required:
          - recruitmentId
        properties:
          recruitmentId:
            type: integer
            format: int64
responses:
  201:
    description: SourcingJobAndRecruitment was successfully duplicated
    content:
      application/json:
        schema:
          $ref: '../schemas/SourcingJobAndRecruitment.yaml'
  400:
    description: Invalid data
  500:
    description: SourcingJobAndRecruitment duplicate failed
