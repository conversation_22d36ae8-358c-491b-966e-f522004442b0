summary: Get similar recruitments
operationId: getSimilarRecruitments
parameters:
  - $ref: '../../parameters/path/ErhgoOccupationId.yaml'
  - in: query
    name: longitude
    schema:
      type: number
      format: float
    required: true
  - in: query
    name: latitude
    schema:
      type: number
      format: float
    required: true
responses:
  200:
    description: Similar recruitments
    content:
      application/json:
        schema:
          type: array
          items:
            $ref: '../schemas/SourcingJobAndRecruitment.yaml'
