summary: get user profile pdf export for candidature (ie. anonymous state)
operationId: getCandidatureUserProfileCompetences
parameters:
  - $ref: '../../parameters/path/CandidatureId.yaml'
  - $ref: '../../parameters/path/ForceAnonymous.yaml'
responses:
  200:
    description: PDF export of user profile profile competences
    content:
      application/pdf:
        schema:
          type: string
          format: binary
  404:
    description: candidature not found for uri
