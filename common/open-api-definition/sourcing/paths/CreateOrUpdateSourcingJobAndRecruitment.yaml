summary: Create sourcing job & recruitment
operationId: createOrUpdateSourcingJobAndRecruitment
requestBody:
  required: true
  content:
    application/json:
      schema:
        $ref: '../schemas/CreateOrUpdateSourcingJobAndRecruitmentCommand.yaml'
responses:
  200:
    description: Created or updated job & recruitmentId
    content:
      application/json:
        schema:
          $ref: '../schemas/SourcingJobAndRecruitment.yaml'
