summary: Invite and create new user
operationId: inviteAndCreateNewUser
requestBody:
  required: true
  content:
    application/json:
      schema:
        type: object
        title: inviteAndCreateNewUserCommand
        required:
          - email
          - fullname
          - organizationCode
          - forceEmail
        properties:
          email:
            type: string
          fullname:
            type: string
          organizationCode:
            type: string
          forceEmail:
            type: boolean
responses:
  201:
    description: Invitation sent successfully
  409:
    description: Email already used
  500:
    description: Keycloak server error
  400:
    description: Email is invalid
  204:
    description: Email sent successfully and user was created
