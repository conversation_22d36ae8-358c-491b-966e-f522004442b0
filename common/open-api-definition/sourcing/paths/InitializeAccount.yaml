summary: Initialize sourcing account, create first 'free' job
operationId: initializeSourcingAccount
responses:
  200:
    description: Previously created job & recruitmentId
    content:
      application/json:
        schema:
          type: object
          title: initializedAccount
          required:
            - hasSiretFormatError
          properties:
            jobId:
              type: string
              format: uuid
            recruitmentId:
              type: integer
              format: int64
            hasSiretFormatError:
              type: boolean
