summary: Simulate sourcing filters
operationId: simulateSourcingFilters
parameters:
  - in: query
    name: occupationId
    schema:
      type: string
      format: uuid
    required: false
  - in: query
    name: classifications
    schema:
      type: array
      items:
        type: string
    required: false
  - in: query
    name: criteriaCodes
    schema:
      type: array
      items:
        type: string
    required: false
  - in: query
    name: typeContractCategoryAsString
    schema:
      type: string
    required: false
  - in: query
    name: workingTimeTypeAsString
    schema:
      type: string
    required: false
  - in: query
    name: masteryLevel
    schema:
      type: integer
    required: false
  - in: query
    name: longitude
    schema:
      type: number
      format: float
    required: false
  - in: query
    name: latitude
    schema:
      type: number
      format: float
    required: false
  - in: query
    name: radius
    schema:
      type: integer
      format: type32
    required: false
  - in: query
    name: salaryMin
    schema:
      type: integer
    required: false
  - in: query
    name: salaryMax
    schema:
      type: integer
    required: false
  - in: query
    name: capacityTolerance
    schema:
      type: number
      format: double
      minimum: 0
      maximum: 100
    required: false
  - in: query
    name: lastConnectionTimestamp
    schema:
      type: integer
      format: int64
    required: false
  - in: query
    name: activeSearch
    schema:
      type: boolean
    required: true
  - in: query
    name: withDetails
    schema:
      type: boolean
    required: true
  - in: query
    name: forcedTechnical
    schema:
      type: boolean
    required: true
  - in: query
    name: showTopTen
    schema:
      type: boolean
    required: true
responses:
  200:
    description: Simulating the sourcing filters
    content:
      application/json:
        schema:
          $ref: '../schemas/SourcingSimulatedResult.yaml'
