summary: Update sourcing organization
operationId: updateSourcingOrganization
requestBody:
  required: true
  content:
    application/json:
      schema:
        type: object
        title: updateSourcingOrganizationCommand
        properties:
          name:
            type: string
          siret:
            type: string
          description:
            type: string
          externalUrl:
            type: string
          gdprMention:
            type: string
          recruitmentId:
            type: integer
            format: int64
responses:
  200:
    description: Update command result
    content:
      application/json:
        schema:
          type: object
          title: updateOrganizationResult
          required:
            - hasSiretFormatError
          properties:
            hasSiretFormatError:
              type: boolean
