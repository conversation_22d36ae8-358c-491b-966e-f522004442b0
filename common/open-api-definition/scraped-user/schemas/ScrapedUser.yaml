type: object
required:
  - uuid
  - candidateId
properties:
  uuid:
    type: string
    format: uuid
  candidateId:
    type: string
  firstName:
    type: string
  lastName:
    type: string
  jobTitle:
    type: string
  location:
    type: string
  email:
    type: string
    format: email
  cvDownloadLink:
    type: string
  cvContent:
    type: string
  profileUrl:
    type: string
  errorMessage:
    type: string
  creationState:
    type: string
    enum:
      - ALREADY_EXISTS
      - CREATED
      - ERROR
      - NOT_INTERESTED
      - PENDING
