openapi: "3.0.0"
info:
  version: 1.0.0
  title: ODAS API

servers:
  - url: https://demo-api.odas.app/api/odas
    description: Serveur de démo
  - url: https://api.odas.app/api/odas
    description: Serveur de prod

paths:
  $ref: "./paths_index.yaml"
components:
  parameters:
    $ref: "./parameters_index.yaml"
  schemas:
    $ref: "./schemas_index.yaml"
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
security:
  - bearerAuth: [ ]
