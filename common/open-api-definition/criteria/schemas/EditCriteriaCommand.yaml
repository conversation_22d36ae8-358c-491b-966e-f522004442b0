type: object
required:
  - questionLabel
  - questionType
  - title
  - criteriaValues
properties:
  title:
    type: string
  questionLabel:
    type: string
  criteriaValues:
    type: array
    items:
      type: object
      title: editCriteriaValueCommand
      required:
        - code
        - titleForQuestion
        - icon
        - titleStandalone
        - titleForBO
      properties:
        code:
          type: string
        titleForQuestion:
          type: string
        titleStandalone:
          type: string
        titleForBO:
          type: string
        icon:
          type: string
