type: object
properties:
  criteria:
    type: array
    items:
      title: criteriaItemForCandidature
      type: object
      required:
        - selectedValuesCodesInRecruitment
        - selectedValuesCodesByUser
        - criteria
      properties:
        selectedValuesCodesInRecruitment:
          type: array
          items:
            type: string
        selectedValuesCodesByUser:
          type: array
          items:
            type: string
        criteria:
          $ref: ../../../criteria/schemas/Criteria.yaml
  availabilityForCandidature:
    $ref: ./AvailabilityForCandidature.yaml
  salary:
    type: object
    properties:
      recruitmentMin:
        type: integer
      recruitmentMax:
        type: integer
      desiredByUser:
        type: integer
