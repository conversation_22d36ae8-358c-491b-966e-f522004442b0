summary: Generate candidatures for candidate given recruitments
operationId: generateCandidaturesOnRecruitments
requestBody:
  required: true
  content:
    application/json:
      schema:
        $ref: '../schemas/GenerateCandidaturesOnRecruitmentsCommand.yaml'
responses:
  200:
    description: The candidature's identifiers
    content:
      application/json:
        schema:
          type: array
          items:
            title: generatedCandidatures
            type: object
            required:
              - candidatureId
              - jobId
              - recruitmentId
            properties:
              candidatureId:
                type: integer
                format: int64
              jobId:
                type: string
                format: uuid
              recruitmentId:
                type: integer
                format: int64
