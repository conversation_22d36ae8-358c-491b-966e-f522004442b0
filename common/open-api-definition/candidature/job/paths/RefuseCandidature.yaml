summary: Refuse a candidature
operationId: markCandidatureAsRefused
requestBody:
  required: true
  content:
    application/json:
      schema:
        title: refuseCandidatureCommand
        type: object
        required:
          - candidatureId
        properties:
          candidatureId:
            type: integer
            format: int64
          emailTemplate:
            $ref: '../../../misc/schemas/CustomEmailTemplate.yaml'
responses:
  204:
    description: candidature refused and sent an email if one was specified
