summary: Set contexts as met by this candidate
operationId: setContextsMet
parameters:
  - $ref: '../../../parameters/path/CandidatureId.yaml'
requestBody:
  required: true
  content:
    application/json:
      schema:
        type: array
        items:
          title: contextMet
          type: object
          required:
            - contextId
            - frequency
            - experiencesIds
          properties:
            contextId:
              type: string
              format: uuid
            frequency:
              $ref: '../../../referential/activity/schemas/Frequency.yaml'
            experiencesIds:
              type: array
              items:
                type: string
                format: uuid
responses:
  204:
    description: Contexts marked as met
