summary: Create or update candidature note
operationId: saveCandidatureNote
parameters:
  - $ref: '../../../parameters/path/CandidatureId.yaml'
requestBody:
  required: true
  content:
    application/json:
      schema:
        title: saveCandidatureNoteCommand
        type: object
        required:
          - id
          - text
        properties:
          id:
            type: string
            format: uuid
          text:
            type: string
            minLength: 1
responses:
  200:
    description: The candidature note was created
    content:
      application/json:
        schema:
          $ref: '../schemas/CandidatureNote.yaml'
