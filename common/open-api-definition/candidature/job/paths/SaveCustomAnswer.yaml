summary: Set candidate answer to recruitment custom question and create candidature if required
operationId: saveCustomAnswer
parameters:
  - $ref: '../../../parameters/path/RecruitmentId.yaml'
requestBody:
  required: true
  content:
    application/json:
      schema:
        type: string
        minLength: 1
responses:
  204:
    description: candidature created if required, answer is saved (the answer max length is managed by client)
