summary: Scrape offers from recruiter website
operationId: scrapeOffers
description: Get all offers from scraping using the URL website as ATS
requestBody:
  required: true
  content:
    application/json:
      schema:
        $ref: '../schemas/ScrapeOffersCommand.yaml'
responses:
  204:
    description: Successfully scraped offers
  400:
    description: Invalid scraping parameters
  500:
    description: Scraping failed
