type: object
properties:
  id:
    type: string
    format: uuid
  title:
    type: string
  location:
    type: string
  remoteId:
    type: string
  recruitmentId:
    type: integer
    format: int64
  recruitmentCreationState:
    $ref: './RecruitmentCreationState.yaml'
  lastModificationDate:
    type: string
    format: date-time
  description:
    type: string
  organizationDescription:
    type: string
  typeContractCategory:
    $ref: '../../job/job/schemas/TypeContractCategory.yaml'
  salaries:
    type: array
    items:
      type: integer
