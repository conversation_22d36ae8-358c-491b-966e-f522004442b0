type: object
title: extractedAtsData
properties:
  typeContractInformations:
    type: object
    additionalProperties:
      type: string
  descriptionParts:
    type: object
    additionalProperties:
      type: string
  organizationDescriptionParts:
    type: object
    additionalProperties:
      type: string
  occupationTitles:
    type: object
    additionalProperties:
      type: string
  localisationInformations:
    type: object
    additionalProperties:
      type: string
  otherInformations:
    type: object
    additionalProperties:
      type: string
  criteriaRelatedData:
    type: object
    additionalProperties:
      type: string
  salaryRelatedData:
    type: object
    additionalProperties:
      type: string
