summary: search esco occupations for ISCO code or URI
operationId: searchEscoOccupation
parameters:
  - $ref: '../../../parameters/query/Query.yaml'
responses:
  200:
    description: filtered list of esco occupations
    content:
      application/json:
        schema:
          type: array
          items:
            $ref: '../schemas/EscoOccupationSummary.yaml' # @FIXME: use a DTO without the activities (which are never returned here)
