summary: get esco occupation capacities
operationId: getEscoOccupationCapacities
parameters:
  - in: query
    name: uri
    schema:
      type: string
    required: true
responses:
  200:
    description: capacities of esco occupation
    content:
      application/json:
        schema:
          $ref: '../../../referential/capacity/schemas/CapacitiesResult.yaml'
  404:
    description: esco occupation not found for uri
