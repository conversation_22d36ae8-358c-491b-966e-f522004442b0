type: object
required:
  - uri
  - title
  - descriptionEN
properties:
  uri:
    type: string
  title:
    type: string
  skillType:
    type: string
  descriptionEN:
    type: string
    maxLength: 2000
  descriptionFR:
    type: string
    maxLength: 2000
  alternativeLabels:
    type: array
    items:
      type: string
  contexts:
    type: array
    items:
      $ref: '../../../referential/context/schemas/Context.yaml'
  behaviors:
    type: array
    items:
      $ref: '../../../referential/behavior/schemas/Behavior.yaml'
  activities:
    type: array
    items:
      $ref: '../../../referential/activity/schemas/ActivityLabelWithCapacities.yaml'
  noActivity:
    type: boolean
  noBehavior:
    type: boolean
  noContext:
    type: boolean
  updatedDate:
    type: string
    format: date-time
  lastModifiedBy:
    type: string
