type: object
required:
  - id
  - title
  - erhgoOccupationState
properties:
  id:
    type: string
    format: uuid
  title:
    type: string
  skillSize:
    type: integer
    format: int32
  qualifiedSkillNumber:
    type: integer
    format: int32
  notQualifiedSkillNumber:
    type: integer
    format: int32
  erhgoOccupationState:
    $ref: './ErhgoOccupationState.yaml'
  romeCodes:
    type: array
    items:
      type: string
