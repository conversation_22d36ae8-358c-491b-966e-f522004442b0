type: object
required:
  - id
  - title
  - erhgoOccupationState
  - activities
  - contexts
  - behaviors
  - alternativeLabels
  - criteriaValues
  - erhgoClassifications
properties:
  id:
    type: string
    format: uuid
  escoOccupations:
    type: array
    items:
      $ref: './../../esco/schemas/EscoOccupationWithIscoOccaptionSummary.yaml'
  title:
    type: string
  description:
    type: string
  alternativeLabels:
    type: array
    items:
      type: string
  romeOccupations:
    type: array
    items:
      $ref: './../../rome/schemas/RomeSummary.yaml'
  accessibleRomeOccupations:
    type: array
    items:
      $ref: './../../rome/schemas/RomeSummary.yaml'
  accessibleFromRomeOccupations:
    type: array
    items:
      $ref: './../../rome/schemas/RomeSummary.yaml'
  level:
    $ref: './MasteryLevel.yaml'
  erhgoOccupationState:
    $ref: './ErhgoOccupationState.yaml'
  skills:
    type: array
    items:
      $ref: '../../esco/schemas/Skill.yaml'
  activities:
    type: array
    items:
      $ref: './OccupationActivity.yaml'
  contexts:
    type: array
    items:
      $ref: './OccupationContext.yaml'
  behaviors:
    type: array
    items:
      $ref: './OccupationBehavior.yaml'
  updatedDate:
    type: string
    format: date-time
  lastModifiedBy:
    type: string
  behaviorsCategories:
    $ref: './ErhgoOccupationBehaviorsCategories.yaml'
  behaviorsDescription:
    type: string
  isVisibleForOrientation:
    type: boolean
    deprecated: true
  workEnvironments:
    type: array
    items:
      $ref: '../../../referential/work-environment/schemas/WorkEnvironment.yaml'
  isTechnical:
    type: boolean
  criteriaValues:
    type: array
    items:
      $ref: '../../../criteria/schemas/CriteriaValue.yaml'
  erhgoClassifications:
    type: array
    items:
      $ref: '../../erhgo/schemas/ErhgoClassification.yaml'
  occupationCreationReason:
    $ref: './OccupationCreationReason.yaml'
