summary: Page of erhgo occupation
operationId: erhgoOccupationPage
parameters:
  - $ref: '../../../parameters/query/Size.yaml'
  - $ref: '../../../parameters/query/Page.yaml'
  - $ref: '../../../parameters/query/Query.yaml'
  - in: query
    name: activityLabelId
    required: false
    schema:
      type: string
      format: uuid
  - in: query
    name: by
    schema:
      type: array
      items:
        $ref: '../schemas/ErhgoSearchOrder.yaml'
  - in: query
    name: direction
    schema:
      type: array
      items:
        $ref: '../../../misc/schemas/SortDirection.yaml'
responses:
  200:
    description: The erhgo occupation page
    content:
      application/json:
        schema:
          $ref: '../schemas/ErhgoOccupationPage.yaml'
