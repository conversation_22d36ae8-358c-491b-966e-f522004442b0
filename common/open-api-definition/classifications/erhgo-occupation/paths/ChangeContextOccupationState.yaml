summary: Set a occupation's context mandatory state
operationId: setOccupationContextMandatoryState
requestBody:
  required: true
  content:
    application/json:
      schema:
        $ref: '../schemas/OccupationReferentialEntityEditWithStateCommand.yaml'
responses:
  204:
    description: success
  404:
    description: occupation not found
  400:
    description: context is not associated to occupation
