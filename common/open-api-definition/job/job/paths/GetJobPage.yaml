summary: Get page of job
operationId: getJobPage
parameters:
  - $ref: '../../../parameters/query/OrganizationCodesQuery.yaml'
  - $ref: '../../../parameters/query/Page.yaml'
  - $ref: '../../../parameters/query/Size.yaml'
  - $ref: '../../../parameters/query/SortDirection.yaml'
  - $ref: '../../../parameters/query/SortBy.yaml'
  - $ref: '../../../parameters/query/Filter.yaml'
  - $ref: '../../../parameters/query/StrictOrganizationFilter.yaml'
responses:
  200:
    description: Page of job
    content:
      application/json:
        schema:
          $ref: '../schemas/JobPage.yaml'
