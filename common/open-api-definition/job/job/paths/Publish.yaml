summary: Set publication date and state for job by gived jobId
operationId: publish
parameters:
  - $ref: '../../../parameters/path/JobId.yaml'
  - $ref: '../../../parameters/query/IsSimpleJobCreated.yaml'
requestBody:
  required: true
  content:
    application/json:
      schema:
        type: object
        title: publishCommand
        properties:
          recruitmentProfileCustomQuestion:
            type: string
responses:
  204:
    description: success
