summary: get profile's mandatory capacities
operationId: getProfileCapacities
parameters:
  - $ref: '../../../parameters/path/ProfileId.yaml'
  - $ref: '../../../parameters/path/JobId.yaml'
responses:
  200:
    description: capacities of profile
    content:
      application/json:
        schema:
          $ref: '../../../referential/capacity/schemas/CapacitiesResult.yaml'
  404:
    description: job not found for id
