summary: Set question to be used for context in profile
operationId: setContextQuestionForProfile
parameters:
  - $ref: '../../../parameters/path/JobId.yaml'
  - $ref: '../../../parameters/path/ProfileId.yaml'
requestBody:
  required: true
  content:
    application/json:
      schema:
        title: setQuestionForContextCommand
        type: object
        required:
          - contextId
          - questionId
        properties:
          contextId:
            type: string
            format: uuid
          questionId:
            type: string
            format: uuid
responses:
  204:
    description: Question was saved for this context in the profile
  404:
    description: Unknown context or question
  400:
    description: Context is optional or question is not defined for this context
