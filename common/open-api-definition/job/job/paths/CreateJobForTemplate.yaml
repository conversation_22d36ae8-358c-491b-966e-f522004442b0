summary: Create a job based on given template id
operationId: createJobForTemplate
requestBody:
  required: true
  description: Template Definition
  content:
    application/json:
      schema:
        title: createJobForTemplateCommand
        type: object
        properties:
          recruiterCode:
            type: string
            minLength: 1
          templateId:
            type: string
            format: uuid
          jobType:
            $ref: '../schemas/JobType.yaml'
responses:
  200:
    description: Details of job
    content:
      application/json:
        schema:
          $ref: '../schemas/JobDetail.yaml'
