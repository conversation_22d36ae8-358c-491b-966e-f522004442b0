summary: Create or update recruitment profile
operationId: saveRecruitmentProfile
parameters:
  - $ref: '../../../parameters/path/JobId.yaml'
requestBody:
  required: true
  content:
    application/json:
      schema:
        title: saveRecruitmentProfileCommand
        type: object
        required:
          - id
          - title
        properties:
          id:
            type: string
            format: uuid
          title:
            type: string
            minLength: 1
          customQuestion:
            type: string
            maxLength: 255
responses:
  204:
    description: The recruitment profile was created or updated
