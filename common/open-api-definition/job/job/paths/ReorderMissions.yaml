summary: Change the order of missions in a job
operationId:  reorderMissions
parameters:
  - $ref: '../../../parameters/path/JobId.yaml'
requestBody:
  required: true
  description: Mission data
  content:
    application/json:
      schema:
        type: array
        items:
          type: integer
          format: int64
          description: Mission's ID
responses:
  204:
    description: Missions were successfully reordered
  404:
    description: Job not found
  400:
    description: Invalid missions' IDs
