summary: get recruitment profile
operationId: getRecruitmentProfile
parameters:
  - $ref: '../../../parameters/path/JobId.yaml'
  - $ref: '../../../parameters/path/ProfileId.yaml'
responses:
  200:
    description: Recruitment profile
    content:
      application/json:
        schema:
          title: recruitmentProfileDetail
          type: object
          properties:
            id:
              type: string
              format: uuid
            title:
              type: string
            modifiable:
              type: boolean
            optionalActivities:
              type: array
              items:
                type: object
                title: optionalActivityDetail
                properties:
                  id:
                    type: string
                    format: uuid
                  activity:
                    $ref: '../../../referential/activity/schemas/ActivityLabelWithCapacities.yaml'
                  acquisitionModality:
                    $ref: '../../../referential/activity/schemas/AcquisitionModality.yaml'
            optionalContexts:
              type: array
              items:
                type: object
                title: optionalContextDetail
                properties:
                  id:
                    type: string
                    format: uuid
                  context:
                    $ref: '../../../referential/context/schemas/Context.yaml'
                  acquisitionModality:
                    $ref: '../../../referential/activity/schemas/AcquisitionModality.yaml'
            contextQuestions:
              type: array
              items:
                type: object
                title: customContextLabel
                properties:
                  contextId:
                    type: string
                    format: uuid
                  question:
                    $ref: '../../../referential/context/schemas/QuestionForContextsSummary.yaml'
            qualifiedMissionIds:
              type: array
              items:
                type: integer
                format: int64
            customQuestion:
              type: string
