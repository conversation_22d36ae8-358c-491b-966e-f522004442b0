summary: Get users matching job export
operationId: getUsersMatchingJobExport
parameters:
  - $ref: '../../../parameters/path/JobId.yaml'
  - $ref: '../../../parameters/query/CapacityThreshold.yaml'
  - in: query
    name: organizationCodes
    schema:
      type: array
      items:
        type: string
        minLength: 1
    required: false
  - $ref: '../../../parameters/query/MasteryLevelRangeQueryOptional.yaml'
  - $ref: '../../../parameters/query/StrictOrganizationFilter.yaml'
  - $ref: '../../../parameters/query/IsAffectedToNoChannel.yaml'
  - in: query
    name: postcode
    schema:
      type: string
  - in: query
    name: criteriaCodes
    schema:
      type: array
      items:
        type: string
    required: false

responses:
  200:
    description: Export of users matching job
    content:
      text/csv:
        schema:
          type: string
  404:
    description: Unknown jobId
