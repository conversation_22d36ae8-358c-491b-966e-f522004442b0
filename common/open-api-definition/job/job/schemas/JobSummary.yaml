type: object
required:
  - id
  - title
  - service
  - recruiterCode
  - state
  - modifiable
properties:
  id:
    type: string
    format: uuid
  title:
    type: string
  description:
    type: string
  service:
    type: string
  recruiterCode:
    type: string
  recruiterTitle:
    type: string
  employerCode:
    type: string
  employerTitle:
    type: string
  state:
    $ref: './JobEvaluationState.yaml'
  modifiable:
    type: boolean
  erhgoOccupationId:
    type: string
    format: uuid
  workingTimes:
    type: array
    items:
      $ref: './WorkingTime.yaml'
  typeContractCategories:
    type: array
    items:
      $ref: './TypeContractCategory.yaml'
  location:
    $ref: '../../../misc/schemas/Location.yaml'
discriminator:
  propertyName: objectType
