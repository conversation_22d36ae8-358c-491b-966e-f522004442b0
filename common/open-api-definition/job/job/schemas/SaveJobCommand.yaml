type: object
required:
  - recruiterCode
  - title
  - state
  - jobType
properties:
  id:
    type: string
    format: uuid
  title:
    type: string
    maxLength: 255
  description:
    type: string
    maxLength: 2000
  recruiterCode:
    type: string
  employerCode:
    type: string
  observationDate:
    type: string
    format: date-time
  service:
    type: string
    maxLength: 50
  observators:
    type: array
    items:
      type: string
  location:
    $ref: '../../../misc/schemas/Location.yaml'
  state:
    $ref: './JobEvaluationState.yaml'
  jobType:
    $ref: './JobType.yaml'
