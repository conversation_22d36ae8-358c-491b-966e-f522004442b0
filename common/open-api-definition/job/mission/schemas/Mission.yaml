type: object
required:
  - id
  - jobId
  - title
  - activitiesIds
  - contextsForCategory
properties:
  id:
    type: integer
    format: int64
  jobId:
    type: string
    format: uuid
  title:
    type: string
    minLength: 1
    maxLength: 100
  activitiesIds:
    type: array
    items:
      type: string
      format: uuid
  contextsForCategory:
    type: array
    items:
      $ref: '../../../referential/context/schemas/ContextsForCategory.yaml'
    minItems: 1
