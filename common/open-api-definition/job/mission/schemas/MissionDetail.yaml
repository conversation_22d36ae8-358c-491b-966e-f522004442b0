type: object
properties:
  id:
    type: integer
    format: int64
  jobId:
    type: string
    format: uuid
  title:
    type: string
    minLength: 1
    maxLength: 50
  activities:
    type: array
    items:
      $ref: '../../../referential/activity/schemas/ActivityLabelWithCapacities.yaml'
  contextsForCategory:
    type: array
    items:
      $ref: '../../../referential/context/schemas/ContextsForCategoryDetail.yaml'
    minItems: 1
