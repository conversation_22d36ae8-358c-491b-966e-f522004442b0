recruitmentId:
  $ref: './parameters/path/RecruitmentId.yaml'
activityId:
  $ref: './parameters/path/ActivityId.yaml'
activityLabelId:
  $ref: './parameters/path/ActivityLabelId.yaml'
activityType:
  $ref: './parameters/path/ActivityType.yaml'
candidatureId:
  $ref: './parameters/path/CandidatureId.yaml'
contextId:
  $ref: './parameters/path/ContextId.yaml'
organizationCode:
  $ref: './parameters/path/OrganizationCode.yaml'
recruiterCode:
  $ref: './parameters/path/RecruiterCode.yaml'
experienceId:
  $ref: './parameters/path/ExperienceId.yaml'
jobId:
  $ref: './parameters/path/JobId.yaml'
missionId:
  $ref: './parameters/path/MissionId.yaml'
profileId:
  $ref: './parameters/path/ProfileId.yaml'
questionId:
  $ref: './parameters/path/QuestionId.yaml'
recruitmentCode:
  $ref: './parameters/path/RecruitmentCode.yaml'
recruitmentState:
  $ref: './parameters/path/RecruitmentState.yaml'
userId:
  $ref: './parameters/path/UserId.yaml'
uuid:
  $ref: './parameters/path/Uuid.yaml'
erhgoOccupationId:
  $ref: './parameters/path/ErhgoOccupationId.yaml'
forceAnonymous:
  $ref: './parameters/path/ForceAnonymous.yaml'
externalOfferId:
  $ref: './parameters/path/ExternalOfferId.yaml'


activityLabelIdQuery:
  $ref: './parameters/query/ActivityLabelId.yaml'
categoryCode:
  $ref: './parameters/query/CategoryCode.yaml'
count:
  $ref: './parameters/query/Count.yaml'
cursor:
  $ref: './parameters/query/Cursor.yaml'
organizationCodesQuery:
  $ref: './parameters/query/OrganizationCodesQuery.yaml'
organizationCodeQuery:
  $ref: './parameters/query/OrganizationCodeQuery.yaml'
organizationCodeQueryOptional:
  $ref: './parameters/query/OrganizationCodeQueryOptional.yaml'
filter:
  $ref: './parameters/query/Filter.yaml'
isco:
  $ref: './parameters/query/Isco.yaml'
page:
  $ref: './parameters/query/Page.yaml'
publishedAfterDate:
  $ref: './parameters/query/PublishedAfterDate.yaml'
publishedBeforeDate:
  $ref: './parameters/query/PublishedBeforeDate.yaml'
qualifiedOnly:
  $ref: './parameters/query/QualifiedOnly.yaml'
query:
  $ref: './parameters/query/Query.yaml'
questionType:
  $ref: './parameters/query/QuestionType.yaml'
size:
  $ref: './parameters/query/Size.yaml'
sortBy:
  $ref: './parameters/query/SortBy.yaml'
sortDirection:
  $ref: './parameters/query/SortDirection.yaml'
uri:
  $ref: './parameters/query/Uri.yaml'
withNewCandidaturesOnly:
  $ref: './parameters/query/WithNewCandidaturesOnly.yaml'
withOpenRecruitmentOnly:
  $ref: './parameters/query/WithOpenRecruitmentOnly.yaml'
isMatch:
  $ref: './parameters/query/IsMatch.yaml'
queryContextId:
  $ref: './parameters/query/ContextId.yaml'
categoryLevelId:
  $ref: './parameters/query/CategoryId.yaml'
occupationSkillThreshold:
  $ref: './parameters/query/OccupationSkillThreshold.yaml'
capacityThreshold:
  $ref: './parameters/query/CapacityThreshold.yaml'
numberOfCapacitiesQualifiedOnErhgoOccupation:
  $ref: './parameters/query/NumberOfCapacitiesQualifiedOnErhgoOccupation.yaml'
mandatoryActivityOnly:
  $ref: './parameters/query/MandatoryActivityOnly.yaml'
behaviorCategories:
  $ref: './parameters/query/BehaviorCategories.yaml'
behaviorCategoriesMatchingType:
  $ref: './parameters/query/BehaviorCategoriesMatchingType.yaml'
minLevel:
  $ref: './parameters/query/MinLevel.yaml'
maxLevel:
  $ref: './parameters/query/MaxLevel.yaml'
userFilter:
  $ref: './parameters/query/UserFilter.yaml'
masteryLevelRangeQueryOptional:
  $ref: './parameters/query/MasteryLevelRangeQueryOptional.yaml'
strictOrganizationFilter:
  $ref: './parameters/query/StrictOrganizationFilter.yaml'
isAffectedToNoChannel:
  $ref: './parameters/query/IsAffectedToNoChannel.yaml'
erhgoOccupationIdQuery:
  $ref: './parameters/query/ErhgoOccupationId.yaml'

