title: userRecruitmentReportItem
type: object
required:
  - recruitmentId
  - recruitmentTitle
  - recruiterName
  - recruiterCode
  - recruiterType
  - notifications
  - candidature
properties:
  recruitmentId:
    type: integer
    format: int64
  recruiterName:
    type: string
  recruiterCode:
    type: string
  recruitmentTitle:
    type: string
  recruiterType:
    $ref: '../../organization/schemas/OrganizationType.yaml'
  notifications:
    type: array
    items:
      $ref: './NotificationForRecruitmentReportItem.yaml'
  candidature:
    $ref: './CandidatureForRecruitmentReportItem.yaml'
