type: object
required:
  - id
  - code
  - jobTitle
  - profileTitle
  - city
  - state
  - publicationDate
  - candidaturesCount
  - sourcingHost
properties:
  id:
    type: integer
    format: int64
  code:
    type: string
  jobTitle:
    type: string
  profileTitle:
    type: string
  recruiterTitle:
    type: string
  recruiterCode:
    type: string
  employerTitle:
    type: string
  city:
    type: string
  state:
    $ref: './RecruitmentState.yaml'
  publicationDate:
    type: string
    format: date-time
  sourcingHost:
    type: string
  sendNotificationState:
    type: string
    enum:
      - CANCEL
      - FORCED
      - WAITING
      - DONE
      - ERROR
  sendNotificationDate:
    type: string
    format: date-time
  mailNotificationCount:
    type: integer
  mobileNotificationsCount:
    type: integer
  candidaturesCount:
    title: RecruitmentSummaryCandidaturesCount
    type: object
    required:
      - newCandidatureCount
      - totalCandidatureCount
      - toContactCandidatureCount
      - contactedCandidatureCount
      - refusedCandidatureCount
      - newMatchingCandidatureCount
    properties:
      newCandidatureCount:
        $ref: './CandidatureCountItem.yaml'
      totalCandidatureCount:
        $ref: './CandidatureCountItem.yaml'
      toContactCandidatureCount:
        type: integer
      contactedCandidatureCount:
        type: integer
      refusedCandidatureCount:
        type: integer
      newMatchingCandidatureCount:
        type: integer
  lastProcessingDate:
    type: string
    format: date-time
  lastProcessingType:
    $ref: './ProcessingType.yaml'

