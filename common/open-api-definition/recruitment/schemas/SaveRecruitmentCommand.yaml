type: object
required:
  - typeContract
  - recruitmentProfileUuid
properties:
  typeContract:
    $ref: './TypeContract.yaml'
  recruitmentProfileUuid:
    type: string
    format: uuid
  workContractDuration:
    type: integer
    format: int32
  workContractDurationUnit:
    $ref: './WorkContractDurationUnit.yaml'
  workingWeeklyTime:
    type: integer
    format: int32
  location:
    $ref: '../../misc/schemas/Location.yaml'
  city:
    type: string
  baseSalary:
    type: integer
    format: int32
  maxSalary:
    type: integer
    format: int32
  hideSalary:
    type: boolean
  externalUrl:
    type: string
  state:
    $ref: './RecruitmentState.yaml'
  startingDate:
    type: string
    format: date-time
  organizationDescription:
    type: string
  title:
    type: string
  description:
    type: string
  usersIdToNotify:
    type: array
    items:
      type: string

