summary: List recruitments
operationId: listRecruitments
parameters:
  - $ref: '../../parameters/query/OrganizationCodeQueryOptional.yaml'
  - in: query
    name: selectedProjects
    schema:
      type: array
      items:
        type: string
        minLength: 1
  - $ref: '../../parameters/query/WithNewCandidaturesOnly.yaml'
  - $ref: '../../parameters/query/WithOpenRecruitmentOnly.yaml'
  - $ref: '../../parameters/query/OrganizationTypeFilter.yaml'
  - $ref: '../../parameters/query/Page.yaml'
  - $ref: '../../parameters/query/Size.yaml'
  - in: query
    name: internal
    schema:
      type: boolean
      default: false
  - in: query
    name: by
    description: The field by which the list should be sorted
    schema:
      $ref: '../schemas/RecruitmentSort.yaml'
    required: true
  - $ref: '../../parameters/query/SortDirection.yaml'
  - in: query
    name: query
    description: search query for job title, recruitment location, recruitment profile title
    schema:
      type: string
    required: false
responses:
  200:
    description: A list of recruitments
    content:
      application/json:
        schema:
          $ref: '../schemas/RecruitmentPage.yaml'
