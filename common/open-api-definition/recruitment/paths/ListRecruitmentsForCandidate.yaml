summary: List recruitments for candidat
operationId: listRecruitmentsForCandidate
parameters:
  - $ref: '../../parameters/path/JobId.yaml'
  - $ref: '../../parameters/path/UserId.yaml'
  - $ref: '../../parameters/query/Page.yaml'
  - $ref: '../../parameters/query/Size.yaml'
responses:
  200:
    description: A list of recruitments
    content:
      application/json:
        schema:
          $ref: '../schemas/RecruitmentPageForCandidate.yaml'
