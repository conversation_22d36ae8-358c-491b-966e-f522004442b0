summary: Get list of candidatures for a recruitment. Returns all candidatures that matches or not matches for external consultant profile
operationId: getCandidaturesForConsultant
parameters:
  - $ref: '../../parameters/query/Size.yaml'
  - $ref: '../../parameters/query/Page.yaml'
  - $ref: '../../parameters/path/RecruitmentId.yaml'
  - $ref: '../../parameters/query/IsMatch.yaml'
responses:
  200:
    description: list of candidates with contact information
    content:
      application/json:
        schema:
          $ref: '../../candidature/job/schemas/CandidatureContactInfoPage.yaml'
