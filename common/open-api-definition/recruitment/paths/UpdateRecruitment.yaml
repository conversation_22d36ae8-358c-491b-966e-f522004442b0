summary: Update an existing recruitment
operationId: updateRecruitment
parameters:
  - $ref: '../../parameters/path/RecruitmentId.yaml'
requestBody:
  required: true
  description: Recruitment data
  content:
    application/json:
      schema:
        $ref: '../schemas/SaveRecruitmentCommand.yaml'
responses:
  200:
    description: Recruitment was successfully updated
    content:
      application/json:
        schema:
          $ref: '../schemas/Recruitment.yaml'
  400:
    description: Invalid data
  500:
    description: Recruitment update failed
