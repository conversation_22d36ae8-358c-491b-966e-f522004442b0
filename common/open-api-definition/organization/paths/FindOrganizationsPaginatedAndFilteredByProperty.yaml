summary: Find organization page filtered by property.
operationId: findOrganizationsPaginatedAndFilteredByProperty
parameters:
  - $ref: '../../parameters/query/Page.yaml'
  - $ref: '../../parameters/query/Size.yaml'
  - $ref: '../../parameters/query/SortBy.yaml'
  - $ref: '../../parameters/query/SortDirection.yaml'
  - $ref: '../../parameters/query/Filter.yaml'
  - in: query
    name: refererRecruiters
    schema:
      type: array
      items:
        type: string
        minLength: 1
    required: false
responses:
  200:
    description: Page of organization
    content:
      application/json:
        schema:
          $ref: '../schemas/OrganizationPage.yaml'
