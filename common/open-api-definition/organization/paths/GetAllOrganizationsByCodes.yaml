summary: Retrieves organizations by codes
operationId: getAllOrganizationsByCodes
parameters:
  - in: query
    name: codes
    schema:
      type: array
      items:
        type: string
responses:
  200:
    description: Organizations summaries by their codes
    content:
      application/json:
        schema:
          type: array
          items:
            $ref: '../schemas/OrganizationSummary.yaml'
