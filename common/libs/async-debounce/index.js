import { debounce } from 'lodash';
import { logErrorToServer } from 'odas-plugins/error-handler';
import Vue from 'vue';

/**
 *
 * @param {function} fn
 * @param {number} msDelay
 * @returns {{pending: boolean, fn: function}|boolean}
 */
export default function asyncDebounce(fn, msDelay) {
  const state = {
    pendingDebounce: false,
    pendingAsync: false,
    error: null,

    waitingFn: null,

    get pending() {
      return this.pendingDebounce || this.pendingAsync;
    },

  };

  const debouncedFunction = debounce(async function () {
    if (state.pendingAsync) {
      state.waitingFn = () => fn(...arguments);
    } else {
      try {
        state.error = null;
        state.pendingAsync = true;
        state.pendingDebounce = false;
        await fn(...arguments);
        if (state.waitingFn) {
          await state.waitingFn();
          state.waitingFn = null;
        }
      } catch (e) {
        state.error = e;
        logErrorToServer(e, Vue.$api);
      } finally {
        state.pendingAsync = false;
      }
    }
  }, msDelay);

  state.fn = function () {
    state.pendingDebounce = true;
    debouncedFunction(...arguments);
  };

  return state;
}
