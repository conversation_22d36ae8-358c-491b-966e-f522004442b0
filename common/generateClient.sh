#!/usr/bin/env bash

set -euo pipefail

docker run --rm -v ${PWD}:/local  -u $(id -u):$(id -g) \
    jeanberu/swagger-cli swagger-cli bundle /local/open-api-definition/api-split.yaml --outfile /local/generated/api.yaml --type yaml

docker run --rm -v ${PWD}/generated/:/local -u $(id -u):$(id -g) \
    openapitools/openapi-generator-cli:v6.6.0 generate -i /local/api.yaml -g typescript-axios -o /local/client --additional-properties=npmName=erhgo-api-client,supportsES6=true,enumPropertyNaming=UPPERCASE,sortModelPropertiesByRequiredFlag=false --type-mappings=set=Array --type-mappings=DateTime=Date --type-mappings=Date=Date

cd generated/client

yarn install --frozen-lockfile

yarn build

../../../node_modules/.bin/eslint --fix --config=../../../.eslintrc.js --resolve-plugins-relative-to=../../.. ./ || true # fix what we can, but don't fail on what we can't

rm -fr ../../../node_modules/erhgo-api-client ../../../sourcing/node_modules/erhgo-api-client ../../../sourcing/.q-cache
cd ../../../sourcing && yarn install --check-files
cd ../../../ && yarn install --check-files

echo 'Pour prise en compte, redémarrez vos front - FO, BO, Sourcing'
