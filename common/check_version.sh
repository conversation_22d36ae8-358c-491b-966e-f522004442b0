#!/usr/bin/env bash

set -uo pipefail

EXPECTED_VERSION="$1"
API_URL="$2"
RETRY_DELAY_IN_SECONDS=15

echo "Expected: $EXPECTED_VERSION"
echo "API URL: $API_URL"

n=0
until [ $n -ge 5 ]
do
  ACTUAL_VERSION=$(curl -s -S "$API_URL")
  echo "Got '$ACTUAL_VERSION'"
  [[ "$ACTUAL_VERSION" == "$EXPECTED_VERSION" ]] && exit 0
  n=$[$n+1]
  echo "Retrying in $RETRY_DELAY_IN_SECONDS seconds"
  sleep $RETRY_DELAY_IN_SECONDS
done

echo "Failed to check version"
exit 1
