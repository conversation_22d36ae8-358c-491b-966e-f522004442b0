{
  "compilerOptions": {
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "declaration": true,
    "target": "es6",
    "module": "commonjs",
    "noImplicitAny": true,
    "outDir": "dist",
    "types": [],
    // Ajout dû à l'erreur : ../../../node_modules/@types/node/url.d.ts:866:31 - error TS2709: Cannot use namespace '_URL' as a type.
    "typeRoots": [
      "../../../node_modules/@types"
    ],
    "rootDir": "."
  },
  "exclude": [
    "dist",
    "node_modules"
  ]
}
