import Vue from 'vue';
import {logErrorToServer} from 'odas-plugins/error-handler';

export default abstract class SafeService {

  _showAPIError = false;
  loading = false;
  success = false;
  private _timeout: number | null = null;

  get showAPIError() {
    return this._showAPIError;
  }

  set showAPIError(newVal) {
    this._showAPIError = newVal;
    Vue.$eventBus.$emit('error', this.showAPIError);
  }

  protected async safeCall(fn: Function, noLoading?: boolean, noSuccess?: boolean) {

    this.showAPIError = false;

    if (!noLoading) {
      this.loading = true;
    }

    this.success = false;

    try {
      const result = await fn();
      if (!noSuccess) {
        this.success = true;
      }
      if (this._timeout) {
        clearTimeout(this._timeout);
      }
      // reference to window to hack TS typing system - see https://github.com/microsoft/TypeScript/issues/842
      this._timeout = window.setTimeout(() => this.success = false, 5000);
      return result;
    } catch (e) {
      this.showAPIError = true;
      logErrorToServer(e, Vue.$api);
    } finally {
      if (!noLoading) {
        this.loading = false;
      }
    }
  }
}
