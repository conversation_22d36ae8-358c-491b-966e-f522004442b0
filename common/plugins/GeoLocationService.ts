interface Location {
  longitude?: number;
  latitude?: number;
}

export default class GeoLocationService {

  private static EARTH_RADIUS = 6378137;

  public static LOCATION_LYON = {
    longitude: 4.86934,
    latitude: 45.7517,
  };

  private static degreesToRadians(degrees: number) {
    return degrees * Math.PI / 180;
  }

  static getDistanceBetweenPointsInKm(origin: Location | undefined, destination: Location | undefined) {
    if (!origin || !destination || !origin.latitude || !origin.longitude || !destination.latitude || !destination.longitude) {
      return undefined;
    }
    const {longitude: lng1, latitude: lat1} = origin;
    const {longitude: lng2, latitude: lat2} = destination;
    const dLat = this.degreesToRadians(lat2 - lat1);
    const dLong = this.degreesToRadians(lng2 - lng1);
    const a = Math.sin(dLat / 2)
      *
      Math.sin(dLat / 2)
      +
      Math.cos(this.degreesToRadians(lat1))
      *
      Math.cos(this.degreesToRadians(lat1))
      *
      Math.sin(dLong / 2)
      *
      Math.sin(dLong / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = GeoLocationService.EARTH_RADIUS * c;

    return Math.round(distance / 1000);
  }

}


