import {AxiosInstance} from 'axios';
import {Default<PERSON>pi} from 'erhgo-api-client';
import {KeycloakInstance} from 'keycloak-js';
import VueRouter from 'vue-router';

declare module 'vue/types/vue' {
  export interface VueConstructor {
    $axios: AxiosInstance;
    $api: DefaultApi;
    $keycloak: KeycloakInstance;
    // NB.: $router is instanciated on application side (typically see router.js file)
    $router: VueRouter;
    $eventBus: Vue;
  }
}
