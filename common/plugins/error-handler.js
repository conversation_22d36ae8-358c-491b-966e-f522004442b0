import Vue from 'vue';
import {baseUrl} from './base-url';

const rawSend = (message) => {
  const url = `${baseUrl('api')}/api/odas/public/log`;
  const xhr = new XMLHttpRequest();
  xhr.open('POST', url);
  xhr.setRequestHeader('Content-Type', 'application/json');
  xhr.send(JSON.stringify(message));
};

export const logErrorToServer = (err, $api, errorContext) => {
  let message;
  if (err) {
    console.error(err);
    message = (err.stack || err);
    if (err.hasOwnProperty('request') && err.hasOwnProperty('config')) { // Axios error
      message = err.config.method.toUpperCase() + ' ' + err.config.url + ' => ' + message;
    }
    if (typeof message !== 'string') {
      message = JSON.stringify(message);
    }
    if (!err.stack) {
      message += ` (stack: ${new Error().stack}) `;
    }
    if (err.message) {
      message += ` --- ${err.message}`;
    }
  } else {
    message = 'Unknown error';
    console.error(message);
  }
  if (errorContext) {
    message += ` --- error context: ${errorContext}`;
  }
  const logCommand = {message: `${message}, location: ${window.location.href}`};
  if ($api) {
    $api.logEvent(logCommand);
  } else {
    rawSend(logCommand);
  }
};

Vue.config.errorHandler = (err, vue) => {
  logErrorToServer(err, vue && vue.$api);

  throw err; // re-throw so the error can appear in the console or be handled somewhere else
};

const loggerPlugin = {
  install() {
    Object.defineProperties(Vue.prototype, {
      logError: {
        get() {
          return (err) => {
            logErrorToServer(err, this.$api);
          };
        },
      },
    });
  },
};

Vue.use(loggerPlugin);

export default loggerPlugin;
