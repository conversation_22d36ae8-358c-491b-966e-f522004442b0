import Vue from 'vue';
import Vuetify from 'vuetify/lib';
import colors from 'vuetify/lib/util/colors';
import '@mdi/font/css/materialdesignicons.css';
import '@fortawesome/fontawesome-pro/css/all.css';
import 'material-design-icons-iconfont/dist/material-design-icons.css';

Vue.use(Vuetify);

export default new Vuetify({
  theme: {
    options: {
      customProperties: true,
    },
    themes: {
      light: {
        primary: '#00856f',
        secondary: '#006655',
        accent: '#006655',
        error: '#e50354',
        warning: colors.yellow.base,
        info: '#00856f',
        success: colors.green.base,
        shiny: '#00f0c8',
        interaction: '#7e02eb',
      },
    },
  },
  icons: {
    iconfont: 'mdi',
  },
});
