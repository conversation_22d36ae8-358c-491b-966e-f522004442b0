<template>
  <div>
    <v-autocomplete
        v-model="place"
        v-bind="$attrs"
        :items="places"
        :loading="loading"
        :search-input.sync="query"
        return-object
        item-text="city"
        item-value="postcode"
        :append-icon="appendIcon"
        no-filter
        :hide-no-data="!query || loading"
        no-data-text="Aucune ville trouvée"
        v-on="$listeners"
        @input="onInput"
        clearable
        @focus="searchPlaces"
        :persistent-hint="true"
        placeholder="Commencez à saisir votre ville"
        :hint="`${$attrs.hint ? ($attrs.hint + ' - '): ''}${(!query || query.length < 3) ? 'Saisir au moins 3 caractères' : ''}`"
        :hide-details="!!$attrs['hide-details'] && !!query && query.length >= 3 "
    >
      <template slot="item" slot-scope="data">
        <template v-if="typeof data.item.city !== 'object'">
          <v-list-item-content v-if="data.item.postcode !== undefined" :id="`city-for-code${data.item.citycode}`">
            {{ data.item.city }}
            ({{ data.item.postcode }})
          </v-list-item-content>
          <v-list-item-content v-else>{{ data.item.city }}</v-list-item-content>
        </template>
      </template>
    </v-autocomplete>
    <div v-if="showRadius && !!place">
      <span class="pl-0 ma-0 grey--text text--darken-2 text-caption">Rayon de {{
          radiusForCandidates ? 'recherche' : 'localisation'
        }} en km&nbsp;:</span>
      <v-slider
          v-if="radiusForCandidates"
          id="inputRadiusForCandidate"
          v-model="candidateRadius"
          min="0"
          :max="tickCandidatesRadiusValues.length - 1"
          :tick-labels="tickCandidatesRadiusValues"
          :ticks="true"
          show-ticks="always"
          class="text-subtitle-2"
          @change="onInput"
      />
      <v-slider
          id="inputRadius"
          v-else
          v-model="place.radiusInKm"
          min="0"
          max="60"
          :thumb-size="24"
          thumb-color="primary"
          thumb-label="always"
          class="ml-3"
          @change="onInput"
      />
    </div>
  </div>
</template>

<script>
import { debounce } from 'lodash';
import axios from 'axios';

const EVERYWHERE_IN_FRANCE_TICK = {label: '+', value: 2000};

export default {
  name: 'VuetifyGeoPlaces',
  props: {
    value: {
      type: Object,
      required: false,
      default: null,
    },
    // Vuetify props
    appendIcon: {
      type: String,
      default: 'location_on',
    },
    showRadius: {
      default: false,
    },
    radiusForCandidates: {
      default: false,
    },
  },
  created() {
    const initialValue = this.value?.city ? {...this.value} : null;
    this.query = initialValue?.city;
    this.place = initialValue;
    this.places = initialValue ? [initialValue] : [];
  },
  data() {
    return {
      isInError: false,
      loading: false,
      query: null,
      place: null,
      places: [],
      showRadiusKm: false,
      tickCandidatesRadiusValues: ['0', '5', '10', '15', '20', '30', '50', '100', '200', EVERYWHERE_IN_FRANCE_TICK.label],
    };
  },
  watch: {
    query(newVal, oldVal) {
      if (newVal !== oldVal && newVal !== this.value?.city) {
        this.searchPlaces();
      }
    },
  },
  computed: {
    defaultIndex() {
      return this.tickCandidatesRadiusValues.indexOf('50');
    },
    candidateRadius: {
      get() {
        const index = this.tickCandidatesRadiusValues.indexOf(`${
          this.place?.radiusInKm > 500 ?
            EVERYWHERE_IN_FRANCE_TICK.label :
            this.place?.radiusInKm}`);
        return index < 0 ? this.defaultIndex : index;
      },
      set(radiusAsIndex) {
        const radiusInKm = this.tickCandidatesRadiusValues[radiusAsIndex] !== EVERYWHERE_IN_FRANCE_TICK.label ?
          this.tickCandidatesRadiusValues[radiusAsIndex] :
          EVERYWHERE_IN_FRANCE_TICK.value;
        this.place = {...(this.place || {}), radiusInKm};
      },
    },
  },
  methods: {
    async doSearchPlaces() {
      if (this.query && this.query.length > 2) {
        const uri = `https://api-adresse.data.gouv.fr/search/?q=${this.query}&type=municipality&autocomplete=1`;
        const encoded = encodeURI(uri);
        try {
          const value = (await axios.get(encoded)).data;
          this.places = value.features.map(x => ({
            city: x.properties?.city,
            citycode: x.properties?.citycode,
            postcode: x.properties?.postcode,
            departmentCode: x.properties?.context.split(', ')[0],
            regionName: x.properties?.context.split(', ')[2],
            longitude: x.geometry?.coordinates[0],
            latitude: x.geometry?.coordinates[1],
          }));
          this.isInError = false;
        } catch (error) {
          this.places = [{city: this.query, postcode: ''}];
          this.loading = false;
          this.isInError = true;
        } finally {
          this.loading = false;
          this.$emit('isOnError', this.isInError);
        }
      }
    },
    searchPlaces() {
      this.loading = !!this.query;
      this.debouncedSearch();
    },
    debouncedSearch: debounce(async function doSearch() {
      await this.doSearchPlaces();
    }, 500),
    onInput() {
      this.$emit('input', this.place);
    },
  },
};
</script>
