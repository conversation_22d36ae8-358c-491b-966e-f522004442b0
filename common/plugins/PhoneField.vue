<template>
  <vue-tel-input-vuetify v-model="phoneNumber"
                         id="phoneField"
                         default-country="fr"
                         :preferred-countries="['fr']"
                         append-icon="mdi-chevron-down"
                         label=""
                         :placeholder="$attrs.placeholder || phoneFieldTranslation"
                         :error-messages="phoneNumberInvalidMessage"
                         background-color="white"
                         @keydown.native="isValidInteger"
                         @input="validateInput"
                         :error="!!phoneNumberInvalidMessage"
                         clearable
                         @blur="phoneNumberInvalidMessage = (!valid && !!phoneNumber) ? errorTranslation : ''"
                         v-bind="{...$attrs}"
  />
</template>

<script>
import VueTelInputVuetify from 'vue-tel-input-vuetify/lib';

export default {
  name: 'PhoneField',
  components: VueTelInputVuetify,
  props: {
    value: {
      type: String,
    },
  },
  data() {
    return {
      valid: true,
      phoneNumber: '',
      phoneNumberInvalidMessage: '',
      errorTranslation: 'N° de téléphone invalide',
      phoneFieldTranslation: 'Saisissez votre téléphone',
    };
  },
  created() {
    this.phoneNumber = this.value;
  },
  methods: {
    validateInput(_, {number, isValid}) {
      this.valid = isValid;
      if (isValid || !number?.input) {
        this.phoneNumberInvalidMessage = '';
        this.$emit('input', number?.e164 || '');
      }
    },
    isValidInteger(e) {
      if (e.key?.match(/^[a-zA-Z]$/)) {
        e.preventDefault();
        return false;
      } else {
        return true;
      }
    },
  },
  watch: {
    value(newValue) {
      this.phoneNumber = newValue;
    },
  },
};
</script>

<style>
.vti__flag {
  margin-left: 5px !important;
}

.vue-tel-input-vuetify .country-code {
  width: 55px !important;
}
</style>
