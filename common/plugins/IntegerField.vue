<!-- Use of 'tel' input to allow maxlength -->
<template>
  <v-tooltip top :disabled="!minError" :value="minError">
    <template v-slot:activator="{ on, attrs }">
      <div v-bind="attrs" v-on="on">
        <v-text-field
          v-bind="{...$props, ...$attrs}"
          :class="[$vnode.data.staticClass, $vnode.data.class]"
          :style="[$vnode.data.staticStyle, $vnode.data.style]"
          v-on="listeners"
          @keydown.native="isValidInteger"
          type="tel"
        />
      </div>
    </template>
    <span>Doit être supérieur à {{ min }}</span>
  </v-tooltip>
</template>
<script>
export default {
  data() {
    return {
      minError: false,
    };
  },
  props: {
    min: {
      type: Number,
      required: false,
    },
  },
  computed: {
    listeners() {
      return { ...this.$listeners, input: this.onChange };
    },
  },
  methods: {
    onChange(val) {
      this.minError = this.min !== undefined && val < this.min;
      if (!this.minError) {
        this.$emit('input', val);
      }
    },
    isValidInteger(e) {
      const maxLength = this.$attrs?.maxlength;
      // FIXME: known bug: compound chars like ^ are not escaped
      // Dot is not allowed here ; Vuetify component does the job for other non-numeric chars
      if (e.key?.match(/^\D$/) ||
        (maxLength && /* allow arrow, backspace, del... keys: */ e.key?.match(/^\d$/) && e.target?.value?.length >= maxLength)
      ) {
        e.preventDefault();
        return false;
      } else {
        return true;
      }
    },
  },
};
</script>
