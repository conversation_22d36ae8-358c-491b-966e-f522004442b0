<template>
  <v-card
    :outlined="$attrs['no-border'] === undefined"
    :elevation="$attrs['no-border'] !== undefined ? 0 : $attrs['elevation']"
    v-bind="{...$attrs}"
    v-on="$listeners"
    :color="getColor"
    :class="btnClass"
  >
    <v-card-text class="pa-2 text-center"
                 :class="{'white--text': $attrs.dark !== undefined, 'black--text': $attrs.primary !== undefined || $attrs.shiny !== undefined }">
      <slot/>
      <v-progress-circular v-if="$attrs.loading"
                           :size="20"
                           :color="getColor ? 'white': 'primary'"
                           indeterminate/>
    </v-card-text>
  </v-card>
</template>
<script>
export default {
  name: 'card-btn',
  props: {
    btnClass: [Object, String],
  },
  computed: {
    getColor() {
      if (this.$attrs.primary !== undefined)
        return 'primary';
      if (this.$attrs.dark !== undefined)
        return 'secondary';
      if (this.$attrs.shiny !== undefined)
        return 'shiny';

      return this.$attrs.color;
    },
  },
};
</script>
