export const getEnv = (): string => {
  const url = window.location.host.split('.');
  let env = url[0].split('-')[0];
  if (env === 'odas' || env === 'bo' || env === 'app') {
    env = 'master';
  }
  return env;
};

export const baseUrl = (target: string) => {
  // target : fo, api, auth
  const env = getEnv();
  const protocol = window.location.protocol;
  const isLocalhost = env.startsWith('localhost');
  const isE2e = window.location.host.includes('e2e');
  const [_, ...domainParts] = window.location.host.split('.');
  const domain = domainParts.join('.');

  const isFo = target === 'fo';
  const isApi = target === 'api';
  const isAuth = target === 'auth';

  let redirect;

  if (isE2e) {
    if (isAuth) {
      redirect = 'keycloak-e2e:8080';
    } else if (isFo) {
      redirect = 'front-office-e2e:8080';
    } else if (isApi) {
      redirect = 'api-e2e:8080';
    }
  } else if (env === 'master') {
    if (isAuth) {
      redirect = `auth.${domain}`;
    } else if (isFo) {
      redirect = `app.${domain}`;
    } else if (isApi) {
      redirect = `api.${domain}`;
    }
  } else if (isLocalhost) {
    if (isAuth) {
      redirect = 'auth.localhost';
    } else if (isFo) {
      redirect = 'localhost:53323';
    } else if (isApi) {
      redirect = 'localhost:8080';
    }
  } else if (isFo) {
    redirect = `${env}.${domain}`;
  } else {
    redirect = `${env}-${target}.${domain}`;
  }
  // @ts-ignore
  const forced = process.env[`VUE_APP_${target.toUpperCase()}_URL`];
  if (forced) {
    return forced;
  }
  return `${protocol}//${redirect}`;

};
