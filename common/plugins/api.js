import Vue from 'vue';
import axios from 'axios';
import { DefaultApi } from 'erhgo-api-client';
import { baseUrl } from './base-url';
import { refreshToken } from 'odas-plugins/keycloak';

const isAndroid = () => {
  return /Android/i.test(navigator.userAgent);
};

const isIOs = () => {
  return /iPhone|iPad|iPod/i.test(navigator.userAgent);
};

const axiosInstance = axios.create({baseURL: baseUrl('api')});
const computeDevice = () => isAndroid() ? 'Android' : isIOs() ? 'Apple' : 'Desktop';
const device = computeDevice();
const computeResolution = () => {
  return `${window.innerWidth}x${window.innerHeight}`;
};

let appVersion;
try {
  appVersion = (await axios.get(`${window.location.origin}/version`)).data || 'unavailable';
} catch (e) {
  appVersion = 'unknown';
}
axiosInstance.interceptors.response.use((response) => {
  return response;
}, async (error) => {
  if (error.response && (error.response.status === 401) && Vue.$keycloak) {
    Vue.$keycloak.authenticated = false;
    await refreshToken();

    if (Vue.$keycloak.authenticated) {
      const requestToReplay = error.config;
      addHeaders(requestToReplay);
      return axios.request(requestToReplay);
    }
  }
  return Promise.reject(error);
});

function addHeaders(request) {
  request.headers = {
    ...(request.headers || {}),
    'X-App-Version': appVersion,
    'X-Device': device,
    'X-Resolution': computeResolution(),
  };
  if (Vue.$keycloak.authenticated) {
    request.headers = {
      ...request.headers,
      Authorization: `Bearer ${Vue.$keycloak.token}`,
      'X-Realm': Vue.$keycloak.realm,
    };

  }
}

axiosInstance.interceptors.request.use((request) => {
  addHeaders(request);
  return request;
}, function (error) {
  return Promise.reject(error);
});

const APIPlugin = {
  install() {
    let basePath = baseUrl('api');
    if (basePath.endsWith('/')) {
      basePath = basePath.substring(0, basePath.length - 1);
    }
    basePath += '/api/odas';

    const apiClient = new DefaultApi({
      basePath,
    }, basePath, axiosInstance);

    Vue.$axios = axiosInstance;
    Vue.$api = apiClient;
    Object.defineProperties(Vue.prototype, {
      $api: {
        get() {
          return apiClient;
        },
      },
      $axios: {
        get() {
          return axiosInstance;
        },
      },
    });
  },
};

Vue.use(APIPlugin);

export default APIPlugin;
