## Security headers
# X-Frame-Options is to prevent from clickJacking attack
add_header X-Frame-Options SAMEORIGIN;
# Disable content-type sniffing on some browsers.
add_header X-Content-Type-Options nosniff;
# This header enables the Cross-site scripting (XSS) filter
add_header X-XSS-Protection "1; mode=block";
# This header tells the browser to always use https rather than http
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload;";
