openapi: 3.0.3
info:
  title: Candidate Jobs
  version: v3.0.1
  description: |
    APIs for the job application, this API orchestrates APIs for external & internal applications to Adecco Group IT FR
    - v3.0.1: adding sponsorship field in postcreateAccountAndApply (Loyalty project)
  contact:
    email: <EMAIL>
    name: API Designer
servers:
  - url: https://apm-eur-fr-dev-apim.azure-api.net/candidate/jobs/v3.0
paths:
  /{id}/apply/candidate:
    post:
      description: Apply for a job for the given candidate to be created. For job boards
      operationId: postcreateAccountandApplyForJobBoard
      summary: createAccountandApplyForJobBoard
      tags:
        - CANDIDATE-JOB unauthenticated
      parameters:
        - $ref: '#/components/parameters/apiKeyHeader'
        - $ref: '#/components/parameters/PartnerIDHeader'
        - $ref: '#/components/parameters/JobId'
      requestBody:
        description: The candidate to create with postulation infos
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/requestJobApplyJobBoard'
      responses:
        '201':
          description: created
        '400':
          description: Bad request error (non expected CV file format, CV file exceeding maximum size)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseError'
              examples:
                400-consent_missing:
                  value:
                    error_code: format_data_failure
                    error_message: 'Format Data function failed'
        '403':
          description: Forbidden - The service is unavailable for the PartnerID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseError'
              example:
                error_code: 403
                error_message: Forbidden - The service is unavailable for the PartnerID
        '500':
          $ref: "#/components/responses/InternalError"
        '503':
          $ref: "#/components/responses/ServiceUnavailable"

components:
  parameters:
    apiKeyHeader:
      name: Ocp-Apim-Subscription-Key
      in: header
      schema:
        type: string
    PartnerIDHeader:
      in: header
      name: partnerID
      description: ID of the Partner, mandatory for external Partner
      schema:
        type: string
      required: false
    JobId:
      name: id
      required: true
      description: Job id
      in: path
      schema:
        type: string
  responses:
    Success:
      description: Success
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/responseSuccess'
    SuccessJobBoard:
      description: Success
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/responseSuccessJobBoard'
    InternalError:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/responseError'
          example:
            error_code: "InternalError"
            error_message: "Internal Error"
    ServiceUnavailable:
      description: Service Unavailable error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/responseError'
          example:
            error_code: '503'
            error_message: The service is currently unavailable
    BadRequest:
      description: Bad request error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/responseError'

  schemas:
    requestJobApplyJobBoard:
      type: object
      properties:
        User:
          $ref: '#/components/schemas/User'
        userProfile:
          $ref: '#/components/schemas/UserProfile'
        UploadedCV:
          $ref: '#/components/schemas/UploadedCV'
        SourceInfo:
          $ref: '#/components/schemas/SourceInfo'
        CreationFlowStatus:
          type: string
    User:
      type: object
      properties:
        firstname:
          type: string
        lastname:
          type: string
        phone:
          type: string
          pattern: '^\d{10}$'
        email:
          type: string
          format: email

    UserProfile:
      type: object
      properties:
        civility:
          type: integer
        address:
          $ref: '#/components/schemas/Address'
        qualification:
          type: array
          items:
            type: integer
        contractType:
          type: array
          items:
            type: integer
        hasHandicap:
          type: boolean
        hasAcceptedByEmail:
          type: boolean
        hasAcceptedByPhone:
          type: boolean
        consent:
          type: boolean

    Address:
      type: object
      properties:
        zipCode:
          type: string
          pattern: '^\d{5}$'
        city:
          type: string
        country:
          type: integer

    UploadedCV:
      type: object
      properties:
        fileName:
          type: string
        file:
          type: string

    SourceInfo:
      type: object
      properties:
        source:
          type: string
          description: "Code à définir"
    source:
      type: string
      description: Job board source info
      example: '14'
    extendedInfo:
      description: Placeholder for API's client extended information to be forwarded to Job application process
      type: object
    responseError:
      type: object
      properties:
        error_code:
          type: string
        error_message:
          type: string
    responseSuccess:
      type: object
      required:
        - success
      properties:
        success:
          type: boolean
          example: true
        urlRecruitementConnectProcess:
          type: string
          format: url
          description: URL on Recruitement Connect to access page to start recruitement process for a candidate. URL is dedicated to a specific candidate. This value is available only for Recruitement Connect job offers.
    responseSuccessJobBoard:
      type: object
      required:
        - success
      properties:
        success:
          type: boolean
          example: true
        created:
          type: boolean
          example: true
    responseNewAccountAndApply:
      type: object
      properties:
        candidateGUID:
          type: string
          example: "0cc3eff5-b0c7-404c-9124-e4d637af8d98"
          format: uuid
        candidateID:
          type: string
          example: "********"
        result:
          type: object
          properties:
            created:
              type: boolean
              example: true
            uploadedSkills:
              type: boolean
              example: true
            uploadedCV:
              type: boolean
              example: true
            apply:
              type: boolean
              example: true
            urlRecruitementConnectProcess:
              type: string
              format: url
              description: URL on Recruitement Connect to access page to start recruitement process for a candidate. URL is dedicated to a specific candidate. This value is available only for Recruitement Connect job offers.
            sponsorshipCreated:
              type: boolean
              example: true
              description: "if sponsorship/sponsorshipCode was provided in request body, this flag indicates if Goeland successfully created the corresponding sponsorship"
