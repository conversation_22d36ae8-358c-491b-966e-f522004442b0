package com.erhgo.repositories;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.classifications.rome.RomeOccupation;
import com.erhgo.domain.criteria.Criteria;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.*;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.CriteriaMotherObject;
import com.erhgo.generators.ErhgoOccupationGenerator;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.openapi.dto.CandidateDetailDTO;
import com.erhgo.services.dto.criteria.SourcingCandidatesCriteria;
import com.erhgo.services.mailing.MailingListService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import static org.assertj.core.api.Assertions.assertThat;


class SourcingUserRepositoryTest extends AbstractIntegrationTest {

    @Autowired
    SourcingUserRepository repository;
    @Autowired
    ApplicationContext applicationContext;

    @MockitoBean
    MailingListService mailingListService;

    @Test
    @ResetDataAfter
    void getCandidates_get_all_candidates_when_no_criteria() {
        var users = new String[]{
                applicationContext.getBean(UserProfileMotherObject.class).buildAndPersist().userId(),
                applicationContext.getBean(UserProfileMotherObject.class).buildAndPersist().userId()
        };

        var candidatesId = repository.getCandidates(applicationContext.getBean(SourcingCandidatesCriteria.class));

        assertThat(candidatesId).containsExactlyInAnyOrder(users);
    }

    @Test
    @ResetDataAfter
    void getCandidates_check_that_blacklisted_occupation_remove_user() {
        var blacklistedOccupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist();
        var otherBlacklistedOccupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist();

        var userThatShouldBeRemoved = applicationContext.getBean(UserProfileMotherObject.class).withBlacklistedOccupation(blacklistedOccupation).buildAndPersist();
        var otherUser = applicationContext.getBean(UserProfileMotherObject.class).withBlacklistedOccupation(otherBlacklistedOccupation).buildAndPersist();

        var usersWithoutBlacklistedOccupation = new String[]{
                applicationContext.getBean(UserProfileMotherObject.class).buildAndPersist().userId(),
                otherUser.userId()
        };

        var criteria = applicationContext.getBean(SourcingCandidatesCriteria.class).occupationId(blacklistedOccupation.getId());
        var candidatesId = repository.getCandidates(criteria);

        assertThat(candidatesId)
                .containsExactlyInAnyOrder(usersWithoutBlacklistedOccupation)
                .doesNotContain(userThatShouldBeRemoved.userId());
    }

    @ParameterizedTest()
    @CsvSource({
            "FULL, 0.0",
            "WITHOUT_NO_CAPACITY, 0.01",
            "DEFAULT, 0.85",
            "NONE, 1.0"
    })
    @ResetDataAfter
    void getCandidates_check_that_user_has_required_capacity(String capacityToleranceName, double capacityToleranceValue) {
        AtomicReference<String> userIdWithFullCapacitiesRef = new AtomicReference<>();
        AtomicReference<String> userIdWithFewCapacitiesRef = new AtomicReference<>();
        AtomicReference<String> userIdWithSmallestNumberOfCapacitiesRef = new AtomicReference<>();
        AtomicReference<String> userIdWithNoCapacityRef = new AtomicReference<>();

        AtomicReference<ErhgoOccupation> occupation = new AtomicReference<>();

        txHelper.doInTransaction(() -> {
            var requiredCapacity1 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-1");
            var requiredCapacity2 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-2");
            var requiredCapacity3 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-3");
            var requiredCapacity4 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-4");
            var requiredCapacity5 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-5");
            var requiredCapacity6 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-6");
            var requiredCapacity7 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-7");

            var occupationWithFullCapacities = applicationContext
                    .getBean(ErhgoOccupationMotherObject.class)
                    .withCapacities(
                            requiredCapacity1,
                            requiredCapacity2,
                            requiredCapacity3,
                            requiredCapacity4,
                            requiredCapacity5,
                            requiredCapacity6,
                            requiredCapacity7)
                    .buildAndPersist();

            var occupationWithFewCapacities = applicationContext
                    .getBean(ErhgoOccupationMotherObject.class)
                    .withCapacities(
                            requiredCapacity1,
                            requiredCapacity2,
                            requiredCapacity3,
                            requiredCapacity4,
                            requiredCapacity5,
                            requiredCapacity6)
                    .buildAndPersist();

            var occupationWithSmallestNumberOfCapacities = applicationContext
                    .getBean(ErhgoOccupationMotherObject.class)
                    .withCapacities(requiredCapacity1)
                    .buildAndPersist();

            var userWithFullCapacities = applicationContext.getBean(UserProfileMotherObject.class)
                    .withExperience(occupationWithFullCapacities, 72)
                    .buildAndPersist();

            var userWithFewCapacities = applicationContext.getBean(UserProfileMotherObject.class)
                    .withExperience(occupationWithFewCapacities, 72)
                    .buildAndPersist();

            var userWithSmallestNumberOfCapacities = applicationContext.getBean(UserProfileMotherObject.class)
                    .withExperience(occupationWithSmallestNumberOfCapacities, 72)
                    .buildAndPersist();
            var userWithNoCapacity = applicationContext.getBean(UserProfileMotherObject.class)
                    .buildAndPersist();

            occupation.set(occupationWithFullCapacities);
            userIdWithFullCapacitiesRef.set(userWithFullCapacities.userId());
            userIdWithFewCapacitiesRef.set(userWithFewCapacities.userId());
            userIdWithSmallestNumberOfCapacitiesRef.set(userWithSmallestNumberOfCapacities.userId());
            userIdWithNoCapacityRef.set(userWithNoCapacity.userId());
        });
        var criteria = applicationContext.getBean(SourcingCandidatesCriteria.class)
                .capacitiesIds(occupation.get().getAllCapacitiesId())
                .customCapacityTolerance(capacityToleranceValue);

        var candidatesId = repository.getCandidates(criteria);

        switch (capacityToleranceName) {
            case "FULL" -> assertThat(candidatesId)
                    .containsExactlyInAnyOrder(
                            userIdWithFullCapacitiesRef.get(),
                            userIdWithFewCapacitiesRef.get(),
                            userIdWithSmallestNumberOfCapacitiesRef.get(),
                            userIdWithNoCapacityRef.get()
                    );
            case "WITHOUT_NO_CAPACITY" -> assertThat(candidatesId)
                    .containsExactlyInAnyOrder(
                            userIdWithFullCapacitiesRef.get(),
                            userIdWithFewCapacitiesRef.get(),
                            userIdWithSmallestNumberOfCapacitiesRef.get()
                    )
                    .doesNotContain(userIdWithNoCapacityRef.get());
            case "DEFAULT" -> assertThat(candidatesId)
                    .containsExactlyInAnyOrder(userIdWithFullCapacitiesRef.get(), userIdWithFewCapacitiesRef.get())
                    .doesNotContain(userIdWithNoCapacityRef.get(), userIdWithSmallestNumberOfCapacitiesRef.get());
            case "NONE" -> assertThat(candidatesId)
                    .containsExactly(userIdWithFullCapacitiesRef.get())
            ;
        }
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @ResetDataAfter
    void getCandidates_check_candidates_based_on_criteria(boolean isFullTimeContract) {
        var thresholdCriteria = applicationContext.getBean(CriteriaMotherObject.class)
                .withQuestionType(CriteriaQuestionType.THRESHOLD)
                .withValueCodes("T1", "T2", "T3")
                .buildAndPersist();
        var multipleCriteria = applicationContext.getBean(CriteriaMotherObject.class)
                .withQuestionType(CriteriaQuestionType.MULTIPLE)
                .withValueCodes("M1", "M2", "M3")
                .buildAndPersist();
        var workingTimeCriteria = applicationContext.getBean(CriteriaMotherObject.class)
                .withQuestionType(CriteriaQuestionType.MULTIPLE)
                .withWorkingTimeCriteria()
                .buildAndPersist();

        var fullTimeWorkingTime = getWorkingTimeCriteriaValue(workingTimeCriteria, TypeWorkingTime.FULL_TIME);
        var partTimeWorkingTime = getWorkingTimeCriteriaValue(workingTimeCriteria, TypeWorkingTime.PART_TIME);

        var userUnderThreshold = applicationContext.getBean(UserProfileMotherObject.class)
                .withCriteriaAnswers(List.of(thresholdCriteria.getCriteriaValues().get(0)), Collections.emptyList())
                .buildAndPersist();
        var userRefusedValueOnMultipleCriteria = applicationContext.getBean(UserProfileMotherObject.class)
                .withCriteriaAnswers(Collections.emptyList(), List.of(multipleCriteria.getCriteriaValues().get(1)))
                .buildAndPersist();
        var userSelectedForBoth = applicationContext.getBean(UserProfileMotherObject.class)
                .withCriteriaAnswers(List.of(
                        multipleCriteria.getCriteriaValues().get(1),
                        thresholdCriteria.getCriteriaValues().get(2)
                ), Collections.emptyList())
                .buildAndPersist();

        var userWantsFullTimeJob = applicationContext.getBean(UserProfileMotherObject.class)
                .withCriteriaAnswers(List.of(fullTimeWorkingTime), List.of(partTimeWorkingTime))
                .buildAndPersist();

        var userWantsPartTimeJob = applicationContext.getBean(UserProfileMotherObject.class)
                .withCriteriaAnswers(List.of(partTimeWorkingTime), List.of(fullTimeWorkingTime))
                .buildAndPersist();


        var criteria = applicationContext.getBean(SourcingCandidatesCriteria.class)
                .criteria(new ArrayList<>(List.of(
                        multipleCriteria.getCriteriaValues().get(1).getCode(),
                        thresholdCriteria.getCriteriaValues().get(1).getCode()
                )))
                .workingTimeType(isFullTimeContract ? TypeWorkingTime.FULL_TIME : TypeWorkingTime.PART_TIME);
        var candidateIds = repository.getCandidates(criteria);

        assertThat(candidateIds)
                .contains(userSelectedForBoth.userId())
                .doesNotContain(userUnderThreshold.userId(), userRefusedValueOnMultipleCriteria.userId());

        if (isFullTimeContract) {
            assertThat(candidateIds)
                    .contains(userWantsFullTimeJob.userId(), userWantsPartTimeJob.userId());
        } else {
            assertThat(candidateIds)
                    .contains(userWantsPartTimeJob.userId())
                    .doesNotContain(userWantsFullTimeJob.userId());
        }

    }


    @Test()
    @ResetDataAfter
    void getCandidates_check_that_location_is_inside_candidates_radius() {
        var candidatureLocation = Location.builder()
                .city("Lyon")
                .citycode("69003")
                .postcode("69003")
                .longitude(4.849664F)
                .latitude(45.764043F)
                .build();

        var userFarAwayButCanWork = applicationContext.getBean(UserProfileMotherObject.class).withLocation(
                        Location.builder()
                                .city("Marseille")
                                .longitude(5.36978F)
                                .latitude(43.296482F)
                                .radiusInKm(300)
                                .build()
                )
                .buildAndPersist();

        var userNearButCantWork = applicationContext.getBean(UserProfileMotherObject.class).withLocation(
                        Location.builder()
                                .city("Saint Etienne")
                                .longitude(4.3871779F)
                                .latitude(45.439695F)
                                .radiusInKm(20)
                                .build()
                )
                .buildAndPersist();

        var userNearAndCanWork = applicationContext.getBean(UserProfileMotherObject.class).withLocation(
                        Location.builder()
                                .city("Grenoble")
                                .longitude(5.71667F)
                                .latitude(45.166672F)
                                .radiusInKm(100)
                                .build()
                )
                .buildAndPersist();

        var criteria = applicationContext.getBean(SourcingCandidatesCriteria.class)
                .longitude(candidatureLocation.getLongitude())
                .latitude(candidatureLocation.getLatitude());

        var candidatesId = repository.getCandidates(criteria);

        assertThat(candidatesId)
                .containsExactlyInAnyOrder(userFarAwayButCanWork.userId(), userNearAndCanWork.userId())
                .doesNotContain(userNearButCantWork.userId())
        ;
    }

    @Test
    @ResetDataAfter
    void getCandidates_check_candidates_based_on_classifications() {
        var classification1 = "SO-01";
        var classification2 = "SO-02";

        var userWithRefusedClassification = applicationContext.getBean(UserProfileMotherObject.class)
                .withErhgoClassification(classification1, true)
                .withErhgoClassification(classification2, false)
                .buildAndPersist();
        var userWithNoRefusedClassification = applicationContext.getBean(UserProfileMotherObject.class)
                .withErhgoClassification(classification1, true)
                .withErhgoClassification(classification2, true)
                .buildAndPersist();

        var criteria = applicationContext.getBean(SourcingCandidatesCriteria.class)
                .classifications(List.of(classification1, classification2));

        var candidateIds = repository.getCandidates(criteria);

        assertThat(candidateIds)
                .containsExactly(userWithNoRefusedClassification.userId())
                .doesNotContain(userWithRefusedClassification.userId());
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @ResetDataAfter
    void getCandidates_check_candidates_based_on_last_connection_date(boolean isTopTen) {

        var userConnectedNotSoLongAgo = applicationContext.getBean(UserProfileMotherObject.class)
                .withLastConnectionDateLessThan(59)
                .buildAndPersist();
        var userConnectedLongTimeAgo = applicationContext.getBean(UserProfileMotherObject.class)
                .withLastConnectionDateLessThan(729)
                .buildAndPersist();

        var criteria = applicationContext.getBean(SourcingCandidatesCriteria.class)
                .topTen(isTopTen)
                .lastConnectionBefore(LocalDateTime.now().minusDays(isTopTen ? 60 : 730));

        var candidateIds = repository.getCandidates(criteria);


        if (isTopTen) {
            assertThat(candidateIds)
                    .contains(userConnectedNotSoLongAgo.userId())
                    .doesNotContain(userConnectedLongTimeAgo.userId());
        } else {
            assertThat(candidateIds)
                    .containsExactlyInAnyOrder(userConnectedNotSoLongAgo.userId(), userConnectedLongTimeAgo.userId());
        }
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @ResetDataAfter
    void getCandidates_includes_already_notified_or_not(boolean excludesNotified) {
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .buildAndPersist();

        var userNotifiedByRecruitment = applicationContext.getBean(UserProfileMotherObject.class)
                .withNotifiedRecruitment(recruitment, NotificationType.EMAIL)
                .buildAndPersist();
        var userHasAppliedToRecruitment = applicationContext.getBean(UserProfileMotherObject.class)
                .buildAndPersist();
        var userPushedToRecruitmentAndNotNotified = applicationContext.getBean(UserProfileMotherObject.class)
                .buildAndPersist();

        var anyUser = applicationContext.getBean(UserProfileMotherObject.class)
                .buildAndPersist();

        applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userHasAppliedToRecruitment)
                .withRecruitment(recruitment)
                .withModifiedByUser(true)
                .buildAndPersist();

        applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userPushedToRecruitmentAndNotNotified)
                .withRecruitment(recruitment)
                .withModifiedByUser(false)
                .buildAndPersist();

        var criteria = applicationContext.getBean(SourcingCandidatesCriteria.class)
                .excludingRecruitment(recruitment)
                .excludesNotified(excludesNotified);

        var candidateIds = repository.getCandidates(criteria);

        if (excludesNotified) {
            assertThat(candidateIds)
                    .containsExactlyInAnyOrder(anyUser.userId(), userPushedToRecruitmentAndNotNotified.userId());
        } else {
            assertThat(candidateIds)
                    .containsExactlyInAnyOrder(anyUser.userId(), userPushedToRecruitmentAndNotNotified.userId(), userNotifiedByRecruitment.userId());
        }
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @ResetDataAfter
    void getCandidates_remove_candidates_after_recruitment_is_published(boolean isTopTen) {
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .buildAndPersist();

        var userNotifiedByRecruitment = applicationContext.getBean(UserProfileMotherObject.class)
                .withNotifiedRecruitment(recruitment, NotificationType.EMAIL)
                .buildAndPersist();
        var userHasAppliedToRecruitment = applicationContext.getBean(UserProfileMotherObject.class)
                .buildAndPersist();
        var userPushedToRecruitmentAndNotNotified = applicationContext.getBean(UserProfileMotherObject.class)
                .buildAndPersist();

        applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userHasAppliedToRecruitment)
                .withRecruitment(recruitment)
                .withModifiedByUser(true)
                .buildAndPersist();

        applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userPushedToRecruitmentAndNotNotified)
                .withRecruitment(recruitment)
                .withModifiedByUser(false)
                .buildAndPersist();

        var criteria = applicationContext.getBean(SourcingCandidatesCriteria.class)
                .excludingRecruitment(recruitment)
                .topTen(isTopTen);
        var candidateIds = repository.getCandidates(criteria);

        if (isTopTen) {
            assertThat(candidateIds)
                    .containsExactly(userNotifiedByRecruitment.userId())
                    .doesNotContain(userHasAppliedToRecruitment.userId());
            ;
        } else {
            assertThat(candidateIds)
                    .containsExactly(userPushedToRecruitmentAndNotNotified.userId())
                    .doesNotContain(userHasAppliedToRecruitment.userId(), userNotifiedByRecruitment.userId());
        }
    }

    @Test
    @ResetDataAfter
    void getCandidates_remove_candidates_by_refused_channels() {
        var refusedChannel = "refused-01";
        applicationContext.getBean(OrganizationGenerator.class).createRecruiter(refusedChannel);

        var userWithRefusedChannel = applicationContext.getBean(UserProfileMotherObject.class)
                .withChannels(refusedChannel)
                .buildAndPersist();

        var criteria = applicationContext.getBean(SourcingCandidatesCriteria.class)
                .refusedChannels(Set.of(refusedChannel));

        var candidateIds = repository.getCandidates(criteria);

        assertThat(candidateIds)
                .isEqualTo(List.of())
                .doesNotContain(userWithRefusedChannel.userId());
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @ResetDataAfter
    void getCandidates_check_candidates_have_necessary_mastery_level(boolean isTopTen) {
        AtomicReference<String> userInsideMasteryLevelRangeOfTopTenRef = new AtomicReference<>();
        AtomicReference<String> userOutsideMasteryLevelRangeOfTopTenRef = new AtomicReference<>();

        txHelper.doInTransaction(() -> {
            // Create occupations for user experience which can compute desired mastery level for test
            var capacity1 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-1");
            var capacity2 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-2");
            var capacity3 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-3");
            var capacity4 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-4");
            var capacity5 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-5");

            var expertLevelOccupation1 = applicationContext
                    .getBean(ErhgoOccupationMotherObject.class)
                    .withCapacities(capacity1)
                    .withLevel(MasteryLevel.EXPERT)
                    .buildAndPersist();
            var expertLevelOccupation2 = applicationContext
                    .getBean(ErhgoOccupationMotherObject.class)
                    .withCapacities(capacity2)
                    .withLevel(MasteryLevel.EXPERT)
                    .buildAndPersist();
            var expertLevelOccupation3 = applicationContext
                    .getBean(ErhgoOccupationMotherObject.class)
                    .withCapacities(capacity3)
                    .withLevel(MasteryLevel.EXPERT)
                    .buildAndPersist();
            var expertLevelOccupation4 = applicationContext
                    .getBean(ErhgoOccupationMotherObject.class)
                    .withCapacities(capacity4)
                    .withLevel(MasteryLevel.EXPERT)
                    .buildAndPersist();
            var complexLevelOccupation = applicationContext
                    .getBean(ErhgoOccupationMotherObject.class)
                    .withCapacities(capacity5)
                    .withLevel(MasteryLevel.COMPLEX)
                    .buildAndPersist();

            // User has mastery level of 3.8
            var userInsideMasteryLevelRangeOfTopTen = applicationContext.getBean(UserProfileMotherObject.class)
                    .withExperience(expertLevelOccupation1, 72)
                    .withExperience(expertLevelOccupation2, 72)
                    .withExperience(expertLevelOccupation3, 72)
                    .withExperience(expertLevelOccupation4, 72)
                    .withExperience(complexLevelOccupation, 72)
                    .buildAndPersist();
            // User has mastery level of 3.0
            var userOutsideMasteryLevelRangeOfTopTen = applicationContext.getBean(UserProfileMotherObject.class)
                    .withExperience(complexLevelOccupation, 72)
                    .buildAndPersist();


            userInsideMasteryLevelRangeOfTopTenRef.set(userInsideMasteryLevelRangeOfTopTen.userId());
            userOutsideMasteryLevelRangeOfTopTenRef.set(userOutsideMasteryLevelRangeOfTopTen.userId());
        });
        var criteria = applicationContext.getBean(SourcingCandidatesCriteria.class)
                .topTen(isTopTen)
                .masteryLevelAround(4);

        var candidateIds = repository.getCandidates(criteria);

        if (isTopTen) {
            assertThat(candidateIds)
                    .contains(userInsideMasteryLevelRangeOfTopTenRef.get())
                    .doesNotContain(userOutsideMasteryLevelRangeOfTopTenRef.get())
            ;
        } else {
            assertThat(candidateIds)
                    .containsExactlyInAnyOrder(userInsideMasteryLevelRangeOfTopTenRef.get(), userOutsideMasteryLevelRangeOfTopTenRef.get())
            ;
        }
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @ResetDataAfter
    void getCandidates_between_salary_range(boolean sameMinAndMax) {
        var salaryMin = 20000;
        var salaryMax = 30000;
        var userWithNoSalary = applicationContext.getBean(UserProfileMotherObject.class)
                .withSalary(0)
                .buildAndPersist();
        var userWithSalaryMinValue = applicationContext.getBean(UserProfileMotherObject.class)
                .withSalary(salaryMin)
                .buildAndPersist();
        var userWithSalaryMaxValue = applicationContext.getBean(UserProfileMotherObject.class)
                .withSalary(salaryMax)
                .buildAndPersist();

        var criteria = applicationContext.getBean(SourcingCandidatesCriteria.class)
                .salaryMin(salaryMin)
                .salaryMax(sameMinAndMax ? salaryMin : salaryMax);
        var candidatesId = repository.getCandidates(criteria);
        if (sameMinAndMax) {
            assertThat(candidatesId)
                    .containsExactlyInAnyOrder(userWithNoSalary.userId(), userWithSalaryMinValue.userId());
        } else {
            assertThat(candidatesId)
                    .containsExactlyInAnyOrder(
                            userWithNoSalary.userId(),
                            userWithSalaryMinValue.userId(),
                            userWithSalaryMaxValue.userId())
            ;
        }
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @ResetDataAfter
    void getCandidates_based_on_rome_codes_if_not_top_ten(boolean isTechnical) {
        var rome1 = generateRomeOccupation("1");
        var rome2 = generateRomeOccupation("2");

        var technicalOccupationWithRomeCode = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withTechnical(isTechnical)
                .withRomeOccupations(List.of(rome1, rome2))
                .buildAndPersist();
        var occupationWithNoRomeCode = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .buildAndPersist();

        var userWithRelevantRomeCode = applicationContext.getBean(UserProfileMotherObject.class)
                .withExperience(technicalOccupationWithRomeCode, 72)
                .buildAndPersist();
        var userWithNoRomeCode = applicationContext.getBean(UserProfileMotherObject.class)
                .withExperience(occupationWithNoRomeCode, 72)
                .buildAndPersist();

        var criteria = applicationContext.getBean(SourcingCandidatesCriteria.class)
                .topTen(false)
                .addRomeAndIscoCodes(technicalOccupationWithRomeCode);
        var candidatesId = repository
                .getCandidates(criteria);
        if (isTechnical) {
            assertThat(candidatesId)
                    .containsExactlyInAnyOrder(userWithRelevantRomeCode.userId())
                    .doesNotContain(userWithNoRomeCode.userId());
        } else {

            assertThat(candidatesId).containsExactlyInAnyOrder(userWithNoRomeCode.userId(), userWithRelevantRomeCode.userId());
        }
    }


    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @ResetDataAfter
    void getCandidates_based_on_rome_codes_if_top_ten(boolean isTechnical) {
        AtomicReference<String> userWithTwoExperienceAndMostRelevantCapacitiesRef = new AtomicReference<>();
        AtomicReference<String> userWithTwoExperienceAndLeastRelevantCapacitiesRef = new AtomicReference<>();
        AtomicReference<String> userIdWithNoRomeOccupationExperienceRef = new AtomicReference<>();
        AtomicReference<ErhgoOccupation> occupationRef = new AtomicReference<>();

        txHelper.doInTransaction(() -> {

            var rome1 = generateRomeOccupation("1");
            var rome2 = generateRomeOccupation("2");

            var capacity1 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-1");
            var capacity2 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-2");
            var capacity3 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-3");
            var capacity4 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-4");
            var capacity5 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-5");
            var capacity6 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-6");
            var capacity7 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-7");

            var technicalOccupationWithMostCapacities = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                    .withTechnical(isTechnical)
                    .withRomeOccupations(List.of(rome1, rome2))
                    .withCapacities(capacity1, capacity2, capacity3, capacity4, capacity5)
                    .buildAndPersist();
            var occupationWithNotInCommonCapacities = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                    .withRomeOccupations(List.of(rome1))
                    .withCapacities(capacity6, capacity7)
                    .buildAndPersist();
            var occupationWithLeastRelevantCapacities = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                    .withRomeOccupations(List.of(rome2))
                    .withCapacities(capacity1, capacity2)
                    .buildAndPersist();
            var occupationWithNoRomeCode = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                    .withCapacities(capacity1, capacity2, capacity3, capacity4)
                    .buildAndPersist();

            var userWithTwoExperienceAndMostRelevantCapacities = applicationContext.getBean(UserProfileMotherObject.class)
                    .withExperience(technicalOccupationWithMostCapacities, 72)
                    .withExperience(occupationWithNotInCommonCapacities, 72)
                    .buildAndPersist();
            var userWithTwoExperienceAndLeastRelevantCapacities = applicationContext.getBean(UserProfileMotherObject.class)
                    .withExperience(occupationWithNotInCommonCapacities, 72)
                    .withExperience(occupationWithLeastRelevantCapacities, 72)
                    .buildAndPersist();
            var userWithNoRomeOccupationExperience = applicationContext.getBean(UserProfileMotherObject.class)
                    .withExperience(occupationWithNoRomeCode, 72)
                    .buildAndPersist();

            userWithTwoExperienceAndMostRelevantCapacitiesRef.set(userWithTwoExperienceAndMostRelevantCapacities.userId());
            userWithTwoExperienceAndLeastRelevantCapacitiesRef.set(userWithTwoExperienceAndLeastRelevantCapacities.userId());
            userIdWithNoRomeOccupationExperienceRef.set(userWithNoRomeOccupationExperience.userId());
            occupationRef.set(technicalOccupationWithMostCapacities);
        });
        var criteria = applicationContext.getBean(SourcingCandidatesCriteria.class)
                .topTen(true)
                .capacitiesIds(occupationRef.get().getAllCapacitiesId())
                .customCapacityTolerance(0.01)
                .addRomeAndIscoCodes(occupationRef.get());
        var candidatesId = repository
                .getCandidates(criteria);
        if (isTechnical) {
            assertThat(candidatesId)
                    .containsExactlyInAnyOrder(userWithTwoExperienceAndLeastRelevantCapacitiesRef.get(), userWithTwoExperienceAndMostRelevantCapacitiesRef.get())
                    .doesNotContain(userIdWithNoRomeOccupationExperienceRef.get());
        } else {
            var expectedUserIdsSortedInOrder = List.of(
                    userWithTwoExperienceAndMostRelevantCapacitiesRef.get(),
                    userWithTwoExperienceAndLeastRelevantCapacitiesRef.get(),
                    userIdWithNoRomeOccupationExperienceRef.get()
            );
            assertThat(candidatesId).isEqualTo(expectedUserIdsSortedInOrder);
        }
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @ResetDataAfter
    void getCandidates_limit_number_of_user_when_top_ten(boolean isTopTen) {
        for (int i = 0; i < 34; i++) {
            applicationContext.getBean(UserProfileMotherObject.class)
                    .buildAndPersist();
        }
        var criteria = applicationContext.getBean(SourcingCandidatesCriteria.class)
                .occupationId(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist().getId())
                .topTen(isTopTen);
        var candidatesId = repository.getCandidates(criteria);

        assertThat(candidatesId)
                .hasSize(isTopTen ? 10 : 34)
        ;

    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @ResetDataAfter
    void getCandidates_with_active_search_passed(boolean isActiveSearch) {
        var userWithJobButNotSearching = applicationContext.getBean(UserProfileMotherObject.class)
                .withSituation(Situation.EMPLOYEE)
                .buildAndPersist();
        var userWithJobButSearching = applicationContext.getBean(UserProfileMotherObject.class)
                .withSituation(Situation.STANDBY)
                .buildAndPersist();
        var userWithNoJobAndSearching = applicationContext.getBean(UserProfileMotherObject.class)
                .withSituation(Situation.RESEARCHING)
                .buildAndPersist();
        var userWithNoSituationSpecified = applicationContext.getBean(UserProfileMotherObject.class)
                .withSituation(null)
                .buildAndPersist();
        var criteria = applicationContext.getBean(SourcingCandidatesCriteria.class)
                .activeSearch(isActiveSearch);
        var candidatesId = repository.getCandidates(criteria);

        if (isActiveSearch) {
            assertThat(candidatesId)
                    .doesNotContain(userWithJobButNotSearching.userId())
                    .contains(
                            userWithJobButSearching.userId(),
                            userWithNoJobAndSearching.userId(),
                            userWithNoSituationSpecified.userId()
                    )
            ;
        } else {
            assertThat(candidatesId)
                    .contains(
                            userWithNoJobAndSearching.userId(),
                            userWithJobButSearching.userId(),
                            userWithJobButNotSearching.userId(),
                            userWithNoSituationSpecified.userId()
                    )
            ;
        }
    }

    @Test
    @ResetDataAfter
    void getCandidateDetails_with_correct_details() {
        AtomicReference<UserProfile> userRef = new AtomicReference<>();
        AtomicReference<ErhgoOccupation> occupationRef = new AtomicReference<>();
        var classification1 = "SO-01";
        var classification2 = "SO-02";
        var classification3 = "SO-03";

        txHelper.doInTransaction(() -> {

            var rome1 = generateRomeOccupation("1");
            var rome2 = generateRomeOccupation("2");

            var capacity1 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-1");
            var capacity2 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-2");
            var capacity3 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-3");
            var capacity4 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-4");
            var capacity5 = applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-5");

            var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                    .withRomeOccupations(List.of(rome1, rome2))
                    .withCapacities(capacity1, capacity2, capacity3, capacity4, capacity5)
                    .buildAndPersist();

            var user = applicationContext.getBean(UserProfileMotherObject.class)
                    .withExperience(occupation, 72)
                    .withLocation(Location.builder().city("Lyon").build())
                    .withErhgoClassification(classification1, true)
                    .withErhgoClassification(classification2, false)
                    .withErhgoClassification(classification3, false)
                    .buildAndPersist();
            userRef.set(user);
            occupationRef.set(occupation);
        });

        var criteria = applicationContext.getBean(SourcingCandidatesCriteria.class)
                .topTen(true)
                .capacitiesIds(occupationRef.get().getAllCapacitiesId())
                .classifications(List.of(classification1))
                .addRomeAndIscoCodes(occupationRef.get());
        var candidates = repository.getCandidatesDetail(criteria);
        assertThat(candidates)
                .hasSize(1);
        var candidateDetail = buildCandidateDetailDTO(candidates.get(0));
        assertThat(candidateDetail.getId()).isEqualTo(userRef.get().userId());
        assertThat(candidateDetail.getCity()).isEqualTo(userRef.get().getCity());
        assertThat(candidateDetail.getMasteryLevelAsFloat()).isEqualTo(userRef.get().masteryLevel());
        assertThat(candidateDetail.getCapacityScore()).isEqualTo(userRef.get().getAllCapacities().size());
        assertThat(candidateDetail.getRomeExperiencesCount()).isEqualTo(userRef.get().experiences().size());
        assertThat(candidateDetail.getRefusedClassifications()).containsExactlyInAnyOrder(classification2, classification3);

    }

    private CandidateDetailDTO buildCandidateDetailDTO(com.erhgo.repositories.dto.CandidateDetailDTO dto) {
        return new CandidateDetailDTO()
                .id(dto.getId())
                .city(dto.getCity())
                .masteryLevelAsFloat(dto.getMasteryLevelAsFloat())
                .capacityScore(dto.getCapacityScore())
                .romeExperiencesCount(dto.getRomeExperiencesCount())
                .refusedClassifications(dto.getRefusedClassifications() != null ?
                        Arrays.stream(dto.getRefusedClassifications().split(",")).toList() :
                        List.of())
                ;
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @ResetDataAfter
    void getCandidates_get_all_candidates_when_criteria_contracts_refused(boolean isProTypeContract) {
        var typeContractCriteria = applicationContext.getBean(CriteriaMotherObject.class)
                .withQuestionType(CriteriaQuestionType.MULTIPLE)
                .withTypeContractCriteria()
                .buildAndPersist();

        var permanentTypeContract = getTypeContractCriteriaValue(typeContractCriteria, TypeContractCategory.PERMANENT);
        var temporaryTypeContract = getTypeContractCriteriaValue(typeContractCriteria, TypeContractCategory.TEMPORARY);
        var proTypeContract = getTypeContractCriteriaValue(typeContractCriteria, TypeContractCategory.PRO);

        assert permanentTypeContract != null;
        assert temporaryTypeContract != null;
        assert proTypeContract != null;

        var userSelectingPermanentContract = applicationContext.getBean(UserProfileMotherObject.class)
                .withCriteriaAnswers(List.of(permanentTypeContract), List.of(temporaryTypeContract, proTypeContract))
                .buildAndPersist();
        var userSelectingTemporaryContract = applicationContext.getBean(UserProfileMotherObject.class)
                .withCriteriaAnswers(List.of(temporaryTypeContract), List.of(permanentTypeContract, proTypeContract))
                .buildAndPersist();
        var userSelectingProContract = applicationContext.getBean(UserProfileMotherObject.class)
                .withCriteriaAnswers(List.of(proTypeContract), List.of(permanentTypeContract, temporaryTypeContract))
                .buildAndPersist();
        var userIndifferentToContractType = applicationContext.getBean(UserProfileMotherObject.class)
                .withCriteriaAnswers(Collections.emptyList(), Collections.emptyList())
                .buildAndPersist();


        var criteria = applicationContext.getBean(SourcingCandidatesCriteria.class)
                .typeContractCategory(isProTypeContract ? TypeContractCategory.PRO : TypeContractCategory.PERMANENT);

        var candidatesId = repository.getCandidates(criteria);

        if (isProTypeContract) {
            assertThat(candidatesId)
                    .containsExactlyInAnyOrder(
                            userSelectingProContract.userId(),
                            userIndifferentToContractType.userId()
                    )
                    .doesNotContain(
                            userSelectingTemporaryContract.userId(),
                            userSelectingPermanentContract.userId()
                    );
        } else {
            assertThat(candidatesId)
                    .containsExactlyInAnyOrder(
                            userIndifferentToContractType.userId(),
                            userSelectingPermanentContract.userId()
                    )
                    .doesNotContain(userSelectingProContract.userId());
        }
    }

    private CriteriaValue getTypeContractCriteriaValue(Criteria typeContractCriteria, TypeContractCategory typeContractCategory) {
        return typeContractCriteria.getCriteriaValues()
                .stream()
                .filter(c -> Objects.equals(c.getCode(), CriteriaValue.getValueCodeForTypeContractCategory(typeContractCategory)))
                .findFirst()
                .orElse(null);
    }

    private CriteriaValue getWorkingTimeCriteriaValue(Criteria workingTimeTypeCriteria, TypeWorkingTime workingTime) {
        return workingTimeTypeCriteria.getCriteriaValues()
                .stream()
                .filter(c -> Objects.equals(c.getCode(), CriteriaValue.getValueCodeForTypeWorkingTime(workingTime)))
                .findFirst()
                .orElse(null);
    }

    private RomeOccupation generateRomeOccupation(String id) {
        return applicationContext.getBean(ErhgoOccupationGenerator.class).createRomeOccupation("R-%s".formatted(id), "Rome %s".formatted(id));
    }
}
