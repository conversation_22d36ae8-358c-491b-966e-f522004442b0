package com.erhgo.repositories;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.referential.Activity;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.ErhgoOccupationGenerator;
import com.erhgo.generators.JobActivityLabelGenerator;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;

import java.util.Set;

class ActivityRepositoryTest extends AbstractIntegrationTest {
    @Autowired
    private CapacityGenerator capacityGenerator;

    @Autowired
    private JobActivityLabelGenerator activityLabelGenerator;

    @Autowired
    private ErhgoOccupationGenerator erhgoOccupationGenerator;

    @Autowired
    private ActivityRepository activityRepository;

    @ResetDataAfter
    @Test
    @DisplayName("Given two activities with distinct capacities " +
            "When I search using one capacity of the first activity " +
            "Then only the first activity is retrieved")
    void findTopActivitiesForRelativeCapacities() {
        var ca1 = capacityGenerator.createCapacity("CA-1");
        var ca2 = capacityGenerator.createCapacity("CA-2");
        var ca3 = capacityGenerator.createCapacity("CA-3");

        var a1 = activityLabelGenerator.createActivity("A1", ca1, ca2);
        var a2 = activityLabelGenerator.createActivity("A2", ca3);

        erhgoOccupationGenerator.createErhgoOccupation(new ErhgoOccupationMotherObject()
                .withOptionalActivities(a1, a2)
                .instance());

        var foundActivities = activityRepository.topActivitiesForRelativeCapacities(Set.of(ca2), Pageable.unpaged());
        Assertions.assertThat(foundActivities)
                .hasSize(1)
                .allMatch(a -> a.getActivity().getUuid().equals(a1.getActivity().getUuid()) && a.getCommonCapacitiesCount() == 1);
    }

    @ResetDataAfter
    @Test
    @DisplayName("Given two activities sharing some capacities " +
            "When I search using two capacities " +
            "Then the two activities are retrieved in correct order")
    void findActivitiesInCorrectOrder() {
        var ca1 = capacityGenerator.createCapacity("CA-1");
        var ca2 = capacityGenerator.createCapacity("CA-2");
        var ca3 = capacityGenerator.createCapacity("CA-3");

        var a1 = activityLabelGenerator.createActivity("A1", ca1, ca2);
        var a2 = activityLabelGenerator.createActivity("A2", ca1, ca3);

        erhgoOccupationGenerator.createErhgoOccupation(new ErhgoOccupationMotherObject()
                .withOptionalActivities(a1, a2)
                .instance());

        var foundActivities = activityRepository.topActivitiesForRelativeCapacities(Set.of(ca1, ca3), Pageable.unpaged());
        Assertions.assertThat(foundActivities)
                .extracting(ActivityRepository.RelativeActivityDTO::getActivity)
                .extracting(Activity::getUuid)
                .containsExactly(a2.getActivity().getUuid(), a1.getActivity().getUuid());
    }

    @ResetDataAfter
    @Test
    @DisplayName("Given two activities sharing same capacity, one is associated to occupation, the other is not" +
            "When I search using capacity " +
            "Then only the one associated to occupation is retrieved")
    void findActivitiesWithNonAssociatedCapacity() {
        var ca1 = capacityGenerator.createCapacity("CA-1");
        var ca2 = capacityGenerator.createCapacity("CA-2");

        var a1 = activityLabelGenerator.createActivity("A1", ca1, ca2);
        var a2 = activityLabelGenerator.createActivity("A2", ca1);

        erhgoOccupationGenerator.createErhgoOccupation(new ErhgoOccupationMotherObject()
                .withOptionalActivities(a1)
                .instance());

        var foundActivities = activityRepository.topActivitiesForRelativeCapacities(Set.of(ca1), Pageable.unpaged());
        Assertions.assertThat(foundActivities)
                .hasSize(1)
                .allMatch(a -> a.getActivity().getUuid().equals(a1.getActivity().getUuid()) && a.getCommonCapacitiesCount() == 1);
    }
}
