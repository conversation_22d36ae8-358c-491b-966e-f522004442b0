package com.erhgo.repositories;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.generators.DataGeneratorService;
import com.erhgo.generators.TestFixtures;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;

import java.util.Collections;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;

public class RecruitmentRepositoryTest extends AbstractIntegrationTestWithFixtures {
    @Autowired
    private RecruitmentRepository recruitmentRepository;

    @Autowired
    DataGeneratorService dataGeneratorService;

    @Test
    public void find_recruitments_should_return_recruitments_with_candidatures_count() {
        var recruitment = dataGeneratorService.createRecruitment(RecruitmentState.PUBLISHED);

        var recruitments = recruitmentRepository.findWithCandidatureCountByOrganizationCodeIn(Collections.singleton(recruitment.getOrganizationCode()), false, false, null, false, true, null, null, null, Pageable.unpaged());

        assertThat(recruitments, hasItem(hasProperty("recruitment", hasProperty("code", equalTo(recruitment.getCode())))));
        var recruitmentSummaryDTO = recruitments.stream().filter(r -> r.getRecruitment().getCode().equals(recruitment.getCode())).findFirst().get();
        assertThat(recruitmentSummaryDTO.getCandidatureCount(), equalTo(0));
        assertThat(recruitmentSummaryDTO.getMatchingCandidatureCount(), equalTo(0));

        assertThat(recruitments, hasItem(hasProperty("recruitment", hasProperty("code", equalTo(TestFixtures.RECRUITMENT_WITH_MATCHING_CANDIDATURE.getCode())))));
        recruitmentSummaryDTO = recruitments.stream().filter(r -> r.getRecruitment().getCode().equals(TestFixtures.RECRUITMENT_WITH_MATCHING_CANDIDATURE.getCode())).findFirst().get();
        assertThat(recruitmentSummaryDTO.getCandidatureCount(), equalTo(2));
        assertThat(recruitmentSummaryDTO.getMatchingCandidatureCount(), equalTo(1));

        assertThat(recruitments, hasItem(hasProperty("recruitment", hasProperty("code", equalTo(TestFixtures.RECRUITMENT_WITHOUT_MATCHING_CANDIDATURES.getCode())))));
        recruitmentSummaryDTO = recruitments.stream().filter(r -> r.getRecruitment().getCode().equals(TestFixtures.RECRUITMENT_WITHOUT_MATCHING_CANDIDATURES.getCode())).findFirst().get();
        assertThat(recruitmentSummaryDTO.getCandidatureCount(), equalTo(1));
        assertThat(recruitmentSummaryDTO.getMatchingCandidatureCount(), equalTo(0));
    }
}
