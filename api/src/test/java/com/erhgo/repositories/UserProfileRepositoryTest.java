package com.erhgo.repositories;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.PageRequest;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
class UserProfileRepositoryTest extends AbstractIntegrationTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Test
    void findUsersToIndex() {

        var userToIndex1 = applicationContext.getBean(UserProfileMotherObject.class).withLastIndexationDate(Date.from(Instant.now())).withIndexationRequiredDate(Date.from(Instant.now().minus(5, ChronoUnit.HOURS))).buildAndPersist();
        var userToIndex2 = applicationContext.getBean(UserProfileMotherObject.class).withLastIndexationDate(null).withIndexationRequiredDate(Date.from(Instant.now().minus(5, ChronoUnit.HOURS))).buildAndPersist();
        var userToIndex3 = applicationContext.getBean(UserProfileMotherObject.class).withLastIndexationDate(Date.from(Instant.now().minus(5, ChronoUnit.MINUTES))).withIndexationRequiredDate(Date.from(Instant.now().minus(5, ChronoUnit.HOURS))).buildAndPersist();
        var userToIndex4 = applicationContext.getBean(UserProfileMotherObject.class).withLastIndexationDate(Date.from(Instant.now().minus(1, ChronoUnit.MINUTES))).withIndexationRequiredDate(Date.from(Instant.now().minus(5, ChronoUnit.HOURS))).buildAndPersist();
        var userNotToIndexNoDate = applicationContext.getBean(UserProfileMotherObject.class).buildAndPersist();
        var userNotToIndexDateTooShort = applicationContext.getBean(UserProfileMotherObject.class).withIndexationRequiredDate(Date.from(Instant.now().plus(5, ChronoUnit.HOURS))).buildAndPersist();

        var usersToIndex = userProfileRepository.findUsersToIndex(Date.from(Instant.now()), PageRequest.of(0, 3));

        Assertions.assertThat(usersToIndex).extracting(UserProfile::userId).containsExactly(userToIndex2.userId(), userToIndex3.userId(), userToIndex4.userId());
    }


}
