package com.erhgo.repositories;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.userprofile.CapacityOccurrence;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.UserProfileGenerator;
import jakarta.persistence.EntityManager;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.IntStream;

// A non-conventional test, to validate custom rules related to CapacityOccurrence and com.erhgo.services.eventlistener.CapacityOccurrenceEventListener
class CapacityOccurrenceRepositoryTest extends AbstractIntegrationTest {

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private CapacityGenerator capacityGenerator;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Autowired
    private EntityManager entityManager;

    private static final int NB_CONCURRENT_UPDATES = 10;


    private final AtomicInteger counter = new AtomicInteger(0);

    @BeforeEach
    public void resetCounter() {
        this.counter.set(0);
    }

    @Test
    @DisplayName("Given a user when a capacity is added to empty profile then CapacityOccurrence contains one entry")
    @ResetDataAfter
    void addSingleCapacityForEmptyUser() {

        var user = userProfileGenerator.createUserProfile();
        var capacityCode = "CA1-1";

        txHelper.doInTransaction(() -> {
            var capacity = capacityGenerator.createCapacity(capacityCode);
            userProfileRepository.findByUserId(user.userId())
                    .orElseThrow()
                    .addCapacities(Collections.singleton(capacity));
        });


        txHelper.doInTransaction(() -> {
            var occurrences = entityManager
                    .createQuery("SELECT co FROM CapacityOccurrence co", CapacityOccurrence.class)
                    .getResultList();

            Assertions.assertThat(occurrences)
                    .hasSize(1)
                    .first()
                    .hasFieldOrPropertyWithValue("occurrence", 1)
                    .hasFieldOrPropertyWithValue("capacity.code", capacityCode);
        });
    }

    @Test
    @DisplayName("Given a user when a capacity is added twice to empty profile then CapacityOccurrence contains one entry with occurrence 2")
    @ResetDataAfter
    void addSingleCapacityTwiceForEmptyUser() {

        var user = userProfileGenerator.createUserProfile();
        var capacityCode = "CA1-1";

        txHelper.doInTransaction(() -> {
            var capacity = capacityGenerator.createCapacity(capacityCode);
            userProfileRepository.findByUserId(user.userId())
                    .orElseThrow()
                    .addCapacities(List.of(capacity, capacity));
        });

        txHelper.doInTransaction(() -> {
            var occurrences = entityManager
                    .createQuery("SELECT co FROM CapacityOccurrence co", CapacityOccurrence.class)
                    .getResultList();

            Assertions.assertThat(occurrences)
                    .hasSize(1)
                    .first()
                    .hasFieldOrPropertyWithValue("occurrence", 2)
                    .hasFieldOrPropertyWithValue("capacity.code", capacityCode);
        });
    }

    @Test
    @DisplayName("Given a user when a capacity is added twice to profile containing this capacity " +
            "then CapacityOccurrence contains one entry with occurrence 3")
    @ResetDataAfter
    void addSingleCapacityTwiceForNonEmptyUser() {

        var uuid = UUID.randomUUID();
        var code = "CA1-1";
        txHelper.doInTransaction(() -> {
            var capacity = capacityGenerator.createCapacity(code);
            var user = userProfileGenerator.createUserProfile(uuid);
            user.addCapacities(Set.of(capacity));
        });


        txHelper.doInTransaction(() -> {
            Capacity capacity = getCapacity(code);

            userProfileRepository.findByUserId(uuid.toString())
                    .orElseThrow()
                    .addCapacities(List.of(capacity, capacity));
        });

        txHelper.doInTransaction(() -> {
            var occurrences = entityManager
                    .createQuery("SELECT co FROM CapacityOccurrence co", CapacityOccurrence.class)
                    .getResultList();

            Assertions.assertThat(occurrences)
                    .hasSize(1)
                    .first()
                    .hasFieldOrPropertyWithValue("occurrence", 3)
                    .hasFieldOrPropertyWithValue("capacity.code", code);
        });
    }

    @Test
    @DisplayName("Given a user when a capacity is removed twice from profile containing this capacity three times " +
            "then CapacityOccurrence contains one entry with occurrence 1")
    @ResetDataAfter
    void removeSingleCapacityForNonEmptyUser() {

        var uuid = UUID.randomUUID();
        var code = "CA1-1";
        txHelper.doInTransaction(() -> {
            var capacity = capacityGenerator.createCapacity(code);
            var user = userProfileGenerator.createUserProfile(uuid);
            user.addCapacities(List.of(capacity, capacity, capacity));
        });


        txHelper.doInTransaction(() -> {
            Capacity capacity = getCapacity(code);

            userProfileRepository.findByUserId(uuid.toString())
                    .orElseThrow()
                    .removeCapacities(List.of(capacity, capacity));
        });

        txHelper.doInTransaction(() -> {
            var occurrences = entityManager
                    .createQuery("SELECT co FROM CapacityOccurrence co", CapacityOccurrence.class)
                    .getResultList();

            Assertions.assertThat(occurrences)
                    .hasSize(1)
                    .first()
                    .hasFieldOrPropertyWithValue("occurrence", 1)
                    .hasFieldOrPropertyWithValue("capacity.code", code);
        });
    }

    @Test
    @DisplayName("Given a user when a capacity is removed twice from profile containing this capacity twice" +
            "then CapacityOccurrence is deleted")
    @ResetDataAfter
    void deleteOccurrenceForNonEmptyUser() {

        var uuid = UUID.randomUUID();
        var code = "CA1-1";
        txHelper.doInTransaction(() -> {
            var capacity = capacityGenerator.createCapacity(code);
            var user = userProfileGenerator.createUserProfile(uuid);
            user.addCapacities(List.of(capacity, capacity));
        });


        txHelper.doInTransaction(() -> {
            Capacity capacity = getCapacity(code);

            userProfileRepository.findByUserId(uuid.toString())
                    .orElseThrow()
                    .removeCapacities(List.of(capacity, capacity));
        });

        txHelper.doInTransaction(() -> {
            var occurrences = entityManager
                    .createQuery("SELECT co FROM CapacityOccurrence co", CapacityOccurrence.class)
                    .getResultList();

            Assertions.assertThat(occurrences).isEmpty();
        });
    }

    @Disabled("Sorry, our runners don't like this test, coming back soon :-( ")
    @Test
    @DisplayName("Given a user when a capacity is added " + NB_CONCURRENT_UPDATES + " times from distinct threads " +
            "then CapacityOccurrence contains " + NB_CONCURRENT_UPDATES)
    @ResetDataAfter
    void addConcurrentCapacity() throws InterruptedException {

        var userUuid = UUID.randomUUID();
        var capacityCode = "CA1-1";
        txHelper.doInTransaction(() -> {
            capacityGenerator.createCapacity(capacityCode);
            userProfileGenerator.createUserProfile(userUuid);
        });

        var executor = Executors.newFixedThreadPool(NB_CONCURRENT_UPDATES);

        IntStream.range(0, NB_CONCURRENT_UPDATES).forEach(index -> executor.submit(() -> addCapacityToUser(userUuid, capacityCode)));
        executor.shutdown();
        Assertions.assertThat(executor.awaitTermination(2, TimeUnit.MINUTES)).isTrue();

        txHelper.doInTransaction(() -> {
            var occurrences = entityManager
                    .createQuery("SELECT co FROM CapacityOccurrence co", CapacityOccurrence.class)
                    .getResultList();

            Assertions.assertThat(occurrences)
                    .hasSize(1)
                    .first()
                    .hasFieldOrPropertyWithValue("occurrence", NB_CONCURRENT_UPDATES)
                    .hasFieldOrPropertyWithValue("capacity.code", capacityCode);
        });
    }


    @Disabled("Sorry, our runners don't like this test, coming back soon :-( ")
    @Test
    @DisplayName("Given a user with capacity with " + NB_CONCURRENT_UPDATES + " occurrences when capacity is removed " + NB_CONCURRENT_UPDATES + " times from distinct threads " +
            "then CapacityOccurrence is empty")
    @ResetDataAfter
    void addAndRemoveConcurrentCapacity() throws InterruptedException {

        var userUuid = UUID.randomUUID();
        var capacityCode = "CA1-1";
        txHelper.doInTransaction(() -> {
            var capacity = capacityGenerator.createCapacity(capacityCode);
            userProfileGenerator.createUserProfile(userUuid)
                    .addCapacities(
                            IntStream.range(0, NB_CONCURRENT_UPDATES).mapToObj(i -> capacity).toList()
                    );
        });

        var executor = Executors.newFixedThreadPool(NB_CONCURRENT_UPDATES);
        IntStream.range(0, NB_CONCURRENT_UPDATES).forEach(index -> executor.submit(() -> removeCapacityFromUser(userUuid, capacityCode)));
        executor.shutdown();
        Assertions.assertThat(executor.awaitTermination(NB_CONCURRENT_UPDATES * 300, TimeUnit.SECONDS)).isTrue();

        txHelper.doInTransaction(() -> {
            var occurrences = entityManager
                    .createQuery("SELECT co FROM CapacityOccurrence co", CapacityOccurrence.class)
                    .getResultList();

            Assertions.assertThat(occurrences).isEmpty();
        });
    }

    @Test
    @DisplayName("Given a user when a capacity is added with recursive capacity then " +
            "then CapacityOccurrence contains recursiceOccurrence")
    @ResetDataAfter
    void addRecursiveCapacity() {

        var userUuid = UUID.randomUUID();
        var inducedCapacityCode = "CA1-1";
        var capacityCode = "CA2-1";
        var topCapacityCode = "CA3-1";
        txHelper.doInTransaction(() -> {
            var inducedCapacity = capacityGenerator.createCapacity(inducedCapacityCode);
            var capacity = capacityGenerator.createCapacity(capacityCode, inducedCapacity);
            capacityGenerator.createCapacity(topCapacityCode, capacity, inducedCapacity);
            userProfileGenerator.createUserProfile(userUuid);
        });

        txHelper.doInTransaction(() -> {
            userProfileRepository.findByUserId(userUuid.toString())
                    .orElseThrow()
                    .addCapacities(List.of(getCapacity(topCapacityCode)));
        });

        txHelper.doInTransaction(() -> {
            var occurrences = entityManager
                    .createQuery("SELECT co FROM CapacityOccurrence co ORDER BY co.capacity.code", CapacityOccurrence.class)
                    .getResultList();

            Assertions.assertThat(occurrences)
                    .hasSize(3);
            Assertions.assertThat(occurrences.get(0))
                    .hasFieldOrPropertyWithValue("occurrence", 0)
                    .hasFieldOrPropertyWithValue("recursiveOccurrence", 2)
                    .hasFieldOrPropertyWithValue("capacity.code", inducedCapacityCode);
            Assertions.assertThat(occurrences.get(1))
                    .hasFieldOrPropertyWithValue("occurrence", 0)
                    .hasFieldOrPropertyWithValue("recursiveOccurrence", 1)
                    .hasFieldOrPropertyWithValue("capacity.code", capacityCode);
            Assertions.assertThat(occurrences.get(2))
                    .hasFieldOrPropertyWithValue("occurrence", 1)
                    .hasFieldOrPropertyWithValue("recursiveOccurrence", 0)
                    .hasFieldOrPropertyWithValue("capacity.code", topCapacityCode);
        });
    }

    @Test
    @DisplayName("Given a user when a capacity with induced capacity is removed then " +
            " CapacityOccurrence is deleted")
    @ResetDataAfter
    void removeRecursiveCapacity() {

        var userUuid = UUID.randomUUID();
        var inducedCapacityCode = "CA1-1";
        var capacityCode = "CA2-1";
        var topCapacityCode = "CA3-1";
        txHelper.doInTransaction(() -> {
            var inducedCapacity = capacityGenerator.createCapacity(inducedCapacityCode);
            var capacity = capacityGenerator.createCapacity(capacityCode, inducedCapacity);
            var topLevelCapacity = capacityGenerator.createCapacity(topCapacityCode, capacity, inducedCapacity);
            userProfileGenerator.createUserProfile(userUuid).addCapacities(Set.of(topLevelCapacity));
        });

        txHelper.doInTransaction(() -> {
            userProfileRepository.findByUserId(userUuid.toString())
                    .orElseThrow()
                    .removeCapacities(List.of(getCapacity(topCapacityCode)));
        });

        txHelper.doInTransaction(() -> {
            var occurrences = entityManager
                    .createQuery("SELECT co FROM CapacityOccurrence co", CapacityOccurrence.class)
                    .getResultList();

            Assertions.assertThat(occurrences)
                    .isEmpty();
        });
    }


    @Test
    @DisplayName("Given a user when a capacity (present directly and recursively) is directly removed then " +
            " CapacityOccurrence for capacity is not deleted")
    @ResetDataAfter
    void removeRecursiveCapacityNotDeleted() {

        var userUuid = UUID.randomUUID();
        var inducedCapacityCode = "CA1-1";
        var capacityCode = "CA2-1";
        var topCapacityCode = "CA3-1";
        txHelper.doInTransaction(() -> {
            var inducedCapacity = capacityGenerator.createCapacity(inducedCapacityCode);
            var capacity = capacityGenerator.createCapacity(capacityCode, inducedCapacity);
            var topLevelCapacity = capacityGenerator.createCapacity(topCapacityCode, capacity, inducedCapacity);
            userProfileGenerator.createUserProfile(userUuid).addCapacities(Set.of(topLevelCapacity, inducedCapacity));
        });

        txHelper.doInTransaction(() -> {
            userProfileRepository.findByUserId(userUuid.toString())
                    .orElseThrow()
                    .removeCapacities(List.of(getCapacity(topCapacityCode)));
        });

        txHelper.doInTransaction(() -> {
            var occurrences = entityManager
                    .createQuery("SELECT co FROM CapacityOccurrence co", CapacityOccurrence.class)
                    .getResultList();

            Assertions.assertThat(occurrences)
                    .hasSize(1)
                    .first()
                    .hasFieldOrPropertyWithValue("occurrence", 1)
                    .hasFieldOrPropertyWithValue("recursiveOccurrence", 0)
                    .hasFieldOrPropertyWithValue("capacity.code", inducedCapacityCode);

        });
    }

    private void addCapacityToUser(UUID uuid, String code) {
        txHelper.doInTransaction(() -> {
            Capacity capacity = getCapacity(code);

            userProfileRepository.findByUserId(uuid.toString())
                    .orElseThrow()
                    .addCapacities(List.of(capacity));
            counter.incrementAndGet();
            while (counter.get() < NB_CONCURRENT_UPDATES) {
                Thread.onSpinWait();
            }
        });
    }

    private void removeCapacityFromUser(UUID uuid, String code) {
        txHelper.doInTransaction(() -> {
            var capacity = getCapacity(code);

            userProfileRepository.findByUserId(uuid.toString())
                    .orElseThrow()
                    .removeCapacities(List.of(capacity));
            counter.incrementAndGet();
            while (counter.get() < NB_CONCURRENT_UPDATES) {
                Thread.onSpinWait();
            }
        });
    }

    private Capacity getCapacity(String code) {
        return entityManager.createQuery("select c from Capacity c where c.code = :code", Capacity.class)
                .setParameter("code", code)
                .getResultList()
                .get(0);
    }
}
