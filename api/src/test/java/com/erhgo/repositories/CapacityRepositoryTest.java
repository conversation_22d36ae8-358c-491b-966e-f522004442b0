package com.erhgo.repositories;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.transaction.annotation.Transactional;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.beans.HasPropertyWithValue.hasProperty;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

public class CapacityRepositoryTest extends AbstractIntegrationTestWithFixtures {

    private Pageable pageable = PageRequest.of(0, 100, Sort.Direction.valueOf("ASC"), "title");

    @Autowired
    private CapacityRepository capacityRepository;

    @Test
    public void shouldFindCapacityByCode() {
        // when
        final var result = capacityRepository.findByCodeContainingIgnoreCaseOrTitleContainingIgnoreCase("CA1-01", "CA1-01", pageable);

        // then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals("Regarder avec attention", result.getContent().get(0).getTitle());
    }

    @Test
    public void shouldFindCapacityByTitle() {
        // when
        final var result = capacityRepository.findByCodeContainingIgnoreCaseOrTitleContainingIgnoreCase("Analyser un besoin", "Analyser un besoin", pageable);

        // then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals("Analyser un besoin", result.getContent().get(0).getTitle());
    }

    @Test
    @Transactional(readOnly = true)
    public void shouldFindCapacityByCodeOrTitle() {
        // when
        final var result = capacityRepository.findByCodeContainingIgnoreCaseOrTitleContainingIgnoreCase("CA2", "CA2", pageable);

        // then
        assertNotNull(result);
        assertEquals(23, result.getTotalElements());
        var firstCapacity = result.getContent().get(0);
        assertEquals("Anticiper les mouvements autour de soi", firstCapacity.getTitle());

        assertEquals(2, firstCapacity.getInducedCapacities().size());
        assertThat(firstCapacity.getInducedCapacities(), containsInAnyOrder(
                hasProperty("code", is("CA1-15")),
                hasProperty("code", is("CA1-23"))
        ));

    }

    @Test
    public void shouldFindCapacityByTitleInLowerCase() {
        // when
        final var result = capacityRepository.findByCodeContainingIgnoreCaseOrTitleContainingIgnoreCase("ca", "ca", pageable);

        // then
        assertNotNull(result);
        assertEquals(65, result.getTotalElements());
        assertEquals("Analyser (une situation, une information)", result.getContent().get(0).getTitle());
    }

    @Test
    public void shouldFindCapacityByTitleWithSpecialCharacters() {
        // when
        final var result = capacityRepository.findByCodeContainingIgnoreCaseOrTitleContainingIgnoreCase("(une situation, une information", "(une situation, une information", pageable);

        // then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals("Analyser (une situation, une information)", result.getContent().get(0).getTitle());
    }

    @Test
    public void shouldNotFindCapacityByTitle() {
        // when
        final var result = capacityRepository.findByCodeContainingIgnoreCaseOrTitleContainingIgnoreCase("xx", "xx", pageable);

        // then
        assertNotNull(result);
        assertEquals(0, result.getTotalElements());
    }

}
