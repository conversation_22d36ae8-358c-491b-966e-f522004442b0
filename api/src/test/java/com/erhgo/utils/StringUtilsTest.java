package com.erhgo.utils;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.RepeatedTest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

class StringUtilsTest {

    @ParameterizedTest
    @NullAndEmptySource
    void anonymize_tolerateNullOrEmptyString(String source) {
        assertThat(StringUtils.anonymize(source)).isEmpty();
    }

    @ParameterizedTest
    @CsvSource({"a,a.", "Ab,A.", "--A,-.",})
    void anonymize_returnFirstLetter(String input, String expected) {
        assertThat(StringUtils.anonymize(input)).isEqualTo(expected);
    }

    @Test
    void insensitiveComparator_should_ignore_case_for_same_letter() {
        var s1 = "Attention";
        var s2 = "attention";
        assertThat(StringUtils.DIATRIC_AND_CASE_INSENSITIVE_COMPARATOR.INSTANCE.compare(s1, s2)).isZero();
    }

    @Test
    void insensitiveComparator_should_ignore_case() {
        var s1 = "Bon";
        var s2 = "alors";
        assertThat(StringUtils.DIATRIC_AND_CASE_INSENSITIVE_COMPARATOR.INSTANCE.compare(s1, s2)).isPositive();
    }

    @Test
    void insensitiveComparator_should_ignore_accent_and_case() {
        var s1 = "alors";
        var s2 = "ÂlörŠ";
        assertThat(StringUtils.DIATRIC_AND_CASE_INSENSITIVE_COMPARATOR.INSTANCE.compare(s1, s2)).isZero();
    }

    @Test
    void insensitiveComparator_should_compare_strings_same_case() {
        var s1 = "alors";
        var s2 = "bien";
        assertThat(StringUtils.DIATRIC_AND_CASE_INSENSITIVE_COMPARATOR.INSTANCE.compare(s1, s2)).isNegative();
    }

    @Test
    void insensitiveComparator_should_compare_strings_different_case_not_first_letter() {
        var s1 = "Donc Rien à dire";
        var s2 = "DÔNC räS ";
        assertThat(StringUtils.DIATRIC_AND_CASE_INSENSITIVE_COMPARATOR.INSTANCE.compare(s1, s2)).isPositive();
    }

    @ParameterizedTest
    @CsvSource({"06123456978,00336123456978",
            "+3360011,003360011",
            "42,003342",
            "e4574585256,00334574585256",
            "06-01-01-02-45,0033601010245",
            "(06)-01-01-02-45,0033601010245",
            ",",
            "06/05/01/11/54,0033605011154",
            " 06./85/45(/78)47.,0033685457847"})
    void cleanupPhoneNumber(String input, String expected) {
        assertThat(StringUtils.normalizePhone(input)).isEqualTo(expected);
    }

    @RepeatedTest(40)
    void generatePassword() {
        var regex = "^[A-Z][a-z]{3}:[0-9]{3}$";
        assertThat(StringUtils.generateRandomPassword()).matches(regex);
    }

    @ParameterizedTest
    @ValueSource(strings = {"<EMAIL>", "<EMAIL>", "<EMAIL>", "Éric<EMAIL>"})
    void emailSucceed(String email) {
        assertThat(StringUtils.isEmail(email)).isTrue();
    }

    @ParameterizedTest
    @NullAndEmptySource
    @ValueSource(strings = {"a@a.", "", "eric+test@john", "noat.com", "nodot@com", "..@abc.fr"})
    void emailFails(String email) {
        assertThat(StringUtils.isEmail(email)).isFalse();
    }

    @RepeatedTest(10)
    void generate_random_string() {
        assertThat(StringUtils.generateRandom4CharactersString()).matches("[A-Z][0-9][A-Z][0-9]");
    }

    @RepeatedTest(10)
    void get_random_color() {
        assertThat(StringUtils.getRandomColorIndex()).matches(c -> Integer.parseInt(c) < 20);
    }

    @Test
    void testFormatHashtag_RegularCase() {
        var result = StringUtils.addWordBreaksInHashtag("#PiouPoiouPloiuyu");
        assertThat(result).isEqualTo("#Piou<wbr/>Poiou<wbr/>Ploiuyu");
    }

    @Test
    void testFormatHashtag_WithAcronym() {
        var result = StringUtils.addWordBreaksInHashtag("#PiouCRSEstOuest");
        assertThat(result).isEqualTo("#Piou<wbr/>CRS<wbr/>Est<wbr/>Ouest");
    }

    @Test
    void testFormatHashtag_NoCapitalLetters() {
        var result = StringUtils.addWordBreaksInHashtag("#pioupoiou");
        assertThat(result).isEqualTo("#pioupoiou");
    }

    @Test
    void testFormatHashtag_SingleCapitalLetters() {
        var result = StringUtils.addWordBreaksInHashtag("#Pioupoiou");
        assertThat(result).isEqualTo("#Pioupoiou");
    }

    @Test
    void testFormatHashtag_AllCapsAcronym() {
        var result = StringUtils.addWordBreaksInHashtag("#NASA");
        assertThat(result).isEqualTo("#NASA");
    }

    @Test
    void testFormatHashtag_EmptyString() {
        var result = StringUtils.addWordBreaksInHashtag("");
        assertThat(result).isEmpty();
    }

    @Test
    void normalizeSalary() {
        assertThat(StringUtils.normalizeSalary(10000)).isEqualTo("10 000 €");
    }


    @ParameterizedTest
    @CsvSource({
            "'Jean Dupond', 'J Dupond', true",
            "'Jean DùpOnd', 'Dupond Jean', true",
            "'J Dùpond', 'John DUPÔND', true",
            "'Jean Dupond', 'Jane Doe', false",
            "'Jean Dupond', 'Jean', true",
            "'Jean Dupond', 'Doe', false",
            "'', '', false"
    })
    void testOneWordMatch(String s1, String s2, boolean expectedResult) {
        assertEquals(expectedResult, StringUtils.oneWordMatch(s1, s2));
    }

    @ParameterizedTest
    @CsvSource({
            "'Ly (0565454)', false",
            "'asa ( 69555)', true",
            "'aa (6922', false",
            "'df ( 07555)', true",
            "'sss (7566', false",
            "'Paris 01 75001', false",
            "'Lyon ( 69 008 )', true",
            "'Lyon 01000', true",
            "'Lyon 69', true",
            "'Lyon (69)', true",
            "'pok (55)', false",
            "'pok 55', false",
            "'', false"
    })
    void isInsideAURA(String s1, boolean expectedResult) {
        assertEquals(expectedResult, StringUtils.isInsideAURA(s1));
    }

    @ParameterizedTest
    @CsvSource(value = {
            "'25876 rue du plobl,75008 Paris, France','75008'",
            "'Mon adresse est 20200 Ajaccio, France.','20200'",
            "'Mon adresse est 591566',null",
            "'...7584',null",
            "'Lille, FR, 59000','59000'",
            "'Aucun code postal ici.',null"
    }, nullValues = "null")
    void testExtractPostalCode(String input, String expected) {
        assertEquals(expected, StringUtils.extractPostcode(input));
    }

    @ParameterizedTest
    @CsvSource(value = {
            "'<a>pipo','pipo'",
            "'<a>pipo</a>b','pipo b'",
            "'<a>pipo</a>b</popopo>','pipo b'",
            "'pipo</a> b ','pipo  b'",
            "'<a><b>yo</b></a><a><b><c>lo</b></c></a>','yo     lo'",
            "null,''"
    }, nullValues = "null")
    void removeHtmlTag(String input, String expected) {
        assertEquals(expected, StringUtils.removeHtmlTag(input));
    }

    @ParameterizedTest
    @CsvSource(value = {
            "'Lyon, 6ème arrondissement','Lyon'",
            "'Lyon. __','Lyon.'",
            "'Mà@rseïLLè','Mà rseïLLè'",
            "null,null"
    }, nullValues = "null")
    void adeccoAddress(String input, String expected) {
        assertEquals(expected, StringUtils.normalizeAddressForAdecco(input));
    }

    @ParameterizedTest
    @CsvSource(value = {
            "'Patrick!!!','Patrick'",
            "'MC. Jeàn@ Moul1','MC  Jeàn  Moul'",
            "null,null"
    }, nullValues = "null")
    void adeccoIdentity(String input, String expected) {
        assertEquals(expected, StringUtils.normalizeIdentityForAdecco(input));
    }


    @Test
    void sanitizeOpenAIResponse() {
        assertEquals("yolo", StringUtils.sanitizeOpenAIResponse("yolo"));
        assertEquals("""
                yolo""", StringUtils.sanitizeOpenAIResponse(
                """
                        ```yolo
                        """));
        assertEquals("""
                yolo""", StringUtils.sanitizeOpenAIResponse(
                """
                        ```y```o```l```o
                        """));
        assertEquals("""
                yolo""", StringUtils.sanitizeOpenAIResponse(
                """
                        ```json
                        yolo
                        ```
                        """));
    }

    @Test
    void buildUri() {
        Assertions.assertThat(StringUtils.buildUri("https://yo.lo?p1=a%7cb&dpt=x-<DPT>".replace("<DPT>", URLEncoder.encode("rhône", StandardCharsets.UTF_8)), Optional.empty()).toString()).isEqualTo("https://yo.lo?p1=a%7cb&dpt=x-rh%C3%B4ne");
    }

    @ParameterizedTest
    @CsvSource(value = {
            "'nope',null",
            "'142 plop',142",
            "'6.55',6",
            "' mois:42',42",
            "' 06 mois 84 ',6"
    }, nullValues = "null")
    void extractInt(String input, Integer expected) {
        assertThat(StringUtils.extractInt(input)).isEqualTo(expected);
    }

}
