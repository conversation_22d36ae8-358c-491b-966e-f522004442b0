package com.erhgo.utils;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.springframework.test.util.ReflectionTestUtils;

class SalaryUtilsTest {

    @ParameterizedTest
    @CsvSource(value = {
            "1,3,2,true",
            "1,2,3,false",
            ",3,1,true",
            "1,,1000,true",
            "1,100,115,true",
            "1,100,116,false",
            "100,1000,70,true",
            "100,1000,69,false",
            ",,,false",
            "1,1000,,false",
    })
    void validateSalary(Integer minSalary, Integer maxSalary, Integer userSalary, boolean expectedResult) {
        var salaryUtils = new SalaryUtils();
        ReflectionTestUtils.setField(salaryUtils, "salaryMaxToleranceRatio", 1.15f);
        ReflectionTestUtils.setField(salaryUtils, "salaryMinToleranceRatio", 0.7f);

        Assertions.assertThat(salaryUtils.userMatchesSalary(userSalary, minSalary, maxSalary)).isEqualTo(expectedResult);
    }

}
