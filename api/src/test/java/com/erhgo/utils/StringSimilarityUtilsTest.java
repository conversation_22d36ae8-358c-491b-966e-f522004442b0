package com.erhgo.utils;

import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.ArgumentsProvider;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.CsvSource;

import java.util.Set;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;

class StringSimilarityUtilsTest {
    @ParameterizedTest
    @CsvSource({
            "Car<PERSON><PERSON>,Magasinier cariste,Magas<PERSON>er",
            "MAGA<PERSON><PERSON><PERSON>,Magasinier cariste,Magasinière cariste",
            "magas<PERSON><PERSON>,Magasinière cariste,Magasinier cariste",
            "magasiniere,Magasini<PERSON> cariste,Ma<PERSON><PERSON><PERSON>",
            "car,cariste,Magasinier cariste",
            //"carist,Ma<PERSON><PERSON>er cariste,Opératrice logistique en entrepôt", PAS REALISTE
            "Vender,Vendeur prêt-à-porter,Vendeur prêt-à-porter enfant",
            "age,agent de police nationale,aide generale",
            "infirm,infirmière,informaticien",
            "compt,comptable,commercial",
            "chef,chef de cuisine,chauffeur de taxi",
            "informat,informaticien,ingénieur civil",
            "médecin,médecin généraliste,ouvrier du bâtiment",
            "dév,développeur web,développeur commercial",
            "dév,expert en développement,développeur du succès commercial",
    })
    void getTheMostSimilarString_must_hit_the_most_similar_everytime(String input, String mostSimilar, String leastSimilar) {
        var result = StringSimilarityUtils.getTheMostSimilarString(input, Set.of(mostSimilar, leastSimilar));
        assertThat(result).isEqualTo(mostSimilar);
    }

    @ParameterizedTest
    @ArgumentsSource(StringSimilarityArgumentsProvider.class)
    void getTheMostSimilarString_test_on_specific_set_of_strings(String input, String expected, Set<String> occupationLabels) {
        var mostSimilarLabel = StringSimilarityUtils.getTheMostSimilarString(input, occupationLabels);

        assertThat(mostSimilarLabel).isEqualTo(expected);

    }

    static class StringSimilarityArgumentsProvider implements ArgumentsProvider {

        private static final String INPUT_FOR_PERFECT_EQUALITY = "PRO";
        private static final String EXPECTED_FOR_PERFECT_EQUALITY = "Projeteur industriel";
        private static final String INPUT_FOR_REALISTIC_OCCUPATION_LABELS = "magasiniere";
        private static final String EXPECTED_FOR_REALISTIC_OCCUPATION_LABELS = "Magasinière";
        private static final String INPUT_FOR_CLEANSED_OCCUPATION_LABELS = "Assistant commercial";
        private static final String EXPECTED_FOR_CLEANSED_OCCUPATION_LABELS = "Assistant commercial (H / F / NB)";

        @Override
        public Stream<? extends Arguments> provideArguments(ExtensionContext extensionContext) throws Exception {
            var argumentsForTestingPerfectEquality =
                    Arguments.of(
                            INPUT_FOR_PERFECT_EQUALITY,
                            EXPECTED_FOR_PERFECT_EQUALITY,
                            Set.of(
                                    "Superviseur de chaîne de production",
                                    "Opérateur de production",
                                    "Chef d'équipe en production",
                                    "Opérateur en contrôle qualité",
                                    "Programmeur industriel",
                                    "Promoteur immobilier",
                                    "Ingénieur méthodes et procédés (H / F / NB)",
                                    "Projeteur industriel",
                                    "Professeur de mécanique")
                    );

            var argumentsForTestingRealisticOccupationLabels =
                    Arguments.of(
                            INPUT_FOR_REALISTIC_OCCUPATION_LABELS,
                            EXPECTED_FOR_REALISTIC_OCCUPATION_LABELS,
                            Set.of(
                                    "Magasinière cariste",
                                    "Chargeuse déchargeuse manutentionnaire",
                                    "Magasinier cariste",
                                    "Magasinier (H / F / NB)",
                                    "Opératrice logistique en entrepôt",
                                    "Opérateur logistique en entrepôt",
                                    "Chargeur déchargeur manutentionnaire",
                                    "Chargeusé déchargeuse manutentionnaire",
                                    "Magasinier",
                                    "Magasinière"));

            var argumentsForTestingToleranceOFEmptySet = Arguments.of("input", "", Set.of());

            var argumentsForTestingCleansingOfOccupationLabel = Arguments.of(
                    INPUT_FOR_CLEANSED_OCCUPATION_LABELS,
                    EXPECTED_FOR_CLEANSED_OCCUPATION_LABELS,
                    Set.of(
                            "Assistant commercial (H / F / NB)",
                            "Assistant commercial / Assistante commerciale",
                            "Assistant commercial sédentaire",
                            "Assistant commercial et ADV",
                            "Assistant en investissements financiers",
                            "Assistant d'achat",
                            "Assistant d'ingénieur",
                            "Assistant marketing",
                            "Assistant de promotion des ventes",
                            "Attaché commercial en énergies renouvelables",
                            "Attaché commercial",
                            "Attaché commercial en électricité",
                            "Ingénieur technico-commercial",
                            "Attaché technico-commercial en machines et équipements de bureau",
                            "Attaché technico-commercial en produits chimiques",
                            "Attaché technico-commercial en machines et équipements industriels",
                            "Attaché technico-commercial en équipements électroniques et de télécommunications",
                            "Attaché technico-commercial en machines du secteur minier et de la construction",
                            "Attaché technico-commercial en quincaillerie et fournitures pour plomberie et chauffage",
                            "Attaché technico-commercial en machines et matériel agricoles",
                            "Attaché technico-commercial de l'industrie des machines textiles",
                            "Agent de prêts")
            );

            return Stream.of(
                    argumentsForTestingPerfectEquality,
                    argumentsForTestingRealisticOccupationLabels,
                    argumentsForTestingToleranceOFEmptySet,
                    argumentsForTestingCleansingOfOccupationLabel
            );
        }
    }
}

