package com.erhgo;

import com.erhgo.openapi.dto.ErhgoOccupationSumUpDTO;
import com.erhgo.services.exporter.PdfExporterService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

@Slf4j
class ErhgoOccupationPdfExportTest {

    private final PdfExporterService pdfExporterService = new PdfExporterService();

    @Test
    void generatePDF() throws IOException {
        var stream = new ByteArrayOutputStream();
        pdfExporterService.generatePdf("erhgoOccupationDetail", new ErhgoOccupationSumUpDTO()
                .description("<p class=\"ql-align-justify\"><strong>Qu'est-ce que vous faites ??</strong></p><p class=\"ql-align-justify\">En tant que poissonnier·ère, vous vendez des produits de la mer dans des magasins spécialisés ou sur les marchés : poissons, des crustacés et des mollusques. Vous conseillez les clients sur les produits de la mer et leur préparation. A la demande des client·e·s, vous préparez le poisson : écailler, vider, nettoyer, couper, séparer les filets et retirer les arêtes du poisson. De plus, vous préparez des plateaux décoratifs de fruits de mer et des plats cuisinés à base de produits de la mer. Enfin, vous participez à la commande, au stockage et à la mise en rayon des produits de la mer dans le respect des délais et modes de conservation des produits.</p><p class=\"ql-align-justify\"><br></p><p class=\"ql-align-justify\"><strong>Quelles sont vos conditions de travail ?</strong></p><p class=\"ql-align-justify\">Vous travaillez dans des grandes surfaces, poissonneries et/ou sur les marchés.&nbsp;Vous travaillez à des horaires de magasin et le week-end.&nbsp;De plus, vous êtes en contact quotidien avec des client·e·s. C’est un métier qui peut s’avérer physique car vous travaillez debout toute la journée et pouvez être amené·e à porter des charges lourdes.&nbsp;</p><p class=\"ql-align-justify\"><br></p><p class=\"ql-align-justify\"><strong style=\"background-color: transparent;\">Comment accéder à ce métier et quelles sont les évolutions possibles ?</strong></p><p class=\"ql-align-justify\"><span style=\"background-color: transparent;\">L’exercice de ce métier permet de développer des connaissances en produits de la mer et des gestes professionnels indispensables à la préparation des produits de la mer.&nbsp;Il existe des baccalauréats professionnels, des CAP ou CQP Poissonnier qui permettent de se préparer à l’exercice de ce métier.&nbsp;</span>Après quelques années d'expérience et selon vos souhaits, vous pourrez évoluer vers des missions d’encadrement d’équipe voir de responsable d’établissement.&nbsp;</p><p class=\"ql-align-justify\"><br></p><p><strong>Quels sont les principaux comportements attendus dans ce métier ?</strong></p><p>Le pragmatisme : Vous avez besoin de voir votre travail se concrétiser rapidement. Vous souhaitez développer votre capacité de réaction et votre efficacité. Vous chercherez ainsi à être utile, d'une manière adaptée à la fois aux personnes et aux situations.&nbsp;</p><p><br></p><p class=\"ql-align-justify\">La sociabilité :&nbsp;<span style=\"background-color: transparent;\">Vous souhaitez porter une attention particulière aux relations humaines et au respect dû aux autres. Vous faites preuve d'ouverture d'esprit face aux différences. Vous savez vous montrer attentif et poli mais aussi solidaire et curieux professionnellement.&nbsp;</span></p><p><br></p>")
                .title("Poissonnière / Poissonnier"), stream);
        TestUtils.assertThatPdfMatchesContent("expectedErhgoOccupationUnitTest.pdf", stream.toByteArray());
    }
}
