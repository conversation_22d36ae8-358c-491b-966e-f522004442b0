package com.erhgo.config;


import com.erhgo.AbstractIntegrationTest;
import com.erhgo.services.notifier.messages.AbstractNotifierMessageDTO;
import com.erhgo.services.notifier.messages.FrontofficeNotifierMessageDTO;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

@TestPropertySource(properties = {"slack.urlForMessageType.FrontofficeNotifierMessageDTO=42", "slack.url_for_message_type.DummyDTO=43"})
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
class SlackPropertiesTest extends AbstractIntegrationTest {

    @Autowired
    SlackProperties slackProperties;

    static class DummyDTO extends AbstractNotifierMessageDTO {
        @Override
        public String getText() {
            return null;
        }
    }

    static class OtherDummyDTO extends AbstractNotifierMessageDTO {
        @Override
        public String getText() {
            return null;
        }
    }

    @Test
    void ensureMapIsProperlyFeed() {
        Assertions.assertThat(slackProperties.getUrl(new FrontofficeNotifierMessageDTO("", "", "")))
                .isEqualTo("42");
        Assertions.assertThat(slackProperties.getUrl(new DummyDTO()))
                .isEqualTo("43");
        Assertions.assertThat(slackProperties.getUrl(new OtherDummyDTO()))
                .isNull();
    }
}
