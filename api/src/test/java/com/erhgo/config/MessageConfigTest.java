package com.erhgo.config;

import com.erhgo.services.notifier.messages.AbstractNotifierMessageDTO;
import lombok.SneakyThrows;
import org.apache.commons.lang3.ArrayUtils;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.type.filter.AssignableTypeFilter;
import org.yaml.snakeyaml.Yaml;

import java.util.Map;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

import static org.assertj.core.api.Assertions.assertThat;

class MessageConfigTest {

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void testYaml(boolean checkProdConf) throws Exception {
        var yamlStream = new ClassPathResource("application.yml").getInputStream();
        var yaml = new Yaml();
        Iterable<Object> yamlMap = yaml.loadAll(yamlStream);
        Map<String, Object> defaultConf = (Map<String, Object>) getYamlStream(yamlMap).filter(doc -> !get(doc, "spring").containsKey("config")).findFirst().orElseThrow();
        Map<String, Object> prodConf = (Map<String, Object>) getYamlStream(yamlMap).filter(doc -> "master".equals(get(doc, "spring", "config", "activate").get("on-profile"))).findFirst().orElseThrow();

        assertForConf(checkProdConf ? prodConf : defaultConf);
    }

    private void assertForConf(Map<String, Object> conf) {

        var messageTypeMap = get(conf, "slack", "url-for-message-type");
        var classpathProvider = new ClassPathScanningCandidateComponentProvider(false);
        classpathProvider.addIncludeFilter(new AssignableTypeFilter(AbstractNotifierMessageDTO.class));
        var components = classpathProvider.findCandidateComponents("com.erhgo.services").stream()
                .map(BeanDefinition::getBeanClassName)
                .map(this::getSimpleName)
                .toList();
        var unknownDTOs = messageTypeMap.keySet().stream().filter(k -> components.stream().noneMatch(c -> c.equals(k)));
        var unconfiguredDTOs = components.stream().filter(k -> !messageTypeMap.containsKey(k));

        assertThat(unknownDTOs).isEmpty();// Failing when Slack dto is configured in application.yml BUT does not exist anymore
        assertThat(unconfiguredDTOs).isEmpty();// Failing when SLack dto exists but is not configured in application.yml (ie. we do not know where to send message)
    }

    @SneakyThrows
    private String getSimpleName(String s) {
        return Class.forName(s).getSimpleName();
    }

    @NotNull
    private static Stream<Object> getYamlStream(Iterable<Object> yamlMap) {
        return StreamSupport.stream(yamlMap.spliterator(), false);
    }

    Map<String, Object> get(Object o, String... path) {
        if (o == null) {
            return Map.of();
        }
        var res = (Map<String, Object>) ((Map<String, Object>) o).get(path[0]);
        return path.length == 1 ? (res == null ? Map.of() : res) : get(res, ArrayUtils.subarray(path, 1, path.length));
    }

}
