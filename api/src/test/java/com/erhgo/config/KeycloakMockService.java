package com.erhgo.config;

import com.erhgo.domain.exceptions.EntityAlreadyExistException;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.openapi.dto.ConfirmFOUserFromBOCommandDTO;
import com.erhgo.openapi.dto.SaveUserNameCommandDTO;
import com.erhgo.openapi.dto.SetFrontOfficeUserPasswordCommandDTO;
import com.erhgo.services.AbstractService;
import com.erhgo.services.dto.UserKeycloakRepresentation;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.google.common.collect.Sets;
import jakarta.ws.rs.core.MultivaluedHashMap;
import org.assertj.core.util.Lists;
import org.springframework.context.annotation.Profile;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Profile("test")
public class KeycloakMockService implements KeycloakService {

    public static final String MOCK_ID_FOR_USER_FETCHED_BY_MAIL = "ID-MOCK";

    public Map<String, UserRepresentation> userprofiles = new HashMap<>();
    private Map<String, Set<String>> organizationRoles = new HashMap<>();
    private MultivaluedHashMap<String, String> groupsOfRoles = new MultivaluedHashMap();

    @Override
    public String createUserInBackOfficeRealm(UserKeycloakRepresentation userKeycloakRepresentation) {
        return UUID.randomUUID().toString();
    }

    @Override
    public String createUserInFrontOfficeRealm(UserKeycloakRepresentation userKeycloakRepresentation) {
        var uuid = UUID.randomUUID().toString();
        var userRepresentation = new UserRepresentation();
        userRepresentation.setFirstName(userKeycloakRepresentation.getFirstName());
        userRepresentation.setLastName(userKeycloakRepresentation.getLastName());
        userRepresentation.setEmail(userKeycloakRepresentation.getEmail());
        userRepresentation.setId(uuid);
        verifyConflict(userKeycloakRepresentation.getEmail(), uuid);
        userprofiles.put(uuid, userRepresentation);
        return uuid;
    }

    private void verifyConflict(String email, String userId) {
        if (userprofiles.values().stream().anyMatch(u -> !u.getId().equals(userId) && u.getEmail().equals(email))) {
            throw new EntityAlreadyExistException(UserProfile.class, email);
        }
    }

    @Override
    public AbstractService.PageDTOAdapter<UserRepresentation> getBackOfficeGroupMembersPaginatedResource(String groupName, int offset, int max) {
        return null;
    }

    @Override
    public int countBackOfficeGroupMembers(String groupName) {
        return 0;
    }

    @Override
    public void createBackOfficeGroupAndRoles(String groupAndRole, String... otherRoles) {
        var roles = Sets.newHashSet(otherRoles);
        roles.add(groupAndRole);
        organizationRoles.put(groupAndRole, roles);
    }

    @Override
    public Optional<UserRepresentation> getBackOfficeUserProfile(String userId) {
        return Optional.ofNullable(userprofiles.get(userId));
    }

    @Override
    public String getBackOfficeUserFullnameOrEmpty(String keycloakId) {
        return userprofiles.get(keycloakId) == null ? "" : (userprofiles.get(keycloakId).getFirstName() + " " + userprofiles.get(keycloakId).getLastName());
    }

    @Override
    public Optional<UserRepresentation> getFrontOfficeUserProfile(String userId) {
        return Optional.ofNullable(userprofiles.get(userId));
    }

    @Override
    public Optional<UserRepresentation> getFrontOfficeUserProfileWithGroups(String userId) {
        return Optional.ofNullable(userprofiles.get(userId));
    }

    public void setUserProfile(String userId, UserRepresentation userRepresentation) {
        if (userRepresentation == null) {
            userprofiles.remove(userId);
        } else {
            userprofiles.put(userId, userRepresentation);

        }
    }

    @Override
    public Page<UserRepresentation> searchFrontOfficeUsersPaginated(String search, int page, int size) {
        return null;
    }

    @Override
    public List<UserRepresentation> getBackOfficeUsers() {
        return null;
    }

    @Override
    public UserRepresentation getFOUserRepresentationByEmail(String email) {
        var userRepresentation = new UserRepresentation();
        userRepresentation.setId(MOCK_ID_FOR_USER_FETCHED_BY_MAIL);
        return userRepresentation;
    }

    @Override
    public void createFrontOfficeGroupAndRole(String role) {
    }

    @Override
    public List<UserRepresentation> findAllFrontOfficeUsers() {
        return Lists.newArrayList(userprofiles.values());
    }

    @Override
    public void assignToFrontOfficeGroups(String userId, Set<String> groupNames) {
        userprofiles.values().stream()
                .filter(u -> u.getId().equals(userId))
                .forEach(u -> u.setGroups(List.copyOf(groupNames)));

    }

    @Override
    public void removeUserFromFrontOfficeGroup(String userId, String groupName) {
        userprofiles.values().stream()
                .filter(u -> u.getId().equals(userId))
                .forEach(x -> x.getGroups().removeIf(g -> g.equals(groupName)));
    }

    @Override
    public Set<String> getRolesForGroup(String group) {
        var roles = organizationRoles.get(group);
        return roles == null ? Sets.newHashSet(group) : roles;
    }

    public void addRoleToGroup(String group, String role) {
        var roles = organizationRoles.getOrDefault(group, new HashSet<>());
        roles.add(role);
        roles.add(group);
        organizationRoles.put(group, roles);
    }

    @Override
    public Set<String> getGroupsOfRoles(Collection<String> roles) {
        return Sets.union(
                Sets.newHashSet(roles),
                roles.stream()
                        .flatMap(g -> groupsOfRoles.getOrDefault(g, new ArrayList<>()).stream())
                        .collect(Collectors.toSet())
        ).immutableCopy();
    }

    @Override
    public void clearCaches() {
    }

    @Override
    public void resetFOPassword(String userId) {

    }

    @Override
    public ArrayList<org.keycloak.representations.idm.UserRepresentation> findAllFrontOfficeUsersForAnonymizationOnly() {
        return null;
    }

    @Override
    public void setFrontOfficeUserPassword(SetFrontOfficeUserPasswordCommandDTO setFrontOfficeUserPasswordCommandDTO) {
    }

    @Override
    public String confirmFOUserFromBO(ConfirmFOUserFromBOCommandDTO confirmFOUserFromBOCommandDTO) {
        return "42";
    }

    @Override
    public void setFrontOfficeUserName(String userId, SaveUserNameCommandDTO setUserNameCommandDTO) {
    }

    @Override
    public boolean deleteFrontOfficeUser(String userId) {
        return userprofiles.remove(userId) != null;
    }


    @Override
    public boolean updateFOEmailAndUsername(String userId, String nextEmail, String firstName, String lastName) {
        updateFOEmail(userId, nextEmail);
        return true;
    }

    @Override
    public boolean updateFOEmail(String userId, String nextEmail) {
        verifyConflict(nextEmail, userId);
        Optional.ofNullable(userprofiles.get(userId)).ifPresent(u -> {
            u.setEmail(nextEmail);
        });
        return true;
    }

    @Override
    public void removePhoneForFOUser(String userId) {

    }

    @Override
    public Map<String, Set<String>> searchBOGroupsWithRoles(String query) {
        return Map.of();
    }


    public void reset() {
        userprofiles.clear();
        organizationRoles.clear();
        groupsOfRoles.clear();
    }
}
