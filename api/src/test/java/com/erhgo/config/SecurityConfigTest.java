package com.erhgo.config;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.security.authorization.method.AuthorizationInterceptorsOrder;

class SecurityConfigTest {


    @Test
    void ensureSecurityOrderConstantHasNotChanged() {
        /*
          In order to ensure permissions evaluations are done in same transaction as app transaction,
          EnableTransactionManagement annotation, on Spring root application {@link com.erhgo.ErhgoApplication} refers
          to SpringSecurity proxy order, which cannot be updated.
         */
        Assertions.assertThat(AuthorizationInterceptorsOrder.PRE_AUTHORIZE.getOrder()).isEqualTo(SecurityConfig.ORDER);
    }
}
