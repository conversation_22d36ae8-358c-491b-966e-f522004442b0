package com.erhgo.config;


import com.erhgo.security.ErhgoCompositePermissionEvaluator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler;
import org.springframework.security.access.expression.method.MethodSecurityExpressionHandler;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.CsrfConfigurer;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.HttpStatusEntryPoint;

@Configuration
@EnableWebSecurity
@Profile("test")
@EnableMethodSecurity(jsr250Enabled = true, prePostEnabled = true, securedEnabled = false)
public class SimpleTestSecurityConfig {

    @Autowired
    private ErhgoCompositePermissionEvaluator customPermissionEvaluator;
    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http.cors(Customizer.withDefaults())
                .csrf(CsrfConfigurer::disable)
                .exceptionHandling(a -> a.authenticationEntryPoint(new HttpStatusEntryPoint(HttpStatus.UNAUTHORIZED)))
                .authorizeHttpRequests(authz -> authz
                        .requestMatchers(
                                HttpMethod.OPTIONS
                        ).permitAll()
                        .requestMatchers("/ssr/public/**").permitAll()
                        .requestMatchers("/actuator/health").permitAll()
                        .requestMatchers("/actuator/health").permitAll()
                        .requestMatchers("/api/public/**").permitAll() // legacy
                        .requestMatchers("/api/odas/public/**").permitAll()
                        .requestMatchers(HttpMethod.GET,"/api/odas/erhgo-occupation/occupation/search-occupations").permitAll()
                        .requestMatchers(HttpMethod.GET,"/api/odas/sourcing/occupation/**").permitAll()
                        .requestMatchers(HttpMethod.GET, "/api/odas/user/recruitments").permitAll()
                        .requestMatchers("/api/odas/sourcing/contact/**").permitAll()
                        .requestMatchers("/api/odas/**").authenticated()
                        .anyRequest().authenticated()
                );

        return http.build();
    }

    @Bean
    public MethodSecurityExpressionHandler expressionHandler() {
        var expressionHandler =
                new DefaultMethodSecurityExpressionHandler();
        expressionHandler.setPermissionEvaluator(customPermissionEvaluator);
        return expressionHandler;
    }
}
