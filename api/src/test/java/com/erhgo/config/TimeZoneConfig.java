package com.erhgo.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.TimeZone;

@Configuration
public class TimeZoneConfig {

    @Bean
    public TimeZone timeZone() {
        var defaultTimeZone = TimeZone.getTimeZone("Europe/Paris");
        TimeZone.setDefault(defaultTimeZone);
        return defaultTimeZone;
    }

}
