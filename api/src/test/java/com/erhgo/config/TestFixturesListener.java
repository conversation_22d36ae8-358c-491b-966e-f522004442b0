package com.erhgo.config;

import com.erhgo.domain.utils.EventPublisherUtils;
import com.erhgo.generators.TestFixtures;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.context.TestContext;
import org.springframework.test.context.support.AbstractTestExecutionListener;

public class TestFixturesListener extends AbstractTestExecutionListener {

    public final static int ORDER = ResetDatabaseTestExecutionListener.ORDER + 1;

    @Autowired
    private TestFixtures testFixtures;

    private boolean forceFixtureLoading = false;

    @Autowired
    ApplicationEventPublisher applicationEventPublisher;

    @Override
    public final int getOrder() {
        return ORDER;
    }

    @Override
    public void beforeTestClass(TestContext testContext) {
        testContext.getApplicationContext()
                .getAutowireCapableBeanFactory()
                .autowireBean(this);
        EventPublisherUtils.resetPublisher(applicationEventPublisher);
        testFixtures.createFixtures();
        forceFixtureLoading = false;
    }

    @Override
    public void beforeTestMethod(TestContext testContext) {
        forceFixtureLoading = forceFixtureLoading || (testContext.getTestMethod().getAnnotation(ResetDataBefore.class) != null);

        if (forceFixtureLoading) {
            testFixtures.createFixtures();
            forceFixtureLoading = false;
        }
    }

    @Override
    public void afterTestExecution(TestContext testContext) {
        if (testContext.getTestMethod().getAnnotation(ResetDataAfter.class) != null) {
            forceFixtureLoading = true;
        }
    }
}
