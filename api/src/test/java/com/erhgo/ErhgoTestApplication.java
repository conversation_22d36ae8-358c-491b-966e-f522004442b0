package com.erhgo;

import com.erhgo.config.SecurityConfig;
import com.erhgo.migrations.AbstractMigrationService;
import com.erhgo.services.http.RetryableHttpClient;
import jakarta.annotation.Nullable;
import org.springframework.ai.model.chat.client.autoconfigure.ChatClientAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.hazelcast.HazelcastAutoConfiguration;
import org.springframework.boot.autoconfigure.integration.IntegrationAutoConfiguration;
import org.springframework.context.annotation.*;
import org.springframework.security.test.context.support.ReactorContextTestExecutionListener;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.Optional;

@SpringBootApplication(exclude = {HazelcastAutoConfiguration.class, ChatClientAutoConfiguration.class, IntegrationAutoConfiguration.class})
@Profile("test")
@ComponentScan(excludeFilters = {
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = {".*openapi.esco.*"}),
        @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = AbstractMigrationService.class),
        @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = ReactorContextTestExecutionListener.class)
})
@EnableTransactionManagement(order = SecurityConfig.ORDER - 1)
public class ErhgoTestApplication {

    @Bean
    @Primary
    public RetryableHttpClient retryableHttpClient(@Nullable Integer a, @Nullable Integer b, @Nullable Integer c) {
        return new RetryableHttpClient(Optional.ofNullable(a).orElse(30), Optional.ofNullable(b).orElse(30), Optional.ofNullable(c).orElse(30));
    }

}
