package com.erhgo;

import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationState;
import com.erhgo.domain.classifications.esco.EscoOccupation;
import com.erhgo.domain.classifications.isco.IscoOccupation;
import com.erhgo.domain.classifications.rome.RomeOccupation;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.services.notifier.messages.AbstractNotifierMessageDTO;
import com.erhgo.services.notifier.messages.SourcingJobOnOccupationMessage;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.util.Set;

@Slf4j
class SlackNotificationMessageTest {

    private static final Recruiter organization = OrganizationGenerator.buildRecruiterWithId();
    private static final String TITLE_USED = " en utilisant son titre principal ";

    @BeforeAll
    static void setup() {
        AbstractNotifierMessageDTO.baseProfilePath = "baseProfilePath";
        AbstractNotifierMessageDTO.baseOccupationPath = "baseOccupationPath";
        AbstractNotifierMessageDTO.baseRecruitmentPath = "baseRecruitmentPath";
        AbstractNotifierMessageDTO.baseOrganizationPath = "baseOrganizationPath";
    }

    @Test()
    void sendMessage_occupation_has_no_code_and_is_not_qualified() {
        var occupation = ErhgoOccupation
                .builder()
                .title("Caviste")
                .technical(true)
                .qualificationState(ErhgoOccupationState.NONE)
                .build();

        var message = new SourcingJobOnOccupationMessage(occupation, organization, "R-00", TITLE_USED, false);

        Assertions.assertThat(message.getText())
                .isEqualTo("""
                        :rotating_light: Un utilisateur <baseOrganizationPath|de l'organisation Entreprise du 42 _(version d'essai)_ > a créé <baseRecruitmentPath|un recrutement> sur le métier (*qualification NON CONFIRMÉE* - état : NONE) <baseOccupationPath|Caviste> en utilisant son titre principal\s
                        :red_circle: Le métier est technique et ne dispose pas de code ROME ni de code ISCO""");
    }

    @Test
    void sendMessage_recruiter_subscription_is_activated() {
        var occupation = buildTechnicalOccupationWithCodes();

        var message = new SourcingJobOnOccupationMessage(occupation, organization, "R-00", TITLE_USED, true);

        Assertions.assertThat(message.getText())
                .isEqualTo("""
                        :white_check_mark: Un utilisateur <baseOrganizationPath|de l'organisation Entreprise du 42 > a créé <baseRecruitmentPath|un recrutement> sur le métier (qualification confirmée) <baseOccupationPath|Caviste> en utilisant son titre principal\s
                        :large_orange_circle: Le métier est technique (1 code(s) ROME, 1 code(s) ISCO)""");
    }

    @Test
    void sendMessage_occupation_has_code_and_is_qualified() {
        var occupation = buildTechnicalOccupationWithCodes();

        var message = new SourcingJobOnOccupationMessage(occupation, organization, "R-00", TITLE_USED, false);

        Assertions.assertThat(message.getText())
                .isEqualTo("""
                        :white_check_mark: Un utilisateur <baseOrganizationPath|de l'organisation Entreprise du 42 _(version d'essai)_ > a créé <baseRecruitmentPath|un recrutement> sur le métier (qualification confirmée) <baseOccupationPath|Caviste> en utilisant son titre principal\s
                        :large_orange_circle: Le métier est technique (1 code(s) ROME, 1 code(s) ISCO)""");
    }

    private ErhgoOccupation buildTechnicalOccupationWithCodes() {
        var escoOccupations = Set.of(EscoOccupation.
                builder()
                .iscoOccupation(IscoOccupation.builder().iscoGroup(1000).build())
                .build());
        var romeOccupations = Set.of(RomeOccupation
                .builder()
                .code("A-1000")
                .build());

        var occupation = ErhgoOccupation
                .builder()
                .title("Caviste")
                .technical(true)
                .qualificationState(ErhgoOccupationState.QUALIFIED_V3_CONFIRMED)
                .escoOccupations(escoOccupations)
                .build();
        romeOccupations.forEach(occupation::addRome);
        return occupation;
    }


}
