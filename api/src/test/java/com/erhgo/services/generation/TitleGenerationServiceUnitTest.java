package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import com.erhgo.services.generation.dto.NormalizedTitlesResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.util.ClassUtils;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.springframework.test.util.ReflectionTestUtils.setField;

@ExtendWith(MockitoExtension.class)
class TitleGenerationServiceUnitTest {

    static final double temperature = 42d;

    static final String model = "gpt42";
    static final String occupationTitle = "Titre masculin";

    static final String OPENAI_RESPONSE_NORMALIZEDTITLE = """
            {"masculine": "Titre masculin", "feminine": "Titre féminin"}
            """;


    @Mock
    GenerationClient generationClient;

    @Mock
    SecurityService securityService;

    @InjectMocks
    TitleGenerationService titleGenerationService;

    @BeforeEach
    void initialize() {
        titleGenerationService.init();
        setField(titleGenerationService, "promptConfig", new PromptConfig().setTemperature(temperature).setModel(model).setMaxRetry(3).setMessageFilename("OccupationTitleMessages.yaml"));
        setField(titleGenerationService, "yamlPromptReader", new YamlPromptReader(new ResourceLoader() {
            @Override
            public Resource getResource(String location) {
                return new ClassPathResource(location.replace("classpath:", ""));
            }

            @Override
            public ClassLoader getClassLoader() {
                return ClassUtils.getDefaultClassLoader();
            }
        }));

    }

    private void mockOpenAIResponse(String response) {
        lenient().when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class))).thenReturn(new ChatCompletionResponse(response, null));
    }

    @Test
    void generateNormalizedOccupationTitle() {
        mockOpenAIResponse(OPENAI_RESPONSE_NORMALIZEDTITLE);
        var expectedNormalizedTitles = new NormalizedTitlesResponse(occupationTitle, occupationTitle.replace("masculin", "féminin"));
        var generateNormalizedTitle = titleGenerationService.normalizeTitle(occupationTitle).getResult();
        Assertions.assertEquals(expectedNormalizedTitles, generateNormalizedTitle);
    }

    @Test
    @SneakyThrows
    void callOpenAIAndValidateNormalizedTitle() {
        var invalidNormalizedTitlesResponse = "{\"erreur\": \"trop générique\"}";
        mockOpenAIResponse(invalidNormalizedTitlesResponse);
        Assertions.assertThrows(GenericTechnicalException.class, () -> titleGenerationService.normalizeTitle(occupationTitle));
    }

    @Test
    @SneakyThrows
    void callOpenAIAndValidateNormalization() {
        var invalidNormalizedTitlesResponse = "{\"erreur\": \"incompréhensible\"}";
        mockOpenAIResponse(invalidNormalizedTitlesResponse);
        Assertions.assertThrows(GenericTechnicalException.class, () -> titleGenerationService.normalizeTitle(occupationTitle));
    }


}
