package com.erhgo.services.generation;

import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import com.erhgo.services.generation.dto.CustomChatCompletionRequest;
import com.erhgo.services.generation.dto.GenericOpenAiPromptTesterCommand;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GenericPromptTesterServiceUnitTest {

    @Mock
    private GenerationClient generationClient;

    @Mock
    private SecurityService securityService;

    private YamlPromptReader yamlPromptReader = new YamlPromptReader(null);

    @InjectMocks
    private GenericPromptTesterService service;

    @Test
    void testPrompt_WithoutFileContent() {
        ReflectionTestUtils.setField(service, "yamlPromptReader", yamlPromptReader);
        var temperature = 0.7;
        var model = "gpt-3.5-turbo";
        var maxTokens = 2000;
        var forceJson = true;
        var promptMessages = """
                promptMessages:
                      - role: system
                        content: test
                """;
        var responseContent = "Test response";


        var command = new GenericOpenAiPromptTesterCommand(temperature, model, maxTokens, forceJson, promptMessages, null);

        var mockResponse = new ChatCompletionResponse(responseContent, new OpenAIResponse<>().setModel(model));
        when(generationClient.createChatCompletion(any(CustomChatCompletionRequest.class), any())).thenReturn(mockResponse);
        when(securityService.isAdmin()).thenReturn(false);
        when(securityService.getAuthenticatedUserId()).thenReturn("user123");

        var result = service.testPrompt(command);

        assertNotNull(result);
        assertEquals(responseContent, result.getContent());
        assertEquals(model, result.getModel());
    }
}
