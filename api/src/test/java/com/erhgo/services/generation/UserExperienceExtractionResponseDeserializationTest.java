package com.erhgo.services.generation;

import com.erhgo.domain.enums.ExperienceType;
import com.erhgo.services.generation.dto.ArrayWrapper;
import com.erhgo.services.generation.dto.UserExperienceExtractionResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import java.time.LocalDate;

import static org.assertj.core.api.Assertions.tuple;

class UserExperienceExtractionResponseDeserializationTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @SneakyThrows
    @ParameterizedTest
    @CsvSource(value = {
            "'1.5', 2",
            "'2.1', 3",
            "'2.0', 2",
            "'3', 3",
            "'0.1', 1",
            "'abc', null",
            "'', null",
            "null, null"
    }, nullValues = "null")
    void durationInMonths_should_handle_decimal_strings_with_ceiling(String durationValue, Integer expectedResult) {
        var json = """
                {
                  "title": "Software Engineer",
                  "organizationTitle": "Tech Corp",
                  "type": "JOB",
                  "durationInMonths": %s
                }
                """.formatted(durationValue == null ? "null" : "\"" + durationValue + "\"");

        var result = objectMapper.readValue(json, UserExperienceExtractionResponse.class);

        Assertions.assertThat(result.getDurationInMonths()).isEqualTo(expectedResult);
        Assertions.assertThat(result.getTitle()).isEqualTo("Software Engineer");
        Assertions.assertThat(result.getOrganizationTitle()).isEqualTo("Tech Corp");
        Assertions.assertThat(result.getType()).isEqualTo(ExperienceType.JOB);
    }

    @SneakyThrows
    @Test
    void arrayWrapper_deserialization_should_work_with_decimal_durations() {
        var json = """
                {
                  "result": [
                    {
                      "title": "Consultant en marketing",
                      "organizationTitle": "ABC Consulting",
                      "type": "JOB",
                      "durationInMonths": "1.5"
                    },
                    {
                      "title": "Stagiaire en ressources humaines",
                      "organizationTitle": "HR Corp",
                      "type": "INTERNSHIP",
                      "durationInMonths": "6.8"
                    },
                    {
                      "title": "Invalid Duration Job",
                      "organizationTitle": "Test Corp",
                      "type": "JOB",
                      "durationInMonths": "invalid"
                    }
                  ]
                }
                """;

        var result = objectMapper.readValue(json, new TypeReference<ArrayWrapper<UserExperienceExtractionResponse>>() {
        });

        Assertions.assertThat(result.getResult())
                .hasSize(3)
                .extracting(UserExperienceExtractionResponse::getDurationInMonths, UserExperienceExtractionResponse::getTitle)
                .containsExactly(
                        tuple(2, "Consultant en marketing"),
                        tuple(7, "Stagiaire en ressources humaines"),
                        tuple(null, "Invalid Duration Job")
                );
    }

    @SneakyThrows
    @Test
    void backward_compatibility_with_integer_values() {
        var json = """
                {
                  "result": [
                    {
                      "title": "Software Engineer",
                      "organizationTitle": "Tech Corp",
                      "type": "JOB",
                      "durationInMonths": 12
                    },
                    {
                      "title": "Data Analyst",
                      "organizationTitle": "Data Corp",
                      "type": "JOB",
                      "durationInMonths": "24"
                    }
                  ]
                }
                """;

        var result = objectMapper.readValue(json, new TypeReference<ArrayWrapper<UserExperienceExtractionResponse>>() {
        });
        var experiences = result.getResult();

        Assertions.assertThat(experiences).hasSize(2);

        Assertions.assertThat(experiences)
                .extracting(UserExperienceExtractionResponse::getDurationInMonths)
                .containsExactly(12, 24);
    }

    @SneakyThrows
    @Test
    void real_world_scenario_with_problematic_ai_response() {
        var problematicJson = """
                {
                  "result": [
                    {
                      "title": "Consultant en marketing",
                      "organizationTitle": "ABC Consulting",
                      "type": "JOB",
                      "durationInMonths": "1.5"
                    },
                    {
                      "title": "Développeur logiciel",
                      "organizationTitle": "Tech Solutions",
                      "type": "JOB",
                      "durationInMonths": "2.8"
                    },
                    {
                      "title": "Stagiaire RH",
                      "organizationTitle": "HR Corp",
                      "type": "INTERNSHIP",
                      "durationInMonths": "invalid_duration"
                    }
                  ]
                }
                """;

        var result = objectMapper.readValue(problematicJson, new TypeReference<ArrayWrapper<UserExperienceExtractionResponse>>() {
        });

        Assertions.assertThat(result.getResult())
                .hasSize(3)
                .extracting(UserExperienceExtractionResponse::getDurationInMonths,
                        UserExperienceExtractionResponse::getTitle,
                        UserExperienceExtractionResponse::getType)
                .containsExactly(
                        tuple(2, "Consultant en marketing", ExperienceType.JOB),
                        tuple(3, "Développeur logiciel", ExperienceType.JOB),
                        tuple(null, "Stagiaire RH", ExperienceType.INTERNSHIP)
                );
    }

    @SneakyThrows
    @ParameterizedTest
    @CsvSource(value = {
            "'2023-01-15', '2023-01-15'",
            "'15/01/2023', '2023-01-15'",
            "'15-01-2023', '2023-01-15'",
            "'1/5/2023', '2023-05-01'",
            "'invalid_date', null",
            "'', null",
            "null, null"
    }, nullValues = "null")
    void startDate_and_endDate_should_handle_various_formats(String dateValue, String expectedDate) {
        var json = """
                {
                  "title": "Test Job",
                  "organizationTitle": "Test Corp",
                  "type": "JOB",
                  "startDate": %s,
                  "endDate": %s
                }
                """.formatted(
                dateValue == null ? "null" : "\"" + dateValue + "\"",
                dateValue == null ? "null" : "\"" + dateValue + "\""
        );

        var result = objectMapper.readValue(json, UserExperienceExtractionResponse.class);
        var expected = expectedDate != null ? LocalDate.parse(expectedDate) : null;

        Assertions.assertThat(result.getStartDate()).isEqualTo(expected);
        Assertions.assertThat(result.getEndDate()).isEqualTo(expected);
    }

    @SneakyThrows
    @ParameterizedTest
    @CsvSource(value = {
            "'JOB', 'JOB'",
            "'INTERNSHIP', 'INTERNSHIP'",
            "'MISSION', 'MISSION'",
            "'job', 'JOB'",
            "'internship', 'INTERNSHIP'",
            "'mission', 'MISSION'",
            "'invalid_type', null",
            "'', null",
            "null, null"
    }, nullValues = "null")
    void type_should_handle_various_formats(String typeValue, String expectedType) {
        var json = """
                {
                  "title": "Test Job",
                  "organizationTitle": "Test Corp",
                  "type": %s
                }
                """.formatted(typeValue == null ? "null" : "\"" + typeValue + "\"");

        var result = objectMapper.readValue(json, UserExperienceExtractionResponse.class);
        var expected = expectedType != null ? ExperienceType.valueOf(expectedType) : null;

        Assertions.assertThat(result.getType()).isEqualTo(expected);
    }

    @SneakyThrows
    @Test
    void complete_fault_tolerant_scenario() {
        var faultyJson = """
                {
                  "result": [
                    {
                      "title": "Valid Experience",
                      "organizationTitle": "Good Corp",
                      "type": "job",
                      "durationInMonths": "12.5",
                      "startDate": "2023-01-15",
                      "endDate": "15/12/2023"
                    },
                    {
                      "title": "Faulty Experience",
                      "organizationTitle": "Bad Corp",
                      "type": "invalid_type",
                      "durationInMonths": "not_a_number",
                      "startDate": "invalid_date",
                      "endDate": ""
                    }
                  ]
                }
                """;

        var result = objectMapper.readValue(faultyJson, new TypeReference<ArrayWrapper<UserExperienceExtractionResponse>>() {
        });

        Assertions.assertThat(result.getResult())
                .hasSize(2)
                .extracting(UserExperienceExtractionResponse::getTitle,
                        UserExperienceExtractionResponse::getType,
                        UserExperienceExtractionResponse::getDurationInMonths,
                        UserExperienceExtractionResponse::getStartDate,
                        UserExperienceExtractionResponse::getEndDate)
                .containsExactly(
                        tuple("Valid Experience", ExperienceType.JOB, 13, LocalDate.of(2023, 1, 15), LocalDate.of(2023, 12, 15)),
                        tuple("Faulty Experience", null, null, null, null)
                );
    }
}
