package com.erhgo.services.generation;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.domain.classifications.erhgooccupation.OccupationCreationReason;
import com.erhgo.openapi.dto.ErhgoOccupationMinimumInfoDTO;
import com.erhgo.services.generation.dto.NormalizedTitlesResponse;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.notifier.OccupationCreationSourceType;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.UUID;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class OccupationForLabelGenerationServiceIntegrationTest extends AbstractIntegrationTest {

    @Autowired
    OccupationForLabelGenerationService occupationForLabelGenerationService;
    @MockitoBean
    FindBestMatchingOccupationService findBestMatchingOccupationService;
    @MockitoBean
    OccupationGenerator occupationGenerator;
    @MockitoBean
    TitleGenerationService titleGenerationService;

    @Test
    void verifyTitleIsWellUsed() {
        titleGenerationService.init();
        when(findBestMatchingOccupationService.findSimilarLabel(any()))
                .thenReturn(new ErhgoOccupationMinimumInfoDTO());

        var inputTitle = "nope";
        var normalizedM = "title M";
        var normalizedF = "title F";
        when(titleGenerationService.normalizeTitle(inputTitle)).thenReturn(new OpenAIResponse<NormalizedTitlesResponse>().setResult(new NormalizedTitlesResponse(normalizedM, normalizedF)));

        occupationForLabelGenerationService.createOrUpdateOccupation(inputTitle, OccupationCreationSourceType.FROM_SOURCING);

        verify(occupationGenerator).qualifyOccupation(any(UUID.class), eq(normalizedM), anyBoolean(), any(OccupationCreationReason.class), eq(normalizedF));
    }

}
