package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.userprofile.experience.UserExperience;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.generation.dto.TitleAndDescription;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.util.ClassUtils;

import java.util.List;

import static org.springframework.test.util.ReflectionTestUtils.setField;

@ExtendWith(MockitoExtension.class)
class SoftSkillDescriptionGenerationServiceUnitTest {
    @Mock
    GenerationClient generationClient;

    @Mock
    SecurityService securityService;

    @InjectMocks
    SoftSkillDescriptionGenerationService service;

    @BeforeEach
    void initialize() {
        service.init();
        setField(service, "promptConfig", new PromptConfig().setMessageFilename("SoftSkillsDescriptionMessages.yaml").setTemperature(1d).setModel("model").setMaxRetry(1).setMaxTokens(1800));
        setField(service, "yamlPromptReader", new YamlPromptReader(new ResourceLoader() {
            @Override
            public Resource getResource(String location) {
                return new ClassPathResource(location.replace("classpath:", ""));
            }

            @Override
            public ClassLoader getClassLoader() {
                return ClassUtils.getDefaultClassLoader();
            }
        }));
    }

    @Test
    void generateSoftSkills_succeed() {
        Mockito.when(generationClient.createChatCompletion(Mockito.any(), Mockito.any())).thenReturn(new ChatCompletionResponse(
                """
                        { "result": [
                            {"title": "Ma capacité d'expression", "description": "Descr 1"},
                            {"title": "Ma sensibilité", "description": "Descr 2"},
                            {"title": "Ma force de conviction", "description": "Descr 3"},
                            {"title": "Ma prise de décision", "description": "Descr 4"}]
                        }
                        """
                , new OpenAIResponse<>()));
        var result = service.generateSoftSkillsDescription(List.of(UserExperience.builder().build()), "firstName");

        Assertions.assertThat(result).containsExactlyInAnyOrder(
                new TitleAndDescription("Ma capacité d'expression", "Descr 1"),
                new TitleAndDescription("Ma sensibilité", "Descr 2"),
                new TitleAndDescription("Ma force de conviction", "Descr 3"),
                new TitleAndDescription("Ma prise de décision", "Descr 4")
        );

    }

}
