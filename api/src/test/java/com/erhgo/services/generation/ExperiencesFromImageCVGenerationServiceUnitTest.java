package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.enums.ExperienceType;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.UserExperienceExtractionResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;

import static org.assertj.core.api.Assertions.tuple;

@ExtendWith(MockitoExtension.class)
class ExperiencesFromImageCVGenerationServiceUnitTest {

    @Mock
    private YamlPromptReader yamlPromptReader;

    @Mock
    private GenerationClient generationClient;

    @Mock
    private PromptConfig promptConfig;

    @Mock
    private ErhgoOccupationRepository erhgoOccupationRepository;

    @Mock
    private SecurityService securityService;

    private ExperiencesFromImageCVGenerationService service;

    @BeforeEach
    void setUp() {
        service = new ExperiencesFromImageCVGenerationService(
                yamlPromptReader,
                generationClient,
                promptConfig,
                erhgoOccupationRepository,
                securityService
        );
        service.init();
    }

    @SneakyThrows
    @Test
    void handleResponse_should_parse_decimal_durations_correctly() {
        var jsonWithDecimalDurations = """
                {
                  "result": [
                    {
                      "title": "Consultant en marketing",
                      "organizationTitle": "ABC Consulting",
                      "type": "JOB",
                      "durationInMonths": "1.5"
                    },
                    {
                      "title": "Développeur logiciel",
                      "organizationTitle": "Tech Solutions",
                      "type": "JOB",
                      "durationInMonths": "2.8"
                    }
                  ]
                }
                """;

        var result = service.handleResponse(jsonWithDecimalDurations);

        Assertions.assertThat(result)
                .hasSize(2)
                .extracting(UserExperienceExtractionResponse::getDurationInMonths,
                        UserExperienceExtractionResponse::getTitle,
                        UserExperienceExtractionResponse::getType)
                .containsExactly(
                        tuple(2, "Consultant en marketing", ExperienceType.JOB),
                        tuple(3, "Développeur logiciel", ExperienceType.JOB)
                );
    }

    @SneakyThrows
    @Test
    void handleResponse_should_handle_invalid_durations_gracefully() {
        var jsonWithInvalidDurations = """
                {
                  "result": [
                    {
                      "title": "Valid Experience",
                      "organizationTitle": "Good Corp",
                      "type": "JOB",
                      "durationInMonths": "12"
                    },
                    {
                      "title": "Invalid Duration Experience",
                      "organizationTitle": "Test Corp",
                      "type": "INTERNSHIP",
                      "durationInMonths": "not_a_number"
                    }
                  ]
                }
                """;

        var result = service.handleResponse(jsonWithInvalidDurations);

        Assertions.assertThat(result)
                .hasSize(2)
                .extracting(UserExperienceExtractionResponse::getDurationInMonths,
                        UserExperienceExtractionResponse::getTitle,
                        UserExperienceExtractionResponse::getType)
                .containsExactly(
                        tuple(12, "Valid Experience", ExperienceType.JOB),
                        tuple(null, "Invalid Duration Experience", ExperienceType.INTERNSHIP)
                );
    }

    @SneakyThrows
    @Test
    void handleResponse_should_maintain_backward_compatibility() {
        var jsonWithIntegerDurations = """
                {
                  "result": [
                    {
                      "title": "Traditional Experience",
                      "organizationTitle": "Old Corp",
                      "type": "JOB",
                      "durationInMonths": 24
                    },
                    {
                      "title": "String Integer Experience",
                      "organizationTitle": "String Corp",
                      "type": "JOB",
                      "durationInMonths": "36"
                    }
                  ]
                }
                """;

        var result = service.handleResponse(jsonWithIntegerDurations);

        Assertions.assertThat(result)
                .hasSize(2)
                .extracting(UserExperienceExtractionResponse::getDurationInMonths)
                .containsExactly(24, 36);
    }

    @SneakyThrows
    @Test
    void handleResponse_should_handle_all_field_types_gracefully() {
        var jsonWithMixedValidInvalidData = """
                {
                  "result": [
                    {
                      "title": "Perfect Experience",
                      "organizationTitle": "Perfect Corp",
                      "type": "job",
                      "durationInMonths": "18.7",
                      "startDate": "2023-01-15",
                      "endDate": "15/06/2024"
                    },
                    {
                      "title": "Broken Experience",
                      "organizationTitle": "Broken Corp",
                      "type": "invalid_type",
                      "durationInMonths": "abc",
                      "startDate": "not_a_date",
                      "endDate": ""
                    },
                    {
                      "title": "French Experience",
                      "organizationTitle": "French Corp",
                      "type": "internship",
                      "durationInMonths": "6",
                      "startDate": "01/03/2023",
                      "endDate": "2023-09-01"
                    }
                  ]
                }
                """;

        var result = service.handleResponse(jsonWithMixedValidInvalidData);

        Assertions.assertThat(result)
                .hasSize(3)
                .extracting(UserExperienceExtractionResponse::getTitle,
                        UserExperienceExtractionResponse::getType,
                        UserExperienceExtractionResponse::getDurationInMonths,
                        UserExperienceExtractionResponse::getStartDate,
                        UserExperienceExtractionResponse::getEndDate)
                .containsExactly(
                        tuple("Perfect Experience", ExperienceType.JOB, 19, LocalDate.of(2023, 1, 15), LocalDate.of(2024, 6, 15)),
                        tuple("Broken Experience", ExperienceType.JOB, null, null, null),
                        tuple("French Experience", ExperienceType.INTERNSHIP, 6, LocalDate.of(2023, 3, 1), LocalDate.of(2023, 9, 1))
                );
    }
}
