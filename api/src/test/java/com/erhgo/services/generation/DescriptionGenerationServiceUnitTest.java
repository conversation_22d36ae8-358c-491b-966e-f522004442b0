package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.ChatGenerationClient;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.util.ClassUtils;

import java.util.Optional;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.util.ReflectionTestUtils.setField;

class DescriptionGenerationServiceUnitTest {

    static final double temperature = 42d;
    static final int maxTokens = 800;

    static final String model = "gpt42";
    static final String occupationTitle = "Title of occupation";
    static final UUID OCCUPATION_UUID = UUID.randomUUID();

    ErhgoOccupationRepository repository;

    ChatGenerationClient generationClient;

    YamlPromptReader yamlPromptReader;

    SecurityService securityService;

    @BeforeEach
    void initialize() {
        repository = Mockito.mock(ErhgoOccupationRepository.class);
        generationClient = Mockito.mock(ChatGenerationClient.class);
        yamlPromptReader = Mockito.mock(YamlPromptReader.class);
        securityService = Mockito.mock(SecurityService.class);

        setField(DescriptionGenerationService.class, "MIN_DESCRIPTION_WORD_COUNT", 2);
        setField(DescriptionGenerationService.class, "MAX_DESCRIPTION_WORD_COUNT", 10);
        setField(DescriptionGenerationService.class, "MIN_BEHAVIOR_DESCRIPTION_WORD_COUNT", 2);
        setField(DescriptionGenerationService.class, "MAX_BEHAVIOR_DESCRIPTION_WORD_COUNT", 10);


    }

    private ChatCompletionResponse chatCompletionResponse(String choice) {
        return new ChatCompletionResponse(choice, null);
    }

    @Test
    void generateOccupationDescription() {
        var service = new DescriptionGenerationService.OccupationDescriptionService(new YamlPromptReader(new ResourceLoader() {
            @Override
            public Resource getResource(String location) {
                return new ClassPathResource(location.replace("classpath:", ""));
            }

            @Override
            public ClassLoader getClassLoader() {
                return ClassUtils.getDefaultClassLoader();
            }
        }), repository, generationClient, new PromptConfig().setTemperature(temperature).setMaxTokens(800).setModel(model).setMaxRetry(3).setMessageFilename("DescriptionMessages.yaml"), securityService);
        setField(service, "generationClient", generationClient);

        var occupation = new ErhgoOccupationMotherObject().withTitle(occupationTitle).withId(OCCUPATION_UUID).build();
        when(repository.findById(OCCUPATION_UUID)).thenReturn(Optional.of(occupation));
        var expectedDescription = "modified description";

        when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class))).thenReturn(chatCompletionResponse(expectedDescription));

        var generatedDescription = service.generateOccupationDescription(OCCUPATION_UUID).getResult().getDescription();

        Assertions.assertEquals(expectedDescription, generatedDescription);

        Mockito.verify(generationClient).createChatCompletion(ArgumentMatchers.argThat(request ->
                temperature == request.getOptions().getTemperature()
                        && model.equals(request.getOptions().getModel())
                        && request.getInstructions().stream().filter(c -> c.getMessageType().equals(MessageType.USER)).count() == 9
                        && request.getInstructions().stream().filter(c -> c.getMessageType().equals(MessageType.ASSISTANT)).count() == 8
                        && request.getInstructions().stream().filter(c -> c.getMessageType().equals(MessageType.SYSTEM)).count() == 1
                        && request.getInstructions().get(17).getText().contains(occupationTitle)
                        && maxTokens == request.getOptions().getMaxTokens()
                        && request.getOptions().getStopSequences() == null
        ), any(PromptConfig.class));
    }

    @Test
    void generateBehaviorDescription() {
        var service = new DescriptionGenerationService.BehaviorDescriptionService(new YamlPromptReader(new ResourceLoader() {
            @Override
            public Resource getResource(String location) {
                return new ClassPathResource(location.replace("classpath:", ""));
            }

            @Override
            public ClassLoader getClassLoader() {
                return ClassUtils.getDefaultClassLoader();
            }
        }), repository, generationClient, new PromptConfig().setTemperature(temperature).setMaxTokens(800).setMaxRetry(3).setModel(model).setMessageFilename("BehaviorsDescriptionMessages.yaml"), securityService);
        setField(service, "generationClient", generationClient);

        var occupation = new ErhgoOccupationMotherObject().withTitle(occupationTitle).withId(OCCUPATION_UUID).build();
        when(repository.findById(OCCUPATION_UUID)).thenReturn(Optional.of(occupation));
        var expectedDescription = "modified description";

        when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class))).thenReturn(chatCompletionResponse(expectedDescription));

        var generatedDescription = service.generateOccupationBehaviorsDescription(OCCUPATION_UUID).getResult().getDescription();

        Assertions.assertEquals(expectedDescription, generatedDescription);

        Mockito.verify(generationClient).createChatCompletion(ArgumentMatchers.argThat(request ->
                temperature == request.getOptions().getTemperature()
                        && model.equals(request.getOptions().getModel())
                        && request.getInstructions().stream().filter(c -> c.getMessageType().equals(MessageType.USER)).count() == 12
                        && request.getInstructions().stream().filter(c -> c.getMessageType().equals(MessageType.ASSISTANT)).count() == 11
                        && request.getInstructions().stream().filter(c -> c.getMessageType().equals(MessageType.SYSTEM)).count() == 1
                        && request.getInstructions().get(23).getText().contains(occupationTitle)
                        && maxTokens == request.getOptions().getMaxTokens()
                        && request.getOptions().getStopSequences() == null
        ), any(PromptConfig.class));
    }

}
