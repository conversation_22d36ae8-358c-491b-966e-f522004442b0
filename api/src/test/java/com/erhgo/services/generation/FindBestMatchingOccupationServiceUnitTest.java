package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.openapi.dto.ErhgoOccupationMinimumInfoDTO;
import com.erhgo.openapi.dto.ErhgoOccupationSearchDTO;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.dtobuilder.ErhgoOccupationDataDTOBuilder;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import com.erhgo.services.generation.dto.FindBestMatchingOccupationArguments;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import com.erhgo.services.search.ErhgoOccupationFinder;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.util.ClassUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.lenient;
import static org.springframework.test.util.ReflectionTestUtils.setField;

@ExtendWith(MockitoExtension.class)
class FindBestMatchingOccupationServiceUnitTest {

    static final double temperature = 42d;

    static final String model = "gpt42";
    static final String occupationTitle = "Title of occupation";
    static final UUID OCCUPATION_UUID = UUID.randomUUID();

    @Mock
    GenerationClient generationClient;
    @Mock
    ErhgoOccupationRepository erhgoOccupationRepository;
    @Mock
    ErhgoOccupationFinder erhgoOccupationFinder;
    @Mock
    SecurityService securityService;

    @InjectMocks
    FindBestMatchingOccupationService service;

    @Mock
    ErhgoOccupationDataDTOBuilder erhgoDataDTOBuilder;


    @BeforeEach
    void initialize() {
        service.init();
        lenient().when(erhgoDataDTOBuilder.buildOccupationMininumInfo(any())).thenAnswer(invocation -> {
            var occupation = (ErhgoOccupation) invocation.getArguments()[0];
            return new ErhgoOccupationMinimumInfoDTO().id(occupation.getId()).title(occupation.getTitle());
        });

        lenient().when(erhgoOccupationFinder.searchOccupations(any(), anyBoolean())).thenAnswer(invocation -> {
            var title = (String) invocation.getArguments()[0];
            return List.of(new ErhgoOccupationSearchDTO().title(title).code(String.valueOf(OCCUPATION_UUID)));
        });

        setField(service, "promptConfig", new PromptConfig().setMessageFilename("FindBestMatchingOccupationMessages.yaml").setTemperature(temperature).setModel(model).setMaxRetry(3).setMaxTokens(1800));
        setField(service, "yamlPromptReader", new YamlPromptReader(new ResourceLoader() {
            @Override
            public Resource getResource(String location) {
                return new ClassPathResource(location.replace("classpath:", ""));
            }

            @Override
            public ClassLoader getClassLoader() {
                return ClassUtils.getDefaultClassLoader();
            }
        }));

        mockOccupationRepository();


    }

    private void mockOpenAIResponse(String response) {
        lenient().when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class))).thenReturn(new ChatCompletionResponse(response, null));
    }

    private void mockOccupationRepository() {
        var occupation = new ErhgoOccupationMotherObject().withTitle(occupationTitle).withId(OCCUPATION_UUID).withCapacities(CapacityGenerator.buildCapacity()).build();
        lenient().when(erhgoOccupationRepository.findById(OCCUPATION_UUID)).thenReturn(Optional.of(occupation));
    }


    @Test
    @SneakyThrows
    void findBestMatchingOccupationLabelWithValidMatch() {
        var openAIResponse = String.format("""
                {"uuid": "%s", "title": "Agent commercial"}
                """, OCCUPATION_UUID);

        var labelArguments = FindBestMatchingOccupationArguments.builder()
                .newLabel(occupationTitle)
                .similarOccupationsList(List.of(new ErhgoOccupationSearchDTO().title("Agent commercial").code(String.valueOf(OCCUPATION_UUID))))
                .build();
        mockOpenAIResponse(openAIResponse);

        var result = service.findSimilarLabel(labelArguments);


        assertNotNull(result);
        Assertions.assertEquals(OCCUPATION_UUID, result.getId());
        Assertions.assertEquals(occupationTitle, result.getTitle());
    }


    @Test
    void findBestMatchingOccupationLabelWithNoMatch() {
        var openAIResponse = """
                {"uuid": null, "title": null}
                """;
        mockOpenAIResponse(openAIResponse);

        var labelArguments = FindBestMatchingOccupationArguments.builder()
                .newLabel("Développeur web")
                .similarOccupationsList(new ArrayList<>())
                .build();

        lenient().when(erhgoOccupationFinder.searchOccupations(any(), anyBoolean())).thenReturn(new ArrayList<>());

        var occupation = service.findSimilarLabel(labelArguments);
        Assertions.assertNull(occupation.getId());
        Assertions.assertNull(occupation.getTitle());

    }

    @Test
    void findBestMatchingOccupationLabelWithInvalidResponse() {
        var invalidOpenAIResponse = "Réponse inattendue";
        mockOpenAIResponse(invalidOpenAIResponse);
        var labelArguments = FindBestMatchingOccupationArguments.builder()
                .newLabel("Développeur web")
                .similarOccupationsList(new ArrayList<>())
                .build();

        org.assertj.core.api.Assertions.assertThat(service.findSimilarLabel(labelArguments)).isEqualTo(new ErhgoOccupationMinimumInfoDTO());
    }


}
