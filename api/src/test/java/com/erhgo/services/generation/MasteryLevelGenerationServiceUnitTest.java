package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ClassUtils;

import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;
import static org.springframework.test.util.ReflectionTestUtils.setField;

@ExtendWith(MockitoExtension.class)
class MasteryLevelGenerationServiceUnitTest {

    static final double temperature = 42d;

    static final String model = "gpt42";
    static final String occupationTitle = "Title of occupation";
    static final UUID OCCUPATION_UUID = UUID.randomUUID();

    @Mock
    GenerationClient generationClient;
    @Mock
    ErhgoOccupationRepository erhgoOccupationRepository;
    @Mock
    SecurityService securityService;

    @InjectMocks
    MasteryLevelGenerationService service;

    static final String OPENAI_RESPONSE_SPECIFICATION_MASTERY_LEVEL = """
            {"masteryLevel": 1}
            """;


    @BeforeEach
    void initialize() {
        service.init();
        setField(service, "promptConfig", new PromptConfig().setMessageFilename("MasteryLevelMessages.yaml").setTemperature(temperature).setModel(model).setMaxRetry(3).setMaxTokens(1800));
        setField(service, "yamlPromptReader", new YamlPromptReader(new ResourceLoader() {
            @Override
            public Resource getResource(String location) {
                return new ClassPathResource(location.replace("classpath:", ""));
            }

            @Override
            public ClassLoader getClassLoader() {
                return ClassUtils.getDefaultClassLoader();
            }
        }));
    }

    private void mockOpenAIResponse(String response) {
        lenient().when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class))).thenReturn(new ChatCompletionResponse(response, null));
    }

    private void mockOccupationRepository() {
        var occupation = new ErhgoOccupationMotherObject().withTitle(occupationTitle).withId(OCCUPATION_UUID).withCapacities(CapacityGenerator.buildCapacity()).build();
        when(erhgoOccupationRepository.findById(OCCUPATION_UUID)).thenReturn(Optional.of(occupation));
    }

    @Test
    @SneakyThrows
    void associateSpecificationAndMasteryLevelToOccupation() {
        when(erhgoOccupationRepository.save(any(ErhgoOccupation.class))).thenAnswer(invocation -> {
            var res = invocation.getArgument(0);
            ReflectionTestUtils.setField(res, "id", OCCUPATION_UUID);
            return res;
        });
        mockOccupationRepository();
        mockOpenAIResponse(OPENAI_RESPONSE_SPECIFICATION_MASTERY_LEVEL);
        service.generateMasteryLevel(OCCUPATION_UUID);
        var occupation = erhgoOccupationRepository.findById(OCCUPATION_UUID).orElseThrow();
        assertEquals(MasteryLevel.PROFESSIONAL, occupation.getLevel());

    }

    @Test
    @SneakyThrows
    void callOpenAIAndValidateSpecificationAndMasteryLevel() {
        mockOpenAIResponse(OPENAI_RESPONSE_SPECIFICATION_MASTERY_LEVEL);
        var generatedSpecificationAndMasteryLevel = service.generate(occupationTitle);
        assertThat(generatedSpecificationAndMasteryLevel.getResult()).matches(i -> i.getMasteryLevel() == 1);
    }


    @Test
    @SneakyThrows
    void callOpenAIAndValidateMasteryLevel_InvalidMasteryLevel() {
        String invalidMasteryLevelResponse = "{\"masteryLevel\": invalid}";
        mockOpenAIResponse(invalidMasteryLevelResponse);
        assertThrows(GenericTechnicalException.class, () -> {
            service.generate(occupationTitle);
        });
    }


    @Test
    void associateSpecificationAndMasteryLevelToOccupation_OccupationNotFound() {
        when(erhgoOccupationRepository.findById(OCCUPATION_UUID)).thenReturn(Optional.empty());
        assertThrows(EntityNotFoundException.class, () ->
                service.generateMasteryLevel(OCCUPATION_UUID)
        );
    }

    @Test
    void associateSpecificationAndMasteryLevelToOccupation_InvalidJSON() {
        mockOpenAIResponse("invalid JSON");
        assertThrows(EntityNotFoundException.class, () ->
                service.generateMasteryLevel(OCCUPATION_UUID)
        );
    }

    @Test
    void associateSpecificationAndMasteryLevelToOccupation_EmptyJSON() {
        mockOpenAIResponse("{}");
        assertThrows(EntityNotFoundException.class, () ->
                service.generateMasteryLevel(OCCUPATION_UUID)
        );
    }


}
