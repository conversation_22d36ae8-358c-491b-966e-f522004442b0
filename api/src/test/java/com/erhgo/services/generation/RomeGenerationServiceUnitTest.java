package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.classifications.rome.RomeOccupation;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.repositories.classifications.RomeOccupationRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ClassUtils;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;
import static org.springframework.test.util.ReflectionTestUtils.setField;

@ExtendWith(MockitoExtension.class)
class RomeGenerationServiceUnitTest {

    static final double temperature = 42d;

    static final String model = "gpt42";
    static final String occupationTitle = "Title of occupation";
    static final UUID OCCUPATION_UUID = UUID.randomUUID();

    @Mock
    GenerationClient generationClient;
    @Mock
    ErhgoOccupationRepository erhgoOccupationRepository;
    @Mock
    RomeOccupationRepository romeOccupationRepository;
    @Mock
    SecurityService securityService;

    @InjectMocks
    RomeGenerationService service;

    static final String OPENAI_RESPONSE_ROMES = "['K1902','K1903','K1904']";

    @BeforeEach
    void initialize() {
        service.init();
        setField(service, "promptConfig", new PromptConfig().setMessageFilename("RomeMessages.yaml").setTemperature(temperature).setModel(model).setMaxRetry(3).setMaxTokens(1800));
        setField(service, "yamlPromptReader", new YamlPromptReader(new ResourceLoader() {
            @Override
            public Resource getResource(String location) {
                return new ClassPathResource(location.replace("classpath:", ""));
            }

            @Override
            public ClassLoader getClassLoader() {
                return ClassUtils.getDefaultClassLoader();
            }
        }));
        lenient().when(romeOccupationRepository.findAllByCodeIn(anyList())).thenAnswer(invocation -> {
            List<String> codes = invocation.getArgument(0);
            return codes.stream().map(code -> {
                var mockRomeOccupation = mock(RomeOccupation.class);
                lenient().when(mockRomeOccupation.getCode()).thenReturn(code);
                return mockRomeOccupation;
            }).toList();
        });
        mockOpenAIResponse(OPENAI_RESPONSE_ROMES);

        setField(RomeGenerationService.class, "MAX_ROME", 4);

    }

    private void mockOpenAIResponse(String response) {
        lenient().when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class))).thenReturn(new ChatCompletionResponse(response, null));
    }

    private void mockOccupationRepository() {
        var occupation = new ErhgoOccupationMotherObject().withTitle(occupationTitle).withId(OCCUPATION_UUID).withCapacities(CapacityGenerator.buildCapacity()).build();
        when(erhgoOccupationRepository.findById(OCCUPATION_UUID)).thenReturn(Optional.of(occupation));
    }

    @Test
    @SneakyThrows
    void callOpenAIAndValidateRomes() {
        var generatedRomes = service.generate(occupationTitle);
        assertThat(generatedRomes.getResult()).extracting(RomeOccupation::getCode).containsExactlyInAnyOrder("K1902", "K1903", "K1904");
    }

    @Test
    @SneakyThrows
    void callOpenAIAndValidateRomes_invalidRomes() {
        String invalidRomeCodesResponse = "['Invalid1','Invalid2']";
        mockOpenAIResponse(invalidRomeCodesResponse);
        when(romeOccupationRepository.findAllByCodeIn(anyList())).thenReturn(List.of());

        Assertions.assertThrows(FatalGenerationException.class, () -> {
            service.generate(occupationTitle);
        });

    }

    @Test
    @SneakyThrows
    void callOpenAIAndValidateRomes_toleratesNoRome() {
        mockOpenAIResponse("[]");

        org.assertj.core.api.Assertions.assertThat(service.generate(occupationTitle).getResult()).isEmpty();

    }

    @Test
    @SneakyThrows
    void callOpenAIAndValidateRomes_invalidTooManyRomes() {
        setField(service, "MAX_ROME", 1);
        Assertions.assertThrows(FatalGenerationException.class, () -> {
            service.generate(occupationTitle);
        });

    }

    @Test
    @SneakyThrows
    void associateRomesToOccupationClearPreviousRomes() {
        when(erhgoOccupationRepository.save(any(ErhgoOccupation.class))).thenAnswer(invocation -> {
            var res = invocation.getArgument(0);
            ReflectionTestUtils.setField(res, "id", OCCUPATION_UUID);
            return res;
        });

        mockOccupationRepository();

        service.generateRomeAssociations(OCCUPATION_UUID);
        var occupation = erhgoOccupationRepository.findById(OCCUPATION_UUID).orElseThrow();
        assertEquals(3, occupation.getRomeOccupations().size());
        Assertions.assertTrue(occupation.getRomeOccupations().stream().map(RomeOccupation::getCode).toList().containsAll(List.of("K1902", "K1903", "K1904")));
    }

    @Test
    @SneakyThrows
    void associateRomesToOccupation_OccupationNotFound() {
        when(erhgoOccupationRepository.findById(OCCUPATION_UUID)).thenReturn(Optional.empty());

        Assertions.assertThrows(EntityNotFoundException.class, () -> {
            service.generateRomeAssociations(OCCUPATION_UUID);
        });
    }

}
