package com.erhgo.services.generation;

import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.PromptConfig;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.domain.exceptions.openai.InvalidActivityCountException;
import com.erhgo.domain.exceptions.openai.InvalidCapacityCodesException;
import com.erhgo.domain.exceptions.openai.InvalidCapacityCountException;
import com.erhgo.domain.referential.Activity;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.repositories.ActivityRepository;
import com.erhgo.repositories.CapacityRepository;
import com.erhgo.repositories.JobActivityLabelRepository;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.dtobuilder.ActivityDTOBuilder;
import com.erhgo.services.dtobuilder.AuditingDTOBuilder;
import com.erhgo.services.dtobuilder.ErhgoOccupationDataDTOBuilder;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ActivityForCapacityModel;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ClassUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyCollection;
import static org.mockito.Mockito.*;
import static org.springframework.test.util.ReflectionTestUtils.setField;

@ExtendWith(MockitoExtension.class)
class ActivityGenerationServiceUnitTest {

    static final double temperature = 42d;

    static final String model = "gpt42";
    static final String occupationTitle = "Title of occupation";
    static final UUID OCCUPATION_UUID = UUID.randomUUID();

    @Mock
    GenerationClient generationClient;
    @Mock
    KeycloakMockService keycloakService;
    @Mock
    ErhgoOccupationRepository erhgoOccupationRepository;
    @Mock
    CapacityRepository capacityRepository;
    @Mock
    JobActivityLabelRepository jobActivityLabelRepository;
    @Mock
    ActivityRepository activityRepository;
    @Mock
    SecurityService securityService;

    @InjectMocks
    ActivityGenerationService service;

    static final List<ActivityForCapacityModel> activities = List.of(
            new ActivityForCapacityModel("Transporter des matériaux de construction", List.of("CA2-14", "CA1-30")),
            new ActivityForCapacityModel("Repérer et traiter des anomalies ou des dysfonctionnements", List.of("CA3-15"))
    );

    static final String MOCKED_JSON = """
            {"capacitiesList": ["CA3-15", "CA2-14", "CA1-30"],
            "activitiesDetail": [
                {"title": "Transporter des matériaux de construction","capacities": ["CA2-14", "CA1-30"]},
                {"title": "Repérer et traiter des anomalies ou des dysfonctionnements","capacities": ["CA3-15"]}
             ],
             "capacitiesConsistency": "OK",
             "numberOfCapacitiesRule": "OK"
             }
            """;
    static final List<AssistantMessage> PROMPT_DIRECTIVES_MESSAGES = List.of(new AssistantMessage(MOCKED_JSON));

    @BeforeEach
    void initialize() {
        service.init();
        setField(service, "erhgoOccupationDataDTOBuilder", new ErhgoOccupationDataDTOBuilder(null, keycloakService, null, new ActivityDTOBuilder(new AuditingDTOBuilder(keycloakService)),
                null,
                null,
                null));
        setField(service, "promptConfig", new PromptConfig().setMaxTokens(4096).setModel(model).setMaxRetry(3).setMessageFilename("ActivityMessages.yaml"));
        setField(service, "yamlPromptReader", new YamlPromptReader(new ResourceLoader() {
            @Override
            public Resource getResource(String location) {
                return new ClassPathResource(location.replace("classpath:", ""));
            }

            @Override
            public ClassLoader getClassLoader() {
                return ClassUtils.getDefaultClassLoader();
            }
        }));
        setField(ActivityGenerationService.class, "MINIMUM_TOLERABLE_NUMBER_OF_CAPACITIES", 2);
        setField(ActivityGenerationService.class, "MAXIMUM_TOLERABLE_NUMBER_OF_CAPACITIES", 3);
        setField(ActivityGenerationService.class, "MINIMUM_TOLERABLE_NUMBER_OF_ACTIVITIES", 1);
        setField(ActivityGenerationService.class, "MAXIMUM_TOLERABLE_NUMBER_OF_ACTIVITIES", 3);

        lenient().when(capacityRepository.findByCodeIn(anyCollection())).thenAnswer(invocation -> {
            List<String> codes = invocation.getArgument(0);
            return codes.stream().map(code -> {
                Capacity capacity = new Capacity();
                capacity.setCode(code);
                return capacity;
            }).collect(Collectors.toList());
        });

        lenient().when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class))).thenReturn(new ChatCompletionResponse(MOCKED_JSON, null));
    }

    @Test
    void associateActivitiesToOccupationClearPreviousActivities() {
        when(activityRepository.save(any(Activity.class))).thenAnswer(invocation -> {
            var a = invocation.getArgument(0);
            ReflectionTestUtils.setField(a, "uuid", UUID.randomUUID());
            return a;
        });
        when(jobActivityLabelRepository.save(any(JobActivityLabel.class))).thenAnswer(invocation -> {
            var res = invocation.getArgument(0);
            ReflectionTestUtils.setField(res, "uuid", UUID.randomUUID());
            return res;
        });
        when(erhgoOccupationRepository.save(any(ErhgoOccupation.class))).thenAnswer(invocation -> {
            var res = invocation.getArgument(0);
            ReflectionTestUtils.setField(res, "id", OCCUPATION_UUID);
            return res;
        });
        mockOccupationRepository();

        var updatedOccupation = service.associateActivitiesToOccupation(OCCUPATION_UUID, activities);

        Assertions.assertNotNull(updatedOccupation);
        Assertions.assertNotNull(updatedOccupation.getActivities());
        Assertions.assertEquals(2, updatedOccupation.getActivities().size());

    }

    private void mockOccupationRepository() {
        var occupation = new ErhgoOccupationMotherObject().withTitle(occupationTitle).withId(OCCUPATION_UUID).withCapacities(CapacityGenerator.buildCapacity()).build();
        when(erhgoOccupationRepository.findById(OCCUPATION_UUID)).thenReturn(Optional.of(occupation));
    }

    @Test
    @SneakyThrows
    void callOpenAIAndValidateJSON() {
        var generatedActivities = service.handleResponse(MOCKED_JSON);
        Assertions.assertEquals(activities, generatedActivities);
    }

    @Test
    @SneakyThrows
    void callOpenAIAndValidateJSON_invalidTooFewCapacities() {
        setField(ActivityGenerationService.class, "MINIMUM_TOLERABLE_NUMBER_OF_CAPACITIES", 42);


        InvalidCapacityCountException e = null;
        try {
            service.handleResponse(MOCKED_JSON);
        } catch (InvalidCapacityCountException exception) {
            e = exception;
        }
        Assertions.assertNotNull(e);
    }

    @Test
    @SneakyThrows
    void callOpenAIAndValidateJSON_invalidTooManyCapacities() {
        setField(ActivityGenerationService.class, "MAXIMUM_TOLERABLE_NUMBER_OF_CAPACITIES", 1);


        InvalidCapacityCountException e = null;
        try {
            service.handleResponse(MOCKED_JSON);
        } catch (InvalidCapacityCountException exception) {
            e = exception;
        }
        Assertions.assertNotNull(e);
    }

    @Test
    @SneakyThrows
    void callOpenAIAndValidateJSON_invalidTooFewActivities() {
        setField(ActivityGenerationService.class, "MINIMUM_TOLERABLE_NUMBER_OF_ACTIVITIES", 42);


        InvalidActivityCountException e = null;
        try {
            service.handleResponse(MOCKED_JSON);
        } catch (InvalidActivityCountException exception) {
            e = exception;
        }
        Assertions.assertNotNull(e);
    }

    @Test
    @SneakyThrows
    void callOpenAIAndValidateJSON_invalidTooManyActivities() {
        setField(ActivityGenerationService.class, "MAXIMUM_TOLERABLE_NUMBER_OF_ACTIVITIES", 1);


        InvalidActivityCountException e = null;
        try {
            service.handleResponse(MOCKED_JSON);
        } catch (InvalidActivityCountException exception) {
            e = exception;
        }
        Assertions.assertNotNull(e);
    }

    @Test
    @SneakyThrows
    void generateActivities_max3attempts() {
        mockOccupationRepository();
        setField(ActivityGenerationService.class, "MAXIMUM_TOLERABLE_NUMBER_OF_ACTIVITIES", 1);

        FatalGenerationException e = null;
        try {
            service.qualifyAndPersistOccupationActivities(OCCUPATION_UUID);
        } catch (FatalGenerationException exception) {
            e = exception;
        }
        Assertions.assertNotNull(e);
        verify(generationClient, times(3)).createChatCompletion(any(), any());
    }

    @Test
    @SneakyThrows
    void generateActivities_failsEarlyWhenNoOccupation() {
        when(erhgoOccupationRepository.findById(OCCUPATION_UUID)).thenReturn(Optional.empty());
        EntityNotFoundException e = null;
        try {
            service.qualifyAndPersistOccupationActivities(OCCUPATION_UUID);
        } catch (EntityNotFoundException exception) {
            e = exception;
        }
        Assertions.assertNotNull(e);
        verifyNoInteractions(generationClient);
    }

    @Test
    @SneakyThrows
    void callOpenAIAndValidateJSON_invalidCapacities() {
        when(capacityRepository.findByCodeIn(anyCollection())).thenReturn(new ArrayList<>());

        InvalidCapacityCodesException e = null;
        try {
            service.handleResponse(MOCKED_JSON);
        } catch (InvalidCapacityCodesException exception) {
            e = exception;
        }
        Assertions.assertNotNull(e);
    }

}
