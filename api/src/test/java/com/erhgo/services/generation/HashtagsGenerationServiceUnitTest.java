package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.enums.HardSkillType;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.util.ClassUtils;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.util.ReflectionTestUtils.setField;

@ExtendWith(MockitoExtension.class)
class HashtagsGenerationServiceUnitTest {
    @Mock
    GenerationClient generationClient;

    @Mock
    SecurityService securityService;

    @Mock
    UserProfileRepository userProfileRepository;

    @InjectMocks
    HashtagsGenerationService service;

    @BeforeEach
    void initialize() {
        service.init();
        setField(service, "promptConfig", new PromptConfig().setMessageFilename("HashtagsMessages.yaml").setTemperature(1d).setModel("model").setMaxRetry(1).setMaxTokens(1800));
        setField(service, "yamlPromptReader", new YamlPromptReader(new ResourceLoader() {
            @Override
            public Resource getResource(String location) {
                return new ClassPathResource(location.replace("classpath:", ""));
            }

            @Override
            public ClassLoader getClassLoader() {
                return ClassUtils.getDefaultClassLoader();
            }
        }));
    }

    @Test
    void generateHashtags_succeed() {
        Mockito.when(generationClient.createChatCompletion(Mockito.any(), Mockito.any())).thenReturn(new ChatCompletionResponse("#A\n#B #C#D", new OpenAIResponse<>()));
        var result = service.generateHashtags(List.of("A"));
        assertThat(result).containsExactlyInAnyOrder("#A", "#B", "#C", "#D");
    }

    @Test
    void generateHashtags_generationFails() {
        Mockito.when(generationClient.createChatCompletion(Mockito.any(), Mockito.any())).thenReturn(new ChatCompletionResponse("A", new OpenAIResponse<>()));
        Assertions.assertThatThrownBy(() -> service.generateHashtags(List.of("A"))).isInstanceOf(FatalGenerationException.class);
    }

    @Test
    void generateHashtags_hashtagsFails() {
        Assertions.assertThatThrownBy(() -> service.generateHashtags(List.of())).isInstanceOf(FatalGenerationException.class);
    }

    @Test
    void regenerateHashtags_validSelectedHashtags() {
        var userId = "test-user";
        var selectedHashtags = List.of("#Java", "#Spring", "#React", "#Docker", "#Kubernetes", "#AWS", "#PostgreSQL", "#Git");
        var deselectedHashtags = List.of("#PHP", "#Python", "#Ruby", "#Go");

        var userProfile = mock(UserProfile.class);
        var activity1 = mock(JobActivityLabel.class);
        var activity2 = mock(JobActivityLabel.class);

        when(activity1.getTitle()).thenReturn("Développement logiciel");
        when(activity2.getTitle()).thenReturn("Gestion de projet");

        when(userProfile.getAllActivities()).thenReturn(Set.of(activity1, activity2));
        when(userProfile.getHardSkills()).thenReturn(Map.of(HardSkillType.KNOWLEDGE, "Java, Spring Boot"));

        when(userProfileRepository.findByUserId(userId)).thenReturn(Optional.of(userProfile));

        var generatedHashtags = List.of("#Microservices", "#DevOps", "#Agile", "#TDD");
        var spyService = spy(service);
        doReturn(generatedHashtags).when(spyService).generateHashtags(any());

        var result = spyService.regenerateHashtags(userId, selectedHashtags, deselectedHashtags);

        assertThat(result)
                .hasSize(12)
                .containsAll(selectedHashtags)
                .containsAll(generatedHashtags)
                .doesNotContainAnyElementsOf(deselectedHashtags);

        verify(userProfile).updateHashtags(result);
        verify(userProfileRepository).findByUserId(userId);
    }

    @Test
    void regenerateHashtags_with12SelectedHashtags() {
        var userId = "test-user";
        var selectedHashtags = List.of(
                "#Java", "#Spring", "#React", "#Docker",
                "#Kubernetes", "#AWS", "#PostgreSQL", "#Git",
                "#Microservices", "#DevOps", "#Agile", "#TDD"
        );

        var userProfile = mock(UserProfile.class);
        when(userProfileRepository.findByUserId(userId)).thenReturn(Optional.of(userProfile));

        var spyService = spy(service);

        var result = spyService.regenerateHashtags(userId, selectedHashtags, Collections.emptyList());

        assertThat(result)
                .hasSize(12)
                .containsExactlyElementsOf(selectedHashtags);

        verify(userProfile).updateHashtags(selectedHashtags);
        verify(userProfileRepository).findByUserId(userId);
        verify(spyService, never()).generateHashtags(any());
    }

    @Test
    void regenerateHashtags_tooFewHashtags() {
        var userId = "test-user";
        var selectedHashtags = List.of("#Java", "#Spring", "#React", "#Docker", "#Kubernetes");
        var deselectedHashtags = List.of("#PHP", "#Python");

        var userProfile = mock(UserProfile.class);
        when(userProfileRepository.findByUserId(userId)).thenReturn(Optional.of(userProfile));

        Assertions.assertThatThrownBy(() -> service.regenerateHashtags(userId, selectedHashtags, deselectedHashtags))
                .isInstanceOf(FatalGenerationException.class)
                .hasMessageContaining("Total selected and deselected hashtags must be between 8 and 12 items, got: 7");

        verify(userProfileRepository).findByUserId(userId);
    }

    @Test
    void regenerateHashtags_userHasNoActivities() {
        var userId = "test-user";
        var selectedHashtags = List.of("#Java", "#Spring", "#React", "#Docker", "#Kubernetes", "#AWS", "#PostgreSQL", "#Git");

        var userProfile = mock(UserProfile.class);
        when(userProfile.getAllActivities()).thenReturn(Set.of());
        when(userProfileRepository.findByUserId(userId)).thenReturn(Optional.of(userProfile));

        var spyService = spy(service);

        var result = spyService.regenerateHashtags(userId, selectedHashtags, List.of("#PHP"));

        assertThat(result)
                .hasSize(8)
                .containsExactlyElementsOf(selectedHashtags);

        verify(userProfile).updateHashtags(selectedHashtags);
        verify(userProfileRepository).findByUserId(userId);
        verify(spyService, never()).generateHashtags(any());
    }

    @Test
    void regenerateHashtags_withSelectedAndDeselectedHashtags() {
        var userId = "test-user";
        var selectedHashtags = List.of("#Java", "#Spring", "#React", "#Docker");
        var deselectedHashtags = List.of("#PHP", "#Python", "#Ruby", "#Go", "#C++", "#C#", "#Kotlin", "#Swift");

        var userProfile = mock(UserProfile.class);
        var activity1 = mock(JobActivityLabel.class);

        when(activity1.getTitle()).thenReturn("Développement logiciel");
        when(userProfile.getAllActivities()).thenReturn(Set.of(activity1));
        when(userProfile.getHardSkills()).thenReturn(Map.of(HardSkillType.KNOWLEDGE, "Java, Spring Boot"));

        when(userProfileRepository.findByUserId(userId)).thenReturn(Optional.of(userProfile));

        var generatedHashtags = List.of("#Kubernetes", "#AWS", "#PostgreSQL", "#Git", "#Microservices", "#DevOps", "#Agile", "#TDD");
        var spyService = spy(service);
        doReturn(generatedHashtags).when(spyService).generateHashtags(any());

        var result = spyService.regenerateHashtags(userId, selectedHashtags, deselectedHashtags);

        assertThat(result)
                .hasSize(12)
                .containsAll(selectedHashtags)
                .containsAll(generatedHashtags)
                .doesNotContainAnyElementsOf(deselectedHashtags);

        verify(userProfile).updateHashtags(result);
        verify(userProfileRepository).findByUserId(userId);
    }

    @Test
    void regenerateHashtags_onlyOneDeselectedHashtag_shouldGenerateExactlyOne() {
        var userId = "test-user";
        var selectedHashtags = List.of("#Java", "#Spring", "#React", "#Docker", "#Kubernetes", "#AWS", "#PostgreSQL", "#Git", "#Microservices", "#DevOps", "#Agile");
        var deselectedHashtags = List.of("#PHP");

        var userProfile = mock(UserProfile.class);
        var activity1 = mock(JobActivityLabel.class);

        when(activity1.getTitle()).thenReturn("Développement logiciel");
        when(userProfile.getAllActivities()).thenReturn(Set.of(activity1));
        when(userProfile.getHardSkills()).thenReturn(Map.of(HardSkillType.KNOWLEDGE, "Java, Spring Boot"));

        when(userProfileRepository.findByUserId(userId)).thenReturn(Optional.of(userProfile));

        var generatedHashtags = List.of("#TDD");
        var spyService = spy(service);
        doReturn(generatedHashtags).when(spyService).generateHashtags(any());

        var result = spyService.regenerateHashtags(userId, selectedHashtags, deselectedHashtags);

        assertThat(result)
                .hasSize(12)
                .containsAll(selectedHashtags)
                .containsAll(generatedHashtags)
                .doesNotContainAnyElementsOf(deselectedHashtags);

        verify(userProfile).updateHashtags(result);
        verify(userProfileRepository).findByUserId(userId);
    }

    @Test
    void regenerateHashtags_noDeselectedHashtags_shouldGenerateToReach12() {
        var userId = "test-user";
        var selectedHashtags = List.of("#Java", "#Spring", "#React", "#Docker", "#Kubernetes", "#AWS", "#PostgreSQL", "#Git");
        var deselectedHashtags = Collections.<String>emptyList();

        var userProfile = mock(UserProfile.class);
        var activity1 = mock(JobActivityLabel.class);
        when(activity1.getTitle()).thenReturn("Développement logiciel");
        when(userProfile.getAllActivities()).thenReturn(Set.of(activity1));
        when(userProfile.getHardSkills()).thenReturn(Map.of(HardSkillType.KNOWLEDGE, "Java, Spring Boot"));
        when(userProfileRepository.findByUserId(userId)).thenReturn(Optional.of(userProfile));

        var generatedHashtags = List.of("#Microservices", "#DevOps", "#Agile", "#TDD");
        var spyService = spy(service);
        doReturn(generatedHashtags).when(spyService).generateHashtags(any());

        var result = spyService.regenerateHashtags(userId, selectedHashtags, deselectedHashtags);

        assertThat(result)
                .hasSize(12)
                .containsAll(selectedHashtags)
                .containsAll(generatedHashtags);

        verify(userProfile).updateHashtags(result);
        verify(userProfileRepository).findByUserId(userId);
        verify(spyService).generateHashtags(any());
    }

    @Test
    void regenerateHashtags_specialCase_userWith11HashtagsSelects9Deselects2() {
        var userId = "test-user";
        var selectedHashtags = List.of("#Java", "#Spring", "#React", "#Docker", "#Kubernetes", "#AWS", "#PostgreSQL", "#Git", "#Microservices");
        var deselectedHashtags = List.of("#PHP", "#Python");

        var userProfile = mock(UserProfile.class);
        var activity1 = mock(JobActivityLabel.class);
        when(activity1.getTitle()).thenReturn("Développement logiciel");
        when(userProfile.getAllActivities()).thenReturn(Set.of(activity1));
        when(userProfile.getHardSkills()).thenReturn(Map.of(HardSkillType.KNOWLEDGE, "Java, Spring Boot"));
        when(userProfileRepository.findByUserId(userId)).thenReturn(Optional.of(userProfile));

        var generatedHashtags = List.of("#DevOps", "#Agile", "#TDD");
        var spyService = spy(service);
        doReturn(generatedHashtags).when(spyService).generateHashtags(any());

        var result = spyService.regenerateHashtags(userId, selectedHashtags, deselectedHashtags);

        assertThat(result)
                .hasSize(12)
                .containsAll(selectedHashtags)
                .containsAll(generatedHashtags)
                .doesNotContainAnyElementsOf(deselectedHashtags);

        verify(userProfile).updateHashtags(result);
        verify(userProfileRepository).findByUserId(userId);
        verify(spyService).generateHashtags(any());
    }
}
