package com.erhgo.services.generation;

import com.erhgo.domain.enums.DiplomaLevel;
import com.erhgo.services.generation.dto.UserInfosExtractionResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

class UserInfosExtractionResponseDeserializationTest {

    @SneakyThrows
    @ParameterizedTest
    @ValueSource(booleans = {false, true})
    void generateUserInfos(boolean nullDiploma) {
        var result = new ObjectMapper().readValue("""
                {
                  "lastName": "LN",
                  "firstName": "fn",
                  "email": "<EMAIL>",
                  "phoneNumber": "123",
                  "driverLicense": true,
                  "diplomaLevel": "%s",
                  "location": "Lyon"
                }
                """.formatted(nullDiploma ? "NOPE" : "BAC_2"), UserInfosExtractionResponse.class);
        Assertions.assertThat(result.getDiplomaLevel()).isEqualTo(nullDiploma ? null : DiplomaLevel.BAC_2);
        Assertions.assertThat(result.getLastName()).isEqualTo("LN");
        Assertions.assertThat(result.getFirstName()).isEqualTo("fn");
        Assertions.assertThat(result.getEmail()).isEqualTo("<EMAIL>");
        Assertions.assertThat(result.getPhoneNumber()).isEqualTo("123");
        Assertions.assertThat(result.getDriverLicense()).isTrue();
        Assertions.assertThat(result.getLocation()).isEqualTo("Lyon");
    }
}
