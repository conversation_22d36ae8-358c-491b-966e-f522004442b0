package com.erhgo.services.mailing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.enums.NotificationType;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.domain.userprofile.notification.AbstractNotification;
import com.erhgo.domain.userprofile.notification.RecruitmentNotification;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.generators.RecruitmentGenerator;
import com.erhgo.generators.UserProfileGenerator;
import com.erhgo.openapi.dto.UsersMobileNotificationDTO;
import com.erhgo.repositories.NotificationRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.notifications.NotificationService;
import com.erhgo.services.userprofile.UserMobileService;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.ApplicationContext;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;

@TestPropertySource(properties = {"sendinblue.templates.classical-candidature-proposal=42", "sendinblue.forcedSourcingRecipients=mail1@test,mail2@test"})
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
class SendCandidatureProposalServiceTest extends AbstractIntegrationTest {

    @SpyBean
    private UserMobileService userMobileService;

    @SpyBean
    private NotificationService notificationService;

    @Autowired
    private RecruitmentNotificationGenerator sendCandidatureProposalService;

    @Autowired
    private CapacityGenerator capacityGenerator;

    @Autowired
    private RecruitmentGenerator recruitmentGenerator;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Autowired
    private OrganizationGenerator organizationGenerator;

    @Autowired
    private NotificationRepository notificationRepository;

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void generates_notifications() {
        var recruitment = getRecruitment();
        var user = userProfileGenerator.createUserProfile();
        var usersId = Set.of(user.userId());
        sendCandidatureProposalService.generateNotifications(usersId, recruitment.getId());
        txHelper.doInTransaction(() -> {
            var notifiedRecruitments = notificationRepository.findRecruitmentNotificationByUserProfileUuid(user.uuid());

            assertThat(notifiedRecruitments)
                    .extracting(RecruitmentNotification::getRecruitment)
                    .extracting(Recruitment::getId)
                    .containsExactly(recruitment.getId());

            assertThat(notifiedRecruitments)
                    .extracting(AbstractNotification::getType)
                    .contains(NotificationType.NONE);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void send_notification_to_candidates() {
        var recruitment = getRecruitment();
        var user = userProfileGenerator.createUserProfile();
        var usersId = Set.of(user.userId());

        sendCandidatureProposalService.generateNotifications(usersId, recruitment.getId());

        var expectedDto = new UsersMobileNotificationDTO()
                .usersId(List.of(user.userId()))
                .subject("Chef de projet à Lyon")
                .content("Consultez cette offre sur #jenesuisPASunCV")
                .data(Map.of("notificationType", "1", "recruitmentCode", "R-1"));
        Mockito.verify(notificationService).sendNotificationToUsers(expectedDto);

        txHelper.doInTransaction(() ->
                assertThat(notificationRepository.findRecruitmentNotificationByUserProfileUuid(user.uuid()))
                        .extracting(RecruitmentNotification::getRecruitment)
                        .extracting(Recruitment::getId)
                        .containsExactly(recruitment.getId())
        );
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void should_not_send_proposal_mail_to_empty_list() {
        var recruitment = getRecruitment();
        sendCandidatureProposalService.generateNotifications(Set.of(), recruitment.getId());
        Mockito.verifyNoInteractions(notificationService);
        Assertions.assertThat(notificationRepository.findAll()).isEmpty();
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void should_send_notification_to_mobile_not_mail() {
        var recruitment = getRecruitment();
        var userWhoDoesntWantJobOffer = applicationContext.getBean(UserProfileMotherObject.class)
                .withJobOfferOptOut(true)
                .buildAndPersist();
        var userWhoWantsJobOffer = applicationContext.getBean(UserProfileMotherObject.class)
                .withJobOfferOptOut(false)
                .buildAndPersist();

        var users = Set.of(userWhoDoesntWantJobOffer, userWhoWantsJobOffer);

        sendCandidatureProposalService.generateNotifications(
                users.stream().map(UserProfile::userId).collect(Collectors.toSet()),
                recruitment.getId()
        );

        var expectedDto = new UsersMobileNotificationDTO()
                .usersId(users.stream().map(UserProfile::userId).sorted().toList())
                .subject("Chef de projet à Lyon")
                .content("Consultez cette offre sur #jenesuisPASunCV")
                .data(Map.of("notificationType", "1", "recruitmentCode", "R-1"));
        Mockito.verify(notificationService).sendNotificationToUsers(expectedDto);
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void should_send_not_notification_to_empty_list() {
        var recruitment = getRecruitment();
        sendCandidatureProposalService.generateNotifications(Set.of(), recruitment.getId());

        Mockito.verifyNoInteractions(userMobileService);
    }

    private Recruitment getRecruitment() {

        var recruiter = organizationGenerator.createRecruiter("T-05", AbstractOrganization.OrganizationType.TERRITORIAL);
        return applicationContext.getBean(RecruitmentMotherObject.class)
                .withRecruiter(recruiter)
                .withJobTitle("Chef de projet")
                .withLocation(Location.builder().city("Lyon").build())
                .buildAndPersist();
    }


}
