package com.erhgo.services.mailing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.CandidatureEmailRefusalState;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.time.OffsetDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.*;

@EnableRetry
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
@TestPropertySource(properties = {"sendinblue.templates.refuse-candidature-sourcing=42"})
@Slf4j
class SendCandidatureRefusalEmailServiceTest extends AbstractIntegrationTest {
    @Autowired
    RecruitmentCandidatureRepository recruitmentCandidatureRepository;

    @Autowired
    SendCandidatureRefusalEmailScheduler sendEmailForSourcingCandidaturesRefused;

    @MockitoBean
    MailingListService mailingListService;

    @Autowired
    KeycloakMockService keycloakMockService;

    @Test
    @ResetDataAfter
    void sendRefusalEmail() {
        var user1 = applicationContext.getBean(UserProfileMotherObject.class)
                .withEmail("email@localhost")
                .buildAndPersist();

        var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withState(GlobalCandidatureState.INTRODUCE_TO_CLIENT)
                .withUserProfile(user1)
                .withRefusalState(CandidatureEmailRefusalState.WAITING)
                .withSubmissionDate(OffsetDateTime.now().minusHours(64))
                .buildAndPersist();
        var candidatureFailed = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withState(GlobalCandidatureState.INTRODUCE_TO_CLIENT)
                .withUserProfile(applicationContext.getBean(UserProfileMotherObject.class)
                        .withEmail("fail@localhost").buildAndPersist())
                .withRefusalState(CandidatureEmailRefusalState.WAITING)
                .withSubmissionDate(OffsetDateTime.now().minusHours(64))
                .buildAndPersist();
        var candidatureMailRefused = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withState(GlobalCandidatureState.INTRODUCE_TO_CLIENT)
                .withUserProfile(applicationContext.getBean(UserProfileMotherObject.class)
                        .withEmail("refuse@localhost").withJobOfferOptOut(true).buildAndPersist())
                .withRefusalState(CandidatureEmailRefusalState.WAITING)
                .withSubmissionDate(OffsetDateTime.now().minusHours(64))
                .buildAndPersist();
        var tooEarlyRefusedCandidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withState(GlobalCandidatureState.INTRODUCE_TO_CLIENT)
                .withUserProfile(user1)
                .withRefusalState(CandidatureEmailRefusalState.WAITING)
                .withSubmissionDate(OffsetDateTime.now().minusHours(12))
                .buildAndPersist();
        var alreadySent = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withState(GlobalCandidatureState.INTRODUCE_TO_CLIENT)
                .withUserProfile(user1)
                .withRefusalState(CandidatureEmailRefusalState.DONE)
                .withSubmissionDate(OffsetDateTime.now().minusHours(64))
                .buildAndPersist();

        var parametersUser = new HashMap<>(Map.of(
                "JOB", candidature.getJobTitle(),
                "RECRUITER", candidature.getOrganizationName(),
                "LOCATION", ""
        ));

        Mockito.when(mailingListService.sendMailsForTemplate(ArgumentMatchers.argThat(m -> m != null && m.stream().anyMatch(e -> e.contains("fail"))), anyLong(), any(), isNull()))
                .thenReturn(CompletableFuture.failedFuture(new RuntimeException()));
        Mockito.when(mailingListService.sendMailsForTemplate(ArgumentMatchers.argThat(m -> m != null && m.stream().anyMatch(e -> !e.contains("fail"))), anyLong(), any(), isNull()))
                .thenReturn(CompletableFuture.completedFuture(Set.of("email@localhost")));
        sendEmailForSourcingCandidaturesRefused.sendCandidatureRefusalEmails();

        Mockito.verify(mailingListService).sendMailsForTemplate(Set.of("email@localhost"), 42L, parametersUser, null);
        Mockito.verify(mailingListService).sendMailsForTemplate(Mockito.eq(Set.of("fail@localhost")), Mockito.eq(42L), anyMap(), isNull());
        Mockito.verifyNoMoreInteractions(mailingListService);
        txHelper.doInTransaction(() -> {
            Assertions.assertThat(recruitmentCandidatureRepository.findById(candidatureFailed.getId()).orElseThrow().getCandidatureRefusalState().getEmailSent()).isEqualTo(CandidatureEmailRefusalState.ERROR);
            Assertions.assertThat(recruitmentCandidatureRepository.findById(candidatureMailRefused.getId()).orElseThrow().getCandidatureRefusalState().getEmailSent()).isEqualTo(CandidatureEmailRefusalState.NONE);
            Assertions.assertThat(recruitmentCandidatureRepository.findById(candidature.getId()).orElseThrow().getCandidatureRefusalState().getEmailSent()).isEqualTo(CandidatureEmailRefusalState.DONE);
            Assertions.assertThat(recruitmentCandidatureRepository.findById(tooEarlyRefusedCandidature.getId()).orElseThrow().getCandidatureRefusalState().getEmailSent()).isEqualTo(CandidatureEmailRefusalState.WAITING);
            Assertions.assertThat(recruitmentCandidatureRepository.findById(alreadySent.getId()).orElseThrow().getCandidatureRefusalState().getEmailSent()).isEqualTo(CandidatureEmailRefusalState.DONE);
        });
    }
}
