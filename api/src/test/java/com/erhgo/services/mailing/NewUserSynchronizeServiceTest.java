package com.erhgo.services.mailing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.AuditableFieldHelper;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.MailConfiguration;
import com.erhgo.domain.userprofile.MailVerification;
import com.erhgo.generators.UserProfileGenerator;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.utils.DateTimeUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;

@EnableRetry
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
class NewUserSynchronizeServiceTest extends AbstractIntegrationTest {

    @Autowired
    UserProfileRepository userProfileRepository;

    @Autowired
    UserProfileGenerator userProfileGenerator;

    @Autowired
    NewUserWelcomeMailService newUserWelcomeMailService;

    @Autowired
    AuditableFieldHelper auditableFieldHelper;

    @MockitoBean
    MailingListService mailingListService;

    @Autowired
    KeycloakMockService keycloakMockService;

    @Test
    @ResetDataAfter
    void sendFirstWelcomeMails() {
        var locationOut = Location.builder().citycode("59000")
                .city("Lille")
                .longitude(42f)
                .latitude(24f)
                .postcode("59000")
                .departmentCode("59")
                .regionName("Là-haut")
                .build();


        var uuid = UUID.randomUUID().toString();
        var uuid2 = UUID.randomUUID().toString();

        var user1 = userProfileGenerator.createUserProfileWithLocation(uuid, locationOut);
        var user2 = userProfileGenerator.createUserProfileWithLocation(uuid2, getLocationIn());
        var userTooRecent = userProfileGenerator.createUserProfileWithLocation(UUID.randomUUID().toString(), getLocationIn());
        txHelper.doInTransaction(() -> {
            userProfileGenerator.createUserProfileWithLocation(UUID.randomUUID().toString(), getLocationIn()).generalInformation()
                    .getMailVerificationState().setState(MailVerification.MailVerificationState.REQUIRES_VERIFICATION);

            var user1_updated = userProfileRepository.findByUserId(user1.userId()).orElseThrow();
            var user2_updated = userProfileRepository.findByUserId(user2.userId()).orElseThrow();
            assertThat(user1_updated.isFirstConfirmationMailSent()).isFalse();
            assertThat(user2_updated.isFirstConfirmationMailSent()).isFalse();
            auditableFieldHelper.updateCreatedDate(userTooRecent, new Date());
            auditableFieldHelper.updateCreatedDate(user1, new Date(Instant.now().minus(8, ChronoUnit.HOURS).toEpochMilli()));

        });
        Mockito.when(mailingListService.sendMailsToProfilesForTemplate(any(), any(), anyLong(), any(), isNull()))
                .thenReturn(CompletableFuture.completedFuture(null));
        newUserWelcomeMailService.sendWelcomeMails();
        txHelper.doInTransaction(() -> {
            var user1_updated = userProfileRepository.findByUserId(user1.userId()).orElseThrow();
            var user2_updated = userProfileRepository.findByUserId(user2.userId()).orElseThrow();
            assertThat(user1_updated.isFirstConfirmationMailSent()).isTrue();
            assertThat(user2_updated.isFirstConfirmationMailSent()).isFalse();
        });
        // template id are customized in application-test.yml
        Mockito.verify(mailingListService).sendMailsToProfilesForTemplate(List.of(user1), null, 5L, null, null);
        Mockito.verifyNoMoreInteractions(mailingListService);
    }

    @Test
    @ResetDataAfter
    void sendSecondWelcomeMails() {
        var uuidOk = UUID.randomUUID();
        txHelper.doInTransaction(() -> {
            var dateOK = DateTimeUtils.offsetDateToDate(OffsetDateTime.now().minusHours(50));
            Stream.of(MailConfiguration.ConfirmationMailState.values()).forEach(s -> {
                var user1 = userProfileGenerator.createUserProfile(s == MailConfiguration.ConfirmationMailState.FIRST_SENT ? uuidOk : UUID.randomUUID());
                user1.confirmWelcomeEmailSent(s);
                if (s == MailConfiguration.ConfirmationMailState.FIRST_SENT) {
                    auditableFieldHelper.updateCreatedDate(user1, dateOK);
                } else {
                    auditableFieldHelper.updateCreatedDate(user1, new Date());
                }
            });
            var userTooSoon = userProfileGenerator.createUserProfile(UUID.randomUUID());
            userTooSoon.confirmWelcomeEmailSent(MailConfiguration.ConfirmationMailState.FIRST_SENT);
            auditableFieldHelper.updateCreatedDate(userTooSoon, new Date());
        });
        Mockito.when(mailingListService.sendMailsToProfilesForTemplate(any(), any(), anyLong(), any(), isNull()))
                .thenReturn(CompletableFuture.completedFuture(null));
        newUserWelcomeMailService.sendWelcomeMails();
        txHelper.doInTransaction(() -> {
            assertThat(userProfileRepository.findByUserId(uuidOk.toString()).orElseThrow().isSecondConfirmationMailSent()).isTrue();
            assertThat(userProfileRepository.findAll()).anyMatch(u -> u.userId().equals(uuidOk.toString()) && u.isSecondConfirmationMailSent());
        });
        // template id are customized in application-test.yml
        Mockito.verify(mailingListService).sendMailsToProfilesForTemplate(argThat(c -> c.size() == 1 && c.stream().anyMatch(u -> u.userId().equals(uuidOk.toString()))), isNull(), eq(374L), isNull(), isNull());
        Mockito.verifyNoMoreInteractions(mailingListService);
    }

    private static Location getLocationIn() {
        return Location.builder().citycode("69000")
                .city("Lyon")
                .longitude(42f)
                .latitude(24f)
                .postcode("69000")
                .departmentCode("69")
                .regionName("here")
                .build();
    }
}
