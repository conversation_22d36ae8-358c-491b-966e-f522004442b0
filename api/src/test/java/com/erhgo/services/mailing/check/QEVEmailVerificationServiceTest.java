package com.erhgo.services.mailing.check;

import com.erhgo.domain.dto.EmailVerificationResultDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class QEVEmailVerificationServiceTest {

    private static final String EMAIL_SYNTACTICALLY_CORRECT = "<EMAIL>";

    @Mock
    RestTemplate restTemplate;

    private QEVEmailVerificationService service;

    @BeforeEach
    public void before() {
        service = new QEVEmailVerificationService();
        ReflectionTestUtils.setField(service, "restTemplate", restTemplate);
        ReflectionTestUtils.setField(service, "baseUrl", "http://erhgo.fr");
    }

    @ParameterizedTest
    @ValueSource(strings = {"rejected_email", "invalid_email", "invalid_domain"})
    void invalidEmailKeys(String key) {
        when(restTemplate.getForObject(anyString(), eq(QEVEmailVerificationService.QEVResultDTO.class)))
                .thenReturn(new QEVEmailVerificationService.QEVResultDTO().setReason(key));

        var result = service.verify(EMAIL_SYNTACTICALLY_CORRECT);

        assertThat(result.getEmailStatus()).isEqualTo(EmailVerificationResultDTO.EmailStatus.INVALID_MAIL);
    }

    @ParameterizedTest
    @ValueSource(strings = {"no_connect", "timeout", "unavailable_smtp", "unexpected_error", "no_mx_record"})
    void invalidServerKeys(String key) {
        when(restTemplate.getForObject(anyString(), eq(QEVEmailVerificationService.QEVResultDTO.class)))
                .thenReturn(new QEVEmailVerificationService.QEVResultDTO().setReason(key));

        var result = service.verify(EMAIL_SYNTACTICALLY_CORRECT);

        assertThat(result.getEmailStatus()).isEqualTo(EmailVerificationResultDTO.EmailStatus.INVALID_SERVER);
    }

    @ParameterizedTest
    @ValueSource(strings = {"accepted_email", "exceeded_storage", "temporarily_blocked"})
    void validEmailKeys(String key) {
        when(restTemplate.getForObject(anyString(), eq(QEVEmailVerificationService.QEVResultDTO.class)))
                .thenReturn(new QEVEmailVerificationService.QEVResultDTO().setReason(key));

        var result = service.verify(EMAIL_SYNTACTICALLY_CORRECT);

        assertThat(result.getEmailStatus()).isEqualTo(EmailVerificationResultDTO.EmailStatus.VALID);
    }

    @Test
    void unknownEmailKeys() {
        when(restTemplate.getForObject(anyString(), eq(QEVEmailVerificationService.QEVResultDTO.class)))
                .thenReturn(new QEVEmailVerificationService.QEVResultDTO().setReason("unknown"));

        var result = service.verify(EMAIL_SYNTACTICALLY_CORRECT);

        assertThat(result.getEmailStatus()).isEqualTo(EmailVerificationResultDTO.EmailStatus.UNKNOWN);
    }

    @Test
    void handleClientError() {
        when(restTemplate.getForObject(anyString(), eq(QEVEmailVerificationService.QEVResultDTO.class)))
                .thenThrow(new RestClientException("ko"));

        var result = service.verify(EMAIL_SYNTACTICALLY_CORRECT);

        assertThat(result.getEmailStatus()).isEqualTo(EmailVerificationResultDTO.EmailStatus.VERIFIER_ERROR);
    }


    @Test
    void noCallForNotSyntacticallyCorrectAddress() {
        var result = service.verify("a");

        assertThat(result.getEmailStatus()).isEqualTo(EmailVerificationResultDTO.EmailStatus.INVALID_MAIL);
        verifyNoInteractions(restTemplate);
    }


}
