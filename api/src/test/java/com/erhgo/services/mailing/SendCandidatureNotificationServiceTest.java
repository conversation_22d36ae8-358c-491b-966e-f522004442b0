package com.erhgo.services.mailing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.enums.CandidatureState;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.generators.RecruitmentGenerator;
import com.erhgo.generators.UserProfileGenerator;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import jakarta.persistence.EntityManager;
import org.hibernate.Hibernate;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.Map;
import java.util.Set;

@TestPropertySource(properties = {"sendinblue.templates.candidature-notification=42"})
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
class SendCandidatureNotificationServiceTest extends AbstractIntegrationTest {
    @MockitoBean
    private MailingListService mailingListService;

    @Autowired
    private KeycloakMockService keycloakMockService;

    @Autowired
    private SendCandidatureNotificationEmailService sendCandidatureNotificationEmailService;

    @Autowired
    private CapacityGenerator capacityGenerator;

    @Autowired
    private RecruitmentGenerator recruitmentGenerator;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Autowired
    private EntityManager entityManager;

    @Autowired
    private OrganizationGenerator organizationGenerator;


    @Test
    @WithMockKeycloakUser(roles = {Role.CANDIDATE})
    @ResetDataAfter
    void should_send_notification_mail_to_notifiers() {
        var recruitment = getRecruitment();
        var user = userProfileGenerator.createUserProfile();
        var userEmail = keycloakMockService.getFrontOfficeUserProfile(user.userId()).orElseThrow().getEmail();
        var usersId = Set.of(user.userId());
        var candidature = createCandidature(user, recruitment);

        sendCandidatureNotificationEmailService.sendCandidatureNotificationEmails(usersId, candidature.getId(), candidature.getJobTitle(), candidature.getOrganizationName(), candidature.getCodeOfRecruiter(), recruitment.getId());

        var parameters = Map.of("jobTitle", candidature.getJobTitle(),
                "organizationName", recruitment.getOrganizationName(),
                "organizationCode", candidature.getCodeOfRecruiter(),
                "candidatureId", candidature.getId(),
                "recruitmentId", recruitment.getId());
        Mockito.verify(mailingListService).sendMailsForTemplate(Set.of(userEmail), 42, parameters, null);
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.CANDIDATE})
    @ResetDataAfter
    void should_not_send_notification_mail_to_empty_list() {
        sendCandidatureNotificationEmailService.sendCandidatureNotificationEmails(Set.of(), 0L, "Titre du job", "Nom de l'organisation", "C-01", 1L);

        Mockito.verifyNoInteractions(mailingListService);
    }

    private Recruitment getRecruitment() {
        return txHelper.doInTransaction(() -> {
            var capacity = capacityGenerator.createCapacity("CA-1");
            var recruiter = organizationGenerator.createRecruiter("T-05", AbstractOrganization.OrganizationType.TERRITORIAL);

            var recruitment = recruitmentGenerator.createRecruitmentWithNoRequirement(recruiter, "J-42", capacity);
            Hibernate.initialize(recruitment.getOrganizationName());
            return recruitment;
        });
    }

    private RecruitmentCandidature createCandidature(UserProfile userProfile, Recruitment recruitment) {
        return txHelper.doInTransaction(() -> {
            return RecruitmentCandidature.builder()
                    .id(99L)
                    .recruitment(entityManager.merge(recruitment))
                    .userProfile(entityManager.merge(userProfile))
                    .state(CandidatureState.STARTED)
                    .build();
        });
    }
}
