package com.erhgo.services.mailing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.generators.UserProfileGenerator;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.services.dto.TransactionalBlackListResult;
import com.erhgo.services.dtobuilder.MailingUserDTO;
import com.erhgo.services.keycloak.UserRepresentation;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@EnableRetry
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
@TestPropertySource(properties = {"sendinblue.apiKey=42", "sendinblue.retry-delay=50"})
@Slf4j
class BlackListsSynchronizeServiceTest extends AbstractIntegrationTest {

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Autowired
    private BlackListsSynchronizeService blackListsSynchronizeService;

    @MockitoBean
    private MailingListService mailingListService;

    @Autowired
    private KeycloakMockService keycloakMockService;

    private static final LocalDateTime DATE = LocalDateTime.now().minusSeconds(24 * 3600);

    private List<String> txSenders = List.of("<EMAIL>", "<EMAIL>");

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @ResetDataAfter
    @DisplayName("Given 2 users with ONLY ONE blacklisted for one sender or both" +
            "When I update the blacklist for UserProfile " +
            "Then only one will be updated")
    void updateTransactionalBlacklistedUsers(boolean revivalEmailBlackListed) {
        var user1 = userProfileGenerator.createUserProfileWithTransactionalBlacklisted(false);
        var user2 = userProfileGenerator.createUserProfileWithTransactionalBlacklisted(true);
        var user2_email = keycloakMockService.getFrontOfficeUserProfile(user2.userId()).orElseThrow().getEmail();

        when(mailingListService.getTransactionalBlacklistedEmails(eq(2L), eq(0L), any(LocalDateTime.class)))
                .thenReturn(List.of(new TransactionalBlackListResult(user2_email, txSenders.stream().filter(a -> revivalEmailBlackListed || !a.contains("juliette")).toList())));
        when(mailingListService.getContacts(eq(2L), eq(0L), any(LocalDateTime.class)))
                .thenReturn(List.of(
                        new MailingUserDTO().setEmail(user2_email)
                ));
        blackListsSynchronizeService.updateMailingRelatedData(2L);
        txHelper.doInTransaction(() -> {
            var user1_updated = userProfileRepository.findByUserId(user1.userId()).orElseThrow();
            var user2_updated = userProfileRepository.findByUserId(user2.userId()).orElseThrow();
            assertThat(user1_updated.getJobOfferOptOut()).isFalse();
            assertThat(user2_updated.getJobOfferOptOut()).isTrue();
            assertThat(user1_updated.getSendersOptOut()).isEmpty();
            assertThat(user2_updated.getSendersOptOut()).contains("<EMAIL>");
            assertThat(user2_updated.getSendersOptOut().contains("<EMAIL>")).isEqualTo(revivalEmailBlackListed);
        });

        verify(mailingListService).getTransactionalBlacklistedEmails(eq(2L), eq(0L), any(LocalDateTime.class));
        verify(mailingListService).getContacts(eq(2L), eq(0L), ArgumentMatchers.argThat(this::checkDates));
        verifyNoMoreInteractions(mailingListService);
    }


    @Test
    @ResetDataAfter
    @DisplayName("Given 2 blacklisted users and limit of 2 " +
            "When I update the blacklist for UserProfile " +
            "Then both will be updated AND an additional call with an empty offset will be made")
    void updateTransactionalBlacklistedUsersOutOfBoundOffset() {
        var user1 = userProfileGenerator.createUserProfileWithTransactionalBlacklisted(false);
        var user2 = userProfileGenerator.createUserProfileWithTransactionalBlacklisted(true);
        var user1_email = keycloakMockService.getFrontOfficeUserProfile(user1.userId()).orElseThrow().getEmail();
        var user2_email = keycloakMockService.getFrontOfficeUserProfile(user2.userId()).orElseThrow().getEmail();

        when(mailingListService.getTransactionalBlacklistedEmails(eq(2L), eq(0L), any(LocalDateTime.class))).thenReturn(List.of(
                new TransactionalBlackListResult(user1_email, txSenders),
                new TransactionalBlackListResult(user2_email, txSenders)));
        when(mailingListService.getTransactionalBlacklistedEmails(eq(2L), eq(2L), any(LocalDateTime.class))).thenReturn(Collections.emptyList());
        when(mailingListService.getContacts(eq(2L), eq(0L), any(LocalDateTime.class)))
                .thenReturn(List.of(
                        new MailingUserDTO().setEmail(user1_email).setSmsBlacklisted(false),
                        new MailingUserDTO().setEmail(user2_email).setSmsBlacklisted(true)
                ));
        when(mailingListService.getContacts(eq(2L), eq(2L), any(LocalDateTime.class))).thenReturn(Collections.emptyList());

        blackListsSynchronizeService.updateMailingRelatedData(2L);
        txHelper.doInTransaction(() -> {
            var user1_updated = userProfileRepository.findByUserId(user1.userId()).orElseThrow();
            var user2_updated = userProfileRepository.findByUserId(user2.userId()).orElseThrow();
            assertThat(user1_updated.getJobOfferOptOut()).isTrue();
            assertThat(user2_updated.getJobOfferOptOut()).isTrue();
            assertThat(user1_updated.getSmsBlacklisted()).isFalse();
            assertThat(user2_updated.getSmsBlacklisted()).isTrue();
        });

        verify(mailingListService).getTransactionalBlacklistedEmails(eq(2L), eq(0L), any(LocalDateTime.class));
        verify(mailingListService).getTransactionalBlacklistedEmails(eq(2L), eq(2L), any(LocalDateTime.class));
        verify(mailingListService).getContacts(eq(2L), eq(0L), ArgumentMatchers.argThat(this::checkDates));
        verify(mailingListService).getContacts(eq(2L), eq(2L), ArgumentMatchers.argThat(this::checkDates));
        verifyNoMoreInteractions(mailingListService);
    }

    boolean checkDates(LocalDateTime date) {
        var duration = Duration.between(date, DATE).toHours();
        var res = duration <= 1;
        if (!res)
            log.error("{} is too far from now ({} hours}", date, duration);
        return res;
    }
    @Test
    @ResetDataAfter
    void updateTransactionalBlacklistedUsersToleratesMailDuplication() {
        var u1 = new UserRepresentation().setEmail("a@a").setId(UUID.randomUUID().toString());
        var u2 = new UserRepresentation().setEmail("a@a").setId(UUID.randomUUID().toString());
        keycloakMockService.userprofiles.put(u1.getId(), u1);
        keycloakMockService.userprofiles.put(u2.getId(), u2);

        blackListsSynchronizeService.updateMailingRelatedData(2L);
    }

    @Test
    @ResetDataAfter
    @DisplayName("Given a failing call to mailingListService.updateTransactionalBlacklistedUsers " +
            "When I update the blacklist for UserProfile " +
            "Then no profile is updated")
    void updateTransactionalBlacklistedUsersWhenGenericTechnicalException() {
        var user1 = userProfileGenerator.createUserProfile();
        var user2 = userProfileGenerator.createUserProfileWithTransactionalBlacklisted(true);
        var user3 = userProfileGenerator.createUserProfileWithTransactionalBlacklisted(false);

        when(mailingListService.getTransactionalBlacklistedEmails(eq(2L), eq(0L), any(LocalDateTime.class))).thenThrow(GenericTechnicalException.class);
        GenericTechnicalException expectedException = null;
        try {
            blackListsSynchronizeService.updateMailingRelatedData(2L);
        } catch (GenericTechnicalException e) {
            expectedException = e;
        }
        assertThat(expectedException).isNotNull();

        txHelper.doInTransaction(() -> {
            var user1_updated = userProfileRepository.findByUserId(user1.userId()).orElseThrow();
            var user2_updated = userProfileRepository.findByUserId(user2.userId()).orElseThrow();
            var user3_updated = userProfileRepository.findByUserId(user3.userId()).orElseThrow();
            assertThat(user1_updated.getJobOfferOptOut()).isNull();
            assertThat(user2_updated.getJobOfferOptOut()).isTrue();
            assertThat(user3_updated.getJobOfferOptOut()).isFalse();
        });

        verify(mailingListService).getTransactionalBlacklistedEmails(eq(2L), eq(0L), any(LocalDateTime.class));
        verifyNoMoreInteractions(mailingListService);
    }
}
