package com.erhgo.services.mailing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TransactionTestHelper;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.domain.userprofile.notification.SuspendedRecruitmentNotification;
import com.erhgo.repositories.NotificationRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.assertThat;

class SendCandidatureSuspendedEmailSchedulerTest extends AbstractIntegrationTest {

    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    SendCandidatureSuspendedEmailScheduler scheduler;

    @Autowired
    RecruitmentCandidatureRepository recruitmentCandidatureRepository;

    @Autowired
    NotificationRepository notificationRepository;

    @Autowired
    TransactionTestHelper txHelper;

    @MockitoBean
    MailingListService mailingListService;

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @SneakyThrows
    void should_not_reprocess_candidatures_on_subsequent_runs() {
        var userProfile = applicationContext.getBean(UserProfileMotherObject.class).withEmail("<EMAIL>").buildAndPersist();

        var suspendedRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.UNPUBLISHED)
                .buildAndPersist();

        var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userProfile)
                .withRecruitment(suspendedRecruitment)
                .withState(GlobalCandidatureState.NOT_TREATED_BY_ERHGO)
                .withNotifiedOnClosedRecruitment(false)
                .buildAndPersist();

        Mockito.when(mailingListService.sendMailsToProfilesForTemplate(
                        Mockito.anySet(), Mockito.anyString(), Mockito.anyLong(), Mockito.anyMap(), Mockito.anyMap()))
                .thenReturn(CompletableFuture.completedFuture(java.util.Set.of(userProfile.userId())));

        scheduler.sendCandidatureSuspendedEmails();

        txHelper.doInTransaction(() -> {
            var updatedCandidature = recruitmentCandidatureRepository.findById(candidature.getId()).orElseThrow();
            assertThat(updatedCandidature.isNotifiedOnClosedRecruitment()).isTrue();

            var notifications = notificationRepository.findAll().stream()
                    .filter(n -> n instanceof SuspendedRecruitmentNotification)
                    .toList();

            assertThat(notifications).hasSize(1);
        });

        Mockito.reset(mailingListService);

        scheduler.sendCandidatureSuspendedEmails();

        txHelper.doInTransaction(() -> {
            var notifications = notificationRepository.findAll().stream()
                    .filter(n -> n instanceof SuspendedRecruitmentNotification)
                    .toList();

            assertThat(notifications).hasSize(1);
        });

        Mockito.verifyNoInteractions(mailingListService);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void should_do_nothing_when_no_candidatures_to_notify() {
        scheduler.sendCandidatureSuspendedEmails();

        txHelper.doInTransaction(() -> {
            var notifications = notificationRepository.findAll().stream()
                    .filter(n -> n instanceof SuspendedRecruitmentNotification)
                    .toList();

            assertThat(notifications).isEmpty();
        });

        Mockito.verifyNoInteractions(mailingListService);
    }
}
