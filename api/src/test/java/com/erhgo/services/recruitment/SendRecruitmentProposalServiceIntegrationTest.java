package com.erhgo.services.recruitment;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.ContractType;
import com.erhgo.domain.enums.NotificationType;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserNotificationMotherObject;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.repositories.NotificationRepository;
import com.erhgo.services.mailing.MailingListService;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Stream;

import static org.mockito.ArgumentMatchers.*;

class SendRecruitmentProposalServiceIntegrationTest extends AbstractIntegrationTest {

    @Autowired
    ApplicationContext applicationContext;
    @MockitoBean
    MailingListService mailingListService;
    @Autowired
    SendRecruitmentProposalService sendRecruitmentProposalService;

    @Test
    @ResetDataAfter
    void doNothingWhenNoNotification() {
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class).buildAndPersist();
        applicationContext.getBean(UserNotificationMotherObject.class)
                .withRecruitment(recruitment)
                .withNotificationType(NotificationType.BOTH)
                .withRequiresMailSending(true)
                .buildAndPersist();
        sendRecruitmentProposalService.sendRecruitmentProposal();
        Mockito.verifyNoInteractions(mailingListService);
    }

    @ParameterizedTest
    @CsvSource(value = {
            "DRAFT,false,true,false",
            "PUBLISHED,true,true,false",
            "PUBLISHED,false,false,false",
            "PUBLISHED,false,true,true",
            "UNPUBLISHED,false,true,false",
            "SELECTION,false,true,false",
            "CLOSED,false,true,false"
    })
    @ResetDataAfter
    void sendSingleMailWhenOneNotification(RecruitmentState state, boolean isBlacklisted, boolean requiresMailSending, boolean expectedMailSent) {
        var email = "<EMAIL>";
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withJobTitle("Technicien de maintenance")
                .withLocation(Location.builder().postcode("69004").city("Lyon").build())
                .withRecruiterTitle("McDonalds")
                .withTypeContract(ContractType.CA)
                .withSalaries(15000, 25000)
                .withState(state)
                .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist())
                .buildAndPersist();

        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withEmail(email)
                .withBlacklistedOccupation(isBlacklisted ? recruitment.getErhgoOccupation() : null)
                .withFirstname("Michel")
                .withErhgoClassification("SO-08", true)
                .withErhgoClassification("SO-06", true)
                .buildAndPersist();

        var userProfileNotMailable = applicationContext.getBean(UserProfileMotherObject.class).withBlacklistedOccupation(isBlacklisted ? recruitment.getErhgoOccupation() : null).buildAndPersist();

        Stream.of(userProfile, userProfileNotMailable).forEach(up -> applicationContext.getBean(UserNotificationMotherObject.class)
                .withRecruitment(recruitment)
                .withNotificationType(NotificationType.NONE)
                .withUserProfile(up)
                .withRequiresMailSending(requiresMailSending)
                .buildAndPersist());
        Mockito.when(mailingListService.sendMailsToProfilesForTemplate(anyCollection(), anyString(), anyLong(), isNull(), anyMap())).thenReturn(CompletableFuture.completedFuture(Set.of(userProfile.userId())));
        sendRecruitmentProposalService.sendRecruitmentProposal();
        var offers = Map.of(
                "SOUHAITS", "La formation, la pédagogie, l’apprentissage ; Le soin, l’attention aux autres, la santé",
                "OFFRES_PAR_RECRUTEUR",
                List.of(Map.of(
                        "RECRUTEUR", "McDonalds",
                        "OFFRES", List.of(Map.of(
                                "TITRE", "Technicien de maintenance",
                                "CONTRAT", "Alternance",
                                "VILLE", "Lyon",
                                "CODE_POSTAL", "69004",
                                "LIEN", "https://fo.erhgo.fr/jobs/%s".formatted(recruitment.getCode()),
                                "SALAIRE", "Entre 15 000 € et 25 000 €",
                                "OCCUPATION_ID", recruitment.getErhgoOccupation().getId().toString()
                        ))
                )), "NB_OFFRES", 1);
        var param = Map.of(userProfile.userId(), offers, userProfileNotMailable.userId(), offers);


        Mockito.verify(mailingListService, Mockito.times(expectedMailSent ? 1 : 0)).sendMailsToProfilesForTemplate(
                ArgumentMatchers.argThat(c -> c.contains(userProfile)),
                anyString(),
                eq(543L),
                isNull(),
                ArgumentMatchers.assertArg(a ->
                        {
                            var expected = param.get(userProfile.userId());
                            var actual = a.get(userProfile.userId());
                            Assertions.assertThat(expected).hasSameSizeAs(actual);
                            actual.forEach((key, value) -> Assertions.assertThat(value).isEqualTo(expected.get(key)));
                            expected.forEach((key, value) -> Assertions.assertThat(value).isEqualTo(actual.get(key)));
                        }
                )
        );

        txHelper.doInTransaction(() -> {
            Assertions.assertThat(applicationContext.getBean(NotificationRepository.class).findAll())
                    .hasSize(2)
                    .allMatch(n -> !n.isRequiresMailSending())
                    .allMatch(n -> (expectedMailSent && n.getUserId().equals(userProfile.userId())) ? n.getType() == NotificationType.EMAIL : n.getType() == NotificationType.NONE);
        });
    }

}
