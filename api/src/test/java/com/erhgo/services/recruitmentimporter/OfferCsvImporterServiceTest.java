package com.erhgo.services.recruitmentimporter;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TestUtils;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.ContractType;
import com.erhgo.domain.enums.TypeContractCategory;
import com.erhgo.domain.exceptions.EntityAlreadyExistException;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.CriteriaMotherObject;
import com.erhgo.generators.RecruiterMotherObject;
import com.erhgo.openapi.dto.ErhgoOccupationMinimumInfoDTO;
import com.erhgo.openapi.dto.LocationDTO;
import com.erhgo.repositories.RecruitmentRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.generation.FindBestMatchingOccupationService;
import com.erhgo.services.generation.OccupationGenerator;
import com.erhgo.services.generation.TitleGenerationService;
import com.erhgo.services.generation.dto.FindBestMatchingOccupationArguments;
import com.erhgo.services.generation.dto.NormalizedTitlesResponse;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.notifier.messages.AbstractNotifierMessageDTO;
import com.erhgo.services.search.ErhgoOccupationFinder;
import com.erhgo.services.search.ErhgoOccupationIndexer;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.StreamSupport;

import static com.erhgo.domain.criteria.CriteriaValue.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class OfferCsvImporterServiceTest extends AbstractIntegrationTest {
    @MockitoBean
    FindBestMatchingOccupationService findBestMatchingOccupationService;

    @MockitoBean
    @SuppressWarnings("unused")
    private ErhgoOccupationFinder erhgoOccupationFinder;

    @MockitoBean
    @SuppressWarnings("unused")
    private ErhgoOccupationIndexer erhgoOccupationIndexer;

    @MockitoBean
    Notifier notifier;

    @Autowired
    RecruitmentRepository repository;

    @Autowired
    CapacityGenerator capacityGenerator;

    @Autowired
    OccupationGenerator occupationGenerator;

    @Autowired
    ApplicationContext applicationContext;

    @MockitoBean
    TitleGenerationService titleGenerationService;

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void recruitments_are_created_from_csv_file() throws Exception {

        applicationContext.getBean(CriteriaMotherObject.class).withWorkingTimeCriteria().buildAndPersist();
        applicationContext.getBean(CriteriaMotherObject.class).withRemoteWorkCriteria().buildAndPersist();
        applicationContext.getBean(CriteriaMotherObject.class).withWeekendAndNightWork().buildAndPersist();
        applicationContext.getBean(CriteriaMotherObject.class).withTypeContractCriteria().buildAndPersist();

        var managerId = "MANAGER_ID";
        when(sourcingKeycloakService.getSourcingUserFromEmail("<EMAIL>")).thenReturn(Optional.ofNullable(new UserRepresentation().setId(managerId)));

        var capacity = capacityGenerator.createCapacity("CA1-01");

        var occupationId = UUID.randomUUID();
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withId(occupationId)
                .withCapacities(capacity)
                .withTitle("Garde enfants")
                .buildAndPersist();

        var occupationId2 = UUID.randomUUID();
        var occupation2 = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withId(occupationId2)
                .withCapacities(capacity)
                .withTitle("Responsable Personnel")
                .buildAndPersist();

        when(titleGenerationService.normalizeTitle(any()))
                .thenReturn(new OpenAIResponse<NormalizedTitlesResponse>()
                        .setResult(new NormalizedTitlesResponse(occupation.getTitle(), occupation.getTitle().replace("Garde", "Gardienne d'"))
                        ));

        when(findBestMatchingOccupationService.findSimilarLabel(any(FindBestMatchingOccupationArguments.class)))
                .thenReturn(new ErhgoOccupationMinimumInfoDTO().title(occupation.getTitle()).id(occupationId))
                .thenReturn(new ErhgoOccupationMinimumInfoDTO().title(occupation2.getTitle()).id(occupationId2));

        when(geoService.fetchGeoCoordinates(any(), any())).thenAnswer(invocation -> new LocationDTO()
                .city(invocation.getArgument(0))
                .latitude(48.6231673F)
                .longitude(7.7118232F));

        var recruiter = applicationContext.getBean(RecruiterMotherObject.class)
                .withCode("S-21614")
                .withTitle("Eolia")
                .withExternalUrl("google.com")
                .buildAndPersist();

        var csvFile = Objects.requireNonNull(TestUtils.class.getResourceAsStream("/data/offers.csv")).readAllBytes();
        Mockito.when(sourcingKeycloakService.createUserInSourcingRealm(Mockito.argThat(a -> a.getEmail().equals("<EMAIL>")))).thenThrow(EntityAlreadyExistException.class);
        Mockito.when(sourcingKeycloakService.getSourcingUsersForGroup(any())).thenReturn(List.of(new UserRepresentation().setId(UUID.randomUUID().toString()).setEmail("<EMAIL>")));
        mvc.perform(multipart(realUrl("/recruitment/csv-import"))
                        .file(new MockMultipartFile(
                                "file",
                                "offers.csv",
                                "text/csv",
                                csvFile
                        ))
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {

            var recruitments = repository.findAll();
            assertThat(recruitments).hasSize(4);
            assertThat(recruitments)
                    .filteredOn(r -> r.getRecruiter().getCode().equals(recruiter.getCode()))
                    .hasSize(2)
                    .anySatisfy(r -> {
                        assertThat(r.getBaseSalary()).isEqualTo(20000);
                        assertThat(r.getMaxSalary()).isEqualTo(40000);
                        assertThat(r.getTypeContract()).isEqualTo(ContractType.CDI);
                        assertThat(r.getManagerUserId()).isEqualTo(managerId);
                        assertThat(r.getSourcingUsersIdToNotify()).contains(managerId);

                    })
                    .anySatisfy(r -> {
                        assertThat(r.getBaseSalary()).isNull();
                        assertThat(r.getMaxSalary()).isNull();
                        assertThat(r.getTypeContract()).isEqualTo(ContractType.CDD);
                        assertThat(r.getManagerUserId()).isEqualTo("uuid");
                        assertThat(r.getSourcingUsersIdToNotify()).isEmpty();

                    })
                    .allSatisfy(r -> assertThat(r.getLocation().getCity()).isEqualTo("Lyon"));

            var newClientRecruitments = StreamSupport.stream(recruitments.spliterator(), false).filter(r -> r.getRecruiterTitle().equalsIgnoreCase("nouveau client")).toList();

            assertThat(newClientRecruitments)
                    .hasSize(2)
                    .allSatisfy(r -> assertThat(r.getRecruiterCode()).isEqualTo(newClientRecruitments.getFirst().getRecruiterCode()))
                    .anySatisfy(r -> {
                        assertThat(r.getLocation().getCity()).isEqualTo("Poitiers");
                        assertThat(r.getCriteriaValues()).extracting(CriteriaValue::getCode).containsExactlyInAnyOrder(
                                FULL_TIME_JOB_CODE,
                                getValueCodeForTypeContractCategory(TypeContractCategory.PERMANENT),
                                getValueCodeForPartialRemoteWork()
                        );
                    })
                    .anySatisfy(r -> {
                        assertThat(r.getLocation().getCity()).isEqualTo("Pau");
                        assertThat(r.getCriteriaValues()).extracting(CriteriaValue::getCode).containsExactlyInAnyOrder(
                                PART_TIME_JOB_CODE,
                                getValueCodeForTypeContractCategory(TypeContractCategory.TEMPORARY),
                                getValueCodeForFullRemoteWork(),
                                NIGHT_WORK_CRITERIA_VALUE_CODE,
                                WEEKEND_WORK_CRITERIA_VALUE_CODE
                        );
                        assertThat(r.getWorkingWeeklyTime()).isEqualTo(5);
                        assertThat(r.getWorkContractDuration()).isEqualTo(2);
                        assertThat(r.getBaseSalary()).isEqualTo(3000);
                        assertThat(r.getMaxSalary()).isEqualTo(450000);
                    })
            ;

            var argument = ArgumentCaptor.forClass(AbstractNotifierMessageDTO.class);

            Mockito.verify(sourcingKeycloakService, times(2)).createUserInSourcingRealm(Mockito.assertArg(up -> assertThat(up.getEmail()).containsAnyOf("<EMAIL>", "<EMAIL>")));
            Mockito.verify(notifier, times(11)).sendMessage(argument.capture());
        });
    }
}
