package com.erhgo.services;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.domain.dummy.DummyEntity;
import com.erhgo.repositories.dummy.DummyRepository;
import com.erhgo.repositories.dummy.DummyService;
import org.hamcrest.MatcherAssert;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.internal.verification.VerificationModeFactory;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.beans.HasPropertyWithValue.hasProperty;
import static org.junit.Assert.*;

public class AbstractServiceTest extends AbstractIntegrationTestWithFixtures {

    private final Pageable pageableSortByCode = PageRequest.of(0, 10, Sort.Direction.valueOf("ASC"), "code");
    private final Pageable pageableSortByTitle = PageRequest.of(0, 10, Sort.Direction.valueOf("ASC"), "title");

    @Mock
    private DummyRepository repository;
    @InjectMocks
    private DummyService service;


    private DummyEntity entity = new DummyEntity();

    @Before
    public void setUp() throws Exception {
        entity.setId(1L);
        entity.setCode("D-001");
        entity.setTitle("title");
        entity.setDescription("description");

        Mockito.reset(repository);
        Mockito.when(repository.count()).thenReturn(1l);

        Mockito.when(repository.findOneByCode("D-404")).thenReturn(null);
        Mockito.when(repository.findOneByCode("D-001")).thenReturn(entity);

        Mockito.when(repository.findById(1L)).thenReturn(Optional.of(entity));
        Mockito.when(repository.findById(1L)).thenReturn(Optional.of(entity));
        Mockito.when(repository.findById(null)).thenReturn(Optional.empty());

        Mockito.when(repository.save(Mockito.any(DummyEntity.class))).thenReturn(entity);
        Mockito.when(repository.save(Mockito.any(DummyEntity.class))).thenReturn(entity);

        Mockito.when(repository.findByCodeContainingIgnoreCaseOrTitleContainingIgnoreCase("title", "title", pageableSortByTitle)).thenReturn(new PageImpl<>(Arrays.asList(entity)));
        Mockito.when(repository.findByCodeContainingIgnoreCaseOrTitleContainingIgnoreCase("001", "001", pageableSortByCode)).thenReturn(new PageImpl<>(Arrays.asList(entity)));
        Mockito.when(repository.findByCodeContainingIgnoreCaseOrTitleContainingIgnoreCase("404", "404", pageableSortByTitle)).thenReturn(new PageImpl<>(new ArrayList<>()));
        Mockito.when(repository.findByCodeContainingIgnoreCaseOrTitleContainingIgnoreCase("404", "404", pageableSortByCode)).thenReturn(new PageImpl<>(new ArrayList<>()));

        Mockito.when(repository.findAll(pageableSortByCode)).thenReturn(new PageImpl<>(Arrays.asList(entity), pageableSortByCode, 1));
        ReflectionTestUtils.setField(service, "repository", repository);
    }

    @After
    public void tearDown() throws Exception {
    }

    @Test
    public void should_count_and_return_1l() {
        // when
        final var count = service.count();

        // then
        assertEquals(1l, count.longValue());
    }

    @Test
    public void should_not_find_an_existing_AbstractEntity_by_code_and_return_Null() {
        // then
        assertNull(service.findOneByCode("D-404"));
    }

    @Test
    public void should_find_an_existing_AbstractEntity_by_code_and_return_DummyEntity() {
        // when
        final var result = service.findOneByCode("D-001");

        // then
        assertNotNull(result);
    }

    @Test
    public void should_find_an_existing_AbstractEntity_by_title_and_return_DummyEntity() {
        // when
        Collection<DummyEntity> result = service.findPaginatedAndFilteredByProperty(0, 10, "title", "ASC", "title").getContent();

        // then
        assertNotNull(result);
        assertThat(result.size()).isEqualTo(1);
        MatcherAssert.assertThat(result, contains(hasProperty("code", is("D-001"))));

        Mockito.verify(repository,
                VerificationModeFactory.times(1)).findByCodeContainingIgnoreCaseOrTitleContainingIgnoreCase("title", "title", pageableSortByTitle);
        Mockito.reset(repository);
    }

    @Test
    public void should_find_an_existing_AbstractEntity_by_code_like_and_return_DummyEntity() {
        // when
        Collection<DummyEntity> result = service.findPaginatedAndFilteredByProperty(0, 10, "code", "ASC", "001").getContent();

        // then
        assertNotNull(result);
        assertThat(result.size()).isEqualTo(1);
        MatcherAssert.assertThat(result, contains(hasProperty("code", is("D-001"))));

        Mockito.verify(repository,
                VerificationModeFactory.times(1)).findByCodeContainingIgnoreCaseOrTitleContainingIgnoreCase("001", "001", pageableSortByCode);
        Mockito.reset(repository);
    }

    @Test
    public void should_not_find_an_unexisting_AbstractEntity_by_code_like_and_return_empty_list() {
        // when
        Collection<DummyEntity> result = service.findPaginatedAndFilteredByProperty(0, 10, "code", "ASC", "404").getContent();

        // then
        assertNotNull(result);
        assertThat(result.size()).isEqualTo(0);


        Mockito.verify(repository,
                VerificationModeFactory.times(1)).findByCodeContainingIgnoreCaseOrTitleContainingIgnoreCase("404", "404", pageableSortByCode);
        Mockito.reset(repository);
    }

    @Test
    public void should_not_find_an_unexisting_AbstractEntity_by_title_like_and_return_empty_list() {
        // when
        Collection<DummyEntity> result = service.findPaginatedAndFilteredByProperty(0, 10, "title", "ASC", "404").getContent();

        // then
        assertNotNull(result);
        assertThat(result.size()).isEqualTo(0);


        Mockito.verify(repository,
                VerificationModeFactory.times(1)).findByCodeContainingIgnoreCaseOrTitleContainingIgnoreCase("404", "404", pageableSortByTitle);
        Mockito.reset(repository);
    }

    @Test
    public void should_find_all_existing_AbstractEntity_with_no_code_or_title_and_return_DummyEntity_list() {
        // when
        Collection<DummyEntity> result = service.findPaginatedAndFilteredByProperty(0, 10, "code", "ASC", "").getContent();

        // then
        assertNotNull(result);
        assertThat(result.size()).isEqualTo(1);
        MatcherAssert.assertThat(result, contains(hasProperty("code", is("D-001"))));

        Mockito.verify(repository, VerificationModeFactory.times(1)).findAll(pageableSortByCode);
        Mockito.reset(repository);
    }

    @Test
    public void should_find_all_existing_AbstractEntity_with_null_code_or_title_and_return_DummyEntity_list() {
        // when
        Collection<DummyEntity> result = service.findPaginatedAndFilteredByProperty(0, 10, "code", "ASC", null).getContent();

        // then
        assertNotNull(result);
        assertThat(result.size()).isEqualTo(1);
        MatcherAssert.assertThat(result, contains(hasProperty("code", is("D-001"))));

        Mockito.verify(repository, VerificationModeFactory.times(1)).findAll(pageableSortByCode);
        Mockito.reset(repository);
    }

    @Test
    public void searching_for_an_AbstractEntity_without_a_sortProperty_should_sort_by_code() {
        // when
        Collection<DummyEntity> result = service.findPaginatedAndFilteredByProperty(0, 10, "", "ASC", null).getContent();

        // then
        assertNotNull(result);
        assertThat(result.size()).isEqualTo(1);
        MatcherAssert.assertThat(result, contains(hasProperty("code", is("D-001"))));

        Mockito.verify(repository, VerificationModeFactory.times(1)).findAll(pageableSortByCode);
        Mockito.reset(repository);
    }

    @Test
    public void should_update_an_AbstractEntity() {
        // given
        final var entity = new DummyEntity();
        entity.setId(1L);
        entity.setCode("D-001");
        entity.setTitle("title");
        entity.setDescription("description");

        // when
        var result = service.update(entity);

        // then
        assertNotNull(result);
        assertThat(result.getCode()).isEqualTo("D-001");

        Mockito.verify(repository, VerificationModeFactory.times(1)).save(entity);
        Mockito.reset(repository);
    }

    @Test
    public void should_update_a_new_AbstractEntity() {
        // given
        final var entity = new DummyEntity();
        entity.setId(null);
        entity.setCode("D-001");
        entity.setTitle("title");
        entity.setDescription("description");

        // when
        var result = service.update(entity);

        // then
        assertNotNull(result);
        assertThat(result.getCode()).isEqualTo("D-001");

        Mockito.verify(repository, VerificationModeFactory.times(3)).save(entity);
        Mockito.reset(repository);
    }

    @Test
    public void should_create_a_new_AbstractEntity() {
        // when
        var result = service.create(entity);

        // then
        assertNotNull(result);
        assertThat(result.getCode()).isEqualTo("D-001");

        Mockito.verify(repository, VerificationModeFactory.times(2)).save(result);
        Mockito.reset(repository);
    }

    @Test
    public void should_prepare_filter_for_a_not_null_string() {
        final var filter = "D-001";
        assertNotNull(filter);
        assertEquals("D-001", filter);
    }
}
