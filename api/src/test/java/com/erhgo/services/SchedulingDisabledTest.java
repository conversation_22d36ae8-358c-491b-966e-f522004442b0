package com.erhgo.services;

import com.erhgo.AbstractIntegrationTest;
import org.junit.Test;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.TaskScheduler;

import static org.assertj.core.api.Assertions.assertThat;

public class SchedulingDisabledTest extends AbstractIntegrationTest {

    @Autowired
    ApplicationContext applicationContext;

    @Test
    public void testTaskSchedulerNotPresentOrEmpty() {
        TaskScheduler scheduler = null;
        try {
            scheduler = applicationContext.getBean(TaskScheduler.class);
        } catch (NoSuchBeanDefinitionException e) {
            // no-op
        }
        assertThat(scheduler).isNull();
    }
}
