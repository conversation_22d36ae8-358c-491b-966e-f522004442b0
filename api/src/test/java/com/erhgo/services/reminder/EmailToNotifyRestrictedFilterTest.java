package com.erhgo.services.reminder;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

class EmailToNotifyRestrictedFilterTest {

    @ParameterizedTest
    @ValueSource(strings = {"<EMAIL>", "nùjù**********"})
    @NullAndEmptySource
    void filterGmailAddressShouldReturnFalse(String email) {
        var filter = new EmailToNotifyRestrictedFilter();
        Assertions.assertThat(filter.emailAccepted(email)).isFalse();
    }


    @ParameterizedTest
    @ValueSource(strings = {"<EMAIL>", "pilou-pilou@localhost", "<EMAIL>"})
    void filterErhgoAddressShouldReturnTrue(String email) {
        var filter = new EmailToNotifyRestrictedFilter();
        Assertions.assertThat(filter.emailAccepted(email)).isTrue();
    }


}
