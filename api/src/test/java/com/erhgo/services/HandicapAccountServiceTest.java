package com.erhgo.services;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.exceptions.EntityAlreadyExistException;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.dto.UserKeycloakRepresentation;
import com.erhgo.services.http.RetryableHttpClient;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailingListService;
import com.erhgo.services.userprofile.FilePartProvider;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import lombok.SneakyThrows;
import okhttp3.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class HandicapAccountServiceTest extends AbstractIntegrationTest {

    @Autowired
    private HandicapAccountService handicapAccountService;

    @MockitoBean
    private KeycloakMockService keycloakService;

    @MockitoBean
    private CVDataExtractorService cvDataExtractorService;

    @MockitoBean
    private UserProfileCompetencesExportService userProfileCompetencesExportService;

    @MockitoBean
    private MailingListService mailingListService;

    @MockitoBean
    private RetryableHttpClient handicapRetryableHttpClient;

    private static final String USER_ID = UUID.randomUUID().toString();
    private static final String USER_EMAIL = "<EMAIL>";
    private static final String FILE_URL = "https://example.com/cv.pdf";
    private static final Long WITH_PROFILE_TEMPLATE_ID = 123L;
    private static final Long WITHOUT_PROFILE_TEMPLATE_ID = 456L;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(handicapAccountService, "handicapWithProfileEmailTemplateId", WITH_PROFILE_TEMPLATE_ID);
        ReflectionTestUtils.setField(handicapAccountService, "handicapWithoutProfileEmailTemplateId", WITHOUT_PROFILE_TEMPLATE_ID);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void createAccount_shouldCreateUserAndProcessCV_whenValidJsonPayload() throws IOException {
        String jsonPayload = createValidJsonPayload();
        byte[] fileData = "mock file data".getBytes();

        when(keycloakService.createUserInFrontOfficeRealm(any(UserKeycloakRepresentation.class))).thenReturn(USER_ID);
        when(keycloakService.getFrontOfficeUserProfileWithGroups(USER_ID)).thenReturn(Optional.of(new UserRepresentation().setId(USER_ID)));
        Response mockResponse = createMockResponse(200, fileData);
        when(handicapRetryableHttpClient.executeRequestWithStatusCheck(any(Request.class))).thenReturn(mockResponse);

        CompletableFuture<Void> mockFuture = CompletableFuture.completedFuture(null);
        when(cvDataExtractorService.extractUserExperiencesFromCV(eq(USER_ID), any(FilePartProvider.class))).thenReturn(mockFuture);

        doNothing().when(userProfileCompetencesExportService).getUserProfileForUser(eq(USER_ID), any(ByteArrayOutputStream.class), eq(false), eq(true));

        handicapAccountService.createAccount(jsonPayload);

        verify(keycloakService).createUserInFrontOfficeRealm(any(UserKeycloakRepresentation.class));
        verify(handicapRetryableHttpClient).executeRequestWithStatusCheck(any(Request.class));
        verify(cvDataExtractorService).extractUserExperiencesFromCV(eq(USER_ID), any(FilePartProvider.class));
        verify(userProfileCompetencesExportService).getUserProfileForUser(eq(USER_ID), any(ByteArrayOutputStream.class), eq(false), eq(true));
        verify(mailingListService).sendMailsForTemplate(
                eq(Set.of(USER_EMAIL)),
                eq(WITH_PROFILE_TEMPLATE_ID),
                any(Map.class),
                any(FilePartProvider.class)
        );
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void createAccount_shouldUseExistingUser_whenUserAlreadyExists() throws IOException {
        String jsonPayload = createValidJsonPayload();
        byte[] fileData = "mock file data".getBytes();

        when(keycloakService.createUserInFrontOfficeRealm(any(UserKeycloakRepresentation.class)))
                .thenThrow(new EntityAlreadyExistException(UserKeycloakRepresentation.class, USER_EMAIL));
        when(keycloakService.getFOUserRepresentationByEmail(USER_EMAIL))
                .thenReturn(new com.erhgo.services.keycloak.UserRepresentation().setId(USER_ID));
        when(keycloakService.getFrontOfficeUserProfileWithGroups(USER_ID)).thenReturn(Optional.of(new UserRepresentation().setId(USER_ID)));
        Response mockResponse = createMockResponse(200, fileData);
        when(handicapRetryableHttpClient.executeRequestWithStatusCheck(any(Request.class))).thenReturn(mockResponse);

        CompletableFuture<Void> mockFuture = CompletableFuture.completedFuture(null);
        when(cvDataExtractorService.extractUserExperiencesFromCV(eq(USER_ID), any(FilePartProvider.class))).thenReturn(mockFuture);

        doNothing().when(userProfileCompetencesExportService).getUserProfileForUser(eq(USER_ID), any(ByteArrayOutputStream.class), eq(false), eq(true));

        handicapAccountService.createAccount(jsonPayload);

        verify(keycloakService).getFOUserRepresentationByEmail(USER_EMAIL);
        verify(handicapRetryableHttpClient).executeRequestWithStatusCheck(any(Request.class));
        verify(cvDataExtractorService).extractUserExperiencesFromCV(eq(USER_ID), any(FilePartProvider.class));
        verify(userProfileCompetencesExportService).getUserProfileForUser(eq(USER_ID), any(ByteArrayOutputStream.class), eq(false), eq(true));
        verify(mailingListService).sendMailsForTemplate(
                eq(Set.of(USER_EMAIL)),
                eq(WITH_PROFILE_TEMPLATE_ID),
                any(Map.class),
                any(FilePartProvider.class)
        );
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void createAccount_shouldSendEmailWithoutProfile_whenCVProcessingFails() throws IOException {
        String jsonPayload = createValidJsonPayload();
        byte[] fileData = "mock file data".getBytes();

        when(keycloakService.createUserInFrontOfficeRealm(any(UserKeycloakRepresentation.class))).thenReturn(USER_ID);
        when(keycloakService.getFrontOfficeUserProfileWithGroups(USER_ID)).thenReturn(Optional.of(new UserRepresentation().setId(USER_ID)));
        Response mockResponse = createMockResponse(200, fileData);
        when(handicapRetryableHttpClient.executeRequestWithStatusCheck(any(Request.class))).thenReturn(mockResponse);

        when(cvDataExtractorService.extractUserExperiencesFromCV(eq(USER_ID), any(FilePartProvider.class)))
                .thenThrow(new RuntimeException("CV processing failed"));
        doThrow(new RuntimeException()).when(userProfileCompetencesExportService).getUserProfileForUser(any(), any(), anyBoolean(), anyBoolean());

        handicapAccountService.createAccount(jsonPayload);

        verify(keycloakService).createUserInFrontOfficeRealm(any(UserKeycloakRepresentation.class));
        verify(handicapRetryableHttpClient).executeRequestWithStatusCheck(any(Request.class));
        verify(cvDataExtractorService).extractUserExperiencesFromCV(eq(USER_ID), any(FilePartProvider.class));
        verify(userProfileCompetencesExportService).getUserProfileForUser(anyString(), any(), any(Boolean.class), any(Boolean.class));
        verify(mailingListService).sendMailsForTemplate(
                eq(Set.of(USER_EMAIL)),
                eq(WITHOUT_PROFILE_TEMPLATE_ID),
                any(Map.class),
                eq(null)
        );
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @SneakyThrows
    void createAccount_shouldSendEmailWithoutProfile_whenFileDownloadFails() {
        String jsonPayload = createValidJsonPayload();

        when(keycloakService.createUserInFrontOfficeRealm(any(UserKeycloakRepresentation.class))).thenReturn(USER_ID);
        when(keycloakService.getFrontOfficeUserProfileWithGroups(USER_ID)).thenReturn(Optional.of(new UserRepresentation().setId(USER_ID)));
        Response mockResponse = createMockResponse(404, null);
        when(handicapRetryableHttpClient.executeRequestWithStatusCheck(any(Request.class))).thenReturn(mockResponse);
        doThrow(new RuntimeException()).when(userProfileCompetencesExportService).getUserProfileForUser(any(), any(), anyBoolean(), anyBoolean());

        handicapAccountService.createAccount(jsonPayload);

        verify(keycloakService).createUserInFrontOfficeRealm(any(UserKeycloakRepresentation.class));
        verify(handicapRetryableHttpClient).executeRequestWithStatusCheck(any(Request.class));
        verify(cvDataExtractorService, never()).extractUserExperiencesFromCV(anyString(), any(FilePartProvider.class));
        verify(userProfileCompetencesExportService).getUserProfileForUser(anyString(), any(), any(Boolean.class), any(Boolean.class));
        verify(mailingListService).sendMailsForTemplate(
                eq(Set.of(USER_EMAIL)),
                eq(WITHOUT_PROFILE_TEMPLATE_ID),
                any(Map.class),
                eq(null)
        );
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void createAccount_shouldSendEmailWithoutProfile_whenUserProfileExportFails() throws IOException {
        String jsonPayload = createValidJsonPayload();
        byte[] fileData = "mock file data".getBytes();

        when(keycloakService.createUserInFrontOfficeRealm(any(UserKeycloakRepresentation.class))).thenReturn(USER_ID);
        when(keycloakService.getFrontOfficeUserProfileWithGroups(USER_ID)).thenReturn(Optional.of(new UserRepresentation().setId(USER_ID)));

        Response mockResponse = createMockResponse(200, fileData);
        when(handicapRetryableHttpClient.executeRequestWithStatusCheck(any(Request.class))).thenReturn(mockResponse);

        CompletableFuture<Void> mockFuture = CompletableFuture.completedFuture(null);
        when(cvDataExtractorService.extractUserExperiencesFromCV(eq(USER_ID), any(FilePartProvider.class))).thenReturn(mockFuture);

        Mockito.doThrow(new IOException("Export failed"))
                .when(userProfileCompetencesExportService)
                .getUserProfileForUser(eq(USER_ID), any(ByteArrayOutputStream.class), eq(false), eq(true));

        handicapAccountService.createAccount(jsonPayload);

        verify(keycloakService).createUserInFrontOfficeRealm(any(UserKeycloakRepresentation.class));
        verify(handicapRetryableHttpClient).executeRequestWithStatusCheck(any(Request.class));
        verify(cvDataExtractorService).extractUserExperiencesFromCV(eq(USER_ID), any(FilePartProvider.class));
        verify(userProfileCompetencesExportService).getUserProfileForUser(eq(USER_ID), any(ByteArrayOutputStream.class), eq(false), eq(true));
        verify(mailingListService).sendMailsForTemplate(
                eq(Set.of(USER_EMAIL)),
                eq(WITHOUT_PROFILE_TEMPLATE_ID),
                any(Map.class),
                eq(null)
        );
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void createAccount_shouldThrowException_whenEmailIsMissing() {
        String jsonPayload = """
                {
                  "form_response": {
                    "answers": [
                      {
                        "type": "file_url",
                        "file_url": "https://example.com/cv.pdf"
                      }
                    ]
                  }
                }
                """;

        org.junit.jupiter.api.Assertions.assertThrows(
                com.erhgo.domain.exceptions.GenericTechnicalException.class,
                () -> handicapAccountService.createAccount(jsonPayload)
        );

        verify(keycloakService, never()).createUserInFrontOfficeRealm(any());
        verify(handicapRetryableHttpClient, never()).executeRequestWithStatusCheck(any());
        verify(cvDataExtractorService, never()).extractUserExperiencesFromCV(any(), any());
        verify(mailingListService, never()).sendMailsForTemplate(any(), anyLong(), any(), any());
    }

    @SneakyThrows
    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void createAccount_shouldProcessWithoutCV_whenFileUrlIsMissing() {
        String jsonPayload = """
                {
                  "form_response": {
                    "answers": [
                      {
                        "type": "email",
                        "email": "<EMAIL>"
                      }
                    ]
                  }
                }
                """;

        when(keycloakService.createUserInFrontOfficeRealm(any(UserKeycloakRepresentation.class))).thenReturn(USER_ID);
        when(keycloakService.getFrontOfficeUserProfileWithGroups(USER_ID)).thenReturn(Optional.of(new UserRepresentation().setId(USER_ID)));
        doThrow(new RuntimeException()).when(userProfileCompetencesExportService).getUserProfileForUser(any(), any(), anyBoolean(), anyBoolean());
        handicapAccountService.createAccount(jsonPayload);

        verify(keycloakService).createUserInFrontOfficeRealm(any(UserKeycloakRepresentation.class));
        verify(handicapRetryableHttpClient, never()).executeRequestWithStatusCheck(any());
        verify(cvDataExtractorService, never()).extractUserExperiencesFromCV(any(), any());
        verify(mailingListService).sendMailsForTemplate(
                eq(Set.of(USER_EMAIL)),
                eq(WITHOUT_PROFILE_TEMPLATE_ID),
                any(Map.class),
                eq(null)
        );
    }

    private String createValidJsonPayload() {
        return """
                {
                  "form_response": {
                    "answers": [
                      {
                        "type": "email",
                        "email": "<EMAIL>"
                      },
                      {
                        "type": "file_url",
                        "file_url": "https://example.com/cv.pdf"
                      }
                    ]
                  }
                }
                """;
    }

    private Response createMockResponse(int statusCode, byte[] responseBody) {
        var body = ResponseBody.create(responseBody == null ? new byte[]{} : responseBody, MediaType.parse("application/octet-stream"));

        return new Response.Builder()
                .request(new Request.Builder().url("https://example.com").build())
                .protocol(Protocol.HTTP_1_1)
                .code(statusCode)
                .message(statusCode == 200 ? "OK" : "Not Found")
                .body(body)
                .build();
    }
}
