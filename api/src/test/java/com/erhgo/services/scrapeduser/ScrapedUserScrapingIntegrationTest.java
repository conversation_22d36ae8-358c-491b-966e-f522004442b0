package com.erhgo.services.scrapeduser;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TestUtils;
import com.erhgo.config.PromptConfig;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.exceptions.InvalidScrapingException;
import com.erhgo.domain.userprofile.ScrapedUser;
import com.erhgo.repositories.ScrapedUserRepository;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import com.erhgo.services.generation.dto.UserInfosExtractionResponse;
import com.erhgo.services.scrapeduser.dto.ScrapedCandidate;
import jakarta.persistence.EntityManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

class ScrapedUserScrapingIntegrationTest extends AbstractIntegrationTest {

    private static final String[][] CANDIDATE_DATA = {
            {"CAND001", "John Doe", "Software Developer", "Paris", "http://example.com/cv1.pdf"},
            {"CAND002", "Jane Smith", "Data Analyst", "Lyon", "http://example.com/cv2.pdf"},
            {"CAND003", "Bob Johnson", "Project Manager", "Marseille", "http://example.com/cv3.pdf"},
            {"CAND004", "Alice Brown", "Designer", "Nice", "http://example.com/cv4.pdf"}
    };

    @Autowired
    private ScrapedUserScheduler scrapedUserScheduler;

    @Autowired
    private ScrapedUserService scrapedUserService;

    @Autowired
    private ScrapedUserRepository scrapedUserRepository;

    @MockitoBean
    private AgefiphScraper agefiphScraper;

    @MockitoBean
    private CvProcessingService cvProcessingService;

    @MockitoBean
    GenerationClient generationClient;


    @BeforeEach
    void before() {
        txHelper.doInTransaction(() -> {
            var sql = """
                    INSERT INTO ConfigurableProperty(propertyKey, propertyValue)
                    VALUES ('agefiph.auth.token', 'tokenxxxxx');
                    """;
            applicationContext.getBean(EntityManager.class).createNativeQuery(sql).executeUpdate();
        });
    }

    @Test
    @ResetDataAfter
    void fullScrapingSuccess() throws Exception {
        var candidates = createTestCandidates("CAND001", "CAND002", "CAND003");

        when(agefiphScraper.searchCandidates()).thenReturn(candidates);
        when(agefiphScraper.downloadCv(anyString())).thenReturn(pdfFile());
        var userInfos = createUserInfos();
        when(cvProcessingService.downloadAndExtractCvData(any(ScrapedCandidate.class)))
                .thenReturn(userInfos);

        prepareCVRead();

        scrapedUserService.processCandidates();

        txHelper.doInTransaction(() -> {
            var savedUsers = scrapedUserRepository.findAll();
            assertThat(savedUsers).hasSize(3);

            var user1 = scrapedUserRepository.findByCandidateId("CAND001").orElseThrow();
            assertThat(user1.getEmail()).isEqualTo(userInfos.getEmail());

            var user2 = scrapedUserRepository.findByCandidateId("CAND002").orElseThrow();
            assertThat(user2.getEmail()).isEqualTo(userInfos.getEmail());

            var user3 = scrapedUserRepository.findByCandidateId("CAND003").orElseThrow();
            assertThat(user3.getEmail()).isEqualTo(userInfos.getEmail());
        });

        verify(agefiphScraper).searchCandidates();
    }

    @Test
    @ResetDataAfter
    void scraping_DailyLimitSucces() throws Exception {
        var originalLimit = ReflectionTestUtils.getField(scrapedUserService, "batchCandidateLimit");
        ReflectionTestUtils.setField(scrapedUserService, "batchCandidateLimit", 2L);

        var candidates = createTestCandidates("CAND001", "CAND002", "CAND003");

        when(agefiphScraper.searchCandidates()).thenReturn(candidates);
        when(agefiphScraper.downloadCv(anyString())).thenReturn(pdfFile());
        prepareCVRead();

        try {
            scrapedUserScheduler.processDailyCandidates();

            txHelper.doInTransaction(() -> {
                var savedUsers = scrapedUserRepository.findAll();
                assertThat(savedUsers).hasSize(2);
                assertThat(savedUsers).extracting("candidateId").containsExactlyInAnyOrder("CAND001", "CAND002");
            });
        } finally {
            ReflectionTestUtils.setField(scrapedUserService, "batchCandidateLimit", originalLimit);
        }
    }

    @Test
    @ResetDataAfter
    void scraping_SuccessAndFailureCVDownload() throws Exception {
        var candidates = createTestCandidates("CAND001", "CAND002", "CAND003", "CAND004");
        // Override CAND002 CV link to null for this test
        candidates.get(1).setCvDownloadLink(null);

        when(agefiphScraper.searchCandidates()).thenReturn(candidates);
        var userInfos = createUserInfos();
        when(cvProcessingService.downloadAndExtractCvData(any(ScrapedCandidate.class)))
                .thenReturn(userInfos);
        when(agefiphScraper.downloadCv("http://example.com/cv1.pdf")).thenReturn(pdfFile());
        when(agefiphScraper.downloadCv("http://example.com/cv3.pdf")).thenReturn("Not a PDF".getBytes());
        when(agefiphScraper.downloadCv("http://example.com/cv4.pdf")).thenThrow(new InvalidScrapingException());

        prepareCVRead();

        scrapedUserService.processCandidates();

        txHelper.doInTransaction(() -> {
            var savedUsers = scrapedUserRepository.findAll();
            assertThat(savedUsers).hasSize(3);

            var user1 = scrapedUserRepository.findByCandidateId("CAND001").orElseThrow();
            assertThat(user1.getEmail()).isEqualTo(userInfos.getEmail());
        });

        verify(agefiphScraper).searchCandidates();
        verify(agefiphScraper, never()).downloadCv("http://example.com/cv2.pdf");
    }

    @Test
    @ResetDataAfter
    void scraping_shouldSkipExistCandidates() throws Exception {
        var existingUser = ScrapedUser.builder()
                .uuid(UUID.randomUUID())
                .candidateId("CAND001")
                .firstName("John")
                .lastName("Doe")
                .jobTitle("Software Developer")
                .location("Paris")
                .cvDownloadLink("http://example.com/cv1.pdf")
                .build();
        scrapedUserRepository.save(existingUser);

        var candidates = createTestCandidates("CAND001", "CAND002");

        when(agefiphScraper.searchCandidates()).thenReturn(candidates);
        when(agefiphScraper.downloadCv(anyString())).thenReturn(pdfFile());

        prepareCVRead();

        scrapedUserService.processCandidates();

        txHelper.doInTransaction(() -> {
            var savedUsers = scrapedUserRepository.findAll();
            assertThat(savedUsers).hasSize(2);
            assertThat(savedUsers).extracting("candidateId").containsExactlyInAnyOrder("CAND001", "CAND002");
        });

        verify(agefiphScraper).searchCandidates();
        verify(agefiphScraper, never()).downloadCv("http://example.com/cv1.pdf");
    }

    @Test
    @ResetDataAfter
    void scrapingUserEmptyCandidatesList() throws IOException {
        when(agefiphScraper.searchCandidates()).thenReturn(List.of());
        prepareCVRead();

        scrapedUserService.processCandidates();

        verify(agefiphScraper).searchCandidates();
        verify(agefiphScraper, never()).downloadCv(anyString());

        assertThat(scrapedUserRepository.findAll()).isEmpty();
    }

    private List<ScrapedCandidate> createTestCandidates(String... candidateIds) {
        return Arrays.stream(candidateIds)
                .map(id -> Arrays.stream(CANDIDATE_DATA)
                        .filter(data -> data[0].equals(id))
                        .findFirst()
                        .orElseThrow(() -> new IllegalArgumentException("Unknown candidate ID: " + id)))
                .map(data -> createTestCandidate(data[0], data[1], data[2], data[3], data[4]))
                .toList();
    }

    private ScrapedCandidate createTestCandidate(String candidateId, String name, String jobTitle, String location, String cvDownloadLink) {
        return ScrapedCandidate.builder()
                .candidateId(candidateId)
                .name(name)
                .jobTitle(jobTitle)
                .location(location)
                .cvDownloadLink(cvDownloadLink)
                .profileUrl("http://example.com/profile/" + candidateId)
                .build();
    }

    private byte[] pdfFile() throws IOException {
        return Objects.requireNonNull(TestUtils.class.getResourceAsStream("/data/cv.pdf")).readAllBytes();
    }

    private void prepareCVRead() throws IOException {
        var userInfosJson = TestUtils.toString(this.getClass().getClassLoader().getResourceAsStream("data/userInfosFromCV.json"));
        when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class)))
                .thenReturn(new ChatCompletionResponse(userInfosJson, null));
    }

    private UserInfosExtractionResponse createUserInfos() {
        var userInfos = new UserInfosExtractionResponse();
        userInfos.setEmail("<EMAIL>");
        userInfos.setFirstName("John");
        userInfos.setLastName("Doe");
        return userInfos;
    }
}
