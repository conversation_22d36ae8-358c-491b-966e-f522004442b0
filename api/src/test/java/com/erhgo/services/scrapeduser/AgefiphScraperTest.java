package com.erhgo.services.scrapeduser;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.exceptions.InvalidScrapingException;
import com.erhgo.services.http.RetryableHttpClient;
import jakarta.persistence.EntityManager;
import okhttp3.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.util.ReflectionTestUtils;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;


class AgefiphScraperTest extends AbstractIntegrationTest {

    @Autowired
    private AgefiphScraper agefiphScraper;

    @MockitoBean
    private RetryableHttpClient retryableHttpClient;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(agefiphScraper, "searchUrl", "https://api.agefiph.fr/search");
        ReflectionTestUtils.setField(agefiphScraper, "searchQueryParams", "type=candidate");
        ReflectionTestUtils.setField(agefiphScraper, "baseUrl", "https://api.agefiph.fr");

        txHelper.doInTransaction(() -> {
            var sql = "INSERT INTO ConfigurableProperty(propertyKey, propertyValue) VALUES ('agefiph.auth.token', 'tokenxxxxx')";
            applicationContext.getBean(EntityManager.class).createNativeQuery(sql).executeUpdate();
        });
    }

    @Test
    @ResetDataAfter
    void searchCandidates_ReturnParsedCandidates() {
        var responseJson = "{\"results\": \"<div class=\\\"candidate-li\\\" data-candidate-id=\\\"12345\\\"><a class=\\\"link-ninja\\\">John Doe</a><div>Recherche un poste de <b>Software Developer</b></div><li><i class=\\\"fa-home\\\"></i>Paris</li><a href=\\\"/company/candidate/search/resume/download/12345\\\">CV</a></div>\"}";

        when(retryableHttpClient.executeRequestWithStatusCheck(any(Request.class)))
                .thenReturn(createMockResponse(responseJson));

        var candidates = agefiphScraper.searchCandidates();

        assertThat(candidates).hasSize(1);
        assertThat(candidates.getFirst().getCandidateId()).isEqualTo("12345");
        assertThat(candidates.getFirst().getName()).isEqualTo("John Doe");
        assertThat(candidates.getFirst().getJobTitle()).isEqualTo("Software Developer");
        assertThat(candidates.getFirst().getLocation()).isEqualTo("Paris");
    }

    @Test
    @ResetDataAfter
    void searchCandidates_ThrowExceptionOnServerError() {
        when(retryableHttpClient.executeRequestWithStatusCheck(any()))
                .thenThrow(new RetryableHttpClient.HttpRetryableException("HTTP error: 500"));

        assertThatThrownBy(() -> agefiphScraper.searchCandidates())
                .isInstanceOf(InvalidScrapingException.class);
    }

    @Test
    @ResetDataAfter
    void downloadCv_ReturnPdfBytes() {
        var mockPdfBytes = "Mock PDF content".getBytes();
        when(retryableHttpClient.executeRequestWithStatusCheck(any()))
                .thenReturn(createMockResponse(mockPdfBytes));

        var result = agefiphScraper.downloadCv("http://example.com/cv.pdf");

        assertThat(result).isEqualTo(mockPdfBytes);
    }

    @Test
    @ResetDataAfter
    void downloadCv_ReturnNullOnError() {
        when(retryableHttpClient.executeRequestWithStatusCheck(any()))
                .thenThrow(new RuntimeException("Error"));

        var result = agefiphScraper.downloadCv("http://example.com/cv.pdf");

        assertThat(result).isNull();
    }

    private Response createMockResponse(String body) {
        var responseBody = ResponseBody.create(body, MediaType.parse("application/json"));
        return new Response.Builder()
                .request(new Request.Builder().url("https://example.com").build())
                .protocol(Protocol.HTTP_1_1)
                .code(200)
                .message("OK")
                .body(responseBody)
                .build();
    }

    private Response createMockResponse(byte[] responseBody) {
        var body = ResponseBody.create(responseBody, MediaType.parse("application/octet-stream"));
        return new Response.Builder()
                .request(new Request.Builder().url("https://example.com").build())
                .protocol(Protocol.HTTP_1_1)
                .code(200)
                .message("OK")
                .body(body)
                .build();
    }
}
