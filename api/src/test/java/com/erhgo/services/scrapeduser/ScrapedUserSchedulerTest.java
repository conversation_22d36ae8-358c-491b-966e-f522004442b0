package com.erhgo.services.scrapeduser;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.userprofile.ScrapedUser;
import com.erhgo.domain.userprofile.UserProfileCreationState;
import com.erhgo.repositories.ScrapedUserRepository;
import com.erhgo.services.SecurityService;
import jakarta.persistence.EntityManager;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.time.OffsetDateTime;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class ScrapedUserSchedulerTest extends AbstractIntegrationTest {

    @Autowired
    private ScrapedUserRepository scrapedUserRepository;

    @Autowired
    private ScrapedUserService scrapedUserService;

    @MockitoBean
    private SecurityService securityService;

    @MockitoBean
    private KeycloakMockService keycloakService;


    @Test
    @ResetDataAfter
    void updateOldScrapedUsersToNotInterested_should_update_pending_users_older_than_one_month() {
        var oldUser = createOldPendingUser();
        var recentUser = createRecentPendingUser();

        when(keycloakService.getFOUserRepresentationByEmail(any())).thenReturn(null);

        scrapedUserService.updateOldScrapedUsersToNotInterested();

        txHelper.doInTransaction(() -> {
            var updatedOldUser = scrapedUserRepository.findById(oldUser.getUuid()).orElseThrow();
            var updatedRecentUser = scrapedUserRepository.findById(recentUser.getUuid()).orElseThrow();

            assertThat(updatedOldUser.getCreationState()).isEqualTo(UserProfileCreationState.NOT_INTERESTED);
            assertThat(updatedRecentUser.getCreationState()).isEqualTo(UserProfileCreationState.PENDING);
        });
    }

    private ScrapedUser createOldPendingUser() {
        var user = ScrapedUser.builder()
                .uuid(UUID.randomUUID())
                .candidateId("OLD001")
                .email("<EMAIL>")
                .firstName("Old")
                .lastName("User")
                .creationState(UserProfileCreationState.PENDING)
                .build();
        scrapedUserRepository.save(user);
        var twoMonthsAgo = OffsetDateTime.now().minusMonths(2).toInstant();
        txHelper.doInTransaction(() -> {
            var sql = "UPDATE ScrapedUser s SET s.createdDate = :createdDate WHERE s.uuid = :uuid";
            var query = applicationContext.getBean(EntityManager.class).createNativeQuery(sql);
            query.setParameter("createdDate", java.sql.Timestamp.from(twoMonthsAgo));
            query.setParameter("uuid", user.getUuid());
            query.executeUpdate();
        });
        return user;
    }


    private ScrapedUser createRecentPendingUser() {
        var user = ScrapedUser.builder()
                .uuid(UUID.randomUUID())
                .candidateId("RECENT001")
                .email("<EMAIL>")
                .firstName("Recent")
                .lastName("User")
                .creationState(UserProfileCreationState.PENDING)
                .build();
        scrapedUserRepository.save(user);
        var recentDate = OffsetDateTime.now().minusDays(15).toInstant();
        txHelper.doInTransaction(() -> {
            var sql = "UPDATE ScrapedUser s SET s.createdDate = :createdDate WHERE s.uuid = :uuid";
            var query = applicationContext.getBean(EntityManager.class).createNativeQuery(sql);
            query.setParameter("createdDate", java.sql.Timestamp.from(recentDate));
            query.setParameter("uuid", user.getUuid());
            query.executeUpdate();
        });

        return user;
    }

}
