package com.erhgo.services.denormalization;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.userprofile.CapacityOccurrence;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.CapacityRelatedQuestionGenerator;
import com.erhgo.generators.UserProfileGenerator;
import com.erhgo.openapi.dto.QuestionTypeDTO;
import com.erhgo.openapi.dto.ResponseForCapacityRelatedQuestionDTO;
import com.erhgo.openapi.dto.SaveCapacityRelatedQuestionCommandDTO;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.CapacityRelatedQuestionService;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.UUID;

class UserCapacitiesFromExtraProfessionalHandlerTest extends AbstractIntegrationTest {

    @Autowired
    UserProfileGenerator userProfileGenerator;

    @Autowired
    CapacityGenerator capacityGenerator;

    @Autowired
    private CapacityRelatedQuestionGenerator capacityRelatedQuestionGenerator;

    @Autowired
    private CapacityRelatedQuestionService capacityRelatedQuestionService;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Test
    @DisplayName("Given a user with a response to an EPA when a capacity is added to response then capacity is added to profile")
    @ResetDataAfter
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void capacityAddedToActivity() {
        var capacity = capacityGenerator.createCapacity("CA1-1");
        var newCapacity = capacityGenerator.createCapacity("CA1-2");
        var user = userProfileGenerator.createUserProfile();
        var question = capacityRelatedQuestionGenerator.createQuestionAndAnswerForCandidateWithCapacities(0, user, capacity);

        var command = new SaveCapacityRelatedQuestionCommandDTO()
                .id(question.getId())
                .questionType(QuestionTypeDTO.EXTRAPROFESSIONAL)
                .responses(question.getResponses().stream().map(r -> new ResponseForCapacityRelatedQuestionDTO()
                        .id(r.getId())
                        .capacities(List.of(capacity.getId(), newCapacity.getId()))
                        .title(r.getTitle())).toList())
                .title(question.getTitle());

        capacityRelatedQuestionService.save(command);

        txHelper.doInTransaction(() -> {
            Assertions.assertThat(userProfileRepository.findByUserId(user.userId())
                    .map(UserProfile::capacityOccurrences)
                    .orElseThrow()
                    .stream()
            )
                    .allMatch(c -> c.getOccurrence() == 1)
                    .extracting(CapacityOccurrence::getCapacity)
                    .containsExactlyInAnyOrder(capacity, newCapacity)
            ;
        });
    }

    @Test
    @DisplayName("Given a user with a response to an EPA when a capacity is removed from response then capacity is removed from profile")
    @ResetDataAfter
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void capacityRemovedFromActivity() {
        var capacity = capacityGenerator.createCapacity("CA1-1");
        var capacityToRemove = capacityGenerator.createCapacity("CA1-2");
        var user = userProfileGenerator.createUserProfile();
        var question = capacityRelatedQuestionGenerator.createQuestionAndAnswerForCandidateWithCapacities(0, user, capacity, capacityToRemove);

        var command = new SaveCapacityRelatedQuestionCommandDTO()
                .id(question.getId())
                .questionType(QuestionTypeDTO.EXTRAPROFESSIONAL)
                .responses(question.getResponses().stream().map(r -> new ResponseForCapacityRelatedQuestionDTO()
                        .id(r.getId())
                        .capacities(List.of(capacity.getId()))
                        .title(r.getTitle())).toList())
                .title(question.getTitle());

        capacityRelatedQuestionService.save(command);

        txHelper.doInTransaction(() -> {
            Assertions.assertThat(userProfileRepository.findByUserId(user.userId())
                    .map(UserProfile::capacityOccurrences)
                    .orElseThrow()
                    .stream()
            )
                    .allMatch(c -> c.getOccurrence() == 1)
                    .extracting(CapacityOccurrence::getCapacity)
                    .containsExactlyInAnyOrder(capacity)
            ;
        });
    }

    @Test
    @DisplayName("Given a user with a response to an EPA when response is removed from question then capacity is removed from profile")
    @ResetDataAfter
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void responseIsRemovedFromQuestion() {
        var capacity = capacityGenerator.createCapacity("CA1-1");
        var user = userProfileGenerator.createUserProfile();
        var question = capacityRelatedQuestionGenerator.createQuestionAndAnswerForCandidateWithCapacities(0, user, capacity);

        var command = new SaveCapacityRelatedQuestionCommandDTO()
                .id(question.getId())
                .questionType(QuestionTypeDTO.EXTRAPROFESSIONAL)
                .responses(question.getResponses().stream().map(r -> new ResponseForCapacityRelatedQuestionDTO()
                        .id(UUID.randomUUID())
                        .title(r.getTitle() + " v2")).toList())
                .title(question.getTitle());

        capacityRelatedQuestionService.save(command);

        txHelper.doInTransaction(() -> {
            Assertions.assertThat(userProfileRepository.findByUserId(user.userId())
                    .map(UserProfile::capacityOccurrences)
                    .orElseThrow()
            )
                    .isEmpty();
        });
    }

}
