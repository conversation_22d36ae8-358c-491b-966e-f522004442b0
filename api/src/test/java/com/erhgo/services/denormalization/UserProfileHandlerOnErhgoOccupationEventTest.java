package com.erhgo.services.denormalization;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.userprofile.experience.UserExperience;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.JobActivityLabelGenerator;
import com.erhgo.generators.UserProfileGenerator;
import com.erhgo.openapi.dto.MasteryLevelDTO;
import com.erhgo.openapi.dto.OccupationReferentialEntitiesEditCommandDTO;
import com.erhgo.openapi.dto.UpdateMasteryLevelCommandDTO;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.ErhgoOccupationService;
import org.assertj.core.data.Offset;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class UserProfileHandlerOnErhgoOccupationEventTest extends AbstractIntegrationTest {

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Autowired
    private ErhgoOccupationService erhgoOccupationService;

    @Autowired
    private CapacityGenerator capacityGenerator;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private JobActivityLabelGenerator jobActivityLabelGenerator;

    @Test
    @DisplayName("given an user with an experience on an erhgo occupation, when occupation's level is updated, then user level is updated")
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void notifyLevelUpdated() {

        var user = userProfileGenerator.createUserProfileWithCapacities(MasteryLevel.MIN_LEVEL.getMasteryLevel(), capacityGenerator.createCapacity("CA1-01"));

        var occupationId = user.experiences().stream().map(UserExperience::getErhgoOccupation).map(ErhgoOccupation::getId).findFirst().orElseThrow();

        erhgoOccupationService.updateMasteryLevel(new UpdateMasteryLevelCommandDTO().level(MasteryLevelDTO.EXPERT).id(occupationId));

        assertThat(userProfileRepository.findByUserId(user.userId()).orElseThrow().masteryLevel()).isCloseTo(MasteryLevel.EXPERT.getMasteryLevel(), Offset.offset(0.01f));

    }

    @Test
    @DisplayName("given an user with an experience on an erhgo occupation, when an activity is added on an occupation, then user capacities are updated")
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void notifyActivityAdded() {
        var capacityToAdd = capacityGenerator.createCapacity("CA1-02");
        var user = userProfileGenerator.createUserProfileWithCapacities(MasteryLevel.MIN_LEVEL.getMasteryLevel());

        var occupationId = user.experiences().stream().map(UserExperience::getErhgoOccupation).map(ErhgoOccupation::getId).findFirst().orElseThrow();

        erhgoOccupationService.addActivitiesToOccupation(new OccupationReferentialEntitiesEditCommandDTO().erhgoOccupationId(occupationId).referentialEntityIds(List.of(jobActivityLabelGenerator.createJobActivityLabelWithCapacities(capacityToAdd).getUuid())));

        txHelper.doInTransaction(() -> {
            assertThat(userProfileRepository.findByUserId(user.userId()).orElseThrow().capacityOccurrences()).anyMatch(c -> c.getCapacity().getCode().equals(capacityToAdd.getCode()) && c.getOccurrence() == 1 && c.getRecursiveOccurrence() == 0);
        });
    }

    @Test
    @DisplayName("given an user with an experience on an erhgo occupation, when an activity is removed from occupation, then user capacities are updated")
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void notifyActivityRemoved() {
        var capacityToRemove = capacityGenerator.createCapacity("CA1-02");
        var user = userProfileGenerator.createUserProfileWithCapacities(MasteryLevel.MIN_LEVEL.getMasteryLevel(), capacityToRemove);
        // safeguard
        txHelper.doInTransaction(() -> {
            assertThat(userProfileRepository.findByUserId(user.userId()).orElseThrow().capacityOccurrences()).isNotEmpty();
        });

        var occupationId = user.experiences().stream().map(UserExperience::getErhgoOccupation).map(ErhgoOccupation::getId).findFirst().orElseThrow();

        erhgoOccupationService.removeActivitiesFromOccupation(new OccupationReferentialEntitiesEditCommandDTO().erhgoOccupationId(occupationId).referentialEntityIds(List.of(jobActivityLabelGenerator.createJobActivityLabelWithCapacities(capacityToRemove).getUuid())));


        txHelper.doInTransaction(() -> {
            assertThat(userProfileRepository.findByUserId(user.userId()).orElseThrow().capacityOccurrences()).isEmpty();
        });
    }

    @Test
    @DisplayName("given an user with an experience on an erhgo occupation, when occupation's level is not updated, then user level is not updated")
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void doNotNotifyLevelNotUpdated() {

        var user = userProfileGenerator.createUserProfileWithCapacities(MasteryLevel.MIN_LEVEL.getMasteryLevel(), capacityGenerator.createCapacity("CA1-01"));
        var occupationId = user.experiences().stream().map(UserExperience::getErhgoOccupation).map(ErhgoOccupation::getId).findFirst().orElseThrow();
        float fakeLevel = 4.4f;
        txHelper.doInTransaction(() -> {
            ReflectionTestUtils.setField(user, "masteryLevel", fakeLevel);
            userProfileRepository.save(user);
        });

        erhgoOccupationService.updateMasteryLevel(new UpdateMasteryLevelCommandDTO().level(MasteryLevelDTO.PROFESSIONAL).id(occupationId));

        assertThat(userProfileRepository.findByUserId(user.userId()).orElseThrow().masteryLevel()).isCloseTo(fakeLevel, Offset.offset(0.01f));

    }
}
