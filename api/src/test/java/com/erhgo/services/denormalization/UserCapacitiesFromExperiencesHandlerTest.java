package com.erhgo.services.denormalization;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.UserProfileGenerator;
import com.erhgo.openapi.dto.ActivityLabelDTO;
import com.erhgo.openapi.dto.ActivityTypeDTO;
import com.erhgo.openapi.dto.SaveActivityCommandDTO;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.ActivityService;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;


class UserCapacitiesFromExperiencesHandlerTest extends AbstractIntegrationTest {

    @Autowired
    UserProfileGenerator userProfileGenerator;

    @Autowired
    CapacityGenerator capacityGenerator;

    @Autowired
    ActivityService activityService;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Test
    @DisplayName("Given a user with an activity when a capacity is added to activity then capacity is added to profile")
    @ResetDataAfter
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void capacityAddedToActivity() {
        var capacity = capacityGenerator.createCapacity("CA1-1");
        var newCapacity = capacityGenerator.createCapacity("CA1-2");
        var user = userProfileGenerator.createUserProfileWithCapacities(1, capacity);

        var activityLabel = user.experiences().stream().flatMap(x -> x.getAllActivities(false)
                .stream())
                .findFirst().orElseThrow();


        var command = getSaveActivityCommand(activityLabel, capacity.getCode(), newCapacity.getCode());
        activityService.saveActivity(ActivityTypeDTO.JOB, command);

        txHelper.doInTransaction(() -> {
            Assertions.assertThat(userProfileRepository.findByUserId(user.userId())
                            .orElseThrow()
                            .requiresCapacitiesRefresh()
                    )
                    .isTrue()
            ;
        });
    }

    @Test
    @DisplayName("Given a user with an activity when a capacity with induced capacity is added to activity then capacity is added to profile")
    @ResetDataAfter
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void inducedCapacityAddedToActivity() {
        var inducedCapacity = capacityGenerator.createCapacity("CA1-1");
        var level2Capacity = capacityGenerator.createCapacity("CA2-1", inducedCapacity);
        var toplevelCapacity = capacityGenerator.createCapacity("CA3-1", level2Capacity);

        var user = userProfileGenerator.createUserProfileWithCapacities(1, level2Capacity);

        var activityLabel = user.experiences().stream().flatMap(x -> x.getAllActivities(false)
                .stream())
                .findFirst().orElseThrow();

        var command = getSaveActivityCommand(activityLabel, toplevelCapacity.getCode(), level2Capacity.getCode());

        activityService.saveActivity(ActivityTypeDTO.JOB, command);

        txHelper.doInTransaction(() -> {
            Assertions.assertThat(userProfileRepository.findByUserId(user.userId())
                            .orElseThrow()
                            .requiresCapacitiesRefresh()

                    )
                    .isTrue();
            ;
        });
    }

    @Test
    @DisplayName("Given a user with an activity when a capacity is removed from an activity then capacity is removed from profile")
    @ResetDataAfter
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void capacityRemovedFromActivity() {

        var capacity = capacityGenerator.createCapacity("CA1-1");
        var capacityToRemove = capacityGenerator.createCapacity("CA1-2");
        var user = userProfileGenerator.createUserProfileWithCapacities(1, capacity, capacityToRemove);

        var activityLabel = user.experiences().stream().flatMap(x -> x.getAllActivities(false)
                .stream())
                .findFirst().orElseThrow();

        var command = getSaveActivityCommand(activityLabel, capacity.getCode());

        activityService.saveActivity(ActivityTypeDTO.JOB, command);

        txHelper.doInTransaction(() -> {
            Assertions.assertThat(userProfileRepository.findByUserId(user.userId())
                    .orElseThrow()
                    .requiresCapacitiesRefresh()
            ).isTrue();
        });
    }

    @Test
    @DisplayName("Given a user with an activity when a capacity with induced capacity is removed from an activity then capacity is removed from profile")
    @ResetDataAfter
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void inducedCapacityRemovedFromActivity() {
        var inducedCapacity = capacityGenerator.createCapacity("CA1-1");
        var simpleCapacity = capacityGenerator.createCapacity("CA1-2");
        var level2Capacity = capacityGenerator.createCapacity("CA2-1", inducedCapacity);
        var toplevelCapacity = capacityGenerator.createCapacity("CA3-1", level2Capacity);

        var user = userProfileGenerator.createUserProfileWithCapacities(1, toplevelCapacity, level2Capacity, simpleCapacity);

        var activityLabel = user.experiences().stream().flatMap(x -> x.getAllActivities(false)
                .stream())
                .findFirst().orElseThrow();

        var command = getSaveActivityCommand(activityLabel, toplevelCapacity.getCode());

        activityService.saveActivity(ActivityTypeDTO.JOB, command);

        txHelper.doInTransaction(() -> {
            Assertions.assertThat(userProfileRepository.findByUserId(user.userId())
                            .orElseThrow()
                            .requiresCapacitiesRefresh()
                    )
                    .isTrue();
            ;
        });
    }

    private SaveActivityCommandDTO getSaveActivityCommand(JobActivityLabel activityLabel, String... capacitiesCode) {
        return new SaveActivityCommandDTO()
                .id(activityLabel.getActivity().getUuid())
                .inducedCapacities(List.of(capacitiesCode))
                .labels(List.of(new ActivityLabelDTO().title("OSEF").id(activityLabel.getActivity().getUuid())));
    }
}
