package com.erhgo.services.externaloffer.successfactors;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.externaloffer.ExternalOffer;
import com.erhgo.domain.externaloffer.ExternalOfferEventType;
import com.erhgo.domain.externaloffer.ExternalOfferMotherObject;
import com.erhgo.domain.externaloffer.RecruitmentCreationState;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.utils.EventPublisherUtils;
import com.erhgo.generators.RecruiterMotherObject;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.externaloffer.ExternalOfferRecruitmentService;
import com.erhgo.services.externaloffer.notification.RemoteOfferEvent;
import org.intellij.lang.annotations.Language;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class ExternalOfferControllerIntegrationTest extends AbstractIntegrationTest {


    @Autowired
    private ApplicationContext applicationContext;

    @MockitoBean
    ExternalOfferRecruitmentService externalOfferRecruitmentService;


    @Language("XML")
    private static final String SYSCO_SAMPLE_XML = """
            <?xml version="1.0" encoding="UTF-8"?>
            <job>
                <id>784</id>
                <date>Tue, 02 04 2024 00:00:00 GMT</date>
                <title>Technicien Frigoriste Itinérant (H/F)</title>
                <description>Vous assurez la maintenance préventive et corrective des installations frigorifiques chez nos clients (supermarchés, entrepôts frigorifiques, industries agroalimentaires...).
                Itinérance sur le secteur géographique de la .</description>
                <contract_type>CDI</contract_type>
                <location>Lyon</location>
                <country>FR</country>
                <postal_code>69001</postal_code>
            </job>
            """;

    private static final UUID offerId = UUID.randomUUID();


    private static final String ATS_CODE = "SUCESS_FACTORS";
    private static final String RECRUITER_CODE = "S-202";

    private ExternalOffer createExternalOffer() {
        return applicationContext.getBean(ExternalOfferMotherObject.class)
                .withATSCode(ATS_CODE)
                .withRecruiterCode(RECRUITER_CODE)
                .withUuid(offerId.toString())
                .withOfferTitle("Test Offer Title")
                .withRemoteId("remoteId123")
                .withRemoteModificationDate(LocalDateTime.now())
                .withRawContent(SYSCO_SAMPLE_XML)
                .withLastEventType(ExternalOfferEventType.CREATED)
                .withOfferLocation("Paris")
                .withRecruitmentCreationState(RecruitmentCreationState.MANUAL)
                .buildAndPersist();
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void givenExistingOffers_whenGetList_thenReturnsOkWithOffers() throws Exception {
        var recruiter = applicationContext.getBean(RecruiterMotherObject.class)
                .withCode(RECRUITER_CODE)
                .withDescription("orgaDescr")
                .withOrganizationType(AbstractOrganization.OrganizationType.SOURCING)
                .buildAndPersist();

        createExternalOffer();
        mvc.perform(get("/api/odas/external-offer/organization/list")
                        .param("organizationCode", RECRUITER_CODE)
                        .param("page", "1")
                        .param("size", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.pageIndex").value(10))
                .andExpect(jsonPath("$.pageSize").value(1))
                .andExpect(jsonPath("$.totalNumberOfElements").value(1))
                .andExpect(jsonPath("$.totalPages").value(1));
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void ignoreExternalOfferSuccessfully() throws Exception {
        createExternalOffer();

        mvc.perform(put("/api/odas/external-offer/%s/ignore".formatted(offerId))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var offers = applicationContext.getBean(ExternalOfferRepository.class).findById(offerId);
            assertThat(offers).isPresent();
            assertThat(offers.get().getRecruitmentCreationState()).isEqualTo(RecruitmentCreationState.IGNORE);
        });
    }


    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void integrateExternalOfferSuccessfully() throws Exception {
        createExternalOffer();
        var busBefore = EventPublisherUtils.applicationEventPublisher;
        try {
            var busMock = Mockito.mock(ApplicationEventPublisher.class);
            EventPublisherUtils.resetPublisher(busMock);
            mvc.perform(put("/api/odas/external-offer/%s/integrate".formatted(offerId))
                            .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isNoContent());

            txHelper.doInTransaction(() -> {
                var offer = applicationContext.getBean(ExternalOfferRepository.class).findById(offerId);
                assertThat(offer).isPresent();
                assertThat(offer.get().getRecruitmentCreationState()).isEqualTo(RecruitmentCreationState.PROCESSING);
            });
            Mockito.verify(busMock).publishEvent((Object) ArgumentMatchers.assertArg(a -> {
                assertThat(((RemoteOfferEvent) a).getCreatedOffers()).extracting(ExternalOffer::getUuid).containsExactly(offerId);
            }));
        } finally {
            EventPublisherUtils.resetPublisher(busBefore);
        }

    }
}
