package com.erhgo.services.externaloffer.candidatus;

import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.parsers.GenericJobXmlParser;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.intellij.lang.annotations.Language;
import org.junit.jupiter.api.Test;

import static com.erhgo.TestUtils.assertJsonSerializedObjectMatchesContent;

class CandidatusJobTest {

    @Language("xml")
    String xml = """
             <CandidatusWebAPIResponse>
              <JobsList>
                <Job>
                    <JobId><![CDATA[12345]]></JobId>
                    <JobDate><![CDATA[20241027]]></JobDate>
                    <JobName><![CDATA[Technicien de Maintenance]]></JobName>
                    <JobItem1Id><![CDATA[1]]></JobItem1Id>
                    <JobItem1Name><![CDATA[ERHGO]]></JobItem1Name>
                    <JobItem2Id><![CDATA[2]]></JobItem2Id>
                    <JobItem2Name><![CDATA[CDD]]></JobItem2Name>
                    <JobItem3Id><![CDATA[3]]></JobItem3Id>
                    <JobItem3Name><![CDATA[Temps Plein]]></JobItem3Name>
                    <JobItem4Id><![CDATA[4]]></JobItem4Id>
                    <JobItem4Name><![CDATA[Confirmé]]></JobItem4Name>
                    <JobDescription><![CDATA[Description du poste de Technicien de Maintenance.]]></JobDescription>
                    <JobURL><![CDATA[https://example.com/offre/12345]]></JobURL>
                    <JobEmail><![CDATA[<EMAIL>]]></JobEmail>
                    <JobReference><![CDATA[REF-TECH-MAINT]]></JobReference>
                    <JobLocationPostalCode><![CDATA[75001]]></JobLocationPostalCode>
                    <JobLocationCity><![CDATA[Paris]]></JobLocationCity>
                    <JobGeneric>
                        <organisme><![CDATA[ERHGO]]></organisme>
                        <contrat><![CDATA[CDI]]></contrat>
                        <type_emploi><![CDATA[Temps Plein]]></type_emploi>
                        <duree_cdd><![CDATA[5mois]]></duree_cdd>
                        <experience><![CDATA[5 ans]]></experience>
                        <qualification><![CDATA[BAC+2]]></qualification>
                    </JobGeneric>
                </Job>
              </JobsList>
            </CandidatusWebAPIResponse>
            """;

    @SneakyThrows
    @Test
    void candidatusOfferDeserialization() {
        var candidatusXmlParser = new GenericJobXmlParser<>(CandidatusJob.class, "/CandidatusWebAPIResponse/JobsList/Job", "CANDIDATUS");
        var jobs = candidatusXmlParser.parseJobs(xml, new AtsGetOfferConfig());
        Assertions.assertThat(jobs).hasSize(1);
        assertJsonSerializedObjectMatchesContent(jobs.getContent().getFirst(), "candidatusExternalOffer.json");
    }
}
