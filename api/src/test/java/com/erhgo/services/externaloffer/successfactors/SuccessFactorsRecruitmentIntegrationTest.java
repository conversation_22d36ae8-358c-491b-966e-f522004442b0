package com.erhgo.services.externaloffer.successfactors;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.externaloffer.ExternalOffer;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.RecruiterMotherObject;
import com.erhgo.openapi.dto.AtsOfferSimulatedDTO;
import com.erhgo.openapi.dto.LocationDTO;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.externaloffer.recruiterdispatcher.PerOfferATSConfigurationItem;
import com.erhgo.services.externaloffer.recruiterdispatcher.PerOfferATSConfigurationItemRepository;
import com.erhgo.services.generation.OccupationForLabelGenerationService;
import com.erhgo.services.generation.dto.OccupationForLabelGenerationResult;
import com.erhgo.services.notifier.OccupationCreationSourceType;
import jakarta.persistence.EntityManager;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.intellij.lang.annotations.Language;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class SuccessFactorsRecruitmentIntegrationTest extends AbstractIntegrationTest {

    @Language("XML")
    private static final String ALL_XML = """
            <?xml version="1.0" encoding="UTF-8" ?>
            <source>
                <job>
                    <title>Insurance and Third-Party Support Officer - PMO</title>
                    <date>Mon, 22 02 2021 00:00:00 GMT</date>
                    <id>78029-en_US</id>
                    <apply_url>https://careers.keolis.com/job...</apply_url>
                    <apply_mode>1</apply_mode>
                    <company>Keolis</company>
                    <city>Dubaï</city>
                    <state/>
                    <country>AE</country>
                    <postal_code/>
                    <location>Dubaï, AE</location>
                    <industry/>
                    <category>Engineering and Projects</category>
                    <contract_type/>
                    <description>D1</description>
                </job>
                <job>
                    <title>Apprenti Mécanicien Bus F/H</title>
                    <date>Sat, 02 04 2022 00:00:00 GMT</date>
                    <id>78439-fr_FR</id>
                    <apply_url>https://careers.keolis.com/IDF/job...</apply_url>
                    <apply_mode>1</apply_mode>
                    <company>Keolis</company>
                    <city>Vélizy</city>
                    <state/>
                    <country>FR</country>
                    <postal_code>78140</postal_code>
                    <location>Vélizy, FR, 78140</location>
                    <industry/>
                    <category>Maintenance</category>
                    <contract_type>CDI</contract_type>
                    <description>D2</description>
                </job>
                <job>
                    <title>Apprenti Mecanicien Bus F/H</title>
                    <date>Sun, 02 04 2023 00:00:00 GMT</date>
                    <id>78426-fr_FR</id>
                    <apply_url>https://careers.keolis.com/IDF/job...</apply_url>
                    <apply_mode>1</apply_mode>
                    <company>Keolis</company>
                    <city>Draveil</city>
                    <state/>
                    <country>FR</country>
                    <postal_code>91210</postal_code>
                    <location>Draveil, FR, 91210</location>
                    <industry/>
                    <category>Maintenance</category>
                    <contract_type>CDD</contract_type>
                    <description>D3</description>
                </job>
                <job>
                    <title>Apprenti mécanicien bus (F/H)</title>
                    <date>Wed, 02 04 2025 00:00:00 GMT</date>
                    <id>78440-fr_FR</id>
                    <apply_url>https://careers.keolis.com/IDF/job/...</apply_url>
                    <apply_mode>1</apply_mode>
                    <company>Keolis</company>
                    <city>Goussainville</city>
                    <state/>
                    <country>FR</country>
                    <postal_code>95190</postal_code>
                    <location>Goussainville, FR, 95190</location>
                    <industry/>
                    <category>Maintenance</category>
                    <contract_type>Stage</contract_type>
                    <description>D4</description>
                </job>
                <job>
                    <title>Apprenti mécanicien bus (F/H)</title>
                    <date>Tue, 02 04 2024 00:00:00 GMT</date>
                    <id>78438-fr_FR</id>
                    <apply_url>https://careers.keolis.com/IDF/job...</apply_url>
                    <apply_mode>1</apply_mode>
                    <company>Keolis</company>
                    <city>Bernes sur Oise</city>
                    <state/>
                    <country>FR</country>
                    <postal_code>69620</postal_code>
                    <location>Bernes sur Oise, FR, 69620</location>
                    <industry/>
                    <category>Maintenance</category>
                    <contract_type>Alternance</contract_type>
                    <description>D5</description>
                </job>
                    <job>
                    <title>Offre sans recruteur configuré</title>
                    <date>Wed, 03 04 2024 00:00:00 GMT</date>
                    <id>784399-fr_FR</id>
                    <apply_url>https://careers.keolis.com/IDF/job...</apply_url>
                    <apply_mode>1</apply_mode>
                    <company>Keolis</company>
                    <city>Nowhere</city>
                    <state/>
                    <country>FR</country>
                    <postal_code>42000</postal_code>
                    <location>Bernes sur Oise, FR, 42000</location>
                    <industry/>
                    <category>Maintenance</category>
                    <contract_type>Alternance</contract_type>
                    <description>D5</description>
                </job>
            </source>
            """;

    @MockitoBean
    OccupationForLabelGenerationService occupationForLabelGenerationService;
    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    ExternalOfferRepository externalOfferRepository;

    @SneakyThrows
    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void successFactors_recruitmentCreation() {
        txHelper.doInTransaction(() -> {
            var sql = """
                    INSERT INTO ConfigurableProperty(propertyKey, propertyValue) VALUES
                            ('ats.limit.per-recruiter.SUCCESS_FACTORS--KEOLIS.default', '10')
                    ON DUPLICATE KEY UPDATE propertyValue = VALUES(propertyValue)
                    """;
            applicationContext.getBean(EntityManager.class).createNativeQuery(sql).executeUpdate();
        });

        Mockito.when(geoService.fetchGeoCoordinates(Mockito.anyString(), Mockito.anyString()))
                .thenAnswer(invocation -> new LocationDTO().city("A").longitude(1f).latitude(1f).postcode(List.of(invocation.getArgument(0).toString().split(",")).getLast()));

        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).withCapacities(applicationContext.getBean(CapacityGenerator.class).createCapacity("CA2-422")).buildAndPersist();
        Mockito.when(occupationForLabelGenerationService.createOrUpdateOccupation(ArgumentMatchers.anyString(), ArgumentMatchers.eq(OccupationCreationSourceType.FROM_ATS)))
                .thenReturn(new OccupationForLabelGenerationResult(occupation.getId(), new ArrayList<>(), false));

        applicationContext.getBean(RecruiterMotherObject.class).withCode("S-1")
                .withTitle("Organisation configurée par code postal")
                .withOrganizationType(AbstractOrganization.OrganizationType.SOURCING).buildAndPersist();
        applicationContext.getBean(RecruiterMotherObject.class).withCode("S-2")
                .withTitle("Organisation configurée par département")
                .withOrganizationType(AbstractOrganization.OrganizationType.SOURCING).buildAndPersist();

        persistConfig("78140", "S-1");
        persistConfig("69", "S-2");
        persistConfig("91", "S-2");

        performPost("/external-offer/simulate-ats-offer", new AtsOfferSimulatedDTO().rawFlow(ALL_XML))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var offers = externalOfferRepository.findExternalOffersForConfig("SUCCESS_FACTORS", null, "KEOLIS");
            Assertions.assertThat(offers)
                    .extracting(ExternalOffer::getRemoteId)
                    .containsExactlyInAnyOrder(
                            "78439-fr_FR",
                            "78426-fr_FR"
                    );
            Assertions.assertThat(offers)
                    .extracting(ExternalOffer::getRemoteLastModificationDate)
                    .containsExactlyInAnyOrder(
                            LocalDateTime.of(2023, 04, 02, 0, 0, 0),
                            LocalDateTime.of(2022, 04, 02, 0, 0, 0)
                    )
            ;
            Assertions.assertThat(offers)
                    .extracting(ExternalOffer::getRecruitment)
                    .filteredOn(Objects::nonNull)
                    .hasSize(2)
                    .anyMatch(r -> r.getRecruiterCode().equals("S-1") && r.getLocation().getPostcode().trim().equals("78140"))
                    .anyMatch(r -> r.getRecruiterCode().equals("S-2") && r.getLocation().getPostcode().trim().startsWith("91"));
        });

    }

    private void persistConfig(String number, String recruiterCode) {
        applicationContext.getBean(PerOfferATSConfigurationItemRepository.class).save(new PerOfferATSConfigurationItem(
                UUID.randomUUID(),
                "SUCCESS_FACTORS",
                "KEOLIS",
                "",
                number,
                recruiterCode
        ));
    }

}
