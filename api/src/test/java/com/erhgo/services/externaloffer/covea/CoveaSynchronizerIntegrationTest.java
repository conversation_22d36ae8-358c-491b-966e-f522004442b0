package com.erhgo.services.externaloffer.covea;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TransactionTestHelper;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.externaloffer.ExternalOfferEventType;
import com.erhgo.domain.externaloffer.ExternalOfferMotherObject;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.generators.RecruiterMotherObject;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.externaloffer.ExternalOfferRecruitmentService;
import com.erhgo.services.externaloffer.GenericAtsClient;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class CoveaSynchronizerIntegrationTest extends AbstractIntegrationTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private ExternalOfferRepository externalOfferRepository;

    @Autowired
    private TransactionTestHelper txHelper;

    @MockitoBean
    private GenericAtsClient genericAtsClient;

    @MockitoBean
    private SimpleSftpClient sftpClient;

    @MockitoBean
    private ExternalOfferRecruitmentService externalOfferRecruitmentService;

    private static final String COVEA_CSV_CONTENT = """
            ID de l'offre d'emploi,Offre d'emploi - Type de publication,Date de l'offre d'emploi publiée,Publication - Date d'expiration,Publication de l'offre d'emploi - Tableau des offres d'emploi - Nom,Statut de l'offre d'emploi,Description de l'offre d'emploi - Externe,Type de contrat,Univers,Poste de l'offre d'emploi,Titre du poste de l'offre d'emploi affiché,Code postal de l'offre d'emploi,Département/région de l'offre d'emploi,Emplacement principal,Ville de l'offre d'emploi,Taux d'activité,Contrat en CDD : durée (en mois)
            req35728,Site de carrière,24/03/2025 01:00,24/04/2025 02:00,Recrutement COVEA,Ouvert,"Premier assureur des agents du service public, GMF s'engage au quotidien à accompagner et protéger ses 3,5 millions d'assurés avec comme priorité l'humain au cœur de ses offres et services.",Durée indéterminée  |CDI,Relation et service client & soutien réseaux,Conseiller(ère) développement relation client / Conseil et vente,Conseiller clientèle en agence F/H,12000,AVEYRON,Oui,RODEZ,100.00  |TAU21,
            req35775,Site de carrière,17/02/2025 01:00,17/04/2025 02:00,Recrutement COVEA,Ouvert,"Rejoignez la Direction Secrétariat Général & Réglementaire de la DG GMF, en qualité de Responsable Etudes & Projets réglementaires F/H en CDI.",Durée indéterminée  |CDI,"Projets, conseil et organisation",Responsable conseil / organisation / Conseil et organisation,Responsable Etudes & Projets réglementaires F/H,92300,HAUTS-DE-SEINE,Oui,LEVALLOIS PERRET,100.00  |TAU21,
            """;

    @Test
    @ResetDataAfter
    @SneakyThrows
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void shouldFetchAndCreateExternalOffers() {
        var recruiterCode = "S-21775";
        applicationContext.getBean(RecruiterMotherObject.class)
                .withCode(recruiterCode)
                .withOrganizationType(AbstractOrganization.OrganizationType.SOURCING)
                .buildAndPersist();

        var inputStream = new ByteArrayInputStream(COVEA_CSV_CONTENT.getBytes(StandardCharsets.UTF_8));
        when(sftpClient.streamLatestDailyOfferFile(any(), any(), any())).thenReturn(inputStream);

        applicationContext.getBean(ExternalOfferMotherObject.class)
                .withATSCode("COVEA")
                .withConfigCode("COVEA")
                .withRecruiterCode(recruiterCode)
                .withRemoteId("removed")
                .buildAndPersist()
        ;
        applicationContext.getBean(ExternalOfferServiceProvider.class)
                .getService(
                        new AtsGetOfferConfig()
                                .setAtsCode("COVEA")
                                .setConfigCode("COVEA")
                                .setRecruiterCode(recruiterCode)
                ).fetchAndUpdateOffers();
        txHelper.doInTransaction(() -> {
            var offers = externalOfferRepository.findExternalOffersForConfig("COVEA", recruiterCode, "COVEA");
            Assertions.assertThat(offers)
                    .hasSize(3)
                    .matches(a -> a.stream().filter(b -> b.getLastEventType() == ExternalOfferEventType.CREATED).count() == 2)
                    .allMatch(a -> a.getAtsCode().equals("COVEA"))
                    .anyMatch(a -> a.getRemoteId().equals("req35728") && a.getOfferTitle().equals("Conseiller clientèle en agence F/H - GMF"))
                    .anyMatch(a -> a.getRemoteId().equals("req35775") && a.getOfferTitle().equals("Responsable Etudes & Projets réglementaires F/H - GMF"))
                    .anyMatch(a -> a.getRemoteId().equals("removed") && a.getLastEventType() == ExternalOfferEventType.SUSPENDED)
            ;
        });
    }

}
