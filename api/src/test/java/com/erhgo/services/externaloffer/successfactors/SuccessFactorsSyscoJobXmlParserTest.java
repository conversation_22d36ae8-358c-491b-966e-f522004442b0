package com.erhgo.services.externaloffer.successfactors;

import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import lombok.SneakyThrows;
import org.intellij.lang.annotations.Language;
import org.junit.jupiter.api.Test;

import static com.erhgo.TestUtils.assertJsonSerializedObjectMatchesContent;
import static org.assertj.core.api.Assertions.assertThat;

class SuccessFactorsSyscoJobXmlParserTest {

    private SuccessFactorsJobParser parser = new SuccessFactorsJobParser(new AtsGetOfferConfig()
            .setAtsCode("SUCCESS_FACTORS")
            .setRootPath("/source/jobs/job"));

    @Language("XML")
    private static final String SYSCO_SAMPLE_XML_WITH_MISSING_FIELDS = """
            <?xml version="1.0" encoding="UTF-8"?>
            <source>
                <jobs>
                    <job>
                        <id>784</id>
                        <date>Tue, 02 04 2024 00:00:00 GMT</date>
                        <title>Technicien Frigoriste Itinérant (H/F)</title>
                        <description>Vous assurez la maintenance préventive et corrective des installations frigorifiques chez nos clients (supermarchés, entrepôts frigorifiques, industries agroalimentaires...).
                        Itinérance sur le secteur géographique de la .</description>
                        <contract_type>CDI</contract_type>
                        <location>Lyon</location>
                        <country>FR</country>
                        <postal_code>69001</postal_code>
                    </job>
                    <job>
                        <id>785</id>
                        <date>Tue, 02 04 2024 00:00:00 GMT</date>
                        <title>Responsable de Site Logistique (H/F)</title>
                        <description>Manager une équipe d'une vingtaine de personnes.
                        Organiser et superviser l'ensemble des activités du site (réception, stockage, préparation de commandes, expédition).
                        Garantir l'optimisation des flux et des coûts.</description>
                        <contract_type>CDI</contract_type>
                        <location>Marseille</location>
                        <country>FR</country>
                        <postal_code>13001</postal_code>
                    </job>
                </jobs>
            </source>
            """;


    @SneakyThrows
    @Test
    void syscoOfferDeserialization() {
        // FIXME: This test uses the XML with the "pretend" missing fields
        var jobs = parser.parseJobs(SYSCO_SAMPLE_XML_WITH_MISSING_FIELDS, new AtsGetOfferConfig()).getContent();
        assertJsonSerializedObjectMatchesContent(jobs, "successFactorsSyscoExternalOffers.json");
    }

    @Test
    void parseSyscoJobsWithEmptyJobsTag() {
        var xmlContent = """
                <?xml version="1.0" encoding="UTF-8"?>
                <source>
                    <jobs>
                    </jobs>
                </source>
                """;
        var jobs = parser.parseJobs(xmlContent, new AtsGetOfferConfig());
        assertThat(jobs).isEmpty();
    }

}
