package com.erhgo.services.externaloffer.eolia;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.RecruitmentSendNotificationState;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.externaloffer.ExternalOffer;
import com.erhgo.domain.externaloffer.ExternalOfferEventType;
import com.erhgo.domain.externaloffer.ExternalOfferMotherObject;
import com.erhgo.domain.externaloffer.RecruitmentCreationState;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.sourcing.SourcingInvitationMotherObject;
import com.erhgo.domain.sourcing.SourcingPreferences;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.SourcingPreferencesMotherObject;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.RecruiterMotherObject;
import com.erhgo.openapi.dto.ErhgoOccupationMinimumInfoDTO;
import com.erhgo.openapi.dto.LocationDTO;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.repositories.RecruitmentRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.notification.RemoteOfferErrorDTO;
import com.erhgo.services.externaloffer.notification.RemoteOfferEvent;
import com.erhgo.services.generation.FindBestMatchingOccupationService;
import com.erhgo.services.generation.TitleGenerationService;
import com.erhgo.services.generation.dto.FindBestMatchingOccupationArguments;
import com.erhgo.services.generation.dto.NormalizedTitlesResponse;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.search.ErhgoOccupationFinder;
import com.erhgo.services.search.ErhgoOccupationIndexer;
import com.erhgo.services.search.recruitment.RecruitmentIndexer;
import com.google.common.base.Joiner;
import org.assertj.core.api.Assertions;
import org.intellij.lang.annotations.Language;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.StreamSupport;

import static com.erhgo.domain.utils.EventPublisherUtils.applicationEventPublisher;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

class EoliaRecruitmentServiceTest extends AbstractIntegrationTest {
    private static final String DEFAULT_CITY = "Etupes (25460)";
    private static final String NO_CITY = "unktown";
    private static final String BAD_CITY = "no long lat city";
    public static final String EOLIA_ATS_CODE = "eolia";
    @MockitoBean
    FindBestMatchingOccupationService findBestMatchingOccupationService;

    @MockitoBean
    ErhgoOccupationIndexer erhgoOccupationIndexerMock;
    @MockitoBean
    RecruitmentIndexer recruitmentIndexer;

    @MockitoBean
    TitleGenerationService titleGenerationService;

    @MockitoBean
    @SuppressWarnings("unused")
    private ErhgoOccupationFinder erhgoOccupationFinder;

    @MockitoBean
    Notifier notifier;

    @Autowired
    RecruitmentRepository repository;

    @Autowired
    CapacityGenerator capacityGenerator;

    @Autowired
    ApplicationContext applicationContext;

    @Value("${ats.eolia.fetch[0].recruiterCode}")
    String atsRecruiterCode;

    private static final String XML_HEADER = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";

    @Language("XML")
    private static final String OFFER_TEMPLATE = """
            <job>
                <id>42</id>
                <ref type="varchar(50)">ref202105-%s</ref>
                <bu label="Business Unit" type="varchar(50)">MonEntreprise</bu>
                <nomclient label="Client" type="varchar(255)">MonClient</nomclient>
                <nomposte type="varchar(255)">offre %s - Adjoint responsable pôle chiffrage (H/F) - Etupes (25)</nomposte>
                <nbrecrut type="varchar(50)">1</nbrecrut>
                <datecreation label="Date de création" type="datetime">07/05/2021 12:31:46</datecreation>
                <datemodification label="Date de modification" type="datetime">10/08/2010 14:00:01</datemodification>
                <datedernierepublication label="Date de publication" type="datetime">23/06/2022 14:35:23</datedernierepublication>
                <listerecrut3 label="Package de rémunération brut annuel">Entre 20 et 24k</listerecrut3>
                <saisie2 label="Ville (CP)" type="varchar(255)">%s</saisie2>
            </job>""";

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void created_events_are_fired_and_recruitments_are_updated_accordingly(boolean badCityCase) {
        var capacity = capacityGenerator.createCapacity("CA1-01");

        var occupationId = UUID.randomUUID();
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withId(occupationId)
                .withCapacities(capacity)
                .withTechnical(true)
                .withTitle("Responsable Commercial")
                .qualified(true)
                .buildAndPersist();

        when(titleGenerationService.normalizeTitle(any()))
                .thenReturn(new OpenAIResponse<NormalizedTitlesResponse>()
                        .setResult(new NormalizedTitlesResponse(occupation.getTitle(), occupation.getTitle().replace("Commercial", "commerciale"))
                        ));

        when(findBestMatchingOccupationService.findSimilarLabel(any(FindBestMatchingOccupationArguments.class)))
                .thenReturn(new ErhgoOccupationMinimumInfoDTO().title(occupation.getTitle()).id(occupationId))
                .thenReturn(new ErhgoOccupationMinimumInfoDTO());

        when(erhgoOccupationIndexerMock.updateOccupationIndexation(any())).thenReturn(CompletableFuture.completedFuture(null));

        when(geoService.fetchGeoCoordinates(eq(DEFAULT_CITY), any())).thenReturn(new LocationDTO()
                .city("Etupes")
                .latitude(48.6231673F)
                .longitude(7.7118232F));

        when(geoService.fetchGeoCoordinates(eq(NO_CITY), any())).thenReturn(new LocationDTO());
        when(geoService.fetchGeoCoordinates(eq(BAD_CITY), any())).thenReturn(new LocationDTO().city("city").postcode("89000"));

        var recruiter = applicationContext.getBean(RecruiterMotherObject.class)
                .withCode(atsRecruiterCode)
                .withTitle("Eolia")
                .withExternalUrl("google.com")
                .withOrganizationType(AbstractOrganization.OrganizationType.SOURCING)
                .buildAndPersist();

        applicationContext.getBean(SourcingInvitationMotherObject.class)
                .withGuests(recruiter)
                .buildAndPersist();

        var id = "12";
        var uuid = UUID.fromString("95596cf2-69a8-42fd-95a1-722f7a101614");
        var externalOffer = applicationContext.getBean(ExternalOfferMotherObject.class)
                .withUuid(uuid.toString())
                .withRemoteId(id)
                .withRemoteModificationDate(LocalDateTime.of(2020, 10, 10, 10, 10))
                .withRawContent(XML_HEADER + OFFER_TEMPLATE.formatted(id, id, badCityCase ? BAD_CITY : DEFAULT_CITY))
                .withLastEventType(ExternalOfferEventType.CREATED)
                .withATSCode(EOLIA_ATS_CODE)
                .withOfferLocation(badCityCase ? BAD_CITY : DEFAULT_CITY)
                .withRecruiterCode(atsRecruiterCode)
                .buildAndPersist();

        var externalOfferUnknownLocation = applicationContext.getBean(ExternalOfferMotherObject.class)
                .withUuid(UUID.randomUUID().toString())
                .withRemoteId("remote_no_loc")
                .withRemoteModificationDate(LocalDateTime.of(2020, 10, 10, 10, 10))
                .withRawContent(XML_HEADER + OFFER_TEMPLATE.formatted(id, id, DEFAULT_CITY))
                .withLastEventType(ExternalOfferEventType.CREATED)
                .withATSCode(EOLIA_ATS_CODE)
                .withOfferLocation(NO_CITY)
                .withRecruiterCode(atsRecruiterCode)
                .buildAndPersist();

        var event = new RemoteOfferEvent(List.of(externalOffer, externalOfferUnknownLocation), List.of(), List.of(), List.of(), new AtsGetOfferConfig().setAtsCode(EOLIA_ATS_CODE).setRecruiterCode(atsRecruiterCode));

        applicationEventPublisher.publishEvent(event);

        txHelper.doInTransaction(() -> {
            assertThat(repository.countByRecruitmentProfileJobRecruiter(recruiter)).isEqualTo(1);

            var foundRecruitment = fetchRecruitment(recruiter, RecruitmentState.PUBLISHED);
            assertThat(foundRecruitment.getLocation().getCity()).isEqualTo(badCityCase ? "city" : "Etupes");
            assertThat(foundRecruitment.getExternalOffer().getRecruitmentCreationState()).isEqualTo(RecruitmentCreationState.DONE);
            assertThat(foundRecruitment.getSendNotificationState()).isEqualTo(RecruitmentSendNotificationState.WAITING);
            var notifiedMessage = new AtomicBoolean(false);
            Mockito.verify(notifier, times(3)).sendMessage(ArgumentMatchers.assertArg(a ->
            {
                if (a instanceof RemoteOfferErrorDTO) {
                    notifiedMessage.set(true);
                }
            }));
            assertThat(notifiedMessage).isFalse();
            Mockito.verify(recruitmentIndexer).index(Mockito.assertArg(r -> Assertions.assertThat(r.getExternalOffer().getUuid()).isEqualTo(uuid)));
        });
    }

    private @NotNull Recruitment fetchRecruitment(Recruiter recruiter, RecruitmentState recruitmentState) {
        return StreamSupport.stream(repository.findAll().spliterator(), false).filter(r -> r.getRecruiter().getCode().equals(recruiter.getCode()) && r.getState() == recruitmentState).findFirst().orElseThrow();
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void modified_events_are_fired_and_recruitments_are_updated_accordingly() {
        var occupationDescription = "L'offre n'ayant pas explicitement de description, on prend celle du poste par défaut.";
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withCapacities(capacityGenerator.createCapacity("CA1-01"))
                .withTitle("Responsable Commercial")
                .withDescription(occupationDescription)
                .buildAndPersist();
        var recruiter = applicationContext.getBean(RecruiterMotherObject.class)
                .withCode(atsRecruiterCode)
                .withDescription("orgaDescr")
                .withOrganizationType(AbstractOrganization.OrganizationType.SOURCING)
                .buildAndPersist();
        var id = "12";
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withJobTitle("TITRE JOB AVANT")
                .withTitle("TITRE AVANT")
                .withRecruiter(recruiter)
                .withState(RecruitmentState.PUBLISHED)
                .withSalaries(2000, 2400)
                .withOccupation(occupation)
                .withLocation(Location.builder().city("Malo").latitude(42F).longitude(0.42F).build())
                .buildAndPersist();
        when(geoService.fetchGeoCoordinates(eq(DEFAULT_CITY), any())).thenReturn(new LocationDTO()
                .city("Etupes")
                .latitude(48.6231673F)
                .longitude(7.7118232F));
        var xml = XML_HEADER + OFFER_TEMPLATE.formatted(id, id, DEFAULT_CITY);
        var uuid = UUID.fromString("95596cf2-69a8-42fd-95a1-722f7a101614");
        var externalOffer = applicationContext.getBean(ExternalOfferMotherObject.class)
                .withUuid(uuid.toString())
                .withRemoteId(id)
                .withRemoteModificationDate(LocalDateTime.of(2020, 10, 10, 10, 10))
                .withRawContent(xml)
                .withLastEventType(ExternalOfferEventType.CREATED)
                .withATSCode(EOLIA_ATS_CODE)
                .withRecruitment(recruitment)
                .withRecruiterCode(atsRecruiterCode)
                .withOfferLocation(DEFAULT_CITY)
                .withOfferTitle("Titre interne de l'offre")
                .buildAndPersist();

        var event = new RemoteOfferEvent(List.of(), List.of(), List.of(), List.of(externalOffer), new AtsGetOfferConfig().setAtsCode(EOLIA_ATS_CODE).setRecruiterCode(atsRecruiterCode));

        applicationEventPublisher.publishEvent(event);

        txHelper.doInTransaction(() -> {
            var foundRecruitment = applicationContext.getBean(ExternalOfferRepository.class).findExternalOffersForConfig(EOLIA_ATS_CODE, atsRecruiterCode, null).getFirst().getRecruitment();
            assertThat(repository.countByRecruitmentProfileJobRecruiter(recruiter)).isEqualTo(1);
            assertThat(foundRecruitment.getLocation().getCity()).isEqualTo("Etupes");
            assertThat(foundRecruitment.getState()).isEqualTo(RecruitmentState.PUBLISHED);
            assertThat(foundRecruitment.getOrganizationOrRecruiterDescription()).isEqualTo("orgaDescr");
            assertThat(foundRecruitment.getBaseSalary()).isEqualTo(20_000);
            assertThat(foundRecruitment.getMaxSalary()).isEqualTo(24_000);
            assertThat(foundRecruitment.getDescription()).isEqualTo(occupation.getDescription());
            var notifiedMessage = new AtomicBoolean(false);

            Mockito.verify(notifier, times(3)).sendMessage(ArgumentMatchers.assertArg(a ->
            {
                if (a instanceof RemoteOfferErrorDTO) {
                    notifiedMessage.set(true);
                    assertThat(a.getText()).isEqualTo("Modification automatique d'<https://sourcing.erhgo.fr/#/edit-recruitment?externalOfferId=95596cf2-69a8-42fd-95a1-722f7a101614|un recrutement> depuis ATS eolia pour l'offre offre 12 - Adjoint responsable pôle chiffrage (H/F) - Etupes (25)");
                }
            }));
            assertThat(notifiedMessage).isFalse();
            Mockito.verify(recruitmentIndexer).index(Mockito.assertArg(r -> Assertions.assertThat(r.getId()).isEqualTo(recruitment.getId())));
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void suspension_events_are_fired_and_recruitments_are_updated_accordingly() {
        var capacity = capacityGenerator.createCapacity("CA1-01");

        var occupationId = UUID.randomUUID();
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withId(occupationId)
                .withCapacities(capacity)
                .withTitle("Responsable Commercial")
                .buildAndPersist();

        when(findBestMatchingOccupationService.findSimilarLabel(any(FindBestMatchingOccupationArguments.class)))
                .thenReturn(new ErhgoOccupationMinimumInfoDTO().title(occupation.getTitle()).id(occupationId))
                .thenReturn(new ErhgoOccupationMinimumInfoDTO());

        when(erhgoOccupationIndexerMock.updateOccupationIndexation(any())).thenReturn(CompletableFuture.completedFuture(null));

        when(geoService.fetchGeoCoordinates(any(), any())).thenReturn(new LocationDTO()
                .city("Etupes")
                .latitude(48.6231673F)
                .longitude(7.7118232F));

        var recruiter = applicationContext.getBean(RecruiterMotherObject.class)
                .withCode(atsRecruiterCode)
                .withTitle("Eolia")
                .withExternalUrl("google.com")
                .withOrganizationType(AbstractOrganization.OrganizationType.SOURCING)
                .buildAndPersist();

        var id = "12";
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withJobTitle("Responsable commercial")
                .withTitle("Responsable commercial Eolia")
                .withRecruiter(recruiter)
                .withState(RecruitmentState.PUBLISHED)
                .withSalaries(20000, 24000)
                .withOccupation(occupation)
                .withLocation(Location.builder().city("Etupes").latitude(48.6231673F).longitude(7.7118232F).build())
                .buildAndPersist();

        var xml = XML_HEADER + OFFER_TEMPLATE.formatted(id, id, DEFAULT_CITY);
        var uuid = UUID.fromString("95596cf2-69a8-42fd-95a1-722f7a101614");
        var externalOffer = applicationContext.getBean(ExternalOfferMotherObject.class)
                .withUuid(uuid.toString())
                .withRemoteId(id)
                .withRemoteModificationDate(LocalDateTime.of(2020, 10, 10, 10, 10))
                .withRawContent(xml)
                .withLastEventType(ExternalOfferEventType.CREATED)
                .withATSCode(EOLIA_ATS_CODE)
                .withRecruitment(recruitment)
                .withOfferTitle("Titre interne de l'offre")
                .buildAndPersist();

        var event = new RemoteOfferEvent(List.of(), List.of(externalOffer), List.of(), List.of(), new AtsGetOfferConfig().setAtsCode(EOLIA_ATS_CODE).setRecruiterCode(atsRecruiterCode));

        applicationEventPublisher.publishEvent(event);

        txHelper.doInTransaction(() -> {
            assertThat(repository.countByRecruitmentProfileJobRecruiter(recruiter)).isEqualTo(1);

            var foundRecruitment = fetchRecruitment(recruiter, RecruitmentState.SELECTION);
            assertThat(foundRecruitment.getLocation().getCity()).isEqualTo("Etupes");

            var unexpectedMessage = new AtomicBoolean(false);
            Mockito.verify(notifier, times(1)).sendMessage(ArgumentMatchers.assertArg(a ->
            {
                if (a instanceof RemoteOfferErrorDTO) {
                    unexpectedMessage.set(true);
                }
            }));

            assertThat(unexpectedMessage).isFalse();
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void ignored_offers_should_not_create_recruitments() {
        var capacity = capacityGenerator.createCapacity("CA1-01");

        var occupationId = UUID.randomUUID();
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withId(occupationId)
                .withCapacities(capacity)
                .withTitle("Responsable Commercial")
                .qualified(true)
                .buildAndPersist();

        when(findBestMatchingOccupationService.findSimilarLabel(any(FindBestMatchingOccupationArguments.class)))
                .thenReturn(new ErhgoOccupationMinimumInfoDTO().title(occupation.getTitle()).id(occupationId));

        when(titleGenerationService.normalizeTitle(any()))
                .thenReturn(new OpenAIResponse<NormalizedTitlesResponse>()
                        .setResult(new NormalizedTitlesResponse(occupation.getTitle(), occupation.getTitle().replace("Commercial", "commerciale"))
                        ));

        var recruiter = applicationContext.getBean(RecruiterMotherObject.class).withCode(atsRecruiterCode).buildAndPersist();

        var id = "ignored-offer-123";
        var uuid = UUID.randomUUID();
        var externalOffer = applicationContext.getBean(ExternalOfferMotherObject.class)
                .withUuid(uuid.toString())
                .withRemoteId(id)
                .withRemoteModificationDate(LocalDateTime.of(2020, 10, 10, 10, 10))
                .withLastEventType(ExternalOfferEventType.CREATED)
                .withATSCode(EOLIA_ATS_CODE)
                .withOfferLocation(DEFAULT_CITY)
                .withRecruiterCode(atsRecruiterCode)
                .withRecruitmentCreationState(RecruitmentCreationState.IGNORE)
                .buildAndPersist();

        var event = new RemoteOfferEvent(List.of(externalOffer), List.of(), List.of(), List.of(), new AtsGetOfferConfig().setAtsCode(EOLIA_ATS_CODE).setRecruiterCode(atsRecruiterCode));

        applicationEventPublisher.publishEvent(event);

        txHelper.doInTransaction(() -> {
            assertThat(repository.countByRecruitmentProfileJobRecruiter(recruiter)).isZero();

            var updatedOffer = applicationContext.getBean(ExternalOfferRepository.class).findById(uuid).orElseThrow();
            assertThat(updatedOffer.getRecruitmentCreationState()).isEqualTo(RecruitmentCreationState.IGNORE);
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void republish_events_are_fired_and_recruitments_are_updated_accordingly() {
        var capacity = capacityGenerator.createCapacity("CA1-01");

        var occupationId = UUID.randomUUID();
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withId(occupationId)
                .withCapacities(capacity)
                .withTitle("Responsable Commercial")
                .buildAndPersist();

        when(findBestMatchingOccupationService.findSimilarLabel(any(FindBestMatchingOccupationArguments.class)))
                .thenReturn(new ErhgoOccupationMinimumInfoDTO().title(occupation.getTitle()).id(occupationId))
                .thenReturn(new ErhgoOccupationMinimumInfoDTO());

        when(erhgoOccupationIndexerMock.updateOccupationIndexation(any())).thenReturn(CompletableFuture.completedFuture(null));

        when(geoService.fetchGeoCoordinates(any(), any())).thenReturn(new LocationDTO()
                .city("Etupes")
                .latitude(48.6231673F)
                .longitude(7.7118232F));

        var recruiter = applicationContext.getBean(RecruiterMotherObject.class)
                .withCode(atsRecruiterCode)
                .withTitle("Eolia")
                .withExternalUrl("google.com")
                .withOrganizationType(AbstractOrganization.OrganizationType.SOURCING)
                .buildAndPersist();

        var id = "12";
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withJobTitle("Responsable commercial")
                .withTitle("Responsable commercial Eolia")
                .withRecruiter(recruiter)
                .withState(RecruitmentState.SELECTION)
                .withSalaries(20000, 24000)
                .withOccupation(occupation)
                .withPublicationEndDate(OffsetDateTime.now())
                .withLocation(Location.builder().city("Etupes").latitude(48.6231673F).longitude(7.7118232F).build())
                .buildAndPersist();

        var xml = XML_HEADER + OFFER_TEMPLATE.formatted(id, id, DEFAULT_CITY);
        var uuid = UUID.fromString("95596cf2-69a8-42fd-95a1-722f7a101614");
        var externalOffer = applicationContext.getBean(ExternalOfferMotherObject.class)
                .withUuid(uuid.toString())
                .withRemoteId(id)
                .withRemoteModificationDate(LocalDateTime.of(2020, 10, 10, 10, 10))
                .withRawContent(xml)
                .withLastEventType(ExternalOfferEventType.SUSPENDED)
                .withRecruitment(recruitment)
                .buildAndPersist();

        var event = new RemoteOfferEvent(List.of(), List.of(), List.of(externalOffer), List.of(), new AtsGetOfferConfig().setAtsCode(EOLIA_ATS_CODE).setRecruiterCode(atsRecruiterCode));

        applicationEventPublisher.publishEvent(event);

        txHelper.doInTransaction(() -> {
            assertThat(repository.countByRecruitmentProfileJobRecruiter(recruiter)).isEqualTo(1);

            var foundRecruitment = fetchRecruitment(recruiter, RecruitmentState.PUBLISHED);
            assertThat(foundRecruitment.getLocation().getCity()).isEqualTo("Etupes");
            assertThat(foundRecruitment.getPublicationEndDate()).isNull();
            Mockito.verify(notifier, times(3)).sendMessage(any());
        });
    }


    @ParameterizedTest
    @ValueSource(booleans = {false, true})
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void created_events_are_fired_and_recruitments_are_created_with_manager(boolean withUserNames) {
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withTitle("Responsable Commercial")
                .withCapacities(capacityGenerator.createCapacity("CA1-01"))
                .qualified(true)
                .buildAndPersist();
        when(titleGenerationService.normalizeTitle(any()))
                .thenReturn(new OpenAIResponse<NormalizedTitlesResponse>()
                        .setResult(new NormalizedTitlesResponse(occupation.getTitle(), occupation.getTitle().replace("Commercial", "commerciale"))
                        ));
        when(findBestMatchingOccupationService.findSimilarLabel(any()))
                .thenReturn(new ErhgoOccupationMinimumInfoDTO().title(occupation.getTitle()).id(occupation.getId()));
        when(erhgoOccupationIndexerMock.updateOccupationIndexation(any())).thenReturn(CompletableFuture.completedFuture(null));
        when(geoService.fetchGeoCoordinates(eq(DEFAULT_CITY), any())).thenReturn(new LocationDTO()
                .city("Etupes")
                .latitude(48.6231673F)
                .longitude(7.7118232F));

        var recruiter = applicationContext.getBean(RecruiterMotherObject.class)
                .withCode(atsRecruiterCode)
                .withOrganizationType(AbstractOrganization.OrganizationType.SOURCING)
                .buildAndPersist();

        applicationContext.getBean(SourcingInvitationMotherObject.class)
                .withGuests(recruiter)
                .buildAndPersist();

        var id = "12";
        var disabledUser = new UserRepresentation().setId("2").setEmail("<EMAIL>").setFirstName("firstDisabledUser").setEnabled(false);
        var notNotifiableUser = new UserRepresentation().setId("1").setEmail("<EMAIL>").setFirstName("bossRefusingNotif").setEnabled(true);
        var expectedManagerWithNotifEnabled = new UserRepresentation().setId("3").setEmail("<EMAIL>").setFirstName("existentNotifiable").setEnabled(true).setCreatedTimestamp(1_000_000L);
        var otherDefaultManagerButTooLate = new UserRepresentation().setId("32").setEmail("<EMAIL>").setFirstName("tooLate").setEnabled(true).setCreatedTimestamp(2 * expectedManagerWithNotifEnabled.getCreatedTimestamp());
        var otherUserWithoutPref = new UserRepresentation().setId("4").setEmail("<EMAIL>").setLastName("anotherDummyUser").setEnabled(true);
        var userWithFirstAndLastName = new UserRepresentation().setId("5").setEmail("<EMAIL>").setFirstName(" MyFirstName ").setLastName(" MyLastName ").setEnabled(true);
        var userWithEmail = new UserRepresentation().setId("6").setEmail("<EMAIL>").setEnabled(true);

        var unknownUser = "unknown user";
        var expectedManagerFirstName = expectedManagerWithNotifEnabled.getFirstName() + " user";
        var expectedManagerDuplicate = expectedManagerWithNotifEnabled.getFirstName() + " same user";
        var userWithFirstAndLastNameFullname = "MyFirstName MyLastName";
        applicationContext.getBean(SourcingPreferencesMotherObject.class).withUserId(disabledUser.getId()).withMailFrequency(SourcingPreferences.MailFrequency.IMMEDIATELY).buildAndPersist();
        applicationContext.getBean(SourcingPreferencesMotherObject.class).withUserId(expectedManagerWithNotifEnabled.getId()).withMailFrequency(SourcingPreferences.MailFrequency.DAILY).buildAndPersist();
        applicationContext.getBean(SourcingPreferencesMotherObject.class).withUserId(notNotifiableUser.getId()).withMailFrequency(SourcingPreferences.MailFrequency.NEVER).buildAndPersist();

        var allUsers = List.of(otherUserWithoutPref, expectedManagerWithNotifEnabled, otherDefaultManagerButTooLate, userWithFirstAndLastName, disabledUser, userWithEmail, notNotifiableUser);

        when(sourcingKeycloakService.getEnabledSourcingUsersForGroup(anyString()))
                .thenReturn(allUsers);
        when(sourcingKeycloakService.getSourcingUserFromEmail(anyString())).thenAnswer(invocation -> allUsers.stream().filter(mail -> mail.getEmail().equals(invocation.getArgument(0))).findFirst());
        var offerId = UUID.fromString("95596cf2-69a8-42fd-95a1-722f7a101614");
        var externalOffer = applicationContext.getBean(ExternalOfferMotherObject.class)
                .withUuid(offerId.toString())
                .withRemoteId(id)
                .withRemoteModificationDate(LocalDateTime.of(2020, 10, 10, 10, 10))
                .withRawContent(XML_HEADER + OFFER_TEMPLATE.formatted(id, id, DEFAULT_CITY))
                .withLastEventType(ExternalOfferEventType.CREATED)
                .withATSCode(EOLIA_ATS_CODE)
                .withOfferLocation(DEFAULT_CITY)
                .withRecruiterCode(atsRecruiterCode)
                .withRelatedUserNames(withUserNames ? Joiner.on(',').join(disabledUser.getFirstName(), expectedManagerFirstName, otherDefaultManagerButTooLate.getFirstName(), notNotifiableUser.getFirstName(), expectedManagerDuplicate, userWithEmail.getEmail(), otherUserWithoutPref.getLastName() + " ploum ", unknownUser, userWithFirstAndLastNameFullname) : "")
                .buildAndPersist();

        var event = new RemoteOfferEvent(List.of(externalOffer), List.of(), List.of(), List.of(), new AtsGetOfferConfig().setAtsCode(EOLIA_ATS_CODE).setRecruiterCode(atsRecruiterCode));

        applicationEventPublisher.publishEvent(event);

        txHelper.doInTransaction(() -> {
            var foundRecruitment = applicationContext.getBean(ExternalOfferRepository.class).findById(offerId).map(ExternalOffer::getRecruitment).orElseThrow();
            assertThat(foundRecruitment.getManagerUserId()).isEqualTo(expectedManagerWithNotifEnabled.getId());
            assertThat(foundRecruitment.getSourcingUsersIdToNotify())
                    .containsExactlyInAnyOrder(withUserNames ? new String[]{
                            notNotifiableUser.getId(),
                            otherDefaultManagerButTooLate.getId(),
                            expectedManagerWithNotifEnabled.getId(),
                            otherUserWithoutPref.getId(),
                            userWithFirstAndLastName.getId(),
                            userWithEmail.getId()}
                            : new String[]{expectedManagerWithNotifEnabled.getId()});
        });
    }
}
