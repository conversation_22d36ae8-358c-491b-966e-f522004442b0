package com.erhgo.services.externaloffer.taleez;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.externaloffer.ExternalOffer;
import com.erhgo.domain.externaloffer.ExternalOfferEventType;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.CriteriaMotherObject;
import com.erhgo.generators.RecruiterMotherObject;
import com.erhgo.openapi.dto.LocationDTO;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.externaloffer.GenericAtsClient;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import com.erhgo.services.generation.OccupationForLabelGenerationService;
import com.erhgo.services.generation.dto.OccupationForLabelGenerationResult;
import com.erhgo.services.notifier.OccupationCreationSourceType;
import jakarta.persistence.EntityManager;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.intellij.lang.annotations.Language;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.ArrayList;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

class TaleezExternalOfferIntegrationTest extends AbstractIntegrationTest {

    @Language("XML")
    private static final String ALL_XML = """
            <?xml version="1.0" encoding="UTF-8" standalone="no"?>
            <jobs>
                <job>
                  <title><![CDATA[Opérateur de production - Chimie - 3X8 - F/H]]></title>
                  <url><![CDATA[https://taleez.com/apply/operateur-de-production-chimie-3x8-f-h-chasse-sur-rhone-condat-cdi-2/applying?utm_source=jenesuisPASunCV]]></url>
                  <referencenumber><![CDATA[khtr9e]]></referencenumber>
                  <companydescription><![CDATA[<p>Condat, groupe international indépendant d'origine française<br></p>]]></companydescription>
                  <description><![CDATA[<p>Vous êtes motivé pour apprendre</p>]]></description>
                  <profile><![CDATA[<p>·&nbsp;<strong>Excellent savoir être : le respect des valeurs de notre groupe est essentiel.</p>]]></profile>
                  <date><![CDATA[2023-02-23]]></date>
                  <jobtype><![CDATA[CDD]]></jobtype>
                  <contractDuration><![CDATA[ 5 mois ]]></contractDuration>
                  <fulltime><![CDATA[false]]></fulltime>
                  <weeklyHours><![CDATA[ 23.0 ]]></weeklyHours>
                  <city><![CDATA[Chasse-sur-Rhône]]></city>
                  <coordinate><![CDATA[45.579489;4.813859]]></coordinate>
                  <postalCode><![CDATA[38670]]></postalCode>
                  <company><![CDATA[CONDAT]]></company>
                  <country><![CDATA[France]]></country>
                  <dateFirstPublished><![CDATA[2024-08-27]]></dateFirstPublished>
                  <status><![CDATA[PUBLISHED]]></status>
                  <companyId><![CDATA[CONDAT]]></companyId>
                </job>
                <job>
                  <title><![CDATA[Recruteur indépendant (F/H/X)]]></title>
                  <url><![CDATA[https://taleez.com/apply/recruteur-euse-independant-e-achil/applying?utm_source=jenesuisPASunCV]]></url>
                  <referencenumber><![CDATA[27j7t2e]]></referencenumber>
                  <companydescription><![CDATA[<p><a href="https://www.achil.io/" target="_blank" rel="ugc">Achil</a> ambitionne </p>]]></companydescription>
                  <description><![CDATA[<p><strong><u>Ton rôle</u></strong><br>Se lancer en tant que recruteur freelance est un</ul>]]></description>
                  <profile><![CDATA[<ul><li><strong>Tu as envie d’entreprendre 🦸</strong> : concernant les motivations, chaque</p>]]></profile>
                  <date><![CDATA[2023-02-23]]></date>
                  <jobtype><![CDATA[CDI]]></jobtype>
                  <contractDuration/>
                  <fulltime><![CDATA[true]]></fulltime>
                  <weeklyHours/>
                  <city><![CDATA[Télétravail]]></city>
                  <coordinate/>
                  <postalCode/>
                  <company><![CDATA[Achil]]></company>
                  <country><![CDATA[France]]></country>
                  <dateFirstPublished><![CDATA[2022-06-01]]></dateFirstPublished>
                  <status><![CDATA[PUBLISHED]]></status>
                  <companyId><![CDATA[ACHIL]]></companyId>
                </job>
                <job>
                  <title><![CDATA[Offre sur recruteur inconnu, à ignorer, log error]]></title>
                  <url><![CDATA[https://taleez.com/apply/recruteur-euse-independant-e-achil/applying?utm_source=jenesuisPASunCV]]></url>
                  <referencenumber><![CDATA[42]]></referencenumber>
                  <companydescription><![CDATA[<p><a href="https://www.achil.io/" target="_blank" rel="ugc">Unknown</a> ambitionne </p>]]></companydescription>
                  <description><![CDATA[<p><strong><u>Ton rôle</u></strong><br>Se lancer en tant que recruteur freelance est un</ul>]]></description>
                  <profile><![CDATA[<ul><li><strong>Tu as envie d’entreprendre 🦸</strong> : concernant les motivations, chaque</p>]]></profile>
                  <date><![CDATA[2023-02-23]]></date>
                  <jobtype><![CDATA[CDI]]></jobtype>
                  <contractDuration/>
                  <fulltime><![CDATA[true]]></fulltime>
                  <weeklyHours/>
                  <city><![CDATA[Télétravail]]></city>
                  <coordinate/>
                  <postalCode/>
                  <company><![CDATA[Unknown]]></company>
                  <country><![CDATA[France]]></country>
                  <dateFirstPublished><![CDATA[2022-06-01]]></dateFirstPublished>
                  <status><![CDATA[PUBLISHED]]></status>
                  <companyId><![CDATA[UNKNOWN]]></companyId>
                </job>
            </jobs>
            """;

    @MockitoBean
    OccupationForLabelGenerationService occupationForLabelGenerationService;
    @MockitoBean
    GenericAtsClient genericAtsClient;

    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    ExternalOfferRepository externalOfferRepository;

    @SneakyThrows
    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void taleez_modification() {
        taleez_recruitmentCreationDoesNotCallBAN();
        var modifiedTitle = "modifiedTitle";
        var xmlsUpdated = ALL_XML.replace("Recruteur indépendant (F/H/X)", modifiedTitle).replaceAll("2023-02-23", "2023-02-24");
        Mockito.reset(genericAtsClient);
        when(genericAtsClient.fetch(any(), any())).thenReturn(xmlsUpdated);
        applicationContext.getBean(ExternalOfferServiceProvider.class).getAllServicesScheduledHavingSecondPass().stream().filter(s -> s.getAtsCode().contains("taleez")).findFirst().orElseThrow().fetchAndUpdateOffers();
        txHelper.doInTransaction(() -> {
            var offers = externalOfferRepository.findExternalOffersForConfig("taleez", null, null);
            Assertions.assertThat(offers)
                    .anyMatch(a -> a.getLastEventType() == ExternalOfferEventType.MODIFIED && a.getRemoteId().equals("27j7t2e") && a.getRecruitment().getJobTitle().equals(modifiedTitle))
                    .anyMatch(a -> a.getRemoteId().equals("27j7t2e") && a.getComputedRecruiterCode().equals("S-21746") && a.getOfferRecruiterCode().equalsIgnoreCase("achil") && a.getRecruitment().getRecruiter().getCode().equals("S-21746"))
                    .anyMatch(a -> a.getLastEventType() == ExternalOfferEventType.CREATED && a.getComputedRecruiterCode().equals("S-21690") && a.getOfferRecruiterCode().equalsIgnoreCase("condat") && a.getRecruitment().getRecruiter().getCode().equals("S-21690"))
                    .allMatch(a -> a.getConfigCode() == null)
                    .extracting(ExternalOffer::getRemoteId)
                    .containsExactlyInAnyOrder("27j7t2e", "khtr9e")
            ;
        });

    }

    @SneakyThrows
    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void taleez_recruitmentCreationDoesNotCallBAN() {
        applicationContext.getBean(CriteriaMotherObject.class).withValueCodes(CriteriaValue.getValueCodeForFullRemoteWork()).buildAndPersist();
        var condat = applicationContext.getBean(RecruiterMotherObject.class).withCode("S-21690")
                .withOrganizationType(AbstractOrganization.OrganizationType.SOURCING)
                .buildAndPersist();
        var achil = applicationContext.getBean(RecruiterMotherObject.class).withCode("S-21746")
                .withOrganizationType(AbstractOrganization.OrganizationType.SOURCING)
                .buildAndPersist();
        when(genericAtsClient.fetch(any(), any())).thenReturn(ALL_XML);
        applicationContext.getBean(ExternalOfferServiceProvider.class).getAllServicesScheduledHavingSecondPass().stream().filter(s -> s.getAtsCode().contains("taleez")).findFirst().orElseThrow().fetchAndUpdateOffers();
        txHelper.doInTransaction(() -> {
            var offers = externalOfferRepository.findExternalOffersForConfig("taleez", null, null);
            Assertions.assertThat(offers)
                    .anyMatch(a -> a.getRemoteId().equals("27j7t2e") && a.getRecruitment().getCriteriaValues().stream().anyMatch(c -> c.getCode().equals(CriteriaValue.getValueCodeForFullRemoteWork())))
                    .anyMatch(a -> a.getRemoteId().equals("27j7t2e") && a.getComputedRecruiterCode().equals("S-21746") && a.getOfferRecruiterCode().equalsIgnoreCase("achil") && a.getRecruitment().getRecruiter().getId().equals(achil.getId()))
                    .anyMatch(a -> a.getComputedRecruiterCode().equals("S-21690") && a.getOfferRecruiterCode().equalsIgnoreCase("condat") && a.getRecruitment().getRecruiter().getId().equals(condat.getId()))
                    .allMatch(a -> a.getConfigCode() == null)
                    .extracting(ExternalOffer::getRemoteId)
                    .containsExactlyInAnyOrder("27j7t2e", "khtr9e")
            ;
        });

        Mockito.verify(geoService, Mockito.times(1)).fetchGeoCoordinates(Mockito.eq(TaleezJob.DEFAULT_LOCATION_INDICATION_WHEN_REMOTE_WORK), Mockito.anyString());

    }


    @BeforeEach
    void prepareConf() {
        txHelper.doInTransaction(() -> {
            var sql = """
                    INSERT INTO PerOfferATSConfigurationItem(id, atsCode, configCode, remoteRecruiterCode, locationCode, recruiterCode)
                    VALUES  (uuidToBin('19c6cd67-d4ff-40aa-97de-72e57a942daa'), 'taleez', '', 'CONDAT', '', 'S-21690'),
                            (uuidToBin('19c6cd67-d4ff-40aa-97de-72e57a942dab'), 'taleez', '', 'ACHIL', '', 'S-21746');
                    """;
            applicationContext.getBean(EntityManager.class).createNativeQuery(sql).executeUpdate();
        });
        when(geoService.fetchGeoCoordinates(anyString(), anyString())).thenReturn(new LocationDTO().city("lyon").postcode("69000"));
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).withCapacities(applicationContext.getBean(CapacityGenerator.class).createCapacity("CA2-422")).buildAndPersist();
        when(occupationForLabelGenerationService.createOrUpdateOccupation(ArgumentMatchers.anyString(), ArgumentMatchers.eq(OccupationCreationSourceType.FROM_ATS)))
                .thenReturn(new OccupationForLabelGenerationResult(occupation.getId(), new ArrayList<>(), false));

    }

    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ParameterizedTest
    @ValueSource(strings = {"default", "taleez", "taleez--ACHIL"})
    void taleez_recruitmentCreationBlocked(String suffix) {
        txHelper.doInTransaction(() -> {
            var sql = """
                    INSERT INTO ConfigurableProperty(propertyKey,propertyValue) VALUES
                                                                ('ats.per-aura-in-out.out.%s', 0),
                                                                ('ats.per-aura-in-out.in.%s', 0);

                    """.formatted(suffix, suffix);
            applicationContext.getBean(EntityManager.class).createNativeQuery(sql).executeUpdate();
        });
        applicationContext.getBean(RecruiterMotherObject.class).withCode("S-21690")
                .withOrganizationType(AbstractOrganization.OrganizationType.SOURCING)
                .buildAndPersist();
        applicationContext.getBean(RecruiterMotherObject.class).withCode("S-21746")
                .withOrganizationType(AbstractOrganization.OrganizationType.SOURCING)
                .buildAndPersist();
        when(genericAtsClient.fetch(any(), any())).thenReturn(ALL_XML);

        applicationContext.getBean(ExternalOfferServiceProvider.class).getAllServicesScheduledHavingSecondPass().stream().filter(s -> s.getAtsCode().contains("taleez")).findFirst().orElseThrow().fetchAndUpdateOffers();

        txHelper.doInTransaction(() -> {
            var offers = externalOfferRepository.findExternalOffersForConfig("taleez", null, null);
            if (suffix.contains("ACHIL")) {
                Assertions.assertThat(offers)
                        .anyMatch(a -> a.getComputedRecruiterCode().equals("S-21690") && a.getOfferRecruiterCode().equalsIgnoreCase("condat") && a.getRecruitment() != null)
                        .extracting(ExternalOffer::getRemoteId)
                        .containsExactly("khtr9e")
                ;
            } else {
                Assertions.assertThat(offers).isEmpty();
            }

        });
    }
}
