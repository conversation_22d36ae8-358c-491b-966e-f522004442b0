package com.erhgo.services.externaloffer.softy;

import com.erhgo.openapi.dto.TypeContractCategoryDTO;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.parsers.GenericJobXmlParser;
import org.assertj.core.data.Offset;
import org.intellij.lang.annotations.Language;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.assertj.core.api.Assertions.assertThat;

class SoftyOfferServiceUnitTest {
    @Language("XML")
    static final String XML = """
            <job>
                <date><![CDATA[2024-07-08 15:26:32]]></date>
                <title><![CDATA[AGENT DE MAINTENANCE - H/F]]></title>
                <id>127954_0</id>
                <contract_type><![CDATA[Apprentissage]]></contract_type>
                <description><![CDATA[M TAG assure la gestion de l'ensemble du réseau bus et tramway de l'agglomération grenobloise, sous Délégation de Service Public, depuis plus de 40 ans. Acteur incontournable de la mobilité sur le bassin grenoblois, M TAG agit au quotidien pour offrir un réseau performant, sûr et accessible à tous les usagers de son agglomération. Pour cela, elle emploie près de 1 500 personnes pour gérer au quotidien le réseau TAG. Avec près de 150 métiers différents, M TAG propose une large diversité de profils et une aventure humaine ancrée dans la proximité.]]></description>
                <description_html><![CDATA[<p>M TAG assure la gestion de l'ensemble du réseau bus et tramway de l'agglomération grenobloise, sous Délégation de Service Public, depuis plus de 40 ans.</p> <p>Acteur incontournable de la mobilité sur le bassin grenoblois, M TAG agit au quotidien pour offrir un réseau performant, sûr et accessible à tous les usagers de son agglomération. Pour cela, elle emploie près de 1 500 personnes pour gérer au quotidien le réseau TAG.</p> <p>Avec près de 150 métiers différents, M TAG propose une large diversité de profils et une aventure humaine ancrée dans la proximité.</p>]]></description_html>
                <position><![CDATA[Rattaché(e) à la Direction Technique et au sein de l'équipe de maintenance des installations fixes composée d'une dizaine de personnes, vous contribuez aux missions suivantes : Opérer la maintenance préventive et curative des équipements du dépôt, tels que les ponts roulants, les colonnes élévatrices, les ponts à colonnes, les barrières et les portes automatiques, les machines à laver les véhicules ou encore les cabines de peinture, la climatisation et le chauffage…. Intervenir et assister les agents de maintenance sur du dépannage 1er et 2eme niveau d'installation électrique et mécanique Exécuter, dans le respect des règlements et procédures établis, les différents travaux confiés par le tuteur Contribuer à la réalisation de devis pour des travaux de maintenance ou des travaux neufs Participer au suivi des travaux des sous-traitants Participer au suivi de l'activité et d'analyse des plans de maintenance sur GMAO Consulter les fournisseurs]]></position>
                <position_html><![CDATA[<p>Rattaché(e) à la Direction Technique et au sein de l’équipe de maintenance des installations fixes composée d’une dizaine de personnes, vous contribuez aux missions suivantes :</p> <p> </p> <ul> <li>Opérer la maintenance préventive et curative des équipements du dépôt, tels que les ponts roulants, les colonnes élévatrices, les ponts à colonnes, les barrières et les portes automatiques, les machines à laver les véhicules ou encore les cabines de peinture, la climatisation et le chauffage….</li> <li>Intervenir et assister les agents de maintenance sur du dépannage 1<sup>er </sup>et 2eme niveau d’installation électrique et mécanique</li> <li>Exécuter, dans le respect des règlements et procédures établis, les différents travaux confiés par le tuteur</li> <li>Contribuer à la réalisation de devis pour des travaux de maintenance ou des travaux neufs</li> <li>Participer au suivi des travaux des sous-traitants</li> <li>Participer au suivi de l’activité et d’analyse des plans de maintenance sur GMAO</li> <li>Consulter les fournisseurs</li> </ul>]]></position_html>
                <profile><![CDATA[Vous suivez une formation BTS Maintenance des Systèmes option A Systèmes de production. Vous avez des connaissances en câblage, relayage, électromécanique, lecture et écriture de schémas électriques. Des connaissances en pneumatique, hydraulique et logiciel GMAO serait un plus. Vous êtes rigoureux avec de bonnes capacités d'analyse et de discernement, de réflexion et de méthode. Vous aimez travailler en équipe et vous avez le goût du terrain. Le poste proposé est en contrat d'apprentissage ou contrat de professionnalisation à durée déterminée pour une période de  24 mois. Une durée de travail hebdomadaire de travail de 35h00 heures, du lundi au vendredi, basée à Eybens]]></profile>
                <profile_html><![CDATA[<p>Vous suivez une formation BTS Maintenance des Systèmes option A Systèmes de production.</p> <p>Vous avez des connaissances en câblage, relayage, électromécanique, lecture et écriture de schémas électriques.</p> <p>Des connaissances en pneumatique, hydraulique et logiciel GMAO serait un plus.</p> <p>Vous êtes rigoureux avec de bonnes capacités d’analyse et de discernement, de réflexion et de méthode. Vous aimez travailler en équipe et vous avez le goût du terrain.</p> <p> </p> <p>Le poste proposé est en contrat d'apprentissage ou contrat de professionnalisation à durée déterminée pour une période de  24 mois. Une durée de travail hebdomadaire de travail de 35h00 heures, du lundi au vendredi, basée à Eybens</p>]]></profile_html>
                <location><![CDATA[Eybens]]></location>
                <postcode><![CDATA[38320]]></postcode>
                %s
                <region><![CDATA[Auvergne-Rhône-Alpes]]></region>
                <country><![CDATA[France]]></country>
                <url><![CDATA[https://m-tag.softy.pro/offre/127954]]></url>
                <salary><![CDATA[A définir suivant profil]]></salary>
                <salary_type><![CDATA[no]]></salary_type>
                <rome><![CDATA[I1203]]></rome>
                <experience><![CDATA[Débutant accepté]]></experience>
                <education><![CDATA[Bac]]></education>
                <work_time><![CDATA[Temps plein]]></work_time>
                <benefits><![CDATA[Une rémunération selon le profil à laquelle s'ajoute un / - 13ème mois - une prime "vacances" d'un montant de 1 694 - avantages sociaux (ticket restaurant, chèques vacances, réductions voyages, spectacles, abonnement culturels et sportifs…)]]></benefits>
                <employment_date><![CDATA[1]]></employment_date>
                <driving_licence><![CDATA[Permis non obligatoire]]></driving_licence>
            </job>
            """;

    @Language("XML")
    static final String WRAPPER = """
            ﻿<?xml version="1.0" encoding="utf-8"?><jobs>%s</jobs>Je n'ai rien à faire ici
            """;


    @Test
    void SoftyOfferDeserializations() {
        var softyJobXmlParser = new GenericJobXmlParser<>(SoftyJob.class, "/jobs/job", "softy");
        var jobs = softyJobXmlParser.parseJobs(WRAPPER.formatted(XML.formatted("")), new AtsGetOfferConfig());
        assertThat(jobs).hasSize(1);
    }

    @ParameterizedTest
    @ValueSource(booleans = {false, true})
    void SoftyOfferDeserialization(boolean hasLongLat) {
        var softyJobXmlParser = new GenericJobXmlParser<>(SoftyJob.class, "/jobs/job", "softy");
        var job = softyJobXmlParser.parseJob(XML.formatted(hasLongLat ?
                """
                        <long>
                        <![CDATA[ 5.74812000 ]]>
                        </long>
                        <lat>
                        <![CDATA[ 45.15240000 ]]>
                        </lat>""" : ""
        ));
        assertThat(job.getId()).isEqualTo("127954_0");
        assertThat(job.getTypeContractCategory()).isEqualTo(TypeContractCategoryDTO.PRO);
        assertThat(job.getLocationIndication()).isEqualTo("Eybens (38320)");
        assertThat(job.getOfferTitle()).isEqualTo("AGENT DE MAINTENANCE - H/F");
        assertThat(job.getCriterias()).containsExactlyInAnyOrder("REP-8-4");
        if (hasLongLat) {
            assertThat(job.getBuiltinLocation().getLatitude()).isCloseTo(45.1524f, Offset.offset(0.0001f));
            assertThat(job.getBuiltinLocation().getLongitude()).isCloseTo(5.74812f, Offset.offset(0.0001f));
        } else {
            assertThat(job.getBuiltinLocation()).isNull();
        }
    }
}
