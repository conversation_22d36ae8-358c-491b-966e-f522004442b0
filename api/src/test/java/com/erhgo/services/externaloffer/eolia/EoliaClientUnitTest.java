package com.erhgo.services.externaloffer.eolia;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.services.externaloffer.ATSRetryableException;
import com.erhgo.services.externaloffer.GenericAtsClient;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EoliaClientUnitTest {

    @Mock
    RestTemplate client;

    @InjectMocks
    GenericAtsClient eoliaClient;

    @BeforeEach
    void before() {
        ReflectionTestUtils.setField(eoliaClient, "restTemplate", client);
    }

    @Test
    void returnContentOnSuccess() {
        var expectedContent = "42";
        var mockedResponse = Mockito.mock(ResponseEntity.class);
        when(mockedResponse.getStatusCode()).thenReturn(HttpStatusCode.valueOf(200));
        when(mockedResponse.getBody()).thenReturn(expectedContent);
        when(client.exchange(Mockito.any(), Mockito.eq(HttpMethod.GET), Mockito.any(), Mockito.eq(String.class))).thenReturn(mockedResponse);
        var actualContent = eoliaClient.fetch(new AtsGetOfferConfig().setRemoteUrl("https://test.com"), Optional.empty());

        Assertions.assertThat(actualContent).isEqualTo(expectedContent);
    }

    @Test
    void throwExceptionOnStatusError() {
        var mockedResponse = Mockito.mock(ResponseEntity.class);
        when(mockedResponse.getStatusCode()).thenReturn(HttpStatusCode.valueOf(500));
        when(client.exchange(Mockito.any(), Mockito.eq(HttpMethod.GET), Mockito.any(), Mockito.eq(String.class))).thenReturn(mockedResponse);

        assertThrows(ATSRetryableException.class, () -> eoliaClient.fetch(new AtsGetOfferConfig().setRemoteUrl("https://test.com"), Optional.empty()));
    }

    @Test
    void throwExceptionOnExecutionOrTimeoutException() {
        when(client.exchange(Mockito.any(), Mockito.eq(HttpMethod.GET), Mockito.any(), Mockito.eq(String.class)))
                .thenThrow(new RestClientException("KO", new Exception()));

        assertThrows(GenericTechnicalException.class, () -> eoliaClient.fetch(new AtsGetOfferConfig().setRemoteUrl("https://test.com"), Optional.empty()));
    }


}
