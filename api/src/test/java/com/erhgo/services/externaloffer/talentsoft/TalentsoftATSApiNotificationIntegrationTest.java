package com.erhgo.services.externaloffer.talentsoft;

import com.erhgo.config.KeycloakMockService;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.externaloffer.AbstractATSApiNotificationIntegrationTest;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class TalentsoftATSApiNotificationIntegrationTest extends AbstractATSApiNotificationIntegrationTest {

    public static final String RECRUITER_CODE = "S-21687";
    @Autowired
    TalentsoftApiCandidatureNotificationScheduler scheduler;
    @MockitoBean
    KeycloakMockService keycloakService;

    @SneakyThrows
    @Test
    void handleNewCandidatures() {
        var capturedRequest = doSendCandidature(RECRUITER_CODE, scheduler, "TALENTSOFT");
        assertBodyContains(capturedRequest.body(), "talentsoftSendCandidatureCommand.txt");
        verify(userProfileCompetencesExportService).getProfileCompetenceForBatch(any(), anyString(), eq(ProfileCompetencesViewObject.AnonymousMode.BOTH));
        verifyNoMoreInteractions(userProfileCompetencesExportService);
    }
}
