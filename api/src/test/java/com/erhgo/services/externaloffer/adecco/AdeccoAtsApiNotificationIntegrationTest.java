package com.erhgo.services.externaloffer.adecco;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TestUtils;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.AbstractCandidature;
import com.erhgo.domain.candidature.job.CandidatureEmailRefusalState;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.CandidatureSynchronizationState;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.openapi.client.adecco.ApiException;
import com.erhgo.openapi.client.adecco.ApiResponse;
import com.erhgo.openapi.client.adecco.api.CandidateJobUnauthenticatedApi;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Answers;
import org.mockito.Mockito;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.OffsetDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;

class AdeccoAtsApiNotificationIntegrationTest extends AbstractIntegrationTest {

    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    AdeccoATSApiNotificationScheduler adeccoScheduler;

    @MockitoBean
    CandidateJobUnauthenticatedApi apiClient;
    @MockitoBean
    KeycloakMockService keycloakService;
    @MockitoBean(answers = Answers.RETURNS_MOCKS)
    UserProfileCompetencesExportService userProfileCompetencesExportService;

    static final String RECRUITER_CODE = "S-21712";

    @BeforeEach
    void initClient() {
        var configMap = ((Map) ReflectionTestUtils.getField(adeccoScheduler, "clientForAts"));
        configMap.put(configMap.keySet().iterator().next(), apiClient);
        txHelper.doInTransaction(() -> {
            var sql = """
                    DELETE FROM ConfigurableProperty
                    WHERE propertyKey = 'ats.disable-candidature-notification.adecco';
                    """;
            applicationContext.getBean(EntityManager.class).createNativeQuery(sql).executeUpdate();
        });
    }

    @ResetDataAfter
    @ParameterizedTest
    @ValueSource(booleans = {false, true})
    void ensure_event_is_not_handled_non_ats(boolean forAts) {

        var stateForId = Stream.of(CandidatureSynchronizationState.values())
                .map(s -> {
                    var userProfileMotherObject = applicationContext.getBean(UserProfileMotherObject.class);
                    // Ignore user without xp (we can't generate profile competences)
                    if (s != CandidatureSynchronizationState.WAITING) {
                        userProfileMotherObject.withExperienceOnOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist());
                    }
                    return applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                            .withRecruiterCode(RECRUITER_CODE)
                            .withATSCode(forAts ? "adecco" : null)
                            .withState(GlobalCandidatureState.NEW)
                            .withSubmissionDate(OffsetDateTime.now().minusMinutes(10))
                            .withUserProfile(userProfileMotherObject.withEmail("email").buildAndPersist())
                            .withCandidatureSynchronizationState(s)
                            .buildAndPersist();
                })
                .collect(Collectors.toMap(AbstractCandidature::getId, RecruitmentCandidature::getSynchronizationState));


        adeccoScheduler.handleNewCandidatures();

        assertThat(applicationContext.getBean(RecruitmentCandidatureRepository.class).findAll())
                .allMatch(c -> stateForId.get(c.getId()) == c.getSynchronizationState());

        Mockito.verifyNoInteractions(apiClient);
    }

    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @Test
    void ensure_service_is_disabled() {
        txHelper.doInTransaction(() -> {
            var sql = """
                    INSERT INTO ConfigurableProperty(propertyKey, propertyValue)
                    VALUES ('ats.disable-candidature-notification.adecco', 'true');
                    """;
            applicationContext.getBean(EntityManager.class).createNativeQuery(sql).executeUpdate();
        });
        var userId = "42";
        String email = "a@a";
        persistCandidature(userId, OffsetDateTime.now().minusMinutes(10), email, true, false, false);
        applicationContext.getBean(AdeccoATSApiNotificationScheduler.class).handleNewCandidatures();
        Mockito.verifyNoInteractions(apiClient);
    }

    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @Test
    void ensure_event_is_handled() {
        var userId = "42";
        var candidature = persistCandidature(userId, OffsetDateTime.now().minusMinutes(100), "ok", true, false, false);
        persistCandidature("noxp", OffsetDateTime.now().minusMinutes(100), "noxp", false, false, false);
        var archived = persistCandidature("arch", OffsetDateTime.now().minusMinutes(100), "archived", true, true, false);
        var refused = persistCandidature("ref", OffsetDateTime.now().minusMinutes(100), "refused", true, false, true);
        var erroneousCandidature = persistCandidature("Bof", OffsetDateTime.now().minusMinutes(100), "erroneous1", true, false, false);
        var erroneousCandidature2 = persistCandidature("Bof2", OffsetDateTime.now().minusMinutes(200), "erroneous2", true, false, false);
        var tooSoonCandidature = persistCandidature("Nope either", OffsetDateTime.now().plusMinutes(10), "soon", true, false, false);

        Mockito.doThrow(GenericTechnicalException.class).when(userProfileCompetencesExportService).getProfileCompetenceForBatch(eq(erroneousCandidature.getId()), anyString(), eq(ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE));
        Mockito.when(apiClient.postcreateAccountandApplyForJobBoardWithHttpInfo(anyString(), argThat(c -> c != null && c.getUser().getEmail().contains("erroneous2")), anyString(), anyString())).thenThrow(ApiException.class);
        Mockito.when(apiClient.postcreateAccountandApplyForJobBoardWithHttpInfo(anyString(), argThat(c -> c != null && !c.getUser().getEmail().contains("erroneous2")), anyString(), anyString())).thenReturn(new ApiResponse<>(200, new HashMap<>()));

        applicationContext.getBean(AdeccoATSApiNotificationScheduler.class).handleNewCandidatures();

        verify(userProfileCompetencesExportService).getProfileCompetenceForBatch(eq(candidature.getId()), Mockito.any(), eq(ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE));
        verify(userProfileCompetencesExportService).getProfileCompetenceForBatch(eq(erroneousCandidature2.getId()), Mockito.any(), eq(ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE));
        verify(userProfileCompetencesExportService).getProfileCompetenceForBatch(eq(erroneousCandidature.getId()), Mockito.any(), eq(ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE));
        var expectedRemoteIds = new HashSet<>(Set.of(erroneousCandidature2.getRecruitment().getExternalOffer().getRemoteId(), candidature.getRecruitment().getExternalOffer().getRemoteId()));
        var fileMatch = new AtomicBoolean(false);
        verify(apiClient, Mockito.times(2)).postcreateAccountandApplyForJobBoardWithHttpInfo(
                assertArg(a -> remove(expectedRemoteIds, a)),
                assertArg(c -> {
                    if (c != null && !c.getUser().getEmail().contains("erroneous")) {
                        TestUtils.assertFileContentMatch("adeccoAtsExpectedCommand", applicationContext.getBean(ObjectMapper.class).writeValueAsString(c), JSONCompareMode.LENIENT);
                        fileMatch.set(true);
                    }
                }),
                anyString(),
                eq("jnspcv_adecco"));
        assertThat(fileMatch).isTrue();
        assertThat(expectedRemoteIds).isEmpty();
        assertThat(applicationContext.getBean(RecruitmentCandidatureRepository.class).findById(candidature.getId()).orElseThrow().getSynchronizationState()).isEqualTo(CandidatureSynchronizationState.DONE);
        assertThat(applicationContext.getBean(RecruitmentCandidatureRepository.class).findById(candidature.getId()).orElseThrow().getRemoteNotifiedIdentifier()).startsWith("https://erhgo.wiremockapi.cloud");
        assertThat(applicationContext.getBean(RecruitmentCandidatureRepository.class).findById(tooSoonCandidature.getId()).orElseThrow().getSynchronizationState()).isEqualTo(CandidatureSynchronizationState.WAITING);
        assertThat(applicationContext.getBean(RecruitmentCandidatureRepository.class).findById(erroneousCandidature.getId()).orElseThrow().getSynchronizationState()).isEqualTo(CandidatureSynchronizationState.ERROR);
        assertThat(applicationContext.getBean(RecruitmentCandidatureRepository.class).findById(erroneousCandidature2.getId()).orElseThrow().getSynchronizationState()).isEqualTo(CandidatureSynchronizationState.ERROR);
        assertThat(applicationContext.getBean(RecruitmentCandidatureRepository.class).findById(archived.getId()).orElseThrow().getSynchronizationState()).isEqualTo(CandidatureSynchronizationState.IGNORE);
        assertThat(applicationContext.getBean(RecruitmentCandidatureRepository.class).findById(refused.getId()).orElseThrow().getSynchronizationState()).isEqualTo(CandidatureSynchronizationState.IGNORE);
    }

    private boolean remove(HashSet<String> expectedRemoteIds, String a) {
        return expectedRemoteIds.remove(a);
    }

    private RecruitmentCandidature persistCandidature(String userId, OffsetDateTime submissionDate, String email, boolean withXP, boolean archived, boolean refused) {
        String ln = "l", fn = "f";
        var userProfileMotherObject = applicationContext.getBean(UserProfileMotherObject.class);
        if (withXP) {
            userProfileMotherObject
                    .withExperienceOnOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist());
        }
        return applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(
                        userProfileMotherObject
                                .withUserId(userId)
                                .withEmail(email)
                                .withFirstname(fn)
                                .withLastname(ln)
                                .withPhoneNumber("0655006655")
                                .withLocation(Location.builder().city("Pau").postcode("42000").build())
                                .buildAndPersist()
                )
                .withIsArchived(archived)
                .withRefusalData(refused ? CandidatureEmailRefusalState.NONE : null, "me")
                .withRecruiterCode(RECRUITER_CODE)
                .withATSCode("adecco")
                .withState(GlobalCandidatureState.NEW)
                .withCandidatureSynchronizationState(CandidatureSynchronizationState.WAITING)
                .withSubmissionDate(submissionDate)
                .withAnonymousCode("AAA %s".formatted(email))
                .buildAndPersist();
    }

}
