package com.erhgo.services.externaloffer.talentplug;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.CandidatureSynchronizationState;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.services.mailing.MailNotifier;
import com.erhgo.services.userprofile.FilePartProvider;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import jakarta.persistence.EntityManager;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Answers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.time.OffsetDateTime;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;

class TalentPlugPerDbConfigSendNotificationTest extends AbstractIntegrationTest {

    public static final String MARIETTON_ORGA_CODE_1 = "S-0421";
    public static final String MARIETTON_ORGA_CODE_2 = "S-0422";
    @Autowired
    ApplicationContext applicationContext;

    @MockitoBean
    MailNotifier mailNotifier;

    @MockitoBean(answers = Answers.RETURNS_MOCKS)
    UserProfileCompetencesExportService userProfileCompetencesExportService;

    @Autowired
    TalentPlugATSMailNotificationScheduler talentPlugATSMailNotificationScheduler;

    @BeforeEach
    void prepareConf() {
        txHelper.doInTransaction(() -> {
            var sql = """
                    INSERT INTO PerOfferATSConfigurationItem(id, atsCode, configCode, remoteRecruiterCode, locationCode, recruiterCode)
                    VALUES  (uuidToBin('19c6cd67-d4ff-40aa-97de-72e57a942daa'), 'TALENT_PLUG', 'MARIETTON', 'M1', '', '%s'),
                            (uuidToBin('19c6cd67-d4ff-40aa-97de-72e57a942dab'), 'TALENT_PLUG', 'MARIETTON', 'M2', '', '%s');
                    """.formatted(MARIETTON_ORGA_CODE_1, MARIETTON_ORGA_CODE_2);
            applicationContext.getBean(EntityManager.class).createNativeQuery(sql).executeUpdate();
        });
    }

    @Test
    @ResetDataAfter
    void ensureSFCandidatureAreSent() {
        var orgaCodeToIgnore = "S-000";
        var staticOrgaCodeToConsider = "S-21508"; // AREAS
        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId("f3f2e850-b5d4-11ef-ac7e-96584d5248b2")
                .withEmail("<EMAIL>")
                .withFirstname("fn")
                .withLastname("ln")
                .withPhoneNumber("0123")
                .withLocation(Location.builder().city("Pau").postcode("42000").build())
                .withExperienceOnOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist())
                .buildAndPersist();
        var candidatures = List.of(
                createCandidature(MARIETTON_ORGA_CODE_1, userProfile),
                createCandidature(MARIETTON_ORGA_CODE_2, userProfile),
                createCandidature(orgaCodeToIgnore, userProfile),
                createCandidature(staticOrgaCodeToConsider, userProfile));

        talentPlugATSMailNotificationScheduler.handleNewCandidatures();

        txHelper.doInTransaction(() -> {
            var upToDateCandidatures = applicationContext.getBean(RecruitmentCandidatureRepository.class).findAllById(candidatures.stream().map(RecruitmentCandidature::getId).toList());
            Assertions.assertThat(upToDateCandidatures).filteredOn(a -> a.getSynchronizationState() == CandidatureSynchronizationState.DONE).hasSize(3);
            Assertions.assertThat(upToDateCandidatures).filteredOn(a -> a.getSynchronizationState() == CandidatureSynchronizationState.WAITING).hasSize(1);
            Mockito.verify(mailNotifier, Mockito.times(3)).sendMail(anyCollection(), anyString(), anyString(), eq(true), anyString(), anyString(), Mockito.any(FilePartProvider.class), Mockito.any(FilePartProvider.class));
        });
    }

    private RecruitmentCandidature createCandidature(String orgaCode, UserProfile userProfile) {
        return applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userProfile)
                .withRecruiterCode(orgaCode)
                .withATSCode("TALENT_PLUG")
                .withState(GlobalCandidatureState.NEW)
                .withEmailToNotify("%<EMAIL>".formatted(orgaCode))
                .withCandidatureSynchronizationState(CandidatureSynchronizationState.WAITING)
                .withSubmissionDate(OffsetDateTime.now().minusHours(5))
                .buildAndPersist();
    }
}
