package com.erhgo.services.externaloffer.digitalrecruiters;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.externaloffer.ExternalOffer;
import com.erhgo.domain.externaloffer.ExternalOfferEventType;
import com.erhgo.domain.externaloffer.ExternalOfferMotherObject;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.openapi.dto.AtsOfferSimulatedDTO;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.externaloffer.ExternalOfferRecruitmentService;
import com.erhgo.services.externaloffer.ExternalOfferScheduler;
import com.erhgo.services.externaloffer.GenericAtsClient;
import com.erhgo.services.externaloffer.notification.RemoteOfferMessageDTO;
import com.erhgo.services.notifier.Notifier;
import jakarta.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.assertj.core.api.Assertions;
import org.intellij.lang.annotations.Language;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
class ExternalOfferDRControllerTest extends AbstractIntegrationTest {

    public static final String DEFAULT_MODIFICATION_DATE_AS_STRING = "10/08/2010 14:00:01";
    public static final LocalDateTime DEFAULT_REMOTE_DATE_TIME = LocalDateTime.of(2020, 10, 10, 10, 10);
    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    ExternalOfferRepository repository;
    @Autowired
    ExternalOfferScheduler externalOfferScheduler;
    @MockitoBean
    Notifier notifier;
    @MockitoBean
    GenericAtsClient eoliaClient;
    @MockitoBean
    //Unused but mocked to simplify tests
    ExternalOfferRecruitmentService externalOfferRecruitmentService;

    private static final String UUID_GENERIC = "00000000-0000-0000-0000-000000000000";

    @Language("json")
    private static final String OFFER_TEMPLATE = """
                {
                  "locale": "fr_FR",
                  "reference": "%s",
                  "published_at": "2024-05-24T20:41:03+00:00",
                  "catch_phrase": "",
                  "contract_type": "CDI",
                  "contract_duration": {
                    "min": "0",
                    "max": "0"
                  },
                  "contract_work_period": "Temps plein",
                  "service": "Chargé d'études actuarielles/Actuaire",
                  "service_internal_ref": null,
                  "service_hash_id": "5ZmDvMeR",
                  "experience_level": "De 2 à 5 ans",
                  "education_level": "Maitrise, IEP, IUP, Bac+4",
                  "title": "Offre %s",
                  "description": "\\u003Cdiv style=\\"text-align:center;\\"\\u003E\\u003Cb\\u003EVenez contribuer par la modélisation statistique et l’analyse actuarielle à la conception et l’adaptation technique de nos offres. \\u003C/div\\u003E\\n\\n",
                  "profile": "\\u003Cdiv\\u003EVous disposez d’une \\u003Cb\\u003Eformation supérieure en Statistiques/Économétrie/Actuariat \\u003C/b\\u003Eet d'une \\u003Cb\\u003Eexpérience de 5 ans sur un poste similaire\\u003C/div\\u003E",
                  "skills": [
                    "SAS",
                    "rigueur",
                    "orientation résultats"
                  ],
                  "salary": {
                    "min": null,
                    "max": null,
                    "kind": null,
                    "rate_type": null,
                    "variable": null,
                    "currency": null
                  },
                  "address": {
                    "parts": {
                      "street": "114 Bd Marius Vivier Merle",
                      "zip": "69003",
                      "city": "Lyon",
                      "county": "Rhône",
                      "state": "Auvergne-Rhône-Alpes",
                      "country": "France"
                    },
                    "formatted": "114 Bd Marius Vivier Merle,  69003 Lyon, France",
                    "position": {
                      "lon": "4.8591925",
                      "lat": "45.7525326"
                    }
                  },
                  "entity": {
                    "public_name": "ASP - Service Actuariat PRO TPE",
                    "around": "\\u003Cp\\u003EAu cœur du quartier de la Part Dieu, APRIL bénéficie d’une situation géographique idéale ; à 10 minutes à pied du centre commercial et de la gare, à 15 minutes du centre-ville et 15 minutes du Parc de la tête d’or.",
                    "address": {
                      "parts": {
                        "street": "114 Bd Marius Vivier Merle",
                        "zip": "69003",
                        "city": "Lyon",
                        "county": "Rhône",
                        "state": "Auvergne-Rhône-Alpes",
                        "country": "France"
                      },
                      "formatted": "114 Bd Marius Vivier Merle,  69003 Lyon, France",
                      "position": {
                        "lon": "4.8591925",
                        "lat": "45.7525326"
                      }
                    },
                    "manager": {
                      "section_title": "Le mot de la DRH",
                      "section_body": "\\u003Cp\\u003EAllier le meilleur de l’humain et de la technologie au service de notre raison d’être \\u003Cbr\\u003E\\n« Accompagner et protéger à chaque moment qui compte, simplement »…\\u003C/p\\u003E",
                      "firstname": "Maud",
                      "lastname": "PADILLA",
                      "position": "DRH Groupe"
                    },
                    "hierarchy": [
                      {
                        "depth": 2,
                        "column_name": "Pays",
                        "public_name": "FRANCE"
                      },
                      {
                        "depth": 1,
                        "column_name": "Compte enfant",
                        "public_name": "APRIL"
                      }
                    ]
                  },
                  "referent_recruiter": {
                    "firstname": "Patrick",
                    "lastname": "Suez",
                    "picture_url": null
                  },
                  "brand": {
                    "name": "APRIL",
                    "description": "APRIL est le leader du courtage grossiste en France avec un réseau de 15 000 courtiers partenaires.\\nLes 2 300 collaborateurs d’APRIL ont l’ambition de proposer."
                  },
                  "apply_url": "yo.lo",
                  "custom_fields": [
                    {
                      "hash": "bZeLg4RD",
                      "name": "Type de contrat",
                      "value": "CDI"
                    },
                    {
                      "hash": "aZKvpERB",
                      "name": "Motif du recrutement",
                      "value": "Remplacement de Jean Dupont"
                    },
                    {
                      "hash": "pZaPgqRP",
                      "name": "Si remplacement, nom de la personne à remplacer / Si renouvellement-mobilité, nom de la personne recrutée",
                      "value": "Jean Dupont"
                    },
                    {
                      "hash": "z9rq44ZG",
                      "name": "Nombre de postes à pourvoir",
                      "value": "1"
                    },
                    {
                      "hash": "pZaPVwRP",
                      "name": "Niveau d'habilitation requis ?",
                      "value": "Non concerné"
                    },
                    {
                      "hash": "dZwKb7Zr",
                      "name": "Diffusion de l’annonce",
                      "value": "En interne et en externe"
                    },
                    {
                      "hash": "Yx13rLZ2",
                      "name": "Validé par le directeur de la matrice ?",
                      "value": "Oui"
                    },
                    {
                      "hash": "G9D2PXRr",
                      "name": "Date envisagée de recrutement",
                      "value": "01/03/22"
                    }
                  ]
                }
            """;

    static final String JOBS_WRAPPER = """
                            {
                          "count": 72,
                          "version": "1.0.1",
                          "fetched_at": "2024-05-27T08:59:20+00:00",
                          "spontaneousApplyUrl": {
                            "fr_FR": "https://recrutement.april.com/fr/candidature/spontanee/1#declareStep1"
                          },
                          "ads": [
                          %s
                          ]}
            """;


    @BeforeEach
    void prepare() {
        Mockito.when(eoliaClient.fetch(ArgumentMatchers.any(), ArgumentMatchers.eq(Optional.empty()))).thenReturn("");
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void simulate_ats_offer_no_modification() throws Exception {
        var id = "12";
        var json = OFFER_TEMPLATE.formatted(id, id);
        applicationContext.getBean(ExternalOfferMotherObject.class)
                .withRemoteId(id)
                .withRemoteModificationDate(DEFAULT_REMOTE_DATE_TIME)
                .withRawContent(json)
                .withLastEventType(ExternalOfferEventType.CREATED)
                .withATSCode("DIGITAL_RECRUITERS")
                .withRecruiterCode("S-21531")
                .buildAndPersist();
        var dto = new AtsOfferSimulatedDTO().rawFlow(JOBS_WRAPPER.formatted(json));

        performPost("/external-offer/simulate-ats-offer", dto)
                .andExpect(status().isNoContent());
        Mockito.verify(notifier).sendMessage(ArgumentMatchers.assertArg(a -> {
            Assertions.assertThat(a.getClass()).isAssignableFrom(RemoteOfferMessageDTO.class);
            Assertions.assertThat(a.getText()).isEqualTo("Aucune modification constatée - ATS DIGITAL_RECRUITERS--APRIL");
        }));
    }

    @ParameterizedTest
    @ValueSource(booleans = {false, true})
    @NullSource
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void initialize_extracted_offer_data_init_data(Boolean withRecruitment) throws Exception {
        var id = "12";
        var rawContent = OFFER_TEMPLATE.formatted(id, id);
        var externalOffer = applicationContext.getBean(ExternalOfferMotherObject.class)
                .withRemoteId(id)
                .withRecruitment(BooleanUtils.isNotFalse(withRecruitment) ? applicationContext.getBean(RecruitmentMotherObject.class).buildAndPersist() : null)
                .withRawContent(rawContent)
                .withATSCode("DIGITAL_RECRUITERS")
                .withPreviousXmls(withRecruitment == null ? new String[]{rawContent, rawContent} : new String[]{})
                .buildAndPersist();

        var expectedResult = withRecruitment == null ? "externalOfferInitDRDataWithRecruitmentAndPreviousContent" : (withRecruitment ? "externalOfferInitDRDataWithRecruitment" : "externalOfferInitDRData");

        performGetAndExpect(
                "/external-offer/initialize-extracted-offer-data?externalOfferId=%s".formatted(externalOffer.getUuid()),
                expectedResult,
                false)
                .andExpect(status().isOk());
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void ats_limited_by_configuration() throws Exception {
        txHelper.doInTransaction(() -> {
            var sql = """
                    INSERT INTO ConfigurableProperty(propertyKey, propertyValue) VALUES
                            ('ats.per-aura-in-out.in.default', '0'),
                            ('ats.per-aura-in-out.out.default', '0')
                    ON DUPLICATE KEY UPDATE propertyValue = VALUES(propertyValue)
                    """;
            applicationContext.getBean(EntityManager.class).createNativeQuery(sql).executeUpdate();
        });
        var createdRemoteId = "1";
        var dto = new AtsOfferSimulatedDTO().rawFlow(JOBS_WRAPPER.formatted(OFFER_TEMPLATE.formatted(createdRemoteId, createdRemoteId)));
        performPost("/external-offer/simulate-ats-offer", dto)
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            Assertions.assertThat(findByRemoteId(createdRemoteId)).isNull();
            Mockito.verify(notifier).sendMessage(ArgumentMatchers.assertArg(a -> {
                Assertions.assertThat(a.getClass()).isAssignableFrom(RemoteOfferMessageDTO.class);
                Assertions.assertThat(a.getText()).isEqualTo("Aucune modification constatée - ATS DIGITAL_RECRUITERS--APRIL");
            }));
        });
    }

    @ParameterizedTest
    @ValueSource(booleans = {false, true})
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void ats_limited_by_configuration_aura_in(boolean isDefault) throws Exception {
        txHelper.doInTransaction(() -> {
            var sql = isDefault ?
                    """
                            INSERT INTO ConfigurableProperty(propertyKey, propertyValue) VALUES
                                    ('ats.per-aura-in-out.in.default', '2'),
                                    ('ats.per-aura-in-out.out.default', '0')
                            ON DUPLICATE KEY UPDATE propertyValue = VALUES(propertyValue)
                            """ :
                    """
                            INSERT INTO ConfigurableProperty(propertyKey, propertyValue) VALUES
                                    ('ats.per-aura-in-out.in.DIGITAL_RECRUITERS--APRIL', '2'),
                                    ('ats.per-aura-in-out.out.DIGITAL_RECRUITERS--APRIL', '0'),
                                    ('ats.per-aura-in-out.in.eolia--MB', '0'),
                                    ('ats.per-aura-in-out.out.eolia--MB', '0'),
                                    ('ats.per-aura-in-out.in.eolia--EMALEC', '0'),
                                    ('ats.per-aura-in-out.out.eolia--EMALEC', '0'),
                                    ('ats.notification-delay-in-hours.eolia--MB', '72')
                            ON DUPLICATE KEY UPDATE propertyValue = VALUES(propertyValue)
                            """;
            applicationContext.getBean(EntityManager.class).createNativeQuery(sql).executeUpdate();
        });
        var createdRemoteId = "1";
        var createdRemoteId2 = "2";
        var dto = new AtsOfferSimulatedDTO().rawFlow(JOBS_WRAPPER.formatted(String.join(",", OFFER_TEMPLATE.formatted(createdRemoteId, createdRemoteId), OFFER_TEMPLATE.formatted(createdRemoteId2, createdRemoteId2).replace("69003", "06011"))));
        performPost("/external-offer/simulate-ats-offer", dto)
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            Assertions.assertThat(findByRemoteId(createdRemoteId)).isNotNull();
            Mockito.verify(notifier).sendMessage(ArgumentMatchers.assertArg(a -> {
                Assertions.assertThat(a.getClass()).isAssignableFrom(RemoteOfferMessageDTO.class);
                Assertions.assertThat(a.getText())
                        .contains("ATS DIGITAL_RECRUITERS--APRIL : 1 modification constatée\n")
                        .contains("69003")
                        .doesNotContain("06011")
                ;
            }));
        });

    }


    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void simulate_ats_offer_check_offers_are_correctly_built() throws Exception {
        final var createdRemoteId = "1";
        final var modifiedDateOnlyRemoteId = "2";
        final var modifiedForRealRemoteId = "42";
        final var republishedRemoteId = "3";
        final var noActionRemoteId = "4";
        final var republishedAndModifiedRemoteId = "5";
        final var suspendedRemoteId = "6";
        final var alreadySuspendedRemoteId = "7";
        final var modifiedInexistant = "8";

        var allContents = Stream
                .of(createdRemoteId, modifiedDateOnlyRemoteId, modifiedForRealRemoteId, republishedRemoteId, noActionRemoteId, republishedAndModifiedRemoteId, modifiedInexistant)
                .collect(Collectors.toMap(
                        Function.identity(),
                        id -> OFFER_TEMPLATE.formatted(id, id)
                ));

        var previousContentMarker = "444444";
        var emulatedGlobalContent = JOBS_WRAPPER.formatted(String.join(",", allContents.values()));
        var modifiedDateOnlyLastContent = OFFER_TEMPLATE.replace("2024-05-24T20:41:03+00:00", "2024-05-23T20:41:03+00:00");
        var modifiedForRealLastContent = modifiedDateOnlyLastContent.replace("modélisation", "cette offre est vraiment modifiée");
        Stream.of(modifiedDateOnlyRemoteId, modifiedForRealRemoteId, republishedRemoteId, noActionRemoteId, republishedAndModifiedRemoteId, suspendedRemoteId, alreadySuspendedRemoteId, modifiedInexistant)
                .map(remoteId ->
                        {
                            var wasSuspended = remoteId.equals(alreadySuspendedRemoteId) || remoteId.equals(republishedRemoteId) || remoteId.equals(republishedAndModifiedRemoteId);
                            var lastContent = OFFER_TEMPLATE.formatted(remoteId, remoteId);
                            var previousContent = OFFER_TEMPLATE.formatted(previousContentMarker, remoteId);
                            return applicationContext.getBean(ExternalOfferMotherObject.class)
                                    .withRemoteId(remoteId)
                                    .withATSCode("DIGITAL_RECRUITERS")
                                    .withRemoteModificationDate(DEFAULT_REMOTE_DATE_TIME)
                                    .withPreviousXmls(remoteId.equals(modifiedForRealRemoteId) ? new String[]{previousContent, previousContent, previousContent, previousContent, previousContent} : new String[]{})
                                    .withRecruiterCode("S-21531")
                                    .withRawContent(
                                            switch (remoteId) {
                                                case modifiedForRealRemoteId, modifiedInexistant,
                                                     republishedAndModifiedRemoteId ->
                                                        modifiedForRealLastContent.formatted(remoteId, remoteId);
                                                case modifiedDateOnlyRemoteId ->
                                                        modifiedDateOnlyLastContent.formatted(remoteId, remoteId);
                                                default -> lastContent;
                                            }
                                    )
                                    .withRecruitment(remoteId.equals(modifiedInexistant) ? null :
                                            applicationContext.getBean(RecruitmentMotherObject.class).withJobTitle("Recrutement sur remoteId %s".formatted(remoteId)).withState(wasSuspended ? RecruitmentState.SELECTION : RecruitmentState.PUBLISHED).buildAndPersist()
                                    )
                                    .withLastEventType(wasSuspended ? ExternalOfferEventType.SUSPENDED : ExternalOfferEventType.CREATED)
                                    .buildAndPersist();
                        }
                ).toList();

        var dto = new AtsOfferSimulatedDTO().rawFlow(emulatedGlobalContent);
        performPost("/external-offer/simulate-ats-offer", dto)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            assertThat(findByRemoteId(createdRemoteId))
                    .matches(r -> matches(r, ExternalOfferEventType.CREATED, """
                            {"locale":"fr_FR","reference":"1","published_at":"2024-05-24T20:41:03+00:00","catch_phrase":"","contract_type":"CDI","contract_duration":{"min":"0","max":"0"},"contract_work_period":"Temps plein","service":"Chargé d'études actuarielles/Actuaire","service_internal_ref":null,"service_hash_id":"5ZmDvMeR","experience_level":"De 2 à 5 ans","education_level":"Maitrise, IEP, IUP, Bac+4","title":"Offre 1","description":"<div style=\\"text-align:center;\\"><b>Venez contribuer par la modélisation statistique et l’analyse actuarielle à la conception et l’adaptation technique de nos offres. </div>\\n\\n","profile":"<div>Vous disposez d’une <b>formation supérieure en Statistiques/Économétrie/Actuariat </b>et d'une <b>expérience de 5 ans sur un poste similaire</div>","skills":["SAS","rigueur","orientation résultats"],"salary":{"min":null,"max":null,"kind":null,"rate_type":null,"variable":null,"currency":null},"address":{"parts":{"street":"114 Bd Marius Vivier Merle","zip":"69003","city":"Lyon","county":"Rhône","state":"Auvergne-Rhône-Alpes","country":"France"},"formatted":"114 Bd Marius Vivier Merle,  69003 Lyon, France","position":{"lon":"4.8591925","lat":"45.7525326"}},"entity":{"public_name":"ASP - Service Actuariat PRO TPE","around":"<p>Au cœur du quartier de la Part Dieu, APRIL bénéficie d’une situation géographique idéale ; à 10 minutes à pied du centre commercial et de la gare, à 15 minutes du centre-ville et 15 minutes du Parc de la tête d’or.","address":{"parts":{"street":"114 Bd Marius Vivier Merle","zip":"69003","city":"Lyon","county":"Rhône","state":"Auvergne-Rhône-Alpes","country":"France"},"formatted":"114 Bd Marius Vivier Merle,  69003 Lyon, France","position":{"lon":"4.8591925","lat":"45.7525326"}},"manager":{"section_title":"Le mot de la DRH","section_body":"<p>Allier le meilleur de l’humain et de la technologie au service de notre raison d’être <br>\\n« Accompagner et protéger à chaque moment qui compte, simplement »…</p>","firstname":"Maud","lastname":"PADILLA","position":"DRH Groupe"},"hierarchy":[{"depth":2,"column_name":"Pays","public_name":"FRANCE"},{"depth":1,"column_name":"Compte enfant","public_name":"APRIL"}]},"referent_recruiter":{"firstname":"Patrick","lastname":"Suez","picture_url":null},"brand":{"name":"APRIL","description":"APRIL est le leader du courtage grossiste en France avec un réseau de 15 000 courtiers partenaires.\\nLes 2 300 collaborateurs d’APRIL ont l’ambition de proposer."},"apply_url": "yo.lo","custom_fields":[{"hash":"bZeLg4RD","name":"Type de contrat","value":"CDI"},{"hash":"aZKvpERB","name":"Motif du recrutement","value":"Remplacement de Jean Dupont"},{"hash":"pZaPgqRP","name":"Si remplacement, nom de la personne à remplacer / Si renouvellement-mobilité, nom de la personne recrutée","value":"Jean Dupont"},{"hash":"z9rq44ZG","name":"Nombre de postes à pourvoir","value":"1"},{"hash":"pZaPVwRP","name":"Niveau d'habilitation requis ?","value":"Non concerné"},{"hash":"dZwKb7Zr","name":"Diffusion de l’annonce","value":"En interne et en externe"},{"hash":"Yx13rLZ2","name":"Validé par le directeur de la matrice ?","value":"Oui"},{"hash":"G9D2PXRr","name":"Date envisagée de recrutement","value":"01/03/22"}]}"""
                    ));

            assertThat(findByRemoteId(republishedRemoteId))
                    .matches(r -> matches(r, ExternalOfferEventType.REPUBLISHED, OFFER_TEMPLATE.formatted(republishedRemoteId, republishedRemoteId))
                    );
            assertThat(findByRemoteId(noActionRemoteId))
                    .matches(r -> matches(r, ExternalOfferEventType.CREATED, OFFER_TEMPLATE.formatted(noActionRemoteId, noActionRemoteId))
                    );

            assertThat(findByRemoteId(republishedAndModifiedRemoteId))
                    .matches(r -> matches(r, ExternalOfferEventType.MODIFIED, """
                            {"locale":"fr_FR","reference":"5","published_at":"2024-05-24T20:41:03+00:00","catch_phrase":"","contract_type":"CDI","contract_duration":{"min":"0","max":"0"},"contract_work_period":"Temps plein","service":"Chargé d'études actuarielles/Actuaire","service_internal_ref":null,"service_hash_id":"5ZmDvMeR","experience_level":"De 2 à 5 ans","education_level":"Maitrise, IEP, IUP, Bac+4","title":"Offre 5","description":"<div style=\\"text-align:center;\\"><b>Venez contribuer par la modélisation statistique et l’analyse actuarielle à la conception et l’adaptation technique de nos offres. </div>\\n\\n","profile":"<div>Vous disposez d’une <b>formation supérieure en Statistiques/Économétrie/Actuariat </b>et d'une <b>expérience de 5 ans sur un poste similaire</div>","skills":["SAS","rigueur","orientation résultats"],"salary":{"min":null,"max":null,"kind":null,"rate_type":null,"variable":null,"currency":null},"address":{"parts":{"street":"114 Bd Marius Vivier Merle","zip":"69003","city":"Lyon","county":"Rhône","state":"Auvergne-Rhône-Alpes","country":"France"},"formatted":"114 Bd Marius Vivier Merle,  69003 Lyon, France","position":{"lon":"4.8591925","lat":"45.7525326"}},"entity":{"public_name":"ASP - Service Actuariat PRO TPE","around":"<p>Au cœur du quartier de la Part Dieu, APRIL bénéficie d’une situation géographique idéale ; à 10 minutes à pied du centre commercial et de la gare, à 15 minutes du centre-ville et 15 minutes du Parc de la tête d’or.","address":{"parts":{"street":"114 Bd Marius Vivier Merle","zip":"69003","city":"Lyon","county":"Rhône","state":"Auvergne-Rhône-Alpes","country":"France"},"formatted":"114 Bd Marius Vivier Merle,  69003 Lyon, France","position":{"lon":"4.8591925","lat":"45.7525326"}},"manager":{"section_title":"Le mot de la DRH","section_body":"<p>Allier le meilleur de l’humain et de la technologie au service de notre raison d’être <br>\\n« Accompagner et protéger à chaque moment qui compte, simplement »…</p>","firstname":"Maud","lastname":"PADILLA","position":"DRH Groupe"},"hierarchy":[{"depth":2,"column_name":"Pays","public_name":"FRANCE"},{"depth":1,"column_name":"Compte enfant","public_name":"APRIL"}]},"referent_recruiter":{"firstname":"Patrick","lastname":"Suez","picture_url":null},"brand":{"name":"APRIL","description":"APRIL est le leader du courtage grossiste en France avec un réseau de 15 000 courtiers partenaires.\\nLes 2 300 collaborateurs d’APRIL ont l’ambition de proposer."},"apply_url": "yo.lo","custom_fields":[{"hash":"bZeLg4RD","name":"Type de contrat","value":"CDI"},{"hash":"aZKvpERB","name":"Motif du recrutement","value":"Remplacement de Jean Dupont"},{"hash":"pZaPgqRP","name":"Si remplacement, nom de la personne à remplacer / Si renouvellement-mobilité, nom de la personne recrutée","value":"Jean Dupont"},{"hash":"z9rq44ZG","name":"Nombre de postes à pourvoir","value":"1"},{"hash":"pZaPVwRP","name":"Niveau d'habilitation requis ?","value":"Non concerné"},{"hash":"dZwKb7Zr","name":"Diffusion de l’annonce","value":"En interne et en externe"},{"hash":"Yx13rLZ2","name":"Validé par le directeur de la matrice ?","value":"Oui"},{"hash":"G9D2PXRr","name":"Date envisagée de recrutement","value":"01/03/22"}]}""")
                    );

            assertThat(findByRemoteId(suspendedRemoteId))
                    .matches(r -> matches(r, ExternalOfferEventType.SUSPENDED, OFFER_TEMPLATE.formatted(suspendedRemoteId, suspendedRemoteId))
                    );

            var actual = findByRemoteId(modifiedDateOnlyRemoteId);
            assertThat(actual)
                    .matches(r -> matches(r, ExternalOfferEventType.CREATED, modifiedDateOnlyLastContent.formatted(modifiedDateOnlyRemoteId, modifiedDateOnlyRemoteId))
                    );
            actual = findByRemoteId(modifiedForRealRemoteId);

            assertThat(actual.getExternalOfferContentHistory().stream().toList().subList(1, 5))
                    .allMatch(a -> a.getRawContent().equals(OFFER_TEMPLATE.formatted(previousContentMarker, modifiedForRealRemoteId)));

            assertThat(actual.getExternalOfferContentHistory())
                    .hasSize(5)
                    .first().satisfies(a -> {
                        Assertions.assertThat(a.getRawContent()).isEqualTo(modifiedForRealLastContent.formatted(modifiedForRealRemoteId, modifiedForRealRemoteId));
                    });
            var expectedNotificationText = """

                    ATS DIGITAL_RECRUITERS--APRIL : 7 modifications constatées
                    - *1 offre nouvelle* :
                        -> <https://sourcing.erhgo.fr/#/edit-recruitment?externalOfferId=00000000-0000-0000-0000-000000000000| Offre 1 à Lyon (69003)> (nom du client: APRIL)
                    - *1 offre suspendue* :
                        -> <https://sourcing.erhgo.fr/#/recruitment-detail/6| <?> à <?>> (nom du client: <?>)
                    - *2 offres republiées* :
                        -> <https://sourcing.erhgo.fr/#/recruitment-detail/3| <?> à <?>> (nom du client: <?>)
                        -> <https://sourcing.erhgo.fr/#/recruitment-detail/5| Offre 5 à Lyon (69003)> (nom du client: APRIL)
                    - *3 offres modifiées* :
                        -> <https://sourcing.erhgo.fr/#/edit-recruitment?externalOfferId=00000000-0000-0000-0000-000000000000| Offre 42 à Lyon (69003)> (nom du client: APRIL)
                        -> <https://sourcing.erhgo.fr/#/edit-recruitment?externalOfferId=00000000-0000-0000-0000-000000000000| Offre 5 à Lyon (69003)> (nom du client: APRIL)
                        -> [:warning: Offre sans recrutement] <https://sourcing.erhgo.fr/#/edit-recruitment?externalOfferId=00000000-0000-0000-0000-000000000000| Offre 8 à Lyon (69003)> (nom du client: APRIL)
                    """;

            Mockito.verify(notifier, Mockito.times(1)).sendMessage(ArgumentMatchers.assertArg(a ->
            {
                assertThat(a).isOfAnyClassIn(RemoteOfferMessageDTO.class);
                Assertions.assertThat(replaceUuidByGenericOne(a.getText())).isEqualTo(expectedNotificationText);
            }));
        });
    }


    private ExternalOffer findByRemoteId(String modifiedRemoteId) {
        var list = applicationContext.getBean(EntityManager.class).createQuery("SELECT a FROM ExternalOffer a WHERE remoteId = ?1", ExternalOffer.class).setParameter(1, modifiedRemoteId).getResultList();
        Assertions.assertThat(list).hasSizeLessThanOrEqualTo(1);
        return list.stream().findFirst().orElse(null);
    }

    private String replaceUuidByGenericOne(String message) {
        var regex = "\\b[0-9a-f]{8}\\b-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-\\b[0-9a-f]{12}\\b";

        var pattern = Pattern.compile(regex);
        var matcher = pattern.matcher(message);

        return matcher.replaceAll(UUID_GENERIC);
    }

    private boolean matches(ExternalOffer offer, ExternalOfferEventType expectedEvent, String expectedXml) {
        assertThat(offer.getLastEventType()).isEqualTo(expectedEvent);
        assertThat(offer.getLastRawContent()).isEqualToIgnoringWhitespace(expectedXml);
        return true;
    }

}
