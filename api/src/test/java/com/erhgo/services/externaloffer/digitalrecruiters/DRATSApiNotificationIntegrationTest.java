package com.erhgo.services.externaloffer.digitalrecruiters;

import com.erhgo.config.KeycloakMockService;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.externaloffer.AbstractATSApiNotificationIntegrationTest;
import com.jayway.jsonpath.JsonPath;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class DRATSApiNotificationIntegrationTest extends AbstractATSApiNotificationIntegrationTest {

    public static final String RECRUITER_CODE = "S-21651";
    @Autowired
    DRApiCandidatureNotificationScheduler scheduler;
    @MockitoBean
    KeycloakMockService keycloakService;

    @SneakyThrows
    @Test
    void handleNewCandidatures() {
        var capturedRequest = doSendCandidature(RECRUITER_CODE, scheduler, "DIGITAL_RECRUITERS");
        var content = assertBodyContains(capturedRequest.body(), "drSendCandidatureCommand");
        String date = JsonPath.read(content, "$.consent_date");
        Assertions.assertThat(date).isNotBlank();
        verify(userProfileCompetencesExportService).getProfileCompetenceForBatch(any(), anyString(), eq(ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE));
        verifyNoMoreInteractions(userProfileCompetencesExportService);
    }
}
