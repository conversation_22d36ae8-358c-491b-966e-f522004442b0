package com.erhgo.services.externaloffer.taleez;

import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.parsers.GenericJobXmlParser;
import org.intellij.lang.annotations.Language;
import org.junit.jupiter.api.Test;

import static com.erhgo.TestUtils.assertJsonSerializedObjectMatchesContent;
import static org.assertj.core.api.Assertions.assertThat;

class TaleezOfferUnitTest {
    @Language("XML")
    private static final String ALL_XML = """
            <?xml version="1.0" encoding="UTF-8" ?>
            <jobs>
                <job>
                    <title>
                        <![CDATA[ Offre de test numero 1, CDI temps plein ]]>
                    </title>
                    <url>
                        <![CDATA[ https://taleez.com/apply/offre-de-test-numero-1-cdi-temps-plein-toulouse-my-company-cdi/applying?utm_source=jobboard ]]>
                    </url>
                    <referencenumber>
                        <![CDATA[ 27j7t2e ]]>
                    </referencenumber>
                    <companydescription>
                        <![CDATA[ <p>Mon entreprise c'est avant tout …… qui ont créé l'entreprise en ……</p><p>Bâtie sur des valeurs de ……, de ……, et de ……, elle n'a cessé de croître et de se renforcer grâce à de nombreux talents (peut-être toi prochainement :) ?)</p><ul><li>Géolocalisation</li><li>Locaux</li><li>Nombre d'employés</li><li>Conditions de travail, bureaux ou open space, …</li><li>Avantages</li></ul><p>Nous privilégions l'humain avant tout, c'est pour cela que nous avons décidé de travailler dans ……</p><p>Communiquez sur l'expérience collaborateur que vous proposez à vos candidats. N'hésitez pas à donner des éléments concrets pour appuyer votre discours !</p> ]]>
                    </companydescription>
                    <description>
                        <![CDATA[ <p>Nous vous proposons de rejoindre notre audacieux pôle ………. vous devrez ………... en collaborant avec les membres de votre équipe. Ce métier est l'un des rouages essentiels dans…… et dans ……</p><p>Chez …. nous souhaitons donner à tous nos collaborateurs l'occasion de se développer et de consolider notre entreprise.</p><p>Vos missions quotidiennes seront :</p><ul><li>Organiser…</li><li>Participer…</li><li>Manager…</li><li>Analyser…</li><li>Réaliser…</li></ul><p>N'oubliez pas de rassurer vos candidats que cela soit d'un point de vue financier (rémunération, avantages) ou bien sur la sécurité du poste (type de contrat, organisation).</p> ]]>
                    </description>
                    <profile>
                        <![CDATA[ <p>Nous cherchons un nouveau collaborateur ou une nouvelle collaboratrice avec une formation en …… et qui a déjà eu une première expérience en ……, idéalement en ……</p><p>Vous avez des compétences en ……, et en …… et …… est votre plus grande force ?</p><p>Alors ce poste est fait pour vous !</p><p>Ce que nous vous offrons :</p><ul><li>Travailler dans un environnement bienveillant et solidaire</li><li>Faire partie d'une aventure humaine et professionnelle passionnante</li><li>Possibilité de formation et de développement</li></ul><p>N'oubliez pas de conclure avec une note sympathique pour motiver votre talent !</p> ]]>
                    </profile>
                    <date>
                        <![CDATA[ 2023-02-23 ]]>
                    </date>
                    <jobtype>
                        <![CDATA[ CDI ]]>
                    </jobtype>
                    <contractDuration/>
                    <fulltime>
                        <![CDATA[ true ]]>
                    </fulltime>
                    <weeklyHours>
                        <![CDATA[ 35.5 ]]>
                    </weeklyHours>
                    <city>
                        <![CDATA[ Toulouse ]]>
                    </city>
                    <coordinate>
                        <![CDATA[ 43.604652;1.444209 ]]>
                    </coordinate>
                    <postalCode>
                        <![CDATA[ 31000 ]]>
                    </postalCode>
                    <company>
                        <![CDATA[ My company ]]>
                    </company>
                    <country>
                        <![CDATA[ France ]]>
                    </country>
                    <dateFirstPublished>
                        <![CDATA[ 2023-02-23 ]]>
                    </dateFirstPublished>
                    <status>
                        <![CDATA[ PUBLISHED ]]>
                    </status>
                    <companyId>
                        <![CDATA[ 123456 ]]>
                    </companyId>
                </job>
            </jobs>
            """;

    @Test
    void taleezOffersDeserialization() {
        var softyJobXmlParser = new GenericJobXmlParser<>(TaleezJob.class, "/jobs/job", "taleez");
        var jobs = softyJobXmlParser.parseJobs(ALL_XML, new AtsGetOfferConfig());
        assertThat(jobs).hasSize(1);
        assertJsonSerializedObjectMatchesContent(jobs.getContent().getFirst(), "taleezExternalOffer.json");

    }
}
