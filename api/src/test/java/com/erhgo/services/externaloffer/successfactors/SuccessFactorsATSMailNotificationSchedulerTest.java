package com.erhgo.services.externaloffer.successfactors;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.CandidatureSynchronizationState;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.services.externaloffer.candidature.DefaultMailNotificationScheduler;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailNotifier;
import com.erhgo.services.userprofile.FilePartProvider;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import jakarta.persistence.EntityManager;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Answers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.time.OffsetDateTime;

import static java.util.Set.of;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;

class SuccessFactorsATSMailNotificationSchedulerTest extends AbstractIntegrationTest {

    @Autowired
    ApplicationContext applicationContext;

    @MockitoBean
    MailNotifier mailNotifier;

    @MockitoBean(answers = Answers.RETURNS_MOCKS)
    UserProfileCompetencesExportService userProfileCompetencesExportService;

    @Autowired
    DefaultMailNotificationScheduler successFactorsATSMailNotificationScheduler;

    @Test
    @ResetDataAfter
    void ensureSFCandidatureAreSent() {
        var orgaCode = "S-042";
        txHelper.doInTransaction(() -> {
            var sql = """
                    INSERT INTO PerOfferATSConfigurationItem(id, atsCode, configCode, remoteRecruiterCode, locationCode, recruiterCode)
                    VALUES  (uuidToBin('19c6cd67-d4ff-40aa-97de-72e57a942daa'), 'SUCCESS_FACTORS', 'KEOLIS', '', '03', '%s');
                    """.formatted(orgaCode);
            applicationContext.getBean(EntityManager.class).createNativeQuery(sql).executeUpdate();
        });
        var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(
                        applicationContext.getBean(UserProfileMotherObject.class)
                                .withUserId("f3f2e850-b5d4-11ef-ac7e-96584d5248b2")
                                .withEmail("<EMAIL>")
                                .withFirstname("fn")
                                .withLastname("ln")
                                .withPhoneNumber("0123")
                                .withLocation(Location.builder().city("Pau").postcode("42000").build())
                                .withExperienceOnOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist())
                                .buildAndPersist()
                )
                .withRecruiterCode(orgaCode)
                .withATSCode("SUCCESS_FACTORS")
                .withState(GlobalCandidatureState.NEW)
                .withCandidatureSynchronizationState(CandidatureSynchronizationState.WAITING)
                .withSubmissionDate(OffsetDateTime.now().minusHours(5))
                .buildAndPersist();

        Mockito.when(sourcingKeycloakService.getEnabledSourcingUsersForGroup(orgaCode)).thenReturn(of(
                new UserRepresentation().setId("42").setEmail("<EMAIL>"),
                new UserRepresentation().setId("4242").setEmail("<EMAIL>")
        ));

        successFactorsATSMailNotificationScheduler.handleNewCandidatures();

        txHelper.doInTransaction(() -> {
            var upToDateCandidature = applicationContext.getBean(RecruitmentCandidatureRepository.class).findById(candidature.getId()).orElseThrow();
            Assertions.assertThat(upToDateCandidature.getSynchronizationState()).isEqualTo(CandidatureSynchronizationState.DONE);
            Mockito.verify(mailNotifier).sendMail(eq(of("<EMAIL>")), anyString(), anyString(), eq(false), anyString(), anyString(), Mockito.any(FilePartProvider.class), Mockito.any(FilePartProvider.class));
        });

    }
}
