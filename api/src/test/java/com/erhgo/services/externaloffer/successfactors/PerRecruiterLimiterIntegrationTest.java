package com.erhgo.services.externaloffer.successfactors;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.generators.RecruiterMotherObject;
import com.erhgo.openapi.dto.AtsOfferSimulatedDTO;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.externaloffer.ExternalOfferRecruitmentService;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import com.erhgo.services.externaloffer.recruiterdispatcher.PerOfferATSConfigurationItem;
import com.erhgo.services.externaloffer.recruiterdispatcher.PerOfferATSConfigurationItemRepository;
import jakarta.persistence.EntityManager;
import org.assertj.core.api.Assertions;
import org.intellij.lang.annotations.Language;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.UUID;
import java.util.stream.IntStream;

class PerRecruiterLimiterIntegrationTest extends AbstractIntegrationTest {

    @Autowired
    ApplicationContext applicationContext;

    @MockitoBean
    ExternalOfferRecruitmentService externalOfferRecruitmentService;

    @Language("XML")
    private static final String ALL_XML = """
            <?xml version="1.0" encoding="UTF-8" ?>
            <source>
                <job>
                    <country>FR</country>
                    <title>Offre 1 - out conf par orga</title>
                    <date>Mon, 22 02 2021 00:00:00 GMT</date>
                    <id>1</id>
                    <city>A</city>
                    <location>69000</location>
                    <contract_type>CDD</contract_type>
                    <description>D1</description>
                </job>
                <job>
                    <country>FR</country>
                    <title>Offre 2 - in</title>
                    <date>Mon, 22 02 2021 00:00:00 GMT</date>
                    <id>2</id>
                    <city>A</city>
                    <location>69001</location>
                    <contract_type>CDD</contract_type>
                    <description>D2</description>
                </job>
                <job>
                    <country>FR</country>
                    <title>Offre 3 - out trop par défaut</title>
                    <date>Mon, 22 02 2021 00:00:00 GMT</date>
                    <id>3</id>
                    <city>A</city>
                    <location>69001</location>
                    <contract_type>CDD</contract_type>
                    <description>D3</description>
                </job>
                <job>
                    <country>FR</country>
                    <title>Offre 4 - in</title>
                    <date>Mon, 22 02 2021 00:00:00 GMT</date>
                    <id>4</id>
                    <city>A</city>
                    <location>69002</location>
                    <contract_type>CDD</contract_type>
                    <description>D4</description>
                </job>
                <job>
                    <country>FR</country>
                    <title>Offre 5 - in</title>
                    <date>Mon, 22 02 2021 00:00:00 GMT</date>
                    <id>5</id>
                    <city>A</city>
                    <location>69002</location>
                    <contract_type>CDD</contract_type>
                    <description>D5</description>
                </job>
                <job>
                    <country>FR</country>
                    <title>Offre 6 - out conf par code orga</title>
                    <date>Mon, 22 02 2021 00:00:00 GMT</date>
                    <id>6</id>
                    <city>A</city>
                    <location>69002</location>
                    <contract_type>CDD</contract_type>
                    <description>D6</description>
                </job>
            </source>
            """;

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void ensureLimitePerRecruiter() {
        txHelper.doInTransaction(() -> {
            var sql = """
                    INSERT INTO ConfigurableProperty(propertyKey, propertyValue) VALUES
                            ('ats.limit.per-recruiter.SUCCESS_FACTORS--KEOLIS.default', '1'),
                            ('ats.limit.per-recruiter.SUCCESS_FACTORS--KEOLIS.S-02', '2'),
                            ('ats.limit.per-recruiter.SUCCESS_FACTORS--KEOLIS.S-00', '0')
                    ON DUPLICATE KEY UPDATE propertyValue = VALUES(propertyValue)
                    """;
            applicationContext.getBean(EntityManager.class).createNativeQuery(sql).executeUpdate();
        });

        applicationContext.getBean(PerOfferATSConfigurationItemRepository.class).saveAll(
                IntStream.range(0, 3).mapToObj(i -> new PerOfferATSConfigurationItem(
                        UUID.randomUUID(),
                        "SUCCESS_FACTORS",
                        "KEOLIS",
                        "",
                        "6900%d".formatted(i),
                        applicationContext.getBean(RecruiterMotherObject.class)
                                .withOrganizationType(AbstractOrganization.OrganizationType.SOURCING)
                                .withCode("S-0%d".formatted(i))
                                .buildAndPersist()
                                .getCode()
                )).toList());

        applicationContext.getBean(ExternalOfferServiceProvider.class).getAllServicesScheduledHavingSecondPass().stream().filter(s -> s.getAtsCode().equals("SUCCESS_FACTORS") && s.getAtsConfig().getConfigCode().equals("KEOLIS")).findFirst().orElseThrow().simulateAtsOffer(new AtsOfferSimulatedDTO().rawFlow(ALL_XML));

        txHelper.doInTransaction(() -> {
            var offers = applicationContext.getBean(ExternalOfferRepository.class).findAll();
            Assertions.assertThat(offers).hasSize(3);
            Assertions.assertThat(offers).filteredOn(o -> o.getComputedRecruiterCode().equals("S-01")).hasSize(1);
            Assertions.assertThat(offers).filteredOn(o -> o.getComputedRecruiterCode().equals("S-02")).hasSize(2);
        });


    }

}
