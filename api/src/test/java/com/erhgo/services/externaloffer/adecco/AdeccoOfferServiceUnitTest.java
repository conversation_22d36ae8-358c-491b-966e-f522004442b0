package com.erhgo.services.externaloffer.adecco;

import com.erhgo.openapi.dto.TypeContractCategoryDTO;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.parsers.GenericJobXmlParser;
import org.intellij.lang.annotations.Language;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import static org.assertj.core.api.Assertions.assertThat;

class AdeccoOfferServiceUnitTest {
    @Language("XML")
    static final String XML = """
            <item>
                <title>
                    <![CDATA[ Conseiller Contrats Collectifs (h/f) ]]>
                </title>
                <metierclient>
                    <![CDATA[ Conseiller Contrats Collectifs (h/f) ]]>
                </metierclient>
                <link>
                    <![CDATA[ https://www.adecco.fr/offres-d-emploi/conseiller-contrats-collectifs-hf-lyon-06?ID=ebaf9c13-a7d3-453e-a3bb-39dd5b841d3f ]]>
                </link>
                <linkjb>
                    <![CDATA[ https://candidat.adecco.fr/apply?OF=17641997 ]]>
                </linkjb>
                <description>
                    <![CDATA[ <div class="VacancyDescription"><p><h2 class="offre-mission">Votre mission</h2><p></br>L&#39;agence Adecco Tertiaire recrute pour son client, spécialisé dans le domaine de Gestion des retraites complémentaires et basé à <strong>Lyon 3e Arrondissement </strong>(69003),en <strong>CDD de 4 à 6 mois</strong> un <strong>Conseiller Contrats Collectifs (h/f)</strong>.</br><br></br>Notre client est une entreprise renommée dans le secteur de la gestion des retraites complémentaires. Elle se distingue par son expertise et son engagement envers ses clients. Rejoindre notre client, c&#39;est intégrer une équipe dynamique et passionnée, et contribuer activement au développement de l&#39;entreprise.</br><br></br>En tant que Conseiller Contrats Collectifs (h/f), vous jouerez un rôle clé dans le succès du développement de l&#39;entreprise. </br><br></br><u>Vos missions comprendront :</u></br><br></br>&#128188; <strong>Souscription/Affiliation</strong> : Évaluation de la recevabilité des demandes d&#39;adhésion, gestion des pré-adhésions, acceptation médicale de premier niveau, émission des contrats et avenants d&#39;adhésion, mise à jour des données des entreprises clientes, affiliation, mise à jour et radiation des salariés et bénéficiaires.</br><br></br>&#128202; <strong>Ajustement Annuel et Analyse Trimestrielle :</strong> Traitement des déclarations annuelles de salaires, analyse trimestrielle des cotisations, établissement de l&#39;ajustement annuel du compte client, traitement des éléments complémentaires et rectificatifs.</br><br></br>&#128176; <strong>Recouvrement :</strong> Établissement de l&#39;appel de cotisation trimestriel, gestion des opérations d&#39;encaissement de cotisations complexes, établissement des attestations de paiement, tenue de compte, traitement des règlements impayés et remboursement des cotisations des soldes créditeurs.</br><br></br>&#128221; <strong>Suivi de Primes :</strong> Gestion des demandes de délais de paiement, actions de pré-contentieux, préparation et transfert des dossiers non réglés au service Contentieux, encaissement des cotisations de type pré-contentieux.</p><h2 class="offre-profil">Votre profil</h2><p></br><u>Profil :</u></br><br></br>Nous recherchons un candidat ayant au moins 1 an d&#39;expérience dans un poste similaire, avec un niveau d&#39;études minimum BAC+2. </br><br></br>Vous êtes reconnu pour votre excellent sens de la communication, votre orientation client et votre capacité à travailler en équipe. </br>Vous êtes rigoureux, organisé et savez gérer votre temps efficacement. </br>Vos compétences en négociation et rédaction de contrats ainsi que votre connaissance des produits d&#39;assurance collective seront des atouts pour réussir dans ce poste.</br><br><ul><li>Compétences en communication</li><li>Orientation client</li><li>Capacité à travailler en équipe</li><li>Résolution de problèmes</li><li>Maîtrise des réglementations relatives aux contrats d&#39;assurance collectifs</li><li>Compétences en négociation et rédaction de contrats</li><li>Capacité à analyser et interpréter les clauses contractuelles</li></ul></br><br></br>En rejoignant notre client, vous bénéficierez de nombreux avantages tels que le télétravail, la prise en charge des frais de transport, les RTT, les primes, les tickets restaurants, la participation, l&#39;intéressement et le 13ème mois.</br><br></br>La rémunération si situe entre 26000€ et 27000€ brut par an</br><br></br>Le poste est à pourvoir dès que possible en journée à temps plein.</br><br></br>Le processus de recrutement comprendra un entretien physique avec le client.</br><br></br>Vous êtes passionné par le domaine de la gestion des retraites complémentaires ? Vous souhaitez rejoindre une entreprise dynamique et innovante ? Alors n&#39;hésitez plus et postulez dès maintenant !</br><br></br>Adecco s&#39;engage à promouvoir la diversité et l&#39;égalité des chances. Toute information liée à la candidature est traitée avec la plus stricte confidentialité.</p><h2 class="offre-proposition">A propos de nous</h2><p>Premier réseau d&#39;agences d&#39;emploi en France, Adecco a développé un savoir-faire unique de proximité et met toutes ses compétences à votre service.<br>Nos équipes sont présentes sur tout le territoire, avec plus de 900 agences. Quel que soit le contrat que vous cherchez : CDI, CDD, Intérim, CDI Intérimaire, CDI Apprenant ou alternance, nos experts travaillent chaque jour, pour vous guider vers ce qui vous correspond. Dès maintenant, devenez acteur de votre vie !</p></p></div> ]]>
                </description>
                <ContractType>CDD</ContractType>
                <PostalCode>69006</PostalCode>
                <Town>Lyon 06</Town>
                <Country>France</Country>
                <State>Rhône</State>
                <PublicationDate>2024-06-19T09:38:16Z</PublicationDate>
                <StartContractDate>2024-06-19T00:00:00Z</StartContractDate>
                <SalaryMin>28000,0</SalaryMin>
                <SalaryMax>29000,0</SalaryMax>
                <branchCode>049</branchCode>
                <branchLocation>
                    <State>Auvergne-Rhône-Alpes</State>
                    <Town>Lyon</Town>
                    <PostalCode>69003</PostalCode>
                    <branchaddress>17 Rue Etienne Dolet</branchaddress>
                </branchLocation>
                <ActivityDomainId>A</ActivityDomainId>
                <ActivityDomainTitle>Accueil - Secrétariat - Fonctions administratives</ActivityDomainTitle>
                <JobCategoryId>A.03</JobCategoryId>
                <JobCategoryTitle>Autres Fonctions Administratives</JobCategoryTitle>
                <guid>A3607000002GU2JAAQ</guid>
                <Origin>ADECCO</Origin>
                <jobOfferRef>17641997</jobOfferRef>
                <VacancyStartAsap>true</VacancyStartAsap>
            </item>
            """;

    @Language("XML")
    static final String WRAPPER = """
            ﻿<?xml version="1.0" encoding="utf-8"?><rss version="2.0" xmlns:a10="http://www.w3.org/2005/Atom"><channel>%s</channel></rss>Je n'ai rien à faire ici
            """;


    @Test
    void AdeccoOfferDeserializations() {
        var adeccoJobXmlParser = new GenericJobXmlParser<>(AdeccoJob.class, "/rss/channel/item", "adecco");
        var jobs = adeccoJobXmlParser.parseJobs(WRAPPER.formatted(XML), new AtsGetOfferConfig());
        assertThat(jobs).hasSize(1);
    }

    @Test
    void AdeccoOfferDeserialization() {
        var adeccoJobXmlParser = new GenericJobXmlParser<>(AdeccoJob.class, "/rss/channel/item", "adecco");
        var job = adeccoJobXmlParser.parseJob(XML);
        assertThat(job.getId()).isEqualTo("17641997");
        assertThat(job.getSalaryMin()).isEqualTo(28000);
        assertThat(job.getSalaryMax()).isEqualTo(29000);
        assertThat(job.getTypeContractCategory()).isEqualTo(TypeContractCategoryDTO.TEMPORARY);
        assertThat(job.getLocationIndication()).isEqualTo("Lyon 06 (69006)");
        assertThat(job.getOfferTitle()).isEqualTo("Conseiller Contrats Collectifs (h/f)");
    }

    @ParameterizedTest
    @CsvSource({
            "'22000', 22000",
            "'2000', 24000",
            "'1200,1400', 14400",
            "'invalid,1000', 0",
            "'', 0",
            "null, 0"
    })
    void testGetSalaryMin(String input, int expected) {
        assertThat(expected).isEqualTo(AdeccoJob.getSalaryMin(input));
    }

    @ParameterizedTest
    @CsvSource({
            "'1200,4500', '', 14400",
            "'1400', '1200', 16800",
            "'invalid', '5000,2000', 24000",
            "'', '', 0",
            "null, '5000,2000', 24000",
            "null, '', 0"
    })
    void testGetSalaryMax(String salaryMax, String salaryMin, int expected) {
        assertThat(expected).isEqualTo(AdeccoJob.getSalaryMax(salaryMax, salaryMin));
    }
}
