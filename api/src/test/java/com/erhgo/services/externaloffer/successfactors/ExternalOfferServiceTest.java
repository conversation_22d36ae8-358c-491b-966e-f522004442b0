package com.erhgo.services.externaloffer.successfactors;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.externaloffer.ExternalOfferMotherObject;
import com.erhgo.domain.externaloffer.RecruitmentCreationState;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.utils.EventPublisherUtils;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.externaloffer.ExternalOfferService;
import lombok.SneakyThrows;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEventPublisher;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertThrows;
import static org.mockito.Mockito.verifyNoInteractions;


class ExternalOfferServiceTest extends AbstractIntegrationTest {

    @Autowired
    private ExternalOfferService externalOfferService;

    @Autowired
    private ApplicationContext applicationContext;

    private static final UUID offerId = UUID.randomUUID();

    ApplicationEventPublisher busBefore;
    @Mock
    ApplicationEventPublisher busMock;

    @BeforeEach
    void prepareBus() {
        busBefore = EventPublisherUtils.applicationEventPublisher;
        EventPublisherUtils.resetPublisher(busMock);
        Mockito.reset(busMock);
    }

    @AfterEach
    void resetBus() {
        EventPublisherUtils.resetPublisher(busBefore);
    }


    @SneakyThrows
    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void ignoreExternalOfferSuccessfully() {

        createExternalOffer(RecruitmentCreationState.MANUAL);

        externalOfferService.ignoreExternalOffer(offerId);
        txHelper.doInTransaction(() -> {
            var offer = applicationContext.getBean(ExternalOfferRepository.class).findById(offerId);
            assertThat(offer).isPresent();
            assertThat(offer.get().getRecruitmentCreationState()).isEqualTo(RecruitmentCreationState.IGNORE);
        });
    }


    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void throwExceptionWhenOfferNotFoundForIgnore() {
        Assertions.assertThrows(EntityNotFoundException.class, () -> externalOfferService.ignoreExternalOffer(offerId));
    }

    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void throwExceptionWhenTryingToIgnoreOfferWithRecruitment() {
        createExternalOfferWithRecruitment();
        assertThrows(GenericTechnicalException.class, () -> externalOfferService.ignoreExternalOffer(offerId));
    }

    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void integrateExternalOfferSuccessfullyFromManualState() {
        createExternalOffer(RecruitmentCreationState.MANUAL);
        externalOfferService.integrateExternalOffer(offerId);

        txHelper.doInTransaction(() -> {
            var offer = applicationContext.getBean(ExternalOfferRepository.class).findById(offerId);
            assertThat(offer).isPresent();
            assertThat(offer.get().getRecruitmentCreationState()).isEqualTo(RecruitmentCreationState.PROCESSING);
        });
    }

    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void integrateExternalOfferFromIgnoreState() {
        createExternalOffer(RecruitmentCreationState.IGNORE);
        externalOfferService.integrateExternalOffer(offerId);

        txHelper.doInTransaction(() -> {
            var offer = applicationContext.getBean(ExternalOfferRepository.class).findById(offerId);
            assertThat(offer).isPresent();
            assertThat(offer.get().getRecruitmentCreationState()).isEqualTo(RecruitmentCreationState.PROCESSING);
        });
    }

    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void throwExceptionWhenOfferNotFoundForIntegrate() {
        Assertions.assertThrows(EntityNotFoundException.class, () -> externalOfferService.integrateExternalOffer(offerId));
    }


    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void throwExceptionWhenOfferIsNotInManualOrIgnoreState() {
        createExternalOffer(RecruitmentCreationState.DONE);

        assertThrows(GenericTechnicalException.class, () -> externalOfferService.integrateExternalOffer(offerId));
        txHelper.doInTransaction(() -> {
            verifyNoInteractions(busMock);
        });
    }

    private void createExternalOffer(RecruitmentCreationState recruitmentCreationState) {
        applicationContext.getBean(ExternalOfferMotherObject.class)
                .withUuid(offerId.toString())
                .withRecruitmentCreationState(recruitmentCreationState)
                .buildAndPersist();
    }

    private void createExternalOfferWithRecruitment() {
        applicationContext.getBean(ExternalOfferMotherObject.class)
                .withUuid(offerId.toString())
                .withRecruitment(applicationContext.getBean(RecruitmentMotherObject.class).buildAndPersist())
                .buildAndPersist();
    }
}
