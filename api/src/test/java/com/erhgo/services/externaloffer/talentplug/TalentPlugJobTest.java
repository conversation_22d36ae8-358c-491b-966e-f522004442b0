package com.erhgo.services.externaloffer.talentplug;

import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.parsers.GenericJobXmlParser;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.intellij.lang.annotations.Language;
import org.junit.jupiter.api.Test;

import static com.erhgo.TestUtils.assertJsonSerializedObjectMatchesContent;

class TalentPlugJobTest {

    @Language("xml")
    String xml = """
            <offers>
                <offer>
                    <offer_keyid><![CDATA[22418381]]></offer_keyid>
                    <posting_date><![CDATA[2024-09-26]]></posting_date>
                    <update_date><![CDATA[2024-09-25]]></update_date>
                    <expiration_date><![CDATA[2024-12-25]]></expiration_date>
                    <posting_command><![CDATA[add]]></posting_command>
                    <job_reference><![CDATA[Employé Polyvalent ]]></job_reference>
                    <job_title><![CDATA[Employé Polyvalent H/F]]></job_title>
                    <company_name><![CDATA[AREAS]]></company_name>
                    <company_description>
                        <![CDATA[Areas est la référence n°1 en France dans le secteur de la restauration de Voyage! Que ce soit dans les gares, les autoroutes, les aéroports ou les centres de loisirs.]]></company_description>
                    <job_description>
                        <![CDATA[Nous recherchons pour le Columbus café et l'hôtel Ibis un(e) employé(e) polyvalent(e)<br /><br />Horaires en journée complète pas d'horaires de coupures<br /><br />Lieu du poste : En présentiel]]></job_description>
                    <applicant_profile><![CDATA[Dynamique/ Travail en équipe/ Solidaire ]]></applicant_profile>
                    <applicant_experience><![CDATA[0-1 an]]></applicant_experience>
                    <applicant_degree><![CDATA[BEP/CAP]]></applicant_degree>
                    <job_function><![CDATA[Hôtellerie, Restauration & Tourisme]]></job_function>
                    <job_industry><![CDATA[Restauration]]></job_industry>
                    <location_administrativearea><![CDATA[Auvergne-Rhône-Alpes]]></location_administrativearea>
                    <location_subadministrativearea><![CDATA[01 - Ain]]></location_subadministrativearea>
                    <location_zipcode><![CDATA[01250]]></location_zipcode>
                    <location_town><![CDATA[Jasseron]]></location_town>
                    <location_country><![CDATA[France]]></location_country>
                    <job_type><![CDATA[Temps plein]]></job_type>
                    <job_contract><![CDATA[CDD]]></job_contract>
                    <job_duration><![CDATA[40]]></job_duration>
                    <type_horaire><![CDATA[Travail le samedi]]></type_horaire>
                    <salary_min><![CDATA[13]]></salary_min>
                    <salary_max><![CDATA[13]]></salary_max>
                    <salary_periode><![CDATA[HEURE]]></salary_periode>
                    <salary_benefits><![CDATA[]]></salary_benefits>
                    <teletravail><![CDATA[Non]]></teletravail>
                    <application_email><![CDATA[<EMAIL>]]></application_email>
                    <application_email_alternatif><![CDATA[]]></application_email_alternatif>
                </offer>
            </offers>
            """;

    @SneakyThrows
    @Test
    void TPOfferDeserialization() {
        var tpXmlParser = new GenericJobXmlParser<>(TalentPlugJob.class, "/offers/offer", "TALENT_PLUG");
        var jobs = tpXmlParser.parseJobs(xml, new AtsGetOfferConfig());
        Assertions.assertThat(jobs).hasSize(1);
        assertJsonSerializedObjectMatchesContent(jobs.getContent().getFirst(), "tpExternalOffer.json");
    }
}
