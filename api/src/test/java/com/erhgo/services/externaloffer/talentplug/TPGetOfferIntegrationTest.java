package com.erhgo.services.externaloffer.talentplug;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.externaloffer.ExternalOfferEventType;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.externaloffer.ExternalOfferRecruitmentService;
import com.erhgo.services.externaloffer.GenericAtsClient;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import org.assertj.core.api.Assertions;
import org.intellij.lang.annotations.Language;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.Optional;

class TPGetOfferIntegrationTest extends AbstractIntegrationTest {
    public static final String ATS_CODE = "TALENT_PLUG";
    public static final String CONFIG_CODE = "AREAS";
    @MockitoBean
    GenericAtsClient atsClient;

    @MockitoBean
    ExternalOfferRecruitmentService externalOfferRecruitmentService;

    @Language("XML")
    private static final String OFFERS = """
            <offers>
                <offer>
                    <offer_keyid><![CDATA[22418381]]></offer_keyid>
                    <posting_date><![CDATA[2024-09-26]]></posting_date>
                    <update_date><![CDATA[2024-09-25]]></update_date>
                    <expiration_date><![CDATA[2024-12-25]]></expiration_date>
                    <posting_command><![CDATA[add]]></posting_command>
                    <job_reference><![CDATA[Employé Polyvalent ]]></job_reference>
                    <job_title><![CDATA[Employé Polyvalent H/F]]></job_title>
                    <company_name><![CDATA[AREAS]]></company_name>
                    <company_description>
                        <![CDATA[Areas est la référence n°1 en France dans le secteur de la restauration de Voyage! Que ce soit dans les gares, les autoroutes, les aéroports ou les centres de loisirs, nous travaillons avec des enseignes renommées telles que McDonald's, Starbucks, Paul, Maison Pradier et bien d'autres.<br /><br />En intégrant notre groupe, tu auras la possibilité de travailler pour plus de 60 enseignes dans plus de 550 points de vente présents dans toute la France. Nous sommes aussi présents dans 10 pays dans le monde.]]></company_description>
                    <job_description>
                        <![CDATA[Nous recherchons pour le Columbus café et l'hôtel Ibis un(e) employé(e) polyvalent(e)<br /><br />Horaires en journée complète pas d'horaires de coupures<br /><br />Vous serez formé(e) à la vente du Columbus café et préparation de cafés mais aussi préparation des sandwichs ainsi qu'en réception hôtel<br /><br />Un métier riche et polyvalent au sein d'une équipe sympa et dynamique<br /><br />Perspective d'évolution selon compétences au sein du site ou au sein du groupe<br /><br />De nombreux avantages au bout d'un an d'ancienneté tels que 13ème mois / chèques vacances / prime de transport<br /><br />Le repas est fournit par l'entreprise pour chaque journée travaillée. (possibilité de manger au Courtepaille de Jasseron)<br /><br />Les horaires peuvent être: 7h-15h30 // 11h-19h // 15h-23h<br /><br />3 Dimanches travaillés sur 4 majorés à +20% payés en fin de mois<br /><br />Heures supplémentaires majorées payées à +10% payées en fin de mois et compteur modulation pour temps de récupération.<br /><br />Heures de soir majorées à +10% entre 19h et 22h et à +15% à partir de 22h payées également en fin de mois<br /><br />Plannings de travail établit au minimum à 4 semaines d'avance<br /><br />Possibilité de poser souhait de repos avant montage des plannings<br /><br />Type d'emploi : Temps plein, CDI<br /><br />Rémunération : à partir de 11,52EUR par heure<br /><br />Horaires :<br /><br />Travail en journée<br />Rémunération supplémentaire :<br /><br />13ème Mois<br />Heures supplémentaires majorées<br />Pourboires<br />Prime annuelle<br />Primes<br />Lieu du poste : En présentiel]]></job_description>
                    <applicant_profile><![CDATA[Dynamique/ Travail en équipe/ Solidaire ]]></applicant_profile>
                    <applicant_experience><![CDATA[0-1 an]]></applicant_experience>
                    <applicant_degree><![CDATA[BEP/CAP]]></applicant_degree>
                    <job_function><![CDATA[Hôtellerie, Restauration & Tourisme]]></job_function>
                    <job_industry><![CDATA[Restauration]]></job_industry>
                    <location_administrativearea><![CDATA[Auvergne-Rhône-Alpes]]></location_administrativearea>
                    <location_subadministrativearea><![CDATA[01 - Ain]]></location_subadministrativearea>
                    <location_zipcode><![CDATA[01250]]></location_zipcode>
                    <location_town><![CDATA[Jasseron]]></location_town>
                    <location_country><![CDATA[France]]></location_country>
                    <job_type><![CDATA[Temps plein]]></job_type>
                    <job_contract><![CDATA[CDD]]></job_contract>
                    <job_duration><![CDATA[40]]></job_duration>
                    <type_horaire><![CDATA[Travail le samedi]]></type_horaire>
                    <salary_min><![CDATA[13]]></salary_min>
                    <salary_max><![CDATA[13]]></salary_max>
                    <salary_periode><![CDATA[HEURE]]></salary_periode>
                    <salary_benefits><![CDATA[]]></salary_benefits>
                    <teletravail><![CDATA[Non]]></teletravail>
                    <application_email><![CDATA[<EMAIL>]]></application_email>
                    <application_email_alternatif><![CDATA[]]></application_email_alternatif>
                </offer>
                <offer>
                    <offer_keyid><![CDATA[22418380]]></offer_keyid>
                    <posting_date><![CDATA[2724-09-26]]></posting_date>
                    <update_date><![CDATA[2024-09-30]]></update_date>
                    <expiration_date><![CDATA[2024-12-25]]></expiration_date>
                    <posting_command><![CDATA[add]]></posting_command>
                    <job_reference><![CDATA[AUT-Opérateur de station]]></job_reference>
                    <job_title><![CDATA[Opérateur de station-service F/H]]></job_title>
                    <company_name><![CDATA[AREAS]]></company_name>
                    <company_description>
                        <![CDATA[Areas est la référence n°1 en France dans le secteur ]]></company_description>
                    <job_description>
                        <![CDATA[Au menu... des missions qui mêlent esprit d'équipe et contact clients]]></job_description>
                    <applicant_profile>
                        <![CDATA[Pour nous rejoindre, l'envie d'apprendre suffit. ]]></applicant_profile>
                    <applicant_experience><![CDATA[0-1 an]]></applicant_experience>
                    <applicant_degree><![CDATA[BEP/CAP]]></applicant_degree>
                    <job_function><![CDATA[Commercial / Vente]]></job_function>
                    <job_industry><![CDATA[Commerce de détail]]></job_industry>
                    <location_administrativearea><![CDATA[Hauts-de-France]]></location_administrativearea>
                    <location_subadministrativearea><![CDATA[80 - Somme]]></location_subadministrativearea>
                    <location_zipcode><![CDATA[80200]]></location_zipcode>
                    <location_town><![CDATA[Assevillers]]></location_town>
                    <location_country><![CDATA[France]]></location_country>
                    <job_type><![CDATA[Temps plein]]></job_type>
                    <job_contract><![CDATA[Intérim]]></job_contract>
                    <job_duration><![CDATA[60]]></job_duration>
                    <type_horaire><![CDATA[Travail de nuit]]></type_horaire>
                    <salary_min><![CDATA[1700]]></salary_min>
                    <salary_max><![CDATA[1800]]></salary_max>
                    <salary_periode><![CDATA[MOIS]]></salary_periode>
                    <salary_benefits><![CDATA[Divers avantages sociaux proposés]]></salary_benefits>
                    <teletravail><![CDATA[Non]]></teletravail>
                    <application_email><![CDATA[<EMAIL>]]></application_email>
                    <application_email_alternatif><![CDATA[]]></application_email_alternatif>
                </offer>
            </offers>
            """;

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void created_events_are_fired_ignoring_too_soon_offers() {
        Mockito.when(atsClient.fetch(ArgumentMatchers.any(), ArgumentMatchers.eq(Optional.empty()))).thenReturn(OFFERS);

        applicationContext.getBean(ExternalOfferServiceProvider.class).getService(new AtsGetOfferConfig().setAtsCode("TALENT_PLUG").setConfigCode("AREAS")).fetchAndUpdateOffers();

        txHelper.doInTransaction(() -> {
            var allOffers = applicationContext.getBean(ExternalOfferRepository.class).findAll();
            Assertions.assertThat(allOffers)
                    .hasSize(1)
                    .allMatch(a -> a.getLastEventType() == ExternalOfferEventType.CREATED)
                    .allMatch(a -> a.getRemoteId().equals("22418381"))
            ;
        });


    }

}
