package com.erhgo.services.externaloffer.successfactors;

import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import lombok.SneakyThrows;
import org.intellij.lang.annotations.Language;
import org.junit.jupiter.api.Test;

import static com.erhgo.TestUtils.assertJsonSerializedObjectMatchesContent;
import static org.assertj.core.api.Assertions.assertThat;

class SuccessFactorsJobXmlParserTest {

    private SuccessFactorsJobParser parser = new SuccessFactorsJobParser(new AtsGetOfferConfig().setAtsCode("SUCCESS_FACTORS").setRootPath("/source/job"));

    @Language("XML")
    private static final String ALL_XML = """
            <?xml version="1.0" encoding="UTF-8" ?>
            <source>
                <publisher>Keolis</publisher>
                <publisherurl>careers.keolis.com</publisherurl>
                <job>
                    <title>Apprenti Mécanicien Bus F/H</title>
                    <date>Tue, 02 04 2024 00:00:00 GMT</date>
                    <id>78439-fr_FR</id>
                    <apply_url>https://careers.keolis.com/IDF/job...</apply_url>
                    <apply_mode>1</apply_mode>
                    <company>Keolis</company>
                    <city>Vélizy</city>
                     <state></state>
                    <country>FR</country>
                    <postal_code>78140</postal_code>
                    <location>Vélizy, FR, 78140</location>
                    <industry></industry>
                    <category>Maintenance</category>
                    <contract_type>Alternance</contract_type>
                    <description><html>Une description de plus de 512 chars Une description de plus de 512 chars Une description de plus de 512 chars Une description de plus de 512 chars Une description de plus de 512 chars Une description de plus de 512 chars Une description de plus de 512 chars Une description de plus de 512 chars Une description de plus de 512 chars Une description de plus de 512 chars Une description de plus de 512 chars Une description de plus de 512 chars Une description de plus de 512 chars Une description de plus de 512 chars Une description de plus de 512 chars Une description de plus de 512 chars</html></description>
                </job>
                <job>
                    <title>Apprenti Mecanicien Bus F/H</title>
                    <date>Tue, 02 04 2024 00:00:00 GMT</date>
                    <id>78426-fr_FR</id>
                    <apply_url>https://careers.keolis.com/IDF/?feedId=393933&utm_campaign=Keol</apply_url>
                    <apply_mode>1</apply_mode>
                    <company>Keolis</company>
                    <city>Draveil</city>
                    <state/>
                    <country>FR</country>
                    <postal_code>91210</postal_code>
                    <location>Draveil, FR, 91210</location>
                    <industry/>
                    <category>Maintenance</category>
                    <contract_type>CDD</contract_type>
                    <description>&lt;p style=&quot;text-align:center&quot;&gt;&lt;span id=&quot;cke_bm_231S&quot; style=&quot;display:none&quot;&gt;&#160;&lt;/span&gt;</description>
                </job>
                <job>
                    <title>Apprenti mécanicien bus (F/H)</title>
                    <date>Tue, 02 04 2024 00:00:00 GMT</date>
                    <id>78440-fr_FR</id>
                    <apply_url>https://careers.keolis.com/IDF/job/?feedId=393933&utm_campaign=Keol</apply_url>
                    <apply_mode>1</apply_mode>
                    <company>Keolis</company>
                    <city>Goussainville</city>
                    <state/>
                    <country>FR</country>
                    <postal_code>95190</postal_code>
                    <location>Goussainville, FR, 95190</location>
                    <industry/>
                    <category>Maintenance</category>
                    <contract_type>Stage</contract_type>
                    <description>D4</description>
                </job>
                <job>
                    <title>Apprenti mécanicien bus (F/H)</title>
                    <date>Tue, 02 04 2024 00:00:00 GMT</date>
                    <id>78438-fr_FR</id>
                    <apply_url>https://careers.keolis.com/IDF/job...</apply_url>
                    <apply_mode>1</apply_mode>
                    <company>Keolis</company>
                    <city>Bernes sur Oise</city>
                    <state/>
                    <country>FR</country>
                    <postal_code>69620</postal_code>
                    <location>Bernes sur Oise, FR, 69620</location>
                    <industry/>
                    <category>Maintenance</category>
                    <contract_type>Alternance</contract_type>
                    <description>D5</description>
                </job>
            </source>
            """;


    @Test
    void parseOnlyContractNotStageOrVie() {
        var xmlContent = """
                <?xml version="1.0" encoding="UTF-8"?>
                <source>
                    <job>
                        <country>FR</country>
                        <contract_type>CDI</contract_type>
                    </job>
                    <job>
                        <country>FR</country>
                        <contract_type>CDD</contract_type>
                    </job>
                    <job>
                        <country>DE</country>
                        <contract_type>CDI</contract_type>
                    </job>
                    <job>
                        <country>FR</country>
                        <contract_type>Stage</contract_type>
                    </job>
                </source>
                """;

        var jobs = parser.parseJobs(xmlContent, new AtsGetOfferConfig());

        assertThat(jobs).hasSize(2)
                .allMatch(c -> c.getContractType().equals("CDD") || c.getContractType().equals("CDI"));
    }

    @Test
    void parseFilterOutAll() {
        var xmlContent = """
                <?xml version="1.0" encoding="UTF-8"?>
                <source>
                    <job>
                        <country>DE</country>
                        <contract_type>CDI</contract_type>
                    </job>
                    <job>
                        <country>FR</country>
                        <contract_type>Stage</contract_type>
                    </job>
                </source>
                """;

        var jobs = parser.parseJobs(xmlContent, new AtsGetOfferConfig());

        assertThat(jobs).isEmpty();
    }

    @Test
    void parseEmptyFIle() {
        var xmlContent = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><source></source>";

        var jobs = parser.parseJobs(xmlContent, new AtsGetOfferConfig());

        assertThat(jobs).isEmpty();
    }

    @Test
    void shouldTolerateInvalidXml() {
        var invalidXml = "{}}";
        assertThat(parser.parseJobs(invalidXml, new AtsGetOfferConfig())).isEmpty();
    }

    @Test
    void ignoreCaseForContractValidation() {
        var xmlContent = """
                <?xml version="1.0" encoding="UTF-8"?>
                <source>
                    <job>
                        <id>1</id>
                        <country>FR</country>
                        <contract_type>CdI</contract_type>
                    </job>
                    <job>
                        <id>2</id>
                        <country>FR</country>
                        <contract_type>StAgE</contract_type>
                    </job>
                    <job>
                        <id>3</id>
                        <country>FR</country>
                        <contract_type>vIe</contract_type>
                    </job>
                </source>
                """;

        var jobs = parser.parseJobs(xmlContent, new AtsGetOfferConfig());

        assertThat(jobs).hasSize(1)
                .allSatisfy(job -> assertThat(job.getId()).isEqualTo("1"));
    }

    @SneakyThrows
    @Test
    void offerDeserialization() {
        // to fetch directly: new GenericAtsClient(new RestTemplateBuilder()).initializeTemplate().fetch("https://careers.keolis.com/feed/393933");
        var jobs = parser.parseJobs(ALL_XML, new AtsGetOfferConfig()).getContent();
        assertJsonSerializedObjectMatchesContent(jobs, "successFactorsExternalOffers.json");
    }


}
