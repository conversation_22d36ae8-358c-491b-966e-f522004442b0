package com.erhgo.services.externaloffer.recruiterdispatcher.fetch;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.generators.RecruiterMotherObject;
import com.erhgo.openapi.dto.LocationDTO;
import com.erhgo.openapi.dto.TypeContractCategoryDTO;
import com.erhgo.openapi.dto.WorkContractDurationUnitDTO;
import com.erhgo.openapi.dto.WorkingTimeDTO;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.OrganizationService;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.PerOfferATSConfigurationItem;
import com.erhgo.services.externaloffer.recruiterdispatcher.PerOfferATSConfigurationItemRepository;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.notifier.messages.ATSRecruiterCreated;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class PerOfferFetcherCreateUnknownRecruiterTest extends AbstractIntegrationTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private PerOfferFetcherCreateUnknownRecruiter perOfferFetcherCreateUnknownRecruiter;

    @Autowired
    private PerOfferATSConfigurationItemRepository perOfferATSConfigurationItemRepository;

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @MockitoBean
    private Notifier notifier;

    private AtsGetOfferConfig atsConfig;
    private MockRemoteOfferContent mockOffer;
    private Recruiter existingRecruiter;
    private String existingRemoteRecruiterCode = "EXISTING_REMOTE_CODE";
    private String newRemoteRecruiterCode = "NEW_REMOTE_CODE";

    @BeforeEach
    void setUp() {
        atsConfig = new AtsGetOfferConfig()
                .setAtsCode("TEST_ATS")
                .setConfigCode("TEST_CONFIG");

        mockOffer = new MockRemoteOfferContent()
                .setRemoteRecruiterTitle("Test Recruiter")
                .setRemoteRecruiterCode(newRemoteRecruiterCode);


        existingRecruiter = transactionTemplate.execute(status -> {
            var recruiter = applicationContext.getBean(RecruiterMotherObject.class)
                    .withTitle("Existing Recruiter")
                    .buildAndPersist();

            perOfferATSConfigurationItemRepository.save(
                    new PerOfferATSConfigurationItem(
                            UUID.randomUUID(),
                            atsConfig.getAtsCode(),
                            "ALL",
                            existingRemoteRecruiterCode,
                            "",
                            recruiter.getCode()
                    )
            );

            return recruiter;
        });
    }

    @Test
    @ResetDataAfter
    void shouldReturnExistingRecruiterCodeWhenConfigurationExists() {

        mockOffer.setRemoteRecruiterCode(existingRemoteRecruiterCode);


        var atsConfiguration =
                perOfferATSConfigurationItemRepository.findOneByAtsCodeAndRemoteRecruiterCode(
                        atsConfig.getAtsCode(), existingRemoteRecruiterCode);
        var recruiterCode = perOfferFetcherCreateUnknownRecruiter.getRecruiterCode(
                mockOffer, atsConfig, atsConfiguration, existingRemoteRecruiterCode);


        assertThat(recruiterCode).isEqualTo(existingRecruiter.getCode());
        verify(notifier, never()).sendMessage(any(ATSRecruiterCreated.class));
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = "ODAS_ADMIN")
    void shouldCreateNewRecruiterWhenConfigurationDoesNotExist() {

        mockOffer.setRemoteRecruiterCode(newRemoteRecruiterCode);

        var recruiterCode = perOfferFetcherCreateUnknownRecruiter.getRecruiterCode(
                mockOffer, atsConfig, Optional.empty(), newRemoteRecruiterCode);


        assertThat(recruiterCode).isNotNull();
        assertThat(recruiterCode).isNotEqualTo(existingRecruiter.getCode());


        Optional<PerOfferATSConfigurationItem> newConfiguration =
                perOfferATSConfigurationItemRepository.findOneByAtsCodeAndRemoteRecruiterCode(
                        atsConfig.getAtsCode(), newRemoteRecruiterCode);
        assertThat(newConfiguration).isPresent();
        assertThat(newConfiguration.get().getRecruiterCode()).isEqualTo(recruiterCode);


        ArgumentCaptor<ATSRecruiterCreated> notificationCaptor = ArgumentCaptor.forClass(ATSRecruiterCreated.class);
        verify(notifier).sendMessage(notificationCaptor.capture());
        ATSRecruiterCreated notification = notificationCaptor.getValue();
        assertThat(notification.getText()).isEqualTo(":bulb: Le recruteur Test Recruiter (code S-0002) a été créé depuis l'ATS TEST_ATS \n\t\tContact : John (a@a)");
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = "ODAS_ADMIN")
    void shouldHandleErrorsGracefullyWhenCreatingRecruiter() {

        var before = ReflectionTestUtils.getField(perOfferFetcherCreateUnknownRecruiter, "perOfferATSConfigurationItemRepository");
        try {
            ReflectionTestUtils.setField(perOfferFetcherCreateUnknownRecruiter, "perOfferATSConfigurationItemRepository", null);
            mockOffer.setRemoteRecruiterCode(newRemoteRecruiterCode);


            Optional<PerOfferATSConfigurationItem> atsConfiguration =
                    perOfferATSConfigurationItemRepository.findOneByAtsCodeAndRemoteRecruiterCode(
                            atsConfig.getAtsCode(), newRemoteRecruiterCode);
            String recruiterCode = perOfferFetcherCreateUnknownRecruiter.getRecruiterCode(
                    mockOffer, atsConfig, atsConfiguration, newRemoteRecruiterCode);


            assertThat(recruiterCode).isNull();


            verify(notifier, never()).sendMessage(any(ATSRecruiterCreated.class));


            Optional<PerOfferATSConfigurationItem> newConfiguration =
                    perOfferATSConfigurationItemRepository.findOneByAtsCodeAndRemoteRecruiterCode(
                            atsConfig.getAtsCode(), newRemoteRecruiterCode);
            assertThat(newConfiguration).isEmpty();
        } finally {
            ReflectionTestUtils.setField(perOfferFetcherCreateUnknownRecruiter, "perOfferATSConfigurationItemRepository", before);
        }
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = "ODAS_ADMIN")
    void shouldHandleMultipleCallsWithSameRemoteRecruiterCode() {

        mockOffer.setRemoteRecruiterCode(newRemoteRecruiterCode);


        Optional<PerOfferATSConfigurationItem> atsConfiguration1 =
                perOfferATSConfigurationItemRepository.findOneByAtsCodeAndRemoteRecruiterCode(
                        atsConfig.getAtsCode(), newRemoteRecruiterCode);
        String recruiterCode1 = perOfferFetcherCreateUnknownRecruiter.getRecruiterCode(
                mockOffer, atsConfig, atsConfiguration1, newRemoteRecruiterCode);


        Optional<PerOfferATSConfigurationItem> atsConfiguration2 =
                perOfferATSConfigurationItemRepository.findOneByAtsCodeAndRemoteRecruiterCode(
                        atsConfig.getAtsCode(), newRemoteRecruiterCode);
        String recruiterCode2 = perOfferFetcherCreateUnknownRecruiter.getRecruiterCode(
                mockOffer, atsConfig, atsConfiguration2, newRemoteRecruiterCode);


        assertThat(recruiterCode1).isNotNull();
        assertThat(recruiterCode2).isEqualTo(recruiterCode1);


        verify(notifier, times(1)).sendMessage(any(ATSRecruiterCreated.class));
    }

    /**
     * Mock implementation of AbstractRemoteOfferContent for testing
     */
    private static class MockRemoteOfferContent extends AbstractRemoteOfferContent<MockRemoteOfferContent> {
        private String id = UUID.randomUUID().toString();
        private LocalDateTime lastModificationDate = LocalDateTime.now();
        private String remoteRecruiterCode;
        private String remoteRecruiterTitle;
        private String locationIndication = "75001 Paris";
        private String offerTitle = "Test Offer";
        private String description = "Test Description";
        private String organizationDescription = "Test Organization";

        @Override
        public String getRecruitersInfos() {
            return "John (a@a)";
        }

        @Override
        public String getId() {
            return id;
        }

        @Override
        public LocalDateTime getLastModificationDate() {
            return lastModificationDate;
        }

        @Override
        public List<Integer> getSalaryValues() {
            return Collections.singletonList(30000);
        }

        @Override
        public String getDescription() {
            return description;
        }

        @Override
        public String getOrganizationDescription() {
            return organizationDescription;
        }

        @Override
        public String getOfferTitle() {
            return offerTitle;
        }

        @Override
        public TypeContractCategoryDTO getTypeContractCategory() {
            return TypeContractCategoryDTO.PERMANENT;
        }

        @Override
        public List<String> getCriterias() {
            return Collections.emptyList();
        }

        @Override
        public String getRemoteRecruiterCode() {
            return remoteRecruiterCode;
        }

        public MockRemoteOfferContent setRemoteRecruiterCode(String remoteRecruiterCode) {
            this.remoteRecruiterCode = remoteRecruiterCode;
            return this;
        }

        public String getRemoteRecruiterTitle() {
            return remoteRecruiterTitle;
        }

        public MockRemoteOfferContent setRemoteRecruiterTitle(String remoteRecruiterTitle) {
            this.remoteRecruiterTitle = remoteRecruiterTitle;
            return this;
        }

        @Override
        public String getLocationIndication() {
            return locationIndication;
        }

        @Override
        public boolean isInFrance() {
            return true;
        }

        @Override
        public LocationDTO getBuiltinLocation() {
            return new LocationDTO().city("Paris").citycode("75001");
        }

        @Override
        public WorkingTimeDTO getWorkingTimeType() {
            return WorkingTimeDTO.FULL_TIME;
        }

        @Override
        public Integer getWorkingTimeWeeklyDuration() {
            return 35;
        }

        @Override
        public WorkContractDurationUnitDTO getWorkContractDurationUnit() {
            return WorkContractDurationUnitDTO.MONTH;
        }
    }
}
