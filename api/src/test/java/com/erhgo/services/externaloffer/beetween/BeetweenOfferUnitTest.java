package com.erhgo.services.externaloffer.beetween;

import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.parsers.GenericJobXmlParser;
import org.intellij.lang.annotations.Language;
import org.junit.jupiter.api.Test;

import static com.erhgo.TestUtils.assertJsonSerializedObjectMatchesContent;
import static org.assertj.core.api.Assertions.assertThat;

class BeetweenOfferUnitTest {
    @Language("XML")
    static final String XML = """
            <job>
                <title>
                    <![CDATA[ Test ]]>
                </title>
                <recruiter>Partenaire Talent Picker</recruiter>
                <creation_date>
                    <![CDATA[ 2024-05-06 17:58:09 +0200 ]]>
                </creation_date>
                <last_modification_date>
                    <![CDATA[ 2024-05-07 19:26:44 +0200 ]]>
                </last_modification_date>
                <application_email><EMAIL></application_email>
                <application_url>https://emploi.beetween.com/apply/job/rnon0d986b5k/test</application_url>
                <job_description>
                    <company>
                        <![CDATA[ <p style="text-align:left">Test</p> ]]>
                    </company>
                    <mission>
                        <![CDATA[ <p style="text-align:left">mission ici</p> ]]>
                    </mission>
                    <profile>
                        <![CDATA[ <p style="text-align:left">Test</p> ]]>
                    </profile>
                </job_description>
                <location>
                    <display>33115 La Teste-de-Buch</display>
                    <city>La Teste-de-Buch</city>
                    <post_code>33115</post_code>
                    <region2_code>33</region2_code>
                    <region2>Gironde</region2>
                    <region1>Nouvelle-Aquitaine</region1>
                    <country_code>FR</country_code>
                    <country_name_fr>France</country_name_fr>
                    <longitude>-1.1491319</longitude>
                    <latitude>44.6316943</latitude>
                </location>
                <contract>
                    <type code="PERMANENT">CDI</type>
                    <rythm hours_per_week="35.0" display_value="Temps plein">35.0h/semaine</rythm>
                    <starting_date asap="true">2024-05-07 19:26:44 +0200</starting_date>
                </contract>
                <job_language>fr</job_language>
                <salary value="33333.0" currency="EUR" unit="YEAR">33333 € (Euros) par an</salary>
                <job_title code="Technician">Technicien</job_title>
                <main_activity_area code="Installation / Maintenance / Repair">Installation / Maintenance / Réparation</main_activity_area>
                <qualification code="P1_P2">Ouvrier qualifié (P1,P2)</qualification>
                <experience experience_year="-2" including_management_years="0.0">Tous niveaux d'expérience</experience>
                <study_levels>
                    <study_level code="ALL">Aucun</study_level>
                    <study_level code="BAC">Bac</study_level>
                </study_levels>
                <id>rnon0d986b</id>
            </job>
            """;

    @Language("XML")
    static final String WRAPPER = """
            <?xml version="1.0" encoding="utf-8"?><root>%s</root>
            """;


    @Test
    void beetweenOffersDeserialization() {
        var softyJobXmlParser = new GenericJobXmlParser<>(BeetweenJob.class, "/root/job", "beetween");
        var jobs = softyJobXmlParser.parseJobs(WRAPPER.formatted(XML), new AtsGetOfferConfig());
        assertThat(jobs).hasSize(1);
        assertJsonSerializedObjectMatchesContent(jobs.getContent().getFirst(), "beetweenExternalOffer.json");

    }
}
