package com.erhgo.services.externaloffer.adecco;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.externaloffer.ExternalOfferEventType;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.externaloffer.ExternalOfferRecruitmentService;
import com.erhgo.services.externaloffer.GenericAtsClient;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.intellij.lang.annotations.Language;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Optional;

class AdeccoGetOfferIntegrationTest extends AbstractIntegrationTest {

    @MockitoBean
    GenericAtsClient atsClient;

    @MockitoBean
    ExternalOfferRecruitmentService externalOfferRecruitmentService;

    @Language("XML")
    String offerRhone = """
            <?xml version="1.0" encoding="utf-8"?>
            <rss version="2.0" xmlns:a10="http://www.w3.org/2005/Atom">
                <channel>
                    <item>
                        <title><![CDATA[Manutentionnaire (h/f)]]></title>
                        <description>
                            <![CDATA[  <div class="VacancyDescription"><p><h2 class="offre-mission">Votre mission 1</h2>]]></description>
                        <ContractType>CDI</ContractType>
                        <PublicationDate>2024-09-12T09:22:35Z</PublicationDate>
                        <SalaryMin>1700,0</SalaryMin>
                        <SalaryMax>2700,0</SalaryMax>
                        <jobOfferRef>16856683</jobOfferRef>
                        <Town>Amplepuis</Town>
                        <PostalCode>69550</PostalCode>
                    </item>
                </channel>
            </rss>
            """;

    @Language("XML")
    String offerVaucluse = """
            <?xml version="1.0" encoding="utf-8"?>
            <rss version="2.0" xmlns:a10="http://www.w3.org/2005/Atom">
            <channel>
                <item>
                    <title><![CDATA[Manutentionnaire 2 (h/f)]]></title>
                    <description>
                        <![CDATA[  <div class="VacancyDescription"><p><h2 class="offre-mission">Votre mission 2</h2>]]></description>
                    <ContractType>CDI</ContractType>
                    <PublicationDate>2024-09-12T09:22:35Z</PublicationDate>
                    <SalaryMin>1700,0</SalaryMin>
                    <SalaryMax>2700,0</SalaryMax>
                    <jobOfferRef>16856684</jobOfferRef>
                    <Town>Avignon</Town>
                    <PostalCode>84000</PostalCode>
                </item>
            </channel>
            </rss>
            """;

    @SneakyThrows
    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void adecco_doesNotConsiderExternalOffersOfDifferentDepartment() {
        Mockito.when(atsClient.fetch(ArgumentMatchers.argThat(a -> a != null && a.getRemoteUrl().contains(URLEncoder.encode("rhône", StandardCharsets.UTF_8))), ArgumentMatchers.eq(Optional.empty()))).thenReturn(offerRhone);
        Mockito.when(atsClient.fetch(ArgumentMatchers.argThat(a -> a != null && a.getRemoteUrl().contains("vaucluse")), ArgumentMatchers.eq(Optional.empty()))).thenReturn(offerVaucluse);

        applicationContext.getBean(ExternalOfferServiceProvider.class).getService(new AtsGetOfferConfig().setAtsCode("adecco").setConfigCode("rhône")).fetchAndUpdateOffers();
        applicationContext.getBean(ExternalOfferServiceProvider.class).getService(new AtsGetOfferConfig().setAtsCode("adecco").setConfigCode("vaucluse")).fetchAndUpdateOffers();

        txHelper.doInTransaction(() -> {
            var allOffers = applicationContext.getBean(ExternalOfferRepository.class).findAll();
            Assertions.assertThat(allOffers)
                    .hasSize(2)
                    .allMatch(a -> a.getLastEventType() == ExternalOfferEventType.CREATED)
                    .anyMatch(a -> a.getConfigCode().equals("vaucluse"))
                    .anyMatch(a -> a.getConfigCode().equals("rhône"))
                    .allMatch(a -> a.getAtsCode().equals("adecco"))
            ;
        });

    }
}
