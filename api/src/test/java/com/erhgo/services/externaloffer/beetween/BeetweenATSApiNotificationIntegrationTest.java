package com.erhgo.services.externaloffer.beetween;

import com.erhgo.config.KeycloakMockService;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.externaloffer.AbstractATSApiNotificationIntegrationTest;
import lombok.SneakyThrows;
import okhttp3.MultipartBody;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.verify;

class BeetweenATSApiNotificationIntegrationTest extends AbstractATSApiNotificationIntegrationTest {

    public static final String JACKY_RECRUITER_CODE = "S-21716";
    @Autowired
    BeetweenATSNotificationScheduler.BeetweenRecruitmentCandidatureSender sender;
    @MockitoBean
    KeycloakMockService keycloakService;

    @SneakyThrows
    @Test
    void handleNewCandidatures() {
        var capturedRequest = doSendCandidature(JACKY_RECRUITER_CODE, sender, "beetween");
        assertThat(capturedRequest.body()).isInstanceOf(MultipartBody.class);
        var multipartBody = (MultipartBody) capturedRequest.body();
        assertThat(multipartBody.parts()).satisfies(parts -> {
            assertThat(parts).hasSize(8); // Ajustez ce nombre selon vos besoins
            // Vérifier le contenu de chaque partie
            assertMultipartPart(parts.get(0), "firstname", "c");
            assertMultipartPart(parts.get(1), "lastname", "ndidate");
            assertMultipartPart(parts.get(2), "wid", "y1s0ru9eoxYY5ju");
            assertMultipartPart(parts.get(3), "email", "c@ndidate");
            assertMultipartPart(parts.get(4), "data");
            assertMultipartPart(parts.get(5), "data");
            assertMultipartPart(parts.get(6), "filenames", "");
            assertMultipartPart(parts.get(7), "filenames", "");
        });

        verify(userProfileCompetencesExportService).getProfileCompetenceForBatch(any(), Mockito.anyString(), eq(ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE));
        verify(userProfileCompetencesExportService).getProfileCompetenceForBatch(any(), Mockito.anyString(), eq(ProfileCompetencesViewObject.AnonymousMode.ANONYMOUS));
    }

}
