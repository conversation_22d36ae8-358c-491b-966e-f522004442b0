package com.erhgo.services.externaloffer.hellowork;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.RecruitmentSendNotificationState;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.enums.WorkContractDurationUnit;
import com.erhgo.domain.externaloffer.ExternalOfferEventType;
import com.erhgo.domain.externaloffer.ExternalOfferMotherObject;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.sourcing.SourcingInvitationMotherObject;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.CriteriaMotherObject;
import com.erhgo.generators.RecruiterMotherObject;
import com.erhgo.openapi.dto.ErhgoOccupationMinimumInfoDTO;
import com.erhgo.openapi.dto.LocationDTO;
import com.erhgo.repositories.RecruitmentRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.notification.RemoteOfferErrorDTO;
import com.erhgo.services.externaloffer.notification.RemoteOfferEvent;
import com.erhgo.services.generation.FindBestMatchingOccupationService;
import com.erhgo.services.generation.TitleGenerationService;
import com.erhgo.services.generation.dto.FindBestMatchingOccupationArguments;
import com.erhgo.services.generation.dto.NormalizedTitlesResponse;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.search.ErhgoOccupationFinder;
import com.erhgo.services.search.ErhgoOccupationIndexer;
import com.erhgo.services.search.recruitment.RecruitmentIndexer;
import org.intellij.lang.annotations.Language;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.StreamSupport;

import static com.erhgo.domain.utils.EventPublisherUtils.applicationEventPublisher;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

class HWRecruitmentServiceTest extends AbstractIntegrationTest {
    public static final String ATS_CODE = "HELLO_WORK";
    public static final String CONFIG_CODE = "MLOGITICS";
    @MockitoBean
    FindBestMatchingOccupationService findBestMatchingOccupationService;

    @MockitoBean
    ErhgoOccupationIndexer erhgoOccupationIndexerMock;
    @MockitoBean
    RecruitmentIndexer recruitmentIndexer;

    @MockitoBean
    TitleGenerationService titleGenerationService;

    @MockitoBean
    @SuppressWarnings("unused")
    private ErhgoOccupationFinder erhgoOccupationFinder;

    @MockitoBean
    Notifier notifier;

    @Autowired
    RecruitmentRepository repository;

    @Autowired
    CapacityGenerator capacityGenerator;

    @Autowired
    ApplicationContext applicationContext;

    @Value("${ats.hello-work.fetch[0].recruiterCode}")
    String atsRecruiterCode;

    @Language("JSON")
    private static final String OFFER = """
                {
                  "NameAdvertise": "Conducteur SPL régional nuit H/F",
                  "RefSubsidiaryCompany": 69502,
                  "LastDiffusion": "2024-09-26T09:52:35.150",
                  "JobContract": "CDD",
                  "MinRemuneration": 50.43,
                  "MaxRemuneration": 100.43,
                  "RemunerationCurrency": "EUR",
                  "RemunerationInterval": "mois",
                  "RemunerationIsNotVisible": true,
                  "City": "Attignat",
                  "ZipCode": "01340",
                  "Country": "France",
                  "LocationTravelArea": "Régionale",
                  "EmailPage": "<EMAIL>",
                  "IsTelework": true,
                  "IsFullTime": false,
                  "TemporaryContractDuration": "1",
                  "TemporaryContractDurationInterval": "mois",
                  "WorkingPeriod": "week-end",
                  "Creator": "<EMAIL>",
                  "Intervenors": [
                    "<EMAIL>"
                  ],
                  "DescriptionFormatted": "<p>Dans le cadre du développement de nos activités, nous recherchons pour notre site d'Attignat (01) :</p>",
                  "CompanyInformationFormatted": "<p><strong>MUTUAL LOGISTICS</strong>, groupe familial indépendant de 600 collaborateurs créé à la fin de l’année 2007, exerce tous les métiers de la prestation de la Supply Chain</strong></p>"
                }
            """;

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void created_events_are_fired_and_recruitments_are_updated_accordingly() {
        var capacity = capacityGenerator.createCapacity("CA1-01");
        var occupationId = UUID.randomUUID();
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withId(occupationId)
                .withCapacities(capacity)
                .withTitle("Responsable Commercial")
                .qualified(true)
                .buildAndPersist();

        when(titleGenerationService.normalizeTitle(any())).thenReturn(new OpenAIResponse<NormalizedTitlesResponse>().setResult(new NormalizedTitlesResponse().setMasculine(occupation.getTitle()).setFeminine(occupation.getTitle())));
        when(findBestMatchingOccupationService.findSimilarLabel(any(FindBestMatchingOccupationArguments.class)))
                .thenReturn(new ErhgoOccupationMinimumInfoDTO().title(occupation.getTitle()).id(occupationId));
        when(erhgoOccupationIndexerMock.updateOccupationIndexation(any())).thenReturn(CompletableFuture.completedFuture(null));
        when(geoService.fetchGeoCoordinates(anyString(), anyString())).thenReturn(new LocationDTO()
                .city("Etupes")
                .latitude(48.6231673F)
                .longitude(7.7118232F));
        var recruiter = applicationContext.getBean(RecruiterMotherObject.class)
                .withCode(atsRecruiterCode)
                .withOrganizationType(AbstractOrganization.OrganizationType.SOURCING)
                .buildAndPersist();

        applicationContext.getBean(SourcingInvitationMotherObject.class)
                .withGuests(recruiter)
                .buildAndPersist();
        applicationContext.getBean(CriteriaMotherObject.class)
                .withValueCodes(CriteriaValue.REGULAR_MOVE)
                .buildAndPersist();
        applicationContext.getBean(CriteriaMotherObject.class)
                .withValueCodes(CriteriaValue.NIGHT_WORK_CRITERIA_VALUE_CODE)
                .buildAndPersist();
        applicationContext.getBean(CriteriaMotherObject.class)
                .withValueCodes(CriteriaValue.getValueCodeForPartialRemoteWork())
                .buildAndPersist();

        var id = "12";
        var uuid = UUID.fromString("95596cf2-69a8-42fd-95a1-722f7a101614");
        var externalOffer = applicationContext.getBean(ExternalOfferMotherObject.class)
                .withUuid(uuid.toString())
                .withRemoteId(id)
                .withRemoteModificationDate(LocalDateTime.of(2020, 10, 10, 10, 10))
                .withRawContent(OFFER)
                .withLastEventType(ExternalOfferEventType.CREATED)
                .withATSCode(ATS_CODE)
                .withOfferLocation("Attignat (01340)")
                .withConfigCode(CONFIG_CODE)
                .withRecruiterCode(atsRecruiterCode)
                .withOfferTitle("Conducteur SPL régional nuit H/F")
                .buildAndPersist();

        var event = new RemoteOfferEvent(List.of(externalOffer), List.of(), List.of(), List.of(), new AtsGetOfferConfig().setAtsCode(ATS_CODE).setRecruiterCode(atsRecruiterCode).setConfigCode(CONFIG_CODE));

        applicationEventPublisher.publishEvent(event);

        txHelper.doInTransaction(() -> {
            assertThat(repository.countByRecruitmentProfileJobRecruiter(recruiter)).isEqualTo(1);

            var foundRecruitment = StreamSupport.stream(repository.findAll().spliterator(), false).filter(r -> r.getRecruiter().getCode().equals(recruiter.getCode()) && r.getState() == RecruitmentState.PUBLISHED).findFirst().orElseThrow();
            assertThat(foundRecruitment.getLocation().getCity()).isEqualTo("Etupes");
            assertThat(foundRecruitment.getHideSalary()).isTrue();
            assertThat(foundRecruitment.getWorkContractDurationUnit()).isEqualTo(WorkContractDurationUnit.MONTH);
            assertThat(foundRecruitment.getWorkContractDuration()).isEqualTo(1);
            assertThat(foundRecruitment.getCriteriaValues().stream().map(CriteriaValue::getCode)).contains(CriteriaValue.REGULAR_MOVE).contains(CriteriaValue.NIGHT_WORK_CRITERIA_VALUE_CODE).contains(CriteriaValue.getValueCodeForPartialRemoteWork());

            assertThat(foundRecruitment.getSendNotificationState()).isEqualTo(RecruitmentSendNotificationState.WAITING);
            Mockito.verify(notifier, times(0)).sendMessage(ArgumentMatchers.any(RemoteOfferErrorDTO.class));
        });
    }

}
