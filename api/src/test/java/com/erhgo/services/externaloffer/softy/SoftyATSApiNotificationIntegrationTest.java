package com.erhgo.services.externaloffer.softy;

import com.erhgo.config.ResetDataAfter;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.externaloffer.AbstractATSApiNotificationIntegrationTest;
import lombok.SneakyThrows;
import okhttp3.MediaType;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class SoftyATSApiNotificationIntegrationTest extends AbstractATSApiNotificationIntegrationTest {
    public static final String SOFTY_RECRUITER_CODE = "S-21691";
    @Autowired
    SoftyATSApiNotificationScheduler scheduler;

    @SneakyThrows
    @Test
    @ResetDataAfter
    void handleNewCandidatures() {
        var capturedRequest = doSendCandidature(SOFTY_RECRUITER_CODE, scheduler, "softy");
        assertBodyContains(capturedRequest.body(), "softySendCandidatureCommand");
        verify(userProfileCompetencesExportService).getProfileCompetenceForBatch(any(), anyString(), eq(ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE));
        verifyNoMoreInteractions(userProfileCompetencesExportService);
    }

    @SneakyThrows
    @Test
    @ResetDataAfter
    void refuseCandidature() {
        var response = mock(Response.class);
        when(response.isSuccessful()).thenReturn(false);
        when(response.code()).thenReturn(400);
        when(response.body()).thenReturn(ResponseBody.create("""
                 {"statut":"error","message":"Job Offer id is not valid"}
                """, MediaType.get("application/json")));

        doSendCandidature(SOFTY_RECRUITER_CODE, scheduler, "softy", response);
        var candidature = applicationContext.getBean(RecruitmentCandidatureRepository.class).findById(getCandidature().getId()).orElseThrow();
        Assertions.assertThat(candidature.isRefused()).isTrue();
    }


}
