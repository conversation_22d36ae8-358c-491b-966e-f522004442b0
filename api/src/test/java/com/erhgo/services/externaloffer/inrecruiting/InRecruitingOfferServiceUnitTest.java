package com.erhgo.services.externaloffer.inrecruiting;

import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.parsers.GenericJobJsonParser;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.intellij.lang.annotations.Language;
import org.junit.jupiter.api.Test;

import static com.erhgo.TestUtils.assertJsonSerializedObjectMatchesContent;

class InRecruitingOfferServiceUnitTest {

    @Language("json")
    String json = """

                        [{
              "title": "Chef de produits Junior Diffusion d’Air",
              "id": "83635",
              "slug": "chef-de-produits-junior-diffusion-dair-83635",
              "company": "FRANCE AIR",
              "style": "",
              "location": "Beynost",
              "nation": "France",
              "region": "Auvergne-Rhône-Alpes",
              "county": "Ain",
              "county_code": "01",
              "city": "Beynost",
              "lat": "45.8381",
              "lng": "5.0061",
              "published": "02-10-2024 (17:14)",
              "expires": "30-12-2024 (16:14)",
              "location_label": "Localisation",
              "expires_label": "Date d'expiration",
              "url": "https://inrecruitingfr.intervieweb.it/franceair/jobs/chef-de-produits-junior-diffusion-dair-83635/fr/",
              "popupname": "popup_announce",
              "industry": "Communication - Marketing - Médias",
              "function": "Marketing",
              "seniority_level": "Confirmé",
              "contract_type": "CDI",
              "contract_duration": null,
              "years_of_experience": "De 3 à 5 ans ",
              "remote_working": "Hybride",
              "description": "\\u003Ch2\\u003EDescription de l'entreprise\\u003C/h2\\u003E\\u003Cp style=\\"text-align: justify;\\"\\u003E\\u003Cstrong\\u003E\\u003Cem\\u003EFrance Air : une entreprise où il faut bon respirer.\\u003C/em\\u003E\\u003C/strong\\u003E\\u003C/p\\u003E\\u003Cp style=\\"text-align: justify;\\"\\u003E\\u003Cbr /\\u003E\\u003C/strong\\u003E\\u003C/p\\u003E",
              "company_description": "\\u003Cp style=\\"text-align: justify;\\"\\u003E\\u003Cstrong\\u003E\\u003Cem\\u003EFrance Air : une entreprise où il faut bon respirer.",
              "position_description": "\\u003Cp\\u003E\\u003Cstrong\\u003E\\u003Cu\\u003EQUELLES SERONT VOS PRINCIPALES ACTIVITÉS ?",
              "requirements_description": "\\u003Cp\\u003E\\u003Cstrong\\u003E\\u003Cu\\u003EVOTRE PROFIL :\\u003C/u\\u003E\\u003C/strong\\u003E\\u003C/p\\u003E\\u003Cp\\u003E\\u003C/p\\u003E\\u003Cp\\u003EDe formation bac+5 dans le domaine du le marketing.",
              "other_information_description": "",
              "vacancy_owner": "",
              "registration_iframe_url": "https://inrecruitingfr.intervieweb.it/",
              "bold": "0",
              "highlighted": "0",
              "logo": "https://inrecruitingfr.intervieweb.it/uploads/13401/loghi/Designsanstitre24.png",
              "coverImage": null,
              "ref_code": null,
              "project": null,
              "project_label": null,
              "last_update": "2024-10-02 17:17:55",
              "team_id": "1402",
              "team": "DUJLO Patoche",
              "external_registration_link": null,
              "url_video": null,
              "tags": []
            }]
            """;

    @SneakyThrows
    @Test
    void IROfferDeserialization() {
        var irJobJSONParser = new GenericJobJsonParser<>(InRecruitingJob.class, null);
        var jobs = irJobJSONParser.parseJobs(json, new AtsGetOfferConfig());
        Assertions.assertThat(jobs).hasSize(1);
        assertJsonSerializedObjectMatchesContent(jobs.getContent().getFirst(), "irExternalOffer.json");
    }
}
