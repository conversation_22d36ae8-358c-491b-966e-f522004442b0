package com.erhgo.services.externaloffer.gestmax;

import com.erhgo.config.KeycloakMockService;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.externaloffer.AbstractATSApiNotificationIntegrationTest;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.web.util.UriUtils;

import java.nio.charset.StandardCharsets;
import java.time.format.DateTimeFormatter;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class GestmaxNotificationIntegrationTest extends AbstractATSApiNotificationIntegrationTest {

    public static final String RECRUITER_CODE = "S-21300";
    @Autowired
    GestmaxATSNotificationScheduler scheduler;
    @MockitoBean
    KeycloakMockService keycloakService;

    @SneakyThrows
    @Test
    void handleNewCandidatures() {
        var capturedRequest = doSendCandidature(RECRUITER_CODE, scheduler, "GESTMAX");
        var formatedDate = UriUtils.encode(applicationContext.getBean(RecruitmentCandidatureRepository.class).findById(getCandidature().getId()).orElseThrow().getSubmissionDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss")), StandardCharsets.UTF_8).replace("%20", "+");
        assertBodyContains(capturedRequest.body(), "gestmaxSendCandidatureCommand.txt", "##DATE##", formatedDate);
        verify(userProfileCompetencesExportService).getProfileCompetenceForBatch(any(), anyString(), eq(ProfileCompetencesViewObject.AnonymousMode.BOTH));
        verifyNoMoreInteractions(userProfileCompetencesExportService);
    }
}
