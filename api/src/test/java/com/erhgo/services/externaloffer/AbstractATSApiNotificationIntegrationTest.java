package com.erhgo.services.externaloffer;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TestUtils;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.CandidatureState;
import com.erhgo.domain.externaloffer.ExternalOfferMotherObject;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.services.externaloffer.candidature.AbstractATSWithHttpClientNotificationSender;
import com.erhgo.services.http.RetryableHttpClient;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import lombok.Getter;
import lombok.SneakyThrows;
import okhttp3.MultipartBody;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okio.Buffer;
import org.mockito.Answers;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.time.OffsetDateTime;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public abstract class AbstractATSApiNotificationIntegrationTest extends AbstractIntegrationTest {

    @Mock
    RetryableHttpClient httpClient;

    @Getter
    RecruitmentCandidature candidature;

    @MockitoBean(answers = Answers.RETURNS_MOCKS)
    protected UserProfileCompetencesExportService userProfileCompetencesExportService;

    @MockitoBean(answers = Answers.RETURNS_MOCKS)
    protected GenericAtsClient genericAtsClient;

    public Request doSendCandidature(String recruiterCode, AbstractATSWithHttpClientNotificationSender scheduler, String atsCode) throws IOException {
        var response = mock(Response.class);
        when(response.isSuccessful()).thenReturn(true);

        return doSendCandidature(recruiterCode, scheduler, atsCode, response);
    }

    public Request doSendCandidature(String recruiterCode, AbstractATSWithHttpClientNotificationSender scheduler, String atsCode, Response response) throws IOException {
        var requestCaptor = ArgumentCaptor.forClass(Request.class);

        var offer = applicationContext.getBean(ExternalOfferMotherObject.class)
                .withRecruitment(applicationContext.getBean(RecruitmentMotherObject.class).withRecruiterCode(recruiterCode).buildAndPersist())
                .withATSCode(atsCode)
                .withRemoteId("y1s0ru9eoxYY")
                .withRecruiterCode(recruiterCode).buildAndPersist();

        var user = applicationContext.getBean(UserProfileMotherObject.class)
                .withEmail("c@ndidate")
                .withFirstname("c")
                .withLastname("ndidate")
                .withPhoneNumber("42")
                .withExperienceOnOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist())
                .withUserId("55")
                .withLocation(Location.builder().city("Lille").postcode("59000").radiusInKm(30).build())
                .buildAndPersist();

        candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withRecruitment(offer.getRecruitment())
                .withCandidatureState(CandidatureState.VALIDATED)
                .withSubmissionDate(OffsetDateTime.now().minusDays(2))
                .withUserProfile(user)
                .withAnonymousCode("AN42")
                .buildAndPersist();

        ReflectionTestUtils.setField(scheduler, "httpClient", httpClient);
        Mockito.when(httpClient.executeRequest(any())).thenReturn(response);
        scheduler.handleNewCandidatures();

        verify(httpClient).executeRequest(requestCaptor.capture());
        return requestCaptor.getValue();
    }

    @SneakyThrows
    public void assertMultipartPart(MultipartBody.Part part, String name, String value) {
        assertThat(part.headers()).isNotNull();
        assertThat(part.headers().get("Content-Disposition"))
                .contains("form-data")
                .contains("name=\"" + name + "\"");

        assertThat(part.body()).isInstanceOf(RequestBody.class);

        var buffer = new Buffer();
        part.body().writeTo(buffer);
        assertThat(buffer.readUtf8()).isEqualTo(value);

    }

    @SneakyThrows
    public String assertBodyContains(RequestBody body, String expectedBodyFile, String... params) {
        var buffer = new Buffer();
        body.writeTo(buffer);
        var actualContent = buffer.readUtf8().replaceAll("(?m)^--.*$", "--");
        TestUtils.assertFileContentMatch(expectedBodyFile, actualContent, JSONCompareMode.LENIENT, params);
        return actualContent;
    }

    public void assertMultipartPart(MultipartBody.Part part, String name) {
        assertThat(part.headers()).isNotNull();
        assertThat(part.headers().get("Content-Disposition"))
                .contains("form-data")
                .contains("name=\"" + name + "\"")
                .contains("filename=\"\"");

        assertThat(part.body()).isInstanceOf(RequestBody.class);
    }


}
