package com.erhgo.services.externaloffer.firecrawl;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.CandidatureSynchronizationState;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.services.mailing.MailNotifier;
import com.erhgo.services.userprofile.FilePartProvider;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Answers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.time.OffsetDateTime;

import static java.util.Set.of;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;

class FirecrawlSendNotificationTest extends AbstractIntegrationTest {

    public static final String STEF_ORGA_CODE = "S-21997";
    @Autowired
    ApplicationContext applicationContext;
    @MockitoBean
    MailNotifier mailNotifier;
    @MockitoBean(answers = Answers.RETURNS_MOCKS)
    UserProfileCompetencesExportService userProfileCompetencesExportService;
    @Autowired
    FirecrawlMailNotificationScheduler firecrawlMailNotificationScheduler;

    @Test
    @ResetDataAfter
    void ensureSFCandidatureAreSent() {
        var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(
                        applicationContext.getBean(UserProfileMotherObject.class)
                                .withUserId("f3f2e850-b5d4-11ef-ac7e-96584d5248b2")
                                .withEmail("<EMAIL>")
                                .withFirstname("fn")
                                .withLastname("ln")
                                .withPhoneNumber("0123")
                                .withLocation(Location.builder().city("Pau").postcode("42000").build())
                                .withExperienceOnOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).withCapacities(applicationContext.getBean(CapacityGenerator.class).createCapacity("CA3-33")).buildAndPersist())
                                .buildAndPersist()
                )
                .withRecruiterCode(STEF_ORGA_CODE)
                .withATSCode("FIRECRAWL")
                .withState(GlobalCandidatureState.NEW)
                .withCandidatureSynchronizationState(CandidatureSynchronizationState.WAITING)
                .withSubmissionDate(OffsetDateTime.now().minusHours(5))
                .buildAndPersist();


        firecrawlMailNotificationScheduler.handleNewCandidatures();
        txHelper.doInTransaction(() -> {
            var upToDateCandidature = applicationContext.getBean(RecruitmentCandidatureRepository.class).findById(candidature.getId()).orElseThrow();
            Assertions.assertThat(upToDateCandidature.getSynchronizationState()).isEqualTo(CandidatureSynchronizationState.DONE);
            Mockito.verify(mailNotifier).sendMail(eq(of("<EMAIL>")), anyString(), anyString(), eq(true), anyString(), anyString(), Mockito.any(FilePartProvider.class), Mockito.any(FilePartProvider.class));
        });

    }

}
