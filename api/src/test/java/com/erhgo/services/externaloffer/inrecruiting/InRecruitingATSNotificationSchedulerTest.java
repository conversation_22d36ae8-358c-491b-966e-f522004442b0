package com.erhgo.services.externaloffer.inrecruiting;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.openapi.client.inrecruiting.ApiException;
import com.erhgo.openapi.client.inrecruiting.api.DefaultApi;
import com.erhgo.openapi.client.inrecruiting.api.model.CandidateStore200Response;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.send.SendCandidaturesRecruiterProvider;
import com.erhgo.services.http.RetryableHttpClient;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Answers;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InRecruitingATSNotificationSchedulerTest {

    @Mock
    KeycloakService keycloakService;

    @Mock
    ConfigurablePropertyRepository configurablePropertyRepository;

    @Mock(answer = Answers.RETURNS_MOCKS)
    UserProfileCompetencesExportService userProfileCompetencesExportService;

    @Mock
    TransactionTemplate transactionTemplate;

    @Mock
    RecruitmentCandidatureRepository recruitmentCandidatureRepository;

    @Mock
    SecurityService securityService;

    @Mock
    RetryableHttpClient httpClient;

    @Mock
    AtsSendCandidaturesConfig config;
    @Mock
    AbstractCandidatureRepository abstractCandidatureRepository;
    @Mock
    SendCandidaturesRecruiterProvider sendCandidaturesRecruiterProvider;

    InRecruitingATSNotificationScheduler scheduler;

    @BeforeEach
    void setUp() {
        scheduler = new InRecruitingATSNotificationScheduler(new ObjectMapper(),
                keycloakService,
                configurablePropertyRepository,
                userProfileCompetencesExportService,
                transactionTemplate,
                recruitmentCandidatureRepository,
                null,
                List.of(config),
                securityService,
                abstractCandidatureRepository,
                sendCandidaturesRecruiterProvider);
        ReflectionTestUtils.setField(scheduler, "httpClient", httpClient);
    }

    @Test
    @SneakyThrows
    void ensureWorkflowForCandidatureSubmit() {
        var applicantId = "42";
        var offerId = "4242";
        var token = "424242";

        var userRepresentation = new UserRepresentation().setFirstName("A").setLastName("B").setEmail("<EMAIL>");
        var candidature = mock(RecruitmentCandidature.class);
        when(candidature.getExternalOfferId()).thenReturn(offerId);
        when(config.getCandidatureNotificationUrl()).thenReturn("http://example.com");
        when(config.getCandidatureNotificationApiKey()).thenReturn("L;P");
        var response = mock(Response.class);
        var body = mock(ResponseBody.class);
        when(body.string()).thenReturn("""
                {"access_token": "%s"}
                """.formatted(token));
        when(response.body()).thenReturn(body);
        when(response.isSuccessful()).thenReturn(true);
        when(httpClient.executeRequest(any())).thenReturn(response);

        try (var mocked = mockConstruction(DefaultApi.class, (mock, context) -> {
            when(mock.candidateStore(any())).thenReturn(new CandidateStore200Response().applicantId(applicantId));
        })) {
            scheduler.sendCandidature(userRepresentation, candidature, config);
            verify(mocked.constructed().get(1)).vacancyApplicationStore(Mockito.assertArg(a -> {
                Assertions.assertThat(a.getVacancyId()).isEqualTo(offerId);
                Assertions.assertThat(a.getCandidateId()).isEqualTo(applicantId);
            }));
        }
    }


    @Test
    @SneakyThrows
    void ensureExistentCandidateUpdate() {
        var applicantId = "42";
        var offerId = "4242";
        var token = "424242";

        var userRepresentation = new UserRepresentation().setFirstName("A").setLastName("B").setEmail("<EMAIL>");
        var candidature = mock(RecruitmentCandidature.class);
        when(candidature.getExternalOfferId()).thenReturn(offerId);
        when(config.getCandidatureNotificationUrl()).thenReturn("http://example.com");
        when(config.getCandidatureNotificationApiKey()).thenReturn("L;P");
        var response = mock(Response.class);
        var body = mock(ResponseBody.class);
        when(body.string()).thenReturn("""
                {"access_token": "%s"}
                """.formatted(token));
        when(response.body()).thenReturn(body);
        when(response.isSuccessful()).thenReturn(true);
        when(httpClient.executeRequest(any())).thenReturn(response);

        try (var mocked = mockConstruction(DefaultApi.class, (mock, context) -> {
            when(mock.candidateStore(any())).thenThrow(new ApiException(303, null, """
                    {"applicantId": "%s"}
                    """.formatted(applicantId)));
        })) {
            scheduler.sendCandidature(userRepresentation, candidature, config);
            verify(mocked.constructed().get(0)).candidateUpdate(Mockito.eq(Integer.parseInt(applicantId)), any());
            verify(mocked.constructed().get(1)).vacancyApplicationStore(Mockito.assertArg(a -> {
                Assertions.assertThat(a.getVacancyId()).isEqualTo(offerId);
                Assertions.assertThat(a.getCandidateId()).isEqualTo(applicantId);
                Assertions.assertThat(a.getSource()).isEqualTo("jenesuispasunCV");
            }));
        }
    }

}
