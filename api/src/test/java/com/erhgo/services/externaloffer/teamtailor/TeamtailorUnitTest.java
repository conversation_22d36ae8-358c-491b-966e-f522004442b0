package com.erhgo.services.externaloffer.teamtailor;

import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.parsers.GenericJobXmlParser;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.intellij.lang.annotations.Language;
import org.junit.jupiter.api.Test;

import static com.erhgo.TestUtils.assertJsonSerializedObjectMatchesContent;

class TeamtailorUnitTest {

    @Language("xml")
    String xml = """
            <source>
                <publisher>Teamtailor</publisher>
                <publisherurl>https://www.teamtailor.com</publisherurl>
                <lastupdatedat>2025-02-25T07:00:01+00:00</lastupdatedat>
                <job>
                    <company>
                        <![CDATA[ Sandbox: jenesuisPASunCV ]]>
                    </company>
                    <companyuuid>
                        <![CDATA[ QhWC8MAk0ZY ]]>
                    </companyuuid>
                    <companydescription>
                        <![CDATA[ ]]>
                    </companydescription>
                    <logotype>
                        <![CDATA[ https://teamtailor-production.s3.eu-west-1.amazonaws.com/image_uploads/8993cde1-4bae-4bfd-a2f7-8315d01dd541/original.png ]]>
                    </logotype>
                    <subdomain>
                        <![CDATA[ sandboxjenesuispasuncv-1739294041 ]]>
                    </subdomain>
                    <headquarters>
                        <country>
                            <![CDATA[ France ]]>
                        </country>
                        <city>
                            <![CDATA[ Lyon ]]>
                        </city>
                        <postalcode>
                            <![CDATA[ 69003 ]]>
                        </postalcode>
                        <address>
                            <![CDATA[ La Part-Dieu ]]>
                        </address>
                        <countrycode>
                            <![CDATA[ FR ]]>
                        </countrycode>
                        <region>
                            <![CDATA[ Auvergne-Rhône-Alpes ]]>
                        </region>
                    </headquarters>
                    <referencenumber>
                        <![CDATA[ teamtailor-5549338 ]]>
                    </referencenumber>
                    <title>
                        <![CDATA[ Plombier ]]>
                    </title>
                    <description>
                        <![CDATA[ <p>Vous effectuerez :</p> <ol> <li>des travaux de <strong>plomberie</strong> </li> <li><strong>dans le <em>domaine <a href="google.com" target="_blank">sous-marin</a></em></strong></li> </ol><p></p> ]]>
                    </description>
                    <startdate>
                        <![CDATA[ 2025-02-12T12:44:46+01:00 ]]>
                    </startdate>
                    <enddate>
                        <![CDATA[ ]]>
                    </enddate>
                    <createddate>
                        <![CDATA[ 2025-02-12T12:44:46+01:00 ]]>
                    </createddate>
                    <remotestatus>
                        <![CDATA[ hybrid ]]>
                    </remotestatus>
                    <employmenttype>
                        <![CDATA[ none ]]>
                    </employmenttype>
                    <employmentlevel>
                        <![CDATA[ none ]]>
                    </employmentlevel>
                    <url>
                        <![CDATA[ https://sandboxjenesuispasuncv-1739294041.teamtailor.com/jobs/5549338-plombier ]]>
                    </url>
                    <applyurl>
                        <![CDATA[ https://sandboxjenesuispasuncv-1739294041.teamtailor.com/jobs/5549338-plombier/applications/new ]]>
                    </applyurl>
                    <coverimageurl>
                        <![CDATA[ https://images.teamtailor-cdn.com/images/s3/teamtailor-production/width_1200-v3/image_uploads/30e5bc47-1def-47d3-b847-548d76f18176/original.jpg ]]>
                    </coverimageurl>
                    <pitch>
                        <![CDATA[ Le taf de vos rêves ]]>
                    </pitch>
                    <applybuttontext>
                        <![CDATA[ Postuler ]]>
                    </applybuttontext>
                    <locations>
                        <location>
                            <country>
                                <![CDATA[ France ]]>
                            </country>
                            <city>
                                <![CDATA[ Lyon ]]>
                            </city>
                            <postalcode>
                                <![CDATA[ 69003 ]]>
                            </postalcode>
                            <address>
                                <![CDATA[ La Part-Dieu ]]>
                            </address>
                            <countrycode>
                                <![CDATA[ FR ]]>
                            </countrycode>
                            <region>
                                <![CDATA[ Auvergne-Rhône-Alpes ]]>
                            </region>
                        </location>
                    </locations>
                    <customfields> </customfields>
                    <department>
                        <name>
                            <![CDATA[ Product ]]>
                        </name>
                    </department>
                    <role>
                        <name>
                            <![CDATA[ iOS developer ]]>
                        </name>
                    </role>
                    <recruiter>
                        <name>
                            <![CDATA[ <EMAIL> ]]>
                        </name>
                        <email>
                            <![CDATA[ <EMAIL> ]]>
                        </email>
                        <phone>
                            <![CDATA[ ]]>
                        </phone>
                    </recruiter>
                    <salary>
                        <min>
                            <![CDATA[ 25000 ]]>
                        </min>
                        <max>
                            <![CDATA[ 30000 ]]>
                        </max>
                        <currency>
                            <![CDATA[ EUR ]]>
                        </currency>
                        <timeunit>
                            <![CDATA[ yearly ]]>
                        </timeunit>
                    </salary>
                </job>
            </source>
            """;

    @SneakyThrows
    @Test
    void teamtailorOfferDeserialization() {
        var jobJSONParser = new GenericJobXmlParser<>(TeamtailorJob.class, "/source/job", "TEAMTAILOR");
        var jobs = jobJSONParser.parseJobs(xml, new AtsGetOfferConfig()).getContent();
        Assertions.assertThat(jobs).hasSize(1);
        assertJsonSerializedObjectMatchesContent(jobs, "teamtailorExternalOffers.json");
    }
}
