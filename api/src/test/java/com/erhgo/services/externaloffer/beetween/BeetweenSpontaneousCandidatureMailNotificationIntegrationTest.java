package com.erhgo.services.externaloffer.beetween;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.CandidatureEmailRefusalState;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.candidature.job.SpontaneousCandidature;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.CandidatureSynchronizationState;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.job.SpontaneousCandidatureMotherObject;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.mailing.MailNotifier;
import com.erhgo.services.mailing.SendEmailValidationCandidatureService;
import com.erhgo.services.sourcing.SourcingMailingService;
import com.erhgo.services.userprofile.FilePartProvider;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.mockito.Answers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.time.OffsetDateTime;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;

class BeetweenSpontaneousCandidatureMailNotificationIntegrationTest extends AbstractIntegrationTest {

    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    SpontaneousCandidatureRepository candidatureRepository;
    @MockBean
    SourcingMailingService sourcingMailingService;
    @MockBean
    SendEmailValidationCandidatureService sendEmailValidationCandidatureService;
    @MockBean
    KeycloakMockService keycloakService;
    @MockBean
    MailNotifier mailNotifier;
    @MockBean(answer = Answers.RETURNS_MOCKS)
    UserProfileCompetencesExportService userProfileCompetencesExportService;

    @Value("${ats.beetween.fetch[2].recruiterCode}")
    private String recruiterCode;

    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @Test
    void ensure_event_is_handled() {
        var userId = "42";
        String email = "a@a";
        var candidature = persistCandidature(userId, OffsetDateTime.now().minusMinutes(100), email, true, false, false);
        persistCandidature("noxp", OffsetDateTime.now().minusMinutes(100), "noxp@a", false, false, false);
        var archived = persistCandidature("arch", OffsetDateTime.now().minusMinutes(100), "arch@a", true, true, false);
        var refused = persistCandidature("ref", OffsetDateTime.now().minusMinutes(100), "ref@a", true, false, true);
        var erroneousCandidature = persistCandidature("Bof", OffsetDateTime.now().minusMinutes(100), "err@a", true, false, false);
        var tooSoonCandidature = persistCandidature("Nope either", OffsetDateTime.now().plusMinutes(10), "sooon@a", true, false, false);

        Mockito.doThrow(GenericTechnicalException.class).when(userProfileCompetencesExportService).getProfileCompetenceForBatch(eq(erroneousCandidature.getId()), anyString(), eq(ProfileCompetencesViewObject.AnonymousMode.ANONYMOUS));

        applicationContext.getBean(BeetweenATSNotificationScheduler.BeetweenSpontaneousCandidatureSender.class).handleNewSpontaneousCandidatures();

        verify(userProfileCompetencesExportService).getProfileCompetenceForBatch(eq(candidature.getId()), Mockito.any(), eq(ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE));
        verify(userProfileCompetencesExportService).getProfileCompetenceForBatch(eq(candidature.getId()), Mockito.any(), eq(ProfileCompetencesViewObject.AnonymousMode.ANONYMOUS));
        verify(userProfileCompetencesExportService, Mockito.never()).getProfileCompetenceForBatch(eq(erroneousCandidature.getId()), Mockito.any(), eq(ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE));
        verify(mailNotifier).sendMail(eq(Set.of("<EMAIL>")), eq(email), anyString(), anyBoolean(), Mockito.contains("jenesuisPASunCV : nouvelle candidature spontanée"
        ), Mockito.contains("""
                - Prénom = f
                - Nom = l
                - Email = a@a
                - Téléphone = 0123
                """), Mockito.any(FilePartProvider.class), Mockito.any(FilePartProvider.class));
        var candidatureOK = candidatureRepository.findById(candidature.getId()).orElseThrow();
        assertThat(candidatureOK.getSynchronizationState()).isEqualTo(CandidatureSynchronizationState.DONE);
        assertThat(candidatureOK.getRemoteNotifiedIdentifier()).isEqualTo("<EMAIL>");
        assertThat(candidatureRepository.findById(tooSoonCandidature.getId()).orElseThrow().getSynchronizationState()).isEqualTo(CandidatureSynchronizationState.WAITING);
        assertThat(candidatureRepository.findById(erroneousCandidature.getId()).orElseThrow().getSynchronizationState()).isEqualTo(CandidatureSynchronizationState.ERROR);
        assertThat(candidatureRepository.findById(archived.getId()).orElseThrow().getSynchronizationState()).isEqualTo(CandidatureSynchronizationState.IGNORE);
        assertThat(candidatureRepository.findById(refused.getId()).orElseThrow().getSynchronizationState()).isEqualTo(CandidatureSynchronizationState.IGNORE);
    }

    private SpontaneousCandidature persistCandidature(String userId, OffsetDateTime submissionDate, String email, boolean withXP, boolean archived, boolean refused) {
        String ln = "l", fn = "f";
        var userProfileMotherObject = applicationContext.getBean(UserProfileMotherObject.class);
        if (withXP) {
            userProfileMotherObject
                    .withExperienceOnOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist());
        }
        return applicationContext.getBean(SpontaneousCandidatureMotherObject.class)
                .withUserProfile(
                        userProfileMotherObject
                                .withUserId(userId)
                                .withEmail(email)
                                .withFirstname(fn)
                                .withLastname(ln)
                                .withPhoneNumber("0123")
                                .withLocation(Location.builder().city("Pau").postcode("42000").build())
                                .buildAndPersist()
                )
                .withIsArchived(archived)
                .withRefusalData(refused ? CandidatureEmailRefusalState.NONE : null, "me")
                .withRecruiterCode(recruiterCode)
                .withState(GlobalCandidatureState.NEW)
                .withCandidatureSynchronizationState(CandidatureSynchronizationState.WAITING)
                .withSubmissionDate(submissionDate)
                .withAnonymousCode("AAA %s".formatted(email))
                .buildAndPersist();
    }

}
