package com.erhgo.services.notifications;

import com.google.firebase.messaging.BatchResponse;
import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.Message;
import com.google.firebase.messaging.Notification;
import lombok.SneakyThrows;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@ExtendWith(MockitoExtension.class)
class FirebaseNotificationSenderUnitTest {

    private static final String SUBJECT = "notif subject";
    private static final String CONTENT = "notif content";
    private static final String TOKEN = "token";
    private static final Map<String, String> DATA = Map.of("unreadNotificationsCount", "3");

    FirebaseNotificationSender firebaseNotificationSender = new FirebaseNotificationSender();

    MockedStatic<FirebaseMessaging> firebaseMessagingStaticMock;
    @Mock
    FirebaseMessaging firebaseMessagingInstance;
    @Mock
    BatchResponse batchResponse;

    @BeforeEach
    @SneakyThrows
    void prepareMock() {
        firebaseMessagingStaticMock = Mockito.mockStatic(FirebaseMessaging.class);
        firebaseMessagingStaticMock.when(FirebaseMessaging::getInstance).thenReturn(firebaseMessagingInstance);
        Mockito.when(firebaseMessagingInstance.sendEach(ArgumentMatchers.anyList())).thenReturn(batchResponse);
    }

    @AfterEach
    void reset() {
        firebaseMessagingStaticMock.close();
    }

    @Test
    @SneakyThrows
    void sendNotificationToSingleDevice() {
        var unreadNotifications = Set.of(TOKEN).stream().collect(Collectors.toMap(x -> x, y -> 3L));
        firebaseNotificationSender.sendNotificationToSpecificDevices(unreadNotifications, SUBJECT, CONTENT, DATA);
        Mockito.verify(firebaseMessagingInstance).sendEach(Mockito.argThat((List<Message> l) -> l.size() == 1 && verifNotification(l.get(0), true)));
    }

    @Test
    @SneakyThrows
    void sendNotificationToManyDevices_generatesMultipleFirebaseRequests() {
        FirebaseNotificationSender.MAX_TOKEN_PER_MESSAGES_REQUEST = 5;
        firebaseNotificationSender.sendNotificationToSpecificDevices(IntStream.range(0, 24).mapToObj("token%d"::formatted).collect(Collectors.toMap(x -> x, y -> 3L)), SUBJECT, CONTENT, DATA);
        Mockito.verify(firebaseMessagingInstance, Mockito.times(4)).sendEach(Mockito.argThat((List<Message> l) -> l.size() == 5 && l.stream().allMatch(i -> this.verifNotification(i, false))));
        Mockito.verify(firebaseMessagingInstance).sendEach(Mockito.argThat((List<Message> l) -> l.size() == 4 && l.stream().allMatch(i -> this.verifNotification(i, false))));
        Mockito.verifyNoMoreInteractions(firebaseMessagingInstance);
    }

    private boolean verifNotification(Message message, boolean verifToken) {
        var notification = (Notification) ReflectionTestUtils.getField(message, "notification");
        return ReflectionTestUtils.getField(notification, "title").equals(SUBJECT)
                && ReflectionTestUtils.getField(notification, "body").equals(CONTENT)
                && (!verifToken || ReflectionTestUtils.getField(message, "token").equals(TOKEN))
                && ReflectionTestUtils.getField(message, "data").equals(DATA)
                ;
    }
}
