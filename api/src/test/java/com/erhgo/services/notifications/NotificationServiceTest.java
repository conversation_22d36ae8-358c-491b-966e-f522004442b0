package com.erhgo.services.notifications;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

class NotificationServiceTest extends AbstractIntegrationTest {
    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private NotificationService notificationService;

    @MockitoBean
    private NotificationSenderInterface notificationSender;

    @Test
    @ResetDataAfter
    void sendTestNotificationTest() {
        applicationContext.getBean(UserProfileMotherObject.class).withUserId("01804453-cf61-4885-beae-e6c24a7ef6a9").withMobileToken("aaaaa").buildAndPersist();
        applicationContext.getBean(UserProfileMotherObject.class).withUserId("cae1c269-acbf-405c-8f68-cd47390f7a4d").withMobileToken("bbbbb").buildAndPersist();

        notificationService.sendTestNotifications();
        Mockito.verify(notificationSender).sendNotificationToSpecificDevices(ArgumentMatchers.argThat(l -> l.size() == 2), anyString(), anyString(), any());
    }
}
