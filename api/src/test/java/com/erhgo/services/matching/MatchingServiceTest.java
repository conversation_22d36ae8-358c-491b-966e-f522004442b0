package com.erhgo.services.matching;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.enums.AcquisitionModality;
import com.erhgo.domain.enums.Frequency;
import com.erhgo.domain.job.ContextsForCategory;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.job.Mission;
import com.erhgo.domain.recruitment.OptionalActivity;
import com.erhgo.domain.recruitment.OptionalContext;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.recruitment.RecruitmentProfile;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.experience.JobContextMet;
import com.erhgo.domain.userprofile.experience.UserExperience;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.JobActivityLabelGenerator;
import com.erhgo.generators.UserExperienceGenerator;
import com.erhgo.generators.UserProfileGenerator;
import org.junit.jupiter.api.Test;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Arrays.asList;
import static java.util.Collections.*;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;
import static org.assertj.core.api.Assertions.assertThat;

class MatchingServiceTest {
    private MatchingService matchingService = new MatchingService(null);
    private static final Capacity low_level_capacity = CapacityGenerator.buildCapacity();
    private static final Capacity high_level_capacity = CapacityGenerator.buildCapacity();

    private static final Capacity high_level_capacity_with_induced_capacity = CapacityGenerator.buildCapacityWithInducedCapacity(low_level_capacity);
    private static final Capacity highest_level_capacity_with_induced_capacity = CapacityGenerator.buildCapacityWithInducedCapacity(high_level_capacity_with_induced_capacity);

    private static final Context CONTEXT = new Context();

    static {
        CONTEXT.setId(UUID.randomUUID());
        CONTEXT.setIndex(1L);
    }

    private static final AcquisitionModality ACQUISITION_MODALITY = AcquisitionModality.SELF_LEARNING;

    private final UserExperience emptyExperience = UserExperience.builder()
            .build();

    private final UserExperience experienceWithUserDefinedContext = UserExperience.builder()
            .build();

    private final UserExperience experienceWithUserDefinedActivity = UserExperience.builder()
            .build();

    @Test
    void a_candidate_missing_1_capacity_should_be_rejected() {
        var activityLabel = JobActivityLabelGenerator.buildActivityWithCapacities(low_level_capacity);
        Job job = buildJob(singleton(activityLabel), emptyList());
        var candidature = buildCandidature(buildRecruitmentProfileWithAllMandatory(job), singleton(emptyExperience));

        var result = matchingService.computeMatchingResult(candidature);
        assertThat(result.matches()).isFalse();
    }

    @Test
    void a_candidate_missing_1_optional_capacity_should_pass() {
        var activityLabel = JobActivityLabelGenerator.buildActivityWithCapacities(low_level_capacity);
        Job job = buildJob(singleton(activityLabel), emptyList());
        var candidature = buildCandidature(buildRecruitmentProfileWithAllActivitiesOptional(job), singleton(emptyExperience));

        var result = matchingService.computeMatchingResult(candidature);

        assertThat(result.matches()).isTrue();
        assertThat(result.getActivities()).hasSize(1);
        assertThat(result.getActivities()).allMatch(matchingResultForMissingActivity(activityLabel, false));
    }

    private Predicate<MatchingResult.MatchingActivity> matchingResultForMissingActivity(JobActivityLabel activity, boolean mandatory) {
        return a -> a.getLevel() == null
                && !a.isMatches()
                && a.getJobActivityLabel().equals(activity)
                && a.getInOwn() == null
                && a.isMandatory() == mandatory;
    }

    @Test
    void a_candidate_missing_1_context_should_be_rejected() {
        Job job = buildJob(emptySet(), singletonList(CONTEXT));
        var candidature = buildCandidature(buildRecruitmentProfileWithAllMandatory(job), singleton(emptyExperience));

        var result = matchingService.computeMatchingResult(candidature);

        assertThat(result.matches()).isFalse();

    }

    @Test
    void a_candidate_missing_1_optional_context_should_pass() {
        Job job = buildJob(emptySet(), singletonList(CONTEXT));
        var candidature = buildCandidature(buildRecruitmentProfileWithAllContextsOptional(job), singleton(emptyExperience));

        var result = matchingService.computeMatchingResult(candidature);

        assertThat(result.matches()).isTrue();
        assertThat(result.getActivities()).isEmpty();
    }

    @Test
    void a_candidate_with_user_defined_context_should_not_match_on_context() {
        Job job = buildJob(emptySet(), singletonList(CONTEXT));
        var candidature = buildCandidature(buildRecruitmentProfileWithAllMandatory(job), singleton(experienceWithUserDefinedContext));

        var result = matchingService.computeMatchingResult(candidature);

        assertThat(result.getContexts()).hasSize(1);
        var context = result.getContexts().iterator().next();
        assertThat(context.getAcquisitionModality()).isEqualTo(null);
        assertThat(context.getContext()).isEqualTo(CONTEXT);
        assertThat(context.isMandatory()).isTrue();
        assertThat(context.isMatches()).isFalse();
        assertThat(result.getActivities()).isEmpty();
    }

    @Test
    void a_candidate_with_user_defined_activity_should_not_match_on_activity() {
        var activityLabel = JobActivityLabelGenerator.buildActivityWithCapacities(low_level_capacity);
        Job job = buildJob(singleton(activityLabel), emptyList());
        var candidature = buildCandidature(buildRecruitmentProfileWithAllMandatory(job), singleton(experienceWithUserDefinedActivity));

        var result = matchingService.computeMatchingResult(candidature);

        assertThat(result.matches()).isFalse();
        assertThat(result.getActivities()).hasSize(1);
        assertThat(result.getActivities()).allMatch(matchingResultForMissingActivity(activityLabel, true));
    }

    @Test
    void a_candidate_with_all_capacities_and_contexts_should_pass() {
        var capacities = asList(low_level_capacity, high_level_capacity);
        var jobActivities = capacities.stream()
                .map(JobActivityLabelGenerator::buildActivityWithCapacities)
                .collect(toSet());
        var userExperienceActivities = capacities.stream()
                .map(JobActivityLabelGenerator::buildActivityWithCapacities)
                .collect(toList());
        var contexts = asList(getContext(UUID.randomUUID()), getContext(UUID.randomUUID()));
        var job = buildJob(jobActivities, contexts);
        var experience = buildCandidatureExperience(userExperienceActivities);
        var candidature = buildCandidature(buildRecruitmentProfileWithAllMandatory(job), singleton(experience), contexts);

        var result = matchingService.computeMatchingResult(candidature);

        assertThat(result.matches()).isTrue();
        assertThat(result.getActivities()).hasSize(2);
        assertThat(result.getActivities()).allMatch(a -> verifyMatchingActivityNotOwn(a, MasteryLevel.DEFAULT_LEVEL_AS_INT));
    }

    private boolean verifyMatchingActivityNotOwn(MatchingResult.MatchingActivity a, int expectedScore) {
        return !a.getInOwn() && a.isMatches() && a.getLevel().equals(expectedScore) && a.isMandatory();
    }

    @Test
    void a_candidate_with_capacity_coming_from_induced_capacity_should_pass_default_score() {
        var jobActivities = Stream.of(low_level_capacity)
                .map(JobActivityLabelGenerator::buildActivityWithCapacities)
                .collect(toSet());

        var userExperienceActivities = Stream.of(high_level_capacity_with_induced_capacity)
                .map(JobActivityLabelGenerator::buildActivityWithCapacities)
                .collect(toList());

        List<Context> contexts = emptyList();
        Job job = buildJob(jobActivities, contexts);
        var experience = buildCandidatureExperienceWithScore(userExperienceActivities, 2);
        var candidature = buildCandidature(buildRecruitmentProfileWithAllMandatory(job), singleton(experience));

        var result = matchingService.computeMatchingResult(candidature);

        assertThat(result.matches()).isTrue();
        assertThat(result.getActivities()).hasSize(1);
        assertThat(result.getActivities()).allMatch(a -> verifyMatchingActivityNotOwn(a, MasteryLevel.DEFAULT_LEVEL_AS_INT));
    }

    @Test
    void a_candidate_with_capacity_coming_from_induced_capacity_of_induced_capacity_should_pass_default_score() {
        var jobActivities = Stream.of(low_level_capacity)
                .map(JobActivityLabelGenerator::buildActivityWithCapacities)
                .collect(toSet());
        var userExperienceActivities = Stream.of(highest_level_capacity_with_induced_capacity)
                .map(JobActivityLabelGenerator::buildActivityWithCapacities)
                .collect(toList());
        List<Context> contexts = emptyList();
        Job job = buildJob(jobActivities, contexts);
        var experience = buildCandidatureExperienceWithScore(userExperienceActivities, 3);
        var candidature = buildCandidature(buildRecruitmentProfileWithAllMandatory(job), singleton(experience));

        var result = matchingService.computeMatchingResult(candidature);

        assertThat(result.matches()).isTrue();
        assertThat(result.getActivities()).hasSize(1);
        assertThat(result.getActivities()).allMatch(a -> verifyMatchingActivityNotOwn(a, MasteryLevel.DEFAULT_LEVEL_AS_INT));
    }

    @Test
    void a_candidate_with_capacity_with_induced_capacity_should_pass() {
        var jobActivities = Stream.of(highest_level_capacity_with_induced_capacity)
                .map(JobActivityLabelGenerator::buildActivityWithCapacities)
                .collect(toSet());
        var userExperienceActivities = Stream.of(highest_level_capacity_with_induced_capacity)
                .map(JobActivityLabelGenerator::buildActivityWithCapacities)
                .collect(toList());
        List<Context> contexts = emptyList();
        Job job = buildJob(jobActivities, contexts);
        var score = 4;
        var experience = buildCandidatureExperienceWithScore(userExperienceActivities, score);
        var candidature = buildCandidature(buildRecruitmentProfileWithAllMandatory(job), singleton(experience));

        var result = matchingService.computeMatchingResult(candidature);

        assertThat(result.matches()).isTrue();
        assertThat(result.getActivities()).hasSize(1);
        assertThat(result.getActivities()).allMatch(a -> verifyMatchingActivityNotOwn(a, score));
    }

    @Test
    void a_candidate_with_all_capacities_induced_in_high_level_capacity_should_not_pass() {
        var jobActivities = Stream.of(high_level_capacity_with_induced_capacity)
                .map(JobActivityLabelGenerator::buildActivityWithCapacities)
                .collect(toSet());
        var userExperienceActivities = Stream.of(low_level_capacity)
                .map(JobActivityLabelGenerator::buildActivityWithCapacities)
                .collect(toList());
        List<Context> contexts = emptyList();
        Job job = buildJob(jobActivities, contexts);
        var experience = buildCandidatureExperience(userExperienceActivities);
        var candidature = buildCandidature(buildRecruitmentProfileWithAllMandatory(job), singleton(experience));

        var result = matchingService.computeMatchingResult(candidature).matches();

        assertThat(result).isFalse();
    }

    @Test
    void a_candidate_with_EPA_capacities_should_pass() {
        var jobActivity = JobActivityLabelGenerator.buildActivityWithCapacities(low_level_capacity);

        var contexts = asList(getContext(UUID.randomUUID()), getContext(UUID.randomUUID()));
        Job job = buildJob(Set.of(jobActivity), contexts);
        var userProfile = UserProfileGenerator.buildUserProfileWithCapacityFromEPA(low_level_capacity);
        var candidature = buildCandidature(buildRecruitmentProfileWithAllContextsOptional(job), userProfile);

        var result = matchingService.computeMatchingResult(candidature);

        assertThat(result.matches()).isTrue();
        assertThat(result.getActivities()).hasSize(1);
        assertThat(result.getActivities()).allMatch(a -> verifyMatchingActivityNotOwn(a, MasteryLevel.DEFAULT_LEVEL_AS_INT));
    }

    @Test
    void a_candidate_with_EPA_induced_capacities_should_pass() {
        var jobActivity = JobActivityLabelGenerator.buildActivityWithCapacities(low_level_capacity);

        var contexts = asList(getContext(UUID.randomUUID()), getContext(UUID.randomUUID()));
        Job job = buildJob(Set.of(jobActivity), contexts);
        var userProfile = UserProfileGenerator.buildUserProfileWithCapacityFromEPA(highest_level_capacity_with_induced_capacity);
        var candidature = buildCandidature(buildRecruitmentProfileWithAllContextsOptional(job), userProfile);

        var result = matchingService.computeMatchingResult(candidature);

        assertThat(result.matches()).isTrue();
        assertThat(result.getActivities()).hasSize(1);
        assertThat(result.getActivities()).allMatch(a -> verifyMatchingActivityNotOwn(a, MasteryLevel.DEFAULT_LEVEL_AS_INT));
    }

    @Test
    void a_candidate_with_all_capacities_should_pass() {
        var capacities = asList(low_level_capacity, high_level_capacity);
        var jobActivities = capacities.stream()
                .map(JobActivityLabelGenerator::buildActivityWithCapacities)
                .collect(toSet());

        var contexts = asList(getContext(UUID.randomUUID()), getContext(UUID.randomUUID()));
        Job job = buildJob(jobActivities, contexts);
        var userProfile = UserProfileGenerator.buildUserProfileWithCapacityFromEPA(low_level_capacity);
        UserExperienceGenerator.buildExperienceWithCapacity(userProfile, MasteryLevel.DEFAULT_LEVEL_AS_INT, capacities.get(1));

        var candidature = buildCandidature(buildRecruitmentProfileWithAllContextsOptional(job), userProfile);

        var result = matchingService.computeMatchingResult(candidature);

        assertThat(result.matches()).isTrue();
        assertThat(result.getActivities()).hasSize(2);
        assertThat(result.getActivities()).allMatch(a -> verifyMatchingActivityNotOwn(a, MasteryLevel.DEFAULT_LEVEL_AS_INT));
    }

    private RecruitmentCandidature buildCandidature(RecruitmentProfile recruitmentProfile, Set<UserExperience> userExperiences) {
        return this.buildCandidature(recruitmentProfile, userExperiences, Collections.emptySet());
    }

    private RecruitmentCandidature buildCandidature(RecruitmentProfile recruitmentProfile, Set<UserExperience> userExperiences, Collection<Context> contexts) {
        var userProfile = new UserProfile().uuid(UUID.randomUUID()).experiences(userExperiences);

        userProfile.contextsMet(contexts.stream().map(c -> JobContextMet.builder()
                .context(c)
                .frequency(Frequency.HIGH)
                .userExperiences(userExperiences)
                .build()).collect(Collectors.toSet()));

        return RecruitmentCandidature.builder()
                .userProfile(userProfile)
                .recruitment(Recruitment.builder().recruitmentProfile(recruitmentProfile).build())
                .build();
    }

    private RecruitmentCandidature buildCandidature(RecruitmentProfile recruitmentProfile, UserProfile userProfile) {
        return RecruitmentCandidature.builder()
                .userProfile(userProfile)
                .recruitment(Recruitment.builder().recruitmentProfile(recruitmentProfile).build())
                .build();
    }

    private RecruitmentProfile buildRecruitmentProfileWithAllMandatory(Job job) {
        return buildRecruitmentProfile(job, false, false);
    }

    private RecruitmentProfile buildRecruitmentProfileWithAllActivitiesOptional(Job job) {
        return buildRecruitmentProfile(job, true, false);
    }

    private RecruitmentProfile buildRecruitmentProfileWithAllContextsOptional(Job job) {
        return buildRecruitmentProfile(job, false, true);
    }

    private RecruitmentProfile buildRecruitmentProfile(Job job, boolean allActivitiesOptional, boolean allContextsOptional) {
        return RecruitmentProfile.builder()
                .job(job)
                .optionalActivities(allActivitiesOptional ?
                        job.getAllMissionsActivities().stream().map(activityLabel -> OptionalActivity.builder().acquisitionModality(ACQUISITION_MODALITY).activityLabel(activityLabel).build()).collect(toSet())
                        : emptySet())
                .optionalContexts(allContextsOptional ?
                        job.getAllMissionsContexts().stream().map(context -> OptionalContext.builder().context(context).acquisitionModality(ACQUISITION_MODALITY).build()).collect(toSet())
                        : emptySet())
                .build();
    }

    private Job buildJob(Set<JobActivityLabel> activities, List<Context> contexts) {
        var job = Job.builder()
                .missions(new TreeSet<>(singletonList(Mission.builder()
                        .activities(activities)
                        .contextsForCategory(contexts.stream().map(context -> ContextsForCategory.builder().contexts(singleton(context)).build()).collect(toSet()))
                        .build())))
                .build();
        job.setId(UUID.randomUUID());
        return job;
    }

    private Context getContext(UUID id) {
        var context = new Context();
        context.setId(id);
        return context;
    }

    /**
     * @return experience with 1 mission
     */
    private UserExperience buildCandidatureExperience(List<JobActivityLabel> jobActivityLabels) {
        return buildCandidatureExperienceWithScore(jobActivityLabels, null);

    }

    private UserExperience buildCandidatureExperienceWithScore(List<JobActivityLabel> jobActivityLabels, Integer score) {
        var erhgoOccupation = new ErhgoOccupationMotherObject()
                .withLevel(score != null ? MasteryLevel.values()[score - 1] : null)
                .withOptionalActivities(jobActivityLabels.toArray(JobActivityLabel[]::new))
                .instance();
        return UserExperience.builder()
                .erhgoOccupation(erhgoOccupation)
                .build();
    }

}
