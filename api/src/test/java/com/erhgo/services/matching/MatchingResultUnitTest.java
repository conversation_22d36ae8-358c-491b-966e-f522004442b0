package com.erhgo.services.matching;

import com.erhgo.domain.enums.AcquisitionModality;
import com.erhgo.domain.referential.CategoryLevel;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.JobActivityLabel;
import com.google.common.collect.Sets;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.erhgo.domain.enums.AcquisitionModality.*;
import static com.google.common.collect.Lists.newArrayList;

class MatchingResultUnitTest {
    @Test
    void matching_result_without_context_and_activity_should_return_0_effort() {
        var matchingResult = MatchingResult.builder().build();

        Assertions.assertThat(matchingResult.getEffort()).isZero();
    }

    @Test
    void matching_result_with_matching_optional_context_and_without_activity_should_return_0_effort() {
        var matchingResult = resultWithMatchingContext();

        Assertions.assertThat(matchingResult.getEffort()).isZero();
    }

    @Test
    void matching_result_with_matching_optional_context_and_activity_should_return_0_effort() {
        var matchingResult = resultWithMatchingContextAndActivity();

        Assertions.assertThat(matchingResult.getEffort()).isZero();
    }

    @Test
    void matching_result_with_missing_optional_context_of_score_20_and_Self_Learning_modality_and_no_missing_activity_should_return_20_effort() {
        var matchingResult = resultWithMatchingActivityAndMissingContextForScores(newArrayList(20), newArrayList(SELF_LEARNING));

        Assertions.assertThat(matchingResult.getEffort()).isEqualTo(20);
    }

    @Test
    void matching_result_with_two_missing_optional_contexts_of_scores_20_and_15_and_Self_Learning_modality_and_no_missing_activity_should_return_35_effort() {
        var matchingResult = resultWithMatchingActivityAndMissingContextForScores(newArrayList(20, 15), newArrayList(SELF_LEARNING, SELF_LEARNING));

        Assertions.assertThat(matchingResult.getEffort()).isEqualTo(35);
    }

    @Test
    void matching_result_with_two_missing_optional_contexts_of_scores_20_and_12_and_Self_Learning_and_integration_process_modalities_and_no_missing_activity_should_return_44_effort() {
        var matchingResult = resultWithMatchingActivityAndMissingContextForScores(newArrayList(20, 12), newArrayList(SELF_LEARNING, INTEGRATION_PROCESS));

        Assertions.assertThat(matchingResult.getEffort()).isEqualTo(44);
    }

    @Test
    void matching_result_without_missing_context_and_one_missing_activity_of_score_20_and_modality_Self_Training_should_return_20_effort() {
        var matchingResult = resultWithMatchingContextAndMissingActivityForScores(newArrayList(SELF_LEARNING));

        Assertions.assertThat(matchingResult.getEffort()).isEqualTo(20);
    }

    @Test
    void matching_result_complex_should_return_effort() {
        var m = resultWithMatchingActivityAndMissingContextForScores(newArrayList(6, 7, 8), newArrayList(SELF_LEARNING, INTEGRATION_PROCESS, TRAINING));
        var m2 = resultWithMatchingContextAndMissingActivityForScores(newArrayList(TRAINING, SELF_LEARNING, INTEGRATION_PROCESS));
        m.setActivities(m2.getActivities());

        Assertions.assertThat(m.getEffort()).isEqualTo((1 + 2 + 3) * 20 + (6 + 2 * 7 + 3 * 8));
    }

    @Test
    void matching_result_with_mandatory_and_matching_elements_should_return_effort() {
        var m = resultWithMatchingActivityAndMissingContextForScores(newArrayList(6, 7, 8), newArrayList(SELF_LEARNING, INTEGRATION_PROCESS, TRAINING));
        var m2 = resultWithMatchingContextAndMissingActivityForScores(newArrayList(TRAINING, SELF_LEARNING, INTEGRATION_PROCESS));
        m.setActivities(m2.getActivities());

        m.getActivities().add(MatchingResult.MatchingActivity.builder().mandatory(true).matches(true).jobActivityLabel(new JobActivityLabel()).build());
        m.getContexts().add(MatchingResult.MatchingContext.builder().mandatory(true).matches(true).context(contextWithScore(250)).build());

        Assertions.assertThat(m.getEffort()).isEqualTo((1 + 2 + 3) * 20 + (6 + 2 * 7 + 3 * 8));
    }

    private MatchingResult resultWithMatchingContextAndMissingActivityForScores(List<AcquisitionModality> modalities) {
        var activities = modalities.stream()
                .map(modality -> MatchingResult.MatchingActivity.builder()
                        .matches(false)
                        .mandatory(false)
                        .jobActivityLabel(new JobActivityLabel())
                        .acquisitionModality(modality)
                        .build()).collect(Collectors.toSet());

        return MatchingResult.builder()
                .contexts(Sets.newHashSet(MatchingResult.MatchingContext.builder()
                        .matches(true)
                        .mandatory(false).build()))
                .activities(activities)
                .build();
    }


    private MatchingResult resultWithMatchingActivityAndMissingContextForScores(List<Integer> scores, List<AcquisitionModality> modalities) {
        var contexts = IntStream.range(0, scores.size())
                .mapToObj(index -> MatchingResult.MatchingContext.builder()
                        .matches(false)
                        .mandatory(false)
                        .context(contextWithScore(scores.get(index)))
                        .acquisitionModality(modalities.get(index))
                        .build()).collect(Collectors.toSet());

        return MatchingResult.builder()
                .contexts(contexts)
                .activities(Sets.newHashSet(MatchingResult.MatchingActivity.builder()
                        .matches(true)
                        .mandatory(false).build())).build();
    }

    private Context contextWithScore(int score) {
        var context = new Context();
        context.setId(UUID.randomUUID());
        var categoryLevel = new CategoryLevel();
        categoryLevel.setScore(score);
        context.setCategoryLevel(categoryLevel);
        return context;
    }

    private MatchingResult resultWithMatchingContextAndActivity() {
        return MatchingResult.builder()
                .contexts(Sets.newHashSet(MatchingResult.MatchingContext.builder()
                        .matches(true)
                        .mandatory(false).build()))
                .activities(Sets.newHashSet(MatchingResult.MatchingActivity.builder()
                        .matches(true)
                        .mandatory(false).build())).build();
    }

    private MatchingResult resultWithMatchingContext() {
        return MatchingResult.builder()
                .contexts(Sets.newHashSet(MatchingResult.MatchingContext.builder().matches(true).mandatory(false).build()))
                .build();
    }
}
