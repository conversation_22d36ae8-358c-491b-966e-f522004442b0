package com.erhgo.services.keycloak;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;

import java.util.List;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class KeycloakCacheWarmUpUnitTest {

    @InjectMocks
    KeycloakCacheWarmUp keycloakCacheWarmUp;

    @Mock
    CacheManager cacheManager;

    @Mock
    KeycloakService keycloakService;

    @Mock
    Cache allUsersCache;

    @Mock
    Cache singleUserCache;

    @Mock
    UserRepresentation userRepresentation;

    @Test
    void cache_warmup_should_clear_previous_list_and_add_each_candidate() {
        var userId = "40";
        when(cacheManager.getCache(KeycloakService.FO_ALL_USERS_CACHE)).thenReturn(allUsersCache);
        when(cacheManager.getCache(KeycloakService.FO_USER_REPRESENTATION_CACHE)).thenReturn(singleUserCache);
        when(keycloakService.findAllFrontOfficeUsers()).thenReturn(List.of(userRepresentation));
        when(userRepresentation.getId()).thenReturn(userId);

        keycloakCacheWarmUp.warmupCache();
        Mockito.verify(allUsersCache).clear();
        Mockito.verify(singleUserCache).put(userId, userRepresentation);

    }
}
