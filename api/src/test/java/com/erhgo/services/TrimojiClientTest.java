package com.erhgo.services;

import com.erhgo.config.KeycloakMockService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.trimoji.TrimojiClientImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

class TrimojiClientTest {


    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void sendTrimojiRequest(boolean noName) {
        var restTemplate = Mockito.mock(RestTemplate.class);
        var objectMapper = new ObjectMapper();
        var keycloakService = new KeycloakMockService();
        var securityService = Mockito.mock(SecurityService.class);
        var userId = "id";
        var email = "e";
        var firstName = noName ? null : "f";
        var lastName = noName ? null : "l";
        var trimojiRequestTemplateJson = """
                {"candidate_email": "%USER_EMAIL%","candidate_first_name": "%USER_FIRSTNAME%","candidate_last_name": "%USER_LASTNAME%","customer_token": "f3b06b20-a889-460b-b51d-7c5f415d6ded","is_trimoji_sending": false,"callback_url": "%CALLBACK_URL%","no_result": true,"test_duration": "short","metadatas": {"candidate_id": "%USER_ID%"}}
                """;
        var trimojiApiKey = "trimojiApiKey";
        var trimojiUrlJsonPath = "$.url";
        var trimojiUrl = "url";
        var callbackUrl = "url";
        var expectedUrl = "http://tri.mo.ji";

        when(securityService.getAuthenticatedUserId()).thenReturn(userId);
        when(restTemplate.postForObject(anyString(), anyString(), eq(String.class))).thenReturn("""
                {"url": "%s"}
                """.formatted(expectedUrl));
        keycloakService.setUserProfile(userId, new UserRepresentation().setEmail(email).setFirstName(firstName).setLastName(lastName).setId("42"));

        var client = new TrimojiClientImpl(keycloakService, securityService, null, objectMapper);
        ReflectionTestUtils.setField(client, "restTemplate", restTemplate);
        ReflectionTestUtils.setField(client, "trimojiRequestTemplateJson", trimojiRequestTemplateJson);
        ReflectionTestUtils.setField(client, "trimojiApiKey", trimojiApiKey);
        ReflectionTestUtils.setField(client, "trimojiUrlJsonPath", trimojiUrlJsonPath);
        ReflectionTestUtils.setField(client, "trimojiUrl", trimojiUrl);
        ReflectionTestUtils.setField(client, "callbackUrl", callbackUrl);
        client.initializeJsonFormatter();
        var url = client.getNewUrl();
        assertEquals(expectedUrl, url);
        Mockito.verify(restTemplate).postForObject("url", """
                {"candidate_email": "%EMAIL%","candidate_first_name": "%FN%","candidate_last_name": "%LN%","customer_token": "f3b06b20-a889-460b-b51d-7c5f415d6ded","is_trimoji_sending": false,"callback_url": "url","no_result": true,"test_duration": "short","metadatas": {"candidate_id": "42"}}
                """, String.class);
    }
}
