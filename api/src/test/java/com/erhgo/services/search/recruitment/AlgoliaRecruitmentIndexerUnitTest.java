package com.erhgo.services.search.recruitment;

import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.ContractType;
import com.erhgo.domain.job.JobMotherObject;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.JobActivityLabelGenerator;
import com.erhgo.generators.RecruiterMotherObject;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.Calendar;
import java.util.Date;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;

class AlgoliaRecruitmentIndexerUnitTest {

    @ParameterizedTest
    @ValueSource(booleans = {false, true})
    void generateDTO(boolean hideSalaryAndVeryLongDescription) {
        var classificationTitle = "42";
        var alternativeLabel = "alt";
        var occupationTitle = "occupation";
        var jobTitle = "jt";
        var criteriaTitle = "ct";
        var descr = "<a>" + "d".repeat(hideSalaryAndVeryLongDescription ? 11_000 : 1) + "</a>";
        var actLabel = "act";
        var recruiter = "recruit";
        var city = "c";
        int salaryMin = 17;
        int salaryMax = 42000;
        int weeklyWorkDuration = 42;
        var orgaCode = "S-42";
        var recruitment = new RecruitmentMotherObject()
                .withLocation(Location.builder().city(city).longitude(5f).latitude(6f).build())
                .withErhgoClassification(Set.of(ErhgoClassification.builder().title(classificationTitle).build()))
                .withOccupation(new ErhgoOccupationMotherObject().withAlternativeLabel(alternativeLabel).withTitle(occupationTitle).build())
                .withJob(
                        new JobMotherObject().withTitle(jobTitle).withMissionActivities(JobActivityLabelGenerator.buildActivityLabel(actLabel, CapacityGenerator.buildCapacity()))
                                .withCriteriaValues(
                                        CriteriaValue.builder().titleStandalone(criteriaTitle).build(),
                                        CriteriaValue.builder().code("REP-2-1").build(),
                                        CriteriaValue.builder().code("REP-1-2").build()
                                )
                                .build())
                .withDescription(descr)
                .withTypeContract(ContractType.CDD)
                .withRecruiter(new RecruiterMotherObject().withTitle(recruiter).withCode(orgaCode).build())
                .withSalaries(salaryMin, salaryMax)
                .withWorkingWeeklyTime(weeklyWorkDuration)
                .withModularWorkingTime(true)
                .withHideSalary(hideSalaryAndVeryLongDescription)
                .withPublicationDate(new Date(124, Calendar.MAY, 5))
                .build();

        recruitment.getJob().setCriteriaValues(Set.of(CriteriaValue.builder().code(CriteriaValue.REMOTE_WORK_CRITERIA_VALUE_CODE_PREFIX + "-1").titleForBO("bim").build()));

        var dto = new AlgoliaRecruitmentIndexer.AlgoliaRecruitmentDTO(recruitment);
        assertThat(dto.getObjectID()).isEqualTo(recruitment.getCode());
        assertThat(dto.getId()).isEqualTo(recruitment.getId());
        assertThat(dto.getTitle()).isEqualTo(jobTitle);
        assertThat(dto.getKeywords()).containsExactly(classificationTitle);
        assertThat(dto.getAlternativeLabels()).containsExactly(alternativeLabel);
        assertThat(dto.getContractType()).isEqualTo("CDD");
        assertThat(dto.getActivities()).containsExactly(actLabel);
        assertThat(dto.getDescription()).isEqualTo(hideSalaryAndVeryLongDescription ? ("d".repeat(10_000 - 3) + "...") : "d");
        assertThat(dto.getRecruiter()).isEqualTo(recruiter);
        assertThat(dto.getCity()).isEqualTo(city);
        assertThat(dto.getSalaryMin()).isEqualTo(salaryMin);
        assertThat(dto.getSalaryMax()).isEqualTo(salaryMax);
        assertThat(dto.getSalaryLabel()).isEqualTo(hideSalaryAndVeryLongDescription ? "" : "Entre 17 € et 42 000 €");
        assertThat(dto.getWorkDuration()).isEqualTo(weeklyWorkDuration);
        assertThat(dto.getWorkDurationLabel()).isEqualTo("42 heures/semaine (variable)");
        assertThat(dto.getPublicationDate()).isEqualTo(recruitment.getPublicationDate());
        assertThat(dto.getPublicationDateLabel()).isEqualTo("05 mai 2024");
        assertThat(dto.getRemoteWorkLabel()).isEqualTo("bim");
        assertThat(dto.getOrganizationCode()).isEqualTo("S-42");

        assertThat(dto.getGeolocation()).extracting("longitude").isEqualTo(recruitment.getLocation().getLongitude());
        assertThat(dto.getGeolocation()).extracting("latitude").isEqualTo(recruitment.getLocation().getLatitude());

    }
}
