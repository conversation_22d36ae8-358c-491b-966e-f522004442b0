package com.erhgo.services.search;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.services.search.GeoService.FeatureDTO;
import com.erhgo.services.search.GeoService.GeoResponseDTO;
import com.erhgo.services.search.GeoService.GeometryDTO;
import com.erhgo.services.search.GeoService.PropertiesDTO;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class GeoServiceUnitTest {

    private static final String CITY_AND_POSTCODE = "Paris 75000";
    private static final String SOURCE = "testSource";
    private static final Double LATITUDE = 2.3522;
    private static final Double LONGITUDE = 48.8566;
    public static final String CITYCODE = "75056";


    @Mock
    RestTemplate client;

    @InjectMocks
    GeoService geoService;

    @BeforeEach
    void before() {
        ReflectionTestUtils.setField(geoService, "restTemplate", client);
    }

    private GeoResponseDTO createGeoResponse(String city, String postcode) {
        var properties = new PropertiesDTO();
        properties.setCity(city);
        properties.setPostcode(postcode);
        properties.setCitycode(CITYCODE);

        var geometry = new GeometryDTO();
        geometry.setCoordinates(List.of(LATITUDE, LONGITUDE));

        var feature = new FeatureDTO();
        feature.setGeometry(geometry);
        feature.setProperties(properties);

        var responseDTO = new GeoResponseDTO();
        responseDTO.setFeatures(List.of(feature));

        return responseDTO;
    }

    private void mockGeoServiceResponse(int statusCode, GeoResponseDTO body) {
        var mockedResponse = mock(ResponseEntity.class);
        when(mockedResponse.getStatusCode()).thenReturn(HttpStatusCode.valueOf(statusCode));
        when(mockedResponse.getBody()).thenReturn(body);
        when(client.getForEntity(Mockito.any(), Mockito.eq(GeoResponseDTO.class), Mockito.anyMap()))
                .thenReturn(mockedResponse);
    }

    @Test
    void shouldReturnLocationOnSuccess() {
        var responseDTO = createGeoResponse("Paris", "75000");
        mockGeoServiceResponse(200, responseDTO);

        var actualLocation = geoService.fetchGeoCoordinates(CITY_AND_POSTCODE, SOURCE);

        Assertions.assertThat(actualLocation.getCity()).isEqualTo("Paris");
        Assertions.assertThat(actualLocation.getPostcode()).isEqualTo("75000");
        Assertions.assertThat(actualLocation.getCitycode()).isEqualTo("75056");
        Assertions.assertThat(actualLocation.getLongitude()).isEqualTo(LATITUDE.floatValue(), Assertions.within(0.0001f));
        Assertions.assertThat(actualLocation.getLatitude()).isEqualTo(LONGITUDE.floatValue(), Assertions.within(0.0001f));
    }

    @Test
    void shouldThrowExceptionOnStatusError() {
        mockGeoServiceResponse(500, null);
        assertThrows(GenericTechnicalException.class, () -> geoService.fetchGeoCoordinates(CITY_AND_POSTCODE, SOURCE));
    }

    @Test
    void shouldThrowExceptionOnExecutionOrTimeoutException() {
        when(client.getForEntity(Mockito.any(), Mockito.eq(GeoResponseDTO.class), Mockito.anyMap()))
                .thenThrow(new RestClientException("KO", new Exception()));
        assertThrows(GenericTechnicalException.class, () -> geoService.fetchGeoCoordinates(CITY_AND_POSTCODE, SOURCE));
    }

    @Test
    void shouldMakeMultipleCallsIfInitialSearchReturnsEmpty() {
        mockGeoServiceResponse(200, new GeoResponseDTO());
        geoService.fetchGeoCoordinates(CITY_AND_POSTCODE, SOURCE);
        verify(client, times(4)).getForEntity(Mockito.any(), Mockito.eq(GeoResponseDTO.class), Mockito.anyMap());
    }

    private void SingleCallForLocation(String city, String postcode, String search) {
        var responseDTO = createGeoResponse(city, postcode);
        mockGeoServiceResponse(200, responseDTO);
        geoService.fetchGeoCoordinates(search, SOURCE);
        verify(client, times(1)).getForEntity(Mockito.any(), Mockito.eq(GeoResponseDTO.class), Mockito.anyMap());
    }

    @Test
    void shouldMakeOneCallForBron69008() {
        SingleCallForLocation("Bron", "69500", "Bron 69008");
    }

    @Test
    void shouldMakeOneCallForLyonFR69500() {
        SingleCallForLocation("Bron", "69500", "LyonFR69500");
    }

    @Test
    void shouldMakeOneCallForLyon46000() {
        SingleCallForLocation("Lyon", "69001", "Lyon 46000");
    }

    @Test
    void shouldMakeOneCallForCahors69008() {
        SingleCallForLocation("Cahors", "46000", "Cahors 69008");
    }

    @Test
    void shouldMakeThreeCallsForLyonFR69570() {
        var responseDTO = createGeoResponse("Dardilly", "69570");
        var emptyResponse = new GeoResponseDTO();
        mockGeoServiceResponse(200, emptyResponse);
        when(client.getForEntity(Mockito.any(), Mockito.eq(GeoResponseDTO.class), Mockito.anyMap()))
                .thenReturn(ResponseEntity.ok(emptyResponse))
                .thenReturn(ResponseEntity.ok(emptyResponse))
                .thenReturn(ResponseEntity.ok(responseDTO));

        geoService.fetchGeoCoordinates("Lyon, FR, 69570", SOURCE);
        verify(client, times(3)).getForEntity(Mockito.any(), Mockito.eq(GeoResponseDTO.class), Mockito.anyMap());
    }

}
