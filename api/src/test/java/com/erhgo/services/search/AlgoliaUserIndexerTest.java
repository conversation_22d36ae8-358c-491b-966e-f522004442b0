package com.erhgo.services.search;

import com.algolia.search.SearchIndex;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.UUID;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class AlgoliaUserIndexerTest {

    AlgoliaUserIndexer algoliaUserIndexer;
    @Mock
    SearchIndex<AlgoliaUserIndexer.UserDTO> index;
    @Mock
    AlgoliaSearchConfigService algoliaSearchConfigService;

    @BeforeEach
    void initialize() {
        this.algoliaUserIndexer = new AlgoliaUserIndexer(null, algoliaSearchConfigService);
        ReflectionTestUtils.setField(this.algoliaUserIndexer, "userIndex", index);
    }

    @Test
    void buildProfile_tolerates_location_is_null() {
        var userProfile = new UserProfileMotherObject().withLocation(null).build();

        var dto = algoliaUserIndexer.buildProfile(userProfile);

        assertThat(dto.getRadiusInKm()).isNull();
    }

    @Test
    void buildProfile_check_that_dto_has_user_blacklisted_occupations() {
        var userProfile = new UserProfileMotherObject()
                .withBlacklistedOccupation(ErhgoOccupation.builder().title("EO1").id(UUID.randomUUID()).build())
                .withBlacklistedOccupation(ErhgoOccupation.builder().title("EO2").id(UUID.randomUUID()).build())
                .withBlacklistedOccupation(ErhgoOccupation.builder().title("EO3").id(UUID.randomUUID()).build())
                .withBlacklistedOccupation(ErhgoOccupation.builder().title("EO4").id(UUID.randomUUID()).build())
                .build();

        var dto = algoliaUserIndexer.buildProfile(userProfile);
        var userBlacklistedOccupations = userProfile.getBlacklistedOccupations().stream()
                .map(ErhgoOccupation::getId)
                .map(UUID::toString)
                .collect(Collectors.toSet());

        assertThat(dto.getBlacklistedOccupations()).hasSize(4).isEqualTo(userBlacklistedOccupations);
    }
}
