package com.erhgo.services.search;

import com.algolia.search.DefaultSearchClient;
import com.algolia.search.SearchClient;
import com.algolia.search.SearchIndex;
import com.algolia.search.models.indexing.SearchResult;
import com.erhgo.TestUtils;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

/*
 * mockito-inline allows mocking final class.
 * See https://github.com/spring-projects/spring-boot/issues/22416
 */

@ExtendWith(MockitoExtension.class)
class AlgoliaTest {

    Algolia algolia;
    ObjectMapper mapper = new ObjectMapper();
    MockedStatic<DefaultSearchClient> mockedClient;
    @Mock
    SearchIndex index;
    @Mock
    SearchClient searchClient;

    @BeforeEach
    void initialize() {
        mockedClient = Mockito.mockStatic(DefaultSearchClient.class);
        mockedClient.when(() -> DefaultSearchClient.create(Mockito.anyString(), Mockito.anyString())).thenReturn(searchClient);
        Mockito.when(searchClient.initIndex(anyString(), any(Class.class))).thenReturn(index);
        this.algolia = new Algolia("erhgoKey", "toto", "titi", null, null);
    }

    @AfterEach
    void reset() {
        mockedClient.close();
    }


    @Test
    void searchOccupations_withNoHighlight() {
        var query = StringUtils.repeat("A", 499);
        UUID id = UUID.randomUUID();
        var description = "description";
        var title = "title";
        var occupationResult = new Algolia.AlgoliaOccupationDTO(new ErhgoOccupationMotherObject().withTitle(title).withId(id).withDescription(description).instance());
        when(index.search(any())).thenReturn(new SearchResult<Algolia.AlgoliaOccupationDTO>().setHits(List.of(occupationResult)));

        var result = algolia.searchOccupations(query, false);

        assertThat(result).hasSize(1);
        assertThat(result.get(0)).matches(a -> a.getTitle().equals(title) && a.getCode().equals(id.toString()));
        verify(index).search(argThat(q -> q.getQuery().equals(query) && q.getAttributesToHighlight().isEmpty()));
    }

    @Test
    void searchOccupationsFailsForTooLongQuery() {
        var query = StringUtils.repeat("A", 501);
        try {
            algolia.searchOccupations(query, false);
            fail();
        } catch (IllegalArgumentException e) {
            assertThat(e.getMessage().toLowerCase()).contains("too big");
        }
        Mockito.verify(index, Mockito.never()).search(any());
    }

    @Test
    void searchOccupations_addTitleToDuplicatedAlternativeLabel() {
        var title1 = "title1";
        var title2 = "title2";
        var duplicatedLabel = "duplicated";
        var occupationResult1 = new Algolia.AlgoliaOccupationDTO(new ErhgoOccupationMotherObject().withTitle(title1).withId(UUID.randomUUID()).withAlternativeLabel(duplicatedLabel).instance());
        var occupationResult2 = new Algolia.AlgoliaOccupationDTO(new ErhgoOccupationMotherObject().withTitle(title2).withId(UUID.randomUUID()).withAlternativeLabel(duplicatedLabel).instance());
        emulateHighlightResultOnLabelAndNotTitle(occupationResult1, duplicatedLabel);
        emulateHighlightResultOnLabelAndNotTitle(occupationResult2, duplicatedLabel);
        when(index.search(any())).thenReturn(new SearchResult<Algolia.AlgoliaOccupationDTO>().setHits(List.of(occupationResult1, occupationResult2)));

        var result = algolia.searchOccupations(duplicatedLabel, true);

        assertThat(result).hasSize(2)
                .anyMatch(dto -> dto.getTitle().equals("%s (%s)".formatted(wrapMatchWordWithStrongTag(duplicatedLabel, duplicatedLabel), title1)))
                .anyMatch(dto -> dto.getTitle().equals("%s (%s)".formatted(wrapMatchWordWithStrongTag(duplicatedLabel, duplicatedLabel), title2)));
        verify(index).search(argThat(q -> q.getAttributesToHighlight().contains("title") && q.getAttributesToHighlight().contains("alternativeLabels")));
    }

    @Test
    void searchOccupations_addTitleWhenNoDuplicatedLabels() {
        var label1 = "label1";
        var label2 = "label2";
        var label3 = "label3";
        var matchedWord = "label";
        var occupationResult1 = new Algolia.AlgoliaOccupationDTO(new ErhgoOccupationMotherObject().withTitle(label1).withId(UUID.randomUUID()).instance());
        var occupationResult2 = new Algolia.AlgoliaOccupationDTO(new ErhgoOccupationMotherObject().withTitle(label2).withId(UUID.randomUUID()).instance());

        var occupationResult3 = new Algolia.AlgoliaOccupationDTO(new ErhgoOccupationMotherObject().withTitle("title").withAlternativeLabel(label3).withId(UUID.randomUUID()).instance());

        emulateHighlightResultOnTitle(occupationResult1, matchedWord);
        emulateHighlightResultOnTitle(occupationResult2, matchedWord);
        emulateHighlightResultOnLabelAndNotTitle(occupationResult3, matchedWord);


        when(index.search(any())).thenReturn(new SearchResult<Algolia.AlgoliaOccupationDTO>().setHits(List.of(occupationResult1, occupationResult2, occupationResult3)));

        var result = algolia.searchOccupations(matchedWord, true);

        assertThat(result).hasSize(3)
                .anyMatch(dto -> dto.getTitle().equals(wrapMatchWordWithStrongTag(label1, matchedWord)))
                .anyMatch(dto -> dto.getTitle().equals(wrapMatchWordWithStrongTag(label2, matchedWord)))
                .anyMatch(dto -> dto.getTitle().equals(wrapMatchWordWithStrongTag(label3, matchedWord)));
    }

    @Test
    void searchOccupations_addTitleToAlternativeLabelDuplicatingTitle() {
        var duplicatedLabel = "duplicated";
        var title2 = "title2";
        var occupationResult1 = new Algolia.AlgoliaOccupationDTO(new ErhgoOccupationMotherObject().withTitle(duplicatedLabel).withId(UUID.randomUUID()).instance());
        var occupationResult2 = new Algolia.AlgoliaOccupationDTO(new ErhgoOccupationMotherObject().withTitle(title2).withId(UUID.randomUUID()).withAlternativeLabel(duplicatedLabel).instance());
        emulateHighlightResultOnTitle(occupationResult1, duplicatedLabel);
        emulateHighlightResultOnLabelAndNotTitle(occupationResult2, duplicatedLabel);
        when(index.search(any())).thenReturn(new SearchResult<Algolia.AlgoliaOccupationDTO>().setHits(List.of(occupationResult1, occupationResult2)));

        var result = algolia.searchOccupations(duplicatedLabel, true);

        assertThat(result).hasSize(2)
                .anyMatch(dto -> dto.getTitle().equals(wrapMatchWordWithStrongTag(duplicatedLabel, duplicatedLabel)))
                .anyMatch(dto -> dto.getTitle().equals("%s (%s)".formatted(wrapMatchWordWithStrongTag(duplicatedLabel, duplicatedLabel), title2)));
    }

    @Test
    void searchOccupations_get_the_most_similar_label_to_the_query_input() throws JsonProcessingException {
        var query = "Magasinier";
        var hitsFromJsonFile = getSearchResultFromJsonFile("algoliaSearchResultForSimilarityTest").getHits();
        when(index.search(any())).thenReturn(new SearchResult<Algolia.AlgoliaOccupationDTO>().setHits(hitsFromJsonFile));

        var result = algolia.searchOccupations(query, false);

        assertThat(result).hasSize(hitsFromJsonFile.size())
                .anyMatch(dto -> dto.getTitle().equals(wrapMatchWordWithEmphasizedTag(query, query)))
        ;

    }

    private SearchResult<Algolia.AlgoliaOccupationDTO> getSearchResultFromJsonFile(String filename) throws JsonProcessingException {
        var type = mapper.getTypeFactory().constructParametricType(SearchResult.class, Algolia.AlgoliaOccupationDTO.class);
        return mapper.readValue(TestUtils.getFileContentAsString("%s.json".formatted(filename)), type);
    }

    private static Algolia.AlgoliaHighlightResultItem getMatchingHighlightResult(String matchedWord, Algolia.AlgoliaOccupationDTO occupation, boolean isAlternativeLabel) {
        return (Algolia.AlgoliaHighlightResultItem) new Algolia.AlgoliaHighlightResultItem()
                .setMatchedWords(List.of(matchedWord))
                .setValue(wrapMatchWordWithStrongTag((!isAlternativeLabel ? occupation.getTitle() : occupation.getAlternativeLabels().stream().findFirst().orElseThrow()), matchedWord))
                .setMatchLevel(Algolia.AbstractAlgoliaResultItem.MatchLevel.FULL);
    }

    private static String wrapMatchWordWithStrongTag(String s, String matchedWord) {
        return s.replaceAll(matchedWord, "<strong>%s</strong>".formatted(matchedWord));
    }

    private static String wrapMatchWordWithEmphasizedTag(String s, String matchedWord) {
        return s.replaceAll(matchedWord, "<em>%s</em>".formatted(matchedWord));
    }

    private static void emulateHighlightResultOnTitle(Algolia.AlgoliaOccupationDTO occupation, String matchedWord) {
        occupation.setHighlightResult(
                new Algolia.AlgoliaOccupationDTO.OccupationHighlightResultWrapper()
                        .setTitle(getMatchingHighlightResult(matchedWord, occupation, false))
                        .setAlternativeLabels(Collections.emptyList())

        );
    }

    private static void emulateHighlightResultOnLabelAndNotTitle(Algolia.AlgoliaOccupationDTO occupation, String matchedWord) {
        occupation.setHighlightResult(
                new Algolia.AlgoliaOccupationDTO.OccupationHighlightResultWrapper()
                        .setTitle((Algolia.AlgoliaHighlightResultItem) new Algolia.AlgoliaHighlightResultItem().setMatchedWords(Collections.emptyList()).setValue(occupation.getTitle()).setMatchLevel(Algolia.AbstractAlgoliaResultItem.MatchLevel.NONE))
                        .setAlternativeLabels(List.of(getMatchingHighlightResult(matchedWord, occupation, true)))

        );
    }
}
