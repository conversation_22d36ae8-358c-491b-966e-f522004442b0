package com.erhgo.services.search.recruitment;

import com.algolia.search.DefaultSearchClient;
import com.algolia.search.SearchClient;
import com.algolia.search.SearchIndex;
import com.erhgo.domain.enums.DiffusionType;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.generators.RecruiterMotherObject;
import com.erhgo.repositories.RecruitmentRepository;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AlgoliaRecruitmentIndexerNoneTypeTest {

    @Mock
    private RecruitmentRepository recruitmentRepository;

    @Mock
    private SearchIndex<AlgoliaRecruitmentIndexer.AlgoliaRecruitmentDTO> offerIndex;

    @Mock
    private SearchClient searchClient;

    private MockedStatic<DefaultSearchClient> mockedClient;

    private AlgoliaRecruitmentIndexer indexer;

    @BeforeEach
    @SneakyThrows
    void setUp() {
        mockedClient = Mockito.mockStatic(DefaultSearchClient.class);
        mockedClient.when(() -> DefaultSearchClient.create(anyString(), anyString())).thenReturn(searchClient);

        when(searchClient.initIndex(anyString(), any(Class.class))).thenReturn(offerIndex);

        indexer = new AlgoliaRecruitmentIndexer("appId", "apiKey", "prefix", recruitmentRepository);
    }

    @AfterEach
    void tearDown() {
        mockedClient.close();
    }

    @Test
    void should_index_recruitment_regardless_of_diffusion_type() {
        var recruiter = new RecruiterMotherObject()
                .withDefaultDiffusionType(DiffusionType.NONE)
                .build();

        var recruitment = new RecruitmentMotherObject()
                .withRecruiter(recruiter)
                .build();

        when(recruitmentRepository.findById(any())).thenReturn(Optional.of(recruitment));

        indexer.index(recruitment);

        verify(offerIndex, times(1)).saveObject(any());
        verify(offerIndex, never()).deleteObject(any());
    }

    @Test
    void should_index_recruitment_with_other_diffusion_type() {
        var recruiter = new RecruiterMotherObject()
                .withDefaultDiffusionType(DiffusionType.BOTH)
                .build();

        var recruitment = new RecruitmentMotherObject()
                .withRecruiter(recruiter)
                .build();

        when(recruitmentRepository.findById(any())).thenReturn(Optional.of(recruitment));

        indexer.index(recruitment);

        verify(offerIndex, times(1)).saveObject(any());
        verify(offerIndex, never()).deleteObject(any());
    }

    @Test
    void should_filter_out_recruitments_with_none_diffusion_type_when_indexing_all() {
        var recruiterNone = new RecruiterMotherObject()
                .withDefaultDiffusionType(DiffusionType.NONE)
                .build();

        var recruiterBoth = new RecruiterMotherObject()
                .withDefaultDiffusionType(DiffusionType.BOTH)
                .build();

        var recruitmentNone = new RecruitmentMotherObject()
                .withRecruiter(recruiterNone)
                .build();

        var recruitmentBoth = new RecruitmentMotherObject()
                .withRecruiter(recruiterBoth)
                .build();

        when(recruitmentRepository.findRecruitmentsToIndex()).thenReturn(List.of(recruitmentNone, recruitmentBoth));

        indexer.indexAll();

        verify(offerIndex, times(1)).replaceAllObjects(Mockito.argThat(list -> Lists.newArrayList(list).size() == 1), Mockito.eq(true));
    }

    @Test
    void should_delete_all_recruitments_when_recruiter_has_none_diffusion_type() {
        var recruiter = new RecruiterMotherObject()
                .withDefaultDiffusionType(DiffusionType.NONE)
                .withCode("R-TEST")
                .build();

        var recruitment1 = new RecruitmentMotherObject()
                .withRecruiter(recruiter)
                .build().setCode("REC-1");

        var recruitment2 = new RecruitmentMotherObject()
                .withRecruiter(recruiter)
                .build().setCode("REC-2");

        when(recruitmentRepository.findRecruitmentToIndexByRecruiter(recruiter)).thenReturn(List.of(recruitment1, recruitment2));

        indexer.indexRecruitmentsForRecruiter(recruiter);

        verify(offerIndex, times(1)).deleteObjects(List.of(recruitment1.getCode(), recruitment2.getCode()));
        verify(offerIndex, never()).saveObjects(any());
    }

    @Test
    void should_index_all_recruitments_when_recruiter_has_other_diffusion_type() {
        var recruiter = new RecruiterMotherObject()
                .withDefaultDiffusionType(DiffusionType.BOTH)
                .withCode("R-TEST")
                .build();

        var recruitment1 = new RecruitmentMotherObject()
                .withRecruiter(recruiter)
                .build()
                .setCode("REC-1");

        var recruitment2 = new RecruitmentMotherObject()
                .withRecruiter(recruiter)
                .build()
                .setCode("REC-2");

        when(recruitmentRepository.findRecruitmentToIndexByRecruiter(recruiter)).thenReturn(List.of(recruitment1, recruitment2));

        indexer.indexRecruitmentsForRecruiter(recruiter);

        verify(offerIndex, never()).deleteObjects(any());
        verify(offerIndex, times(1)).saveObjects(Mockito.argThat(list -> Lists.newArrayList(list).size() == 2));
    }
}
