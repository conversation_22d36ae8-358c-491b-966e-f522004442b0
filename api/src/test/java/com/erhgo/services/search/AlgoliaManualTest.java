package com.erhgo.services.search;

import com.algolia.search.models.indexing.AroundRadius;
import com.algolia.search.models.indexing.Query;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

@Disabled
class AlgoliaManualTest {

    Algolia algolia;

    @Test
    void test() {
        var algoliaSearchConfigService = new AlgoliaSearchConfigService();
        ReflectionTestUtils.setField(algoliaSearchConfigService, "applicationId", "G6Z1VNAQ6M");
        ReflectionTestUtils.setField(algoliaSearchConfigService, "searchApiKey", "XXX");
        ReflectionTestUtils.setField(algoliaSearchConfigService, "adminApiKey", "XXX");
        ReflectionTestUtils.setField(algoliaSearchConfigService, "indexPrefix", "eric");
        algoliaSearchConfigService.initialize();
        var index = algoliaSearchConfigService.getSearchClient().initIndex(algoliaSearchConfigService.getUserIndexName(), AlgoliaUserIndexer.UserDTO.class);
        var a = index.search(
                new Query("a")
                        .setFilters("criteria:\"REP-1-1\" AND (erhgoOccupations:\"Un métier disposant d'une description à rechercher\") AND (channels:\"T-0003\") AND (masteryLevel:\"4\") AND (mainBehaviors:\"Persévérance\") AND (city:\"Lyon\") AND (mainCapacityFamilies:\"DECISION\")")
                        .setAroundLatLng("45.758,4.835")
                        .setAroundRadius(AroundRadius.of(97000)));
    }
}
