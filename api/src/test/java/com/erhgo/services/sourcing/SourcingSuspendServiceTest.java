package com.erhgo.services.sourcing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.AuditableFieldHelper;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.externaloffer.ExternalOfferMotherObject;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.sourcing.SourcingSubscriptionMotherObject;
import com.erhgo.generators.RecruiterMotherObject;
import com.erhgo.repositories.RecruitmentRepository;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailingListService;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.notifier.messages.SourcingDisabledAccountMessageDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.time.OffsetDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static java.time.temporal.ChronoUnit.MINUTES;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.within;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class SourcingSuspendServiceTest extends AbstractIntegrationTest {

    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    AuditableFieldHelper auditableFieldHelper;

    @Autowired
    SourcingJobRecruitmentService sourcingService;

    @Autowired
    RecruitmentRepository recruitmentRepository;

    @MockitoBean
    Notifier notifier;

    @MockitoBean
    MailingListService mockMailingListService;


    @Test
    @ResetDataAfter
    void suspendRecruitmentsOnDisabledAccount() {
        var disabledRecruiter = createExpiredOrga();
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class).withState(RecruitmentState.PUBLISHED).withRecruiter(disabledRecruiter).buildAndPersist();
        sourcingService.suspendSourcingRecruitmentsOnDisabledAccount();
        var updatedRecruitment = recruitmentRepository.findById(recruitment.getId()).orElseThrow();
        assertThat(updatedRecruitment.getPublicationEndDate()).isCloseToUtcNow(within(1, MINUTES));
        assertThat(updatedRecruitment.getState()).isEqualTo(RecruitmentState.SELECTION);
        verify(notifier).sendMessage(any(SourcingDisabledAccountMessageDTO.class));
    }

    @ResetDataAfter
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void suspendRecruitmentsAfterPublicationEndDate(boolean isExternal) {
        var userId = "4224";
        var email = "test@mail";
        var recruitment = txHelper.doInTransaction(() -> {
            var innerRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                    .withState(RecruitmentState.PUBLISHED)
                    .withPublicationEndDate(OffsetDateTime.now().minusDays(2))
                    .withSourcingNotifiedUsersIds(userId)
                    .buildAndPersist();
            if (isExternal) {
                innerRecruitment.setExternalOffer(applicationContext.getBean(ExternalOfferMotherObject.class).withRecruitment(innerRecruitment).buildAndPersist());
            }
            return innerRecruitment;
        });

        var noToSuspendRecruitment = applicationContext.getBean(RecruitmentMotherObject.class).withRecruiter(recruitment.getRecruiter()).withState(RecruitmentState.PUBLISHED).withPublicationEndDate(OffsetDateTime.now().plusDays(2)).buildAndPersist();

        when(sourcingKeycloakService.getSourcingUser(userId)).thenReturn(Optional.of(new UserRepresentation().setEmail(email)));

        sourcingService.suspendEndedSourcingRecruitments();
        var expectedParameters = Map.of("poste", recruitment.getJobTitle());
        if (isExternal) {
            verifyNoInteractions(mockMailingListService);
        } else {
            verify(mockMailingListService).sendMailsForTemplate(eq(Set.of(email)), anyLong(), eq(expectedParameters), isNull());
        }

        assertThat(recruitmentRepository.findById(recruitment.getId()).orElseThrow().getState()).isEqualTo(RecruitmentState.SELECTION);
        assertThat(recruitmentRepository.findById(noToSuspendRecruitment.getId()).orElseThrow().getState()).isEqualTo(RecruitmentState.PUBLISHED);
        verifyNoInteractions(notifier);
    }

    @ResetDataAfter
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void doNotSuspendAnythingWhenNoRecruitment(boolean withOrga) {
        if (withOrga) {
            createExpiredOrga();
        }
        //recruitmentToIgnore
        applicationContext.getBean(RecruitmentMotherObject.class).withState(RecruitmentState.PUBLISHED).withPublicationEndDate(OffsetDateTime.now().minusDays(3)).buildAndPersist();
        sourcingService.suspendSourcingRecruitmentsOnDisabledAccount();
        verifyNoInteractions(notifier);
        assertThat(recruitmentRepository.findAll()).noneMatch(r -> r.getState() == RecruitmentState.SELECTION);
    }

    private Recruiter createExpiredOrga() {
        var orga = applicationContext.getBean(RecruiterMotherObject.class).withOrganizationType(AbstractOrganization.OrganizationType.SOURCING).buildAndPersist();
        applicationContext.getBean(SourcingSubscriptionMotherObject.class).withRecruiter(orga).withExpirationDate(OffsetDateTime.now().minusDays(5)).buildAndPersist();
        return orga;
    }
}
