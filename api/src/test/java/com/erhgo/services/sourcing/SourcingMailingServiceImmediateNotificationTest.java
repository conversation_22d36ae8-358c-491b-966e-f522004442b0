package com.erhgo.services.sourcing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.enums.CandidatureSynchronizationState;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.job.SpontaneousCandidatureMotherObject;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.sourcing.SourcingPreferences;
import com.erhgo.domain.userprofile.SourcingPreferencesMotherObject;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.RecruitmentRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailingListService;
import com.erhgo.services.userprofile.FilePartProvider;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class SourcingMailingServiceImmediateNotificationTest extends AbstractIntegrationTest {

    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    SourcingMailingService sourcingMailingService;

    @MockitoBean
    MailingListService mailingListService;
    @MockitoBean
    UserProfileCompetencesExportService userProfileCompetencesExportService;

    private List<SourcingPreferences> allPreferences;
    private UserRepresentation user1;
    private UserRepresentation user2;
    private UserRepresentation user3;
    private UserRepresentation user4;
    private UserRepresentation user5;

    @BeforeEach
    void setUp() {
        // With prefs, on spontaneous and notified on recruitment:
        user1 = new UserRepresentation().setId("user1").setEmail("<EMAIL>");
        // With prefs, NOT on spontaneous and notified on recruitment:
        user2 = new UserRepresentation().setId("user2").setEmail("<EMAIL>");
        // With prefs, on spontaneous AND NOT notified on recruitment:
        user3 = new UserRepresentation().setId("user3").setEmail("<EMAIL>");
        // No prefs, notified on recruitment
        user4 = new UserRepresentation().setId("user4").setEmail("<EMAIL>");
        // No prefs, not notified on recruitment
        user5 = new UserRepresentation().setId("user5").setEmail("<EMAIL>");

        allPreferences = Stream.of(
                "user1",
                "user2",
                "user3"
        ).map(
                id -> applicationContext.getBean(SourcingPreferencesMotherObject.class)
                        .withUserId(id)
                        .withMailFrequency(SourcingPreferences.MailFrequency.IMMEDIATELY)
                        .withNotifiedOnSpontaneousCandidatures(id.equals("user1"))
                        .buildAndPersist()
        ).toList();

        when(sourcingKeycloakService.getEnabledSourcingUsersForGroup(anyString()))
                .thenReturn(List.of(user1, user2, user3, user4, user5));

        when(mailingListService.sendMailsForTemplate(anySet(), anyLong(), anyMap(), any()))
                .thenReturn(CompletableFuture.completedFuture(Set.of("<EMAIL>")));
    }

    @SneakyThrows
    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void sendImmediateNotificationToSourcingUsers_shouldNotifyForRecruitmentCandidature() {
        var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withState(GlobalCandidatureState.NOT_TREATED_BY_ERHGO)
                .withOrganizationOfType(AbstractOrganization.OrganizationType.SOURCING)
                .withCandidatureSynchronizationState(CandidatureSynchronizationState.WAITING)
                .withSubmissionDate(OffsetDateTime.now().minusHours(2))
                .withAtLeastOneXp()
                .buildAndPersist();

        txHelper.doInTransaction(() -> {
            candidature.getRecruitment().setSourcingUsersIdToNotify(Set.of("user1", "user2", "user4"));
            applicationContext.getBean(RecruitmentRepository.class).save(candidature.getRecruitment());
        });
        var providerMock = Mockito.mock(FilePartProvider.class, RETURNS_MOCKS);
        Mockito.when(userProfileCompetencesExportService.getProfileCompetenceForBatch(Mockito.eq(candidature.getId()), anyString(), eq(ProfileCompetencesViewObject.AnonymousMode.BOTH)))
                .thenReturn(providerMock);
        var fetchedCandidature = applicationContext.getBean(AbstractCandidatureRepository.class).findCandidaturesToNotifyImmediately(CandidatureSynchronizationState.WAITING, OffsetDateTime.now().minusHours(2), OffsetDateTime.now().minusDays(15)).getFirst();
        sourcingMailingService.sendImmediateNotificationToSourcingUsers(fetchedCandidature, allPreferences);

        ArgumentCaptor<Set<String>> emailsCaptor = ArgumentCaptor.forClass(Set.class);
        ArgumentCaptor<Map<String, String>> paramsCaptor = ArgumentCaptor.forClass(Map.class);

        verify(mailingListService).sendMailsForTemplate(emailsCaptor.capture(), anyLong(), paramsCaptor.capture(), eq(providerMock));

        assertThat(emailsCaptor.getValue()).containsExactlyInAnyOrder("<EMAIL>", "<EMAIL>", "<EMAIL>");
        assertThat(paramsCaptor.getValue()).containsKey("TITLE");
        assertThat(paramsCaptor.getValue()).containsKey("LINK");

        // Verify the candidature state was updated
        var updatedCandidature = applicationContext.getBean(RecruitmentCandidatureRepository.class).findById(candidature.getId()).orElseThrow();
        assertThat(updatedCandidature.getSynchronizationState()).isEqualTo(CandidatureSynchronizationState.DONE);
    }

    @SneakyThrows
    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void sendImmediateNotificationToSourcingUsers_shouldNotifyForSpontaneousCandidature() {
        var candidature = applicationContext.getBean(SpontaneousCandidatureMotherObject.class)
                .withState(GlobalCandidatureState.NOT_TREATED_BY_ERHGO)
                .withCandidatureSynchronizationState(CandidatureSynchronizationState.WAITING)
                .withSubmissionDate(OffsetDateTime.now().minusHours(2))
                .withOrganizationOfType(AbstractOrganization.OrganizationType.SOURCING)
                .buildAndPersist();

        var providerMock = Mockito.mock(FilePartProvider.class, RETURNS_MOCKS);
        Mockito.when(userProfileCompetencesExportService.getProfileCompetenceForBatch(eq(candidature.getId()), anyString(), eq(ProfileCompetencesViewObject.AnonymousMode.BOTH)))
                .thenReturn(providerMock);
        sourcingMailingService.sendImmediateNotificationToSourcingUsers(candidature, allPreferences);

        ArgumentCaptor<Set<String>> emailsCaptor = ArgumentCaptor.forClass(Set.class);
        ArgumentCaptor<Map<String, String>> paramsCaptor = ArgumentCaptor.forClass(Map.class);

        verify(mailingListService).sendMailsForTemplate(emailsCaptor.capture(), anyLong(), paramsCaptor.capture(), eq(providerMock));

        // Only user1 should be notified for spontaneous candidatures based on preferences
        assertThat(emailsCaptor.getValue()).containsExactlyInAnyOrder("<EMAIL>", "<EMAIL>", "<EMAIL>");
        assertThat(paramsCaptor.getValue()).containsKey("LINK");

        // Verify the candidature state was updated
        var updatedCandidature = applicationContext.getBean(SpontaneousCandidatureRepository.class).findById(candidature.getId()).orElseThrow();
        assertThat(updatedCandidature.getSynchronizationState()).isEqualTo(CandidatureSynchronizationState.DONE);
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void sendImmediateNotificationToSourcingUsers_shouldIgnoreNonSourcingCandidatures() {
        var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withState(GlobalCandidatureState.REFUSED_ON_CALL) // Not in NOT_TREATED_BY_ERHGO state
                .withSubmissionDate(OffsetDateTime.now().minusHours(2))
                .withCandidatureSynchronizationState(CandidatureSynchronizationState.WAITING)
                .buildAndPersist();

        sourcingMailingService.sendImmediateNotificationToSourcingUsers(candidature, allPreferences);

        verify(mailingListService, never()).sendMailsForTemplate(anySet(), anyLong(), anyMap(), any());

        // Verify the candidature state was updated to IGNORE
        RecruitmentCandidature updatedCandidature = applicationContext.getBean(RecruitmentCandidatureRepository.class).findById(candidature.getId()).orElseThrow();
        assertThat(updatedCandidature.getSynchronizationState()).isEqualTo(CandidatureSynchronizationState.IGNORE);
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void sendImmediateNotificationToSourcingUsers_shouldIgnoreExternalOfferCandidatures() {
        var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withState(GlobalCandidatureState.NOT_TREATED_BY_ERHGO)
                .withCandidatureSynchronizationState(CandidatureSynchronizationState.WAITING)
                .withSubmissionDate(OffsetDateTime.now().minusHours(2))
                .buildAndPersist();

        // Make it an external offer candidature
        doReturn(true).when(spy(candidature)).isHandledByAts();
        sourcingMailingService.sendImmediateNotificationToSourcingUsers(candidature, allPreferences);

        verify(mailingListService, never()).sendMailsForTemplate(anySet(), anyLong(), anyMap(), any());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void sendImmediateNotificationToSourcingUsers_shouldHandleNoUsersToNotify() {
        var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withState(GlobalCandidatureState.NOT_TREATED_BY_ERHGO)
                .withSubmissionDate(OffsetDateTime.now().minusHours(2))
                .withCandidatureSynchronizationState(CandidatureSynchronizationState.WAITING)
                .buildAndPersist();

        // No users to notify
        when(sourcingKeycloakService.getEnabledSourcingUsersForGroup(anyString()))
                .thenReturn(List.of());
        sourcingMailingService.sendImmediateNotificationToSourcingUsers(candidature, allPreferences);

        verify(mailingListService, never()).sendMailsForTemplate(anySet(), anyLong(), anyMap(), any());

        // Verify the candidature state was updated to IGNORE
        var updatedCandidature = applicationContext.getBean(RecruitmentCandidatureRepository.class).findById(candidature.getId()).orElseThrow();
        assertThat(updatedCandidature.getSynchronizationState()).isEqualTo(CandidatureSynchronizationState.IGNORE);
    }
}
