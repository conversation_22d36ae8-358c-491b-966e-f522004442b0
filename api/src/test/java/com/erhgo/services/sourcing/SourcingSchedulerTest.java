package com.erhgo.services.sourcing;


import com.erhgo.AbstractIntegrationTest;
import com.erhgo.domain.enums.RecruitmentSendNotificationState;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.repositories.RecruitmentRepository;
import com.erhgo.repositories.SourcingUserRepository;
import com.erhgo.services.mailing.RecruitmentNotificationGenerator;
import org.assertj.core.api.Assertions;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.RepeatedTest;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class SourcingSchedulerTest extends AbstractIntegrationTest {

    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    SourcingScheduler sourcingScheduler;

    @MockitoBean
    SourcingUserRepository sourcingUserRepository;
    @MockitoBean
    RecruitmentNotificationGenerator sendCandidatureProposalService;


    @Test
    void doNothingWhenNoRecruitment() {
        sourcingScheduler.sendRecruitmentNotifications();
        Mockito.verifyNoInteractions(sendCandidatureProposalService);
    }

    @RepeatedTest(3)
    void considerOnlySourcingRecruitmentPublishedMoreThan48HoursAgo() {
        // Old published recruitment but null SendNotificationState & date => ko
        applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withSendNotificationState(null, null)
                .buildAndPersist();

        // Less than 48 hours => ko
        applicationContext.getBean(RecruitmentMotherObject.class)
                .withPublicationDate(new Date())
                .withState(RecruitmentState.PUBLISHED)
                .withSendNotificationState(RecruitmentSendNotificationState.WAITING, Instant.now().plus(48, ChronoUnit.HOURS))
                .buildAndPersist();

        applicationContext.getBean(RecruitmentMotherObject.class)
                .withPublicationDate(new Date())
                .withState(RecruitmentState.PUBLISHED)
                .withSendNotificationState(RecruitmentSendNotificationState.WAITING, Instant.now().plus(4, ChronoUnit.HOURS))
                .buildAndPersist();

        // 48 hours but not WAITING => ko
        Stream.of(RecruitmentSendNotificationState.values()).filter(s -> s != RecruitmentSendNotificationState.WAITING)
                .forEach(s -> applicationContext.getBean(RecruitmentMotherObject.class)
                        .withPublicationDate(new Date())
                        .withState(RecruitmentState.PUBLISHED)
                        .withSendNotificationState(s, Instant.now().minus(1, ChronoUnit.HOURS))
                        .buildAndPersist());


        var expectedIdOK = generateRecruitment();
        var expectedIdExpectedError = generateRecruitment();
        var expectedIdUnexpectedError = generateRecruitment();

        var set1 = List.of("1");
        var set2 = List.of("2");
        var set3 = List.of("3");
        var executor = Executors.newCachedThreadPool();

        when(sourcingUserRepository.getCandidates(any())).thenReturn(set1).thenReturn(set1).thenReturn(set2).thenReturn(set3);

        Mockito.when(sendCandidatureProposalService.generateNotifications(any(), eq(expectedIdOK)))
                .thenReturn(CompletableFuture.runAsync(() -> Awaitility.await().during(500, TimeUnit.MILLISECONDS), executor));

        Mockito.when(sendCandidatureProposalService.generateNotifications(any(), eq(expectedIdExpectedError))).thenReturn(CompletableFuture.supplyAsync(() -> {
            Awaitility.await().during(200, TimeUnit.MILLISECONDS);
            throw new GenericTechnicalException("ko");
        }, executor));
        Mockito.when(sendCandidatureProposalService.generateNotifications(any(), eq(expectedIdUnexpectedError))).thenReturn(CompletableFuture.supplyAsync(() -> {
            Awaitility.await().during(100, TimeUnit.MILLISECONDS);
            throw new RuntimeException();
        }, executor));

        sourcingScheduler.sendRecruitmentNotifications();
        executor.shutdown();
        verifyRecruitment(expectedIdOK, RecruitmentSendNotificationState.DONE);
        verifyRecruitment(expectedIdExpectedError, RecruitmentSendNotificationState.ERROR);
        verifyRecruitment(expectedIdUnexpectedError, RecruitmentSendNotificationState.ERROR);
        verifyNoMoreInteractions(sendCandidatureProposalService);
        var expectedIds = new HashSet<>(Set.of(expectedIdExpectedError, expectedIdUnexpectedError, expectedIdOK));
        Mockito.verify(sourcingUserRepository, times(3)).getCandidates(ArgumentMatchers.assertArg(c -> {
            Assertions.assertThat(c.excludesNotified()).isTrue();
            Assertions.assertThat(c.topTen()).isFalse();
            expectedIds.remove(c.recruitmentId());
        }));
        Assertions.assertThat(expectedIds).isEmpty();
    }


    private void verifyRecruitment(Long expectedId, RecruitmentSendNotificationState expectedState) {
        verify(sendCandidatureProposalService).generateNotifications(any(), eq(expectedId));
        var recruitment = applicationContext.getBean(RecruitmentRepository.class).findById(expectedId).orElseThrow();
        Assertions.assertThat(recruitment.getSendNotificationDate()).isNull();
        Assertions.assertThat(recruitment.getSendNotificationState()).isEqualTo(expectedState);
    }

    private Long generateRecruitment() {
        return applicationContext.getBean(RecruitmentMotherObject.class)
                .withPublicationDate(new Date())
                .withState(RecruitmentState.PUBLISHED)
                .withSendNotificationState(RecruitmentSendNotificationState.WAITING, Instant.now().minus(1, ChronoUnit.HOURS))
                .buildAndPersist()
                .getId();
    }

    @Test
    void sendRecruitmentReminderNotifications() {

    }
}
