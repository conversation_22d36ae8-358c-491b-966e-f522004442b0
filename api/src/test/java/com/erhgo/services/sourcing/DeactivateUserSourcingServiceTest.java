package com.erhgo.services.sourcing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.sourcing.SourcingPreferences;
import com.erhgo.domain.userprofile.SourcingPreferencesMotherObject;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.repositories.RecruitmentRepository;
import com.erhgo.repositories.SourcingPreferencesRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.keycloak.SourcingKeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;


class DeactivateUserSourcingServiceTest extends AbstractIntegrationTest {

    @Autowired
    private SourcingKeycloakService sourcingKeycloakService;

    @MockitoBean
    private SourcingPreferencesRepository sourcingPreferencesRepository;

    @Autowired
    private DeactivateUserSourcingService deactivateUserSourcingService;

    @Autowired
    private ApplicationContext applicationContext;


    private static final String USER_ID_TO_DEACTIVATE = "6cf3b6ca-8157-4bdf-b186-2e71644c032e";
    private static final String REPLACEMENT_USER_ID = "d01cd104-04df-40a4-9348-5033f78fbb03";
    private static final String OTHER_USER_ID = "other-user";

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void deactivateUserSuccessfully() {
        getUserRepresentation();

        applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID_TO_DEACTIVATE)
                .buildAndPersist();

        deactivateUserSourcingService.deactivateUser(USER_ID_TO_DEACTIVATE, "");
        verify(sourcingKeycloakService).disableUser(USER_ID_TO_DEACTIVATE);
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void activateUserSuccessfully() {
        getUserRepresentation();
        deactivateUserSourcingService.activateUser(USER_ID_TO_DEACTIVATE);
        verify(sourcingKeycloakService).enableUser(USER_ID_TO_DEACTIVATE);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void deactivateUserAndTransferRecruitments() {
        applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withManagerUserId(USER_ID_TO_DEACTIVATE)
                .buildAndPersist();
        applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withManagerUserId(USER_ID_TO_DEACTIVATE)
                .buildAndPersist();

        getUserRepresentation();

        deactivateUserSourcingService.deactivateUser(USER_ID_TO_DEACTIVATE, REPLACEMENT_USER_ID);

        assertThat(applicationContext.getBean(RecruitmentRepository.class).findAll())
                .hasSize(2)
                .extracting(Recruitment::getManagerUserId)
                .containsExactly(REPLACEMENT_USER_ID, REPLACEMENT_USER_ID);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @Transactional
    void deactivateUserAndAddReplacementToNotifications() {
        applicationContext.getBean(RecruitmentMotherObject.class)
                .withSourcingNotifiedUsersIds(USER_ID_TO_DEACTIVATE)
                .buildAndPersist();
        applicationContext.getBean(RecruitmentMotherObject.class)
                .withSourcingNotifiedUsersIds(USER_ID_TO_DEACTIVATE, OTHER_USER_ID)
                .buildAndPersist();

        getUserRepresentation();

        deactivateUserSourcingService.deactivateUser(USER_ID_TO_DEACTIVATE, REPLACEMENT_USER_ID);
        assertThat(applicationContext.getBean(RecruitmentRepository.class).findAll())
                .hasSize(2)
                .extracting(Recruitment::getSourcingUsersIdToNotify)
                .containsExactlyInAnyOrder(Set.of(REPLACEMENT_USER_ID), Set.of(OTHER_USER_ID, REPLACEMENT_USER_ID));
    }


    @Test
    @ResetDataAfter
    @Transactional
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void deactivateUserAndRemoveFromNotifications() {
        applicationContext.getBean(RecruitmentMotherObject.class)
                .withSourcingNotifiedUsersIds(USER_ID_TO_DEACTIVATE, OTHER_USER_ID)
                .buildAndPersist();
        applicationContext.getBean(RecruitmentMotherObject.class)
                .withSourcingNotifiedUsersIds(USER_ID_TO_DEACTIVATE)
                .buildAndPersist();

        getUserRepresentation();

        deactivateUserSourcingService.deactivateUser(USER_ID_TO_DEACTIVATE, REPLACEMENT_USER_ID);

        assertThat(applicationContext.getBean(RecruitmentRepository.class).findAll())
                .hasSize(2)
                .extracting(Recruitment::getSourcingUsersIdToNotify)
                .doesNotContain(Set.of(USER_ID_TO_DEACTIVATE), Set.of(USER_ID_TO_DEACTIVATE));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void updateSourcingPreferencesToNeverNotify() {
        applicationContext.getBean(SourcingPreferencesMotherObject.class)
                .withUserId(USER_ID_TO_DEACTIVATE)
                .withMailFrequency(SourcingPreferences.MailFrequency.DAILY)
                .buildAndPersist();

        getUserRepresentation();

        deactivateUserSourcingService.deactivateUser(USER_ID_TO_DEACTIVATE, REPLACEMENT_USER_ID);
        verify(sourcingPreferencesRepository).save(any(SourcingPreferences.class));

    }


    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void dotNotDeactivateUserIfUserNotFound() {
        when(sourcingKeycloakService.getSourcingUser(USER_ID_TO_DEACTIVATE)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> deactivateUserSourcingService.deactivateUser(USER_ID_TO_DEACTIVATE, REPLACEMENT_USER_ID))
                .isInstanceOf(EntityNotFoundException.class);

        verify(sourcingKeycloakService, never()).disableUser(anyString());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void dotNotActivateUserIfUserNotFound() {
        when(sourcingKeycloakService.getSourcingUser(USER_ID_TO_DEACTIVATE)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> deactivateUserSourcingService.activateUser(USER_ID_TO_DEACTIVATE))
                .isInstanceOf(EntityNotFoundException.class);

        verify(sourcingKeycloakService, never()).enableUser(anyString());
    }

    private void getUserRepresentation() {
        var userRepresentation = new UserRepresentation();
        userRepresentation.setId(USER_ID_TO_DEACTIVATE);
        when(sourcingKeycloakService.getSourcingUser(USER_ID_TO_DEACTIVATE)).thenReturn(Optional.of(userRepresentation));
    }
}
