package com.erhgo.services.sourcing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.AbstractCandidature;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.candidature.job.SpontaneousCandidature;
import com.erhgo.domain.enums.CandidatureSynchronizationState;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.job.SpontaneousCandidatureMotherObject;
import com.erhgo.domain.sourcing.SourcingPreferences;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.SourcingPreferencesRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.time.OffsetDateTime;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class SourcingSchedulerImmediateNotificationTest extends AbstractIntegrationTest {

    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    SourcingScheduler sourcingScheduler;

    @Autowired
    AbstractCandidatureRepository abstractCandidatureRepository;

    @MockitoBean
    SourcingMailingService sourcingMailingService;

    @MockitoBean
    SourcingPreferencesRepository sourcingPreferencesRepository;

    private List<SourcingPreferences> allPreferences;

    @BeforeEach
    void setUp() {
        allPreferences = List.of(
                SourcingPreferences.fromDefault("user1").notifyOnSpontaneousCandidature(true),
                SourcingPreferences.fromDefault("user2").notifyOnSpontaneousCandidature(false)
        );
        when(sourcingPreferencesRepository.findAll()).thenReturn(allPreferences);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void immediateNotificationScheduler_shouldNotifyRecruitmentCandidatures() {
        var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withAtLeastOneXp()
                .withState(GlobalCandidatureState.NOT_TREATED_BY_ERHGO)
                .withSubmissionDate(OffsetDateTime.now().minusHours(2))
                .buildAndPersist();

        candidature.setSynchronizationState(CandidatureSynchronizationState.WAITING);
        abstractCandidatureRepository.save(candidature);
        sourcingScheduler.immediateNotificationScheduler();

        verify(sourcingMailingService).sendImmediateNotificationToSourcingUsers(eq(candidature), eq(allPreferences));
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void immediateNotificationScheduler_shouldNotifySpontaneousCandidatures() {
        SpontaneousCandidature candidature = applicationContext.getBean(SpontaneousCandidatureMotherObject.class)
                .withState(GlobalCandidatureState.NOT_TREATED_BY_ERHGO)
                .withAtLeastOneXp()
                .withSubmissionDate(OffsetDateTime.now().minusHours(2))
                .buildAndPersist();

        candidature.setSynchronizationState(CandidatureSynchronizationState.WAITING);
        abstractCandidatureRepository.save(candidature);
        sourcingScheduler.immediateNotificationScheduler();

        verify(sourcingMailingService).sendImmediateNotificationToSourcingUsers(eq(candidature), eq(allPreferences));
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void immediateNotificationScheduler_shouldNotNotifyOldCandidatures() {
        RecruitmentCandidature candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withAtLeastOneXp()
                .withState(GlobalCandidatureState.NOT_TREATED_BY_ERHGO)
                .withSubmissionDate(OffsetDateTime.now().minusDays(20)) // Older than 15 days
                .buildAndPersist();

        candidature.setSynchronizationState(CandidatureSynchronizationState.WAITING);
        abstractCandidatureRepository.save(candidature);
        sourcingScheduler.immediateNotificationScheduler();

        verify(sourcingMailingService, never()).sendImmediateNotificationToSourcingUsers(eq(candidature), any());
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void immediateNotificationScheduler_shouldNotNotifyNonWaitingCandidatures() {
        RecruitmentCandidature candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withAtLeastOneXp()
                .withState(GlobalCandidatureState.NOT_TREATED_BY_ERHGO)
                .withSubmissionDate(OffsetDateTime.now().minusHours(2))
                .buildAndPersist();

        candidature.setSynchronizationState(CandidatureSynchronizationState.DONE); // Not in WAITING state
        abstractCandidatureRepository.save(candidature);
        sourcingScheduler.immediateNotificationScheduler();

        verify(sourcingMailingService, never()).sendImmediateNotificationToSourcingUsers(eq(candidature), any());
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void immediateNotificationScheduler_shouldNotNotifyRefusedCandidatures() {
        RecruitmentCandidature candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withAtLeastOneXp()
                .withState(GlobalCandidatureState.REFUSED_ON_CALL) // Refused state
                .withSubmissionDate(OffsetDateTime.now().minusHours(2))
                .buildAndPersist();

        candidature.setSynchronizationState(CandidatureSynchronizationState.WAITING);
        abstractCandidatureRepository.save(candidature);
        sourcingScheduler.immediateNotificationScheduler();

        ArgumentCaptor<AbstractCandidature> candidatureCaptor = ArgumentCaptor.forClass(AbstractCandidature.class);
        verify(sourcingMailingService).sendImmediateNotificationToSourcingUsers(candidatureCaptor.capture(), eq(allPreferences));

        // The candidature should be marked as IGNORE in sendImmediateNotificationToSourcingUsers
        // because it doesn't meet the requiresSourcingSync condition (not in NOT_TREATED_BY_ERHGO state)
        assertThat(candidatureCaptor.getValue().getId()).isEqualTo(candidature.getId());
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void immediateNotificationScheduler_shouldProcessMultipleCandidatures() {
        RecruitmentCandidature candidature1 = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withAtLeastOneXp()
                .withState(GlobalCandidatureState.NOT_TREATED_BY_ERHGO)
                .withSubmissionDate(OffsetDateTime.now().minusHours(2))
                .buildAndPersist();

        SpontaneousCandidature candidature2 = applicationContext.getBean(SpontaneousCandidatureMotherObject.class)
                .withState(GlobalCandidatureState.NOT_TREATED_BY_ERHGO)
                .withAtLeastOneXp()
                .withSubmissionDate(OffsetDateTime.now().minusHours(3))
                .buildAndPersist();

        candidature1.setSynchronizationState(CandidatureSynchronizationState.WAITING);
        candidature2.setSynchronizationState(CandidatureSynchronizationState.WAITING);
        abstractCandidatureRepository.save(candidature1);
        abstractCandidatureRepository.save(candidature2);
        sourcingScheduler.immediateNotificationScheduler();

        verify(sourcingMailingService).sendImmediateNotificationToSourcingUsers(eq(candidature1), eq(allPreferences));
        verify(sourcingMailingService).sendImmediateNotificationToSourcingUsers(eq(candidature2), eq(allPreferences));
    }
}
