package com.erhgo.services.userprofile;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.HandicapAccountService;
import com.erhgo.services.keycloak.UserRepresentation;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

class BulkCVProcessingServiceTest extends AbstractIntegrationTest {

    @Autowired
    BulkCVProcessingService bulkCVProcessingService;

    @MockitoBean
    HandicapAccountService handicapAccountService;

    @MockitoBean
    KeycloakMockService keycloakService;

    @Autowired
    ApplicationContext applicationContext;

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @SneakyThrows
    void createOrUpdateProfilesForCVs_shouldProcessValidCsv() {
        var csvContent = "email,url,comment\<EMAIL>,https://example.com/cv.pdf";
        var csvFile = new MockMultipartFile("csvFile", "test.csv", "text/csv", csvContent.getBytes());

        when(handicapAccountService.createOrUpdateUserForFileURL(anyString(), ArgumentMatchers.any(), anyBoolean()))
                .thenReturn("Traité avec succès - fichier trouvé et traité");

        var result = bulkCVProcessingService.createOrUpdateProfilesForCVs(csvFile, false);

        assertThat(result.getTotalRows()).isEqualTo(1);
        assertThat(result.getValidRows()).isEqualTo(1);
        assertThat(result.getInvalidRows()).isEqualTo(0);
        assertThat(result.getEmails()).containsExactly("<EMAIL>");
        assertThat(result.getMessage()).contains("CSV validé: 1 lignes au total, 1 en cours de traitement, 0 ignorées.");
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @SneakyThrows
    void createOrUpdateProfilesForCVs_shouldIncludeUserWithExperience() {
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withTitle("Test Occupation")
                .buildAndPersist();
        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withEmail("<EMAIL>")
                .withExperience(occupation, 3)
                .buildAndPersist();

        var csvContent = "email,url,comment\<EMAIL>,https://example.com/cv.pdf";
        var csvFile = new MockMultipartFile("csvFile", "test.csv", "text/csv", csvContent.getBytes());

        var kcUser = new UserRepresentation().setId(userProfile.userId()).setEmail("<EMAIL>");
        when(keycloakService.getFOUserRepresentationByEmail("<EMAIL>")).thenReturn(kcUser);

        var result = bulkCVProcessingService.createOrUpdateProfilesForCVs(csvFile, true);

        assertThat(result.getTotalRows()).isEqualTo(1);
        assertThat(result.getValidRows()).isEqualTo(1);
        assertThat(result.getInvalidRows()).isEqualTo(0);
        assertThat(result.getEmails()).containsExactly("<EMAIL>");
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @SneakyThrows
    void createOrUpdateProfilesForCVs_shouldExcludeUserWithExperiences() {
        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withEmail("<EMAIL>")
                .withExperience(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist(), 3)
                .withExperience(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist(), 9)
                .buildAndPersist();

        var csvContent = "email,url,comment\<EMAIL>,https://example.com/cv.pdf";
        var csvFile = new MockMultipartFile("csvFile", "test.csv", "text/csv", csvContent.getBytes());

        var kcUser = new UserRepresentation().setId(userProfile.userId()).setEmail("<EMAIL>");
        when(keycloakService.getFOUserRepresentationByEmail("<EMAIL>")).thenReturn(kcUser);

        var result = bulkCVProcessingService.createOrUpdateProfilesForCVs(csvFile, true);

        assertThat(result.getTotalRows()).isEqualTo(1);
        assertThat(result.getValidRows()).isEqualTo(0);
        assertThat(result.getInvalidRows()).isEqualTo(1);
        assertThat(result.getEmails()).isEmpty();
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @SneakyThrows
    void createOrUpdateProfilesForCVs_shouldExcludeNonExistentUser() {
        var csvContent = "email,url,comment\<EMAIL>,https://example.com/cv.pdf";
        var csvFile = new MockMultipartFile("csvFile", "test.csv", "text/csv", csvContent.getBytes());

        when(keycloakService.getFOUserRepresentationByEmail("<EMAIL>")).thenReturn(null);

        var result = bulkCVProcessingService.createOrUpdateProfilesForCVs(csvFile, true);

        assertThat(result.getTotalRows()).isEqualTo(1);
        assertThat(result.getValidRows()).isEqualTo(0); // Exclu car n'existe pas
        assertThat(result.getInvalidRows()).isEqualTo(1);
        assertThat(result.getEmails()).isEmpty();
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @SneakyThrows
    void createOrUpdateProfilesForCVs_shouldHandleInvalidCsvData() {
        var csvContent = "email,url,comment\n,https://example.com/cv.pdf"; // Missing email
        var csvFile = new MockMultipartFile("csvFile", "test.csv", "text/csv", csvContent.getBytes());

        var result = bulkCVProcessingService.createOrUpdateProfilesForCVs(csvFile, false);

        assertThat(result.getTotalRows()).isEqualTo(1);
        assertThat(result.getValidRows()).isEqualTo(0); // Ligne invalide
        assertThat(result.getInvalidRows()).isEqualTo(1);
        assertThat(result.getEmails()).isEmpty();
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @SneakyThrows
    void createOrUpdateProfilesForCVs_shouldProcessMultipleRows() {
        var csvContent = """
                email,url,comment
                <EMAIL>,https://example.com/cv1.pdf
                <EMAIL>,https://example.com/cv2.pdf
                """;
        var csvFile = new MockMultipartFile("csvFile", "test.csv", "text/csv", csvContent.getBytes());

        var result = bulkCVProcessingService.createOrUpdateProfilesForCVs(csvFile, false);

        assertThat(result.getTotalRows()).isEqualTo(2);
        assertThat(result.getValidRows()).isEqualTo(2);
        assertThat(result.getInvalidRows()).isEqualTo(0);
        assertThat(result.getEmails()).containsExactly("<EMAIL>", "<EMAIL>");
    }
}
