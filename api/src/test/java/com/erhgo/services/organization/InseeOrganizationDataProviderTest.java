package com.erhgo.services.organization;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.domain.exceptions.InvalidSiretException;
import com.erhgo.domain.exceptions.SiretValidationTechnicalException;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

@Disabled("helper for testing insee API")
@TestPropertySource(properties = {"insee.apiKey=<replace_with_api_key>"})
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
class InseeOrganizationDataProviderTest extends AbstractIntegrationTest {

    @Autowired
    private InseeOrganizationDataProvider organizationDataProvider;

    @Test
    void get_orga_name_by_siret() throws InvalidSiretException, SiretValidationTechnicalException {
        organizationDataProvider.getNameForSiret("85269574100011");
    }

}
