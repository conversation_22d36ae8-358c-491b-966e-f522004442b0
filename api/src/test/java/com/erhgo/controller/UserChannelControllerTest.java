package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TestUtils;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.landingpage.LandingPage;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.userprofile.UserChannel;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.UserRegistrationState;
import com.erhgo.generators.*;
import com.erhgo.openapi.dto.InitializeProfileCommandDTO;
import com.erhgo.openapi.dto.UpdateUsersChannelsCommandDTO;
import com.erhgo.openapi.dto.UserChannelSourceDTO;
import com.erhgo.repositories.LandingPageRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.AbstractService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.notifier.Notifier;
import org.assertj.core.data.TemporalUnitWithinOffset;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mockito;
import org.mockito.internal.verification.VerificationModeFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.core.Is.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class UserChannelControllerTest extends AbstractIntegrationTest {

    private Recruiter organization;
    private static final String NEW_USER_ID = "3c2a6fbf-e563-4b0b-9ae0-af62cfa12f8d";
    private static final String NEW_USER_ID_2 = "3c2a6fbf-e563-4b0b-9ae0-af62cfa12fdd";

    @Autowired
    private OrganizationGenerator organizationGenerator;

    @Autowired
    private CandidatureGenerator candidatureGenerator;

    @Autowired
    private RecruitmentGenerator recruitmentGenerator;

    @Autowired
    private CapacityGenerator capacityGenerator;

    @Autowired
    private LandingPageRepository landingPageRepository;

    @MockitoBean
    private KeycloakMockService keycloakService;

    @MockitoBean
    private Notifier notifier;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Autowired
    private UserExperienceGenerator userExperienceGenerator;

    private static final String ORGA_CODE = "T-48", PROJECT_CODE = "P-89";

    private void setKeycloakContext() {
        var userRepresentation = createUserRepresentation();

        Mockito.when(keycloakService.getFrontOfficeUserProfileWithGroups(NEW_USER_ID)).thenReturn(Optional.of(userRepresentation));
        Mockito.when(keycloakService.getFrontOfficeUserProfile(NEW_USER_ID)).thenReturn(Optional.of(userRepresentation));
    }

    @NotNull
    private UserRepresentation createUserRepresentation() {
        final var email = "<EMAIL>";
        final var firstName = "tester";
        final var lastName = "Sogilis";

        var userRepresentation = new UserRepresentation();
        userRepresentation.setEmail(email);
        userRepresentation.setFirstName(firstName);
        userRepresentation.setLastName(lastName);
        userRepresentation.setId(NEW_USER_ID);
        return userRepresentation;
    }

    @BeforeEach
    @Transactional
    public void initialize() {
        organization = organizationGenerator.createRecruiter();
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    void assign_group_to_user_should_succeed() throws Exception {
        setKeycloakContext();

        performPut("/user/initialize-profile", createLandingPageAndInitializeProfileCommand(true)).andExpect(status().isOk());

        verify(keycloakService).assignToFrontOfficeGroups(NEW_USER_ID, Set.of(organization.getCode()));
        txHelper.doInTransaction(() -> assertThat(userProfileRepository.findByUserId(NEW_USER_ID).orElseThrow().channels())
                .contains(organization.getCode()));
        verify(notifier, VerificationModeFactory.times(2)).sendMessage(any());
        reset(notifier);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = NEW_USER_ID_2, roles = Role.CANDIDATE)
    void create_user_assign_to_default_channel() throws Exception {
        var userRepresentation = createUserRepresentation();
        var group = "T-999";
        userRepresentation.setId(NEW_USER_ID_2);
        userRepresentation.setGroups(List.of(Role.CANDIDATE, group));
        Mockito.when(keycloakService.getFrontOfficeUserProfileWithGroups(NEW_USER_ID_2)).thenReturn(Optional.of(userRepresentation));
        Mockito.when(keycloakService.getFrontOfficeUserProfile(NEW_USER_ID_2)).thenReturn(Optional.of(userRepresentation));

        performPut("/user/initialize-profile", new InitializeProfileCommandDTO().channelSource(UserChannelSourceDTO.NOTHING)).andExpect(status().isOk());

        txHelper.doInTransaction(() -> assertThat(userProfileRepository.findByUserId(NEW_USER_ID_2).orElseThrow().channels())
                .containsExactly(group));
        reset(notifier);
    }

    @ParameterizedTest
    @ValueSource(booleans = {false, true})
    @ResetDataAfter
    @WithMockKeycloakUser(id = NEW_USER_ID_2, roles = Role.CANDIDATE)
    void create_user_assign_user_kc_phone_then_reset_it(boolean withPhone) throws Exception {
        var userRepresentation = createUserRepresentation();
        userRepresentation.setId(NEW_USER_ID_2);
        if (withPhone) userRepresentation.setMiscAttributes(Map.of("phone", List.of("04-28.20+18+98")));
        Mockito.when(keycloakService.getFrontOfficeUserProfileWithGroups(NEW_USER_ID_2)).thenReturn(Optional.of(userRepresentation));
        Mockito.when(keycloakService.getFrontOfficeUserProfile(NEW_USER_ID_2)).thenReturn(Optional.of(userRepresentation));

        performPut("/user/initialize-profile", new InitializeProfileCommandDTO().channelSource(UserChannelSourceDTO.NOTHING)).andExpect(status().isOk());

        txHelper.doInTransaction(() -> assertThat(userProfileRepository.findByUserId(NEW_USER_ID_2).orElseThrow().generalInformation().getPhoneNumber())
                .isEqualTo(withPhone ? "0033428201898" : null));
        Mockito.verify(keycloakService, times(withPhone ? 1 : 0)).removePhoneForFOUser(NEW_USER_ID_2);
        reset(notifier);
    }


    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    void assign_groups_to_user_from_url_without_lp_should_succeed() throws Exception {
        setKeycloakContext();
        var organization1 = "T-011";
        var organization2 = "T-012";
        var badOrganization = "T-XXX";
        organizationGenerator.createRecruiter(organization1);
        organizationGenerator.createRecruiter(organization2);
        organizationGenerator.createRecruiter("T-013");
        var command = new InitializeProfileCommandDTO()
                .channelSource(UserChannelSourceDTO.URL)
                .value(List.of(organization1, organization2, badOrganization));


        performPut("/user/initialize-profile", command).andExpect(status().isOk());

        verify(keycloakService).assignToFrontOfficeGroups(NEW_USER_ID, Set.of(organization1, organization2));
        verify(keycloakService, never()).assignToFrontOfficeGroups(NEW_USER_ID, Set.of(badOrganization));
        txHelper.doInTransaction(() -> assertThat(userProfileRepository.findByUserId(NEW_USER_ID).orElseThrow().channels())
                .contains(organization1, organization2));
        verify(notifier, VerificationModeFactory.times(2)).sendMessage(any());
        reset(notifier);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    void assign_group_without_organization_should_succeed() throws Exception {
        setKeycloakContext();

        performPut("/user/initialize-profile", createLandingPageAndInitializeProfileCommand(false)).andExpect(status().isOk());

        verify(keycloakService, never())
                .assignToFrontOfficeGroups(anyString(), any());
        txHelper.doInTransaction(() -> assertThat(userProfileRepository.findByUserId(NEW_USER_ID).orElseThrow().channels())
                .isEmpty()
        );
        verify(notifier, VerificationModeFactory.times(1)).sendMessage(any());
        reset(notifier);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = {Role.CANDIDATE, Role.ODAS_ADMIN})
    void remove_single_user_from_single_group() throws Exception {
        setKeycloakContext();

        performPut("/user/initialize-profile", createLandingPageAndInitializeProfileCommand(true)).andExpect(status().isOk());

        verify(keycloakService).assignToFrontOfficeGroups(NEW_USER_ID, Set.of(organization.getCode()));
        txHelper.doInTransaction(() -> assertThat(userProfileRepository.findByUserId(NEW_USER_ID).orElseThrow().channels())
                .contains(organization.getCode()));

        var removalCommand = new UpdateUsersChannelsCommandDTO()
                .channelsToRemove(List.of(organization.getCode()))
                .usersId(Collections.singletonList(NEW_USER_ID));

        performPut("/user/update-users-channels", removalCommand).andExpect(status().isNoContent());

        verify(keycloakService).removeUserFromFrontOfficeGroup(NEW_USER_ID, organization.getCode());
        txHelper.doInTransaction(() ->
                assertThat(userProfileRepository.findByUserId(NEW_USER_ID)
                        .orElseThrow().channels())
                        .doesNotContain(organization.getCode()));
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void list_all_users_of_channels() throws Exception {
        var jobOrganizationCode = "E-001";
        var otherOrganizationCode = "E-002";
        var allOrganizationsCodes = Set.of(jobOrganizationCode, otherOrganizationCode);

        var usersOfJobOrganization = new AbstractService.PageDTOAdapter<>(new PageImpl<>(List.of(
                getUserRepresentation(2, true),
                getUserRepresentation(1, true),
                getUserRepresentation(3, false),
                getUserRepresentation(5, true)
        )));
        var usersOfOtherOrganization = new AbstractService.PageDTOAdapter<>(new PageImpl<>(List.of(
                getUserRepresentation(4, true),
                getUserRepresentation(0, true),
                getUserRepresentation(6, false)
        )));

        when(keycloakService.getGroupsOfRoles(Collections.singleton(jobOrganizationCode))).thenReturn(allOrganizationsCodes);
        when(keycloakService.getBackOfficeGroupMembersPaginatedResource(jobOrganizationCode, 0, 10000)).thenReturn(usersOfJobOrganization);
        when(keycloakService.getBackOfficeGroupMembersPaginatedResource(otherOrganizationCode, 0, 10000)).thenReturn(usersOfOtherOrganization);
        performGetAndExpect("/user/" + jobOrganizationCode + "/list/", "usersHavingAccessToChannel", true);
    }

    private UserRepresentation getUserRepresentation(int index, boolean enabled) {
        return new UserRepresentation()
                .setEnabled(enabled)
                .setFirstName("John" + index)
                .setLastName(index % 3 == 0 ? null : ((index % 2 == 0 ? "Ùl" : "ul") + "mith" + index))
                .setEmail("john@smith" + index)
                .setId("johnId" + index);
    }


    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, ORGA_CODE, PROJECT_CODE})
    void switch_two_users_channels_OT() throws Exception {
        switch_two_users_channels_common();
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void switch_two_users_channels_admin() throws Exception {
        switch_two_users_channels_common();
    }

    private void switch_two_users_channels_common() throws Exception {
        var userId1 = UUID.randomUUID();
        var userId2 = UUID.randomUUID();
        userProfileGenerator.createUserProfile(userId1, PROJECT_CODE);
        userProfileGenerator.createUserProfile(userId2, PROJECT_CODE);

        var switchCommand = new UpdateUsersChannelsCommandDTO()
                .channelsToRemove(List.of(PROJECT_CODE))
                .usersId(List.of(userId1.toString(), userId2.toString()))
                .channelsToAdd(List.of(ORGA_CODE));

        performPut("/user/update-users-channels", switchCommand).andExpect(status().isNoContent());

        verify(keycloakService).removeUserFromFrontOfficeGroup(userId1.toString(), PROJECT_CODE);
        verify(keycloakService).removeUserFromFrontOfficeGroup(userId2.toString(), PROJECT_CODE);
        verify(keycloakService).assignToFrontOfficeGroups(userId1.toString(), Set.of(ORGA_CODE));
        verify(keycloakService).assignToFrontOfficeGroups(userId2.toString(), Set.of(ORGA_CODE));
        txHelper.doInTransaction(() -> {
            assertThat(userProfileRepository.findByUserId(userId1.toString())
                    .orElseThrow().channels())
                    .doesNotContain(PROJECT_CODE)
                    .contains(ORGA_CODE);
            assertThat(userProfileRepository.findByUserId(userId2.toString())
                    .orElseThrow().channels())
                    .doesNotContain(PROJECT_CODE)
                    .contains(ORGA_CODE);
        });
    }

    @ResetDataAfter
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    @DisplayName("Given a user without profile when init profile is called then profile is created without channel ")
    @ParameterizedTest
    @EnumSource(value = UserChannelSourceDTO.class)
    void init_profile_without_channel(UserChannelSourceDTO source) throws Exception {

        setKeycloakContext();

        var command = new InitializeProfileCommandDTO()
                .channelSource(source);

        performPut("/user/initialize-profile", command).andExpect(status().isOk());

        verify(keycloakService, never()).assignToFrontOfficeGroups(anyString(), any());
        txHelper.doInTransaction(() ->
                assertThat(userProfileRepository.findByUserId(NEW_USER_ID).orElseThrow())
                        .matches(a -> a.channelSourceType().name().equals(source.name()))
                        .extracting(UserProfile::channels)
                        .matches(Set::isEmpty));
    }


    @ResetDataAfter
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    @DisplayName("Given a user without profile when init profile is called then profile is created without channel ")
    @Test
    void init_profile_without_channel_mandatoryIdentity_for_previous_channel() throws Exception {
        setKeycloakContext();
        txHelper.doInTransaction(() -> {
            var user = userProfileGenerator.createUserProfile(UUID.fromString(NEW_USER_ID));
            var recruiter = organizationGenerator.createRecruiter("O-0042");
            user.updatedChannels(Set.of(recruiter.getCode()), UserChannel.ChannelSourceType.ADMIN);
            recruiter.setMandatoryIdentity(true);
        });

        var command = new InitializeProfileCommandDTO()
                .channelSource(UserChannelSourceDTO.NOTHING);

        performPut("/user/initialize-profile", command)
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.mandatoryIdentity", is(true)));

    }

    @ResetDataAfter
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    @Test
    void init_profile_should_reset_last_connexion_date() throws Exception {

        setKeycloakContext();
        txHelper.doInTransaction(() -> {
            var user = userProfileGenerator.createUserProfile(UUID.fromString(NEW_USER_ID));
            user.lastConnectionDate(LocalDateTime.of(2020, 8, 10, 0, 0, 0, 0));
        });
        var command = new InitializeProfileCommandDTO()
                .channelSource(UserChannelSourceDTO.NOTHING);

        performPut("/user/initialize-profile", command).andExpect(status().isOk());

        txHelper.doInTransaction(() ->
                assertThat(userProfileRepository.findByUserId(NEW_USER_ID).orElseThrow().lastConnectionDate())
                        .isCloseTo(LocalDateTime.now(), new TemporalUnitWithinOffset(2, ChronoUnit.HOURS)));
    }

    @ResetDataAfter
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    @DisplayName("Given a user initialized at step 3 when init profile is called then profile is retrieved with step ")
    @Test
    void init_profile_without_channel_retrieves_step() throws Exception {
        setKeycloakContext();
        txHelper.doInTransaction(() -> userProfileGenerator.createUserProfile(UUID.fromString(NEW_USER_ID)).userRegistrationState().updateRegistrationStep(UserRegistrationState.RegistrationStep.CONFIRMED_CITY));

        var command = new InitializeProfileCommandDTO()
                .channelSource(UserChannelSourceDTO.URL);

        performPut("/user/initialize-profile", command)
                .andExpect(status().isOk())
                .andExpect(TestUtils.jsonMatchesContent("profileInitialized"))
        ;
    }

    private InitializeProfileCommandDTO createLandingPageAndInitializeProfileCommand(boolean withOrganization) {
        var landingPage = landingPageRepository.save(LandingPage
                .builder()
                .id(UUID.randomUUID())
                .urlKey("key")
                .organizations(withOrganization ? Set.of(organization) : null)
                .content("content")
                .build());

        return new InitializeProfileCommandDTO()
                .channelSource(UserChannelSourceDTO.LANDING_PAGE)
                .value(List.of(landingPage.getUrlKey()));
    }

}
