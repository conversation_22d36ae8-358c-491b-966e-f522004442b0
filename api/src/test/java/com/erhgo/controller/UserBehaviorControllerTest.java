package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.enums.BehaviorCategory;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.generators.BehaviorGenerator;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.List;
import java.util.Set;

class UserBehaviorControllerTest extends AbstractIntegrationTest {

    static final String USER_ID = "123e4567-e89b-12d3-a456-426614174000";

    @Autowired
    BehaviorGenerator behaviorGenerator;

    @Autowired
    UserProfileMotherObject userProfileMotherObject;

    @Autowired
    UserProfileRepository userProfileRepository;

    @Autowired
    Environment environment;

    @Autowired
    ApplicationContext applicationContext;

    @ResetDataAfter
    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    void getUserBehaviors() {
        createUserWithBehaviors();
        performGetAndExpect("/user/behavior/details/" + USER_ID, "userBehaviors", true);
    }

    private UserProfile createUserWithBehaviors() {
        var constancy = behaviorGenerator.createBehavior("be-1", "title be-1", "descr be-1", BehaviorCategory.SOCIABILITY, 1);
        var sociability = behaviorGenerator.createBehavior("be-2", "a - title be-2", "descr be-2", BehaviorCategory.CONSTANCY, 2);
        behaviorGenerator.createBehaviorForCategory(BehaviorCategory.HONESTY);

        return userProfileMotherObject.withUserId(USER_ID).withBehaviors(Set.of(constancy, sociability)).buildAndPersist();
    }

    @SneakyThrows
    @ResetDataAfter
    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    void emptyUserBehaviors() {
        createUserWithBehaviors();
        performPut("/user/behavior/" + USER_ID, List.of()).andExpect(MockMvcResultMatchers.status().isNoContent());
        txHelper.doInTransaction(() -> {
            var updatedUser = userProfileRepository.findByUserId(USER_ID).orElseThrow();
            Assertions.assertThat(updatedUser.getBehaviorsCategories()).isEmpty();
        });
    }

    @SneakyThrows
    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    void updateUserBehaviors() {
        var confidentiality = behaviorGenerator.createBehaviorForCategory(BehaviorCategory.CONFIDENTIALITY);
        var user = createUserWithBehaviors();
        var other = user.behaviors().iterator().next();
        performPut("/user/behavior/" + USER_ID, List.of(confidentiality.getId(), other.getId())).andExpect(MockMvcResultMatchers.status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedUser = userProfileRepository.findByUserId(USER_ID).orElseThrow();
            Assertions.assertThat(updatedUser.getBehaviorsCategories()).containsExactlyInAnyOrder(BehaviorCategory.CONFIDENTIALITY, other.getBehaviorCategory());
        });
    }
}
