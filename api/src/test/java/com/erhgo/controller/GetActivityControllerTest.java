package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.TestUtils;
import com.erhgo.security.Role;
import org.junit.Test;
import org.springframework.security.test.context.support.WithMockUser;

import static com.erhgo.generators.TestFixtures.ACT_01;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class GetActivityControllerTest extends AbstractIntegrationTestWithFixtures {
    @Test
    @WithMockUser(roles = Role.ODAS_ADMIN)
    public void getJobActivityWithLabels() throws Exception {
        mvc.perform(get("/api/odas/activityLabel/JOB/" + ACT_01.getUuid() + "/activity"))
                .andExpect(TestUtils.jsonMatchesContent("jobActivity"))
                .andExpect(status().isOk())
        ;
    }

}
