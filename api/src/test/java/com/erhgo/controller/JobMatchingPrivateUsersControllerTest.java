package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TestUtils;
import com.erhgo.config.ApiConstants;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.job.JobType;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.JobGenerator;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.generators.UserProfileGenerator;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class JobMatchingPrivateUsersControllerTest extends AbstractIntegrationTest {

    @Autowired
    private JobGenerator jobGenerator;

    @Autowired
    private CapacityGenerator capacityGenerator;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Autowired
    private OrganizationGenerator organizationGenerator;


    private MockHttpServletRequestBuilder getMatchingUsersRequestBuilder(UUID jobId) {
        return get(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + jobId + "/matching-users")
                .param("page", "0")
                .param("size", "600")
                .contentType(MediaType.APPLICATION_JSON);
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    @DisplayName("" +
            "Given four users having one capacity C1 and: \n" +
            "   - U1: public channel T-1 \n" +
            "   - U2: no channel \n" +
            "   - U3: private channel T-2 \n" +
            "   - U4: private channel T-3 \n" +
            " And a job with same capacity on organization T-2 \n" +
            " When I find matching users \n" +
            " Then users U1, U2, U3 are retrieved \n"
    )
    void findMatchingUsersForJob_sort_by_multiple_criteria_enabled_then_capacities() throws Exception {
        var matchingCapacity = capacityGenerator.createCapacity("CA-1");
        var uuidRef = "3d903206-7ae9-48d6-862b-001e9201599";
        var expectedUuid1 = UUID.fromString(uuidRef + "1");
        var expectedUuid2 = UUID.fromString(uuidRef + "2");
        var expectedUuid3 = UUID.fromString(uuidRef + "3");
        var jobIdHolder = new AtomicReference<UUID>();
        txHelper.doInTransaction(() -> {
            var publicChannel = "T-1";
            var privateChannel1 = "T-2";
            var privateChannel2 = "T-3";
            organizationGenerator.createRecruiter(publicChannel).setPrivateUsers(false);
            var jobOrga = organizationGenerator.createRecruiter(privateChannel1);
            jobOrga.setPrivateUsers(true);
            organizationGenerator.createRecruiter(privateChannel2).setPrivateUsers(true);
            userProfileGenerator.createUserProfileWithChannelsAndCapacities(expectedUuid1, 1, Set.of(publicChannel), matchingCapacity);
            userProfileGenerator.createUserProfileWithChannelsAndCapacities(expectedUuid2, 1, Set.of(), matchingCapacity);
            userProfileGenerator.createUserProfileWithChannelsAndCapacities(expectedUuid3, 1, Set.of(privateChannel1), matchingCapacity);
            userProfileGenerator.createUserProfileWithChannelsAndCapacities(UUID.randomUUID(), 1, Set.of(privateChannel2), matchingCapacity);

            var job = jobGenerator.createJobWithCapacitiesAndLevel(JobType.SIMPLE, "J-1", jobOrga, com.erhgo.domain.classifications.erhgooccupation.MasteryLevel.MIN_LEVEL, matchingCapacity);
            jobIdHolder.set(job.getId());
        });
        mvc.perform(getMatchingUsersRequestBuilder(jobIdHolder.get())
                        .param("capacityThreshold", "0.1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(TestUtils.jsonMatchesContent("jobMatchingUsersWithPrivateChannels"));
    }

}
