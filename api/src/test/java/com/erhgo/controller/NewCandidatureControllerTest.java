package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.criteria.Criteria;
import com.erhgo.domain.enums.CriteriaQuestionType;
import com.erhgo.domain.enums.Situation;
import com.erhgo.domain.job.JobMotherObject;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.generators.CriteriaMotherObject;
import com.erhgo.generators.RecruiterMotherObject;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.mailing.MailingListService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Collections;
import java.util.Set;

import static org.hamcrest.Matchers.is;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


class NewCandidatureControllerTest extends AbstractIntegrationTest {

    private static final String USER_UUID = "2e9a801b-7443-4e03-ae38-1ed64c1a175c";
    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    MockMvc mvc;

    Criteria thresholdCriteria1, thresholdCriteria2, thresholdCriteria3, thresholdCriteria4, multipleCriteria1, multipleCriteria2, multipleCriteria3, multipleCriteria4;
    int index;

    @MockitoBean
    MailingListService mailingListService;

    @BeforeEach
    void initData() {
        thresholdCriteria1 = buildCriteria(CriteriaQuestionType.THRESHOLD, "TC1");
        thresholdCriteria2 = buildCriteria(CriteriaQuestionType.THRESHOLD, "TC2");
        multipleCriteria1 = buildCriteria(CriteriaQuestionType.MULTIPLE, "MC1");
        multipleCriteria2 = buildCriteria(CriteriaQuestionType.MULTIPLE, "MC2");
        multipleCriteria3 = buildCriteria(CriteriaQuestionType.MULTIPLE, "MC3");
        thresholdCriteria3 = buildCriteria(CriteriaQuestionType.THRESHOLD, "TC3");
        multipleCriteria4 = buildCriteria(CriteriaQuestionType.MULTIPLE, "MC4");
        thresholdCriteria4 = buildCriteria(CriteriaQuestionType.THRESHOLD, "TC4");
    }

    private Criteria buildCriteria(CriteriaQuestionType questionType, String valueCode) {
        return applicationContext.getBean(CriteriaMotherObject.class)
                .withQuestionType(questionType)
                .withCriteriaIndex(index++)
                .withValueCodes(
                        "%s-1".formatted(valueCode),
                        "%s-2".formatted(valueCode),
                        "%s-3".formatted(valueCode)
                )
                .buildAndPersist();

    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_UUID, roles = Role.CANDIDATE)
    void initialize_candidature_with_perfect_match_and_complete_profile() {
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist();
        var recruitment = getCompletedRecruitment();
        applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_UUID)
                .withEmail("<EMAIL>")
                .withFirstname("Philippe")
                .withLastname("Dupont")
                .withLocation(Location.builder().city("Lyon").build())
                .withSalary(20500)
                .withSituation(Situation.RESEARCHING)
                .withDelayInMonth(null)
                .withCriteriaAnswers(Set.of(
                                thresholdCriteria1.getCriteriaValues().get(1),
                                thresholdCriteria2.getCriteriaValues().get(2),
                                multipleCriteria1.getCriteriaValues().get(0),
                                multipleCriteria2.getCriteriaValues().get(0),
                                multipleCriteria2.getCriteriaValues().get(2),
                                multipleCriteria3.getCriteriaValues().get(0),
                                multipleCriteria3.getCriteriaValues().get(1),
                                multipleCriteria3.getCriteriaValues().get(2),
                                thresholdCriteria3.getCriteriaValues().get(2),
                                multipleCriteria4.getCriteriaValues().get(2)
                        ),
                        Collections.emptyList())
                .withExperience(occupation, 9)
                .withJobOfferOptOut(false)
                .buildAndPersist();

        performGetAndExpect(
                "/candidature/for-job/%s/initial-data".formatted(recruitment.getId()),
                "informationsForCandidatureWithPerfectMatch",
                false
        );
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_UUID, roles = Role.CANDIDATE)
    void initialize_candidature_with_mismatch_and_incomplete_profile() {
        var recruitment = getCompletedRecruitment();

        applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_UUID)
                .withEmail("<EMAIL>")
                .withFirstname(null)
                .withLastname(null)
                .withSalary(30000)
                .withSituation(Situation.RESEARCHING)
                .withDelayInMonth(2)
                .withCriteriaAnswers(
                        Set.of(
                                /* TC1 ok - not in result*/
                                thresholdCriteria1.getCriteriaValues().get(1),
                                /* No answer for TC2 */
                                /* TC3 too low */
                                thresholdCriteria3.getCriteriaValues().get(0),
                                multipleCriteria4.getCriteriaValues().get(1),
                                multipleCriteria4.getCriteriaValues().get(2)
                        ),
                        Set.of(
                                multipleCriteria1.getCriteriaValues().get(0),
                                multipleCriteria3.getCriteriaValues().get(0),
                                multipleCriteria3.getCriteriaValues().get(1)
                        )
                )
                .withJobOfferOptOut(true)
                .buildAndPersist();

        performGetAndExpect(
                "/candidature/for-job/%s/initial-data".formatted(recruitment.getId()),
                "informationsForCandidatureWithMismatch",
                true
        );
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_UUID, roles = Role.CANDIDATE)
    void initialize_candidature_with_one_mismatch_and_one_match_on_same_criteria() {
        var recruitment = getCompletedRecruitment();

        applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_UUID)
                .withEmail("<EMAIL>")
                .withFirstname(null)
                .withLastname(null)
                .withSalary(30000)
                .withSituation(Situation.RESEARCHING)
                .withDelayInMonth(2)
                .withCriteriaAnswers(
                        Set.of(
                                thresholdCriteria1.getCriteriaValues().get(1),
                                thresholdCriteria2.getCriteriaValues().get(2),
                                multipleCriteria1.getCriteriaValues().get(0),
                                multipleCriteria2.getCriteriaValues().get(0),
                                multipleCriteria2.getCriteriaValues().get(2),
                                multipleCriteria3.getCriteriaValues().get(0),
                                thresholdCriteria3.getCriteriaValues().get(1),
                                thresholdCriteria4.getCriteriaValues().get(0)
                        ),
                        Set.of(
                                multipleCriteria3.getCriteriaValues().get(2)
                        )
                )
                .withJobOfferOptOut(true)
                .buildAndPersist();

        performGetAndExpect(
                "/candidature/for-job/%s/initial-data".formatted(recruitment.getId()),
                "informationsForCandidatureWithMismatchAndMatchOnSameCriteria",
                true
        );
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_UUID, roles = Role.CANDIDATE)
    void initialize_candidature_user_answered_custom_question() throws Exception {
        var recruitment = getCompletedRecruitment();

        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_UUID)
                .withEmail("<EMAIL>")
                .buildAndPersist();

        var availabilityDelayInMonth = 42;
        var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userProfile)
                .withRecruitment(recruitment)
                .withAnswer("Réponse à la question personnalisée")
                .withAvailability(false, availabilityDelayInMonth)
                .buildAndPersist();

        mvc.perform(get("/api/odas/candidature/for-job/%s/initial-data".formatted(recruitment.getId())))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.answer", is(candidature.getCustomAnswer())))
                .andExpect(jsonPath("$.condition.availabilityForCandidature.isAvailable", is(false)))
                .andExpect(jsonPath("$.condition.availabilityForCandidature.availabilityDelayInMonth", is(availabilityDelayInMonth)))
        ;

    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_UUID, roles = Role.CANDIDATE)
    void initialize_candidature_with_new_account() {
        var recruitment = getCompletedRecruitment();

        applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_UUID)
                .withEmail("<EMAIL>")
                .withFirstname(null)
                .withLastname(null)
                .withSalary(null)
                .withSituation(null)
                .withDelayInMonth(null)
                .withJobOfferOptOut(null)
                .withLocation(null)
                .buildAndPersist();

        performGetAndExpect(
                "/candidature/for-job/%s/initial-data".formatted(recruitment.getId()),
                "informationsForCandidatureWithNewAccount",
                true
        );
    }

    private Recruitment getCompletedRecruitment() {
        var recruiter = applicationContext.getBean(RecruiterMotherObject.class)
                .withTitle("Nom Entreprise")
                .withGdprMention("<p>Mention RGPD</p>")
                .buildAndPersist();
        var job = applicationContext.getBean(JobMotherObject.class)
                .withCriteriaValues(
                        thresholdCriteria1.getCriteriaValues().get(1),
                        thresholdCriteria2.getCriteriaValues().get(2),
                        multipleCriteria1.getCriteriaValues().get(0),
                        multipleCriteria2.getCriteriaValues().get(0),
                        multipleCriteria2.getCriteriaValues().get(2),
                        multipleCriteria3.getCriteriaValues().get(0),
                        multipleCriteria3.getCriteriaValues().get(1),
                        multipleCriteria3.getCriteriaValues().get(2),
                        thresholdCriteria3.getCriteriaValues().get(1),
                        // Next criteria value is the lowest value of threshold criteria, should not be considered
                        // see https://erhgo.atlassian.net/browse/ERHGO-1577?focusedCommentId=14452
                        thresholdCriteria4.getCriteriaValues().get(0)
                )
                .withRecruiter(recruiter)
                .buildAndPersist();
        return applicationContext.getBean(RecruitmentMotherObject.class)
                .withRecruitmentProfileQuestion("Question personnalisée")
                .withJob(job)
                .withSalaries(10000, 20000)
                .buildAndPersist();
    }
}
