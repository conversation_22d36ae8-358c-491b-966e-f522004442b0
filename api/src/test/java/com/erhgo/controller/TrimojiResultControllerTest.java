package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.assertj.core.data.TemporalUnitWithinOffset;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper;

import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Map;

class TrimojiResultControllerTest extends AbstractIntegrationTest {

    private static final String TRIMOJI_AS_JSON_NO_USER = """
            {
                "token": "t",
                "candidate_mail": "<EMAIL>",
                "result_pdf": "%s",
                "result_url": "https://assess.trimoji.fr/test/3cc47972-1496-4561-8260-abb4b0cd12f8/profile/long",
                "metadatas": { "customer_id": "ID_IN_YOUR_DB", "candidate_id": "%s", "exemple": "whatever" }
            }
            """;
    public static final String TRIMOJI_AS_STRING = """
            {"token":"t","candidate_mail":"xcandidate@localhost","result_pdf":"%s","result_url":"https://assess.trimoji.fr/test/08313f34-c337-4157-a03a-4775f91a5ed6/profile","metadatas":"{\\"candidate_id\\":\\"%s\\"}"}
            """;

    static final String USER_ID = "3cc42424-1496-4242-4242-4242b0cd12f8";

    @Autowired
    ApplicationContext applicationContext;

    @Test
    @SneakyThrows
    void postTrimojiResultAsJsonSucceedUnknownUser() {
        performPost("/public/trimoji", new ObjectMapper().readValue("""
                {"result_pdf": "pouf"}
                """, Map.class))
                .andExpect(MockMvcResultMatchers.status().isNoContent());
    }

    @Test
    @SneakyThrows
    void postTrimojiBadJsonFails400() {
        performPost("/public/trimoji", """
                {"result_pdf": "pouf"
                """)
                .andExpect(MockMvcResultMatchers.status().isBadRequest());
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @SneakyThrows
    void postTrimojiResultAsJsonSucceedUnknownUser(boolean metadatasAsString) {
        performPost("/public/trimoji", new ObjectMapper().readValue((metadatasAsString ? TRIMOJI_AS_STRING : TRIMOJI_AS_JSON_NO_USER).formatted("url", "unknown"), Map.class))
                .andExpect(MockMvcResultMatchers.status().isNoContent());
    }

    @ParameterizedTest
    @ResetDataAfter
    @ValueSource(booleans = {true, false})
    @SneakyThrows
    void postTrimojiResultAsJsonSucceedKnownUser(boolean metadatasAsString) {
        var url = "https://24.fr";
        applicationContext.getBean(UserProfileMotherObject.class).withUserId(USER_ID).buildAndPersist();
        performPost("/public/trimoji", new ObjectMapper().readValue((metadatasAsString ? TRIMOJI_AS_STRING : TRIMOJI_AS_JSON_NO_USER).formatted(url, USER_ID), Map.class))
                .andExpect(MockMvcResultMatchers.status().isNoContent());
        Assertions.assertThat(applicationContext.getBean(UserProfileRepository.class).findByUserId(USER_ID).map(UserProfile::trimojiPdfUrl).orElseThrow()).isEqualTo(url);
    }

    @Test
    @ResetDataAfter
    @SneakyThrows
    @WithMockKeycloakUser(roles = Role.CANDIDATE, id = USER_ID)
    void getTrimojiURL() {
        var url = "https://24.fr";
        applicationContext.getBean(UserProfileMotherObject.class).withUserId(USER_ID).buildAndPersist();
        performGetAndExpect("/trimoji", "trimojiResult", false);
        txHelper.doInTransaction(() -> {
            Assertions.assertThat(applicationContext.getBean(UserProfileRepository.class).findByUserId(USER_ID).orElseThrow().trimojiStatus().startedDate()).isCloseTo(OffsetDateTime.now(), new TemporalUnitWithinOffset(30, ChronoUnit.MINUTES));
        });
    }
}
