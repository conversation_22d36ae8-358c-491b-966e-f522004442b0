package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.AuditableFieldHelper;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.CandidatureEmailRefusalState;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.dto.EmailVerificationResultDTO;
import com.erhgo.domain.enums.*;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.userprofile.*;
import com.erhgo.domain.userprofile.experience.UserExperience;
import com.erhgo.generators.*;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.GeneralInformationRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.RecruitmentRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.dto.UserKeycloakRepresentation;
import com.erhgo.services.dtobuilder.GeneralInformationDTOBuilder;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailingListService;
import com.erhgo.services.mailing.check.EmailVerificationService;
import lombok.SneakyThrows;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.servlet.ResultActions;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.erhgo.TestUtils.jsonMatchesContent;
import static com.erhgo.domain.candidature.job.CandidatureRecruitmentState.NEW;
import static com.erhgo.domain.candidature.job.CandidatureRecruitmentState.SELECTED;
import static com.erhgo.generators.TestFixtures.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.fail;
import static org.hamcrest.Matchers.*;
import static org.hamcrest.Matchers.contains;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.any;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class UserProfileControllerTest extends AbstractIntegrationTestWithFixtures {

    private static final String NEW_USER_ID = "56b64e21-4771-4d01-8cf7-696866d3ae49";

    @SpyBean
    private KeycloakMockService keycloakMockService;

    @Autowired
    private DataGeneratorService dataGeneratorService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private UserProfileRepository userProfileRepository;

    private static RecruitmentCandidature LOCAL_CANDIDATURE_1, LOCAL_CANDIDATURE_2;

    @Autowired
    private RecruitmentCandidatureRepository recruitmentCandidatureRepository;

    @Autowired
    private RecruitmentRepository recruitmentRepository;

    @Autowired
    private GeneralInformationRepository generalInformationRepository;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Autowired
    private CandidatureGenerator candidatureGenerator;

    @Autowired
    private RecruitmentGenerator recruitmentGenerator;

    @Autowired
    private OrganizationGenerator organizationGenerator;

    @Autowired
    private CapacityGenerator capacityGenerator;

    @Autowired
    private UserExperienceGenerator userExperienceGenerator;
    @MockitoBean
    private MailingListService mailingListService;

    @MockitoBean
    private EmailVerificationService emailVerificationService;

    @Autowired
    private BehaviorGenerator behaviorGenerator;

    @Autowired
    AuditableFieldHelper auditableFieldHelper;

    private static final String CHANNEL1 = "E-0123", CHANNEL2 = "E-0124";

    @BeforeEach
    public void init() {
        Mockito.reset(mailingListService);
        txHelper.doInTransaction(() -> {
            recruitmentCandidatureRepository.findByRecruitmentCodeAndUserProfileUserId(RECRUITMENT_WITHOUT_MATCHING_CANDIDATURES.getCode(), USER_PROFILE_WITH_MATCHING_CANDIDATURE.userId()).ifPresent(recruitmentCandidatureRepository::delete);
            recruitmentCandidatureRepository.findByRecruitmentCodeAndUserProfileUserId(RECRUITMENT_WITH_QUESTION_AND_CANDIDATURE.getCode(), USER_PROFILE_WITH_MATCHING_CANDIDATURE.userId()).ifPresent(recruitmentCandidatureRepository::delete);
        });
        txHelper.doInTransaction(() -> {
            LOCAL_CANDIDATURE_1 = applicationContext.getBean(RecruitmentCandidatureMotherObject.class).withRecruitment(RECRUITMENT_WITHOUT_MATCHING_CANDIDATURES).withState(GlobalCandidatureState.INTRODUCE_TO_CLIENT).withUserProfile(USER_PROFILE_WITH_MATCHING_CANDIDATURE).buildAndPersist();

            auditableFieldHelper.updateCreatedDate(LOCAL_CANDIDATURE_1, new Date(0));
            LOCAL_CANDIDATURE_1.getRecruitment().setState(RecruitmentState.CLOSED, false);
            recruitmentCandidatureRepository.save(LOCAL_CANDIDATURE_1);

            LOCAL_CANDIDATURE_2 = applicationContext.getBean(RecruitmentCandidatureMotherObject.class).withRecruitment(RECRUITMENT_WITH_QUESTION_AND_CANDIDATURE).withState(GlobalCandidatureState.NOT_FINALIZED).withUserProfile(USER_PROFILE_WITH_MATCHING_CANDIDATURE).buildAndPersist();
            auditableFieldHelper.updateCreatedDate(LOCAL_CANDIDATURE_2, new Date(1_000_000));
            LOCAL_CANDIDATURE_2.setState(CandidatureState.STARTED);
            recruitmentCandidatureRepository.save(LOCAL_CANDIDATURE_2);

            recruitmentRepository.save(LOCAL_CANDIDATURE_1.getRecruitment());

            organizationGenerator.createRecruiter(CHANNEL1);
            organizationGenerator.createRecruiter(CHANNEL2).setPrivateUsers(true);
            ReflectionTestUtils.setField(userProfileRepository.findByUserId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE).orElseThrow(), "lastConnectionDate", LocalDateTime.of(2004, 12, 25, 13, 11));
        });
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = Role.CANDIDATE)
    @ResetDataAfter
    void get_profile_should_return_profile_for_normal_user() throws Exception {
        var blacklistedOccupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withId(UUID.fromString("00000000-0000-0000-0000-000000000000"))
                .withTitle("Métier refusé")
                .buildAndPersist();
        txHelper.doInTransaction(() -> {
            var userProfile = userProfileRepository.findByUserId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE).orElseThrow();
            userProfile.updateBehaviors(Set.of(behaviorGenerator.createBehaviorForCategory(BehaviorCategory.SOCIABILITY, 421), behaviorGenerator.createBehaviorForCategory(BehaviorCategory.CONSTANCY, 422)));
            userProfile.addOccupationToBlacklist(blacklistedOccupation);
            userProfile.trimojiPdfUrl("https://trimoji");
            userProfileGenerator.prepareCriteria(userProfile, DriverLicence.LICENCE_B);
        });
        mvc.perform(get("/api/odas/user/%s/profileSummary".formatted(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE)))
                .andExpect(status().isOk())
                .andExpect(jsonMatchesContent("userProfileSummary"))
                .andExpect(jsonPath("$.lastCandidatures[*].recruitmentCode", contains(Stream.of(CANDIDATURE_MATCHING, LOCAL_CANDIDATURE_2, LOCAL_CANDIDATURE_1).map(c -> c.getRecruitment().getCode()).toArray())));
    }


    @ResetDataAfter
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    @DisplayName("Given a user on a 'private users' channel when init profile is called then profile is retrieved without any optional menu")
    @Test
    void init_profile_for_private_user() throws Exception {
        txHelper.doInTransaction(() -> {
            var privateChannel = "T-021";
            var publicChannel = "T-022";
            organizationGenerator.createRecruiter(privateChannel).setPrivateUsers(true);
            organizationGenerator.createRecruiter(publicChannel).setPrivateUsers(false);
            userProfileGenerator.createUserProfile(UUID.fromString(NEW_USER_ID), privateChannel, publicChannel);
        });

        mvc.perform(get("/api/odas/user/" + NEW_USER_ID + "/profileSummary"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.includesMenus[*]", Matchers.hasSize(0)))
        ;
    }

    @ResetDataAfter
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    @DisplayName("Given a user with candidatures & recruitment when get profile is called then profile is retrieved with flags set ")
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void init_profile_without_channel_retrieves_step_and_menus_to_show(boolean modifiedByUser) throws Exception {
        txHelper.doInTransaction(() ->
        {
            var userProfile = userProfileGenerator.createUserProfile(UUID.fromString(NEW_USER_ID));
            var capacities = IntStream.range(0, 11).mapToObj(i -> capacityGenerator.createCapacity("CA42-" + i)).toArray(Capacity[]::new);
            userProfile.userRegistrationState().updateRegistrationStep(UserRegistrationState.RegistrationStep.CREATED_ACCOUNT);
            userExperienceGenerator.createExperienceWithCapacities(userProfile, MasteryLevel.MAX_LEVEL.getMasteryLevel(), capacities);
            applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                    .withUserProfile(userProfile)
                    .withModifiedByUser(modifiedByUser)
                    .withRecruitment(
                            applicationContext.getBean(RecruitmentMotherObject.class)
                                    .withOccupation(
                                            applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist()
                                    )
                                    .buildAndPersist()
                    ).buildAndPersist();

            applicationContext.getBean(RecruitmentMotherObject.class)
                    .withOccupation(
                            applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist()
                    )
                    .withState(RecruitmentState.PUBLISHED)
                    .buildAndPersist();
            // Recruitment without candidature
            recruitmentGenerator.createRecruitmentWithNoRequirement(organizationGenerator.createRecruiter("T-23"), "J-56", capacities[2], capacities[3]);
        });
        performGetAndExpect("/user/" + NEW_USER_ID + "/profileSummary", modifiedByUser ? "profileAllMenus" : "profileMenusNoCandidature", false)
        ;
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = Role.CANDIDATE)
    @ResetDataAfter
    void get_profile_should_return_profile_for_normal_user_with_channel_and_no_soft_skills() throws Exception {
        txHelper.doInTransaction(() -> userProfileRepository.findByUserId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE)
                .orElseThrow()
                .updatedChannels(null, UserChannel.ChannelSourceType.NOTHING));
        mvc.perform(get("/api/odas/user/%s/profileSummary".formatted(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.isSourceInitialized", is(true)))
                .andExpect(jsonPath("$.softSkillsStatus", is(UserProfileSummaryDTO.SoftSkillsStatusEnum.NOT_STARTED.name())))
        ;

    }

    @Test
    @WithMockKeycloakUser(id = "pipo", roles = Role.CANDIDATE)
    @ResetDataAfter
    void get_profile_should_fail_wrong_user() throws Exception {
        mvc.perform(get("/api/odas/user/" + KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE + "/profileSummary"))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockKeycloakUser(id = "test1", roles = Role.CANDIDATE)
    @ResetDataAfter
    void get_profile_should_return_top_5_candidatures() throws Exception {
        var userProfile = userProfileRepository.save(new UserProfile().uuid(UUID.randomUUID()).userId("test1"));
        var u1 = new UserRepresentation();
        u1.setMiscAttributes(Map.of("fullname", List.of("Jean Valjean 1"))).setId(userProfile.userId());
        Mockito.when(keycloakMockService.getFrontOfficeUserProfile(ArgumentMatchers.anyString()))
                .thenReturn(Optional.of(u1));
        IntStream.range(0, 15)
                .forEach(index -> txHelper.doInTransaction(() -> {
                    var recruitment = dataGeneratorService.createRecruitment(RecruitmentState.PUBLISHED);
                    var candidature = dataGeneratorService.createCandidature(recruitment, userProfile, index % 2 == 0 ? NEW : null);

                    auditableFieldHelper.updateCreatedDate(candidature, new Date(1_000_000 * index));

                    recruitmentCandidatureRepository.save(candidature);
                }));

        var refusedRecruitment = dataGeneratorService.createRecruitment(RecruitmentState.PUBLISHED);
        var archivedCandidature = dataGeneratorService.createCandidature(refusedRecruitment, userProfile, SELECTED).markAsRefused(CandidatureEmailRefusalState.NONE, "uuid");
        archivedCandidature.setArchived(true);

        mvc.perform(get("/api/odas/user/test1/profileSummary"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.lastCandidatures", hasSize(5)))
                .andExpect(jsonPath("$.lastCandidatures[*].state", containsInAnyOrder("VALIDATED", "WAITING", "VALIDATED", "WAITING", "VALIDATED")))
                .andExpect(jsonPath("$.lastCandidatures[*].id", not(contains(archivedCandidature.getId()))));
    }

    @Test
    @WithMockKeycloakUser(id = "test2", roles = Role.CANDIDATE)
    @ResetDataAfter
    void get_profile_should_return_top_5_experiences() throws Exception {
        var userProfile = userProfileRepository.save(new UserProfile().uuid(UUID.randomUUID()).userId("test2"));
        IntStream.range(0, 15)
                .forEach(index -> dataGeneratorService.createExperience(UserExperience.builder()
                        .type(index >= 3 ? ExperienceType.INTERNSHIP : ExperienceType.JOB)
                        .organizationName("Recruiter" + index)
                        .jobTitle("UX" + index)
                        .uuid(UUID.randomUUID())
                        .durationInMonths(index * 3)
                        .userProfile(userProfile)
                        .build())
                );

        mvc.perform(get("/api/odas/user/test2/profileSummary"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.lastExperiences", hasSize(5)))
                .andExpect(jsonPath("$.lastExperiences[*].jobTitle", contains("UX2", "UX1", "UX0", "UX14", "UX13")));
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = Role.CANDIDATE)
    @ResetDataAfter
    void should_return_contact_info() throws Exception {
        should_return_contact_info_common();

    }

    private void should_return_contact_info_common() throws Exception {
        var source = "https://pole-emploi.fr";
        txHelper.doInTransaction(() -> {
            userProfileRepository.findByUserId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE).orElseThrow().source(source);
        });
        performGetAndExpect("/user/" + KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE + "/contact-info", "userContactInfo", false);
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void should_return_contact_info_for_admin() throws Exception {
        should_return_contact_info_common();
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = Role.CANDIDATE)
    @ResetDataAfter
    void update_contact_info() throws Exception {
        var department = "59";
        var regionName = "Nord";

        var newRadiusInKm = 30;
        var location = GeneralInformationDTOBuilder.buildLocationDTO(Location.builder().city("Dunkerque").departmentCode(department).regionName(regionName).postcode("59140").radiusInKm(newRadiusInKm).build());
        var persisted = updateContactInfoCommon(location, true, null);


        verify(mailingListService).updateLocation(persisted.getUserProfile());
        assertThat(persisted.getLocation().getPostcode()).isEqualTo(location.getPostcode());
        assertThat(persisted.getLocation().getCity()).isEqualTo(location.getCity());
        Optional.ofNullable(persisted.getLocation()).ifPresent(l -> assertThat(l.getRadiusInKm()).isEqualTo(newRadiusInKm));
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = Role.CANDIDATE)
    @ResetDataAfter
    void update_contact_info_with_conflicting_email() throws Exception {
        var duplicatedEmail = "a@a";
        keycloakMockService.createUserInFrontOfficeRealm(new UserKeycloakRepresentation().setEmail(duplicatedEmail));
        var contactInfo = new SaveUserContactInfoCommandDTO().email(duplicatedEmail);
        performPost("/api/odas/user/" + KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE + "/contact-info", contactInfo)
                .andExpect(status().isConflict());
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = Role.CANDIDATE)
    @ResetDataAfter
    void update_contact_info_with_TMP_email_does_not_update_SIB() throws Exception {
        var email = "<EMAIL>";
        keycloakMockService.updateFOEmailAndUsername(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, email, "a", "n");
        var contactInfo = new SaveUserContactInfoCommandDTO().receiveJobOfferEmails(true).email(email);
        performPost("/api/odas/user/" + KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE + "/contact-info", contactInfo)
                .andExpect(status().isNoContent());
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = Role.CANDIDATE)
    @ResetDataAfter
    void update_contact_info_with_email() throws Exception {
        var newEmail = "a@a";
        var contactInfo = new SaveUserContactInfoCommandDTO().email(newEmail);
        performPost("/api/odas/user/" + KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE + "/contact-info", contactInfo)
                .andExpect(status().isNoContent());
        assertThat(keycloakMockService.getFrontOfficeUserProfile(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE).orElseThrow().getEmail()).isEqualTo(newEmail);
        verify(mailingListService).updateContactInfo(any());
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = Role.CANDIDATE)
    @ResetDataAfter
    void update_contact_info_is_tolerant_to_null_location() throws Exception {
        var user = updateContactInfoCommon(null, true, null).getUserProfile();

        verify(mailingListService, never()).updateLocation(user);
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = Role.CANDIDATE)
    @ResetDataAfter
    void update_contact_info_is_tolerant_to_empty_data() throws Exception {
        performPost("/api/odas/user/" + KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE + "/contact-info", new SaveUserContactInfoCommandDTO())
                .andExpect(status().isNoContent());
    }

    private GeneralInformation updateContactInfoCommon(LocationDTO location, boolean isCandidate, String email) throws Exception {
        var setUserNameCommandDTO = new SaveUserNameCommandDTO()
                .firstName("Jean")
                .lastName("Dupont");

        var contactInfo = new SaveUserContactInfoCommandDTO()
                .firstName(setUserNameCommandDTO.getFirstName())
                .lastName(setUserNameCommandDTO.getLastName())
                .contactTime(ContactTimeDTO.EVENING)
                .location(location)
                .birthDate(LocalDate.of(2020, Month.JANUARY, 8))
                .phoneNumber("0123456789")
                .salary(4000)
                .email(email)
                .delayInMonth(42)
                .source("L'APEC - https://apec.example.com")
                .situation(SituationDTO.EMPLOYEE);

        performPost("/api/odas/user/" + KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE + "/contact-info", contactInfo)
                .andExpect(status().isNoContent());

        verify(keycloakMockService, Mockito.times(1)).updateFOEmailAndUsername(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, email, setUserNameCommandDTO.getFirstName(), setUserNameCommandDTO.getLastName());
        AtomicReference<GeneralInformation> persistedRef = new AtomicReference<>();
        txHelper.doInTransaction(() -> {
            var persisted = userProfileRepository.findByUserId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE).map(UserProfile::generalInformation).orElseThrow();
            assertThat(persisted.getContactTime()).hasToString(contactInfo.getContactTime().toString());
            assertThat(persisted.getPhoneNumber()).isEqualTo(contactInfo.getPhoneNumber());
            assertThat(persisted.getBirthDate()).isEqualTo(contactInfo.getBirthDate());
            assertThat(persisted.getSalary()).hasToString(contactInfo.getSalary().toString());
            assertThat(persisted.getSituation()).hasToString(contactInfo.getSituation().toString());
            assertThat(persisted.getSource()).isEqualTo(isCandidate ? null : contactInfo.getSource());
            assertThat(persisted.getDelayInMonth()).isEqualTo(42);
            persistedRef.set(persisted);
        });
        return persistedRef.get();
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = Role.CANDIDATE)
    @ResetDataAfter
    void update_contact_info_without_general_info() throws Exception {
        generalInformationRepository.deleteAll();
        update_contact_info();
    }


    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void update_contact_info_as_admin() throws Exception {
        updateContactInfoCommon(null, false, "<EMAIL>");
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void get_user_profile_detail_with_capacities_should_return_activities_with_capacities_admin() throws Exception {
        getProfileWithCapacitiesCommon()
                .andExpect(jsonPath("$.generalInformation.channels[*]", hasSize(0)))
                .andExpect(jsonPath("$.generalInformation.isPrivate", is(false)));
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void get_user_profile_detail_with_capacities_should_return_activities_with_capacities_and_channels_admin() throws Exception {
        affectUserToChannels(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE);
        getProfileWithCapacitiesCommon()
                .andExpect(jsonPath("$.generalInformation.channels[*]", contains("Organization for channel E-0123", "Organization for channel E-0124")))
                .andExpect(jsonPath("$.generalInformation.isPrivate", is(true)));
    }

    private ResultActions getProfileWithCapacitiesCommon() {
        return performGetAndExpect("/user/" + KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE + "/profileDetailWithCapacities", "userProfileDetailWithCapacities", false);
    }


    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = Role.CANDIDATE)
    @ResetDataAfter
    void get_user_profile_detail_with_capacities_should_return_activities_with_capacities_candidate() {
        affectUserToChannels(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE);
        getProfileWithCapacitiesCommon();
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void get_user_profile() throws Exception {
        var userProfile = userProfileGenerator.createUserProfileWithLevelAndCapacities(new Integer[]{5, 3, 1}, Arrays.asList(CA1_01, CA1_13, CA2_07), Arrays.asList(CA3_12, CA1_13, CA2_08), Arrays.asList(CA3_01, CA1_27, CA2_08));

        mvc.perform(get("/api/odas/user/" + userProfile.userId() + "/level").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", closeTo(3f, 0.01f)));
    }


    @ResetDataAfter
    @Test
    @DisplayName("Given a user with two candidatures when I get user progress as admin then all channels candidatures are included")
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void get_user_profile_progress_admin() throws Exception {

        var userProfile = getUserProfileProgressCommon();
        performGetAndExpect("/api/odas/user/" + userProfile.userId() + "/progress", "user-progress-admin", false);
    }

    private UserProfile getUserProfileProgressCommon() {
        var userId = UUID.randomUUID();
        var recruiter = organizationGenerator.createRecruiter(CHANNEL1);
        var userProfile = userProfileGenerator.createUserProfileWithCapacitiesFromEPAAndExperience(userId, CA1_01, CA2_07);
        txHelper.doInTransaction(() -> {
            userProfileRepository.findById(userProfile.uuid()).orElseThrow().updateBehaviors(Set.of(behaviorGenerator.createBehaviorForCategory(BehaviorCategory.CONSTANCY), behaviorGenerator.createBehaviorForCategory(BehaviorCategory.HONESTY)));
        });
        var capacity = capacityGenerator.createCapacity("CA-5454");
        candidatureGenerator.createCandidature(userProfile, recruitmentGenerator.createRecruitmentWithNoRequirement(recruiter, "J-565", capacity));
        candidatureGenerator.createCandidature(userProfile, recruitmentGenerator.createRecruitmentWithNoRequirement(organizationGenerator.createRecruiter("E-NOPE"), "J-566", capacity));
        userExperienceGenerator.createExperienceWithCapacities(userProfile, 4, capacityGenerator.createCapacity("CA-5455"));
        userExperienceGenerator.createExperienceWithCapacities(userProfile, 2, capacityGenerator.createCapacity("CA-5456"));
        affectUserToChannels(userId.toString());
        return userProfile;
    }

    @ResetDataAfter
    @Test
    @DisplayName("Given a user with two candidatures, one validated, one started when I get user progress as admin or ot then only the validated candidature is included")
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void get_user_profile_progress_candidature_validated() throws Exception {
        var userProfile = getUserProfileProgressValidated();
        performGetAndExpect("/api/odas/user/" + userProfile.userId() + "/progress", "user-progress-validated", false);
    }

    private UserProfile getUserProfileProgressValidated() {
        var userId = UUID.randomUUID();
        var recruiter = organizationGenerator.createRecruiter(CHANNEL1);
        var userProfile = userProfileGenerator.createUserProfileWithCapacitiesFromEPAAndExperience(userId, CA1_01, CA2_07);
        var capacity = capacityGenerator.createCapacity("CA-5454");
        candidatureGenerator.createCandidature(userProfile, recruitmentGenerator.createRecruitmentWithNoRequirement(recruiter, "J-565", capacity));
        candidatureGenerator.createCandidatureNotFinalized(userProfile, recruitmentGenerator.createRecruitmentWithNoRequirement(organizationGenerator.createRecruiter("E-NOPE"), "J-566", capacity));
        affectUserToChannels(userId.toString());
        return userProfile;
    }

    private void affectUserToChannels(String userId) {
        var channels = Set.of(CHANNEL1, CHANNEL2);
        txHelper.doInTransaction(() -> {
            var user = userProfileRepository.findByUserId(userId).orElseThrow();
            user.updatedChannels(channels, UserChannel.ChannelSourceType.LANDING_PAGE);
        });
        keycloakMockService.assignToFrontOfficeGroups(userId, channels);
    }

    @Test
    @WithMockKeycloakUser
    @SneakyThrows
    void setShouldBeContacted() {
        // Legacy API - just to ensure Mobile does not fail
        performPost("/user/should-be-contacted", new DeprecatedCommandForLegacyMobileVersionDTO().shouldBeContacted(true))
                .andExpect(status().isNoContent());
    }

    @ResetDataAfter
    @ParameterizedTest
    @EnumSource(EmailVerificationResultDTO.EmailStatus.class)
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = Role.CANDIDATE)
    void setEmail(EmailVerificationResultDTO.EmailStatus status) throws Exception {
        var previousEmail = "old@old";
        var expectedEmail = "pl@pl";
        var suggestion = "sug@gestion";
        var isInvalid = status == EmailVerificationResultDTO.EmailStatus.INVALID_MAIL || status == EmailVerificationResultDTO.EmailStatus.INVALID_SERVER;
        when(emailVerificationService.verify(expectedEmail)).thenReturn(new EmailVerificationResultDTO().setEmailStatus(status).setSuggestion(suggestion));
        when(keycloakMockService.updateFOEmail(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, expectedEmail)).thenReturn(true);
        when(keycloakMockService.getFrontOfficeUserProfile(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE)).thenReturn(Optional.of(new UserRepresentation().setEmail(previousEmail).setId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE)));
        performPost("/user/%s/email".formatted(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE), new SetUserEmailCommandDTO().email(expectedEmail))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.confirmationRequired", is(isInvalid)))
                .andExpect(jsonPath("$.correctEmailSuggestion", is(suggestion)))
        ;

        verify(keycloakMockService).updateFOEmail(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, expectedEmail);
        verify(emailVerificationService).verify(expectedEmail);
        verify(mailingListService).processFOEmailUpdate(previousEmail, expectedEmail);
        assertThat(userProfileRepository.findByUserId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE).orElseThrow())
                .matches(up -> !up.mailConfiguration().isFirstConfirmationMailSent())
                .extracting(UserProfile::generalInformation)
                .extracting(GeneralInformation::getMailVerificationState)
                .extracting(MailVerification::getState)
                .isEqualTo(switch (status) {
                    case VALID -> MailVerification.MailVerificationState.VERIFIED;
                    case INVALID_MAIL, INVALID_SERVER -> MailVerification.MailVerificationState.REQUIRES_VERIFICATION;
                    case UNKNOWN, VERIFIER_ERROR -> MailVerification.MailVerificationState.UNKNOWN;
                });
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = Role.CANDIDATE)
    void setEmailConflict() throws Exception {
        var expectedEmail = "pl@pl";
        Mockito.doThrow(EntityNotFoundException.class).when(keycloakMockService).updateFOEmail(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, expectedEmail);
        try {
            performPost("/user/%s/email".formatted(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE), new SetUserEmailCommandDTO().email(expectedEmail));
            fail("Should catch EntityNotFoundException");
        } catch (Exception a) {
            assertThat(a).hasCauseInstanceOf(EntityNotFoundException.class);
        }
        verifyNoInteractions(emailVerificationService);
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = Role.CANDIDATE)
    void setEmailForced() throws Exception {
        var expectedEmail = "pl@pl";
        when(keycloakMockService.updateFOEmail(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, expectedEmail)).thenReturn(false);

        performPost("/user/%s/email".formatted(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE), new SetUserEmailCommandDTO().email(expectedEmail))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.confirmationRequired", is(false)))
        ;

        verify(keycloakMockService).updateFOEmail(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, expectedEmail);
        verifyNoInteractions(emailVerificationService);
        assertThat(userProfileRepository.findByUserId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE).orElseThrow())
                .extracting(UserProfile::generalInformation)
                .extracting(GeneralInformation::getMailVerificationState)
                .extracting(MailVerification::getState)
                .isEqualTo(MailVerification.MailVerificationState.FORCED);

    }

    @ResetDataAfter
    @Test
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    void check_user_profile_is_not_complete() {
        var emptyUserProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(NEW_USER_ID)
                .buildAndPersist();
        when(keycloakMockService.getFrontOfficeUserProfile(NEW_USER_ID)).thenReturn(Optional.of(new UserRepresentation().setEmail("a@a")));
        performGetAndExpect("/user/%s/profileSummary".formatted(emptyUserProfile.userId()), "userProfileSummaryNotCompleted", true);
    }

    @ResetDataAfter
    @Test
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    void check_user_profile_is_complete() throws Exception {
        var completedUserProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .buildPartiallyCompletedUserProfile(NEW_USER_ID)
                .withLastConnectionDateLessThan(2)
                .buildAndPersist();
        var u1 = new UserRepresentation().setFirstName("fn").setLastName("ln").setId(completedUserProfile.userId());
        Mockito.when(keycloakMockService.getFrontOfficeUserProfile(ArgumentMatchers.anyString()))
                .thenReturn(Optional.of(u1));

        applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withSubmissionDate(OffsetDateTime.now())
                .withUserProfile(completedUserProfile)
                .buildAndPersist();

        mvc.perform(get(realUrl("/user/%s/profileSummary".formatted(completedUserProfile.userId()))))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.incompleteInformations[*]", hasSize(0)));

    }

    @ResetDataAfter
    @Test
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    void check_user_profile_completion_is_high() throws Exception {
        var completedUserProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .buildPartiallyCompletedUserProfile(NEW_USER_ID)
                .withLastConnectionDateLessThan(2)
                .buildAndPersist();
        var u1 = new UserRepresentation().setFirstName("fn").setLastName("ln").setId(completedUserProfile.userId());
        Mockito.when(keycloakMockService.getFrontOfficeUserProfile(ArgumentMatchers.anyString()))
                .thenReturn(Optional.of(u1));

        applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withSubmissionDate(OffsetDateTime.now())
                .withUserProfile(completedUserProfile)
                .buildAndPersist();

        mvc.perform(get(realUrl("/user/profile-completion")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.value", is(100)));
    }

    @ResetDataAfter
    @Test
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    void check_user_profile_completion_is_low() throws Exception {
        var emptyUserProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(NEW_USER_ID)
                .withEmail("email@localhost")
                .buildAndPersist();
        var u1 = new UserRepresentation().setFirstName("fn").setLastName("ln").setId(emptyUserProfile.userId());
        Mockito.when(keycloakMockService.getFrontOfficeUserProfile(ArgumentMatchers.anyString()))
                .thenReturn(Optional.of(u1));

        mvc.perform(get(realUrl("/user/profile-completion")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.value", is(65)));
    }

    @ResetDataAfter
    @Test
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    void check_user_profile_is_not_active_with_connection_and_candidature() {
        var inactiveUserProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .buildPartiallyCompletedUserProfile(NEW_USER_ID)
                .withLastConnectionDateLessThan(11)
                .withEmail("aa@aa")
                .buildAndPersist();

        applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withSubmissionDate(OffsetDateTime.now().minusMonths(1L).minusDays(5L))
                .withUserProfile(inactiveUserProfile)
                .buildAndPersist();

        performGetAndExpect("/user/%s/profileSummary".formatted(inactiveUserProfile.userId()), "userProfileSummaryInactive", true);

    }

    @Test
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void get_profile_should_return_unread_notification_count() throws Exception {
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class).withJobTitle("Ouvrier").buildAndPersist();
        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(NEW_USER_ID)
                .buildAndPersist();

        var unrelatedUserProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId("other")
                .buildAndPersist();

        applicationContext.getBean(UserNotificationMotherObject.class)
                .withUserProfile(userProfile)
                .withRecruitment(recruitment)
                .withState(NotificationState.NOT_INTERESTED)
                .buildAndPersist();

        applicationContext.getBean(UserNotificationMotherObject.class)
                .withUserProfile(unrelatedUserProfile)
                .withRecruitment(recruitment)
                .withState(NotificationState.NOT_INTERESTED)
                .buildAndPersist();

        applicationContext.getBean(UserNotificationMotherObject.class)
                .withUserProfile(userProfile)
                .withRecruitment(recruitment)
                .buildAndPersist();

        applicationContext.getBean(UserNotificationMotherObject.class)
                .withUserProfile(userProfile)
                .withLink("http://yo.lo/")
                .buildAndPersist();

        mvc.perform(get("/api/odas/user/%s/profileSummary".formatted(userProfile.userId())))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.unreadNotificationsCount", is(2)));
    }

    @Test
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void removeUserBlacklistOccupations_remove_occupation_to_blacklist() {
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .buildAndPersist();

        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(NEW_USER_ID)
                .withExperienceOnOccupation(occupation)
                .withBlacklistedOccupation(occupation)
                .buildAndPersist();

        var commandRemoved = new RemoveUserBlacklistOccupationsCommandDTO()
                .occupationId(occupation.getId())
                .userId(userProfile.userId());

        performPost("/user/remove-blacklisted-occupation", commandRemoved);

        txHelper.doInTransaction(() -> {
            var updatedUserProfile = userProfileRepository.findByUserId(userProfile.userId()).orElseThrow();
            assertThat(updatedUserProfile.blacklistedOccupations()).isEmpty();
        });
    }

    @Test
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void setUserBlacklistOccupations_add_new_occupation_to_blacklist() {
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .buildAndPersist();

        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(NEW_USER_ID)
                .withExperienceOnOccupation(occupation)
                .buildAndPersist();

        var command = new SetUserBlacklistOccupationsCommandDTO()
                .occupationId(occupation.getId().toString())
                .userId(userProfile.userId());

        performPost("/user/blacklisted-occupations", command);

        txHelper.doInTransaction(() -> {
            var updatedUserProfile = userProfileRepository.findByUserId(userProfile.userId()).orElseThrow();
            assertThat(updatedUserProfile.blacklistedOccupations()).contains(occupation);
        });
    }

    @Test
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void setUserBlacklistOccupations_add_already_blacklisted_occupation_to_blacklist() {
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .buildAndPersist();

        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(NEW_USER_ID)
                .withExperienceOnOccupation(occupation)
                .withBlacklistedOccupation(occupation)
                .buildAndPersist();

        assertThat(userProfile.blacklistedOccupations()).hasSize(1);

        var command = new SetUserBlacklistOccupationsCommandDTO()
                .occupationId(occupation.getId().toString())
                .userId(userProfile.userId());

        performPost("/user/blacklisted-occupations", command);

        txHelper.doInTransaction(() -> {
            var updatedUserProfile = userProfileRepository.findByUserId(userProfile.userId()).orElseThrow();
            assertThat(updatedUserProfile.blacklistedOccupations()).contains(occupation);
            assertThat(updatedUserProfile.blacklistedOccupations()).hasSize(1);
        });

    }
}
