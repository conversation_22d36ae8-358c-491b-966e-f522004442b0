package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.OpenAIGenerationMockUtils;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.ErhgoOccupationGenerator;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.UserExperienceRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.generation.FindBestMatchingOccupationService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.FindBestMatchingOccupationArguments;
import com.erhgo.services.mailing.MailingListService;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.notifier.messages.ExperienceOnUnqualifiedOccupationMessageDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.auditing.AuditingHandler;
import org.springframework.data.auditing.DateTimeProvider;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.ResultActions;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.erhgo.generators.TestFixtures.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class UserExperienceControllerTest extends AbstractIntegrationTestWithFixtures {
    @Autowired
    private RecruitmentCandidatureRepository recruitmentCandidatureRepository;

    @Autowired
    private UserExperienceRepository userExperienceRepository;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private ErhgoOccupationGenerator erhgoOccupationGenerator;

    @MockitoBean
    GenerationClient generationClient;

    @MockitoBean
    private Notifier notifier;
    private UUID experienceUUID;

    private static final UUID OCCUPATION_ID = UUID.randomUUID();

    private static final String FAT_STRING = IntStream.range(0, 256).mapToObj(a -> "!").collect(Collectors.joining(""));

    @Autowired
    private AuditingHandler auditingHandler;

    @Mock
    DateTimeProvider dateTimeProvider;

    Capacity ca1_1, ca1_2, ca2, capacityWithInducedCapacities;

    @Autowired
    private CapacityGenerator capacityGenerator;
    @Autowired
    private ApplicationContext applicationContext;
    @MockitoBean
    MailingListService mailingListService;

    @MockitoBean
    FindBestMatchingOccupationService findBestMatchingOccupationService;

    @BeforeEach
    void resetTimeProvider() {
        Mockito.when(dateTimeProvider.getNow()).thenReturn(Optional.of(LocalDateTime.of(2020, 2, 2, 0, 0, 0)));
        auditingHandler.setDateTimeProvider(dateTimeProvider);

        OpenAIGenerationMockUtils.prepare(generationClient);
    }

    private void prepareCapacities() {
        ca1_1 = capacityGenerator.createCapacity("CA1-99");
        ca1_2 = capacityGenerator.createCapacity("CA1-98");
        ca2 = capacityGenerator.createCapacity("CA2-99", ca1_1);
        capacityWithInducedCapacities = capacityGenerator.createCapacity("CA3-99", ca2, ca1_1, ca1_2);
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_EMPTY_EXPERIENCE, roles = {Role.CANDIDATE})
    void experienceCreationUsingExperienceTypeShouldReturnNoContent() throws Exception {
        doCreateExperience();
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_EMPTY_EXPERIENCE, roles = {Role.CANDIDATE})
    @ResetDataAfter
    void experienceCreationWithErhgoOccupationShouldSucceedAndUseCorrectScore() throws Exception {
        var erhgoOccupation = ERHGO_OCCUPATION_QUALIFIED;
        doCreateExperienceWithErhgoId(erhgoOccupation.getId());
        makeCandidatureMatch(CANDIDATURE_NOT_MATCHING_FOR_USER_WITH_EMPTY_EXPERIENCE);
        txHelper.doInTransaction(() -> {
            var candidature = recruitmentCandidatureRepository.findById(CANDIDATURE_NOT_MATCHING_FOR_USER_WITH_EMPTY_EXPERIENCE.getId()).orElseThrow();
            assertThat(candidature.getValid()).isTrue();
        });
        // Not a sourcing user => no notif
        verifyNoInteractions(notifier);
    }


    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = {Role.CANDIDATE})
    @ResetDataAfter
    void addingExperience_does_not_reset_global_state() throws Exception {
        var candidatureId = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(USER_PROFILE_WITH_MATCHING_CANDIDATURE)
                .withState(GlobalCandidatureState.REFUSED_ON_CALL)
                .withValid(true)
                .buildAndPersist()
                .getId();
        var erhgoOccupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).withCapacities(CA3_04, CA1_01, CA2_04).buildAndPersist();

        prepareExperience(erhgoOccupation.getId(), KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE).andExpect(status().isOk());
        txHelper.doInTransaction(() -> {
            var candidature = recruitmentCandidatureRepository.findById(candidatureId).orElseThrow();
            assertThat(candidature.getValid()).isTrue();
            assertThat(candidature.getGlobalCandidatureState()).isEqualTo(GlobalCandidatureState.REFUSED_ON_CALL);
        });
    }

    private final String NEW_USER_ID = "f368fcf5-502d-4c2d-80e8-6aa1bb2c6dab";

    @Test
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = {Role.CANDIDATE})
    @ResetDataAfter
    void removingExperience_does_not_reset_global_state() throws Exception {
        var erhgoOccupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).withCapacities(CA3_04, CA1_01, CA2_04).buildAndPersist();
        var userProfile = applicationContext.getBean(UserProfileMotherObject.class).withUuid(UUID.fromString(NEW_USER_ID)).withExperienceOnOccupation(erhgoOccupation).buildAndPersist();

        var candidatureId = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userProfile)
                .withState(GlobalCandidatureState.REFUSED_ON_CALL)
                .withValid(true)
                .buildAndPersist()
                .getId();

        doDeleteExperience(userProfile.experiences().stream().findFirst().orElseThrow().getUuid());
        verify(mailingListService).updateNumberOfExperiences(userProfile);
        txHelper.doInTransaction(() -> {
            var candidature = recruitmentCandidatureRepository.findById(candidatureId).orElseThrow();
            assertThat(candidature.getValid()).isTrue();
            assertThat(candidature.getGlobalCandidatureState()).isEqualTo(GlobalCandidatureState.REFUSED_ON_CALL);
        });
    }

    private void doDeleteExperience(UUID experienceUUID) throws Exception {
        mvc.perform(delete("/api/odas/userExperience/" + experienceUUID)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
    }


    private void makeCandidatureMatch(RecruitmentCandidature candidature) {
        updateExperienceWithActivitiesOfJobWithLevel(candidature, ERHGO_OCCUPATION_QUALIFIED.getLevel(), experienceUUID);
        candidature.getRecruitmentProfile().getMandatoryContexts().forEach(this::setContextAsMet);
        doPublishCandidature(candidature);
    }

    private void updateExperienceWithActivitiesOfJobWithLevel(RecruitmentCandidature candidature, MasteryLevel level, UUID experienceUUID) {
        var allActivities = candidature.getRecruitmentProfile().getJob().getAllMissionsActivities();
        txHelper.doInTransaction(() -> {
            var occupation = erhgoOccupationGenerator.createErhgoOccupation(new ErhgoOccupationMotherObject()
                    .withOptionalActivities(allActivities.toArray(JobActivityLabel[]::new))
                    // same level as the one used in experienceCreationWithErhgoOccupationShouldSucceedAndUseCorrectScore
                    .withLevel(level)
                    .instance());
            userExperienceRepository.getReferenceById(experienceUUID).setErhgoOccupation(occupation);
        });
    }

    @SneakyThrows
    private void doPublishCandidature(RecruitmentCandidature candidatureMatching) {
        mvc.perform(post("/api/odas/candidature/publish")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"candidatureId\": " + candidatureMatching.getId() + "}"))
                .andExpect(status().isNoContent())
                .andReturn();
    }

    @SneakyThrows
    private void setContextAsMet(Context c) {
        var contextsMet = Collections.singletonList(
                (new ContextMetDTO()).contextId(c.getId()).experiencesIds(Collections.singletonList(experienceUUID)).frequency(FrequencyDTO.HIGH)
        );

        mvc.perform(post("/api/odas/candidature/" + CANDIDATURE_NOT_MATCHING_FOR_USER_WITH_EMPTY_EXPERIENCE.getId() + "/contextsMet")
                        .content(objectMapper.writeValueAsBytes(contextsMet))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_EMPTY_EXPERIENCE, roles = {Role.CANDIDATE})
    void experienceCreationWithInvalidErhgoOccupationShouldFail() throws Exception {
        prepareExperience(UUID.randomUUID(), KEYCLOAK_USER_ID_WITH_EMPTY_EXPERIENCE).andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_EMPTY_EXPERIENCE, roles = {Role.CANDIDATE})
    void experienceCreation_with_too_long_title_should_fail() throws Exception {

        String content = createSaveExperienceCommand(FAT_STRING, "en");

        mvc.perform(post("/api/odas/userExperience/saveExperience")
                        .content(content)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_EMPTY_EXPERIENCE, roles = {Role.CANDIDATE})
    void saveExperience_check_that_SIB_attribute_is_updated() throws Exception {
        prepareExperience().andExpect(status().isOk());
        txHelper.doInTransaction(() -> {
            var user = userProfileRepository.findByUserId(KEYCLOAK_USER_ID_WITH_EMPTY_EXPERIENCE).orElseThrow();
            verify(mailingListService).updateNumberOfExperiences(user);
        });
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_EMPTY_EXPERIENCE, roles = {Role.CANDIDATE})
    void experienceCreation_with_too_long_organization_should_fail() throws Exception {

        String content = createSaveExperienceCommand("yo", FAT_STRING);

        mvc.perform(post("/api/odas/userExperience/saveExperience")
                        .content(content)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    private String createSaveExperienceCommand(String jobTitle, String organizationName) throws JsonProcessingException {
        return objectMapper.writeValueAsString(new SaveExperienceCommandDTO()
                .organizationName(organizationName)
                .jobTitle(jobTitle)
                .experienceType(ExperienceTypeDTO.JOB)
                .userId(KEYCLOAK_USER_ID_WITH_EMPTY_EXPERIENCE)
                .durationInMonths(3));
    }

    private void doCreateExperienceWithErhgoId(UUID id) throws Exception {
        prepareExperience(id, KEYCLOAK_USER_ID_WITH_EMPTY_EXPERIENCE).andExpect(status().isOk()).andReturn();
        var userExperience = userExperienceRepository.findById(experienceUUID).orElseThrow();
        assertThat(userExperience.getOccupationId()).isEqualTo(id);
    }

    private void doCreateExperience() throws Exception {
        prepareExperience().andExpect(status().isOk()).andReturn();
        var userExperience = userExperienceRepository.findById(experienceUUID).orElseThrow();
        assertThat(userExperience.getUserProfile().userId()).isEqualTo(KEYCLOAK_USER_ID_WITH_EMPTY_EXPERIENCE);
    }

    private ResultActions prepareExperience() throws Exception {
        return prepareExperience(null, KEYCLOAK_USER_ID_WITH_EMPTY_EXPERIENCE);
    }

    private ResultActions prepareExperience(UUID erhgoOccupationId, String userId) throws Exception {
        when(findBestMatchingOccupationService.findSimilarLabel(any(FindBestMatchingOccupationArguments.class)))
                .thenReturn(new ErhgoOccupationMinimumInfoDTO());

        experienceUUID = UUID.randomUUID();

        var content = "{" +
                "\"experienceId\": \"" + experienceUUID + "\", " +
                "\"experienceType\": \"JOB\", " +
                "\"jobTitle\": \"Title of job\"," +
                "\"userId\": \"" + userId + "\"," +
                "\"organizationName\": \"ACME\"" +
                (erhgoOccupationId == null ? "" : ",\"erhgoOccupationId\":\"" + erhgoOccupationId + "\"") +
                "}";

        // @formatter:off
        return mvc.perform(post("/api/odas/userExperience/saveExperience")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON));
    }

    @Test
    @WithMockKeycloakUser(id = "wrong user", roles = {"ODAS_NOT_ADMIN"})
    void addExperienceToAnotherUserShouldFail() throws Exception {
        prepareExperience().andExpect(status().isForbidden());
    }


    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_EMPTY_EXPERIENCE, roles = {Role.CANDIDATE})
    void deleteEmptyExperienceShouldSucceed() throws Exception {
        doCreateExperience();

        doDeleteExperience(experienceUUID);

        assertThat(userExperienceRepository.findById(experienceUUID)).isNotPresent();

    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = {Role.CANDIDATE})
    @ResetDataAfter
    void deleteExperienceShouldResetValidForCandidatureFlag() throws Exception {
        doDeleteExperience(EXPERIENCE_MATCHING_CANDIDATURE.getUuid());

        txHelper.doInTransaction(() -> {
            assertThat(recruitmentCandidatureRepository.findById(CANDIDATURE_MATCHING.getId()).orElseThrow().getValid()).isNotEqualTo(true);
        });
    }

    @ParameterizedTest
    @CsvSource({"true, true", "true, false", "false, true", "false, false"})
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITHOUT_EXPERIENCE, roles = {Role.CANDIDATE})
    @ResetDataAfter
    @SneakyThrows
    void addExperience_adds_capacity_occurrences_and_notify_when_not_qualified(boolean qualified, boolean repeat) {
        createCandidatureOnSourcingOrgaToEnsureNotificationAreSent();
        prepareCapacities();

        erhgoOccupationGenerator.createErhgoOccupation(
                new ErhgoOccupationMotherObject()
                        .withId(OCCUPATION_ID)
                        .qualified(qualified)
                        .withCapacities(capacityWithInducedCapacities, ca2)
                        .instance()
        );
        var o2 = erhgoOccupationGenerator.createErhgoOccupation(
                new ErhgoOccupationMotherObject()
                        .qualified(qualified)
                        .withCapacities(capacityWithInducedCapacities, ca2)
                        .instance()
        );
        experienceUUID = UUID.randomUUID();

        if (repeat) {
            performPost("/userExperience/saveExperience", new SaveExperienceCommandDTO()
                    .organizationName("a")
                    .experienceId(experienceUUID)
                    .jobTitle("b")
                    .experienceType(ExperienceTypeDTO.JOB)
                    .userId(KEYCLOAK_USER_ID_WITHOUT_EXPERIENCE)
                    .erhgoOccupationId(o2.getId())
                    .durationInMonths(3)).andExpect(status().isOk());
        }
        // retourne une experience après la création d'ou le contenu n'est pas vide
        performPost("/userExperience/saveExperience", getSaveExperienceCommandDTO())
                .andExpect(status().isOk());

        verifyCapacityOccurrences();
        verify(notifier, times(qualified ? 0 : repeat ? 2 : 1)).sendMessage(any(ExperienceOnUnqualifiedOccupationMessageDTO.class));
    }

    private void createCandidatureOnSourcingOrgaToEnsureNotificationAreSent() {
        applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userProfileRepository.findByUserId(KEYCLOAK_USER_ID_WITHOUT_EXPERIENCE).orElseThrow())
                .withOrganizationOfType(AbstractOrganization.OrganizationType.SOURCING)
                .buildAndPersist();
    }

    private void verifyCapacityOccurrences() {
        txHelper.doInTransaction(() -> {
            var occurrences = userProfileRepository.findByUserId(KEYCLOAK_USER_ID_WITHOUT_EXPERIENCE).orElseThrow().capacityOccurrences();
            assertThat(occurrences).hasSize(4);
            assertThat(occurrences)
                    .anyMatch(c -> c.getOccurrence() == 0 && c.getRecursiveOccurrence() == 3 && c.getCapacity().getCode().equals(ca1_1.getCode()))
                    .anyMatch(c -> c.getOccurrence() == 0 && c.getRecursiveOccurrence() == 1 && c.getCapacity().getCode().equals(ca1_2.getCode()))
                    .anyMatch(c -> c.getOccurrence() == 1 && c.getRecursiveOccurrence() == 1 && c.getCapacity().getCode().equals(ca2.getCode()))
                    .anyMatch(c -> c.getOccurrence() == 1 && c.getRecursiveOccurrence() == 0 && c.getCapacity().getCode().equals(capacityWithInducedCapacities.getCode()));
        });
    }

    private SaveExperienceCommandDTO getSaveExperienceCommandDTO() {
        return new SaveExperienceCommandDTO()
                .organizationName("a")
                .experienceId(experienceUUID)
                .jobTitle("b")
                .experienceType(ExperienceTypeDTO.JOB)
                .userId(KEYCLOAK_USER_ID_WITHOUT_EXPERIENCE)
                .erhgoOccupationId(OCCUPATION_ID)
                .durationInMonths(3)
                ;

    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITHOUT_EXPERIENCE, roles = {Role.CANDIDATE})
    @ResetDataAfter
    @SneakyThrows
    void removeExperience_removes_capacity_occurrences() {
        addExperience_adds_capacity_occurrences_and_notify_when_not_qualified(true, false);
        doDeleteExperience(experienceUUID);
        verifyNoCapacity();
    }

    private void verifyNoCapacity() {
        txHelper.doInTransaction(() -> {
            var occurrences = userProfileRepository.findByUserId(KEYCLOAK_USER_ID_WITHOUT_EXPERIENCE).orElseThrow().capacityOccurrences();
            assertThat(occurrences).isEmpty();
        });
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITHOUT_EXPERIENCE, roles = {Role.CANDIDATE})
    @ResetDataAfter
    @SneakyThrows
    void updateExperience_does_not_re_add_capacities(boolean qualified) {
        addExperience_adds_capacity_occurrences_and_notify_when_not_qualified(qualified, false);
        Mockito.reset(notifier);
        performPost("/userExperience/saveExperience", getSaveExperienceCommandDTO()).andExpect(status().isOk());
        verifyCapacityOccurrences();
        verifyNoInteractions(notifier);
    }

    @ParameterizedTest
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITHOUT_EXPERIENCE, roles = {Role.CANDIDATE})
    @ValueSource(booleans = {true, false})
    @ResetDataAfter
    @SneakyThrows
    void updateExperienceOccupation_updates_capacities(boolean qualified) {
        addExperience_adds_capacity_occurrences_and_notify_when_not_qualified(true, false);
        Mockito.reset(notifier);
        performPost("/userExperience/saveExperience", getSaveExperienceCommandDTO().erhgoOccupationId(erhgoOccupationGenerator.createErhgoOccupation(new ErhgoOccupationMotherObject().qualified(qualified).instance()).getId())).andExpect(status().isOk());
        verifyNoCapacity();
        verify(notifier, qualified ? never() : times(1)).sendMessage(any(ExperienceOnUnqualifiedOccupationMessageDTO.class));
    }

}
