package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ApiConstants;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.repositories.CapacityRepository;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;

import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.iterableWithSize;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

class CapacityControllerTest extends AbstractIntegrationTest {
    @Autowired
    private CapacityGenerator capacityGenerator;

    @Autowired
    private CapacityRepository capacityRepository;

    private static final String CODE = "CA1-01", TITLE = "Regarder avec attention";

    @BeforeEach
    public void setUp() {
        capacityGenerator.createCapacity(CODE, TITLE, "");
    }

    @AfterEach
    public void tearDown() {
        capacityRepository.deleteAll();
    }

    @Test
    @WithMockKeycloakUser
    void shouldCountCapacities() throws Exception {
        // @formatter:off
        mvc.perform(get(ApiConstants.API_ODAS_CAPACITY + ApiConstants.SEPARATOR + "/count").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string("1"));
        // @formatter:on
    }

    @Test
    @WithMockKeycloakUser
    void shouldFindAnExistingCapacityByCode() throws Exception {
        // @formatter:off
        mvc.perform(get(ApiConstants.API_ODAS_CAPACITY + ApiConstants.SEPARATOR +
                        "list?page=0&size=10&by=code&direction=ASC&filter=CA1-01").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("application/hal+json;charset=UTF-8"))
                .andExpect(jsonPath("$.content[0].code", is(CODE)))
                .andExpect(jsonPath("$.content[0].title", is(TITLE)))
                .andExpect(jsonPath("$.content[0].description", is("")))
                ;
        // @formatter:on
    }

    @Test
    @WithMockKeycloakUser
    void shouldFindAnExistingCapacityByTitle() throws Exception {
        // @formatter:off
        mvc.perform(get(ApiConstants.API_ODAS_CAPACITY + ApiConstants.SEPARATOR +
                        "list?page=0&size=10&by=title&direction=ASC&filter=Regarder").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("application/hal+json;charset=UTF-8"))
                .andExpect(jsonPath("$.content[0].code", is(CODE)))
                .andExpect(jsonPath("$.content[0].title", is(TITLE)))
                .andExpect(jsonPath("$.content[0].description", is("")))
        ;
        // @formatter:on
    }

    @Test
    @WithMockKeycloakUser
    void shouldNotFindAnUnExistingCapacityByCodeLike() throws Exception {
        // @formatter:off
        mvc.perform(get(ApiConstants.API_ODAS_CAPACITY + ApiConstants.SEPARATOR +
                        "list?page=0&size=10&by=code&direction=ASC&filter=XX").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", iterableWithSize(0)));
        // @formatter:on
    }

    @Test
    @WithMockKeycloakUser
    void shouldNotFindAnUnExistingCapacityByTitleLike() throws Exception {
        // @formatter:off
        mvc.perform(get(ApiConstants.API_ODAS_CAPACITY + ApiConstants.SEPARATOR +
                        "list?page=0&size=10&by=title&direction=ASC&filter=XX").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", iterableWithSize(0)));
        // @formatter:on
    }

}
