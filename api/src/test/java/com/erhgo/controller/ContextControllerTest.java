package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.TestUtils;
import com.erhgo.TransactionTestHelper;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.referential.CategoryLevel;
import com.erhgo.domain.referential.Origin;
import com.erhgo.generators.JobGenerator;
import com.erhgo.generators.TestFixtures;
import com.erhgo.openapi.dto.ReferentialElementOriginDTO;
import com.erhgo.openapi.dto.SaveContextCommandDTO;
import com.erhgo.repositories.JobRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jayway.jsonpath.JsonPath;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.auditing.AuditingHandler;
import org.springframework.data.auditing.DateTimeProvider;
import org.springframework.http.MediaType;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

import static com.erhgo.generators.TestFixtures.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class ContextControllerTest extends AbstractIntegrationTestWithFixtures {

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private TransactionTestHelper transactionTestHelper;

    @Autowired
    private JobGenerator jobGenerator;

    @Autowired
    private AuditingHandler auditingHandler;

    @Mock
    public DateTimeProvider dateTimeProvider;

    @Before
    public void resetTimeProvider() {
        Mockito.when(dateTimeProvider.getNow()).thenReturn(Optional.of(LocalDateTime.of(2020, 2, 2, 0, 0, 0)));
        auditingHandler.setDateTimeProvider(dateTimeProvider);
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void shouldSaveAndUpdateAValidContext() throws Exception {
        final var categoryLevel = categoryLevelGenerales;

        final var contextId = this.shouldSaveAValidNewContext(categoryLevel);

        this.shouldUpdateAnExistingContext(contextId, categoryLevel);
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void updateContextLevelAndCategoryShouldUpdateARelatedJobAndNotOtherJobs() throws Exception {

        var newJob = jobGenerator.createJobWithMission("J-424", E_01_CERA, CT_26, jobActivityLabelUsedOnlyInJobMission);

        var initialLevel = CONTEXT_USED_IN_JOB_WITH_QUESTION.getCategoryLevel();
        verifyContextContains(initialLevel);
        // Change first job related context's category
        var content = buildUpdateContextCommand(categoryLevelGenerales.getId(), true);
        mvc.perform(post("/api/odas/context/" + CONTEXT_USED_IN_JOB_WITH_QUESTION.getId() + "/update")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        verifyContextContains(categoryLevelGenerales);
        ensureJobHasNoContextForCategories(newJob, initialLevel, categoryLevelGenerales);

        // Change second job related context's category
        content = buildUpdateContextCommand(proGesturesAndAssociatedMaterialCategory.getLevels().last().getId(), true);
        mvc.perform(post("/api/odas/context/" + CONTEXT_USED_IN_JOB_OF_FORMATION.getId() + "/update")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        transactionTestHelper.doInTransaction(() -> {
            var job = jobRepository.findById(TestFixtures.J_01.getId()).orElseThrow();
            var contextsForCategory = job.getMissions().stream().flatMap(m -> m.getContextsForCategory().stream()).filter(c -> c.getCategory().getCode().equals(initialLevel.getCategory().getCode())).findFirst().orElseThrow();
            assertThat(contextsForCategory.getNoContextForCategory()).isTrue();
            ensureJobHasNoContextForCategories(newJob, initialLevel, categoryLevelGenerales);
        });

        // restore first job related context's category
        content = buildUpdateContextCommand(initialLevel.getId(), true);
        mvc.perform(post("/api/odas/context/" + CONTEXT_USED_IN_JOB_WITH_QUESTION.getId() + "/update")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        verifyContextContains(initialLevel);
        ensureJobHasNoContextForCategories(newJob, initialLevel, categoryLevelGenerales);
    }

    private void ensureJobHasNoContextForCategories(Job newJob, CategoryLevel... levels) {
        transactionTestHelper.doInTransaction(() -> {
            var otherJob = jobRepository.findById(newJob.getId()).orElseThrow();
            // Ensure other jobs' contexts have not been modified
            assertThat(otherJob.getMissions()
                    .stream()
                    .flatMap(m -> m.getContextsForCategory().stream())
                    .filter(c -> Stream.of(levels).anyMatch(lc -> lc.getCategory().getCode().equals(c.getCategory().getCode()))))
                    .allMatch(cfc -> cfc.getNoContextForCategory() && cfc.getContexts().isEmpty());
        });
    }

    private void verifyContextContains(CategoryLevel level) {
        transactionTestHelper.doInTransaction(() -> {
            var job = jobRepository.findById(TestFixtures.J_01.getId()).orElseThrow();
            var contextsForCategory = job.getMissions().stream()
                    .flatMap(m -> m.getContextsForCategory().stream())
                    .filter(c -> c.getContexts().stream().anyMatch(context -> context.getId().equals(CONTEXT_USED_IN_JOB_WITH_QUESTION.getId())))
                    .findFirst()
                    .orElseThrow();
            assertThat(contextsForCategory.getCategory()).isEqualTo(level.getCategory());
            assertThat(contextsForCategory.getNoContextForCategory()).isFalse();
        });
    }

    @Test
    @WithMockKeycloakUser
    public void shouldNotSaveContextAsNormalUser() throws Exception {

        final var content = buildUpdateContextCommand(categoryLevelGenerales.getId(), false);

        mvc.perform(post("/api/odas/context/create")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isForbidden());
    }

    private UUID shouldSaveAValidNewContext(CategoryLevel categoryLevel) throws Exception {

        final String content = buildUpdateContextCommand(categoryLevel.getId(), false);

        final var creationResult = mvc.perform(post("/api/odas/context/create")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("application/json;charset=UTF-8"))
                .andExpect(jsonPath("$.id", notNullValue()))
                .andExpect(jsonPath("$.title", is("test context")))
                .andExpect(jsonPath("$.description", is("test context description\nmulti-line")))
                .andExpect(jsonPath("$.origin", is(Origin.ESCO.name())))
                .andExpect(jsonPath("$.categoryLevel.id", is(categoryLevel.getId().intValue())))
                .andExpect(jsonPath("$.categoryLevel.title", is("Générales")))
                .andExpect(jsonPath("$.categoryLevel.description", is("Sans spécialisation, communes à tous dans la vie quotidienne")))
                .andExpect(jsonPath("$.categoryLevel.category.id", is(categoryLevel.getCategory().getId().intValue())))
                .andExpect(jsonPath("$.categoryLevel.category.title", is("Connaissances")))
                .andExpect(jsonPath("$.categoryLevel.category.description", is("Permet de décrire ce qu’il faut savoir et comprendre pour réaliser le travail. Les connaissances s'acquièrent par l’étude, la pratique ou les recherches.")))
                .andReturn();

        final var contextId = JsonPath.parse(creationResult.getResponse().getContentAsString()).read(JsonPath.compile("$.id"));

        mvc.perform(get("/api/odas/context/" + contextId.toString()))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("application/json;charset=UTF-8"))
                .andExpect(content().string(creationResult.getResponse().getContentAsString()));

        return UUID.fromString(contextId.toString());
    }

    private String buildUpdateContextCommand(Long levelId, boolean update) throws IOException {
        return new ObjectMapper()
                .writeValueAsString(new SaveContextCommandDTO()
                        .categoryLevelId(levelId)
                        .origin(ReferentialElementOriginDTO.ESCO)
                        .description("test context description\nmulti-line" + (update ? " updaté" : ""))
                        .title("test context" + (update ? " updaté" : "")));
    }

    private void shouldUpdateAnExistingContext(UUID contextId, CategoryLevel categoryLevel) throws Exception {
        final var content = buildUpdateContextCommand(categoryLevel.getId(), true);

        final var creationResult = mvc.perform(post("/api/odas/context/" + contextId.toString() + "/update")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("application/json;charset=UTF-8"))
                .andExpect(jsonPath("$.id", is(contextId.toString())))
                .andExpect(jsonPath("$.title", is("test context updaté")))
                .andExpect(jsonPath("$.description", is("test context description\nmulti-line updaté")))
                .andExpect(jsonPath("$.categoryLevel.id", is(categoryLevel.getId().intValue())))
                .andExpect(jsonPath("$.categoryLevel.title", is("Générales")))
                .andExpect(jsonPath("$.categoryLevel.description", is("Sans spécialisation, communes à tous dans la vie quotidienne")))
                .andExpect(jsonPath("$.categoryLevel.category.id", is(categoryLevel.getCategory().getId().intValue())))
                .andExpect(jsonPath("$.categoryLevel.category.title", is("Connaissances")))
                .andExpect(jsonPath("$.categoryLevel.category.description", is("Permet de décrire ce qu’il faut savoir et comprendre pour réaliser le travail. Les connaissances s'acquièrent par l’étude, la pratique ou les recherches.")))
                .andReturn();

        mvc.perform(get("/api/odas/context/" + contextId.toString()))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("application/json;charset=UTF-8"))
                .andExpect(content().string(creationResult.getResponse().getContentAsString()));
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void creatingWithABadCategoryLevelIdShouldReturnAnError() throws Exception {
        final String content = buildUpdateContextCommand(-404L, false);

        mvc.perform(post("/api/odas/context/create")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentTypeCompatibleWith("application/json;charset=UTF-8"));
    }

    @Test
    @WithMockKeycloakUser
    public void gettingABadContextIdShouldReturnAnError() throws Exception {
        mvc.perform(get("/api/odas/context/" + UUID.randomUUID()))
                .andExpect(status().isNotFound())
                .andExpect(content().contentTypeCompatibleWith("application/json;charset=UTF-8"));
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void updatingABadContextIdShouldReturnAnError() throws Exception {
        final String content = buildUpdateContextCommand(-404L, false);

        mvc.perform(post("/api/odas/context/" + UUID.randomUUID() + "/update").content(content).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(content().contentTypeCompatibleWith("application/json;charset=UTF-8"));
    }

    @Test
    @WithMockKeycloakUser
    public void findByTitleWithUnknownQueryShouldRetrieveEmptyResult() throws Exception {
        mvc.perform(get("/api/odas/context/search?query=ilSAgitDUnContexteInconnuDuSysteme!&categoryCode=CCT-03"))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("application/json;charset=UTF-8"))
                .andExpect(jsonPath("$[*]", hasSize(0)));
    }

    @Test
    @WithMockKeycloakUser
    public void findByTitleWithEmptyQueryShouldRetrieveAllContexts() throws Exception {
        mvc.perform(get("/api/odas/context/search?query=&categoryCode=CCT-03"))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("application/json;charset=UTF-8"))
                .andExpect(jsonPath("$[*]", hasSize(7)));
    }

    @Test
    @WithMockKeycloakUser
    public void findByTitleWithUnknownQueryShouldIgnoreCase() throws Exception {
        mvc.perform(get("/api/odas/context/search?query=vO&categoryCode=CCT-03"))
                .andExpect(status().isOk())
                .andExpect(content().json(TestUtils.toString(getClass().getResourceAsStream("/expected/contextSearch.json")), false));
    }

    @Test
    @WithMockKeycloakUser
    public void findPageByTitleShouldIgnoreCase() throws Exception {
        mvc.perform(get("/api/odas/context/list?page=0&size=100&by=title&direction=ASC&filter=" + CT_02.getTitle().toLowerCase()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", hasSize(1)))
                .andExpect(jsonPath("$.content[0].code", is(CT_02.getCode())));
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    public void findPageByAdminId() throws Exception {

        final var content = buildUpdateContextCommand(categoryLevelGenerales.getId(), false);

        var creationResult = mvc.perform(post("/api/odas/context/create")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();

        final var contextId = JsonPath.parse(creationResult.getResponse().getContentAsString()).read(JsonPath.compile("$.id"));

        mvc.perform(get("/api/odas/context/list?page=0&size=100&by=title&direction=ASC&userFilter=" + ADMIN_USER_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", hasSize(1)))
                .andExpect(jsonPath("$.content[0].id", is(contextId)));;
    }

}
