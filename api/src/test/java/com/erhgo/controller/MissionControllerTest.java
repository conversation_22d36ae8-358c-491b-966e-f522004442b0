package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.job.ContextsForCategory;
import com.erhgo.generators.TestFixtures;
import com.erhgo.openapi.dto.ContextsForCategoryDTO;
import com.erhgo.openapi.dto.CreateMissionCommandDTO;
import com.erhgo.openapi.dto.UpdateMissionCommandDTO;
import com.erhgo.repositories.JobMissionRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.UUID;

import static com.erhgo.config.ApiConstants.API_ODAS_MISSION;
import static com.erhgo.config.ApiConstants.SEPARATOR;
import static com.erhgo.generators.TestFixtures.*;
import static com.google.common.collect.Lists.newArrayList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.*;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@Slf4j
public class MissionControllerTest extends AbstractIntegrationTestWithFixtures {

    private String TITLE = "newMissionTitle";

    @Autowired
    private JobMissionRepository repository;

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    @ResetDataAfter
    public void shouldSaveMission() throws Exception {

        var request = new CreateMissionCommandDTO();
        UUID idAct1 = ACT_01.getUuid(), idAct6 = jobActivityLabelUsedInCandidatureExperienceAndJobMission.getUuid();
        request.setJobId(JOB_MODIFIABLE.getId());
        request.setActivitiesIds(newArrayList(idAct1, idAct6));
        request.setContextsForCategory(newArrayList(getContextsForCategoryDTO()));

        request.setTitle(TITLE);

        mvc.perform(post(API_ODAS_MISSION + SEPARATOR + "create")
                        .content(objectMapper.writeValueAsBytes(request))
                        .contentType(APPLICATION_JSON))
                .andExpect(status().isCreated())
                .andExpect(content().contentTypeCompatibleWith(APPLICATION_JSON))
                .andExpect(jsonPath("$.title", is(TITLE)))
                .andExpect(jsonPath("$.activitiesIds", containsInAnyOrder(idAct1.toString(), idAct6.toString())))
                .andExpect(jsonPath("$.contextsForCategory.length()", is(1)))
                .andExpect(jsonPath("$.contextsForCategory[0].categoryId", is(modusOperandisCategory.getId().intValue())))
                .andExpect(jsonPath("$.contextsForCategory[0].contextsIds.length()", is(2)))
                .andExpect(jsonPath("$.contextsForCategory[0].contextsIds", containsInAnyOrder(CT_21.getId().toString(), CONTEXT_USED_IN_JOB_WITH_QUESTION.getId().toString())))
                .andExpect(jsonPath("$.contextsForCategory[0].noContextForCategory", is((Object) null)));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    public void shouldNotSaveMissionOnUnmodifiableJob() throws Exception {

        var request = new CreateMissionCommandDTO();
        UUID idAct1 = ACT_01.getUuid(), idAct6 = jobActivityLabelUsedInCandidatureExperienceAndJobMission.getUuid();
        request.setJobId(TestFixtures.J_01.getId());
        request.setActivitiesIds(newArrayList(idAct1, idAct6));
        request.setContextsForCategory(newArrayList(getContextsForCategoryDTO()));
        request.setTitle(TITLE);

        mvc.perform(post(API_ODAS_MISSION + SEPARATOR + "create")
                        .content(objectMapper.writeValueAsBytes(request))
                        .contentType(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(content().string(containsString("modifiable")));
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    public void updateWithContextNotInCategoryShouldFail() throws Exception {

        var request = new UpdateMissionCommandDTO();
        UUID idAct5 = ACT_05.getUuid(), idAct6 = jobActivityLabelUsedInCandidatureExperienceAndJobMission.getUuid();
        var contextsForCategory = getContextsForCategoryDTOContextNotInCategory();

        request.setActivitiesIds(newArrayList(idAct5, idAct6));
        request.setContextsForCategory(newArrayList(contextsForCategory));
        request.setTitle(TITLE);

        mvc.perform(post(API_ODAS_MISSION + SEPARATOR + MISSION_MODIFIABLE_01.getId() + SEPARATOR + "update")
                        .content(objectMapper.writeValueAsBytes(request))
                        .contentType(APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    @ResetDataAfter
    public void shouldUpdateMission() throws Exception {
        var request = new UpdateMissionCommandDTO();
        UUID idAct5 = ACT_05.getUuid(), idAct6 = jobActivityLabelUsedInCandidatureExperienceAndJobMission.getUuid();
        var contextsForCategory = getContextsForCategoryDTO();

        request.setActivitiesIds(newArrayList(idAct5, idAct6));
        request.setContextsForCategory(newArrayList(contextsForCategory));
        request.setTitle(TITLE);


        mvc.perform(post(API_ODAS_MISSION + SEPARATOR + MISSION_MODIFIABLE_01.getId() + SEPARATOR + "update")
                        .content(objectMapper.writeValueAsBytes(request))
                        .contentType(APPLICATION_JSON))
                .andExpect(status().isCreated())
                .andExpect(content().contentTypeCompatibleWith(APPLICATION_JSON))
                .andExpect(jsonPath("$.title", is(TITLE)))
                .andExpect(jsonPath("$.activitiesIds", containsInAnyOrder(idAct5.toString(), idAct6.toString())))
                .andExpect(jsonPath("$.contextsForCategory.length()", is(1)))
                .andExpect(jsonPath("$.contextsForCategory[0].categoryId", is(modusOperandisCategory.getId().intValue())))
                .andExpect(jsonPath("$.contextsForCategory[0].contextsIds.length()", is(2)))
                .andExpect(jsonPath("$.contextsForCategory[0].contextsIds", containsInAnyOrder(CONTEXT_USED_IN_JOB_WITH_QUESTION.getId().toString(), CT_21.getId().toString())))
                .andExpect(jsonPath("$.contextsForCategory[0].noContextForCategory", is((Object) null)));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldUpdateOnlyTitleOnMissionOnUnmodifiableJob() throws Exception {
        var request = new UpdateMissionCommandDTO();
        UUID idAct5 = ACT_05.getUuid(), idAct8 = ACT_08.getUuid();
        var contextsForCategory = getContextsForCategoryDTO();

        request.setActivitiesIds(newArrayList(idAct5, idAct8));
        request.setContextsForCategory(newArrayList(contextsForCategory));
        request.setTitle(TITLE);

        mvc.perform(post(API_ODAS_MISSION + SEPARATOR + M_01.getId() + SEPARATOR + "update")
                        .content(objectMapper.writeValueAsBytes(request))
                        .contentType(APPLICATION_JSON))
                .andExpect(status().isCreated());

        txHelper.doInTransaction(() -> {
            var mission = repository.findById(M_01.getId()).get();
            log.info("Ensure Job {} is not modifiable", mission.getJob());
            assertThat(mission.getJob().isModifiable()).isFalse();
            assertThat(mission.getActivities()).containsExactlyInAnyOrder(ACT_01, jobActivityLabelUsedOnlyInJobMission);
            assertThat(mission.getContextsForCategory())
                    .flatExtracting(ContextsForCategory::getContexts)
                    .containsExactlyInAnyOrder(CONTEXT_USED_IN_JOB_OF_FORMATION, CONTEXT_USED_IN_JOB_WITH_QUESTION);
        });
    }

    private ContextsForCategoryDTO getContextsForCategoryDTO() {
        var contextsForCategory = new ContextsForCategoryDTO();
        contextsForCategory.setCategoryId(modusOperandisCategory.getId());
        contextsForCategory.setContextsIds(newArrayList(CONTEXT_USED_IN_JOB_WITH_QUESTION.getId(), CT_21.getId()));
        return contextsForCategory;
    }

    private ContextsForCategoryDTO getContextsForCategoryDTOContextNotInCategory() {
        var contextsForCategory = new ContextsForCategoryDTO();
        contextsForCategory.setCategoryId(modusOperandisCategory.getId());
        contextsForCategory.setContextsIds(newArrayList(MANDATORY_CONTEXT.getId(), CT_02.getId()));
        return contextsForCategory;
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    public void shouldDeleteMission() throws Exception {

        mvc.perform(delete(API_ODAS_MISSION + SEPARATOR + MISSION_MODIFIABLE_02.getId() + SEPARATOR))
                .andExpect(status().isNoContent());


        mvc.perform(delete(API_ODAS_MISSION + SEPARATOR + MISSION_MODIFIABLE_02.getId() + SEPARATOR))
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    public void shouldNotDeleteMissionOnUnmodifiableJob() throws Exception {

        mvc.perform(delete(API_ODAS_MISSION + SEPARATOR + TestFixtures.M_02.getId() + SEPARATOR))
                .andExpect(status().isBadRequest())
                .andExpect(content().string(containsString("modifiable")))
        ;
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    public void shouldRetrieve404WhenMissionNotFound() throws Exception {

        mvc.perform(delete(API_ODAS_MISSION + SEPARATOR + 42424242))
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    public void shouldRetrieve404WhenJobNotFound() throws Exception {

        var request = new CreateMissionCommandDTO();
        UUID idAct1 = ACT_01.getUuid(), idAct6 = jobActivityLabelUsedInCandidatureExperienceAndJobMission.getUuid();
        request.setActivitiesIds(newArrayList(idAct1, idAct6));
        request.setJobId(UUID.randomUUID());
        request.setActivitiesIds(newArrayList(idAct1, idAct6));
        request.setTitle(TITLE);

        mvc.perform(post(API_ODAS_MISSION + SEPARATOR + "create")
                        .content(objectMapper.writeValueAsBytes(request))
                        .contentType(APPLICATION_JSON))
                .andExpect(status().isNotFound());

    }

}
