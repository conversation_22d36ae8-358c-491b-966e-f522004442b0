package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TransactionTestHelper;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.configuration.DataHealthChecker;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.repositories.DataHealthCheckerRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.datahealthchecker.DataHealthCheckerService;
import com.erhgo.services.notifier.Notifier;
import liquibase.Liquibase;
import liquibase.database.jvm.JdbcConnection;
import liquibase.resource.ClassLoaderResourceAccessor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;

import javax.sql.DataSource;

import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


@Slf4j
class DataHealthCheckerControllerTest extends AbstractIntegrationTest {

    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    DataHealthCheckerRepository repository;

    @Autowired
    TransactionTestHelper txHelper;

    @Autowired
    MockMvc mvc;

    @Autowired
    DataHealthCheckerService service;

    @MockitoBean
    Notifier notifier;

    @Autowired
    DataSource dataSource;

    @BeforeEach
    void initData() {
        applicationContext.getBean(UserProfileMotherObject.class).buildAndPersist();
        applicationContext.getBean(UserProfileMotherObject.class).buildAndPersist();
        applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist();
    }

    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @SneakyThrows
    @ResetDataAfter
    @Test
    void checkActualDataHealthCheck() {
        try (var connection = dataSource.getConnection()) {
            new Liquibase("db/changelog/db.changelog-utils.data-health-checks.sql", new ClassLoaderResourceAccessor(), new JdbcConnection(connection)).update("");
            service.executeAllDataHealthChecker();
            verify(notifier).sendMessage(argThat(arg -> !arg.getText().contains(":red_circle:")));
        }
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void executeAllDataHealthChecker_with_successful_queries() {
        buildAndPersistDataHealthChecker("SELECT COUNT(*)=2, COUNT(*) FROM UserProfile");
        buildAndPersistDataHealthChecker("SELECT COUNT(*)=1, COUNT(*) FROM ErhgoOccupation");
        buildAndPersistDataHealthChecker("SELECT COUNT(*)=0, COUNT(*) FROM Criteria");

        service.executeAllDataHealthChecker();
        verifyNoInteractions(notifier);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void executeAllDataHealthChecker_with_failing_queries() {
        buildAndPersistDataHealthChecker("SELECT COUNT(*)=1, COUNT(*) FROM UserProfile");
        buildAndPersistDataHealthChecker("SELECT COUNT(*)=2, COUNT(*) FROM ErhgoOccupation");
        buildAndPersistDataHealthChecker("SELECT COUNT(*)=40, COUNT(*) FROM Criteria");
        var message = """
                Il y a 3 test(s) en échec et 0 erreur(s) d'exécution sur 3 tests :
                :large_orange_circle: Le test Title of DHC for query SELECT COUNT(*)=1, COUNT(*) FROM UserProfile a échoué. (raison: Error message, valeur obtenue : 2)
                :large_orange_circle: Le test Title of DHC for query SELECT COUNT(*)=2, COUNT(*) FROM ErhgoOccupation a échoué. (raison: Error message, valeur obtenue : 1)
                :large_orange_circle: Le test Title of DHC for query SELECT COUNT(*)=40, COUNT(*) FROM Criteria a échoué. (raison: Error message, valeur obtenue : 0)""";

        service.executeAllDataHealthChecker();
        verify(notifier).sendMessage(argThat((arg) -> arg.getText().equals(message)));
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void executeAllDataHealthChecker_with_non_executable_queries() {
        buildAndPersistDataHealthChecker("INSERT INTO ...");
        buildAndPersistDataHealthChecker("SELECT COUNT(*)=1 FROM UserProfile");
        buildAndPersistDataHealthChecker("SELECT * FROM UserProfile");
        buildAndPersistDataHealthChecker("SELECT COUNT(*)=2 FROM UserProfile;DROP TABLE Activity_Capacity");
        var message = """
                Il y a 0 test(s) en échec et 4 erreur(s) d'exécution sur 4 tests :
                :red_circle: Le test Title of DHC for query INSERT INTO ... n'a pas pu être exécuté correctement (raison: Query cannot execute properly (reason: contains INSERT))
                :red_circle: Le test Title of DHC for query SELECT * FROM UserProfile n'a pas pu être exécuté correctement (raison: Query did not return a unique result: 2 results were returned)
                :red_circle: Le test Title of DHC for query SELECT COUNT(*)=1 FROM UserProfile n'a pas pu être exécuté correctement (raison: Query cannot execute properly (reason: SQL query must return two columns: {boolean - true if test succeeds; any value - the obtained wrong value making this test fail}))
                :red_circle: Le test Title of DHC for query SELECT COUNT(*)=2 FROM UserProfile;DROP TABLE Activity_Capacity n'a pas pu être exécuté correctement (raison: Query cannot execute properly (reason: contains ';'))""";
        service.executeAllDataHealthChecker();
        verify(notifier).sendMessage(ArgumentMatchers.assertArg(arg ->
                {
                    Assertions.assertThat(arg.getText()).isEqualTo(message);
                }
        ));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void executeAllHealthCheckQueries_verify_endpoint_exists_with_expected_status() throws Exception {
        mvc.perform(get("/api/odas/data-health-check/execute-queries"))
                .andExpect(status().isNoContent());
    }

    private DataHealthChecker buildAndPersistDataHealthChecker(String query) {
        return buildAndPersistDataHealthChecker(
                new DataHealthChecker()
                        .title("Title of DHC for query %s".formatted(query))
                        .query(query)
                        .errorMessage("Error message")
        );
    }

    private DataHealthChecker buildAndPersistDataHealthChecker(DataHealthChecker dhc) {
        return txHelper.doInTransaction(() -> repository.save(dhc));
    }

}
