package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TestUtils;
import com.erhgo.config.ApiConstants;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.enums.BehaviorCategory;
import com.erhgo.domain.enums.Situation;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Behavior;
import com.erhgo.domain.userprofile.GeneralInformation;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.generators.*;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.JobRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.*;

import static com.erhgo.TestUtils.stringMatchesContent;
import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

class JobMatchingUsersControllerTest extends AbstractIntegrationTest {

    @Autowired
    private JobGenerator jobGenerator;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private CapacityGenerator capacityGenerator;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Autowired
    private OrganizationGenerator organizationGenerator;

    @Autowired
    private KeycloakMockService keycloakMockService;

    @Autowired
    private RecruitmentGenerator recruitmentGenerator;

    @Autowired
    private CandidatureGenerator candidatureGenerator;

    @Autowired
    private BehaviorGenerator behaviorGenerator;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private ErhgoOccupationGenerator erhgoOccupationGenerator;


    @Autowired
    private ConfigurablePropertyRepository configurablePropertyRepository;

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void findMatchingUsersForJob_should_retrieve_empty_list() throws Exception {

        var capacity = capacityGenerator.createCapacity("CA-1");

        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", MasteryLevel.MAX_LEVEL, capacity);

        performGetMatchingUsers(job)
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", hasSize(0)));
    }


    private ResultActions performGetMatchingUsers(Job job) throws Exception {
        return mvc.perform(getMatchingUsersRequestBuilder(job));
    }

    private MockHttpServletRequestBuilder getMatchingUsersRequestBuilder(Job job) {
        return get(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + job.getId() + "/matching-users")
                .param("page", "0")
                .param("size", "600")
                .contentType(MediaType.APPLICATION_JSON);
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void findMatchingUsersForJob_with_single_capacity_should_retrieve_user() throws Exception {

        var matchingCapacity = capacityGenerator.createCapacity("CA-1");
        var otherCapacity = capacityGenerator.createCapacity("CA-2");
        var expectedUuid = UUID.randomUUID();
        userProfileGenerator.createUserProfileWithCapacities(expectedUuid, 1, matchingCapacity);
        userProfileGenerator.createUserProfileWithCapacities(UUID.randomUUID(), 1, otherCapacity);
        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", MasteryLevel.MIN_LEVEL, matchingCapacity);

        var expectedEmail = expectedUuid + "@erhgo.fr";

        mvc.perform(getMatchingUsersRequestBuilder(job)
                        .param("capacityThreshold", "0.99")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].contactInformation.email", contains(expectedEmail)));
    }


    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void findMatchingUsersForJob_should_tolerate_no_kc_user() throws Exception {

        var expectedUuid = UUID.randomUUID();
        var matchingCapacity = capacityGenerator.createCapacity("CA-1");
        userProfileGenerator.createUserProfileWithCapacities(expectedUuid, 1, matchingCapacity);
        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", MasteryLevel.MIN_LEVEL, matchingCapacity);

        var expectedEmail = expectedUuid + "@erhgo.fr";
        keycloakMockService.setUserProfile(expectedUuid.toString(), null);

        mvc.perform(getMatchingUsersRequestBuilder(job)
                        .param("capacityThreshold", "0.99")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*]", hasSize(1)));
    }

    @Test
    @DisplayName("Given two users with a mastery level of 1 and 1.5 when I retrieve users for job with mastery level 2 and range 0.5 then only second user should be retrieved")
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void findMatchingUsersForJob_with_level_range() throws Exception {

        var matchingCapacity = capacityGenerator.createCapacity("CA-1");
        var expectedUuid = UUID.randomUUID();
        userProfileGenerator.createUserProfileWithCapacitiesAndLevel1dot5(expectedUuid, List.of(matchingCapacity, capacityGenerator.createCapacity("CA-240")));
        userProfileGenerator.createUserProfileWithCapacities(UUID.randomUUID(), 1, matchingCapacity);
        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", MasteryLevel.forLevel(2), matchingCapacity);

        var expectedEmail = expectedUuid + "@erhgo.fr";

        mvc.perform(getMatchingUsersRequestBuilder(job)
                        .param("capacityThreshold", "0.99")
                        .param("masteryLevelRange", "0.5")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].contactInformation.email", contains(expectedEmail)));
    }

    @ParameterizedTest
    @ValueSource(strings = {"1.6", "2"})
    @DisplayName("Given two users with a mastery level of 1 and 1.5 when I retrieve users for job with mastery level 3 and threshold 1.6 then only second user should be retrieved, if threshold is 2 then both users are retrieved")
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void findMatchingUsersForJob_uses_configurable_threshold(String masteryLevelThreshold) throws Exception {

        txHelper.doInTransaction(() -> {
            configurablePropertyRepository.findOneByPropertyKey("user_mastery_level_threshold").setPropertyValue(masteryLevelThreshold);
        });
        var matchingCapacity = capacityGenerator.createCapacity("CA-1");
        var expectedUuid = UUID.randomUUID();
        var otherUuid = UUID.randomUUID();
        userProfileGenerator.createUserProfileWithCapacitiesAndLevel1dot5(expectedUuid, List.of(matchingCapacity, capacityGenerator.createCapacity("CA-240")));
        userProfileGenerator.createUserProfileWithCapacities(otherUuid, 1, matchingCapacity);
        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", MasteryLevel.forLevel(3), matchingCapacity);

        var expectedEmail = expectedUuid + "@erhgo.fr";
        var otherEmail = otherUuid + "@erhgo.fr";

        mvc.perform(getMatchingUsersRequestBuilder(job)
                        .param("capacityThreshold", "0.99")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].contactInformation.email", Float.parseFloat(masteryLevelThreshold) < 1.7f ? containsInAnyOrder(expectedEmail) : containsInAnyOrder(expectedEmail, otherEmail)));
    }


    @ParameterizedTest
    @ValueSource(floats = {-1.0f, 6.0f})
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    @DisplayName("" +
            "when I get users with range negative or greater than 5 " +
            "then request fails"
    )
    void findMatchingUsersForJob_with_level_range_fails(float range) throws Exception {
        mvc.perform(getMatchingUsersRequestBuilder(jobGenerator.createJob())
                .param("capacityThreshold", "0.99")
                .param("masteryLevelRange", String.valueOf(range))
                .contentType(MediaType.APPLICATION_JSON)).andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    @DisplayName("" +
            "Given two EPA capacities CA1 CA2, two users U-CA1 U-CA2 and a job having CA1 " +
            "when I search for matching users " +
            "then U-CA is retrieved"
    )
    void findMatchingUsersForJob_EPA() throws Exception {

        var matchingCapacity = capacityGenerator.createCapacity("CA-1");
        var otherCapacity = capacityGenerator.createCapacity("CA-2");
        var expectedUuid = UUID.randomUUID();
        userProfileGenerator.createUserProfileWithCapacitiesFromEPA(expectedUuid, matchingCapacity);
        userProfileGenerator.createUserProfileWithCapacitiesFromEPA(UUID.randomUUID(), otherCapacity);
        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", MasteryLevel.MIN_LEVEL, matchingCapacity);

        var expectedEmail = expectedUuid + "@erhgo.fr";

        mvc.perform(getMatchingUsersRequestBuilder(job)
                        .param("capacityThreshold", "0.99")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].contactInformation.email", contains(expectedEmail)));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    @DisplayName("" +
            "Given two EPA capacities CA1 CA2, two capacities used in experience CA3 CA4, two users U1 (with CA1 & CA3) and U2 (with CA2 and CA4) and a job having CA1 and CA3" +
            "when I search for matching users " +
            "then U1 is retrieved"
    )
    void findMatchingUsersForJob_EPA_and_xp() throws Exception {

        var matchingCapacity1 = capacityGenerator.createCapacity("CA-1");
        var matchingCapacity2 = capacityGenerator.createCapacity("CA-3");

        var otherCapacity1 = capacityGenerator.createCapacity("CA-2");
        var otherCapacity2 = capacityGenerator.createCapacity("CA-4");

        var expectedUuid = UUID.randomUUID();
        userProfileGenerator.createUserProfileWithCapacitiesFromEPAAndExperience(expectedUuid, matchingCapacity1, matchingCapacity2);
        userProfileGenerator.createUserProfileWithCapacitiesFromEPAAndExperience(UUID.randomUUID(), otherCapacity1, otherCapacity2);
        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", MasteryLevel.MIN_LEVEL, matchingCapacity1, matchingCapacity2);

        var expectedEmail = expectedUuid + "@erhgo.fr";

        mvc.perform(getMatchingUsersRequestBuilder(job)
                        .param("capacityThreshold", "0.99")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].contactInformation.email", contains(expectedEmail)));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void findMatchingUsersForJob_with_single_capacity_and_threshold_should_retrieve_user() throws Exception {

        var matchingCapacity = capacityGenerator.createCapacity("CA-1");
        var secondJobCapacity = capacityGenerator.createCapacity("CA-2");
        var otherCapacity = capacityGenerator.createCapacity("CA-3");
        var uuid = UUID.randomUUID();
        var user = userProfileGenerator.createUserProfileWithCapacities(uuid, 1, matchingCapacity);
        userProfileGenerator.createUserProfileWithCapacities(UUID.randomUUID(), 1, otherCapacity);
        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", MasteryLevel.MIN_LEVEL, matchingCapacity, secondJobCapacity);

        var email = uuid + "@erhgo.fr";

        mvc.perform(getMatchingUsersRequestBuilder(job)
                        .param("capacityThreshold", "0.5")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].matchingRateInPercent", contains(50)))
                .andExpect(jsonPath("$.content[*].contactInformation.email", contains(email)));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void findMatchingUsersForJob_should_paginate_and_sort_by_match_page1() throws Exception {
        var firstCapacity = capacityGenerator.createCapacity("CA-1");
        var secondJobCapacity = capacityGenerator.createCapacity("CA-2");
        var thirdCapacity = capacityGenerator.createCapacity("CA-3");
        var otherCapacity = capacityGenerator.createCapacity("CA-4");
        var third = userProfileGenerator.createUserProfileWithCapacities(UUID.randomUUID(), 1, firstCapacity);
        var second = userProfileGenerator.createUserProfileWithCapacities(UUID.randomUUID(), 1, firstCapacity, secondJobCapacity, thirdCapacity);
        var first = userProfileGenerator.createUserProfileWithCapacities(UUID.randomUUID(), 1, firstCapacity, secondJobCapacity);

        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", MasteryLevel.MIN_LEVEL, firstCapacity, secondJobCapacity, thirdCapacity);

        mvc.perform(get(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + job.getId() + "/matching-users")
                        .param("page", "0")
                        .param("size", "2")
                        .param("capacityThreshold", "0.5")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].contactInformation.email", contains(second.uuid() + "@erhgo.fr", first.uuid() + "@erhgo.fr")))
                .andExpect(jsonPath("$.totalNumberOfElements", is(2)));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void findMatchingUsersForJob_should_paginate_and_sort_by_match_page2() throws Exception {

        var firstCapacity = capacityGenerator.createCapacity("CA-1");
        var secondJobCapacity = capacityGenerator.createCapacity("CA-2");
        var thirdJobCapacity = capacityGenerator.createCapacity("CA-3");
        var otherCapacity = capacityGenerator.createCapacity("CA-4");

        var third = userProfileGenerator.createUserProfileWithCapacities(UUID.randomUUID(), 1, firstCapacity, secondJobCapacity);
        var second = userProfileGenerator.createUserProfileWithCapacities(UUID.randomUUID(), 1, firstCapacity, secondJobCapacity, thirdJobCapacity);
        var first = userProfileGenerator.createUserProfileWithCapacities(UUID.randomUUID(), 1, firstCapacity, secondJobCapacity, thirdJobCapacity);
        userProfileGenerator.createUserProfileWithCapacities(UUID.randomUUID(), 1, firstCapacity);
        userProfileGenerator.createUserProfileWithCapacities(UUID.randomUUID(), 1, otherCapacity);

        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", MasteryLevel.MIN_LEVEL, firstCapacity, secondJobCapacity, thirdJobCapacity);

        mvc.perform(get(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + job.getId() + "/matching-users")
                        .param("page", "1")
                        .param("size", "2")
                        .param("capacityThreshold", "0.5")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].contactInformation.email", contains(third.uuid() + "@erhgo.fr")))
                .andExpect(jsonPath("$.totalNumberOfElements", is(3)));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void findMatchingUsersForJob_should_consider_induced_capacities() throws Exception {

        var lowLevelCapacity1 = capacityGenerator.createCapacity("CA1-1");
        var lowLevelCapacity2 = capacityGenerator.createCapacity("CA1-2");
        var inducedCapacity = capacityGenerator.createCapacity("CA2-1", lowLevelCapacity1);
        var topLevelCapacity = capacityGenerator.createCapacity("CA3-1", inducedCapacity, lowLevelCapacity2);
        var otherCapacity = capacityGenerator.createCapacity("CA3-2", inducedCapacity);

        var uuid = UUID.randomUUID();
        var user = userProfileGenerator.createUserProfileWithCapacities(uuid, 1, topLevelCapacity);
        userProfileGenerator.createUserProfileWithCapacities(UUID.randomUUID(), 1, otherCapacity);
        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", MasteryLevel.MIN_LEVEL, lowLevelCapacity1, lowLevelCapacity2, inducedCapacity, topLevelCapacity);

        var email = uuid + "@erhgo.fr";

        mvc.perform(getMatchingUsersRequestBuilder(job)
                        .param("capacityThreshold", "1.0")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].contactInformation.email", contains(email)));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void findMatchingUsersForJob_should_consider_level() throws Exception {

        var masteredCapacity = capacityGenerator.createCapacity("CA1-1");
        var expectedUserUuid = UUID.randomUUID();
        userProfileGenerator.createUserProfileWithCapacities(expectedUserUuid, MasteryLevel.MAX_LEVEL.getMasteryLevel(), masteredCapacity);
        userProfileGenerator.createUserProfileWithCapacities(UUID.randomUUID(), MasteryLevel.MIN_LEVEL.getMasteryLevel(), masteredCapacity);
        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", MasteryLevel.MAX_LEVEL, masteredCapacity);

        var email = expectedUserUuid + "@erhgo.fr";

        mvc.perform(getMatchingUsersRequestBuilder(job)
                        .param("capacityThreshold", "1.0")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].contactInformation.email", contains(email)));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void findMatchingUsersForJob_should_consider_average_level_and_tolerate_one_degree() throws Exception {

        var highLevelCapacity = capacityGenerator.createCapacity("CA1-1");
        var lowLevelCapacity = capacityGenerator.createCapacity("CA1-2");

        var firstUser = userProfileGenerator.createUserProfileWithLevelAndCapacities(new Integer[]{MasteryLevel.MAX_LEVEL.getMasteryLevel(), MasteryLevel.MIN_LEVEL.getMasteryLevel()}, List.of(highLevelCapacity), List.of(lowLevelCapacity));
        var secondUser = userProfileGenerator.createUserProfileWithCapacities(UUID.randomUUID(), MasteryLevel.TECHNICAL.getMasteryLevel(), highLevelCapacity, lowLevelCapacity);
        userProfileGenerator.createUserProfileWithCapacities(UUID.randomUUID(), MasteryLevel.MIN_LEVEL.getMasteryLevel(), highLevelCapacity, lowLevelCapacity);

        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", MasteryLevel.COMPLEX, highLevelCapacity, lowLevelCapacity);

        var expectedEmails = new String[]{firstUser.uuid() + "@erhgo.fr", secondUser.uuid() + "@erhgo.fr"};

        mvc.perform(getMatchingUsersRequestBuilder(job)
                        .param("capacityThreshold", "1.0")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].contactInformation.email", containsInAnyOrder(expectedEmails)));
    }


    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void findMatchingUsersForJob_should_not_consider_level_of_occupation_without_capacity() throws Exception {

        var capacity = capacityGenerator.createCapacity("CA1-1");
        var otherCapacity = capacityGenerator.createCapacity("CA1-2");

        var firstUser = userProfileGenerator.createUserProfileWithLevelAndCapacities(new Integer[]{MasteryLevel.MIN_LEVEL.getMasteryLevel()}, List.of(capacity));
        var secondUser = userProfileGenerator.createUserProfileWithLevelAndCapacities(new Integer[]{MasteryLevel.MIN_LEVEL.getMasteryLevel(), MasteryLevel.MAX_LEVEL.getMasteryLevel()}, List.of(capacity), List.of(otherCapacity));

        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", MasteryLevel.COMPLEX, capacity);

        var expectedEmails = new String[]{secondUser.uuid() + "@erhgo.fr"};

        mvc.perform(getMatchingUsersRequestBuilder(job)
                        .param("capacityThreshold", "1.0")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].contactInformation.email", containsInAnyOrder(expectedEmails)));
    }


    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    @DisplayName("" +
            "Given a 3 users matching a job, one on orga O, one on project P, one on some other orga, " +
            "When an admin access to users matching job for an organization or a project " +
            "Then user has channels")
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void findMatchingUsers_admin(boolean forProject) throws Exception {
        findMatchingUsers_channels(false, forProject);
    }

    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    @DisplayName("" +
            "Given 3 users matching a job with one empty and two non empty postcode" +
            "When postcode filter is not empty " +
            "Then users are filtered on postcode ")
    @Test
    void findMatchingUsers_postcode() throws Exception {
        var matchingCapacity = capacityGenerator.createCapacity("CA1-1");
        userProfileGenerator.createUserProfileWithCapacities(UUID.randomUUID(), MasteryLevel.MIN_LEVEL.getMasteryLevel(), matchingCapacity);
        var user69100 = userProfileGenerator.createUserProfileWithCapacities(UUID.randomUUID(), MasteryLevel.MIN_LEVEL.getMasteryLevel(), matchingCapacity);
        var user69200 = userProfileGenerator.createUserProfileWithCapacities(UUID.randomUUID(), MasteryLevel.MIN_LEVEL.getMasteryLevel(), matchingCapacity);

        txHelper.doInTransaction(() -> {
            userProfileRepository.findByUserId(user69100.userId()).orElseThrow().generalInformation().setLocation(Location.builder().postcode("69100").build());
            userProfileRepository.findByUserId(user69200.userId()).orElseThrow().generalInformation().setLocation(Location.builder().postcode("69200").build());
        });

        var organization = organizationGenerator.createRecruiter("T-01", AbstractOrganization.OrganizationType.TERRITORIAL);
        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", organization, MasteryLevel.MIN_LEVEL, matchingCapacity);
        mvc.perform(getMatchingUsersRequestBuilder(job)
                        .param("postcode", "691")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].contactInformation.email", contains(user69100.uuid() + "@erhgo.fr")));
    }

    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, "T-01", "P-01"})
    @ResetDataAfter
    @DisplayName("" +
            "Given a 3 users matching a job, one on orga O, one on project P, one on some other orga," +
            "When an TO access to users matching job for an organization or a project " +
            "Then user has channels")
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void findMatchingUsers_TO(boolean forProject) throws Exception {
        findMatchingUsers_channels(false, forProject);
    }

    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    @DisplayName("" +
            "Given a user matching job with multiple capacities " +
            "When an admin access to users matching job " +
            "Then user has channels without duplicates")
    @Test
    void findMatchingUsers_project() throws Exception {
        var organization = organizationGenerator.createRecruiter("T-01", AbstractOrganization.OrganizationType.TERRITORIAL);
        keycloakMockService.createBackOfficeGroupAndRoles(organization.getCode());
        var matchingCapacity1 = capacityGenerator.createCapacity("CA1-1");
        var matchingCapacity2 = capacityGenerator.createCapacity("CA1-2");

        userProfileGenerator.createUserProfileWithRoleAndCapacities(UUID.randomUUID(), 1, organization.getCode(), matchingCapacity1, matchingCapacity2);
        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", organization, MasteryLevel.MIN_LEVEL, matchingCapacity1, matchingCapacity2);
        mvc.perform(getMatchingUsersRequestBuilder(job)
                        .param("organizationCodes", organization.getCode())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].contactInformation.channels[*]", containsInAnyOrder(organization.getTitle())));
    }

    private void findMatchingUsers_channels(boolean asProject, boolean targetsProject) throws Exception {
        var project = organizationGenerator.createRecruiter("P-01", AbstractOrganization.OrganizationType.PROJECT);
        var organization = organizationGenerator.createRecruiter("T-01", AbstractOrganization.OrganizationType.TERRITORIAL);
        var otherOrganization = organizationGenerator.createRecruiter("T-02", AbstractOrganization.OrganizationType.TERRITORIAL);

        keycloakMockService.createBackOfficeGroupAndRoles(organization.getCode(), project.getCode());
        keycloakMockService.createBackOfficeGroupAndRoles(project.getCode());
        keycloakMockService.createBackOfficeGroupAndRoles(otherOrganization.getCode(), project.getCode());

        var matchingCapacity = capacityGenerator.createCapacity("CA-1");

        userProfileGenerator.createUserProfileWithCapacities(UUID.randomUUID(), 1, matchingCapacity);

        var userInProject = userProfileGenerator.createUserProfileWithChannelsAndCapacities(UUID.randomUUID(), 1, Set.of(project.getCode(), otherOrganization.getCode()), matchingCapacity);
        var userInProjectAndOrganization = userProfileGenerator.createUserProfileWithChannelsAndCapacities(UUID.randomUUID(), 1, Set.of(project.getCode(), organization.getCode()), matchingCapacity);
        txHelper.doInTransaction(() -> {
            var location = Location.builder()
                    .city("Lyon")
                    .citycode("59000")
                    .postcode("42123")
                    .latitude(42.213f)
                    .longitude(59f)
                    .build();

            var userProfile = userProfileRepository.findByUserId(userInProjectAndOrganization.userId()).orElseThrow();
            userProfile.generalInformation().setLocation(location);
            userProfile.updateJobOfferOptOut(true);
        });

        var userInOrganization = userProfileGenerator.createUserProfileWithChannelsAndCapacities(UUID.randomUUID(), 1, Set.of(organization.getCode()), matchingCapacity);
        userProfileGenerator.createUserProfileWithChannelsAndCapacities(UUID.randomUUID(), 1, Set.of(otherOrganization.getCode()), matchingCapacity);

        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", targetsProject ? project : organization, MasteryLevel.MIN_LEVEL, matchingCapacity);

        var expectations = mvc.perform(getMatchingUsersRequestBuilder(job)
                .param("organizationCodes", (targetsProject ? project : organization).getCode())
                .contentType(MediaType.APPLICATION_JSON));

        expectations.andExpect(status().isOk());
        if (!asProject) {
            expectations.andExpect(TestUtils.jsonMatchesContent("matchingUsersFor" + (targetsProject ? "Project" : "Orga")));
        } else {
            expectations
                    .andExpect(jsonPath("$.content[*].contactInformation.channels[*]", containsInAnyOrder(project.getTitle(), project.getTitle())));
        }

        var expectedUsers = new HashSet<>(Set.of(userInProject, userInProjectAndOrganization));
        if (!asProject && !targetsProject) {
            expectedUsers.add(userInOrganization);
        }
        String[] expectedEmails = expectedUsers.stream()
                .map(UserProfile::uuid)
                .map(Objects::toString)
                .map(s -> s + "@erhgo.fr")
                .toArray(String[]::new);
        expectations
                .andExpect(jsonPath("$.content[*].contactInformation.email", containsInAnyOrder(expectedEmails)))
        ;
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void findMatchingUsers_for_job_of_project_returns_users_associated_to_project_and_its_organization() throws Exception {

        var project = organizationGenerator.createRecruiter("P-01", AbstractOrganization.OrganizationType.PROJECT);
        var organization = organizationGenerator.createRecruiter("T-01", AbstractOrganization.OrganizationType.TERRITORIAL);
        var otherOrganization = organizationGenerator.createRecruiter("T-02", AbstractOrganization.OrganizationType.TERRITORIAL);
        keycloakMockService.createBackOfficeGroupAndRoles(organization.getCode(), project.getCode());
        keycloakMockService.createBackOfficeGroupAndRoles(project.getCode());
        keycloakMockService.createBackOfficeGroupAndRoles(otherOrganization.getCode(), project.getCode());

        var matchingCapacity = capacityGenerator.createCapacity("CA-1");

        userProfileGenerator.createUserProfileWithCapacities(UUID.randomUUID(), 1, matchingCapacity);

        var userInProject = userProfileGenerator.createUserProfileWithRoleAndCapacities(UUID.randomUUID(), 1, project.getCode(), matchingCapacity);
        var userInOrga = userProfileGenerator.createUserProfileWithRoleAndCapacities(UUID.randomUUID(), 1, organization.getCode(), matchingCapacity);
        userProfileGenerator.createUserProfileWithRoleAndCapacities(UUID.randomUUID(), 1, otherOrganization.getCode(), matchingCapacity);

        var expectedEmails = new String[]{userInProject.uuid() + "@erhgo.fr", userInOrga.uuid() + "@erhgo.fr"};

        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", project, MasteryLevel.MIN_LEVEL, matchingCapacity);

        TestUtils.mockRoles(Role.ODAS_ADMIN, organization.getCode(), project.getCode());

        mvc.perform(getMatchingUsersRequestBuilder(job)
                        .param("organizationCodes", organization.getCode(), project.getCode())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].contactInformation.email", containsInAnyOrder(expectedEmails)));

    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    @DisplayName("given a user with a candidature on job when I get matching user then user has candidatureId")
    void findMatchingUsersForJob_with_candidature() throws Exception {
        var otherCapacity = capacityGenerator.createCapacity("CA-11");
        var otherCapacity2 = capacityGenerator.createCapacity("CA-12");

        var capacity = capacityGenerator.createCapacity("CA-1");
        var matchingCapacity = capacityGenerator.createCapacity("CA-2", capacity);

        var uuid = UUID.randomUUID();
        var user = userProfileGenerator.createUserProfileWithCapacities(uuid, 1, matchingCapacity, otherCapacity, otherCapacity2);
        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", MasteryLevel.MIN_LEVEL, matchingCapacity, capacity);

        var recruitment = recruitmentGenerator.createRecruitmentWithNoRequirement(job);

        var candidature = candidatureGenerator.createCandidature(user, recruitment);

        var expectedEmail = uuid + "@erhgo.fr";
        txHelper.doInTransaction(() -> {
            var refetchUser = userProfileRepository.findById(user.uuid()).orElseThrow();
            var behavior1 = behaviorGenerator.createBehavior("B_01", "Title1", "Description", BehaviorCategory.CONSTANCY, 1);
            var behavior2 = behaviorGenerator.createBehavior("B_02", "Title2", "Description", BehaviorCategory.HONESTY, 1);
            useBehaviorOnUserProfile(refetchUser, behavior1);
            useBehaviorOnUserProfile(refetchUser, behavior2);
        });

        performGetMatchingUsers(job)
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].contactInformation.email", contains(expectedEmail)))
                .andExpect(jsonPath("$.content[*].candidatureId", contains(candidature.getId().intValue())))
                .andExpect(jsonPath("$.content[*].userProfileProgress.capacitiesCount", contains(3)))
                .andExpect(jsonPath("$.content[*].userProfileProgress.experiencesCount", contains(1)))
                .andExpect(jsonPath("$.content[*].userProfileProgress.hasBehaviors", contains(true)))
        ;
    }

    @ParameterizedTest
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ValueSource(ints = {1, 2, 3})
    @DisplayName("" +
            "Given a user with a 0 level and a job of varying level " +
            "When I get matching users " +
            "Then job is retrieved when level is <= 2")
    @ResetDataAfter
    void findMatchingUsersForJob_without_experience(int level) throws Exception {
        var userId = UUID.randomUUID();
        var capacity = capacityGenerator.createCapacity("CA1-1");
        var user = userProfileGenerator.createUserProfileWithCapacitiesFromEPA(userId, capacity);
        userProfileGenerator.createUserProfileWithCapacitiesFromEPA(UUID.randomUUID(), capacityGenerator.createCapacity("CA1-2"));
        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", MasteryLevel.forLevel(level), capacity);
        var action = performGetMatchingUsers(job)
                .andExpect(status().isOk());
        if (level <= 2) {
            action.andExpect(jsonPath("$.content[*].contactInformation.email", contains(userId + "@erhgo.fr")));
        } else {
            action.andExpect(jsonPath("$.content[*]", hasSize(0)));
        }

    }

    private void useBehaviorOnUserProfile(UserProfile userProfile, Behavior behavior) {
        userProfile.updateBehaviors(Set.of(behavior));
    }


    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void export_csv_should_succeed_ADMIN() throws Exception {
        export_csv_should_succeed_common("usersMatchingJobExport.csv");
    }

    void export_csv_should_succeed_common(String expectedContentFile) throws Exception {
        var capacity = capacityGenerator.createCapacity("CA1-1");
        var recruiter = organizationGenerator.createRecruiter("T-01", AbstractOrganization.OrganizationType.TERRITORIAL);
        var employer = organizationGenerator.createEmployer("P-02", recruiter);
        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-01", recruiter, MasteryLevel.PROFESSIONAL, capacity);
        txHelper.doInTransaction(() -> {
            jobRepository.findById(job.getId()).orElseThrow().setEmployer(employer);
        });
        var user1_uuid = UUID.fromString("0a384a23-c765-423b-88e9-38faad5acaff");
        var user1_generalInfos = GeneralInformation.builder()
                .userId(user1_uuid.toString())
                .location(Location.builder().city("Lille").postcode("59000").build())
                .phoneNumber("0659595959")
                .situation(Situation.RESEARCHING)
                .salary(1500)
                .smsBlacklisted(true)
                .build();
        var user1 = userProfileGenerator.createUserProfileWithChannelsAndCapacitiesAndGeneralInfos(user1_uuid, 1, List.of("T-01"), user1_generalInfos, capacity);

        var user2_uuid = UUID.fromString("28291ce1-e260-4b62-8c07-5d0301b17327");
        var user2_generalInfos = GeneralInformation.builder()
                .userId(user2_uuid.toString())
                .location(Location.builder().city("Lyon").postcode("69000").build())
                .phoneNumber("0669696969")
                .situation(Situation.EMPLOYEE)
                .salary(2500)
                .build();
        var user2 = userProfileGenerator.createUserProfileWithChannelsAndCapacitiesAndGeneralInfos(user2_uuid, 1, List.of("T-01"), user2_generalInfos, capacity);
        var user3_uuid = UUID.fromString("ca5ecca9-6d62-4ba5-89e1-66b0ed0822c0");
        var user3_generalInfos = GeneralInformation.builder().userId(user3_uuid.toString()).build();
        userProfileGenerator.createUserProfileWithChannelsAndCapacitiesAndGeneralInfos(user3_uuid, 1, List.of("T-01"), user3_generalInfos, capacity);

        txHelper.doInTransaction(() -> userProfileRepository.findById(user3_uuid).orElseThrow().updateJobOfferOptOut(true));
        mvc.perform(MockMvcRequestBuilders.get("/api/odas/job/" + job.getId() + "/matching-users/export")
                        .param("capacityThreshold", "0.99")
                        .param("organizationCodes", "T-01")
                        .param("masteryLevelRange", "1.00")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("text/csv"))
                .andExpect(stringMatchesContent(expectedContentFile));
    }

}
