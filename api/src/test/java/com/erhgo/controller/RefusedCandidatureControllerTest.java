package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.TransactionTestHelper;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.CandidatureEmailRefusalState;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.openapi.dto.CustomEmailTemplateDTO;
import com.erhgo.openapi.dto.RefuseCandidatureCommandDTO;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.mailing.MailingListService;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.concurrent.CompletableFuture;

import static com.erhgo.generators.TestFixtures.*;
import static java.time.temporal.ChronoUnit.MINUTES;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.within;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class RefusedCandidatureControllerTest extends AbstractIntegrationTestWithFixtures {
    private static final String USER_ID = "user_id";
    @Autowired
    private TransactionTestHelper txHelper;

    @Autowired
    private RecruitmentCandidatureRepository recruitmentCandidatureRepository;

    @MockitoBean
    private MailingListService mailingListService;

    @Autowired
    private KeycloakMockService keycloakMockService;

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN, id = USER_ID)
    @ResetDataAfter
    void should_refuse_candidature_without_email_as_admin() throws Exception {
        refuseCandidatureCommon();
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE}, id = USER_ID)
    @ResetDataAfter
    void should_refuse_candidature_without_email_as_rh() throws Exception {
        refuseCandidatureCommon();
    }

    private void refuseCandidatureCommon() throws Exception {
        var refuseCandidatureCommandDTO = new RefuseCandidatureCommandDTO();
        refuseCandidatureCommandDTO.setCandidatureId(CANDIDATURE_MATCHING.getId());

        mvc.perform(post("/api/odas/candidature/refuse")
                        .content(objectMapper.writeValueAsBytes(refuseCandidatureCommandDTO))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent())
                .andReturn();

        Mockito.verifyNoInteractions(mailingListService);

        txHelper.doInTransaction(() -> {
            var candidature = getCandidature(CANDIDATURE_MATCHING.getId());
            assertThat(candidature.getCandidatureRefusalState().getRefusalDate()).isCloseToUtcNow(within(1, MINUTES));
            assertThat(candidature.getCandidatureRefusalState().getEmailSent()).isEqualTo(CandidatureEmailRefusalState.NONE);
            assertThat(candidature.getCandidatureRefusalState().getRefusedBy()).isEqualTo(USER_ID);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void should_refuse_candidature_with_exception() throws Exception {
        Mockito.when(mailingListService.sendMail(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(CompletableFuture.failedFuture(new GenericTechnicalException("email failure")));

        Exception expectedException = null;
        mvc.perform(post("/api/odas/candidature/refuse")
                        .content(objectMapper.writeValueAsBytes(getRefuseCandidatureCommandDTO()))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var candidature = getCandidature(CANDIDATURE_MATCHING.getId());
            assertThat(candidature.getCandidatureRefusalState().getEmailSent()).isEqualTo(CandidatureEmailRefusalState.ERROR);
        });

    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void should_refuse_candidature_and_send_email() throws Exception {
        Mockito.when(mailingListService.sendMail(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(CompletableFuture.completedFuture(true));

        mvc.perform(post("/api/odas/candidature/refuse")
                        .content(objectMapper.writeValueAsBytes(getRefuseCandidatureCommandDTO()))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent())
                .andReturn();

        Mockito.verify(mailingListService).sendMail(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString());

        txHelper.doInTransaction(() -> {
            var candidature = getCandidature(CANDIDATURE_MATCHING.getId());
            assertThat(candidature.getCandidatureRefusalState().getEmailSent()).isEqualTo(CandidatureEmailRefusalState.DONE);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void should_refuse_candidature_waiting_send_email_admin() throws Exception {
        refuseCandidatureWithEmailCommon();
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    @ResetDataAfter
    void should_refuse_candidature_waiting_send_email_rh() throws Exception {
        refuseCandidatureWithEmailCommon();
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    @ResetDataAfter
    void should_refuse_candidature_waiting_send_email_ot() throws Exception {
        refuseCandidatureWithEmailCommon();
    }


    private void refuseCandidatureWithEmailCommon() throws Exception {
        var futureThatNeverCompletes = new CompletableFuture<Boolean>();
        Mockito.when(mailingListService.sendMail(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(futureThatNeverCompletes);

        mvc.perform(post("/api/odas/candidature/refuse")
                        .content(objectMapper.writeValueAsBytes(getRefuseCandidatureCommandDTO()))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent())
                .andReturn();
        var user = keycloakMockService.getFrontOfficeUserProfile(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE).orElseThrow();
        Mockito.verify(mailingListService).sendMail(
                user.getEmail(),
                user.getFirstName() + " " + user.getLastName(),
                getRefuseCandidatureCommandDTO().getEmailTemplate().getSubject(),
                getRefuseCandidatureCommandDTO().getEmailTemplate().getContent(),
                getRefuseCandidatureCommandDTO().getEmailTemplate().getEmailFrom(),
                getRefuseCandidatureCommandDTO().getEmailTemplate().getAuthorAlias()
        );

        txHelper.doInTransaction(() -> {
            var candidature = getCandidature(CANDIDATURE_MATCHING.getId());
            assertThat(candidature.getCandidatureRefusalState().getEmailSent()).isEqualTo(CandidatureEmailRefusalState.WAITING);
        });
    }


    private RecruitmentCandidature getCandidature(Long candidatureId) {

        return recruitmentCandidatureRepository.findById(candidatureId).orElseThrow();
    }

    private RefuseCandidatureCommandDTO getRefuseCandidatureCommandDTO() {
        var customEmailTemplateDTO = new CustomEmailTemplateDTO();
        customEmailTemplateDTO.emailFrom("test@localhost").authorAlias("erhgo").subject("refused candidature").content("sorry goodbye");
        var refuseCandidatureCommandDTO = new RefuseCandidatureCommandDTO();
        refuseCandidatureCommandDTO.setCandidatureId(CANDIDATURE_MATCHING.getId());
        refuseCandidatureCommandDTO.setEmailTemplate(customEmailTemplateDTO);
        return refuseCandidatureCommandDTO;
    }
}
