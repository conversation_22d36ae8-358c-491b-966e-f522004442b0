package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.config.ApiConstants;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.criteria.Criteria;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.BehaviorCategory;
import com.erhgo.domain.enums.CriteriaQuestionType;
import com.erhgo.domain.enums.JobEvaluationState;
import com.erhgo.domain.enums.SourcingCriteriaStep;
import com.erhgo.domain.job.*;
import com.erhgo.domain.recruitment.OptionalActivity;
import com.erhgo.domain.recruitment.OptionalContext;
import com.erhgo.domain.referential.*;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.generators.*;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.JobRepository;
import com.erhgo.repositories.RecruiterRepository;
import com.erhgo.repositories.RecruitmentProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.dtobuilder.BehaviorDTOBuilder;
import com.erhgo.services.keycloak.UserRepresentation;
import jakarta.validation.Valid;
import org.assertj.core.matcher.AssertionMatcher;
import org.hamcrest.Matcher;
import org.hamcrest.collection.IsIterableContainingInAnyOrder;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.ResultActions;

import java.time.OffsetDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Stream;

import static com.erhgo.generators.TestFixtures.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.is;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

class JobAPIControllerTest extends AbstractIntegrationTestWithFixtures {

    @Autowired
    private KeycloakMockService keycloakMockService;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private RecruiterRepository recruiterRepository;

    @Autowired
    private RecruitmentProfileRepository recruitmentProfileRepository;

    @Autowired
    private OrganizationGenerator organizationGenerator;

    @Autowired
    private JobGenerator jobGenerator;

    @Autowired
    private BehaviorGenerator behaviorGenerator;

    @Autowired
    private RecruitmentGenerator recruitmentGenerator;

    @Autowired
    private ContextGenerator contextGenerator;

    @Autowired
    private JobActivityLabelGenerator jobActivityLabelGenerator;

    @Autowired
    private CapacityGenerator capacityGenerator;

    @Autowired
    private ErhgoOccupationGenerator erhgoOccupationGenerator;


    @Autowired
    private ApplicationContext applicationContext;

    private static final String RECRUITER_CODE = "E-0045";
    private static final String EMPLOYER_CODE = "M-0045";
    private static final String CRITERIA_MULTIPLE_VALUE = "CM-2";
    private static final String CRITERIA_THRESHOLD_VALUE = "CT-2";

    private final LocationDTO locationDTO = new LocationDTO()
            .city("Lyon")
            .postcode("69001")
            .citycode("69123")
            .departmentCode("69")
            .regionName("Auvergne-Rhône-Alpes")
            .latitude(45.758f)
            .longitude(4.835f)
            .radiusInKm(30);

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void create_a_new_job_as_admin() throws Exception {
        doCreateJob("test", JobTypeDTO.OBSERVED);
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void create_a_new_job_as_external_consultant() throws Exception {
        doCreateJob("test", JobTypeDTO.OBSERVED);
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void create_a_new_simple_job_as_admin_without_service() throws Exception {
        doCreateJob(null, JobTypeDTO.SIMPLE);
    }

    private void doCreateJob(String service, JobTypeDTO jobType) throws Exception {
        var command = new SaveJobCommandDTO()
                .jobType(JobTypeDTO.OBSERVED)
                .id(UUID.randomUUID())
                .title("test")
                .recruiterCode(E_02_SOGILIS_CODE)
                .service(service)
                .location(locationDTO)
                .state(JobEvaluationStateDTO.INFOS_PROVIDED)
                .observators(Collections.singletonList("test"));

        mvc.perform(put(ApiConstants.API_ODAS_JOB)
                        .content(objectMapper.writeValueAsString(command))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> assertThat(jobRepository.findByRecruiterCodeInOrEmployerCodeIn(Collections.singleton(
                        command.getRecruiterCode()),
                PageRequest.of(0, 5))).isNotNull());
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    @DisplayName("Given a non modifiable job when state appears in command then job remains unmodifiable")
    void update_state_should_do_nothing() throws Exception {
        var job = jobGenerator.createJob("J-01", E_01_CERA, "title");

        var command = new SaveJobCommandDTO()
                .jobType(JobTypeDTO.OBSERVED)
                .id(job.getId())
                .title("newTitle")
                .service(job.getService())
                .state(JobEvaluationStateDTO.INFOS_PROVIDED)
                .recruiterCode(E_01_CERA.getCode());

        mvc.perform(put(ApiConstants.API_ODAS_JOB)
                        .content(objectMapper.writeValueAsString(command))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() ->
                assertThat(jobRepository.getOne(job.getId()).getState()).isEqualTo(JobEvaluationState.PUBLISHED)
        );
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    @DisplayName("Given a non modifiable job when LISTED state appears in command then job becomes modifiable")
    void update_state_should_do_something() throws Exception {
        var job = jobGenerator.createJob("J-01", E_01_CERA, "title");

        var command = new SaveJobCommandDTO()
                .jobType(JobTypeDTO.OBSERVED)
                .id(job.getId())
                .title("newTitle")
                .service(job.getService())
                .state(JobEvaluationStateDTO.LISTED)
                .recruiterCode(E_01_CERA.getCode());

        mvc.perform(put(ApiConstants.API_ODAS_JOB)
                        .content(objectMapper.writeValueAsString(command))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() ->
                assertThat(jobRepository.getOne(job.getId()).getState()).isEqualTo(JobEvaluationState.LISTED)
        );
    }

    @Test
    @WithMockKeycloakUser(id = "anotherAdmin", roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    @DisplayName("Job update preserves creator and organization and updates modifier")
    void update_job_preserves_creator_and_updates_organization_and_modifying_user() throws Exception {
        var userRepresentation = new UserRepresentation();
        userRepresentation.setId("anotherAdmin");
        keycloakMockService.setUserProfile("anotherAdmin", userRepresentation);

        var jobCommand = new SaveJobCommandDTO()
                .jobType(JobTypeDTO.OBSERVED)
                .id(J_01.getId())
                .title("updateAfterUpdate")
                .service("serviceAfterUpdate")
                .state(JobEvaluationStateDTO.INFOS_PROVIDED)
                .recruiterCode(E_01_CERA_CODE);

        mvc.perform(put(ApiConstants.API_ODAS_JOB)
                        .content(objectMapper.writeValueAsString(jobCommand))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var jobAfterUpdate = jobRepository.findById(jobCommand.getId()).orElseThrow();
            Assertions.assertAll("jobAfterUpdate",
                    () -> assertThat(jobAfterUpdate.getCreatedBy().getKeycloakId()).isEqualTo("uuid"),
                    () -> assertThat(jobAfterUpdate.getLastModifiedBy().getKeycloakId()).isEqualTo("anotherAdmin"),
                    () -> assertThat(jobAfterUpdate.getCreatedDate()).isNotNull(),
                    () -> assertThat(jobAfterUpdate.getUpdatedDate()).isNotNull(),
                    () -> assertThat(jobAfterUpdate.getRecruiterCode()).isEqualTo(E_01_CERA_CODE)
            );
        });
    }

    @Test
    @WithMockKeycloakUser
    void should_detect_missing_organizationCode_and_give_an_error() throws Exception {
        var command = new SaveJobCommandDTO()
                .jobType(JobTypeDTO.OBSERVED)
                .id(UUID.randomUUID())
                .title("test")
                .service("test")
                .location(locationDTO)
                .observators(Collections.singletonList("test"));
        mvc.perform(put(ApiConstants.API_ODAS_JOB)
                        .content(objectMapper.writeValueAsString(command))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().is(400));
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void should_set_behaviors_succeed_for_admin() throws Exception {
        setBehaviors();
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void should_set_behaviors_succeed_for_external_consultant() throws Exception {
        setBehaviors();
    }

    private void setBehaviors() throws Exception {
        var content = Arrays.asList(B_01.getId(), B_02.getId());
        mvc.perform(put(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + J_01.getId() + ApiConstants.SEPARATOR + "behavior")
                        .content(objectMapper.writeValueAsString(content))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var job = jobRepository.findById(J_01.getId()).orElseThrow();
            assertThat(job.getBehaviors()).hasSize(2);
            assertThat(job.getBehaviors()).contains(B_01, B_02);
        });
    }


    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void should_set_behaviors_with_unknown_job_id_return_not_found() throws Exception {
        var content = Arrays.asList(B_01.getId(), B_02.getId());
        mvc.perform(put(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + UUID.randomUUID() + ApiConstants.SEPARATOR + "behavior")
                        .content(objectMapper.writeValueAsString(content))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void should_set_behaviors_with_unknown_behavior_id_return_not_found() throws Exception {
        var content = Arrays.asList(B_01.getId(), UUID.randomUUID());
        mvc.perform(put(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + J_01.getId() + ApiConstants.SEPARATOR + "behavior")
                        .content(objectMapper.writeValueAsString(content))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void should_set_publicationDate_succeed() throws Exception {
        performPut(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + J_01.getId() + ApiConstants.SEPARATOR + "publish?isSimpleJobCreated=true", new PublishCommandDTO())
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var job = jobRepository.findById(J_01.getId()).orElseThrow();
            assertThat(job.getPublicationDate()).isNotNull();
            assertThat(job.getState()).isEqualTo(JobEvaluationState.PUBLISHED);
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void publish_creates_default_profile_for_simple_job() throws Exception {
        var job = jobGenerator.createJobWithCapacitiesAndLevel(JobType.SIMPLE, "J-74", E_02_SOGILIS, MasteryLevel.MIN_LEVEL, capacityGenerator.createCapacity("CA4-44*"));
        performPut(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + job.getId() + ApiConstants.SEPARATOR + "publish?isSimpleJobCreated=true", new PublishCommandDTO())
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var profiles = recruitmentProfileRepository.findByJobIdOrderByTitleAsc(job.getId());
            assertThat(profiles)
                    .hasSize(1)
                    .allMatch(p -> !p.getOptionalActivities().isEmpty() && p.getOptionalActivities().stream().map(OptionalActivity::getActivityLabel).toList().containsAll(job.getAllMissionsActivities()))
                    .allMatch(p -> !p.getOptionalContexts().isEmpty() && p.getOptionalContexts().stream().map(OptionalContext::getContext).toList().containsAll(job.getAllMissionsContexts()))
                    .allMatch(p -> p.isQualified() && !p.isModifiable())
            ;
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void publish_creates_default_profile_for_simple_job_and_custom_question() throws Exception {
        var customQuestion = "customQuestion ?";
        var job = jobGenerator.createJobWithCapacitiesAndLevel(JobType.SIMPLE, "J-74", E_02_SOGILIS, MasteryLevel.MIN_LEVEL, capacityGenerator.createCapacity("CA4-44*"));
        performPut(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + job.getId() + ApiConstants.SEPARATOR + "publish?isSimpleJobCreated=true", new PublishCommandDTO().recruitmentProfileCustomQuestion(customQuestion))
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var profiles = recruitmentProfileRepository.findByJobIdOrderByTitleAsc(job.getId());
            assertThat(profiles)
                    .hasSize(1)
                    .allMatch(p -> p.getCustomQuestion().equals(customQuestion));
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void publish_creates_no_profile_for_simple_job_with_already_one_profile() throws Exception {
        txHelper.doInTransaction(() -> jobRepository.findById(J_01.getId()).orElseThrow().setJobType(JobType.SIMPLE));
        var nbBeforeRepublish = recruitmentProfileRepository.countByJobId(J_01.getId());
        performPut(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + J_01.getId() + ApiConstants.SEPARATOR + "publish?isSimpleJobCreated=true", new PublishCommandDTO())
                .andExpect(status().isNoContent());
        assertThat(recruitmentProfileRepository.countByJobId(J_01.getId())).isEqualTo(nbBeforeRepublish);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void publish_not_creates_default_profile_for_simple_job() throws Exception {
        var job = jobGenerator.createJobWithCapacitiesAndLevel(JobType.SIMPLE, "J-74", E_02_SOGILIS, MasteryLevel.MIN_LEVEL, capacityGenerator.createCapacity("CA4-44*"));
        performPut(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + job.getId() + ApiConstants.SEPARATOR + "publish?isSimpleJobCreated=false", new PublishCommandDTO())
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var profiles = recruitmentProfileRepository.findByJobIdOrderByTitleAsc(job.getId());
            assertThat(profiles)
                    .isEmpty()
            ;
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void should_set_publicationDate_with_unknown_id_return_not_found() throws Exception {
        var content = Arrays.asList(BehaviorDTOBuilder.buildDTO(B_01), BehaviorDTOBuilder.buildDTO(B_02));
        mvc.perform(put(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + UUID.randomUUID() + ApiConstants.SEPARATOR + "publicationDate")
                        .content(objectMapper.writeValueAsString(content))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    void shouldUpdateAndRetrieveRecommendation() throws Exception {

        var newComment = "Hello World !";

        mvc.perform(put(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + J_01.getId() + ApiConstants.SEPARATOR + "recommendation")
                        .content(newComment)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        mvc.perform(get(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + J_01.getId() + ApiConstants.SEPARATOR + "detail")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.recommendation", is(newComment)));
    }

    @Test
    @WithMockKeycloakUser
    @ResetDataAfter
    void shouldRetrieveJobWithDetail() throws Exception {
        addCriteriaAndDomainToJob();

        var expectedJob = J_01;
        var resultActions = performGetAndExpect(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + J_01.getId() + ApiConstants.SEPARATOR + "detail", "jobDetail", false);
        checkDates(expectedJob, resultActions);
        checkIds(expectedJob, resultActions);
    }

    @Test
    @WithMockKeycloakUser
    @ResetDataAfter
    void retrieve_job_with_private_flag() throws Exception {
        txHelper.doInTransaction(() -> {
            recruiterRepository.findOneByCode(E_02_SOGILIS_CODE).setPrivateJobs(true);
        });

        var expectedJob = J_01;
        mvc.perform(get(realUrl("/job/" + J_01.getId() + "/detail")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.isPrivate", is(true)));
    }


    @Test
    @WithMockKeycloakUser
    @ResetDataAfter
    void retrievesJobWithDetails() throws Exception {
        var jobHolder = new AtomicReference<Job>();
        txHelper.doInTransaction(() -> {
            var occupation = new ErhgoOccupationMotherObject()
                    .withTitle("My occupation")
                    .instance();
            erhgoOccupationGenerator.createErhgoOccupation(occupation);
            var job = applicationContext.getBean(JobMotherObject.class)
                    .withTitle("Job with details")
                    .withLevel(MasteryLevel.MAX_LEVEL)
                    .withType(JobType.SIMPLE)
                    .withLocation(
                            Location.builder().citycode("59000").city("Lille").longitude(42f).latitude(24f).postcode("59123").departmentCode("59").regionName("Là-haut").radiusInKm(30).build()
                    )
                    .withState(JobEvaluationState.PUBLISHED)
                    .withCapacities(capacityGenerator.createCapacity("CA1-99"))
                    .buildAndPersist();
            job.setErhgoOccupation(occupation);
            jobHolder.set(job);
            assertThat(job.getTitle()).isEqualTo("Job with details");
        });
        performGetAndExpect("/job/" + jobHolder.get().getId() + "/detail", "jobDetailWithWithLocation", true);
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void shouldRetrieveJobCapacities() {
        performGetAndExpect("/api/odas/job/" + J_01.getId() + "/capacities", "jobCapacities", false);
    }

    private void checkIds(Job expectedJob, ResultActions resultActions) throws Exception {
        resultActions
                .andExpect(jsonPath("$.id", is(expectedJob.getId().toString())))
                .andExpect(jsonPath("$.behaviors[*].id", contains(expectedJob.getBehaviors().stream().map(Behavior::getId).map(UUID::toString).toList())))
                .andExpect(jsonPath("$.missions[*].id", contains(getMissionsStream(expectedJob).map(Mission::getId).map(Long::intValue).toList())))
                .andExpect(jsonPath("$.missions[*].jobId", contains(getMissionsStream(expectedJob).map(m -> m.getJob().getId().toString()).toList())))
                .andExpect(jsonPath("$.missions[*].activities[*].id", contains(getActivitiesStream(expectedJob).map(JobActivityLabel::getUuid).map(Object::toString).toList())))
                .andExpect(jsonPath("$.missions[*].activities[*].inducedCapacities[*].id", contains(getCapacitiesStream(expectedJob).map(Capacity::getId).map(Long::intValue).toList())))
                .andExpect(jsonPath("$.missions[*].contextsForCategory[*].contexts[*].id", contains(getContextsStream(expectedJob).map(Context::getId).map(UUID::toString).toList())))
                .andExpect(jsonPath("$.missions[*].contextsForCategory[*].contexts[*].categoryLevel.id", contains(getCategoryLevelsStream(expectedJob).map(CategoryLevel::getId).map(Long::intValue).toList())))
                .andExpect(jsonPath("$.missions[*].contextsForCategory[*].contexts[*].categoryLevel.category.id", contains(getCategoryLevelsStream(expectedJob).map(CategoryLevel::getCategory).map(Category::getId).map(Long::intValue).toList())))
                .andExpect(jsonPath("$.missions[*].contextsForCategory[*].category.id", contains(getCategoriesStream(expectedJob).map(Category::getId).map(Long::intValue).toList())));
    }

    private void checkDates(Job job, ResultActions resultActions) throws Exception {

        resultActions.andExpect(jsonPath("$.observationDate", new AssertionMatcher<String>() {
                    @Override
                    public void assertion(String actual) throws AssertionError {
                        assertThat(OffsetDateTime.parse(actual).toInstant().getEpochSecond()).isEqualTo(job.getObservationDate().toInstant().getEpochSecond());
                    }
                }))
                .andExpect(jsonPath("$.publicationDate", new AssertionMatcher<String>() {
                    @Override
                    public void assertion(String actual) throws AssertionError {
                        assertThat(OffsetDateTime.parse(actual).toInstant().getEpochSecond()).isEqualTo(job.getPublicationDate().toInstant().getEpochSecond());
                    }
                }));
    }

    private Stream<Category> getCategoriesStream(Job job) {
        return getMissionsStream(job).flatMap(m -> m.getContextsForCategory().stream()).map(ContextsForCategory::getCategory);
    }

    private Stream<CategoryLevel> getCategoryLevelsStream(Job job) {
        return getContextsStream(job).map(Context::getCategoryLevel);
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    void should_reorder_missions_without_error() throws Exception {
        final var initialMissionsIds = J_01.getMissions().stream().map(Mission::getId).toList();

        var content = objectMapper.writeValueAsString(Arrays.asList(initialMissionsIds.get(2), initialMissionsIds.get(0), initialMissionsIds.get(1)));

        mvc.perform(post("/api/odas/job/" + J_01.getId() + "/reorderMissions")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(content))
                .andExpect(status().isNoContent());

        // we check that the order changed as we wanted
        txHelper.doInTransaction(() -> assertThat(jobRepository.findById(J_01.getId()).orElseThrow().getMissions()).extracting("id").containsExactly(initialMissionsIds.get(2), initialMissionsIds.get(0), initialMissionsIds.get(1)));
    }


    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void reordering_mission_not_associated_with_job_should_error_400() throws Exception {
        var content = objectMapper.writeValueAsString(Arrays.asList(M_02.getId(), M_03.getId(), 404L));

        mvc.perform(post("/api/odas/job/" + J_01.getId() + "/reorderMissions")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(content))
                .andExpect(status().isBadRequest())
                .andExpect(content().string(containsString("is not associated to job")));
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void reordering_not_all_missions_should_error_400() throws Exception {
        var content = objectMapper.writeValueAsString(Collections.singletonList(M_02.getId()));

        mvc.perform(post("/api/odas/job/" + J_01.getId() + "/reorderMissions")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(content))
                .andExpect(status().isBadRequest())
                .andExpect(content().string(containsString("unexpected number of missions")));
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void set_working_time_type_for_job() throws Exception {
        var workingTimes = Arrays.asList(WorkingTimeDTO.FULL_TIME.toString(), WorkingTimeDTO.PART_TIME.toString());
        var content = objectMapper.writeValueAsString(workingTimes);

        mvc.perform(put("/api/odas/job/" + J_01.getId() + "/working-time-type")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(content))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var job = jobRepository.findById(J_01.getId()).orElseThrow(null);
            assertThat(job.getWorkingTimes().stream().map(Enum::toString)).containsAll(workingTimes);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void create_job_with_employer() throws Exception {
        var recruiter = organizationGenerator.createRecruiter(RECRUITER_CODE, AbstractOrganization.OrganizationType.TERRITORIAL);
        organizationGenerator.createEmployer(EMPLOYER_CODE, recruiter);

        var command = new SaveJobCommandDTO()
                .jobType(JobTypeDTO.OBSERVED)
                .id(UUID.randomUUID())
                .title("test")
                .recruiterCode(RECRUITER_CODE)
                .employerCode(EMPLOYER_CODE)
                .service("test")
                .location(locationDTO)
                .state(JobEvaluationStateDTO.INFOS_PROVIDED)
                .observators(Collections.singletonList("test"));

        mvc.perform(put(ApiConstants.API_ODAS_JOB)
                        .content(objectMapper.writeValueAsString(command))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var jobs = jobRepository.findByRecruiterCodeInOrEmployerCodeIn(
                    Collections.singleton(command.getRecruiterCode()),
                    PageRequest.of(0, 5));
            var job = jobs.iterator().next();
            Assertions.assertAll(
                    () -> assertThat(jobs.getTotalElements()).isEqualTo(1),
                    () -> assertThat(job.getEmployerCode()).isEqualTo(EMPLOYER_CODE),
                    () -> assertThat(job.getLocation()).isEqualTo(Location.buildLocationFromDTO(locationDTO))
            );
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void create_job_with_employer_not_associated_to_recruiter() throws Exception {
        organizationGenerator.createRecruiter(RECRUITER_CODE, AbstractOrganization.OrganizationType.TERRITORIAL);
        var otherRecruiter = organizationGenerator.createRecruiter("NOWAY", AbstractOrganization.OrganizationType.TERRITORIAL);
        organizationGenerator.createEmployer(EMPLOYER_CODE, otherRecruiter);

        var command = new SaveJobCommandDTO()
                .jobType(JobTypeDTO.OBSERVED)
                .id(UUID.randomUUID())
                .title("test")
                .recruiterCode(RECRUITER_CODE)
                .employerCode(EMPLOYER_CODE)
                .service("test")
                .location(locationDTO)
                .state(JobEvaluationStateDTO.INFOS_PROVIDED)
                .observators(Collections.singletonList("test"));

        mvc.perform(put(ApiConstants.API_ODAS_JOB)
                        .content(objectMapper.writeValueAsString(command))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void delete_job_when_job_does_not_exist_should_return_http_404() throws Exception {
        performDelete(String.format("/job/%s/delete", UUID.randomUUID()), null, new HashMap<>())
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_01_CERA_CODE})
    @ResetDataAfter
    void delete_job_even_when_recruitment_remains_should_return_http_204() throws Exception {
        var expectedJob = jobGenerator.createJobWithMission("J-424", E_01_CERA, CT_26, jobActivityLabelUsedOnlyInJobMission);
        expectedJob.setObservators(Set.of("Obs_1", "Obs_2"));
        expectedJob.setLocation(Location.builder().city("Site_1").build());
        var behavior1 = behaviorGenerator.createBehavior("B_01", "Title1", "Description", BehaviorCategory.RIGOR, 1);
        expectedJob.setBehaviors(Set.of(behavior1));
        jobRepository.save(expectedJob);
        recruitmentGenerator.createRecruitmentWithNoRequirement(expectedJob);

        performDelete(String.format("/job/%s/delete", expectedJob.getId()), null, new HashMap<>())
                .andExpect(status().isNoContent());

        assertThat(recruitmentProfileRepository.findByJobIdOrderByTitleAsc(expectedJob.getId())).isEmpty();
        assertThat(jobRepository.findById(expectedJob.getId())).isNotPresent();
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_01_CERA_CODE})
    @ResetDataAfter
    void delete_job_on_cascade_should_return_http_204() throws Exception {
        var expectedJob = jobGenerator.createJobWithMission("J-424", E_01_CERA, CT_26, jobActivityLabelUsedOnlyInJobMission);
        expectedJob.setObservators(Set.of("Obs_1", "Obs_2"));
        expectedJob.setLocation(Location.builder().city("Site_1").build());
        var behavior1 = behaviorGenerator.createBehavior("B_01", "Title1", "Description", BehaviorCategory.RIGOR, 1);
        expectedJob.setBehaviors(Set.of(behavior1));
        jobRepository.save(expectedJob);

        performDelete(String.format("/job/%s/delete", expectedJob.getId()), null, new HashMap<>())
                .andExpect(status().isNoContent());

        assertThat(recruitmentProfileRepository.findByJobIdOrderByTitleAsc(expectedJob.getId())).isEmpty();
        assertThat(jobRepository.findById(expectedJob.getId())).isNotPresent();
    }

    private Stream<Context> getContextsStream(Job job) {
        return getMissionsStream(job).flatMap(m -> m.getContextsForCategory().stream()).flatMap(c -> c.getContexts().stream());
    }

    private Stream<Capacity> getCapacitiesStream(Job job) {
        return getActivitiesStream(job).flatMap(a -> a.getInducedCapacities().stream());
    }

    private Stream<JobActivityLabel> getActivitiesStream(Job job) {
        return getMissionsStream(job).flatMap(m -> m.getActivities().stream());
    }

    private Stream<@Valid Mission> getMissionsStream(Job job) {
        return job.getMissions().stream();
    }

    <E> Matcher<Iterable<?>> contains(List<E> items) {
        return IsIterableContainingInAnyOrder.containsInAnyOrder(items.toArray(new Object[0]));
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    void set_criteria_for_external_consultant() throws Exception {
        setCriteria();
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void set_criteria_for_admin() throws Exception {
        setCriteria();
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void clear_criteria_for_admin() throws Exception {
        addCriteriaAndDomainToJob();
        mvc.perform(put(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + J_01.getId() + "/criteria")
                        .content(objectMapper.writeValueAsString(new ArrayList<>()))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var job = jobRepository.findById(J_01.getId()).orElseThrow();
            assertThat(job.getCriteriaValues()).isNullOrEmpty();
        });
    }

    private void addCriteriaAndDomainToJob() {
        txHelper.doInTransaction(() -> {
            var job = jobRepository.findById(J_01.getId()).orElseThrow();
            job.resetCriteriaValues(List.of(
                    createMultipleCriteria().getCriteriaValues().get(0),
                    createThresholdCriteria().getCriteriaValues().get(0))
            );
        });
    }

    private void setCriteria() throws Exception {

        createMultipleCriteria();
        createThresholdCriteria();
        applicationContext.getBean(CriteriaMotherObject.class).buildAndPersist();

        var content = Arrays.asList(CRITERIA_MULTIPLE_VALUE, CRITERIA_THRESHOLD_VALUE);

        mvc.perform(put(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + J_01.getId() + "/criteria")
                        .content(objectMapper.writeValueAsString(content))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var job = jobRepository.findById(J_01.getId()).orElseThrow();
            assertThat(job.getCriteriaValues()).hasSize(2);
            assertThat(job.getCriteriaValues()).extracting(CriteriaValue::getCode).containsExactlyInAnyOrder(CRITERIA_MULTIPLE_VALUE, CRITERIA_THRESHOLD_VALUE);
        });
    }

    private Criteria createThresholdCriteria() {
        return applicationContext.getBean(CriteriaMotherObject.class).withValueCodes("CT-1", CRITERIA_THRESHOLD_VALUE).withQuestionType(CriteriaQuestionType.THRESHOLD).buildAndPersist();
    }

    private Criteria createMultipleCriteria() {
        return applicationContext.getBean(CriteriaMotherObject.class).withSourcingCriteriaStep(SourcingCriteriaStep.STEP1).withValueCodes("CM-1", CRITERIA_MULTIPLE_VALUE).withQuestionType(CriteriaQuestionType.MULTIPLE).buildAndPersist();
    }

}
