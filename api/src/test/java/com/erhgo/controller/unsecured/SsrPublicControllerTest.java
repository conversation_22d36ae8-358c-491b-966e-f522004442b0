package com.erhgo.controller.unsecured;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TestUtils;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.userprofile.Location;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.Date;

class SsrPublicControllerTest extends AbstractIntegrationTest {

    @Autowired
    RecruitmentMotherObject recruitmentMotherObject;


    @SneakyThrows
    @Test
    @ResetDataAfter
    void ensureCardIsAccessible() {
        var recruitment = recruitmentMotherObject.withLocation(Location.builder()
                        .city("Pau")
                        .postcode("3200")
                        .build())
                .withRecruiterTitle("MacDo")
                .withJobTitle("Serveur")
                .withTitle("serveur")
                .withPublicationDate(new Date(1_000_000_000_000L))
                .buildAndPersist();
        mvc.perform(MockMvcRequestBuilders.get("/ssr/public/job/%s".formatted(recruitment.getCode())))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(TestUtils.stringMatchesContent("recruitmentSsrView.html"));
    }
}
