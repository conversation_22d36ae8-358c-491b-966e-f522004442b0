package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TestUtils;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.CandidatureRecruitmentState;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.generators.RecruitmentGenerator;
import com.erhgo.generators.UserProfileGenerator;
import com.erhgo.openapi.dto.GenerateCandidaturesOnRecruitmentsCommandDTO;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

// Temporary disabled, see https://erhgo.atlassian.net/browse/ERHGO-2009
@Disabled
class CandidatureGenerationControllerTest extends AbstractIntegrationTest {

    @Autowired
    private RecruitmentGenerator recruitmentGenerator;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Autowired
    private RecruitmentCandidatureRepository recruitmentCandidatureRepository;

    @Autowired
    private CapacityGenerator capacityGenerator;

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    @DisplayName("Given a user without capacity when a candidature is generated then user is valid and no activity match")
    void generateCandidature_generates_candidature_activities_without_match() throws Exception {
        var capacity = capacityGenerator.createCapacity("CA-1");
        var recruitment = recruitmentGenerator.createRecruitmentWithNoRequirement("J-42", capacity);
        var user = userProfileGenerator.createUserProfile();
        var recruitmentsId
                = List.of(recruitment.getId());
        var command = new GenerateCandidaturesOnRecruitmentsCommandDTO().userId(user.userId()).recruitmentsId(recruitmentsId);

        postAndAssertOK(recruitment, command);

        txHelper.doInTransaction(() -> {
            var candidature = recruitmentCandidatureRepository.findByRecruitmentCodeAndUserProfileUserId(recruitment.getCode(), user.userId()).orElseThrow();
            assertAll(
                    () -> assertThat(candidature.getValid()).isTrue(),
                    () -> assertThat(candidature.getCandidatureRecruitmentState()).isEqualTo(CandidatureRecruitmentState.NEW)
            );
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    @DisplayName("Given a user with capacity when a candidature is generated then user is valid and activity matches")
    void generateCandidature_generates_candidature_activities_with_match() throws Exception {
        generateCandidature_generates_candidature_activities_with_match_common();
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    @DisplayName("Given an OT authenticated user and a FO user with capacity when a candidature is generated then user is valid and activity matches")
    void generateCandidature_generates_candidature_activities_with_match_for_OT() throws Exception {
        generateCandidature_generates_candidature_activities_with_match_common();
    }

    private void generateCandidature_generates_candidature_activities_with_match_common() throws Exception {
        var capacity = capacityGenerator.createCapacity("CA-1");
        var recruitment = recruitmentGenerator.createRecruitmentWithNoRequirement("J-42", capacity);
        var user = userProfileGenerator.createUserProfileWithCapacities(MasteryLevel.MAX_LEVEL.getMasteryLevel(), capacity);
        var recruitmentsId
                = List.of(recruitment.getId());
        var command = new GenerateCandidaturesOnRecruitmentsCommandDTO().userId(user.userId()).recruitmentsId(recruitmentsId);

        postAndAssertOK(recruitment, command);

        txHelper.doInTransaction(() -> {
            var candidature = recruitmentCandidatureRepository.findByRecruitmentCodeAndUserProfileUserId(recruitment.getCode(), user.userId()).orElseThrow();
            assertAll(
                    () -> assertThat(candidature.getValid()).isTrue());
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    @DisplayName("Generate several candidatures on differents recruitments for user")
    void generate_candidatures_for_recruitments_in_bo() throws Exception {
        var capacity = capacityGenerator.createCapacity("CA-1");
        var recruitment = recruitmentGenerator.createRecruitmentWithNoRequirement("J-42", capacity);
        var recruitmentBis = recruitmentGenerator.createRecruitmentWithNoRequirement("J-43", capacity);
        var user = userProfileGenerator.createUserProfileWithCapacities(MasteryLevel.MAX_LEVEL.getMasteryLevel(), capacity);
        var recruitmentsId
                = List.of(recruitment.getId(), recruitmentBis.getId());
        var recruitments = List.of(recruitment, recruitmentBis);
        var command = new GenerateCandidaturesOnRecruitmentsCommandDTO().userId(user.userId()).recruitmentsId(recruitmentsId);

        postAndAssertSeveralOK(recruitments, command);

    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    @DisplayName("Given a candidate and an admin connected when I preview candidature then a transient matching result is computed")
    void preview_candidature_as_admin() throws Exception {
        previewCandidature();
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, OrganizationGenerator.ORGANIZATION_CODE})
    @ResetDataAfter
    @DisplayName("Given a candidate and an OT authenticated user when I preview candidature then a transient matching result is computed")
    void preview_candidature_as_ot() throws Exception {
        previewCandidature();
    }

    private void previewCandidature() throws Exception {
        var masteredCapacity = capacityGenerator.createCapacity("CA-1");
        var notMasteredCapacity1 = capacityGenerator.createCapacity("CA-2");
        var notMasteredCapacity2 = capacityGenerator.createCapacity("CA-3");
        var recruitment = recruitmentGenerator.createRecruitmentWithNoRequirement("J-42",
                masteredCapacity,
                notMasteredCapacity1,
                notMasteredCapacity2,
                capacityGenerator.createCapacity("CA-4"),
                capacityGenerator.createCapacity("CA-5"),
                capacityGenerator.createCapacity("CA-6")
        );
        var user = userProfileGenerator.createUserProfileWithLevelAndCapacities(new Integer[]{
                MasteryLevel.MAX_LEVEL.getMasteryLevel(),
                MasteryLevel.MIN_LEVEL.getMasteryLevel()
        }, List.of(masteredCapacity), List.of(notMasteredCapacity1, notMasteredCapacity2));

        mvc.perform(MockMvcRequestBuilders.get("/api/odas/candidature/for-job/"
                        + recruitment.getJob().getId()
                        + "/preview-for-user/"
                        + user.userId()))
                .andExpect(status().isOk())
                .andExpect(TestUtils.jsonMatchesContent("candidaturePreview"));
    }

    private void postAndAssertOK(Recruitment recruitment, GenerateCandidaturesOnRecruitmentsCommandDTO command) throws Exception {
        performPost("/candidature/generate", command)
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].jobId", Matchers.is(recruitment.getJob().getId().toString())))
                .andExpect(jsonPath("$[0].candidatureId", Matchers.not(Matchers.emptyOrNullString())))
                .andExpect(jsonPath("$[0].recruitmentId", Matchers.is(recruitment.getId().intValue())))
                .andExpect(jsonPath("$[*]", Matchers.hasSize(1)));
    }

    private void postAndAssertSeveralOK(List<Recruitment> recruitments, GenerateCandidaturesOnRecruitmentsCommandDTO command) throws Exception {
        performPost("/candidature/generate", command)
                .andExpect(status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$[*].jobId", Matchers.containsInAnyOrder(
                        recruitments.stream().map(r -> r.getJob().getId().toString()).toArray(String[]::new))))
                .andExpect(MockMvcResultMatchers.jsonPath("$[*].recruitmentId", Matchers.containsInAnyOrder(
                        recruitments.stream().map(r -> r.getId().intValue()).toArray(Integer[]::new))))
                .andExpect(MockMvcResultMatchers.jsonPath("$[*].candidatureId", Matchers.hasSize(2)))
                .andExpect(jsonPath("$[*]", Matchers.hasSize(2)));
    }

}
