package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.generators.UserProfileGenerator;
import com.erhgo.openapi.dto.SetUserErhgoClassificationCommandDTO;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.repositories.classifications.ErhgoClassificationRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.apache.commons.lang3.BooleanUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class UserErhgoClassificationsControllerTest extends AbstractIntegrationTest {

    @Autowired
    UserProfileGenerator userProfileGenerator;

    @Autowired
    UserProfileRepository userProfileRepository;

    @Autowired
    ErhgoClassificationRepository erhgoClassificationRepository;

    @Autowired
    ApplicationContext applicationContext;

    static final String UUID = "8ec4e001-d76e-41bc-8f7b-5d8e8bcd3ed4";

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @NullSource
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void addErhgoClassificationToUser(Boolean isAccepted) throws Exception {
        var user = applicationContext.getBean(UserProfileMotherObject.class).withErhgoClassification("SO-02", true).buildAndPersist();
        var command = new SetUserErhgoClassificationCommandDTO().userId(user.userId()).erhgoClassificationCode("SO-01").isAccepted(isAccepted);
        performPost("/user/erhgo-classification", command).andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var userProfile = userProfileRepository.findByUserId(user.userId()).orElseThrow();
            assertThat(userProfile).extracting(UserProfile::indexationRequiredDate).isNotNull();
            assertThat(userProfile.erhgoClassifications()).matches(l ->
                    l.size() == (isAccepted == null ? 1 : 2)
                            && l.stream().anyMatch(c -> c.isAccepted() && c.getErhgoClassification().getCode().equals("SO-02"))
                            && l.stream().anyMatch(c -> isAccepted == null || (isAccepted == c.isAccepted()) && c.getErhgoClassification().getCode().equals("SO-01"))
            );
        });
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @NullSource
    @WithMockKeycloakUser(roles = {Role.CANDIDATE}, id = UUID)
    @ResetDataAfter
    void updateErhgoClassificationToUser(Boolean isAccepted) throws Exception {
        var user = applicationContext.getBean(UserProfileMotherObject.class).withUserId(UUID).withErhgoClassification("SO-01", BooleanUtils.isFalse(isAccepted)).buildAndPersist();
        var command = new SetUserErhgoClassificationCommandDTO().userId(user.userId()).erhgoClassificationCode("SO-01").isAccepted(isAccepted);
        performPost("/user/erhgo-classification", command).andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var userProfile = userProfileRepository.findByUserId(user.userId()).orElseThrow();
            assertThat(userProfile).extracting(UserProfile::indexationRequiredDate).isNotNull();
            assertThat(userProfile.erhgoClassifications()).matches(l ->
                    l.size() == (isAccepted == null ? 0 : 1)
                            && (isAccepted == null || l.stream().anyMatch(c -> (isAccepted == c.isAccepted()) && c.getErhgoClassification().getCode().equals("SO-01")))
            );
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.CANDIDATE}, id = UUID)
    @ResetDataAfter
    void getWorkDomainsOfUsers() {
        var user = applicationContext.getBean(UserProfileMotherObject.class)
                .withErhgoClassification("SO-01", true)
                .withErhgoClassification("SO-02", false)
                .withUserId(UUID)
                .buildAndPersist();

        performGetAndExpect("/user/" + user.userId() + "/erhgo-classifications", "userErhgoClassifications", false);
    }

}
