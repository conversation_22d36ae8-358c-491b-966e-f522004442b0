package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.CriteriaQuestionType;
import com.erhgo.domain.userprofile.criteria.UserCriteriaValue;
import com.erhgo.generators.CriteriaMotherObject;
import com.erhgo.openapi.dto.SaveUserCriteriasCommandDTO;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import jakarta.persistence.EntityManager;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.Collections;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.erhgo.generators.CriteriaMotherObject.absentThresholdValue;

class UserCriteriaFOControllerTest extends AbstractIntegrationTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private EntityManager entityManager;

    private static final String USER_ID = "cf6c1057-dbc4-4297-856a-ee6a0cd5d6ce";

    @Test
    @WithMockKeycloakUser(roles = Role.CANDIDATE, id = USER_ID)
    @ResetDataAfter
    void answer_to_multiple_criteria() throws Exception {
        applicationContext.getBean(CriteriaMotherObject.class).prepareMultipleCriteriaAndAnswers(USER_ID);

        performPost("/user/criterias", new SaveUserCriteriasCommandDTO()
                .userId(USER_ID)
                .selectedValueCodes(List.of(CriteriaMotherObject.absentCriteria1Value, CriteriaMotherObject.selectedMultipleValue2, CriteriaMotherObject.unselectedMultipleValue1))
                .unselectedValueCodes(List.of(CriteriaMotherObject.selectedMultipleValue1)))
                .andExpect(MockMvcResultMatchers.status().isNoContent());

        txHelper.doInTransaction(() -> {
            List<UserCriteriaValue> values = entityManager.createQuery("SELECT v FROM UserCriteriaValue v").getResultList();
            Assertions.assertThat(values).hasSize(7);
            var multipleValues = values.stream()
                    .filter(a -> a.getValue().getCriteria().getQuestionType() == CriteriaQuestionType.MULTIPLE && a.getUserProfile().userId().equals(USER_ID))
                    .collect(Collectors.toSet());
            Assertions.assertThat(multipleValues)
                    .hasSize(4)
                    .filteredOn(UserCriteriaValue::isSelected)
                    .hasSize(3)
                    .extracting(UserCriteriaValue::getValue)
                    .extracting(CriteriaValue::getCode)
                    .containsExactlyInAnyOrder(CriteriaMotherObject.absentCriteria1Value, CriteriaMotherObject.selectedMultipleValue2, CriteriaMotherObject.unselectedMultipleValue1);

            Assertions.assertThat(multipleValues).filteredOn(Predicate.not(UserCriteriaValue::isSelected))
                    .extracting(UserCriteriaValue::getValue)
                    .extracting(CriteriaValue::getCode)
                    .containsExactlyInAnyOrder(CriteriaMotherObject.selectedMultipleValue1);

        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.CANDIDATE, id = USER_ID)
    @ResetDataAfter
    void answer_to_threshold_criteria() throws Exception {

        applicationContext.getBean(CriteriaMotherObject.class).prepareMultipleCriteriaAndAnswers(USER_ID);

        performPost("/user/criterias", new SaveUserCriteriasCommandDTO()
                .userId(USER_ID)
                .unselectedValueCodes(Collections.emptyList())
                .selectedValueCodes(List.of(absentThresholdValue)))
                .andExpect(MockMvcResultMatchers.status().isNoContent());

        txHelper.doInTransaction(() -> {
            List<UserCriteriaValue> values = entityManager.createQuery("SELECT v FROM UserCriteriaValue v").getResultList();
            Assertions.assertThat(values).hasSize(6);
            var thresholdValues = values.stream()
                    .filter(a -> a.getValue().getCriteria().getQuestionType() == CriteriaQuestionType.THRESHOLD && a.getUserProfile().userId().equals(USER_ID))
                    .collect(Collectors.toSet());
            Assertions.assertThat(thresholdValues)
                    .hasSize(1)
                    .filteredOn(UserCriteriaValue::isSelected)
                    .extracting(UserCriteriaValue::getValue)
                    .extracting(CriteriaValue::getCode)
                    .containsExactlyInAnyOrder(absentThresholdValue);
        });
    }
}
