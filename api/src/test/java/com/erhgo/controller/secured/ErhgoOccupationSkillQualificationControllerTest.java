package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.config.ApiConstants;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.*;
import com.erhgo.domain.classifications.esco.EscoSkill;
import com.erhgo.domain.referential.Behavior;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.generators.ErhgoOccupationGenerator;
import com.erhgo.generators.TestFixtures;
import com.erhgo.openapi.dto.SkillLinkToActivityCommandDTO;
import com.erhgo.openapi.dto.SkillLinkToBehaviorCommandDTO;
import com.erhgo.openapi.dto.SkillLinkToContextCommandDTO;
import com.erhgo.openapi.dto.SkillSetBooleanValueCommandDTO;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;

import java.util.UUID;

import static com.erhgo.config.ApiConstants.SEPARATOR;
import static com.erhgo.generators.TestFixtures.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


class ErhgoOccupationSkillQualificationControllerTest extends AbstractIntegrationTestWithFixtures {

    @Autowired
    private ErhgoOccupationRepository erhgoOccupationRepository;

    @Autowired
    private ErhgoOccupationGenerator erhgoOccupationGenerator;

    private ErhgoOccupation notQualifiedOccupation, qualifiedOccupation, otherOccupation;

    private EscoSkill skill;
    JobActivityLabel initialSkillActivity, initialManualActivity;
    Context initialSkillContext, initialManualContext;
    Behavior initialSkillBehavior, initialManualBehavior;

    @BeforeEach
    void initDatas() {
        initialSkillActivity = ACT_25;
        initialManualActivity = ACT_12;
        initialManualContext = CT_21;
        initialSkillContext = CT_03;
        initialManualBehavior = B_02;
        initialSkillBehavior = B_05;

        skill = erhgoOccupationGenerator.createSkillWithReferentialData("Partially qualified", initialSkillActivity, initialSkillBehavior, initialSkillContext);
        notQualifiedOccupation = buildOccupation(false, skill);
        qualifiedOccupation = buildOccupation(true, skill);
        otherOccupation = buildOccupation(true);
    }

    private ErhgoOccupation buildOccupation(boolean qualifiedState, EscoSkill... skills) {
        return erhgoOccupationGenerator.createErhgoOccupationForReferentialDatas(
                UUID.randomUUID(),
                qualifiedState,
                initialSkillActivity,
                initialManualActivity,
                initialSkillContext,
                initialManualContext,
                initialSkillBehavior,
                initialManualBehavior, skills
        );
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void addActivityToSkill_should_add_to_not_qualified_jobs_and_not_to_qualified_ones() throws Exception {
        var skillAddedActivity = TestFixtures.jobActivityLabelUsedOnlyInJobMission;

        var requestBody = new SkillLinkToActivityCommandDTO()
                .uri(skill.getUri())
                .activityId(skillAddedActivity.getUuid());

        performPost(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToActivity", requestBody)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.getOne(notQualifiedOccupation.getId());
            assertThat(updatedOccupation.getOccupationActivities()).hasSize(3);
            var activity = updatedOccupation.getOccupationActivities().stream().filter(a -> a.getActivity().equals(skillAddedActivity)).findFirst().orElseThrow();
            assertThat(activity.getState()).isEqualTo(MandatoryState.OPTIONAL);
            assertThat(activity.getSource()).isEqualTo(OccupationQualificationSource.SKILL);

            // Occupation not referencing skill should not be modified
            assertThat(erhgoOccupationRepository.getOne(otherOccupation.getId()).getOccupationActivities()).hasSize(2);
            // Qualified occupation should not be modified
            assertThat(erhgoOccupationRepository.getOne(qualifiedOccupation.getId()).getOccupationActivities()).hasSize(2);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void removeActivityFromSkill_should_remove_from_not_qualified_jobs_and_not_from_qualified_ones() throws Exception {
        var requestBody = new SkillLinkToActivityCommandDTO()
                .uri(skill.getUri())
                .activityId(initialSkillActivity.getUuid());

        mvc.perform(delete(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToActivity")
                        .content(objectMapper.writeValueAsString(requestBody))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        verifyActivityRemoval();
    }

    private void verifyActivityRemoval() {
        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.getOne(notQualifiedOccupation.getId());
            assertThat(updatedOccupation.getOccupationActivities()).hasSize(1);
            var activity = updatedOccupation.getOccupationActivities().iterator().next();
            assertThat(activity.getActivity()).isEqualTo(initialManualActivity);
            assertThat(activity.getState()).isEqualTo(MandatoryState.OPTIONAL);
            assertThat(activity.getSource()).isEqualTo(OccupationQualificationSource.MANUAL);

            // Occupation not referencing skill should not be modified
            assertThat(erhgoOccupationRepository.getOne(otherOccupation.getId()).getOccupationActivities()).hasSize(2);
            // Qualified occupation should not be modified
            assertThat(erhgoOccupationRepository.getOne(qualifiedOccupation.getId()).getOccupationActivities()).hasSize(2);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void removeActivityFromSkill_should_not_remove_from_not_qualified_jobs_having_multiple_skills_with_same_activity() throws Exception {
        var occupationWithMultipleSkills = buildOccupation(false,
                skill,
                erhgoOccupationGenerator.createSkillWithReferentialData("Another partially qualified", initialSkillActivity, initialSkillBehavior));

        var requestBody = new SkillLinkToActivityCommandDTO()
                .uri(skill.getUri())
                .activityId(initialSkillActivity.getUuid());

        mvc.perform(delete(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToActivity")
                        .content(objectMapper.writeValueAsString(requestBody))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.getOne(occupationWithMultipleSkills.getId());
            assertThat(updatedOccupation.getOccupationActivities().stream().map(OccupationActivity::getActivity)).containsExactlyInAnyOrder(initialSkillActivity, initialManualActivity);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void setNoActivity_to_skill_should_remove_all_activities_on_not_qualified_occupation() throws Exception {
        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "detail" + SEPARATOR + "setNoActivity")
                        .content(objectMapper.writeValueAsString(new SkillSetBooleanValueCommandDTO().uri(skill.getUri()).value(true)))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        verifyActivityRemoval();
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void setNoActivity_false_should_do_nothing() throws Exception {
        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "detail" + SEPARATOR + "setNoActivity")
                        .content(objectMapper.writeValueAsString(new SkillSetBooleanValueCommandDTO().uri(skill.getUri()).value(false)))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            assertThat(erhgoOccupationRepository.getOne(notQualifiedOccupation.getId()).getOccupationActivities()).hasSize(2);
            // Occupation not referencing skill should not be modified
            assertThat(erhgoOccupationRepository.getOne(otherOccupation.getId()).getOccupationActivities()).hasSize(2);
            // Qualified occupation should not be modified
            assertThat(erhgoOccupationRepository.getOne(qualifiedOccupation.getId()).getOccupationActivities()).hasSize(2);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void addContextToSkill_should_add_to_not_qualified_jobs_and_not_to_qualified_ones() throws Exception {
        var skillAddedContext = CT_02;

        var requestBody = new SkillLinkToContextCommandDTO()
                .uri(skill.getUri())
                .contextId(skillAddedContext.getId());

        performPost(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToContext", requestBody)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.getOne(notQualifiedOccupation.getId());
            assertThat(updatedOccupation.getOccupationContexts()).hasSize(3);
            var context = updatedOccupation.getOccupationContexts().stream().filter(a -> a.getContext().equals(skillAddedContext)).findFirst().orElseThrow();
            assertThat(context.getState()).isEqualTo(MandatoryState.OPTIONAL);
            assertThat(context.getSource()).isEqualTo(OccupationQualificationSource.SKILL);

            // Occupation not referencing skill should not be modified
            assertThat(erhgoOccupationRepository.getOne(otherOccupation.getId()).getOccupationContexts()).hasSize(2);
            // Qualified occupation should not be modified
            assertThat(erhgoOccupationRepository.getOne(qualifiedOccupation.getId()).getOccupationContexts()).hasSize(2);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void removeContextFromSkill_should_remove_from_not_qualified_jobs_and_not_from_qualified_ones() throws Exception {
        var requestBody = new SkillLinkToContextCommandDTO()
                .uri(skill.getUri())
                .contextId(initialSkillContext.getId());

        mvc.perform(delete(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToContext")
                        .content(objectMapper.writeValueAsString(requestBody))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        verifyContextRemoval();
    }

    private void verifyContextRemoval() {
        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.getOne(notQualifiedOccupation.getId());
            assertThat(updatedOccupation.getOccupationContexts()).hasSize(1);
            var context = updatedOccupation.getOccupationContexts().iterator().next();
            assertThat(context.getContext()).isEqualTo(initialManualContext);
            assertThat(context.getState()).isEqualTo(MandatoryState.ESSENTIAL);
            assertThat(context.getSource()).isEqualTo(OccupationQualificationSource.MANUAL);

            // Occupation not referencing skill should not be modified
            assertThat(erhgoOccupationRepository.getOne(otherOccupation.getId()).getOccupationContexts()).hasSize(2);
            // Qualified occupation should not be modified
            assertThat(erhgoOccupationRepository.getOne(qualifiedOccupation.getId()).getOccupationContexts()).hasSize(2);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void removeContextFromSkill_should_not_remove_from_not_qualified_jobs_having_multiple_skills_with_same_context() throws Exception {
        var occupationWithMultipleSkills = erhgoOccupationGenerator.createErhgoOccupationForReferentialDatas(
                UUID.randomUUID(),
                false,
                initialSkillActivity,
                initialManualActivity,
                initialSkillContext,
                initialManualContext,
                initialSkillBehavior, initialManualBehavior, skill,
                erhgoOccupationGenerator.createSkillWithReferentialData("Another partially qualified", initialSkillActivity, initialSkillBehavior, initialSkillContext)
        );

        var requestBody = new SkillLinkToContextCommandDTO()
                .uri(skill.getUri())
                .contextId(initialSkillContext.getId());

        mvc.perform(delete(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToContext")
                        .content(objectMapper.writeValueAsString(requestBody))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.getOne(occupationWithMultipleSkills.getId());
            assertThat(updatedOccupation.getOccupationContexts().stream().map(OccupationContext::getContext)).containsExactlyInAnyOrder(initialSkillContext, initialManualContext);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void setNoContext_to_skill_should_remove_all_activities_on_not_qualified_occupation() throws Exception {
        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "detail" + SEPARATOR + "setNoContext")
                        .content(objectMapper.writeValueAsString(new SkillSetBooleanValueCommandDTO().uri(skill.getUri()).value(true)))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        verifyContextRemoval();
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void setNoContext_false_should_do_nothing() throws Exception {
        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "detail" + SEPARATOR + "setNoContext")
                        .content(objectMapper.writeValueAsString(new SkillSetBooleanValueCommandDTO().uri(skill.getUri()).value(false)))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            assertThat(erhgoOccupationRepository.getOne(notQualifiedOccupation.getId()).getOccupationContexts()).hasSize(2);
            // Occupation not referencing skill should not be modified
            assertThat(erhgoOccupationRepository.getOne(otherOccupation.getId()).getOccupationContexts()).hasSize(2);
            // Qualified occupation should not be modified
            assertThat(erhgoOccupationRepository.getOne(qualifiedOccupation.getId()).getOccupationContexts()).hasSize(2);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void addBehaviorToSkill_should_add_to_not_qualified_jobs_and_not_to_qualified_ones() throws Exception {
        var skillAddedBehavior = B_08;

        var requestBody = new SkillLinkToBehaviorCommandDTO()
                .uri(skill.getUri())
                .behaviorId(skillAddedBehavior.getId());

        performPost(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToBehavior", requestBody)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.getOne(notQualifiedOccupation.getId());
            assertThat(updatedOccupation.getOccupationBehaviors()).hasSize(3);
            var behavior = updatedOccupation.getOccupationBehaviors().stream().filter(a -> a.getBehavior().equals(skillAddedBehavior)).findFirst().orElseThrow();
            assertThat(behavior.getSource()).isEqualTo(OccupationQualificationSource.SKILL);

            // Occupation not referencing skill should not be modified
            assertThat(erhgoOccupationRepository.getOne(otherOccupation.getId()).getOccupationBehaviors()).hasSize(2);
            // Qualified occupation should not be modified
            assertThat(erhgoOccupationRepository.getOne(qualifiedOccupation.getId()).getOccupationBehaviors()).hasSize(2);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void removeBehaviorFromSkill_should_remove_from_not_qualified_jobs_and_not_from_qualified_ones() throws Exception {
        var requestBody = new SkillLinkToBehaviorCommandDTO()
                .uri(skill.getUri())
                .behaviorId(initialSkillBehavior.getId());

        mvc.perform(delete(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToBehavior")
                        .content(objectMapper.writeValueAsString(requestBody))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        verifyBehaviorRemoval();
    }

    private void verifyBehaviorRemoval() {
        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.getOne(notQualifiedOccupation.getId());
            assertThat(updatedOccupation.getOccupationBehaviors()).hasSize(1);
            var behavior = updatedOccupation.getOccupationBehaviors().iterator().next();
            assertThat(behavior.getBehavior()).isEqualTo(initialManualBehavior);
            assertThat(behavior.getSource()).isEqualTo(OccupationQualificationSource.MANUAL);

            // Occupation not referencing skill should not be modified
            assertThat(erhgoOccupationRepository.getOne(otherOccupation.getId()).getOccupationBehaviors()).hasSize(2);
            // Qualified occupation should not be modified
            assertThat(erhgoOccupationRepository.getOne(qualifiedOccupation.getId()).getOccupationBehaviors()).hasSize(2);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void removeBehaviorFromSkill_should_not_remove_from_not_qualified_jobs_having_multiple_skills_with_same_behavior() throws Exception {
        var occupationWithMultipleSkills = erhgoOccupationGenerator.createErhgoOccupationForReferentialDatas(
                UUID.randomUUID(),
                false,
                initialSkillActivity,
                initialManualActivity,
                initialSkillContext,
                initialManualContext,
                initialSkillBehavior,
                initialManualBehavior,
                skill,
                erhgoOccupationGenerator.createSkillWithReferentialData("Another partially qualified", initialSkillActivity, initialSkillBehavior)
        );

        var requestBody = new SkillLinkToBehaviorCommandDTO()
                .uri(skill.getUri())
                .behaviorId(initialSkillBehavior.getId());

        mvc.perform(delete(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToBehavior")
                        .content(objectMapper.writeValueAsString(requestBody))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.getOne(occupationWithMultipleSkills.getId());
            assertThat(updatedOccupation.getOccupationBehaviors().stream().map(OccupationBehavior::getBehavior)).containsExactlyInAnyOrder(initialSkillBehavior, initialManualBehavior);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void setNoBehavior_to_skill_should_remove_all_activities_on_not_qualified_occupation() throws Exception {
        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "detail" + SEPARATOR + "setNoBehavior")
                        .content(objectMapper.writeValueAsString(new SkillSetBooleanValueCommandDTO().uri(skill.getUri()).value(true)))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        verifyBehaviorRemoval();
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void setNoBehavior_false_should_do_nothing() throws Exception {
        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "detail" + SEPARATOR + "setNoBehavior")
                        .content(objectMapper.writeValueAsString(new SkillSetBooleanValueCommandDTO().uri(skill.getUri()).value(false)))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            assertThat(erhgoOccupationRepository.getOne(notQualifiedOccupation.getId()).getOccupationBehaviors()).hasSize(2);
            // Occupation not referencing skill should not be modified
            assertThat(erhgoOccupationRepository.getOne(otherOccupation.getId()).getOccupationBehaviors()).hasSize(2);
            // Qualified occupation should not be modified
            assertThat(erhgoOccupationRepository.getOne(qualifiedOccupation.getId()).getOccupationBehaviors()).hasSize(2);
        });
    }

}
