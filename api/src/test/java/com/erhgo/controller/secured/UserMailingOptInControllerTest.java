package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TestUtils;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.openapi.dto.UpdateUserMailingListOptInCommandDTO;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailingListService;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.time.Instant;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
class UserMailingOptInControllerTest extends AbstractIntegrationTest {

    @MockitoBean
    MailingListService mailingListService;
    @Autowired
    private KeycloakMockService keycloakMockService;

    @Autowired
    ApplicationContext applicationContext;

    private static final String UUID = "bf5eee37-a6b8-43a4-8e38-b61b82296eac";
    private static final String EMAIL = "a@a";

    @BeforeEach
    void before() {
        var userRepresentation = new UserRepresentation();
        userRepresentation.setEmail(EMAIL);
        keycloakMockService.setUserProfile(UUID, userRepresentation);
        Mockito.when(mailingListService.getJobDatingOptIn(any())).thenReturn(true);
    }

    @ParameterizedTest
    @ValueSource(strings = {Role.ODAS_ADMIN, Role.CANDIDATE})
    @ResetDataAfter
    void getJobOfferOptIn(String role) {
        applicationContext.getBean(UserProfileMotherObject.class).withUserId(UUID).withJobOfferOptOut(false).buildAndPersist();
        TestUtils.mockAuthentication(EMAIL, UUID, role);
        performGetAndExpect("/user/job-offers-opt-in/" + UUID, "optIn", true);
    }

    @ParameterizedTest
    @ValueSource(strings = {Role.ODAS_ADMIN, Role.CANDIDATE})
    @ResetDataAfter
    void getJDOptIn(String role) {
        applicationContext.getBean(UserProfileMotherObject.class).withUserId(UUID).withJobOfferOptOut(false).buildAndPersist();
        TestUtils.mockAuthentication(EMAIL, UUID, role);
        performGetAndExpect("/user/job-dating-opt-in/" + UUID, "optIn", true);
    }

    @ParameterizedTest
    @ValueSource(strings = {Role.ODAS_ADMIN, Role.CANDIDATE})
    @ResetDataAfter
    void setJobOfferOptIn(String role) throws Exception {
        applicationContext.getBean(UserProfileMotherObject.class).withUserId(UUID).buildAndPersist();
        TestUtils.mockAuthentication(EMAIL, UUID, role);
        performPost("/user/job-offers-opt-in", new UpdateUserMailingListOptInCommandDTO().value(false).userId(UUID))
                .andExpect(status().isNoContent());
        verify(mailingListService).updateJobOffersOptIn(any());
        txHelper.doInTransaction(() -> {
            Assertions.assertThat(applicationContext.getBean(UserProfileRepository.class).findByUserId(UUID).orElseThrow().indexationRequiredDate()).isCloseTo(Instant.now(), 2000);
        });
    }


    @ParameterizedTest
    @ValueSource(strings = {Role.ODAS_ADMIN, Role.CANDIDATE})
    @ResetDataAfter
    void setJDOptIn(String role) throws Exception {
        applicationContext.getBean(UserProfileMotherObject.class).withUserId(UUID).withJobOfferOptOut(false).buildAndPersist();
        TestUtils.mockAuthentication(EMAIL, UUID, role);
        performPost("/user/job-dating-opt-in", new UpdateUserMailingListOptInCommandDTO().value(false).userId(UUID))
                .andExpect(status().isNoContent());
        verify(mailingListService).updateNewsOptIn(any(), eq(false));
    }

}
