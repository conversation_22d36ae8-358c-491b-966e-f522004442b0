package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.CandidatureEmailRefusalState;
import com.erhgo.domain.candidature.job.CandidatureRecruitmentState;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.BehaviorCategory;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.userprofile.*;
import com.erhgo.domain.userprofile.notification.RecruitmentNotification;
import com.erhgo.generators.*;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.UserMobileTokenRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.generation.HashtagsGenerationService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailingListService;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.search.UserIndexer;
import jakarta.persistence.EntityManager;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.mockito.internal.verification.VerificationModeFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.servlet.ResultActions;

import java.time.OffsetDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import static com.erhgo.TestUtils.jsonMatchesContent;
import static com.erhgo.domain.candidature.job.CandidatureRecruitmentState.*;
import static com.erhgo.generators.TestFixtures.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.*;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class UserControllerTest extends AbstractIntegrationTestWithFixtures {

    @Autowired
    private BehaviorGenerator behaviorGenerator;
    @Autowired
    private EntityManager entityManager;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Autowired
    private CapacityRelatedQuestionGenerator capacityRelatedQuestionGenerator;

    @Autowired
    private DataGeneratorService dataGeneratorService;

    @Autowired
    private CapacityGenerator capacityGenerator;

    @Autowired
    private JobActivityLabelGenerator jobActivityLabelGenerator;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @SpyBean
    private KeycloakMockService keycloakMockService;

    @MockitoBean
    private Notifier notifier;

    @MockitoBean
    private MailingListService mailingListService;

    @MockitoBean
    private UserIndexer userIndexer;

    @MockitoBean
    private HashtagsGenerationService hashtagsGenerationService;

    @Autowired
    ApplicationContext applicationContext;

    private static final String USER_ID = "test";
    private static final String USER_ID_KEYCLOAK = "uuid";

    private static final String NEW_USER_ID = "3c2a6fbf-e563-4b0b-9ae0-af62cfa12f8d";

    static final String FIRST_NAME = "firstName";
    static final String LAST_NAME = "lastName";
    static final String EMAIL = "email";
    private static final String NEW_TEXT = "new text";

    @BeforeEach
    void initUserChannel() {
        keycloakMockService.assignToFrontOfficeGroups(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, Set.of(E_02_SOGILIS_CODE));
        txHelper.doInTransaction(() -> {
            userProfileRepository.findByUserId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE).orElseThrow().updatedChannels(Set.of(E_02_SOGILIS_CODE), UserChannel.ChannelSourceType.UNKNOWN);
        });
    }

    @Test
    @WithMockKeycloakUser()
    void create_admin_user_fails_for_non_admin() throws Exception {
        var createUserCommandDTO = new CreateUserCommandDTO()
                .firstName(FIRST_NAME)
                .lastName(LAST_NAME)
                .email(EMAIL)
                .password("password");

        mvc.perform(post("/api/odas/user/create")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createUserCommandDTO)))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = {"ODAS_NOT_ADMIN"})
    void get_simple_candidatures_should_return_candidatures_with_state() throws Exception {
        var userProfile = userProfileRepository.save(new UserProfile().uuid(UUID.randomUUID()).userId(USER_ID));

        var publishedRecruitment = dataGeneratorService.createRecruitment(RecruitmentState.PUBLISHED);
        var otherPublishedRecruitment = dataGeneratorService.createRecruitment(RecruitmentState.PUBLISHED);
        var refusedRecruitment = dataGeneratorService.createRecruitment(RecruitmentState.PUBLISHED);
        var refusedRecruitmentAndArchived = dataGeneratorService.createRecruitment(RecruitmentState.PUBLISHED);
        var selectionRecruitment = dataGeneratorService.createRecruitment(RecruitmentState.SELECTION);
        var closedRecruitment = dataGeneratorService.createRecruitment(RecruitmentState.CLOSED);

        Map<Recruitment, Optional<CandidatureRecruitmentState>> statePerRecruitment = Map.of(
                publishedRecruitment, Optional.of(NEW),
                otherPublishedRecruitment, Optional.empty(),
                selectionRecruitment, Optional.of(SELECTED),
                closedRecruitment, Optional.of(WAITING)
        );

        statePerRecruitment.forEach((k, v) -> dataGeneratorService.createCandidature(k, userProfile, v.orElse(null)));

        var allRecruitments = new HashSet<>(statePerRecruitment.keySet());
        allRecruitments.add(refusedRecruitment);
        var archivedCandidatureIdRef = new AtomicReference<Long>();
        txHelper.doInTransaction(() -> {
            dataGeneratorService.createCandidature(refusedRecruitment, userProfile, SELECTED).markAsRefused(CandidatureEmailRefusalState.NONE, "uuid");
            var archivedCandidature = dataGeneratorService.createCandidature(refusedRecruitmentAndArchived, userProfile, SELECTED).markAsRefused(CandidatureEmailRefusalState.NONE, "uuid");
            archivedCandidature.setArchived(true);
            archivedCandidatureIdRef.set(archivedCandidature.getId())
            ;
        });
        applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withModifiedByUser(false)
                .withUserProfile(userProfile)
                .buildAndPersist();
        mvc.perform(get("/api/odas/user/" + USER_ID + "/simple-candidatures"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[*].recruitmentCode", containsInAnyOrder(allRecruitments.stream().map(Recruitment::getCode).toArray())))
                .andExpect(jsonPath("$[*].jobTitle", containsInAnyOrder(allRecruitments.stream().map(Recruitment::getJobTitle).toArray())))
                .andExpect(jsonPath("$[*].city", containsInAnyOrder(allRecruitments.stream().map(Recruitment::getCity).toArray())))
                .andExpect(jsonPath("$[*].state", containsInAnyOrder(UserCandidatureStateDTO.CLOSED.name(), UserCandidatureStateDTO.CLOSED.name(), UserCandidatureStateDTO.VALIDATED.name(), UserCandidatureStateDTO.WAITING.name(), UserCandidatureStateDTO.REFUSED.name())))
                .andExpect(jsonPath("$[*].id", not(contains(archivedCandidatureIdRef.get()))))
                .andExpect(jsonPath("$[*]", hasSize(5)));
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = {"ODAS_NOT_ADMIN"})
    void get_candidatures_should_return_candidatures_with_state() throws Exception {
        var userProfile = userProfileRepository.save(new UserProfile().uuid(UUID.randomUUID()).userId(USER_ID));

        var publishedRecruitment = dataGeneratorService.createRecruitment(RecruitmentState.PUBLISHED);
        var otherPublishedRecruitment = dataGeneratorService.createRecruitment(RecruitmentState.PUBLISHED);
        var refusedRecruitment = dataGeneratorService.createRecruitment(RecruitmentState.PUBLISHED);
        var refusedRecruitmentAndArchived = dataGeneratorService.createRecruitment(RecruitmentState.PUBLISHED);
        var selectionRecruitment = dataGeneratorService.createRecruitment(RecruitmentState.SELECTION);
        var closedRecruitment = dataGeneratorService.createRecruitment(RecruitmentState.CLOSED);

        Map<Recruitment, Optional<CandidatureRecruitmentState>> statePerRecruitment = Map.of(
                publishedRecruitment, Optional.of(NEW),
                otherPublishedRecruitment, Optional.empty(),
                selectionRecruitment, Optional.of(SELECTED),
                closedRecruitment, Optional.of(WAITING)
        );

        statePerRecruitment.forEach((k, v) -> dataGeneratorService.createCandidature(k, userProfile, v.orElse(null)));

        var allRecruitments = new HashSet<>(statePerRecruitment.keySet());
        allRecruitments.add(refusedRecruitment);
        var archivedCandidatureIdRef = new AtomicReference<Long>();
        txHelper.doInTransaction(() -> {
            dataGeneratorService.createCandidature(refusedRecruitment, userProfile, SELECTED).markAsRefused(CandidatureEmailRefusalState.NONE, "uuid");
            var archivedCandidature = dataGeneratorService.createCandidature(refusedRecruitmentAndArchived, userProfile, SELECTED).markAsRefused(CandidatureEmailRefusalState.NONE, "uuid");
            archivedCandidature.setArchived(true);
            archivedCandidatureIdRef.set(archivedCandidature.getId())
            ;
        });
        applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withModifiedByUser(false)
                .withUserProfile(userProfile)
                .buildAndPersist();
        mvc.perform(get("/api/odas/user/" + USER_ID + "/candidatures"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[*].recruitment.code", containsInAnyOrder(allRecruitments.stream().map(Recruitment::getCode).toArray())))
                .andExpect(jsonPath("$[*].recruitment.state", containsInAnyOrder(allRecruitments.stream().map(Recruitment::getState).map(Enum::name).toArray())))
                .andExpect(jsonPath("$[*].recruitment.id", containsInAnyOrder(allRecruitments.stream().map(r -> r.getId().intValue()).toArray())))
                .andExpect(jsonPath("$[*].state", containsInAnyOrder(UserCandidatureStateDTO.CLOSED.name(), UserCandidatureStateDTO.CLOSED.name(), UserCandidatureStateDTO.VALIDATED.name(), UserCandidatureStateDTO.WAITING.name(), UserCandidatureStateDTO.REFUSED.name())))
                .andExpect(jsonPath("$[*].id", not(contains(archivedCandidatureIdRef.get()))))
                .andExpect(jsonPath("$[*]", hasSize(5)));
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = {"ODAS_NOT_ADMIN"})
    void refused_candidature_should_not_be_displayed_before_email() throws Exception {
        var userProfile = userProfileRepository.save(new UserProfile().uuid(UUID.randomUUID()).userId(USER_ID));
        var recruitment1 = dataGeneratorService.createRecruitment(RecruitmentState.PUBLISHED);
        var recruitment2 = dataGeneratorService.createRecruitment(RecruitmentState.PUBLISHED);

        txHelper.doInTransaction(() -> {
            dataGeneratorService.createCandidature(recruitment1, userProfile, SELECTED).markAsRefused(CandidatureEmailRefusalState.WAITING, "uuid");
            dataGeneratorService.createCandidature(recruitment2, userProfile, SELECTED).markAsRefused(CandidatureEmailRefusalState.DONE, "uuid");
        });
        applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withModifiedByUser(false)
                .withUserProfile(userProfile)
                .buildAndPersist();
        mvc.perform(get("/api/odas/user/" + USER_ID + "/candidatures"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].state", is(UserCandidatureStateDTO.VALIDATED.name())))
                .andExpect(jsonPath("$[1].state", is(UserCandidatureStateDTO.REFUSED.name())))
                .andExpect(jsonPath("$[*]", hasSize(2)));
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = "ODAS_NOT_ADMIN")
    void get_experiences_should_return_sort_by_job_title_desc() throws Exception {

        mvc.perform(get("/api/odas/user/" + KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE + "/experiences"))
                .andExpect(status().isOk())
                .andExpect(jsonMatchesContent("userExperiences"));
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = {"ODAS_NOT_ADMIN"})
    void get_experiences_should_fail_wrong_user() throws Exception {
        mvc.perform(get("/api/odas/user/" + KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE + "/experiences"))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    void getting_a_userprofile_from_a_new_user_should_return_an_empty_profile() throws Exception {
        final var email = "<EMAIL>";
        final var firstName = "tester";
        final var lastName = "Sogilis";

        var userRepresentation = new UserRepresentation();
        userRepresentation.setEmail(email);
        userRepresentation.setFirstName(firstName);
        userRepresentation.setLastName(lastName);
        userRepresentation.setId(NEW_USER_ID);
        keycloakMockService.setUserProfile(NEW_USER_ID, userRepresentation);

        mvc.perform(get("/api/odas/user/%s/profileSummary".formatted(NEW_USER_ID)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.email", is(email)))
                .andExpect(jsonPath("$.firstName", is(firstName)))
                .andExpect(jsonPath("$.lastName", is(lastName)));
        Mockito.verify(notifier, VerificationModeFactory.times(1)).sendMessage(Mockito.any());
        Mockito.reset(notifier);
        txHelper.doInTransaction(() -> {
            var newUserMailVerificationState = userProfileRepository.findByUserId(NEW_USER_ID)
                    .orElseThrow()
                    .generalInformation()
                    .getMailVerificationState()
                    .getState();
            Assertions.assertThat(newUserMailVerificationState).isEqualTo(MailVerification.MailVerificationState.UNKNOWN);
        });
    }

    @ResetDataAfter
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    void update_user_job_offers_opt_in(boolean optIn) throws Exception {
        var user = userProfileGenerator.createUserProfile(UUID.fromString(NEW_USER_ID));
        var userRepresentation = keycloakMockService.getFrontOfficeUserProfile(NEW_USER_ID);

        performPost("/api/odas/user/job-offers-opt-in", new UpdateUserMailingListOptInCommandDTO().userId(NEW_USER_ID).value(optIn))
                .andExpect(status().isNoContent());
        Mockito.verify(mailingListService).updateJobOffersOptIn(user);
        Mockito.reset(mailingListService);
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void should_find_user_capacities_succeed() throws Exception {

        mvc.perform(get("/api/odas/user/" + USER_PROFILE_WITH_MATCHING_CANDIDATURE.userId() + "/capacities"))
                .andExpect(status().isOk())
                .andExpect(jsonMatchesContent("userCapacities"));
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void should_find_user_capacities_fail_for_unknown_userId() throws Exception {

        mvc.perform(get("/api/odas/user/" + UUID.randomUUID() + "/capacities"))
                .andExpect(status().isNotFound());
    }

    @ParameterizedTest
    @ResetDataAfter
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = Role.CANDIDATE)
    @EnumSource(UserRegistrationStateStepDTO.class)
    void should_update_user_registration_state_step(UserRegistrationStateStepDTO targetState) throws Exception {
        var command = new UpdateRegistrationStepCommandDTO().value(targetState);
        performPost("/user/registration-state/update-step", command)
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() ->
                assertThat(userProfileRepository.findByUserId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE)
                        .orElseThrow()
                        .registrationStep()).isEqualTo(UserRegistrationState.RegistrationStep.valueOf(targetState.name()))
        );
    }


    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void getUserRegistrationState_OT_authorized() throws Exception {
        txHelper.doInTransaction(() -> {
            userProfileRepository.findByUserId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE).orElseThrow().updatedChannels(Set.of(E_02_SOGILIS_CODE), UserChannel.ChannelSourceType.LANDING_PAGE);

        });
        performGetAndExpect("/user/" + KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE + "/registration-state", "userRegistrationState.json", false);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void getUserRegistrationState() throws Exception {
        performGetAndExpect("/user/" + KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE + "/registration-state", "userRegistrationState.json", false);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void delete_user_profile_should_delete_orphan_entities() throws Exception {
        var userProfile = userProfileGenerator.createUserProfile();
        mvc.perform(delete("/api/odas/user/" + userProfile.userId()))
                .andExpect(status().isNoContent());
        Mockito.verify(mailingListService).blacklistFromAllSenders(ArgumentMatchers.any());
        txHelper.doInTransaction(() -> {
            var actual = userProfileRepository.findByUserId(userProfile.userId());
            assertThat(actual).isEmpty();
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.CANDIDATE, USER_ID_KEYCLOAK})
    void candidate_delete_user_profile_should_delete_orphan_entities() throws Exception {
        var email = "<EMAIL>";
        var userRepresentation = new UserRepresentation();
        userRepresentation.setEmail(email);
        userRepresentation.setId(USER_ID_KEYCLOAK);

        keycloakMockService.setUserProfile(USER_ID_KEYCLOAK, userRepresentation);
        mvc.perform(delete(realUrl("/user/%s".formatted(USER_ID_KEYCLOAK))))
                .andExpect(status().isNoContent());
        Mockito.verify(mailingListService).sendMailsForTemplate(Set.of(email), 232L, Map.of(
                "EMAIL", email
        ), null);
        verify(notifier).sendMessage(argThat(a -> a.getText().equals(":boom: Un utilisateur FO a supprimé son compte\n*<EMAIL>* (<Nom inconnu>) / id : uuid")));
        Mockito.verify(mailingListService).blacklistFromAllSenders(ArgumentMatchers.any());
        txHelper.doInTransaction(() -> {
            var actual = userProfileRepository.findByUserId(USER_ID_KEYCLOAK);
            assertThat(actual).isEmpty();
        });
    }


    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void delete_user_profile_with_token() throws Exception {
        var userId = "MY_USER";
        txHelper.doInTransaction(() -> {
            var userProfile = applicationContext.getBean(UserProfileMotherObject.class).withUserId(userId).buildAndPersist();
            applicationContext.getBean(UserMobileTokenRepository.class).save(UserMobileToken.builder()
                    .token("a")
                    .userProfile(userProfile)
                    .timestamp(OffsetDateTime.now())
                    .build());
        });
        mvc.perform(delete("/api/odas/user/%s".formatted(userId)))
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var actual = userProfileRepository.findByUserId(userId);
            assertThat(actual).isEmpty();
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void delete_user_with_extra_pro_answers_behaviors_and_notifications_succeed() throws Exception {
        txHelper.doInTransaction(() -> {
            var userProfile = userProfileRepository.findByUserId(USER_PROFILE_WITH_MATCHING_CANDIDATURE.userId()).orElseThrow();
            capacityRelatedQuestionGenerator.createQuestionWithSelectedAnswer(0, userProfile, UUID.randomUUID());
            userProfile.updateBehaviors(Set.of(behaviorGenerator.createBehaviorForCategory(BehaviorCategory.CONSTANCY), behaviorGenerator.createBehaviorForCategory(BehaviorCategory.HONESTY)));
            var recruitment = dataGeneratorService.createRecruitment(RecruitmentState.PUBLISHED);
            entityManager.persist(RecruitmentNotification.builder().recruitment(recruitment).userProfile(userProfile).build());
            applicationContext.getBean(UserNotificationMotherObject.class).withUserProfile(userProfile).withRecruitment(recruitment).buildAndPersistSuspendedRecruitmentNotification();
        });

        mvc.perform(delete("/api/odas/user/" + USER_PROFILE_WITH_MATCHING_CANDIDATURE.userId()))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var actual = userProfileRepository.findByUserId(USER_PROFILE_WITH_MATCHING_CANDIDATURE.userId());
            assertThat(actual).isEmpty();
            assertThat(entityManager.createQuery("FROM AnswerForCapacityRelatedQuestion").getResultList()).isEmpty();
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void delete_user_should_delete_in_keycloak_and_blacklist_in_sendinblue_when_successful() throws Exception {
        var userProfile = userProfileGenerator.createUserProfile();

        mvc.perform(delete("/api/odas/user/" + userProfile.userId()))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var actual = userProfileRepository.findByUserId(userProfile.userId());
            assertThat(actual).isEmpty();
            Mockito.verify(keycloakMockService).deleteFrontOfficeUser(userProfile.userId());
            Mockito.verify(mailingListService).blacklistFromAllSenders(ArgumentMatchers.any());
            Mockito.verify(userIndexer).delete(userProfile.userId());

        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void delete_user_proceed_when_not_found_in_keycloak() throws Exception {
        var userProfile = userProfileGenerator.createUserProfile();
        Mockito.doReturn(false).when(keycloakMockService).deleteFrontOfficeUser(userProfile.userId());

        mvc.perform(delete("/api/odas/user/" + userProfile.userId()));

        txHelper.doInTransaction(() -> {
            var actual = userProfileRepository.findByUserId(userProfile.userId());
            assertThat(actual).isEmpty();
            Mockito.verify(keycloakMockService).deleteFrontOfficeUser(userProfile.userId());
            Mockito.verify(mailingListService).blacklistFromAllSenders(ArgumentMatchers.any());
            Mockito.verify(userIndexer).delete(userProfile.userId());
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void delete_user_proceed_when_not_found_in_db() throws Exception {
        var id = "42";
        var email = "a@a";
        var userRepresentation = new UserRepresentation();
        userRepresentation.setEmail(email);
        userRepresentation.setId(id);

        keycloakMockService.setUserProfile(id, userRepresentation);

        Mockito.doReturn(true).when(keycloakMockService).deleteFrontOfficeUser(id);

        mvc.perform(delete("/api/odas/user/" + id));

        Mockito.verify(keycloakMockService).deleteFrontOfficeUser(id);
        Mockito.verifyNoInteractions(mailingListService);
        Mockito.verify(userIndexer).delete(id);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void delete_user_reverts_on_keycloak_exception() {
        var userProfile = userProfileGenerator.createUserProfile();
        Mockito.doThrow(GenericTechnicalException.class).when(keycloakMockService).deleteFrontOfficeUser(userProfile.userId());

        Exception expectedException = null;
        try {
            mvc.perform(delete("/api/odas/user/" + userProfile.userId()));
        } catch (Exception e) {
            expectedException = e;
        }
        assertThat(expectedException).isNotNull().hasCauseExactlyInstanceOf(GenericTechnicalException.class);

        txHelper.doInTransaction(() -> {
            var actual = userProfileRepository.findByUserId(userProfile.userId());
            assertThat(actual).isPresent();
            Mockito.verify(keycloakMockService).deleteFrontOfficeUser(userProfile.userId());
            Mockito.verifyNoMoreInteractions(userIndexer);
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void save_new_note_to_user_without_organization_admin() throws Exception {
        save_new_note_common();
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void save_new_note_with_new_id() throws Exception {
        String note = "test note";
        var command = new SaveUserNoteCommandDTO().id(UUID.randomUUID()).userId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE).text(note);
        performPost("/user/save-note", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var notes = userProfileRepository.findByUserId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE).orElseThrow().userNotes();
            assertThat(notes)
                    .hasSize(1)
                    .allMatch(n -> n.getId().equals(command.getId()));
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    void save_new_note_to_user_with_organization_ot() throws Exception {
        var command = new SaveUserNoteCommandDTO().userId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE).text("yolo").organizationId(E_02_SOGILIS.getId());
        performPost("/user/save-note", command)
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var notes = userProfileRepository.findByUserId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE).orElseThrow().userNotes();
            assertThat(notes)
                    .hasSize(1)
                    .allMatch(n -> n.getOrganization().getId().equals(E_02_SOGILIS.getId()));
        });
    }


    private void save_new_note_common() throws Exception {
        String note = "test note";
        var command = new SaveUserNoteCommandDTO().userId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE).text(note);
        performPost("/user/save-note", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var notes = userProfileRepository.findByUserId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE).orElseThrow().userNotes();
            assertThat(notes)
                    .hasSize(1)
                    .allMatch(n -> ReflectionTestUtils.getField(n, "content").equals(note) && ReflectionTestUtils.getField(n, "organization") == null);
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void edit_note_updates_text_and_orga_admin() throws Exception {
        edit_note_common(E_01_CERA);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    void edit_note_updates_ot() throws Exception {
        edit_note_common(E_02_SOGILIS);
    }


    private void edit_note_common(Recruiter newOrga) throws Exception {
        prepareAndBuildEditNoteRequest(newOrga).andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var notes = userProfileRepository.findByUserId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE).orElseThrow().userNotes();
            assertThat(notes)
                    .hasSize(1)
                    .allMatch(n -> ReflectionTestUtils.getField(n, "content").equals(NEW_TEXT) && ((AbstractOrganization) ReflectionTestUtils.getField(n, "organization")).getCode().equals(newOrga.getCode()));
        });
    }

    private ResultActions prepareAndBuildEditNoteRequest(Recruiter newOrga) throws Exception {
        var refIdToUpdate = new AtomicReference<UUID>();

        txHelper.doInTransaction(() -> {
            var user = userProfileRepository.findByUserId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE).orElseThrow();
            var note = UserNote.builder()
                    .userProfile(user)
                    .organization(E_02_SOGILIS)
                    .content("Test note with orga")
                    .build();
            user.userNotes()
                    .add(note);
            userProfileRepository.flush();
            refIdToUpdate.set(note.getId());
        });

        var command = new SaveUserNoteCommandDTO()
                .id(refIdToUpdate.get())
                .userId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE)
                .text(NEW_TEXT)
                .organizationId(newOrga.getId());

        return performPost("/user/save-note", command);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void remove_note_admin() throws Exception {
        remove_ote_common();
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    void remove_note_ot() throws Exception {
        remove_ote_common();
    }

    private void remove_ote_common() throws Exception {
        var refIdToKeep = new AtomicReference<UUID>();
        var refIdToRemove = new AtomicReference<UUID>();
        txHelper.doInTransaction(() -> {
            var user = userProfileRepository.findByUserId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE).orElseThrow();
            var noteToKeep = UserNote.builder()
                    .userProfile(user)
                    .content("Test note")
                    .build();
            var noteToRemove = UserNote.builder()
                    .userProfile(user)
                    .organization(E_02_SOGILIS)
                    .content("Test note with orga")
                    .build();

            user.userNotes()
                    .add(noteToKeep);
            user.userNotes()
                    .add(noteToRemove);
            userProfileRepository.flush();
            refIdToKeep.set(noteToKeep.getId());
            refIdToRemove.set(noteToRemove.getId());
        });

        performPost("/user/delete-note", new DeleteUserNoteCommandDTO()
                .userId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE)
                .id(refIdToRemove.get()))
                .andExpect(status().isNoContent());


        txHelper.doInTransaction(() -> {
            var notes = userProfileRepository.findByUserId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE)
                    .orElseThrow()
                    .userNotes();
            assertThat(notes)
                    .hasSize(1)
                    .allMatch(n -> n.getId().equals(refIdToKeep.get()));
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void get_notes_admin() throws Exception {
        var users = prepareThreeNotes();

        performGetAndExpect("/user/" + KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE + "/note", "userNotesAdmin", false)
                .andExpect(jsonPath("$[*].id", containsInAnyOrder(users.userNotes().stream().map(UserNote::getId).map(Object::toString).toArray(String[]::new))));
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void get_user_channel_affectations() {
        var prescriberChannel = "P-4242";
        var channel = "E-4242";

        applicationContext.getBean(OrganizationGenerator.class)
                .createRecruiter(prescriberChannel);
        applicationContext.getBean(OrganizationGenerator.class)
                .createRecruiter(channel);

        var user = applicationContext.getBean(UserProfileMotherObject.class)
                .withPrescriber(prescriberChannel, UserChannel.ChannelSourceType.LANDING_PAGE)
                .withChannels(channel, prescriberChannel)
                .buildAndPersist();

        performGetAndExpect("/user/%s/channel-affectations".formatted(user.userId()), "userChannelAffectations", false);

    }

    private UserProfile prepareThreeNotes() {
        var userHolder = new AtomicReference<UserProfile>();
        txHelper.doInTransaction(() -> {
            var user = userProfileRepository.findByUserId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE).orElseThrow();
            user.userNotes()
                    .add(UserNote.builder()
                            .userProfile(user)
                            .content("Test note")
                            .build());

            user.userNotes()
                    .add(UserNote.builder()
                            .userProfile(user)
                            .organization(E_02_SOGILIS)
                            .content("Test note with orga sogilis")
                            .build());

            user.userNotes()
                    .add(UserNote.builder()
                            .userProfile(user)
                            .organization(E_01_CERA)
                            .content("Test note with orga cera")
                            .build());
            userHolder.set(user);
        });
        return userHolder.get();
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void get_user_handicap_mode_enabled_should_return_null_when_not_set() throws Exception {
        var userProfile = userProfileGenerator.createUserProfile();
        // Don't set handicapModeEnabled, should default to null

        mvc.perform(get("/api/odas/user/handicap-mode-enabled/" + userProfile.userId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").doesNotExist());
    }

    @Test
    @WithMockKeycloakUser(roles = Role.CANDIDATE)
    void get_user_handicap_mode_enabled_should_fail_for_unauthorized_user() throws Exception {
        var userProfile = userProfileGenerator.createUserProfile();

        mvc.perform(get("/api/odas/user/handicap-mode-enabled/" + userProfile.userId()))
                .andExpect(status().isForbidden());
    }

    @ResetDataAfter
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void update_user_handicap_mode_enabled_parameterized_test(boolean handicapModeValue) throws Exception {
        var userProfile = userProfileGenerator.createUserProfile();

        var requestBody = Map.of(
                "userId", userProfile.userId(),
                "value", handicapModeValue
        );

        mvc.perform(put("/api/odas/user/handicap-mode-enabled")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestBody)))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedUser = userProfileRepository.findByUserId(userProfile.userId()).orElseThrow();
            assertThat(updatedUser.handicapModeEnabled()).isEqualTo(handicapModeValue);
        });
    }

    @ResetDataAfter
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void get_user_handicap_mode_enabled_parameterized_test(boolean handicapModeValue) throws Exception {
        var userProfile = userProfileGenerator.createUserProfile();
        txHelper.doInTransaction(() -> {
            var user = userProfileRepository.findByUserId(userProfile.userId()).orElseThrow();
            user.handicapModeEnabled(handicapModeValue);
        });

        mvc.perform(get("/api/odas/user/handicap-mode-enabled/" + userProfile.userId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", is(handicapModeValue)));
    }

    private UserProfile createUserWithActivities() {
        return txHelper.doInTransaction(() -> {
            var capacity1 = capacityGenerator.createCapacity("CAP-DEV");
            var capacity2 = capacityGenerator.createCapacity("CAP-PROJ");
            var capacity3 = capacityGenerator.createCapacity("CAP-DATA");

            var activity1 = jobActivityLabelGenerator.createActivity("Développement logiciel", capacity1);
            var activity2 = jobActivityLabelGenerator.createActivity("Gestion de projet", capacity2);
            var activity3 = jobActivityLabelGenerator.createActivity("Analyse de données", capacity3);

            var occupation1 = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                    .withTitle("Développeur")
                    .withActivity(activity1, false)
                    .buildAndPersist();

            var occupation2 = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                    .withTitle("Chef de projet")
                    .withActivity(activity2, false)
                    .buildAndPersist();

            var occupation3 = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                    .withTitle("Analyste")
                    .withActivity(activity3, false)
                    .buildAndPersist();

            return applicationContext.getBean(UserProfileMotherObject.class)
                    .withUserId(USER_ID)
                    .withFirstname("Jean")
                    .withLastname("Dupont")
                    .withExperience(occupation1, 24)
                    .withExperience(occupation2, 18)
                    .withExperience(occupation3, 12)
                    .buildAndPersist();
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    void regenerate_user_hashtags_with_8_selected_hashtags_should_return_12_total_hashtags() throws Exception {
        var userProfile = createUserWithActivities();

        var selectedHashtags = List.of(
                "#Java", "#Spring", "#React", "#Docker",
                "#Kubernetes", "#AWS", "#PostgreSQL", "#Git"
        );

        var generatedHashtags = List.of(
                "#Microservices", "#DevOps", "#Agile", "#TDD"
        );

        var expectedFinalHashtags = new ArrayList<>(selectedHashtags);
        expectedFinalHashtags.addAll(generatedHashtags);

        var deselectedHashtags = List.of("#PHP", "#Python");

        when(hashtagsGenerationService.regenerateHashtags(userProfile.userId(), selectedHashtags, deselectedHashtags))
                .thenReturn(expectedFinalHashtags);

        var requestBody = new HashtagsDTO().hashtags(selectedHashtags).deselectedHashtags(deselectedHashtags);

        mvc.perform(post("/api/odas/user/" + userProfile.userId() + "/hashtags/regenerate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestBody)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.hashtags", hasSize(12)))
                .andExpect(jsonPath("$.hashtags", containsInAnyOrder(
                        "#Java", "#Spring", "#React", "#Docker",
                        "#Kubernetes", "#AWS", "#PostgreSQL", "#Git",
                        "#Microservices", "#DevOps", "#Agile", "#TDD"
                )));

        verify(hashtagsGenerationService).regenerateHashtags(userProfile.userId(), selectedHashtags, deselectedHashtags);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    void regenerate_user_hashtags_with_12_selected_hashtags_should_return_same_hashtags_without_generation() throws Exception {
        var userProfile = createUserWithActivities();

        var selectedHashtags = List.of(
                "#Java", "#Spring", "#React", "#Docker",
                "#Kubernetes", "#AWS", "#PostgreSQL", "#Git",
                "#Microservices", "#DevOps", "#Agile", "#TDD"
        );

        var deselectedHashtags = Collections.<String>emptyList(); // No deselected hashtags in this case

        when(hashtagsGenerationService.regenerateHashtags(userProfile.userId(), selectedHashtags, deselectedHashtags))
                .thenReturn(new ArrayList<>(selectedHashtags));

        var requestBody = new HashtagsDTO().hashtags(selectedHashtags).deselectedHashtags(deselectedHashtags);

        mvc.perform(post("/api/odas/user/" + userProfile.userId() + "/hashtags/regenerate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestBody)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.hashtags", hasSize(12)))
                .andExpect(jsonPath("$.hashtags", containsInAnyOrder(
                        "#Java", "#Spring", "#React", "#Docker",
                        "#Kubernetes", "#AWS", "#PostgreSQL", "#Git",
                        "#Microservices", "#DevOps", "#Agile", "#TDD"
                )));

        verify(hashtagsGenerationService).regenerateHashtags(userProfile.userId(), selectedHashtags, deselectedHashtags);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    void regenerate_user_hashtags_should_not_return_duplicate_hashtags() throws Exception {
        var userProfile = createUserWithActivities();

        var selectedHashtags = List.of(
                "#Java", "#Spring", "#React", "#Docker",
                "#Kubernetes", "#AWS", "#PostgreSQL", "#Git"
        );

        var generatedHashtags = List.of(
                "#Microservices", "#DevOps", "#Agile", "#TDD"
        );

        var expectedFinalHashtags = new ArrayList<>(selectedHashtags);
        expectedFinalHashtags.addAll(generatedHashtags);

        var deselectedHashtags = List.of("#PHP", "#Python");

        when(hashtagsGenerationService.regenerateHashtags(userProfile.userId(), selectedHashtags, deselectedHashtags))
                .thenReturn(expectedFinalHashtags);

        var requestBody = new HashtagsDTO().hashtags(selectedHashtags).deselectedHashtags(deselectedHashtags);

        mvc.perform(post("/api/odas/user/" + userProfile.userId() + "/hashtags/regenerate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestBody)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.hashtags", hasSize(12)))
                .andExpect(jsonPath("$.hashtags", not(hasItem("#java"))))
                .andExpect(jsonPath("$.hashtags", not(hasItem("#JAVA"))))
                .andExpect(jsonPath("$.hashtags", containsInAnyOrder(
                        "#Java", "#Spring", "#React", "#Docker",
                        "#Kubernetes", "#AWS", "#PostgreSQL", "#Git",
                        "#Microservices", "#DevOps", "#Agile", "#TDD"
                )));

        verify(hashtagsGenerationService).regenerateHashtags(userProfile.userId(), selectedHashtags, deselectedHashtags);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    void regenerate_user_hashtags_with_deselected_hashtags_should_exclude_them() throws Exception {
        var userProfile = createUserWithActivities();

        var selectedHashtags = List.of(
                "#Java", "#Spring", "#React", "#Docker",
                "#Kubernetes", "#AWS", "#PostgreSQL", "#Git"
        );

        var deselectedHashtags = List.of("#PHP", "#Python");

        var generatedHashtags = List.of(
                "#Microservices", "#DevOps", "#Agile", "#TDD"
        );

        var expectedFinalHashtags = new ArrayList<>(selectedHashtags);
        expectedFinalHashtags.addAll(generatedHashtags);

        when(hashtagsGenerationService.regenerateHashtags(userProfile.userId(), selectedHashtags, deselectedHashtags))
                .thenReturn(expectedFinalHashtags);

        var requestBody = new HashtagsDTO()
                .hashtags(selectedHashtags)
                .deselectedHashtags(deselectedHashtags);

        mvc.perform(post("/api/odas/user/" + userProfile.userId() + "/hashtags/regenerate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestBody)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.hashtags", hasSize(12)))
                .andExpect(jsonPath("$.hashtags", containsInAnyOrder(
                        "#Java", "#Spring", "#React", "#Docker",
                        "#Kubernetes", "#AWS", "#PostgreSQL", "#Git",
                        "#Microservices", "#DevOps", "#Agile", "#TDD"
                )))
                .andExpect(jsonPath("$.hashtags", not(hasItem("#PHP"))))
                .andExpect(jsonPath("$.hashtags", not(hasItem("#Python"))));

        verify(hashtagsGenerationService).regenerateHashtags(userProfile.userId(), selectedHashtags, deselectedHashtags);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    void regenerate_user_hashtags_should_generate_exactly_deselected_count() throws Exception {
        var userProfile = createUserWithActivities();

        var selectedHashtags = List.of(
                "#Java", "#Spring", "#React", "#Docker", "#Kubernetes"
        );

        var deselectedHashtags = List.of("#PHP", "#Python", "#Ruby", "#Go", "#C++", "#C#", "#Kotlin");

        var generatedHashtags = List.of(
                "#AWS", "#PostgreSQL", "#Git", "#Microservices", "#DevOps", "#Agile", "#TDD"
        );

        var expectedFinalHashtags = new ArrayList<>(selectedHashtags);
        expectedFinalHashtags.addAll(generatedHashtags);

        when(hashtagsGenerationService.regenerateHashtags(userProfile.userId(), selectedHashtags, deselectedHashtags))
                .thenReturn(expectedFinalHashtags);

        var requestBody = new HashtagsDTO()
                .hashtags(selectedHashtags)
                .deselectedHashtags(deselectedHashtags);

        mvc.perform(post("/api/odas/user/" + userProfile.userId() + "/hashtags/regenerate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestBody)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.hashtags", hasSize(12)))
                .andExpect(jsonPath("$.hashtags", containsInAnyOrder(
                        "#Java", "#Spring", "#React", "#Docker", "#Kubernetes",
                        "#AWS", "#PostgreSQL", "#Git", "#Microservices", "#DevOps", "#Agile", "#TDD"
                )))
                .andExpect(jsonPath("$.hashtags", not(hasItem("#PHP"))))
                .andExpect(jsonPath("$.hashtags", not(hasItem("#Python"))))
                .andExpect(jsonPath("$.hashtags", not(hasItem("#Ruby"))))
                .andExpect(jsonPath("$.hashtags", not(hasItem("#Go"))))
                .andExpect(jsonPath("$.hashtags", not(hasItem("#C++"))))
                .andExpect(jsonPath("$.hashtags", not(hasItem("#C#"))))
                .andExpect(jsonPath("$.hashtags", not(hasItem("#Kotlin"))));

        verify(hashtagsGenerationService).regenerateHashtags(userProfile.userId(), selectedHashtags, deselectedHashtags);
    }
}
