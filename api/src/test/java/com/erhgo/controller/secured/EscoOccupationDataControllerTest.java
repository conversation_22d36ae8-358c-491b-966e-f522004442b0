package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.TestUtils;
import com.erhgo.TransactionTestHelper;
import com.erhgo.config.ApiConstants;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.esco.EscoSkill;
import com.erhgo.domain.referential.Behavior;
import com.erhgo.domain.referential.Context;
import com.erhgo.openapi.dto.SkillLinkToBehaviorCommandDTO;
import com.erhgo.openapi.dto.SkillLinkToContextCommandDTO;
import com.erhgo.openapi.dto.SkillSetBooleanValueCommandDTO;
import com.erhgo.openapi.dto.UpdateSkillDescriptionCommandDTO;
import com.erhgo.repositories.classifications.EscoSkillRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.google.common.collect.Sets;
import org.hamcrest.Matchers;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;

import java.util.UUID;

import static com.erhgo.config.ApiConstants.SEPARATOR;
import static com.erhgo.generators.TestFixtures.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class EscoOccupationDataControllerTest extends AbstractIntegrationTestWithFixtures {

    @Autowired
    private EscoSkillRepository escoSkillRepository;

    @Autowired
    private TransactionTestHelper txHelper;

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldReturnCompleteSkill() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .activities(Sets.newHashSet(ACT_01, ACT_05))
                .behaviors(Sets.newHashSet(B_01, B_02))
                .contexts(Sets.newHashSet(MANDATORY_CONTEXT, CT_02))
                .build());

        mvc.perform(get(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "detail")
                .param("uri", skill.getUri())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.contexts[*].categoryLevel.title", contains(
                        MANDATORY_CONTEXT.getCategoryLevel().getTitle(),
                        CT_02.getCategoryLevel().getTitle())))
                .andExpect(jsonPath("$.contexts[*].categoryLevel.category.title", contains(
                        MANDATORY_CONTEXT.getCategoryLevel().getCategory().getTitle(),
                        CT_02.getCategoryLevel().getCategory().getTitle()
                )));
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void shouldReturn404WhenUpdatingDescriptionAndSkillNotFound() throws Exception {
        var requestBody = new UpdateSkillDescriptionCommandDTO();
        requestBody.setUri("unknown");
        requestBody.setDescription("description");

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "detail" + SEPARATOR + "updateDescription")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void shouldFailWhenLinkingToContextAndSkillNotFound() throws Exception {
        var requestBody = new SkillLinkToContextCommandDTO()
                .uri("unknown")
                .contextId(CT_03.getId());

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToContext")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void shouldFailWhenLinkingToContextAndNoContextId() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .build());

        var requestBody = new SkillLinkToContextCommandDTO()
                .uri(skill.getUri());

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToContext")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void shouldFailWhenLinkingToContextAndContextNotFound() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .build());

        var requestBody = new SkillLinkToContextCommandDTO()
                .uri(skill.getUri())
                .contextId(UUID.randomUUID());

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToContext")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldLinkWithContextAndSetNoContextToFalse() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .build());

        var requestBody = new SkillLinkToContextCommandDTO()
                .uri(skill.getUri())
                .contextId(MANDATORY_CONTEXT.getId());

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToContext")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedSkill = escoSkillRepository.findById(skill.getUri()).orElseThrow();
            assertThat(updatedSkill.getContexts()).isNotEmpty();
            assertThat(updatedSkill.getContexts()).extracting(Context::getId).containsOnly(MANDATORY_CONTEXT.getId());
            assertThat(updatedSkill.getNoContext()).isFalse();
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldAddLinkWithContextWhenNotEmpty() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .contexts(Sets.newHashSet(MANDATORY_CONTEXT))
                .build());

        var requestBody = new SkillLinkToContextCommandDTO()
                .uri(skill.getUri())
                .contextId(CT_02.getId());

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToContext")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var updatedSkill = escoSkillRepository.findById(skill.getUri()).orElseThrow();
            assertThat(updatedSkill.getContexts()).isNotEmpty();
            assertThat(updatedSkill.getContexts()).extracting(Context::getId).containsOnly(MANDATORY_CONTEXT.getId(), CT_02.getId());
            assertThat(updatedSkill.getNoContext()).isFalse();
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldDoNothingWhenContextIsAlreadyLinked() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .contexts(Sets.newHashSet(MANDATORY_CONTEXT))
                .build());

        var requestBody = new SkillLinkToContextCommandDTO()
                .uri(skill.getUri())
                .contextId(MANDATORY_CONTEXT.getId());

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToContext")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var updatedSkill = escoSkillRepository.findById(skill.getUri()).orElseThrow();
            assertThat(updatedSkill.getContexts()).isNotEmpty();
            assertThat(updatedSkill.getContexts()).extracting(Context::getId).containsOnly(MANDATORY_CONTEXT.getId());
            assertThat(updatedSkill.getNoContext()).isFalse();
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldFailWhenDeletingLinkToContextAndNoContextId() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .build());

        var requestBody = new SkillLinkToContextCommandDTO()
                .uri(skill.getUri());

        mvc.perform(delete(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToContext")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void shouldFailWhenDeletingLinkToContextAndSkillNotFound() throws Exception {
        var requestBody = new SkillLinkToContextCommandDTO()
                .uri("unknown")
                .contextId(CT_03.getId());

        mvc.perform(delete(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToContext")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldDoNothingWhenDeletingLinkToContextAndContextNotLinked() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .contexts(Sets.newHashSet(MANDATORY_CONTEXT))
                .build());

        var requestBody = new SkillLinkToContextCommandDTO()
                .uri(skill.getUri())
                .contextId(CT_02.getId());

        mvc.perform(delete(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToContext")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedSkill = escoSkillRepository.findById(skill.getUri()).orElseThrow();
            assertThat(updatedSkill.getContexts()).extracting(Context::getId).containsOnly(MANDATORY_CONTEXT.getId());
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldDoNothingWhenNoContextLinked() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .build());

        var requestBody = new SkillLinkToContextCommandDTO()
                .uri(skill.getUri())
                .contextId(MANDATORY_CONTEXT.getId());

        mvc.perform(delete(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToContext")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var updatedSkill = escoSkillRepository.findById(skill.getUri()).orElseThrow();
            assertThat(updatedSkill.getContexts()).isEmpty();
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldDeleteLinkWithContext() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .contexts(Sets.newHashSet(MANDATORY_CONTEXT, CT_02))
                .build());

        var requestBody = new SkillLinkToContextCommandDTO()
                .uri(skill.getUri())
                .contextId(MANDATORY_CONTEXT.getId());

        mvc.perform(delete(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToContext")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var updatedSkill = escoSkillRepository.findById(skill.getUri()).orElseThrow();
            assertThat(updatedSkill.getContexts()).extracting(Context::getId).containsOnly(CT_02.getId());
            assertThat(updatedSkill.getNoContext()).isFalse();
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldDeleteLinkWithContextAndSetNoContextToNullWhenNoMoreContext() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .noContext(false)
                .contexts(Sets.newHashSet(MANDATORY_CONTEXT))
                .build());

        var requestBody = new SkillLinkToContextCommandDTO()
                .uri(skill.getUri())
                .contextId(MANDATORY_CONTEXT.getId());

        mvc.perform(delete(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToContext")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var updatedSkill = escoSkillRepository.findById(skill.getUri()).orElseThrow();
            assertThat(updatedSkill.getContexts()).isEmpty();
            assertThat(updatedSkill.getNoContext()).isNull();
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void shouldFailWhenLinkingToBehaviorAndSkillNotFound() throws Exception {
        var requestBody = new SkillLinkToBehaviorCommandDTO()
                .uri("unknown")
                .behaviorId(UUID.randomUUID());

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToBehavior")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser
    public void shouldFailWhenUserNotAdmin() throws Exception {
        var requestBody = new SkillLinkToBehaviorCommandDTO()
                .uri("unknown")
                .behaviorId(UUID.randomUUID());

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToBehavior")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldFailWhenLinkingToBehaviorAndNoBehaviorId() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .build());

        var requestBody = new SkillLinkToBehaviorCommandDTO()
                .uri(skill.getUri());

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToBehavior")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldFailWhenLinkingToBehaviorAndBehaviorNotFound() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .build());

        var requestBody = new SkillLinkToBehaviorCommandDTO()
                .uri(skill.getUri())
                .behaviorId(UUID.randomUUID());

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToBehavior")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldLinkWithBehaviorAndSetNoBehaviorToFalse() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .build());

        var requestBody = new SkillLinkToBehaviorCommandDTO()
                .uri(skill.getUri())
                .behaviorId(B_01.getId());

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToBehavior")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var updatedSkill = escoSkillRepository.findById(skill.getUri()).orElseThrow();
            assertThat(updatedSkill.getBehaviors()).isNotEmpty();
            assertThat(updatedSkill.getBehaviors()).extracting(Behavior::getId).containsOnly(B_01.getId());
            assertThat(updatedSkill.getNoBehavior()).isFalse();
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldAddLinkWithBehaviorWhenNotEmpty() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .behaviors(Sets.newHashSet(B_01))
                .build());

        var requestBody = new SkillLinkToBehaviorCommandDTO()
                .uri(skill.getUri())
                .behaviorId(B_02.getId());

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToBehavior")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedSkill = escoSkillRepository.findById(skill.getUri()).orElseThrow();
            assertThat(updatedSkill.getBehaviors()).isNotEmpty();
            assertThat(updatedSkill.getBehaviors()).extracting(Behavior::getId).containsOnly(B_01.getId(), B_02.getId());
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void shouldDoNothingWhenBehaviorIsAlreadyLinked() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .behaviors(Sets.newHashSet(B_01))
                .build());

        var requestBody = new SkillLinkToBehaviorCommandDTO()
                .uri(skill.getUri())
                .behaviorId(B_01.getId());

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToBehavior")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var updatedSkill = escoSkillRepository.findById(skill.getUri()).orElseThrow();
            assertThat(updatedSkill.getBehaviors()).isNotEmpty();
            assertThat(updatedSkill.getBehaviors()).extracting(Behavior::getId).containsOnly(B_01.getId());
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldFailWhenDeletingLinkToBehaviorAndNoBehaviorId() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .build());

        var requestBody = new SkillLinkToBehaviorCommandDTO()
                .uri(skill.getUri());

        mvc.perform(delete(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToBehavior")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void shouldFailWhenDeletingLinkToBehaviorAndSkillNotFound() throws Exception {
        var requestBody = new SkillLinkToBehaviorCommandDTO()
                .uri("unknown")
                .behaviorId(UUID.randomUUID());

        mvc.perform(delete(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToBehavior")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldDoNothingWhenDeletingLinkToBehaviorAndBehaviorNotLinked() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .behaviors(Sets.newHashSet(B_01))
                .build());

        var requestBody = new SkillLinkToBehaviorCommandDTO()
                .uri(skill.getUri())
                .behaviorId(B_02.getId());

        mvc.perform(delete(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToBehavior")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var updatedSkill = escoSkillRepository.findById(skill.getUri()).orElseThrow();
            assertThat(updatedSkill.getBehaviors()).extracting(Behavior::getId).containsOnly(B_01.getId());
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldDoNothingWhenNoBehaviorLinked() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .build());

        var requestBody = new SkillLinkToBehaviorCommandDTO()
                .uri(skill.getUri())
                .behaviorId(B_01.getId());

        mvc.perform(delete(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToBehavior")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedSkill = escoSkillRepository.findById(skill.getUri()).orElseThrow();
            assertThat(updatedSkill.getBehaviors()).isEmpty();
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldDeleteLinkWithBehavior() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .noBehavior(false)
                .behaviors(Sets.newHashSet(B_01, B_02))
                .build());

        var requestBody = new SkillLinkToBehaviorCommandDTO()
                .uri(skill.getUri())
                .behaviorId(B_01.getId());

        mvc.perform(delete(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToBehavior")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var updatedSkill = escoSkillRepository.findById(skill.getUri()).orElseThrow();
            assertThat(updatedSkill.getBehaviors()).extracting(Behavior::getId).containsOnly(B_02.getId());
            assertThat(updatedSkill.getNoBehavior()).isFalse();
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldDeleteLinkWithBehaviorAndSetNoBehaviorToNull() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .noBehavior(false)
                .behaviors(Sets.newHashSet(B_02))
                .build());

        var requestBody = new SkillLinkToBehaviorCommandDTO()
                .uri(skill.getUri())
                .behaviorId(B_02.getId());

        mvc.perform(delete(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToBehavior")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var updatedSkill = escoSkillRepository.findById(skill.getUri()).orElseThrow();
            assertThat(updatedSkill.getBehaviors()).isEmpty();
            assertThat(updatedSkill.getNoBehavior()).isNull();
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void shouldFailWhenSettingNoActivityAndSkillNotFound() throws Exception {
        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "detail" + SEPARATOR + "setNoActivity")
                .content(objectMapper.writeValueAsString(getSetBooleanValueCommand("unknown uri", false)))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void shouldFailWhenSettingNoActivityAndNoUri() throws Exception {
        performPost(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "detail" + SEPARATOR + "setNoActivity", null)
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldSetNoActivityToTrueAndRemoveLinkWithActivity() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .activities(Sets.newHashSet(ACT_01))
                .build());

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "detail" + SEPARATOR + "setNoActivity")
                .content(objectMapper.writeValueAsString(getSetBooleanValueCommand("uri", true)))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedSkill = escoSkillRepository.findById(skill.getUri()).orElseThrow();
            assertThat(updatedSkill.getNoActivity()).isTrue();
            assertThat(updatedSkill.getActivities()).isNullOrEmpty();
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldSetNoActivityToNull() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .activities(Sets.newHashSet(ACT_01))
                .build());

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "detail" + SEPARATOR + "setNoActivity")
                .content(objectMapper.writeValueAsString(getSetBooleanValueCommand("uri", false)))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        var updatedSkill = escoSkillRepository.findById(skill.getUri()).orElseThrow();
        assertThat(updatedSkill.getNoActivity()).isNull();
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void shouldFailWhenSettingNoBehaviorAndSkillNotFound() throws Exception {
        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "detail" + SEPARATOR + "setNoBehavior")
                .content(objectMapper.writeValueAsString(getSetBooleanValueCommand("unknown uri", false)))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void shouldFailWhenSettingNoBehaviorAndNoUri() throws Exception {
        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "detail" + SEPARATOR + "setNoBehavior")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldSetNoBehaviorToTrueAndRemoveLinkWithBehaviors() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .behaviors(Sets.newHashSet(B_01, B_02))
                .build());

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "detail" + SEPARATOR + "setNoBehavior")
                .content(objectMapper.writeValueAsString(getSetBooleanValueCommand("uri", true)))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var updatedSkill = escoSkillRepository.findById(skill.getUri()).orElseThrow();
            assertThat(updatedSkill.getNoBehavior()).isTrue();
            assertThat(updatedSkill.getBehaviors()).isEmpty();
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldSetNoBehaviorToNull() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .behaviors(Sets.newHashSet(B_01, B_02))
                .build());

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "detail" + SEPARATOR + "setNoBehavior")
                .content(objectMapper.writeValueAsString(getSetBooleanValueCommand("uri", false)))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        var updatedSkill = escoSkillRepository.findById(skill.getUri()).orElseThrow();
        assertThat(updatedSkill.getNoBehavior()).isNull();
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void shouldFailWhenSettingNoContextAndSkillNotFound() throws Exception {
        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "detail" + SEPARATOR + "setNoContext")
                .content(objectMapper.writeValueAsString(getSetBooleanValueCommand("unknown uri", true)))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void shouldFailWhenSettingNoContextAndNoUri() throws Exception {
        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "detail" + SEPARATOR + "setNoContext")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldSetNoContextToTrueAndRemoveLinkWithContexts() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .contexts(Sets.newHashSet(MANDATORY_CONTEXT, CT_02))
                .build());

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "detail" + SEPARATOR + "setNoContext")
                .content(objectMapper.writeValueAsString(getSetBooleanValueCommand("uri", true)))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var updatedSkill = escoSkillRepository.findById(skill.getUri()).orElseThrow();
            assertThat(updatedSkill.getNoContext()).isTrue();
            assertThat(updatedSkill.getContexts()).isEmpty();
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldSetNoContextToNull() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .contexts(Sets.newHashSet(MANDATORY_CONTEXT, CT_02))
                .build());

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "detail" + SEPARATOR + "setNoContext")
                .content(objectMapper.writeValueAsString(getSetBooleanValueCommand("uri", false)))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        var updatedSkill = escoSkillRepository.findById(skill.getUri()).orElseThrow();
        assertThat(updatedSkill.getNoContext()).isNull();
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void shouldListCapacitiesOfOccupation() throws Exception {
        final var content = TestUtils.toString(this.getClass().getClassLoader().getResourceAsStream("expected/escoCapacities.json"));
        performGetAndExpect("/api/odas/esco/occupation/capacities", "escoCapacities", false, r -> r.param("uri", ESCO_OCCUPATION_WITH_REFERENTIAL_DATA.getUri()), null);
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void shouldMarkOccupationAsNotQualified() throws Exception {
        mvc.perform(get("/api/odas/esco/occupation/capacities")
                .param("uri", ESCO_OCCUPATION_WITHOUT_REFERENTIAL_DATA.getUri()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.capacities", hasSize(0)))
                .andExpect(jsonPath("$.fullyQualified", Matchers.is(false)));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    public void searchEscoShouldSucceed() throws Exception {
        mvc.perform(get("/api/odas/esco/occupation/list")
                .param("query", "e"))
                .andExpect(status().isOk())
                .andExpect(TestUtils.jsonMatchesContentWithOrderedArray("escoOccupations"));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    public void searchEscoShouldFindByIsco() throws Exception {
        mvc.perform(get("/api/odas/esco/occupation/list")
                .param("query", "666"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[*]", hasSize(1)))
                .andExpect(jsonPath("$[0].uri", is(ESCO_OCCUPATION_WITH_REFERENTIAL_DATA.getUri())));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    public void searchEscoShouldFindByURI() throws Exception {
        mvc.perform(get("/api/odas/esco/occupation/list")
                .param("query", ESCO_OCCUPATION_WITH_REFERENTIAL_DATA.getUri()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[*]", hasSize(1)))
                .andExpect(jsonPath("$[0].uri", is(ESCO_OCCUPATION_WITH_REFERENTIAL_DATA.getUri())));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    public void getEscoJobShouldSucceed() throws Exception {
        mvc.perform(get("/api/odas/esco/occupation/detail")
                .param("uri", ESCO_OCCUPATION_WITH_REFERENTIAL_DATA.getUri()))
                .andExpect(status().isOk())
                .andExpect(TestUtils.jsonMatchesContent("escoDetail"));
    }

    private SkillSetBooleanValueCommandDTO getSetBooleanValueCommand(String uri, boolean value) {
        return new SkillSetBooleanValueCommandDTO().uri(uri).value(value);
    }

}
