package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.openapi.dto.SetRecruitmentErhgoClassificationsCommandDTO;
import com.erhgo.repositories.RecruitmentRepository;
import com.erhgo.repositories.classifications.ErhgoClassificationRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.RecruitmentService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.ApplicationContext;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class RecruitmentErhgoClassificationsControllerTest extends AbstractIntegrationTest {


    @Autowired
    RecruitmentRepository recruitmentRepository;

    @Autowired
    ErhgoClassificationRepository erhgoClassificationRepository;

    @Autowired
    ApplicationContext applicationContext;

    @SpyBean
    RecruitmentService recruitmentService;

    static final String CODE_ORGA = "S-01";

    @Test
    @WithMockKeycloakUser(roles = {Role.SOURCING, CODE_ORGA})
    @ResetDataAfter
    void addErhgoClassificationToRecruitment() throws Exception {
        var list = List.of("SO-02", "SO-03", "SO-04");
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class).withRecruiterCode(CODE_ORGA).buildAndPersist();
        var recruitmentId = recruitment.getId();
        var command = new SetRecruitmentErhgoClassificationsCommandDTO().recruitmentId(recruitmentId).erhgoClassificationCodes(list);

        performPut("/sourcing/recruitment/erhgo-classifications", command).andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            assertThat(recruitmentRepository.findOneByCode(recruitment.getCode()).getErhgoClassifications()).extracting(ErhgoClassification::getCode).containsAll(list);
        });
    }


    @Test
    @WithMockKeycloakUser(roles = {Role.SOURCING, CODE_ORGA})
    @ResetDataAfter
    void removeErhgoClassificationToRecruitment() throws Exception {
        var list = List.of("SO-02", "SO-03", "SO-04");
        var erhgoClassification = new HashSet<>(erhgoClassificationRepository
                .findErhgoClassificationByCodeIn(list));
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class).withRecruiterCode(CODE_ORGA).withErhgoClassification(erhgoClassification).buildAndPersist();
        var recruitmentId = recruitment.getId();
        var command = new SetRecruitmentErhgoClassificationsCommandDTO().recruitmentId(recruitmentId).erhgoClassificationCodes(Collections.emptyList());

        performPut("/sourcing/recruitment/erhgo-classifications", command).andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            assertThat(recruitmentRepository.findOneByCode(recruitment.getCode()).getErhgoClassifications()).isEmpty();
        });
    }
}
