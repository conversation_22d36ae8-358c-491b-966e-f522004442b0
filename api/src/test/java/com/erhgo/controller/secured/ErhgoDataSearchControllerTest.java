package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.CriteriaQuestionType;
import com.erhgo.generators.CriteriaMotherObject;
import com.erhgo.generators.ErhgoOccupationGenerator;
import com.erhgo.generators.JobActivityLabelGenerator;
import com.erhgo.openapi.dto.ErhgoSearchOrderDTO;
import com.erhgo.repositories.classifications.ErhgoClassificationRepository;
import com.erhgo.repositories.classifications.WorkEnvironmentRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.servlet.ResultActions;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static com.erhgo.TestUtils.*;
import static com.erhgo.generators.TestFixtures.*;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

class ErhgoDataSearchControllerTest extends AbstractIntegrationTestWithFixtures {

    @Autowired
    private ErhgoOccupationGenerator erhgoOccupationGenerator;

    @Autowired
    private JobActivityLabelGenerator activityLabelGenerator;

    @Autowired
    private WorkEnvironmentRepository workEnvironmentRepository;

    @Autowired
    private ErhgoClassificationRepository erhgoClassificationRepository;

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void should_ot_search_return_ordered() throws Exception {
        performSearchOTOk(ErhgoSearchOrderDTO.TITLE.toString(), "ASC", "");
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void should_search_return_ordered_qualifiedSkillNumber() throws Exception {
        performSearchOk(new String[]{ErhgoSearchOrderDTO.QUALIFIED_SKILL_NUMBER.toString(), ErhgoSearchOrderDTO.TITLE.toString()}, new String[]{"ASC", "ASC"}, "", null)
                .andExpect(jsonMatchesContentWithOrderedArray("erhgoOccupationPageOrderByQualifiedSkillNumber"));
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void should_search_by_alternative_label() throws Exception {
        var occupation = createErhgoOccupationForDetails();
        performSearch(new String[]{ErhgoSearchOrderDTO.TITLE.toString()}, new String[]{"ASC"}, "le b", null)
                .andExpect(jsonPath("$.content[0].id", is(occupation.getId().toString())));
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void should_search_return_ordered_not_qualifiedSkillNumber() throws Exception {
        performSearchOk(new String[]{ErhgoSearchOrderDTO.NOT_QUALIFIED_SKILL_NUMBER.toString(), ErhgoSearchOrderDTO.TITLE.toString()}, new String[]{"DESC", "ASC"}, "", null)
                .andExpect(jsonMatchesContentWithOrderedArray("erhgoOccupationPageOrderByNotQualifiedSkillNumber"));
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void should_search_return_ordered_state() throws Exception {
        performSearchOk(new String[]{ErhgoSearchOrderDTO.QUALIFICATION_STATE.toString(), ErhgoSearchOrderDTO.TITLE.toString()}, new String[]{"ASC", "ASC"}, "", null)
                .andExpect(jsonMatchesContentWithOrderedArray("erhgoOccupationPageOrderByState"));
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void should_search_return_ordered_total_desc() throws Exception {
        performSearchOk(new String[]{ErhgoSearchOrderDTO.QUALIFIED_SKILL_NUMBER.toString(), ErhgoSearchOrderDTO.SKILL_SIZE.toString(), ErhgoSearchOrderDTO.TITLE.toString()}, new String[]{"ASC", "DESC", "DESC"}, "", null)
                .andExpect(jsonMatchesContentWithOrderedArray("erhgoOccupationPageOrderByTotalSkills"));
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void should_search_return_with_activity_filter() throws Exception {
        var activity1 = activityLabelGenerator.createActivity("ACT-42", CA1_01, CA1_12);
        var activity2 = activityLabelGenerator.createActivity("ACT-43", CA1_01, CA1_14);

        var skill = erhgoOccupationGenerator.createSkillWithCapacities("Skill for capacities", CA1_01, CA1_16);

        // Occupation 1
        erhgoOccupationGenerator.createErhgoOccupation(
                new ErhgoOccupationMotherObject()
                        .withTitle("Occupation 1")
                        .withSkills(skill)
                        .withOptionalActivities(activity1)
                        .instance()
        );

        // Occupation 2
        erhgoOccupationGenerator.createErhgoOccupation(
                new ErhgoOccupationMotherObject()
                        .withTitle("Occupation 2")
                        .withSkills(skill)
                        .withOptionalActivities(activity1, activity2)
                        .instance()
        );

        // Occupation 3
        erhgoOccupationGenerator.createErhgoOccupation(
                new ErhgoOccupationMotherObject()
                        .withTitle("Occupation 3")
                        .withSkills(skill)
                        .withOptionalActivities(activity2)
                        .instance()
        );

        performSearchOk(new String[]{ErhgoSearchOrderDTO.QUALIFICATION_STATE.toString(), ErhgoSearchOrderDTO.TITLE.toString()}, new String[]{"ASC", "ASC"}, "", activity1.getUuid())
                .andExpect(jsonMatchesContentWithOrderedArray("erhgoOccupationPageWithActivityFilter"));
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void should_search_by_isco_group() throws Exception {
        performSearchOk(new String[]{ErhgoSearchOrderDTO.NOT_QUALIFIED_SKILL_NUMBER.toString(), ErhgoSearchOrderDTO.TITLE.toString()}, new String[]{"ASC", "ASC"}, "666", null)
                .andExpect(jsonPath("$.content", hasSize(1)))
                .andExpect(jsonPath("$.content[0].id", is(ERHGO_OCCUPATION_WITHOUT_ROMES.getId().toString())));
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void should_search_by_esco_uri() throws Exception {
        performSearchOk(new String[]{ErhgoSearchOrderDTO.NOT_QUALIFIED_SKILL_NUMBER.toString(), ErhgoSearchOrderDTO.TITLE.toString()}, new String[]{"ASC", "ASC"}, "http://nodata.odas.app", null)
                .andExpect(jsonPath("$.content", hasSize(1)))
                .andExpect(jsonPath("$.content[0].id", is(ERHGO_OCCUPATION_QUALIFIED.getId().toString())));
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void should_search_by_title() throws Exception {
        performSearchOk(new String[]{ErhgoSearchOrderDTO.NOT_QUALIFIED_SKILL_NUMBER.toString(), ErhgoSearchOrderDTO.TITLE.toString()}, new String[]{"ASC", "ASC"}, "ERHGO_OCCUPATION_WITH", null)
                .andExpect(jsonPath("$.content", hasSize(3)));
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void should_search_fail_for_bad_sortBy() throws Exception {
        performSearch(new String[]{"pipo", ErhgoSearchOrderDTO.TITLE.toString()}, new String[]{"ASC", "ASC"}, "title for", null)
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void should_search_fail_for_bad_sortDirection() throws Exception {
        performSearch(new String[]{ErhgoSearchOrderDTO.NOT_QUALIFIED_SKILL_NUMBER.toString(), ErhgoSearchOrderDTO.TITLE.toString()}, new String[]{"ASC"}, "title for", null)
                .andExpect(status().isBadRequest());
    }

    private ResultActions performSearchOk(String[] sortBy, String[] sortDirection, String query, UUID activityId) throws Exception {
        return performSearch(sortBy, sortDirection, query, activityId)
                .andExpect(status().isOk());
    }

    private ResultActions performSearch(String[] sortBy, String[] sortDirection, String query, UUID activityId) throws Exception {
        return mvc.perform(get("/api/odas/erhgo-occupation/list")
                .param("size", "5")
                .param("by", sortBy)
                .param("direction", sortDirection)
                .param("query", query)
                .param("activityLabelId", activityId == null ? "" : activityId.toString())
                .param("page", "0"));
    }

    private ResultActions performSearchOTOk(String sortBy, String sortDirection, String query) throws Exception {
        return performSearchOT(sortBy, sortDirection, query)
                .andExpect(status().isOk());
    }

    private ResultActions performSearchOT(String sortBy, String sortDirection, String query) throws Exception {
        return mvc.perform(get("/api/odas/erhgo-occupation/simple-list")
                .param("size", "5")
                .param("by", sortBy)
                .param("direction", sortDirection)
                .param("query", query)
                .param("page", "0"));
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void should_search_details_by_id() throws Exception {
        var occupation = createErhgoOccupationForDetails();

        mvc.perform(get("/api/odas/erhgo-occupation/detail/%s".formatted(occupation.getId())))
                .andExpect(status().isOk())
                .andExpect(jsonMatchesContentWithOrderedArray("erhgoOccupationDetails"));

    }

    @Test
    @WithMockKeycloakUser(roles = Role.CANDIDATE)
    @ResetDataAfter
    void should_search_details_by_id_sum_up() throws Exception {
        var occupation = createErhgoOccupationForDetails();

        mvc.perform(get("/api/odas/erhgo-occupation/sum-up/%s".formatted(occupation.getId())))
                .andExpect(status().isOk())
                .andExpect(jsonMatchesContent("erhgoOccupationSumUp"));

    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void should_export_details_by_id() throws Exception {
        var occupation = createErhgoOccupationForDetails();

        mvc.perform(get("/api/odas/erhgo-occupation/" + occupation.getId() + "/pdf"))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_PDF))
                .andExpect(pdfMatchesContent("expectedErhgoOccupationIntegrationTest.pdf"));

    }


    private ErhgoOccupation createErhgoOccupationForDetails() {
        return txHelper.doInTransaction(() -> {
            var skill1 = erhgoOccupationGenerator.createSkillWithReferentialData("Sk 1", ACT_20, B_02, CT_21);
            var skill2 = erhgoOccupationGenerator.createSkillWithReferentialData("Sk 2", ACT_20, B_02, CT_21);
            var occupation = erhgoOccupationGenerator.createErhgoOccupationForReferentialDatas(
                    UUID.fromString("ac68c0bc-c83a-4c56-8393-db7d72644be0"),
                    true,
                    ACT_20,
                    ACT_11,
                    CT_21,
                    MANDATORY_CONTEXT,
                    B_02,
                    B_04,
                    skill1,
                    skill2
            );
            var criteria1 = applicationContext.getBean(CriteriaMotherObject.class).withValueCodes("1-1", "1-2", "1-3").withQuestionType(CriteriaQuestionType.THRESHOLD).buildAndPersist();
            var criteria2 = applicationContext.getBean(CriteriaMotherObject.class).withValueCodes("2-1", "2-2", "2-3").withQuestionType(CriteriaQuestionType.MULTIPLE).buildAndPersist();
            occupation.setWorkEnvironments(new HashSet<>(workEnvironmentRepository.findByCodeIn(Set.of("ENV-05", "ENV-02"))));
            occupation.setCriteriaValues(Set.of(criteria1.getCriteriaValues().get(1), criteria2.getCriteriaValues().get(0), criteria1.getCriteriaValues().get(2)));
            ((Set<ErhgoClassification>) ReflectionTestUtils.getField(occupation, "erhgoClassifications")).addAll(erhgoClassificationRepository.findErhgoClassificationByCodeIn(List.of("SO-03", "SO-05")));
            occupation.setBehaviorsDescription("Behaviors Description of erhgo occupation %s".formatted(occupation.getId()));
            return occupation;
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void should_find_erhgo_capacities() throws Exception {
        var activity1 = activityLabelGenerator.createActivity("ACT-42", CA1_01, CA1_12);
        var activity2 = activityLabelGenerator.createActivity("ACT-43", CA1_01, CA1_14);

        var skill = erhgoOccupationGenerator.createSkillWithCapacities("Skill for capacities", CA1_01, CA1_16);

        var occupation = erhgoOccupationGenerator.createErhgoOccupation(
                new ErhgoOccupationMotherObject()
                        .withSkills(skill)
                        .withOptionalActivities(activity1, activity2)
                        .instance()
        );

        mvc.perform(get("/api/odas/erhgo-occupation/" + occupation.getId() + "/capacities"))
                .andExpect(status().isOk())
                .andExpect(jsonMatchesContent("erhgoCapacities"));

    }
}
