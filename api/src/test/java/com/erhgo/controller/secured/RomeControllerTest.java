package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.mailing.MailingListService;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import static com.erhgo.TestUtils.jsonMatchesContentWithOrderedArray;
import static com.erhgo.generators.TestFixtures.R1;
import static org.hamcrest.Matchers.is;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class RomeControllerTest extends AbstractIntegrationTestWithFixtures {

    @MockitoBean
    MailingListService mailingListService;

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void search_rome_page_by_title_should_succeed() throws Exception {

        mvc.perform(get("/api/odas/rome/list")
                        .param("size", "5")
                        .param("query", "TlE foR")
                        .param("page", "0"))
                .andExpect(jsonMatchesContentWithOrderedArray("romePage"))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void search_rome_page_by_code_should_succeed() throws Exception {

        mvc.perform(get("/api/odas/rome/list")
                        .param("size", "5")
                        .param("query", R1.getCode())
                        .param("page", "0"))
                .andExpect(jsonPath("$.content[0].code", is(R1.getCode())))
                .andExpect(status().isOk());
    }
}
