package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.enums.NotificationType;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.domain.userprofile.notification.DefaultNotification;
import com.erhgo.openapi.dto.AlgoliaQueryDTO;
import com.erhgo.openapi.dto.SaveUserMobileTokenCommandDTO;
import com.erhgo.openapi.dto.SendNotificationsToUsersSelectionCommandDTO;
import com.erhgo.openapi.dto.UsersMobileNotificationDTO;
import com.erhgo.repositories.NotificationRepository;
import com.erhgo.repositories.UserMobileTokenRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.mailing.MailingListService;
import com.erhgo.services.notifications.NotificationSenderInterface;
import com.erhgo.services.notifications.NotificationService;
import com.erhgo.services.search.UserIndexer;
import com.erhgo.services.userprofile.UserMobileService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.testcontainers.shaded.org.bouncycastle.util.Objects;

import java.net.URI;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.IntStream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class UserMobileControllerTest extends AbstractIntegrationTest {
    public static final String URL_OK = "http://yo.lo";
    @Autowired
    UserMobileTokenRepository userMobileRepository;

    @Autowired
    UserMobileService userMobileService;

    @MockitoBean
    NotificationSenderInterface notificationSenderService;

    @MockitoBean
    UserIndexer userIndexer;

    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    NotificationRepository notificationRepository;

    @MockitoBean
    MailingListService mailingListService;

    static final String USER_ID_1 = "b2eb9fd1-a22a-438e-9a58-f6f347b5d645";
    static final String USER_ID_2 = "b2eb9fd1-a22a-438e-9a58-f6f347b5d646";
    static final String SUBJECT = "Sujet de la notif";
    static final String CONTENT = "Contenu de la notif";
    private LocalDateTime lastMobileDate;

    @BeforeEach
    void initUser() {
        applicationContext.getBean(UserProfileMotherObject.class).withUserId(USER_ID_1).buildAndPersist();
        lastMobileDate = LocalDateTime.of(2022, 10, 9, 8, 7, 6);
        applicationContext.getBean(UserProfileMotherObject.class).withUserId(USER_ID_2).withLastMobileConnectionDate(lastMobileDate).buildAndPersist();
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.CANDIDATE)
    void save_new_mobile_token() throws Exception {
        var token = "test token notification firebase";
        var command = new SaveUserMobileTokenCommandDTO().token(token).userId(USER_ID_1);
        performPost("/user-mobile/save-token", command)
                .andExpect(status().isNoContent());

        Mockito.verify(mailingListService).updateUserMobileUsage(any());

        txHelper.doInTransaction(() -> {
            var tokens = userMobileRepository.findByUserProfileUserId(USER_ID_1);
            assertThat(tokens)
                    .hasSize(1)
                    .allMatch(n -> n.getToken().equals(command.getToken()))
                    .allMatch(n -> n.getUserProfile().generalInformation().getLastConnectionDate() != null);
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.CANDIDATE)
    void edit_existing_mobile_token() throws Exception {
        var token = "test token notification firebase";
        var command = new SaveUserMobileTokenCommandDTO().token(token).userId(USER_ID_1);
        performPost("/user-mobile/save-token", command)
                .andExpect(status().isNoContent());

        performPost("/user-mobile/save-token", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var tokens = userMobileRepository.findByUserProfileUserId(USER_ID_1);
            assertThat(tokens)
                    .hasSize(1)
                    .allMatch(n -> n.getToken().equals(command.getToken()));
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.CANDIDATE)
    void delete_obsolete_mobile_tokens() throws Exception {
        var firstCommand = new SaveUserMobileTokenCommandDTO()
                .token("obsolete token")
                .userId(USER_ID_1);
        performPost("/user-mobile/save-token", firstCommand)
                .andExpect(status().isNoContent());

        var secondCommand = new SaveUserMobileTokenCommandDTO()
                .token("second token")
                .userId(USER_ID_2);
        performPost("/user-mobile/save-token", secondCommand)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var obsoleteToken = userMobileRepository.findByUserProfileUserId(USER_ID_1).stream().findFirst().orElseThrow();
            obsoleteToken.setTimestamp(OffsetDateTime.now().minusMonths(3L));
            userMobileRepository.save(obsoleteToken);

            assertThat(userMobileRepository.findAll()).hasSize(2);

            userMobileService.cleanUsersMobileTokens();

            var tokens = userMobileRepository.findAll();
            assertThat(tokens)
                    .hasSize(1)
                    .allMatch(n -> n.getToken().equals(secondCommand.getToken()))
                    .allMatch(n -> n.getTimestamp().toLocalDateTime().isAfter(lastMobileDate))
            ;
        });
        Mockito.verify(mailingListService, Mockito.times(3)).updateUserMobileUsage(any());
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void send_notification_to_multiple_users() throws Exception {
        var usersId = List.of(USER_ID_1, USER_ID_2);
        var firstCommand = new SaveUserMobileTokenCommandDTO()
                .token("first token")
                .userId(usersId.get(0));
        performPost("/user-mobile/save-token", firstCommand)
                .andExpect(status().isNoContent());

        var secondCommand = new SaveUserMobileTokenCommandDTO()
                .token("second token")
                .userId(usersId.get(1));
        performPost("/user-mobile/save-token", secondCommand)
                .andExpect(status().isNoContent());

        var notificationsCommand = new UsersMobileNotificationDTO()
                .usersId(usersId)
                .subject("test subject")
                .content("lorem ipsum")
                .data(Map.of());
        performPost("/user-mobile/send-notifications", notificationsCommand)
                .andExpect(status().isNoContent());

        var expectedNotifications = new TreeMap<>(Map.of("first token", 1L, "second token", 1L));
        Mockito.verify(notificationSenderService).sendNotificationToSpecificDevices(
                eq(expectedNotifications),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyMap());
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void send_notification_to_users_selection_empty_token() throws Exception {
        var query = new AlgoliaQueryDTO();
        Mockito.when(userIndexer.findMatchingUserIds(query)).thenReturn(List.of(USER_ID_1));
        performPost("/user-mobile/notification/send", new SendNotificationsToUsersSelectionCommandDTO().usersSelectionQuery(query).subject(SUBJECT).content(CONTENT)).andExpect(status().isNoContent());
        Mockito.verifyNoInteractions(notificationSenderService);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void send_notification_to_users_selection_single_token() throws Exception {
        var query = new AlgoliaQueryDTO();
        var mobileToken = "4422";
        var user = applicationContext.getBean(UserProfileMotherObject.class).withMobileToken(mobileToken).buildAndPersist();
        Mockito.when(userIndexer.findMatchingUserIds(query)).thenReturn(List.of(user.userId()));
        performPost("/user-mobile/notification/send", new SendNotificationsToUsersSelectionCommandDTO().usersSelectionQuery(query).subject(SUBJECT).content(CONTENT)).andExpect(status().isNoContent());

        Mockito.verify(notificationSenderService).sendNotificationToSpecificDevices(Map.of(mobileToken, 1L), SUBJECT, CONTENT, null);
        Mockito.verifyNoMoreInteractions(notificationSenderService);
    }

    @ParameterizedTest
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ValueSource(strings = {URL_OK})
    @NullSource
    void send_notification_to_users_selection_multiple_token(String url) throws Exception {
        NotificationService.MAX_LENGTH_OF_IN = 2;
        var query = new AlgoliaQueryDTO();
        var mobileToken = "4422";
        var users = IntStream.range(0, 10).mapToObj(a -> applicationContext.getBean(UserProfileMotherObject.class).withMobileToken(mobileToken + a).buildAndPersist().userId()).toList();
        Mockito.when(userIndexer.findMatchingUserIds(query)).thenReturn(users);
        performPost("/user-mobile/notification/send", new SendNotificationsToUsersSelectionCommandDTO()
                .usersSelectionQuery(query)
                .link(url == null ? null : URI.create(url))
                .subject(SUBJECT)
                .content(CONTENT))
                .andExpect(status().isNoContent());

        Mockito.verify(notificationSenderService).sendNotificationToSpecificDevices(ArgumentMatchers.argThat(c -> c.size() == 10 && c.keySet().stream().allMatch(e -> e.startsWith(mobileToken))), eq(SUBJECT), eq(CONTENT), isNull());
        Mockito.verifyNoMoreInteractions(notificationSenderService);
        txHelper.doInTransaction(() -> {
            var allNotif = notificationRepository.findAll();
            assertThat(allNotif).hasSize(10);
            assertThat(allNotif).allMatch(u -> users.stream().anyMatch(up -> up.equals(u.getUserProfile().userId())));
            assertThat(allNotif).allMatch(u -> u.getType() == NotificationType.MOBILE);
            assertThat(allNotif).allMatch(u -> Objects.areEqual(((DefaultNotification) u).getLink(), url));
            assertThat(allNotif).allMatch(u -> ((DefaultNotification) u).getSubject().equals(SUBJECT));
            assertThat(allNotif).allMatch(u -> ((DefaultNotification) u).getContent().equals(CONTENT));
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void count_notifications_one_with_token_two_without() throws Exception {
        var query = new AlgoliaQueryDTO().query("").filters("");
        var mobileToken = "4422";
        var users = IntStream.range(0, 3).mapToObj(a -> applicationContext.getBean(UserProfileMotherObject.class).withMobileToken(a == 0 ? mobileToken + a : null).buildAndPersist().userId()).toList();
        Mockito.when(userIndexer.findMatchingUserIds(query)).thenReturn(users);
        performGetAndExpect("/user-mobile/notification/count?query=&filters=", "notified-user-count", true);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void send_notifications_one_with_token_two_without() throws Exception {
        var query = new AlgoliaQueryDTO().query("").filters("");
        var mobileToken = "4422";
        var users = IntStream.range(0, 3).mapToObj(a -> applicationContext.getBean(UserProfileMotherObject.class).withMobileToken(a == 0 ? mobileToken + a : null).buildAndPersist().userId()).toList();
        Mockito.when(userIndexer.findMatchingUserIds(query)).thenReturn(users);
        performPost("/user-mobile/notification/send", new SendNotificationsToUsersSelectionCommandDTO().usersSelectionQuery(query).subject(SUBJECT).content(CONTENT)).andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var allNotif = notificationRepository.findAll();
            assertThat(allNotif).hasSize(3);
            assertThat(allNotif).anyMatch(u -> u.getUserProfile().userId().equals(users.get(0)) && u.getType() == NotificationType.MOBILE);
            assertThat(allNotif).filteredOn(u -> !u.getUserProfile().userId().equals(users.get(0))).allMatch(u -> !u.getUserProfile().userId().equals(users.get(0)) && u.getType() == NotificationType.NONE);
        });

    }


    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void count_notifications_serialize_query() throws Exception {
        var query = new AlgoliaQueryDTO().query("a").filters("b").aroundLatLng("c").aroundRadius(42);
        var mobileToken = "4422";
        var users = IntStream.range(0, 3).mapToObj(a -> applicationContext.getBean(UserProfileMotherObject.class).withMobileToken(a == 0 ? mobileToken + a : null).buildAndPersist().userId()).toList();
        Mockito.when(userIndexer.findMatchingUserIds(query)).thenReturn(users);
        performGetAndExpect("/user-mobile/notification/count?query=a&filters=b&aroundLatLng=c&aroundRadius=42", "notified-user-count", true);
    }
}
