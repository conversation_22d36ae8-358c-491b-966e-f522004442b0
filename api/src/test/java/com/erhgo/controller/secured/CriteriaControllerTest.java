package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.enums.CriteriaQuestionType;
import com.erhgo.domain.enums.SourcingCriteriaStep;
import com.erhgo.generators.CriteriaMotherObject;
import com.erhgo.generators.UserProfileGenerator;
import com.erhgo.openapi.dto.EditCriteriaCommandDTO;
import com.erhgo.openapi.dto.EditCriteriaValueCommandDTO;
import com.erhgo.repositories.CriteriaRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.dao.DataIntegrityViolationException;

import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class CriteriaControllerTest extends AbstractIntegrationTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Autowired
    private CriteriaRepository criteriaRepository;

    private static final String USER_ID = "cf6c1057-dbc4-4297-856a-ee6a0cd5d6ce";

    @Test
    @WithMockKeycloakUser
    @ResetDataAfter
    void get_criteria() throws Exception {
        applicationContext.getBean(CriteriaMotherObject.class)
                .withCriteriaIndex(1)
                .withQuestionType(CriteriaQuestionType.MULTIPLE)
                .withRequired(true)
                .buildAndPersist();
        applicationContext.getBean(CriteriaMotherObject.class)
                .withCriteriaIndex(2)
                .withQuestionType(CriteriaQuestionType.THRESHOLD)
                .withRequired(false)
                .buildAndPersist();
        applicationContext.getBean(CriteriaMotherObject.class)
                .withCriteriaIndex(3)
                .withSourcingCriteriaStep(SourcingCriteriaStep.STEP1)
                .buildAndPersist();
        applicationContext.getBean(CriteriaMotherObject.class)
                .withCriteriaIndex(4)
                .withSourcingCriteriaStep(SourcingCriteriaStep.STEP1)
                .withQuestionType(CriteriaQuestionType.THRESHOLD)
                .withRequired(false)
                .buildAndPersist();

        performGetAndExpect("/criteria/list", "criteriaQuestionsList", true);
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void update_criteria_value() throws Exception {
        var criteria = applicationContext.getBean(CriteriaMotherObject.class)
                .withQuestionType(CriteriaQuestionType.THRESHOLD)
                .withValueCodes("1", "2", "3")
                .buildAndPersist();
        var newTitleForBO = "un CDI";
        var newTitleStandalone = "Toto";
        var newTitleForQuestion = "en CDI uniquement";
        var newIcon = "newIcon";
        var editCriteriaValueCommandDTO = new EditCriteriaValueCommandDTO()
                .titleForBO(newTitleForBO)
                .titleForQuestion(newTitleForQuestion)
                .titleStandalone(newTitleStandalone)
                .code("1")
                .icon(newIcon);
        var editCriteriaCommand = new EditCriteriaCommandDTO().criteriaValues(List.of(editCriteriaValueCommandDTO)).title(criteria.getTitle()).questionLabel(criteria.getQuestionLabel());

        performPost("/criteria/edit/" + criteria.getCode(), editCriteriaCommand);

        txHelper.doInTransaction(() -> {
            var criterion = criteriaRepository.findById(criteria.getCode()).orElseThrow();
            var updatedCriterionValue = criterion.getCriteriaValues().get(0);
            assertThat(updatedCriterionValue.getTitleForBO()).isEqualTo(newTitleForBO);
            assertThat(updatedCriterionValue.getTitleForQuestion()).isEqualTo(newTitleForQuestion);
            assertThat(updatedCriterionValue.getTitleStandalone()).isEqualTo(newTitleStandalone);
            assertThat(updatedCriterionValue.getIcon()).isEqualTo(newIcon);
            assertThat(criteriaRepository.findById(criteria.getCode()).orElseThrow().getCriteriaValues().get(1)).isEqualTo(criteria.getCriteriaValues().get(1));
            assertThat(criteriaRepository.findById(criteria.getCode()).orElseThrow().getCriteriaValues().get(2)).isEqualTo(criteria.getCriteriaValues().get(2));
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void update_criteria_title_and_questionLabel() throws Exception {
        var criteria = applicationContext.getBean(CriteriaMotherObject.class)
                .withQuestionType(CriteriaQuestionType.THRESHOLD)
                .withValueCodes("1", "2", "3").buildAndPersist();
        var expectedTitle = "nouveau titre";
        var expectedQuestion = "nouvelle question";
        var editCriteriaCommand = new EditCriteriaCommandDTO()
                .title(expectedTitle)
                .questionLabel(expectedQuestion);

        performPost("/criteria/edit/" + criteria.getCode(), editCriteriaCommand);

        txHelper.doInTransaction(() -> {
            var criterion = criteriaRepository.findById(criteria.getCode()).orElseThrow();
            assertThat(criterion.getTitle()).isEqualTo(expectedTitle);
            assertThat(criterion.getQuestionLabel()).isEqualTo(expectedQuestion);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.CANDIDATE, id = USER_ID)
    @ResetDataAfter
    void get_criteria_sorted_by_criteria_index() throws Exception {
        applicationContext.getBean(CriteriaMotherObject.class).prepareCriteriaAnswers(USER_ID);
        performGetAndExpect("/user/" + USER_ID + "/profileSummary", "userProfileWithSortedCriteria", true);
    }

    @Test
    void criteria_value_index_is_uniq() {
        Exception caught = null;
        try {
            applicationContext.getBean(CriteriaMotherObject.class)
                    .withValueAtIndex(0)
                    .withValueAtIndex(0)
                    .buildAndPersist();
        } catch (DataIntegrityViolationException e) {
            caught = e;
        }
        assertNotNull(caught);
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void get_profile_contains_multiple_criteria_only() {
        applicationContext.getBean(CriteriaMotherObject.class).prepareMultipleCriteriaAndAnswers(USER_ID);
        performGetAndExpect("/user/" + USER_ID + "/profileSummary", "userProfileSummaryWithCriteria", false);
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void reset_will_remove_filtered_criteria_only() throws Exception {
        var user = userProfileGenerator.createUserProfile(UUID.fromString(USER_ID));
        applicationContext.getBean(CriteriaMotherObject.class).prepareFilteredCriteriaAnswers(user);
        performDelete("/user/criterias/reset", null).andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var criteriaValues = criteriaRepository.findUserCriteriaValuesByUserId(USER_ID);
            assertThat(criteriaValues).hasSize(2);
            assertThat(criteriaValues.stream().map(cv -> cv.getValue().getCriteria().getCode())).contains("CR-10");

            var simpleCriteriasValues = criteriaRepository.findCriteriaValuesByUserIdAndCriteriaCodes(USER_ID, List.of("CR-1", "CR-2"));
            assertThat(simpleCriteriasValues).hasSize(6);
        });
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void get_simple_criteria_contains_filtered_criteria_only() {
        var user = userProfileGenerator.createUserProfile(UUID.fromString(USER_ID));
        applicationContext.getBean(CriteriaMotherObject.class).prepareFilteredCriteriaAnswers(user);
        performGetAndExpect("/api/odas/user/simple-criterias", "userFilteredCriterias", true);
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void get_simple_criteria_first_time() {
        var user = userProfileGenerator.createUserProfile(UUID.fromString(USER_ID));
        var user2 = userProfileGenerator.createUserProfile(UUID.fromString("cf6c1057-dbc4-4297-856a-ee6a0cd5d6cf"));
        applicationContext.getBean(CriteriaMotherObject.class).prepareFilteredCriteriaAnswers(user2);
        performGetAndExpect("/api/odas/user/simple-criterias", "userFilteredCriteriasEmptyAnswer", true);
    }
}
