package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTest;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;

import static org.apache.commons.lang3.StringUtils.repeat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class ErhgoOccupationControllerTest extends AbstractIntegrationTest {

    @SneakyThrows
    @Test
    void search_fails_for_too_long_query() {
        var longString = repeat('A', 251);
        mvc.perform(get(realUrl("/erhgo-occupation/occupation/search-occupations"))
                        .queryParam("query", longString)
                        .queryParam("highlight", String.valueOf(false)))
                .andExpect(status().isBadRequest());
    }

    @SneakyThrows
    @Test
    void search_succeed_for_not_too_long_query() {
        var longString = repeat('à', 250);
        mvc.perform(get(realUrl("/erhgo-occupation/occupation/search-occupations"))
                        .queryParam("query", longString)
                        .queryParam("highlight", String.valueOf(false)))
                .andExpect(status().isOk());
    }
}
