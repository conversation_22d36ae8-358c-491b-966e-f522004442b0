package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.AbstractEntity;
import com.erhgo.domain.enums.QuestionType;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.CapacityRelatedQuestionGenerator;
import com.erhgo.generators.UserProfileGenerator;
import com.erhgo.openapi.dto.QuestionTypeDTO;
import com.erhgo.openapi.dto.ResponseForCapacityRelatedQuestionDTO;
import com.erhgo.openapi.dto.SaveCapacityRelatedQuestionCommandDTO;
import com.erhgo.repositories.AnswerForCapacityRelatedQuestionRepository;
import com.erhgo.repositories.CapacityRelatedQuestionRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.assertj.core.util.Lists;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.HashMap;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class CapacityRelatedQuestionControllerTest extends AbstractIntegrationTest {

    @Autowired
    private CapacityGenerator capacityGenerator;

    @Autowired
    private CapacityRelatedQuestionRepository repository;

    @Autowired
    private AnswerForCapacityRelatedQuestionRepository answerRepository;

    @Autowired
    private CapacityRelatedQuestionGenerator questionGenerator;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Test
    @DisplayName("Given two capacities when I put extra pro question save command then question is persisted")
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void save_question() throws Exception {
        var capacity1 = capacityGenerator.createCapacity("CA-1");
        var capacity2 = capacityGenerator.createCapacity("CA-2");

        var title1 = "Answer 1";
        var title2 = "Answer 2";
        var command = new SaveCapacityRelatedQuestionCommandDTO()
                .id(UUID.randomUUID())
                .questionType(QuestionTypeDTO.EXTRAPROFESSIONAL)
                .title("How are you ?")
                .responses(
                        List.of(
                                new ResponseForCapacityRelatedQuestionDTO()
                                        .id(UUID.randomUUID())
                                        .title(title1)
                                        .capacities(
                                                List.of(capacity1.getId(), capacity2.getId())),
                                new ResponseForCapacityRelatedQuestionDTO()
                                        .id(UUID.randomUUID())
                                        .title(title2)
                                        .capacities(List.of(capacity2.getId()))
                        )
                );

        performPut("/capacity-related-question/question/save", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var question = repository.getOne(command.getId());
            var answers = question.getResponses();
            var answersIterator = answers.iterator();
            var answer1 = answersIterator.next();
            var answer2 = answersIterator.next();
            assertAll(
                    () -> assertThat(question.getQuestionIndex()).isZero(),
                    () -> assertThat(question.getQuestionType()).hasToString(QuestionType.EXTRAPROFESSIONAL.name()),
                    () -> assertThat(answers).hasSize(2),
                    () -> assertThat(answer1.getPosition()).isZero(),
                    () -> assertThat(answer2.getPosition()).isEqualTo(1),
                    () -> assertThat(answer1.getCapacities().stream().map(AbstractEntity::getId)).contains(capacity1.getId(), capacity2.getId()),
                    () -> assertThat(answer2.getCapacities().stream().map(AbstractEntity::getId)).contains(capacity2.getId()),
                    () -> assertThat(answer1.getTitle()).isEqualTo(title1),
                    () -> assertThat(answer2.getTitle()).isEqualTo(title2)
            );
        });
    }

    @ParameterizedTest
    @EnumSource(QuestionTypeDTO.class)
    @DisplayName("Given an extra pro activity question when I remove an answer used by any user then answer is no more associated to user")
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void remove_answer(QuestionTypeDTO questionType) throws Exception {

        var secondAnswerId = UUID.randomUUID();
        var userProfile = userProfileGenerator.createUserProfile();
        var question = questionGenerator.createExtraProQuestionWithSelectedAnswer(
                0, userProfile, UUID.randomUUID(), secondAnswerId);

        var title1 = "Answer 1";
        var title2 = "Answer 2";
        var firstAnswerId = UUID.randomUUID();
        var command = new SaveCapacityRelatedQuestionCommandDTO()
                .id(question.getId())
                .questionType(questionType)
                .title("How are you ?")
                .responses(
                        List.of(
                                new ResponseForCapacityRelatedQuestionDTO()
                                        .id(firstAnswerId)
                                        .title(title1),
                                new ResponseForCapacityRelatedQuestionDTO()
                                        .id(secondAnswerId)
                                        .title(title2))
                );

        performPut("/capacity-related-question/question/save", command)
                .andExpect(status().isNoContent());
        assertThat(answerRepository.count()).isZero();
    }


    @ParameterizedTest
    @EnumSource(QuestionTypeDTO.class)
    @DisplayName("Given an extra pro activity question when I update, add and remove answer then it should be updated")
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void update_question(QuestionTypeDTO questionType) throws Exception {
        var capacity1 = capacityGenerator.createCapacity("CA-1");
        var capacity2 = capacityGenerator.createCapacity("CA-2");
        var capacity3 = capacityGenerator.createCapacity("CA-3");

        var question = questionGenerator.createQuestion(capacity1, capacity2, 0);

        var title1 = "Answer 1";
        var title2 = "Answer 2";
        var firstAnswerId = question.getResponses().iterator().next().getId();
        var secondAnswerId = UUID.randomUUID();
        var command = new SaveCapacityRelatedQuestionCommandDTO()
                .id(question.getId())
                .title("How are you ?")
                .questionType(questionType)
                .responses(
                        List.of(
                                new ResponseForCapacityRelatedQuestionDTO()
                                        .id(firstAnswerId)
                                        .title(title1)
                                        .capacities(
                                                List.of(capacity1.getId(), capacity3.getId())),
                                new ResponseForCapacityRelatedQuestionDTO()
                                        .id(secondAnswerId)
                                        .title(title2)
                                        .capacities(List.of(capacity3.getId()))
                        )
                );

        performPut("/capacity-related-question/question/save", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var questions = repository.findAll();
            var updatedQuestion = questions.iterator().next();
            var answers = updatedQuestion.getResponses();
            var answersIterator = answers.iterator();
            var answer1 = answersIterator.next();
            var answer2 = answersIterator.next();
            assertAll(
                    () -> assertThat(updatedQuestion.getQuestionIndex()).isZero(),
                    () -> assertThat(question.getQuestionType()).isEqualTo(QuestionType.valueOf(questionType.name())),
                    () -> assertThat(updatedQuestion.getId()).isEqualTo(command.getId()),
                    () -> assertThat(questions).hasSize(1),
                    () -> assertThat(answers).hasSize(2),
                    () -> assertThat(answer1.getId()).isEqualTo(firstAnswerId),
                    () -> assertThat(answer2.getId()).isEqualTo(secondAnswerId),
                    () -> assertThat(answer1.getCapacities().stream().map(AbstractEntity::getId)).contains(capacity1.getId(), capacity3.getId()),
                    () -> assertThat(answer2.getCapacities().stream().map(AbstractEntity::getId)).contains(capacity3.getId()),
                    () -> assertThat(answer1.getTitle()).isEqualTo(title1),
                    () -> assertThat(answer2.getTitle()).isEqualTo(title2)
            );

        });
    }


    @Test
    @DisplayName("Given an extra pro activity question when I get details then details is properly serialized")
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void get_question() throws Exception {
        var capacity1 = capacityGenerator.createCapacity("CA-1");
        var capacity2 = capacityGenerator.createCapacity("CA-2", capacity1);
        var capacity3 = capacityGenerator.createCapacity("CA-3", capacity2);

        var question = questionGenerator.createQuestion(capacity1, capacity3, 0);
        performGetAndExpect("/capacity-related-question/question/" + question.getId(), "capacityRelatedQuestionDetails", false)
                .andExpect(jsonPath("$.questionType", Matchers.is(QuestionType.EXTRAPROFESSIONAL.name())));
    }

    @Test
    @DisplayName("Given a wrong question id when I get details then I get code 404")
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void get_question_404() throws Exception {
        mvc.perform(get("/api/odas/capacity-related-question/question/" + UUID.randomUUID()))
                .andExpect(MockMvcResultMatchers.status().isNotFound());
    }

    @Test
    @DisplayName("Given a pagination size and a page I get a list of questions summary")
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void list_question_questions() {
        questionGenerator.createEmptyQuestion(1);
        questionGenerator.createEmptyQuestion(0);
        questionGenerator.createEmptyQuestion(2);

        performGetAndExpect("/capacity-related-question/question/list?page=0&size=25&questionType=EXTRAPROFESSIONAL", "capacityRelatedQuestionSummaryList", false);
    }

    @Test
    @DisplayName("Given a list of questions When I request a reorder of an index I get a reordered list")
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void should_reorder_formation_questions() throws Exception {
        var question1 = questionGenerator.createEmptyQuestion(1);
        var question2 = questionGenerator.createEmptyQuestion(0);

        var questionsToReorder = new HashMap<String, Integer>();
        questionsToReorder.put(question1.getId().toString(), question2.getQuestionIndex());
        questionsToReorder.put(question2.getId().toString(), question1.getQuestionIndex());

        performPost("/capacity-related-question/question/reorderQuestions", questionsToReorder).andExpect(status().isNoContent());
        assertThat(repository.findById(question1.getId()).orElseThrow().getQuestionIndex()).isEqualTo(question2.getQuestionIndex());
        assertThat(repository.findById(question2.getId()).orElseThrow().getQuestionIndex()).isEqualTo(question1.getQuestionIndex());
    }

    @ParameterizedTest
    @EnumSource(QuestionType.class)
    @DisplayName("Given two duplicated responses title when I save extra pro question then DataIntegrityViolationException is thrown")
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void should_http_500_when_duplicated_response_title(QuestionType questionType) throws Exception {
        var title = "Duplicated answer";
        var command = new SaveCapacityRelatedQuestionCommandDTO()
                .id(UUID.randomUUID())
                .questionType(QuestionTypeDTO.fromValue(questionType.name()))
                .title("How are you ?")
                .responses(
                        List.of(
                                new ResponseForCapacityRelatedQuestionDTO()
                                        .id(UUID.randomUUID())
                                        .title(title)
                                        .capacities(Lists.emptyList()),
                                new ResponseForCapacityRelatedQuestionDTO()
                                        .id(UUID.randomUUID())
                                        .title(title)
                                        .capacities(Lists.emptyList())
                        )
                );

        boolean caught = true;
        try {
            performPut("/capacity-related-question/question/save", command);
        } catch (Exception e) {
            caught = ExceptionUtils.indexOfType(e, DataIntegrityViolationException.class) >= 0;
        }
        assertThat(caught).isTrue();
    }


}
