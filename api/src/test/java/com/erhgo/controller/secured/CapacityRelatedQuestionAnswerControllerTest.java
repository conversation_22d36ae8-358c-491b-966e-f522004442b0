package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.referential.CapacityRelatedQuestion;
import com.erhgo.domain.userprofile.CapacityOccurrence;
import com.erhgo.domain.userprofile.capacityrelatedquestion.AnswerForCapacityRelatedQuestion;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.CapacityRelatedQuestionGenerator;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.generators.UserProfileGenerator;
import com.erhgo.openapi.dto.SaveAnswerToCapacityRelatedQuestionCommandDTO;
import com.erhgo.repositories.AnswerForCapacityRelatedQuestionRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.userprofile.UserProfileProvider;
import jakarta.persistence.EntityManager;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

class CapacityRelatedQuestionAnswerControllerTest extends AbstractIntegrationTest {

    @Autowired
    private AnswerForCapacityRelatedQuestionRepository answerRepository;

    @Autowired
    private CapacityGenerator capacityGenerator;

    @Autowired
    private CapacityRelatedQuestionGenerator questionGenerator;

    @Autowired
    private UserProfileProvider userProfileProvider;

    @Autowired
    private EntityManager entityManager;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    private static final String USER_ID = "171b4993-698f-4f9d-89b0-b5e9818c8114";

    private static final String CHANNEL = "E-456";

    @Autowired
    private KeycloakMockService keycloakService;

    @Autowired
    private OrganizationGenerator organizationGenerator;

    @BeforeEach
    void before() {
        var user = new UserRepresentation();
        user.setId(USER_ID);
        keycloakService.setUserProfile(USER_ID, user);
    }

    @Test
    @DisplayName("Given an extra pro activity question when I answer the question then it should be persisted")
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void first_answer_to_question() throws Exception {
        answerToQuestionCommon();
    }

    private CapacityRelatedQuestion answerToQuestionCommon() throws Exception {
        var answer1Uuid = UUID.randomUUID();
        var answer2Uuid = UUID.randomUUID();
        var answer3Uuid = UUID.randomUUID();

        var question = questionGenerator.createQuestion(0, answer1Uuid, answer2Uuid, answer3Uuid);

        var command = new SaveAnswerToCapacityRelatedQuestionCommandDTO()
                .answers(List.of(answer2Uuid, answer3Uuid))
                .questionId(question.getId());

        performPut("/capacity-related-question/question/for-user", command)
                .andExpect(MockMvcResultMatchers.status().isNoContent());

        var allAnswers = answerRepository.findAll();
        Assertions.assertAll(
                () -> assertThat(allAnswers).hasSize(2),
                () -> assertThat(allAnswers).allMatch(a -> a.getUserProfile().userId().equals(USER_ID)),
                () -> assertThat(allAnswers).anyMatch(a -> a.getResponse().getId().equals(answer2Uuid)),
                () -> assertThat(allAnswers).anyMatch(a -> a.getResponse().getId().equals(answer3Uuid))
        );
        return question;
    }

    @Test
    @DisplayName("Given an extra pro activity question already answered when I update the question answer then it should be persisted")
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void re_answer_to_question() throws Exception {
        var question = answerToQuestionCommon();

        var newSelectedResponseId = question.getResponses().first().getId();

        performPut(
                "/capacity-related-question/question/for-user",
                new SaveAnswerToCapacityRelatedQuestionCommandDTO()
                        .answers(List.of(newSelectedResponseId))
                        .questionId(question.getId())
        )
                .andExpect(MockMvcResultMatchers.status().isNoContent());

        var allAnswers = answerRepository.findAll();
        Assertions.assertAll(
                () -> assertThat(allAnswers).hasSize(1),
                () -> assertThat(allAnswers).anyMatch(a -> a.getResponse().getId().equals(newSelectedResponseId))
        );

    }

    @Test
    @DisplayName("Given two extra pro questions when I get questions list then I get two questions correctly ordered")
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void get_extra_pro_activity_summary() throws Exception {
        var capacity1 = capacityGenerator.createCapacity("CA-1");
        var capacity2 = capacityGenerator.createCapacity("CA-2");

        questionGenerator.createQuestion(capacity2, capacity1, 1);
        questionGenerator.createQuestion(capacity1, capacity2, 0);

        performGetAndExpect("/capacity-related-question/question/for-user?questionType=EXTRAPROFESSIONAL", "capacityRelatedQuestionSummaryForUser", true);
    }

    @Test
    @DisplayName("Given 3 EPA questions and 1 answer When I get question for one user Then answer is serialized")
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void get_extra_pro_activity_summary_with_answer() throws Exception {
        var capacity1 = capacityGenerator.createCapacity("CA-1");
        var capacity2 = capacityGenerator.createCapacity("CA-2");

        questionGenerator.createQuestion(capacity2, capacity1, 1);
        questionGenerator.createQuestion(capacity1, capacity2, 0);
        questionGenerator.createExtraProQuestionWithSelectedAnswer(
                2,
                userProfileProvider.getUserProfileOrCreate(USER_ID),
                UUID.randomUUID(),
                UUID.randomUUID()
        );

        performGetAndExpect("/capacity-related-question/question/for-user?questionType=EXTRAPROFESSIONAL", "extraProfessionalQuestionSummaryWithAnswer", true);

    }

    @DisplayName("Given a response with a capacity when user choose this answer then capacityOccurrence set is up to date")
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    @Test
    void answer_to_question_updates_capacity_occurrences() throws Exception {
        var capacity1 = capacityGenerator.createCapacity("CA-1");
        var capacity2 = capacityGenerator.createCapacity("CA-2");

        var question = questionGenerator.createQuestion(capacity2, capacity1, 1);

        var command = new SaveAnswerToCapacityRelatedQuestionCommandDTO()
                .answers(List.of(question.getResponses().first().getId()))
                .questionId(question.getId());

        performPut("/capacity-related-question/question/for-user", command)
                .andExpect(MockMvcResultMatchers.status().isNoContent());

        txHelper.doInTransaction(() -> {
            var occurrences = entityManager.createQuery("SELECT c FROM CapacityOccurrence c WHERE c.userProfile.uuid = :id", CapacityOccurrence.class).setParameter("id", UUID.fromString(USER_ID)).getResultList();
            assertThat(occurrences).hasSize(2);
            assertThat(occurrences).allMatch(c -> c.getOccurrence() == 1);
            assertThat(occurrences.stream().map(CapacityOccurrence::getCapacity)).containsExactlyInAnyOrder(capacity1, capacity2);
        });
    }

    @Test
    @DisplayName("Given an extra pro activity question when a user answers a question already answered by some other user then the two answers remain")
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void multiple_answers_ERHGO_750() throws Exception {
        var user = userProfileGenerator.createUserProfile();
        var answerId1 = UUID.randomUUID();
        var answerId2 = UUID.randomUUID();
        var question = questionGenerator.createQuestionWithAllSelectedAnswers(0, user, answerId1, answerId2);

        var command = new SaveAnswerToCapacityRelatedQuestionCommandDTO()
                .answers(List.of(answerId1))
                .questionId(question.getId());

        performPut("/capacity-related-question/question/for-user", command)
                .andExpect(MockMvcResultMatchers.status().isNoContent());

        var allAnswers = answerRepository.findAll();
        Assertions.assertAll(
                () -> assertThat(allAnswers).hasSize(3),
                () -> assertThat(allAnswers).filteredOn(a -> a.getUserProfile().userId().equals(USER_ID))
                        .anyMatch(a -> a.getId().equals(new AnswerForCapacityRelatedQuestion.ID(answerId1, UUID.fromString(USER_ID))))
                        .hasSize(1),
                () -> assertThat(allAnswers).filteredOn(a -> a.getUserProfile().userId().equals(user.userId()))
                        .anyMatch(a -> a.getId().equals(new AnswerForCapacityRelatedQuestion.ID(answerId1, user.uuid())))
                        .anyMatch(a -> a.getId().equals(new AnswerForCapacityRelatedQuestion.ID(answerId2, user.uuid())))
                        .hasSize(2)
        );
    }

    @Test
    @DisplayName("As an admin Given a user with multiple responses to one over two questions when I get sumup then sumup is correct")
    @WithMockKeycloakUser(id = USER_ID, roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void get_sumup_admin() throws Exception {

        getSumupCommon();
    }

    @Test
    @DisplayName("As a TO Given a user with multiple responses to one over two questions when I get sumup then sumup is correct")
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.ODAS_ADMIN, CHANNEL})
    @ResetDataAfter
    void get_sumup_TO() throws Exception {

        getSumupCommon();
    }

    private void getSumupCommon() throws Exception {
        var user = userProfileGenerator.createUserProfile(UUID.randomUUID(), CHANNEL);
        var answerId1 = UUID.randomUUID();
        var answerId2 = UUID.randomUUID();
        var question = questionGenerator.createQuestionWithAllSelectedAnswers(0, user, answerId1, answerId2);
        questionGenerator.createQuestion(capacityGenerator.createCapacity("CA3-12"), capacityGenerator.createCapacity("CA3-13"), 1);
        questionGenerator.createExtraProQuestionWithSelectedAnswer(2, user, UUID.randomUUID());

        performGetAndExpect("/capacity-related-question/" + user.userId() + "/sumup", "questions-sumup", false);
    }

}
