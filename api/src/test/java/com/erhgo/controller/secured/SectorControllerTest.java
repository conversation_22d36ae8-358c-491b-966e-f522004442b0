package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

class SectorControllerTest extends AbstractIntegrationTest {

    @Autowired
    ApplicationContext applicationContext;

    @Test
    @WithMockKeycloakUser(roles = {Role.CANDIDATE})
    @ResetDataAfter
    void getReferentialSectors() {
        performGetAndExpect("/sector/list", "sectors", true);
    }
}
