package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class ErhgoClassificationControllerTest extends AbstractIntegrationTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    @WithMockKeycloakUser
    void listErhgoClassifications() {
        performGetAndExpect("/erhgo-classification/list", "erhgoClassifications", true);
    }

    @Test
    @WithMockKeycloakUser
    void listErhgoClassifications_when_passing_occupation_id() {
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withClassificationsCodes("SO-05", "SO-07")
                .buildAndPersist();

        performGetAndExpect("/erhgo-classification/list?occupationId=%s".formatted(occupation.getId()),
                "selectedErhgoClassificationsOfSpecificOccupation", false);
    }

    @Test
    @WithMockKeycloakUser
    void listErhgoClassifications_when_passing_invalid_occupation_id() throws Exception {
        mvc.perform(get("/erhgo-classification/list?occupationId=%s".formatted("INVALID ID")))
                .andExpect(status().isNotFound());
    }
}
