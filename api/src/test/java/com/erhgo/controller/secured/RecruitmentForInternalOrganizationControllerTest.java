package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;

import java.util.concurrent.atomic.AtomicReference;

import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class RecruitmentForInternalOrganizationControllerTest extends AbstractIntegrationTest {

    @Autowired
    private OrganizationGenerator organizationGenerator;

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void list_recruitments_filtered_on_internal_only() throws Exception {
        var internalRecruiter = new AtomicReference<Recruiter>();
        var externalRecruiter = organizationGenerator.createRecruiterWithGeneratedCode();
        txHelper.doInTransaction(() ->
                {
                    var organization = organizationGenerator.createRecruiterWithGeneratedCode();
                    organization.setInternal(true);
                    internalRecruiter.set(organization);
                }
        );
        var internalRecruitment = applicationContext.getBean(RecruitmentMotherObject.class).withRecruiter(internalRecruiter.get()).buildAndPersist();
        var externalRecruitment = applicationContext.getBean(RecruitmentMotherObject.class).withRecruiter(externalRecruiter).buildAndPersist();
        mvc.perform(get("/api/odas/recruitment/list?withNewCandidaturesOnly=false&withOpenRecruitmentOnly=false&page=0&size=10&by=JOB&direction=ASC&query=")
                        .param("internal", "true")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].code", contains(internalRecruitment.getCode())));
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void list_recruitments_filtered_on_internal_only_false() throws Exception {
        var internalRecruiter = new AtomicReference<Recruiter>();
        var externalRecruiter = new AtomicReference<Recruiter>();
        txHelper.doInTransaction(() ->
                {
                    var organization = organizationGenerator.createRecruiterWithGeneratedCode();
                    organization.setInternal(true);
                    internalRecruiter.set(organization);

                    var organizationBis = organizationGenerator.createRecruiterWithGeneratedCode();
                    organizationBis.setInternal(false);
                    externalRecruiter.set(organizationBis);
                }
        );
        var internalRecruitment = applicationContext.getBean(RecruitmentMotherObject.class).withRecruiter(internalRecruiter.get()).buildAndPersist();
        var externalRecruitment = applicationContext.getBean(RecruitmentMotherObject.class).withRecruiter(externalRecruiter.get()).buildAndPersist();
        mvc.perform(get("/api/odas/recruitment/list?withNewCandidaturesOnly=false&withOpenRecruitmentOnly=false&page=0&size=10&by=JOB&direction=ASC&query=")
                        .param("internal", "false")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].code", containsInAnyOrder(externalRecruitment.getCode(), internalRecruitment.getCode())));
    }
}
