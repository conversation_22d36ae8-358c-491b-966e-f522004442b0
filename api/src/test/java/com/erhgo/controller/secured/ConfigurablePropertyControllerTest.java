package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.openapi.dto.ConfigurablePropertyDTO;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class ConfigurablePropertyControllerTest extends AbstractIntegrationTestWithFixtures {
    @Autowired
    private ConfigurablePropertyRepository repository;

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void get_all_configurable_properties() {
        performGetAndExpect("/config/configurable-properties", "configurableProperties", false);
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void edit_configurable_property() throws Exception {
        var propertyKey = "user_mastery_level_threshold";
        var editedProperty = new ConfigurablePropertyDTO().propertyKey(propertyKey).propertyValue("2");

        performPost("/config/edit-configurable-property", editedProperty)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var editedPropertyValue = repository.findOneByPropertyKey(propertyKey).getPropertyValue();
            assertThat(editedPropertyValue).isEqualTo("2");
        });
    }
}
