package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.JobGenerator;
import com.erhgo.generators.RecruitmentGenerator;
import com.erhgo.generators.UserProfileGenerator;
import com.erhgo.services.mailing.MailingListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

class UserMatchingRecruitmentsControllerTest extends AbstractIntegrationTest {

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Autowired
    private UserMatchingRecruitmentGenerator userMatchingRecruitmentGenerator;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private CapacityGenerator capacityGenerator;

    @Autowired
    private JobGenerator jobGenerator;

    @Autowired
    private RecruitmentGenerator recruitmentGenerator;

    @MockitoBean
    MailingListService mailingListService;

}
