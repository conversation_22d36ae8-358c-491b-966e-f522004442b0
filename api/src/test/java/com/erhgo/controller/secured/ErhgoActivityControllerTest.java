package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;

import static org.apache.commons.lang3.StringUtils.repeat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


class ErhgoActivityControllerTest extends AbstractIntegrationTest {

    @SneakyThrows
    @Test
    @WithMockKeycloakUser(roles = Role.CANDIDATE)
    void search_fails_for_too_long_query() {
        var longString = repeat('A', 251);
        mvc.perform(get(realUrl("/activityLabel/search"))
                        .queryParam("query", longString)
                        .queryParam("highlight", String.valueOf(false)))
                .andExpect(status().isBadRequest());
    }

    @SneakyThrows
    @Test
    @WithMockKeycloakUser(roles = Role.CANDIDATE)
    void search_succeed_for_not_too_long_query() {
        var longString = repeat('à', 250);
        mvc.perform(get(realUrl("/activityLabel/search"))
                        .queryParam("query", longString)
                        .queryParam("highlight", String.valueOf(false)))
                .andExpect(status().isOk());
    }
}
