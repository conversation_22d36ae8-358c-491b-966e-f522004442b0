package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TestUtils;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.enums.CriteriaQuestionType;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.job.JobMotherObject;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.CriteriaMotherObject;
import com.erhgo.generators.RecruiterMotherObject;
import com.erhgo.security.Role;
import io.micrometer.common.util.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Stream;

import static com.erhgo.TestUtils.mockAuthentication;
import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class UserRecruitmentsControllerTest extends AbstractIntegrationTest {
    @Autowired
    private CapacityGenerator capacityGenerator;

    @Test
    @ResetDataAfter
    void getRecruitmentsCount_returns_recruitments_count_with_startingDate_filtering() throws Exception {
        var capacity = capacityGenerator.createCapacity("CA58-58");
        applicationContext.getBean(RecruitmentMotherObject.class)
                .withRecruiterTitle("McDonalds")
                .withLocation(Location.builder().latitude(45.764043F).longitude(4.835659F).city("Lyon").build())
                .withState(RecruitmentState.PUBLISHED)
                .withJob(applicationContext.getBean(JobMotherObject.class).withTitle("Équipier").withCapacities(capacity).withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist()).buildAndPersist())
                .buildAndPersist();

        var futureRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withRecruiterTitle("Burger King")
                .withLocation(Location.builder().latitude(45.764043F).longitude(4.835659F).city("Lyon").build())
                .withState(RecruitmentState.PUBLISHED)
                .withJob(applicationContext.getBean(JobMotherObject.class).withTitle("Vendeur").withCapacities(capacity).withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist()).buildAndPersist())
                .withStartingDate(OffsetDateTime.now().plusDays(1))
                .buildAndPersist();

        var pastRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withRecruiterTitle("McDonalds")
                .withLocation(Location.builder().latitude(45.764043F).longitude(4.835659F).city("Lyon").build())
                .withState(RecruitmentState.PUBLISHED)
                .withJob(applicationContext.getBean(JobMotherObject.class).withTitle("Technicien").withCapacities(capacity).withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist()).buildAndPersist())
                .withStartingDate(OffsetDateTime.now().minusDays(1))
                .buildAndPersist();

        var user = applicationContext.getBean(UserProfileMotherObject.class)
                .withEmail("<EMAIL>")
                .buildAndPersist();

        mockAuthentication(user.userId(), user.userId(), Role.CANDIDATE);
        mvc.perform(get("/api/odas/user/recruitments-count"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.value", is(2)));
    }

    @Test
    @ResetDataAfter
    void getRecruitments_filters_by_startingDate_for_authenticated_user() throws Exception {
        var nullStartingDateRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist())
                .buildAndPersist();

        var futureStartingDateRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist())
                .withStartingDate(OffsetDateTime.now().plusDays(1))
                .buildAndPersist();

        var pastStartingDateRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist())
                .withStartingDate(OffsetDateTime.now().minusDays(1))
                .buildAndPersist();

        var user = applicationContext.getBean(UserProfileMotherObject.class)
                .withEmail("<EMAIL>")
                .buildAndPersist();
        performSearch(user.userId(), null, null, nullStartingDateRecruitment, pastStartingDateRecruitment);
    }

    @Test
    @ResetDataAfter
    void getRecruitments_returns_published_recruitment_for_user() throws Exception {
        var expectedRecruitment1 = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist())
                .buildAndPersist();

        applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.DRAFT)
                .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist())
                .buildAndPersist();

        var user = applicationContext.getBean(UserProfileMotherObject.class)
                .withEmail("<EMAIL>")
                .buildAndPersist();

        performSearch(user.userId(), null, null, expectedRecruitment1);
    }

    @Test
    @ResetDataAfter
    void getRecruitments_filters_by_startingDate_for_anonymous_user() throws Exception {
        var nullStartingDateRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist())
                .buildAndPersist();

        var futureStartingDateRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist())
                .withStartingDate(OffsetDateTime.now().plusDays(1))
                .buildAndPersist();

        var pastStartingDateRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist())
                .withStartingDate(OffsetDateTime.now().minusDays(1))
                .buildAndPersist();
        performSearch(null, null, null, nullStartingDateRecruitment, pastStartingDateRecruitment);
    }

    @Test
    @ResetDataAfter
    void getRecruitments_with_no_user_returns_published_recruitment() throws Exception {
        var expectedRecruitment1 = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist())
                .buildAndPersist();

        var expectedRecruitment2 = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist())
                .buildAndPersist();

        performSearch(null, null, null, expectedRecruitment1, expectedRecruitment2);
    }

    @Test
    @ResetDataAfter
    void getRecruitments_filters_blacklisted_job() throws Exception {
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist();

        applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withOccupation(occupation)
                .buildAndPersist();

        var expectedRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist())
                .buildAndPersist();

        var user = applicationContext.getBean(UserProfileMotherObject.class)
                .withEmail("<EMAIL>")
                .withBlacklistedOccupation(occupation)
                .buildAndPersist();

        performSearch(user.userId(), null, null, expectedRecruitment);
    }

    @Test
    @ResetDataAfter
    void getRecruitments_filters_already_candidated_recruitment() throws Exception {
        var user = applicationContext.getBean(UserProfileMotherObject.class)
                .withEmail("<EMAIL>")
                .buildAndPersist();

        var candidatedRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist())
                .buildAndPersist();

        applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(user)
                .withRecruitment(candidatedRecruitment)
                .withAvailability(null, null)
                .buildAndPersist();

        var expectedRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist())
                .buildAndPersist();

        performSearch(user.userId(), null, null, expectedRecruitment);
    }

    @Test
    @ResetDataAfter
    void getRecruitments_filters_refused_classification() throws Exception {
        var refusedClassification = ErhgoClassification.builder().code("SO-02").build();
        var user = applicationContext.getBean(UserProfileMotherObject.class)
                .withEmail("<EMAIL>")
                .withErhgoClassification(refusedClassification.getCode(), false)
                .buildAndPersist();

        applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist())
                .withErhgoClassification(Set.of(refusedClassification))
                .buildAndPersist();

        var expectedRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist())
                .buildAndPersist();

        performSearch(user.userId(), null, null, expectedRecruitment);
    }


    @Test
    @ResetDataAfter
    void getRecruitments_returns_recruitment_ordered_with_query_on_recruiter() throws Exception {
        var capacity = capacityGenerator.createCapacity("CA58-58");
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).withLevel(MasteryLevel.MIN_LEVEL).buildAndPersist();
        var expectedRecruitment1 = applicationContext.getBean(RecruitmentMotherObject.class)
                .withJob(applicationContext.getBean(JobMotherObject.class)
                        .withTitle("Vendeur 1")
                        .withCapacities(capacity)
                        .withOccupation(occupation)
                        .withRecruiter(applicationContext.getBean(RecruiterMotherObject.class).withTitle("42yolo").buildAndPersist())
                        .buildAndPersist())
                .withState(RecruitmentState.PUBLISHED)
                .buildAndPersist();

        applicationContext.getBean(RecruitmentMotherObject.class)
                .withJob(applicationContext.getBean(JobMotherObject.class)
                        .withTitle("Vendeur 2")
                        .withCapacities(capacity)
                        .withOccupation(occupation)
                        .buildAndPersist())
                .withState(RecruitmentState.PUBLISHED)
                .buildAndPersist();

        var user = applicationContext.getBean(UserProfileMotherObject.class)
                .withEmail("<EMAIL>")
                .withExperienceOnOccupation(occupation)
                .buildAndPersist();

        performSearch(user.userId(), null, "yo", expectedRecruitment1)
                .andExpect(jsonPath("$.content[*].code", contains("R-1")));
    }


    @Test
    @ResetDataAfter
    void getRecruitments_returns_recruitment_ordered_with_query() throws Exception {
        var capacity = capacityGenerator.createCapacity("CA58-58");
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).withLevel(MasteryLevel.MIN_LEVEL).buildAndPersist();
        var expectedRecruitment1 = applicationContext.getBean(RecruitmentMotherObject.class)
                .withJob(applicationContext.getBean(JobMotherObject.class)
                        .withTitle("Vendeur itinérant")
                        .withCapacities(capacity)
                        .withOccupation(occupation)
                        .buildAndPersist())
                .withState(RecruitmentState.PUBLISHED)
                .buildAndPersist();

        var expectedRecruitment2 = applicationContext.getBean(RecruitmentMotherObject.class)
                .withJob(applicationContext.getBean(JobMotherObject.class)
                        .withTitle("Vendeur")
                        .withCapacities(capacity)
                        .withOccupation(occupation)
                        .buildAndPersist())
                .withState(RecruitmentState.PUBLISHED)
                .buildAndPersist();

        var expectedRecruitment3 = applicationContext.getBean(RecruitmentMotherObject.class)
                .withJob(applicationContext.getBean(JobMotherObject.class)
                        .withTitle("Technicien")
                        .withCapacities(capacity)
                        .withOccupation(occupation)
                        .buildAndPersist())
                .withState(RecruitmentState.PUBLISHED)
                .buildAndPersist();

        var expectedRecruitment4 = applicationContext.getBean(RecruitmentMotherObject.class)
                .withJob(applicationContext.getBean(JobMotherObject.class)
                        .withTitle("TechnîciÈn vendeur pro")
                        .withCapacities(capacity)
                        .withOccupation(occupation)
                        .buildAndPersist())
                .withState(RecruitmentState.PUBLISHED)
                .buildAndPersist();

        applicationContext.getBean(RecruitmentMotherObject.class)
                .withJob(applicationContext.getBean(JobMotherObject.class)
                        .withTitle("Ignoré")
                        .withCapacities(capacity)
                        .withOccupation(occupation)
                        .buildAndPersist())
                .withState(RecruitmentState.PUBLISHED)
                .buildAndPersist();

        var user = applicationContext.getBean(UserProfileMotherObject.class)
                .withEmail("<EMAIL>")
                .withExperienceOnOccupation(occupation)
                .buildAndPersist();

        performSearch(user.userId(), null, "Vendeur", expectedRecruitment1, expectedRecruitment2, expectedRecruitment3, expectedRecruitment4)
                .andExpect(jsonPath("$.content[*].code", contains("R-2", "R-1", "R-4", "R-3")));
    }

    @Test
    @ResetDataAfter
    void getRecruitments_returns_recruitments_ordered_by_proximity() throws Exception {
        var capacity = capacityGenerator.createCapacity("CA58-58");
        var expectedRecruitment1 = applicationContext.getBean(RecruitmentMotherObject.class)
                .withLocation(Location.builder().latitude(43.296482F).longitude(5.36978F).city("Marseille").build())
                .withRecruiterTitle("McDonalds")
                .withState(RecruitmentState.PUBLISHED)
                .withJob(applicationContext.getBean(JobMotherObject.class).withTitle("Équipier").withCapacities(capacity).withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist()).buildAndPersist())
                .buildAndPersist();

        var expectedRecruitment2 = applicationContext.getBean(RecruitmentMotherObject.class)
                .withLocation(Location.builder().latitude(45.764043F).longitude(4.835659F).city("Lyon").build())
                .withRecruiterTitle("McDonalds")
                .withState(RecruitmentState.PUBLISHED)
                .withJob(applicationContext.getBean(JobMotherObject.class).withTitle("Vendeur").withCapacities(capacity).withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist()).buildAndPersist())
                .buildAndPersist();

        var expectedRecruitment3 = applicationContext.getBean(RecruitmentMotherObject.class)
                .withLocation(Location.builder().latitude(48.856614F).longitude(2.3522219F).city("Paris").build())
                .withRecruiterTitle("McDonalds")
                .withState(RecruitmentState.PUBLISHED)
                .withJob(applicationContext.getBean(JobMotherObject.class).withTitle("Technicien").withCapacities(capacity).withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist()).buildAndPersist())
                .buildAndPersist();

        var user = applicationContext.getBean(UserProfileMotherObject.class)
                .withEmail("<EMAIL>")
                .withLocation(Location.builder().latitude(45.764043F).longitude(4.835659F).city("Lyon").build())
                .buildAndPersist();

        performSearch(user.userId(), null, null, expectedRecruitment1, expectedRecruitment2, expectedRecruitment3)
                .andExpect(jsonPath("$.content[*].city", contains("Lyon", "Marseille", "Paris")));
    }

    @Test
    @ResetDataAfter
    void getRecruitments_returns_recruitments_ordered_by_mastery_level() throws Exception {
        var capacity = capacityGenerator.createCapacity("CA58-58");
        var expectedRecruitment1 = applicationContext.getBean(RecruitmentMotherObject.class)
                .withLocation(Location.builder().latitude(43.296482F).longitude(5.36978F).city("Marseille").build())
                .withRecruiterTitle("McDonalds")
                .withState(RecruitmentState.PUBLISHED)
                .withJob(applicationContext.getBean(JobMotherObject.class)
                        .withTitle("Équipier")
                        .withCapacities(capacity)
                        .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).withLevel(MasteryLevel.MIN_LEVEL).buildAndPersist())
                        .buildAndPersist())
                .buildAndPersist();

        var expectedRecruitment2 = applicationContext.getBean(RecruitmentMotherObject.class)
                .withLocation(Location.builder().latitude(45.764043F).longitude(4.835659F).city("Lyon").build())
                .withRecruiterTitle("McDonalds")
                .withState(RecruitmentState.PUBLISHED)
                .withJob(applicationContext.getBean(JobMotherObject.class)
                                .withTitle("Vendeur")
                                .withCapacities(capacity)
                                .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).withLevel(MasteryLevel.MAX_LEVEL).buildAndPersist())
                                .buildAndPersist())
                .buildAndPersist();

        var user = applicationContext.getBean(UserProfileMotherObject.class)
                .withEmail("<EMAIL>")
                .withLocation(Location.builder().latitude(45.764043F).longitude(4.835659F).city("Lyon").build())
                .buildAndPersist();

        performSearch(user.userId(), null, null, expectedRecruitment1, expectedRecruitment2)
                .andExpect(jsonPath("$.content[*].jobTitle", contains(expectedRecruitment2.getJobTitle(), expectedRecruitment1.getJobTitle())));
    }


    @Test
    @ResetDataAfter
    void getRecruitments_ensure_multiple_criteria_filtered() throws Exception {
        AtomicReference<Recruitment> expectedRecruitment1 = new AtomicReference<>();
        AtomicReference<UserProfile> user = new AtomicReference<>();
        var multipleCriteria = applicationContext.getBean(CriteriaMotherObject.class).withQuestionType(CriteriaQuestionType.MULTIPLE).withValueCodes("REP-1-1", "REP-2-1", "REP-3-1").buildAndPersist();

        var userSelectedCriteria = List.of(multipleCriteria.getCriteriaValues().get(0));
        var userRefusedCriteria = List.of(multipleCriteria.getCriteriaValues().get(1), multipleCriteria.getCriteriaValues().get(2));

        var capacity = capacityGenerator.createCapacity("CA-01");

        user.set(applicationContext.getBean(UserProfileMotherObject.class)
                .withEmail("<EMAIL>")
                .withCriteriaAnswers(userSelectedCriteria, userRefusedCriteria)
                .buildAndPersist());

        var bestJob = applicationContext.getBean(JobMotherObject.class)
                .withTitle("Best Job")
                .withRecruiter(applicationContext.getBean(RecruiterMotherObject.class).withTitle("J1").buildAndPersist())
                .withCapacities(capacity)
                .withCriteriaValues(multipleCriteria.getCriteriaValues().get(0))
                .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).withLevel(MasteryLevel.MIN_LEVEL).buildAndPersist())
                .buildAndPersist();
        var worseJob = applicationContext.getBean(JobMotherObject.class)
                .withTitle("Worse Job")
                .withRecruiter(applicationContext.getBean(RecruiterMotherObject.class).withTitle("J2").buildAndPersist())
                .withCapacities(capacity)
                .withCriteriaValues(multipleCriteria.getCriteriaValues().get(0), multipleCriteria.getCriteriaValues().get(1))
                .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).withLevel(MasteryLevel.MIN_LEVEL).buildAndPersist())
                .buildAndPersist();

        expectedRecruitment1.set(applicationContext.getBean(RecruitmentMotherObject.class)
                    .withState(RecruitmentState.PUBLISHED)
                    .withJob(bestJob)
                    .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).withLevel(MasteryLevel.MIN_LEVEL).buildAndPersist())
                    .buildAndPersist());
        applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withJob(worseJob)
                .buildAndPersist();
        applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.DRAFT)
                .withJob(bestJob)
                .buildAndPersist();

        performSearch(user.get().userId(), "userRecruitments", null, expectedRecruitment1.get());
    }

    private ResultActions performSearch(String userId, String expectedFile, String query, Recruitment... recruitments) throws Exception {
        if (!StringUtils.isBlank(userId)) {
            mockAuthentication(userId, userId, Role.CANDIDATE);
        }
        MockHttpServletRequestBuilder action = bootstrapMatchingRequest(query);
        var result = mvc.perform(action).andExpect(status().isOk());
        if (recruitments.length > 0) {
            result.andExpect(jsonPath("$.content[*].jobTitle", containsInAnyOrder(Stream.of(recruitments).map(Recruitment::getJobTitle).toArray(String[]::new))))
                    .andExpect(jsonPath("$.numberOfElementsInPage", is(recruitments.length)));
            if (expectedFile != null) {
                result.andExpect(TestUtils.jsonMatchesContentWithOrderedArray(expectedFile));
            }
        } else {
            result.andExpect(jsonPath("$.content[*]", hasSize(0)));
        }
        return result.andExpect(jsonPath("$.totalNumberOfElements", is(recruitments.length)));
    }

    private MockHttpServletRequestBuilder bootstrapMatchingRequest(String query) {
        return get("/api/odas/user/recruitments")
                .param("query", query)
                .param("page", "0")
                .param("size", "10");
    }
}
