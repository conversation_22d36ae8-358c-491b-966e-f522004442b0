package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.HandicapAccountService;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.mock.web.MockMultipartFile;

import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class UserControllerBulkCVTest extends AbstractIntegrationTest {

    @MockBean
    private HandicapAccountService handicapAccountService;

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @SneakyThrows
    void createOrUpdateProfilesForCVs_shouldReturnProcessingResults() {
        var csvContent = "email,url,comment\<EMAIL>,https://example.com/cv.pdf,test comment";
        var csvFile = new MockMultipartFile("csvFile", "test.csv", "text/csv", csvContent.getBytes());

        when(handicapAccountService.createOrUpdateUserForFileURL(anyString(), ArgumentMatchers.any(), anyBoolean()))
                .thenReturn("Traité avec succès - fichier trouvé et traité");

        mvc.perform(multipart(realUrl("/user/bulk-cv-processing"))
                        .file(csvFile)
                        .param("excludeExistingUsers", "false"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.totalRows").value(1))
                .andExpect(jsonPath("$.validRows").value(1))
                .andExpect(jsonPath("$.invalidRows").value(0))
                .andExpect(jsonPath("$.emails[0]").value("<EMAIL>"));
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @SneakyThrows
    void createOrUpdateProfilesForCVs_shouldHandleMultipleRows() {
        var csvContent = """
                email,url,comment
                <EMAIL>,https://example.com/cv1.pdf,comment1
                <EMAIL>,https://example.com/cv2.pdf,comment2
                """;
        var csvFile = new MockMultipartFile("csvFile", "test.csv", "text/csv", csvContent.getBytes());

        when(handicapAccountService.createOrUpdateUserForFileURL(anyString(), ArgumentMatchers.any(), anyBoolean()))
                .thenReturn("Traité avec succès - fichier trouvé et traité");

        mvc.perform(multipart(realUrl("/user/bulk-cv-processing"))
                        .file(csvFile)
                        .param("excludeExistingUsers", "true"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.totalRows").value(2))
                .andExpect(jsonPath("$.validRows").value(2))
                .andExpect(jsonPath("$.invalidRows").value(0))
                .andExpect(jsonPath("$.emails[0]").value("<EMAIL>"))
                .andExpect(jsonPath("$.emails[1]").value("<EMAIL>"));
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @SneakyThrows
    void createOrUpdateProfilesForCVs_shouldReturnBadRequestForInvalidCsv() {
        var invalidCsvContent = "invalid csv content without proper headers";
        var csvFile = new MockMultipartFile("csvFile", "test.csv", "text/csv", invalidCsvContent.getBytes());

        mvc.perform(multipart(realUrl("/user/bulk-cv-processing"))
                        .file(csvFile)
                        .param("excludeExistingUsers", "false"))
                .andExpect(jsonPath("$.validRows").value(0))
        ;
    }

    @Test
    @ResetDataAfter
    @SneakyThrows
    void createOrUpdateProfilesForCVs_shouldRequireAuthentication() {
        var csvContent = "email,url,comment\<EMAIL>,https://example.com/cv.pdf,test comment";
        var csvFile = new MockMultipartFile("csvFile", "test.csv", "text/csv", csvContent.getBytes());

        mvc.perform(multipart(realUrl("/user/bulk-cv-processing"))
                        .file(csvFile)
                        .param("excludeExistingUsers", "false")
                        .header("Authorization", "")) // Remove auth
                .andExpect(status().isUnauthorized());
    }
}
