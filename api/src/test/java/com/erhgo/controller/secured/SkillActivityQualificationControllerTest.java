package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.config.ApiConstants;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.esco.EscoSkill;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.classifications.EscoSkillRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.google.common.collect.Sets;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;

import java.util.UUID;

import static com.erhgo.config.ApiConstants.SEPARATOR;
import static com.erhgo.generators.TestFixtures.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class SkillActivityQualificationControllerTest extends AbstractIntegrationTestWithFixtures {

    @Autowired
    private EscoSkillRepository escoSkillRepository;

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void shouldFailWhenLinkingToActivityAndNoSkillUri() throws Exception {
        var requestBody = new SkillLinkToActivityCommandDTO();

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToActivity")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void shouldFailWhenLinkingToActivityAndSkillNotFound() throws Exception {
        var requestBody = new SkillLinkToActivityCommandDTO()
                .uri("unknown")
                .activityId(UUID.randomUUID());

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToActivity")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldFailWhenLinkingToActivityAndNoActivityId() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .build());

        var requestBody = new SkillLinkToActivityCommandDTO()
                .uri(skill.getUri());

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToActivity")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldFailWhenLinkingToActivityAndActivityNotFound() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .build());

        var requestBody = new SkillLinkToActivityCommandDTO()
                .uri(skill.getUri())
                .activityId(UUID.randomUUID());

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToActivity")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }


    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldLinkWithActivityAndSetNoActivityToFalse() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .build());

        var requestBody = new SkillLinkToActivityCommandDTO()
                .uri(skill.getUri())
                .activityId(ACT_01.getUuid());

        mvc.perform(post(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToActivity")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedSKill = escoSkillRepository.findById(skill.getUri()).orElseThrow(IllegalStateException::new);
            assertThat(updatedSKill.getActivities()).hasSize(1);
            assertThat(updatedSKill.getActivities().iterator().next().getUuid()).isEqualTo(ACT_01.getUuid());
            assertThat(updatedSKill.getNoActivity()).isFalse();
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void shouldFailWhenDeletingLinkToActivityAndNoActivityId() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .build());

        var requestBody = new SkillLinkToActivityCommandDTO()
                .uri(skill.getUri());

        mvc.perform(delete(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToActivity")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void shouldFailWhenDeletingLinkToActivityAndSkillNotFound() throws Exception {
        var requestBody = new SkillLinkToActivityCommandDTO()
                .uri("unknown")
                .activityId(UUID.randomUUID());

        mvc.perform(delete(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToActivity")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldSucceedWhenNoActivityLinked() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .build());

        var requestBody = new SkillLinkToActivityCommandDTO()
                .uri(skill.getUri())
                .activityId(jobActivityLabelUsedOnlyInJobMission.getUuid());

        mvc.perform(delete(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToActivity")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var updatedSkill = escoSkillRepository.findById(skill.getUri()).orElseThrow(IllegalStateException::new);
            assertThat(updatedSkill.getActivities()).isNullOrEmpty();
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldDeleteLinkWithActivityAndSetNoActivityFlagToNull() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .noActivity(false)
                .activities(Sets.newHashSet(ACT_01))
                .build());

        var requestBody = new SkillLinkToActivityCommandDTO()
                .uri(skill.getUri())
                .activityId(ACT_01.getUuid());

        mvc.perform(delete(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToActivity")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var updatedSkill = escoSkillRepository.findById(skill.getUri()).orElseThrow(IllegalStateException::new);
            assertThat(updatedSkill.getActivities()).isNullOrEmpty();
            assertThat(updatedSkill.getNoActivity()).isNull();
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void shouldDeleteLinkWithActivity() throws Exception {
        var skill = escoSkillRepository.save(EscoSkill.builder()
                .uri("uri")
                .title("title")
                .noActivity(false)
                .activities(Sets.newHashSet(ACT_01, ACT_05))
                .build());

        var requestBody = new SkillLinkToActivityCommandDTO()
                .uri(skill.getUri())
                .activityId(ACT_01.getUuid());

        mvc.perform(delete(ApiConstants.API_ODAS_ESCO_SKILL + SEPARATOR + "linkToActivity")
                .content(objectMapper.writeValueAsString(requestBody))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var updatedSkill = escoSkillRepository.findById(skill.getUri()).orElseThrow(IllegalStateException::new);
            assertThat(updatedSkill.getActivities()).containsExactly(ACT_05);
            assertThat(updatedSkill.getNoActivity()).isFalse();
        });
    }

}
