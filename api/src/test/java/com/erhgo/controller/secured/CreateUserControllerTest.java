package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.userprofile.UserChannel;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.domain.userprofile.UserRegistrationState;
import com.erhgo.openapi.dto.ConfirmFOUserFromBOCommandDTO;
import com.erhgo.openapi.dto.CreateUserFOCommandDTO;
import com.erhgo.openapi.dto.CreateUserFOResultDTO;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.dto.UserKeycloakRepresentation;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailingListService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.time.LocalDateTime;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class CreateUserControllerTest extends AbstractIntegrationTest {

    @Autowired
    private UserProfileRepository userProfileRepository;
    @SpyBean
    private KeycloakMockService keycloakMockService;
    @Autowired
    private ApplicationContext applicationContext;
    @MockitoBean
    private MailingListService mailingListService;

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void create_user_in_keycloak_with_email() throws Exception {
        var phone = "0752452587";
        var email = "<EMAIL>";
        var lastName = "LAST_NAME";
        var firstName = "FIRST_NAME";

        // GIVEN
        var createUserFOCommandDTO = new CreateUserFOCommandDTO()
                .firstName(firstName)
                .lastName(lastName)
                .email(email)
                .phoneNumber(phone);

        // WHEN
        var result = performPost("/user/create-fo", createUserFOCommandDTO)
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();
        var userId = objectMapper.readValue(result, CreateUserFOResultDTO.class).getUserId();
        txHelper.doInTransaction(() -> {
            // THEN
            assertThat(userId).isNotNull();
            var createdUser = userProfileRepository.findByUserId(userId).orElseThrow();
            assertThat(createdUser.generalInformation().getPhoneNumber()).isEqualTo(phone);
            assertThat(createdUser.channelSourceType()).isEqualTo(UserChannel.ChannelSourceType.CREATION_BO);
            assertThat(createdUser.registrationStep()).isEqualTo(UserRegistrationState.RegistrationStep.BO_INITIALIZED);
            assertThat(createdUser.lastConnectionDate()).isBefore(LocalDateTime.now());
            var keycloakUser = keycloakMockService.getFrontOfficeUserProfile(userId).orElseThrow();
            assertThat(keycloakUser)
                    .matches(u -> u.getEmail().equals(email))
                    .matches(u -> u.getFirstName().equals(firstName))
                    .matches(u -> u.getLastName().equals(lastName));
        });
        verifyNoInteractions(mailingListService);
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void create_user_in_keycloak_without_email() throws Exception {
        var phone = "0752452587";

        // GIVEN
        var createUserFOCommandDTO = new CreateUserFOCommandDTO()
                .phoneNumber(phone);

        // WHEN
        var result = performPost("/user/create-fo", createUserFOCommandDTO)
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();
        var userId = objectMapper.readValue(result, CreateUserFOResultDTO.class).getUserId();
        txHelper.doInTransaction(() -> {
            // THEN
            assertThat(userId).isNotNull();
            var createdUser = userProfileRepository.findByUserId(userId).orElseThrow();
            assertThat(createdUser.generalInformation().getPhoneNumber()).isEqualTo(phone);
            assertThat(createdUser.channelSourceType()).isEqualTo(UserChannel.ChannelSourceType.CREATION_BO);
            assertThat(createdUser.registrationStep()).isEqualTo(UserRegistrationState.RegistrationStep.BO_INITIALIZED);
            var keycloakUser = keycloakMockService.getFrontOfficeUserProfile(userId).orElseThrow();
            assertThat(keycloakUser)
                    .extracting(UserRepresentation::getEmail)
                    .matches(m -> m.equals("TMP_" + phone + "@jenesuispasuncv.fr"))
            ;
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void create_user_already_exists_in_keycloak() throws Exception {
        var phone = "0752452587";
        var email = "a@a";

        // GIVEN
        var createUserFOCommandDTO = new CreateUserFOCommandDTO()
                .email(email)
                .phoneNumber(phone);

        keycloakMockService.createUserInFrontOfficeRealm(new UserKeycloakRepresentation().setEmail(email));

        // WHEN
        performPost("/user/create-fo", createUserFOCommandDTO)
                // THEN
                .andExpect(status().isConflict());
    }

    @ParameterizedTest
    @ValueSource(strings = {"0752452587", "+33752452587", "0033752452587"})
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void create_user_phone_already_exists_without_prefix(String phone) throws Exception {
        var email = "a@a";

        // GIVEN
        applicationContext.getBean(UserProfileMotherObject.class)
                .withPhoneNumber("07 5/2-45+25.87Z")
                .buildAndPersist();

        var createUserFOCommandDTO = new CreateUserFOCommandDTO()
                .email(email)
                .phoneNumber(phone);

        // WHEN
        performPost("/user/create-fo", createUserFOCommandDTO)
                // THEN
                .andExpect(status().isConflict());
    }

    @ParameterizedTest
    @ValueSource(strings = {"0752452587", "+33752452587", "0033752452587"})
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void create_user_phone_already_exists_with_prefix_plus(String phone) throws Exception {
        var email = "a@a";

        // GIVEN
        applicationContext.getBean(UserProfileMotherObject.class)
                .withPhoneNumber("+337 5/2-45+25.87Z")
                .buildAndPersist();

        var createUserFOCommandDTO = new CreateUserFOCommandDTO()
                .email(email)
                .phoneNumber(phone);

        // WHEN
        performPost("/user/create-fo", createUserFOCommandDTO)
                // THEN
                .andExpect(status().isConflict());
    }

    @ParameterizedTest
    @ValueSource(strings = {"0752452587", "+33752452587", "0033752452587"})
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void create_user_phone_already_exists_with_prefix_00(String phone) throws Exception {
        var email = "a@a";

        // GIVEN
        applicationContext.getBean(UserProfileMotherObject.class)
                .withPhoneNumber("0033!(7) 5/2-45+25.87Z")
                .buildAndPersist();

        var createUserFOCommandDTO = new CreateUserFOCommandDTO()
                .email(email)
                .phoneNumber(phone);

        // WHEN
        performPost("/user/create-fo", createUserFOCommandDTO)
                // THEN
                .andExpect(status().isConflict());
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void confirm_User_In_FO_From_BO() throws Exception {
        var email = "test@test";
        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withRegistrationStep(UserRegistrationState.RegistrationStep.BO_INITIALIZED).withEmail(email).buildAndPersist();

        var confirmFOUserFromBOCommandDTO = new ConfirmFOUserFromBOCommandDTO().userId(userProfile.userId());
        performPost("/api/odas/user/confirm-user-from-bo", confirmFOUserFromBOCommandDTO)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() ->
                assertThat(userProfileRepository.findByUserId(userProfile.userId())
                        .orElseThrow()
                        .registrationStep()).isEqualTo(UserRegistrationState.RegistrationStep.BO_CONFIRMED)
        );
        Mockito.verify(keycloakMockService).confirmFOUserFromBO(confirmFOUserFromBOCommandDTO);
        Mockito.verify(mailingListService).sendMailsForTemplate(ArgumentMatchers.argThat(set -> set.contains(email) && set.size() == 1),
                ArgumentMatchers.eq(1L), ArgumentMatchers.argThat(map -> ((Map) map).get("password") != null), isNull());
    }
}
