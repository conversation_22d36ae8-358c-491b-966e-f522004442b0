package com.erhgo.controller.secured;

import com.erhgo.TransactionTestHelper;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.enums.CriteriaQuestionType;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.enums.TypeWorkingTime;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.job.JobMotherObject;
import com.erhgo.domain.job.JobType;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.generators.*;
import com.erhgo.repositories.JobRepository;
import lombok.Getter;
import org.junit.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Service
public class UserMatchingRecruitmentGenerator {

    @Autowired
    protected TransactionTestHelper txHelper;

    @Autowired
    private JobGenerator jobGenerator;

    @Autowired
    private RecruitmentGenerator recruitmentGenerator;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Autowired
    private CapacityGenerator capacityGenerator;

    @Autowired
    private OrganizationGenerator organizationGenerator;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private CandidatureGenerator candidatureGenerator;

    @Autowired
    private ErhgoOccupationGenerator erhgoOccupationGenerator;

    @Autowired
    private KeycloakMockService keycloakMockService;

    @Autowired
    private ApplicationContext applicationContext;

    public static class UserAndRecruitmentsDTO {

        private final UserProfile userProfile;
        private final List<Recruitment> recruitments = new ArrayList<>();
        @Getter
        private RecruitmentCandidature candidature = null;

        public UserAndRecruitmentsDTO(UserProfile userProfile, Recruitment recruitment) {
            this.userProfile = userProfile;
            this.recruitments.add(recruitment);
        }

        public UserAndRecruitmentsDTO(UserProfile userProfile, Recruitment recruitment, RecruitmentCandidature candidature) {
            this.userProfile = userProfile;
            this.recruitments.add(recruitment);
            this.candidature = candidature;
        }

        public UserAndRecruitmentsDTO(UserProfile userProfile, List<Recruitment> recruitments) {
            this.userProfile = userProfile;
            this.recruitments.addAll(recruitments);
        }

        public UserProfile getUserProfile() {
            return userProfile;
        }

        public Recruitment getSingleRecruitment() {
            if (recruitments.size() != 1 ) {
                Assert.fail("Recruitments is not singleton got size" + recruitments.size() + ", expected 1");
            }
            return recruitments.get(0);
        }

        public Job getSingleJob() {
            return getSingleRecruitment().getJob();
        }

        public Job[] getJobsArray() {
            return recruitments.stream().map(Recruitment::getJob).toArray(Job[]::new);
        }

        public Recruitment[] getRecruitmentsArray() {
            return recruitments.toArray(Recruitment[]::new);
        }
    }

    @Transactional
    public UserAndRecruitmentsDTO createUserWithCapacitiesAndRecruitmentWithCapacitiesAndLocation(boolean tooFarFromEachOther) {
        var capacity = capacityGenerator.createCapacity("CA-1");
        var userLocation = Location.builder()
                .city("Lyon")
                .postcode("69001")
                .citycode("69123")
                .latitude(45.76404f)
                .longitude(4.835659f)
                .build();
        var userProfile = userProfileGenerator.createUserProfileWithLocationAndCapacities(String.valueOf(UUID.randomUUID()), userLocation, createCapacitiesIncluding(10, capacity).toArray(Capacity[]::new));
        var recruiter = organizationGenerator.createRecruiter("E-44", AbstractOrganization.OrganizationType.ENTERPRISE);
        var recruitment = recruitmentGenerator.createRecruitmentWithRequiredCapacityAndMasteryLevel("J1", recruiter, MasteryLevel.MIN_LEVEL, capacity);

        if (tooFarFromEachOther) {
            recruitment.setLocation(Location.builder().city("Marseille").citycode("13055").postcode("13000").latitude(43.296482f).longitude(5.36978f).build());
        } else {
            recruitment.setLocation(userLocation);
        }
        return new UserAndRecruitmentsDTO(userProfile, recruitment);
    }

    public UserAndRecruitmentsDTO createUserWithoutCapacitiesAndRecruitmentWithCapacities() {
        var userProfile = userProfileGenerator.createUserProfile();
        var capacity = capacityGenerator.createCapacity("CA-01");
        var recruiter = organizationGenerator.createRecruiter("E-44", AbstractOrganization.OrganizationType.ENTERPRISE);
        var recruitment = recruitmentGenerator.createRecruitmentWithRequiredCapacityAndMasteryLevel("J42", recruiter, MasteryLevel.MIN_LEVEL, capacity);
        return new UserAndRecruitmentsDTO(userProfile, recruitment);
    }

    public UserAndRecruitmentsDTO createUserWithLevelAndCapacitiesAndRecruitmentWithCapacities(int nbCapacities) {
        var capacity = capacityGenerator.createCapacity("CA-01");
        var userProfile = userProfileGenerator.createUserProfileWithLevelAndCapacities(new Integer[]{MasteryLevel.MAX_LEVEL.getMasteryLevel()}, createCapacitiesIncluding(nbCapacities, capacity));
        var recruiter = organizationGenerator.createRecruiter("E-44", AbstractOrganization.OrganizationType.ENTERPRISE, false, false, false);
        var recruitment = recruitmentGenerator.createRecruitmentWithRequiredCapacityAndMasteryLevel("J1", recruiter, MasteryLevel.MIN_LEVEL, capacity);
        return new UserAndRecruitmentsDTO(userProfile, recruitment);
    }

    public UserAndRecruitmentsDTO createUserWithTwoRecruitmentsOnDifferentPrivateChannels(int nbCapacities) {
        var capacity = capacityGenerator.createCapacity("CA-01");
        var privateChannel = "E-44";
        var recruiter1 = organizationGenerator.createRecruiter(privateChannel, AbstractOrganization.OrganizationType.ENTERPRISE, true, false, false);
        var recruitment1 = recruitmentGenerator.createRecruitmentWithRequiredCapacityAndMasteryLevel("J1", recruiter1, MasteryLevel.MIN_LEVEL, capacity);
        var recruiter2 = organizationGenerator.createRecruiter("E-45", AbstractOrganization.OrganizationType.ENTERPRISE, true, false, false);
        var recruitment2 = recruitmentGenerator.createRecruitmentWithRequiredCapacityAndMasteryLevel("J1", recruiter2, MasteryLevel.MIN_LEVEL, capacity);
        var userProfile = userProfileGenerator.createUserProfileWithChannelLevelAndCapacities(new String[]{privateChannel}, new Integer[]{MasteryLevel.MAX_LEVEL.getMasteryLevel()}, createCapacitiesIncluding(nbCapacities, capacity));
        return new UserAndRecruitmentsDTO(userProfile, recruitment1);
    }

    List<Capacity> createCapacitiesIncluding(int capacitiesLength, Capacity... requiredCapacities) {
        var capacities = new ArrayList<Capacity>();
        IntStream.range(0, Math.max(requiredCapacities.length, capacitiesLength))
                .mapToObj(i -> i >= requiredCapacities.length ? capacityGenerator.createCapacity("CA3-XX" + i) : requiredCapacities[i])
                .forEach(capacities::add);
        return capacities;
    }

    @Transactional
    public UserAndRecruitmentsDTO createUserHalfMatchingWithRecruitments() {
        var ca1 = capacityGenerator.createCapacity("CA-01");
        var ca2 = capacityGenerator.createCapacity("CA-02");
        var ca3 = capacityGenerator.createCapacity("CA-03");
        var ca4 = capacityGenerator.createCapacity("CA-04");

        var jobCode = "J";
        var index = 1;
        var user = userProfileGenerator.createUserProfileWithLevelAndCapacities(new Integer[]{MasteryLevel.MAX_LEVEL.getMasteryLevel()}, createCapacitiesIncluding(10, ca1));

        var recruiter = organizationGenerator.createRecruiter("E-44", AbstractOrganization.OrganizationType.ENTERPRISE);
        var fiftyPercentMatchingRecruitment = recruitmentGenerator.createRecruitmentWithRequiredCapacityAndMasteryLevel(jobCode + index++, recruiter, MasteryLevel.MIN_LEVEL, ca1, ca2);
        var thirdMatchingRecruitment = recruitmentGenerator.createRecruitmentWithRequiredCapacityAndMasteryLevel(jobCode + index++, recruiter, MasteryLevel.MIN_LEVEL, ca1, ca2, ca3);
        // Next two jobs/recruitments should not be in result
        recruitmentGenerator.createRecruitmentWithRequiredCapacityAndMasteryLevel(jobCode + index++, recruiter, MasteryLevel.MIN_LEVEL, ca1, ca2, ca3, ca4);
        recruitmentGenerator.createRecruitmentWithRequiredCapacityAndMasteryLevel(jobCode + index, recruiter, MasteryLevel.MIN_LEVEL, ca4);

        return new UserAndRecruitmentsDTO(user, List.of(fiftyPercentMatchingRecruitment, thirdMatchingRecruitment));
    }

    @Transactional
    public UserAndRecruitmentsDTO createUserWithCapacitiesAndTwoRecruitmentsWithDifferentMasteryLevels(boolean hasLevel1dot5) {
        var ca1 = capacityGenerator.createCapacity("CA-01");
        var ca1_55 = capacityGenerator.createCapacity("CA1-55");
        var capacities = createCapacitiesIncluding(10, ca1, ca1_55);
        var user = hasLevel1dot5 ?
                userProfileGenerator.createUserProfileWithCapacitiesAndLevel1dot5(
                        UUID.randomUUID(),
                        capacities
                ) :
                userProfileGenerator.createUserProfileWithLevelAndCapacities(
                        new Integer[]{MasteryLevel.MIN_LEVEL.getMasteryLevel()},
                        capacities
                );
        var recruiter = organizationGenerator.createRecruiter("E-44", AbstractOrganization.OrganizationType.ENTERPRISE);
        var recruitmentLevel1 = recruitmentGenerator.createRecruitmentWithRequiredCapacityAndMasteryLevel("J-1", recruiter, MasteryLevel.forLevel(1), ca1);
        var recruitmentLevel2 = recruitmentGenerator.createRecruitmentWithRequiredCapacityAndMasteryLevel("J-2", recruiter, MasteryLevel.forLevel(2), ca1);

        var expectedRecruitments = new ArrayList<Recruitment>();
        expectedRecruitments.add(recruitmentLevel1);
        if (hasLevel1dot5) {
            expectedRecruitments.add(recruitmentLevel2);
        }

        return new UserAndRecruitmentsDTO(user, expectedRecruitments);
    }

    public UserAndRecruitmentsDTO createJobsAndRecruitmentsWithOneSourcingOrganization() {
        var ca1 = capacityGenerator.createCapacity("CA-01");
        var ca1_55 = capacityGenerator.createCapacity("CA1-55");
        var capacities = createCapacitiesIncluding(10, ca1, ca1_55);
        var user = userProfileGenerator.createUserProfileWithCapacitiesAndLevel1dot5(
                UUID.randomUUID(),
                capacities);
        var recruiter = organizationGenerator.createRecruiter("E-44", AbstractOrganization.OrganizationType.ENTERPRISE);
        var recruiter2 = organizationGenerator.createRecruiter("E-44", AbstractOrganization.OrganizationType.SOURCING);
        var recruitmentLevel1 = recruitmentGenerator.createRecruitmentWithRequiredCapacityAndMasteryLevel("J-1", recruiter, MasteryLevel.forLevel(1), ca1);
        var recruitmentLevel2 = recruitmentGenerator.createRecruitmentWithRequiredCapacityAndMasteryLevel("J-2", recruiter2, MasteryLevel.forLevel(2), ca1);

        var expectedRecruitments = new ArrayList<Recruitment>();
        expectedRecruitments.add(recruitmentLevel1);
        expectedRecruitments.add(recruitmentLevel2);

        return new UserAndRecruitmentsDTO(user, expectedRecruitments);
    }


    public UserAndRecruitmentsDTO createUserWithCapacitiesAndRecruitmentsForEachMasteryLevel() {
        var ca1 = capacityGenerator.createCapacity("CA-01");

        var jobCode = "J";
        var index = new int[]{1};
        var user = userProfileGenerator.createUserProfileWithLevelAndCapacities(new Integer[]{MasteryLevel.MIN_LEVEL.getMasteryLevel()}, createCapacitiesIncluding(10, ca1));
        var recruiter = organizationGenerator.createRecruiter("E-44", AbstractOrganization.OrganizationType.ENTERPRISE);
        var recruitments = Stream.of(MasteryLevel.values())
                .map(l -> recruitmentGenerator.createRecruitmentWithRequiredCapacityAndMasteryLevel(jobCode + (index[0]++), recruiter, l, ca1))
                .filter(r -> r.getJob().getMasteryLevel().getMasteryLevel() <= MasteryLevel.TECHNICAL.getMasteryLevel())
                .toList();

        return new UserAndRecruitmentsDTO(user, recruitments);
    }

    @Transactional
    public UserAndRecruitmentsDTO createJobsAndRecruitmentsInVariousStates() {
        var capacity = capacityGenerator.createCapacity("CA-01");
        var user = userProfileGenerator.createUserProfileWithLevelAndCapacities(new Integer[]{MasteryLevel.MAX_LEVEL.getMasteryLevel()}, createCapacitiesIncluding(10, capacity));
        var expectedJob = applicationContext.getBean(JobMotherObject.class)
                .withWorkingTimes(TypeWorkingTime.PART_TIME)
                .withLevel(MasteryLevel.MIN_LEVEL)
                .withCapacities(capacity)
                .withType(JobType.SIMPLE)
                .withTitle("J1")
                .buildAndPersist();
        var expectedRecruitment = recruitmentGenerator.createDefaultRecruitmentForJob(expectedJob, RecruitmentState.PUBLISHED);
        recruitmentGenerator.createDefaultRecruitmentForJob(expectedJob, RecruitmentState.CLOSED);
        recruitmentGenerator.createDefaultRecruitmentForJob(expectedJob, RecruitmentState.DRAFT);
        var jobWithoutRecruitment = jobGenerator.createJobWithCapacitiesAndLevel("J2", MasteryLevel.MIN_LEVEL, capacity);
        recruitmentGenerator.createDefaultRecruitmentForJob(jobWithoutRecruitment, RecruitmentState.DRAFT);
        recruitmentGenerator.createDefaultRecruitmentForJob(jobWithoutRecruitment, RecruitmentState.SELECTION);
        return new UserAndRecruitmentsDTO(user, expectedRecruitment);
    }

    @Transactional
    public UserAndRecruitmentsDTO getRecruitmentWithCandidature() {
        var capacity = capacityGenerator.createCapacity("CA-01");
        var user = userProfileGenerator.createUserProfileWithLevelAndCapacities(new Integer[]{MasteryLevel.MAX_LEVEL.getMasteryLevel()}, createCapacitiesIncluding(10, capacity));
        var expectedJob = jobGenerator.createJobWithCapacitiesAndLevel("J1", MasteryLevel.MIN_LEVEL, capacity);
        var recruitment = recruitmentGenerator.createDefaultRecruitmentForJob(expectedJob, RecruitmentState.PUBLISHED);
        var candidature = candidatureGenerator.createCandidature(user, recruitment);
        return new UserMatchingRecruitmentGenerator.UserAndRecruitmentsDTO(user, recruitment, candidature);
    }

    public UserAndRecruitmentsDTO getRecruitmentWithRomeCodes(String romeCode1, String romeCode2) {
        var capacity = capacityGenerator.createCapacity("CA-01");
        var user = userProfileGenerator.createUserProfileWithLevelAndCapacities(new Integer[]{MasteryLevel.MAX_LEVEL.getMasteryLevel()}, createCapacitiesIncluding(10, capacity));
        var expectedJob = jobGenerator.createJobWithCapacitiesAndLevel("J1", MasteryLevel.MIN_LEVEL, capacity);

        txHelper.doInTransaction(() -> {
            var rome1 = erhgoOccupationGenerator.createRomeOccupation(romeCode1, "Rome 01");
            var rome2 = erhgoOccupationGenerator.createRomeOccupation(romeCode2, "Rome 02");
            var erhgoOccupation = erhgoOccupationGenerator.save(new ErhgoOccupationMotherObject()
                    .withRomeOccupations(Set.of(rome1, rome2))
                    .instance());
            expectedJob.setErhgoOccupation(erhgoOccupation);
            jobRepository.save(expectedJob);
        });
        var recruitment = recruitmentGenerator.createDefaultRecruitmentForJob(expectedJob, RecruitmentState.PUBLISHED);
        var candidature = candidatureGenerator.createCandidature(user, recruitment);
        return new UserAndRecruitmentsDTO(user, recruitment, candidature);
    }

    public UserAndRecruitmentsDTO createForMatchingOnEPA() {
        var ca1 = capacityGenerator.createCapacity("CA-01");
        var ca2 = capacityGenerator.createCapacity("CA-02");
        var expectedJob = jobGenerator.createJobWithLevelAndCapacitiesOverMultipleMissions("J1", MasteryLevel.MIN_LEVEL, ca1);
        var expectedRecruitment = recruitmentGenerator.createDefaultRecruitmentForJob(expectedJob, RecruitmentState.PUBLISHED);
        jobGenerator.createJobWithLevelAndCapacitiesOverMultipleMissions("J2", MasteryLevel.MIN_LEVEL, ca1, ca2);

        var user = userProfileGenerator.createUserProfileWithCapacitiesFromEPA(UUID.randomUUID(), createCapacitiesIncluding(10, ca1).toArray(Capacity[]::new));
        return new UserAndRecruitmentsDTO(user, expectedRecruitment);
    }

    public UserAndRecruitmentsDTO createJobsAndRecruitmentsWithMultipleRecruitments() {
        var capacity = capacityGenerator.createCapacity("CA-01");
        var user = userProfileGenerator.createUserProfileWithLevelAndCapacities(new Integer[]{MasteryLevel.MAX_LEVEL.getMasteryLevel()}, createCapacitiesIncluding(10, capacity));
        var expectedJob = jobGenerator.createJobWithCapacitiesAndLevel("J1", MasteryLevel.MIN_LEVEL, capacity);
        var expectedRecruitment1 = recruitmentGenerator.createDefaultRecruitmentForJob(expectedJob, RecruitmentState.PUBLISHED);
        var expectedRecruitment2 = recruitmentGenerator.createDefaultRecruitmentForJob(expectedJob, RecruitmentState.PUBLISHED);
        recruitmentGenerator.createDefaultRecruitmentForJob(expectedJob, RecruitmentState.CLOSED);
        recruitmentGenerator.createDefaultRecruitmentForJob(expectedJob, RecruitmentState.DRAFT);
        return new UserAndRecruitmentsDTO(user, List.of(expectedRecruitment1, expectedRecruitment2));
    }

    @Transactional
    public UserAndRecruitmentsDTO createJobsAndRecruitmentsWithMultipleRecruitmentsAndUserWithSelectedCriteria() {
        var multipleCriteria = applicationContext.getBean(CriteriaMotherObject.class).withQuestionType(CriteriaQuestionType.MULTIPLE).withValueCodes("REP-1-1", "REP-2-1").buildAndPersist();

        var capacity = capacityGenerator.createCapacity("CA-01");
        var user = userProfileGenerator.createUserProfileWithLevelAndCapacitiesAndCriteria(new Integer[]{MasteryLevel.MAX_LEVEL.getMasteryLevel()}, multipleCriteria.getCriteriaValues(), Collections.emptyList(), createCapacitiesIncluding(10, capacity));
        var expectedJob = jobGenerator.createJobWithLevelCriteriaAndCapacities("J1", multipleCriteria.getCriteriaValues(), MasteryLevel.MIN_LEVEL, capacity);
        var expectedRecruitment = recruitmentGenerator.createDefaultRecruitmentForJob(expectedJob, RecruitmentState.PUBLISHED);
        recruitmentGenerator.createDefaultRecruitmentForJob(expectedJob, RecruitmentState.DRAFT);

        return new UserAndRecruitmentsDTO(user, expectedRecruitment);
    }

    public UserAndRecruitmentsDTO createPrivateUserAndThreeRecruitments(int nbCapacities) {
        var capacity = capacityGenerator.createCapacity("CA-01");
        var privateOrga1 = organizationGenerator.createRecruiter("T-1", AbstractOrganization.OrganizationType.PROJECT, false, true, false);
        var privateOrga2 = organizationGenerator.createRecruiter("T-2", AbstractOrganization.OrganizationType.PROJECT, false, true, false);
        var publicOrga3 = organizationGenerator.createRecruiter("T-3");
        var user = userProfileGenerator.createUserProfileWithChannelLevelAndCapacities(new String[]{privateOrga1.getCode()}, new Integer[]{MasteryLevel.MAX_LEVEL.getMasteryLevel()}, createCapacitiesIncluding(10, capacity));
        keycloakMockService.createBackOfficeGroupAndRoles(privateOrga1.getCode());
        keycloakMockService.createBackOfficeGroupAndRoles(privateOrga2.getCode());
        keycloakMockService.createBackOfficeGroupAndRoles(publicOrga3.getCode());

        var job1 = jobGenerator.createJobWithCapacitiesAndLevel(JobType.SIMPLE, "J-1", privateOrga1, MasteryLevel.MIN_LEVEL, capacity);
        var job2 = jobGenerator.createJobWithCapacitiesAndLevel(JobType.SIMPLE, "J-2", privateOrga2, MasteryLevel.MIN_LEVEL, capacity);
        var job3 = jobGenerator.createJobWithCapacitiesAndLevel(JobType.SIMPLE, "J-3", publicOrga3, MasteryLevel.MIN_LEVEL, capacity);

        var recruitment1 = recruitmentGenerator.createDefaultRecruitmentForJob(job1, RecruitmentState.PUBLISHED);
        var recruitment2 = recruitmentGenerator.createDefaultRecruitmentForJob(job2, RecruitmentState.PUBLISHED);
        var recruitment3 = recruitmentGenerator.createDefaultRecruitmentForJob(job3, RecruitmentState.PUBLISHED);

        return new UserAndRecruitmentsDTO(user, recruitment1);

    }


}
