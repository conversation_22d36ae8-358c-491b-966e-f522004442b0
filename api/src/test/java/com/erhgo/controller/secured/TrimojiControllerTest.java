package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TestUtils;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.openapi.dto.UserProfileSummaryDTO;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.trimoji.TrimojiClient;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.assertj.core.data.TemporalUnitWithinOffset;
import org.hamcrest.Matchers;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.RepeatedTest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.ResultActions;

import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
class TrimojiControllerTest extends AbstractIntegrationTest {

    @MockitoBean
    TrimojiClient trimojiClient;

    @Autowired
    ApplicationContext applicationContext;

    static final String USER_ID = "56b64e21-4771-4d01-8cf7-696866d3ae49";

    @Test
    @SneakyThrows
    @WithMockKeycloakUser(roles = Role.CANDIDATE, id = USER_ID)
    @ResetDataAfter
    void requestUrlToTrimoji() {
        var expectedUrl = "expectedUrl";
        Mockito.when(trimojiClient.getNewUrl()).thenReturn(expectedUrl);
        applicationContext.getBean(UserProfileMotherObject.class).withUserId(USER_ID).buildAndPersist();

        mvc.perform(get(realUrl("/trimoji")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.trimojiUrl", Matchers.is(expectedUrl)));
    }

    @Test
    @WithMockKeycloakUser(roles = Role.CANDIDATE, id = USER_ID)
    @ResetDataAfter
    void markTestAsEnded() throws Exception {
        applicationContext.getBean(UserProfileMotherObject.class).withUserId(USER_ID).buildAndPersist();
        performPut(realUrl("/trimoji"), null)
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var softSkillsStatus = applicationContext.getBean(UserProfileRepository.class).findByUserId(USER_ID).orElseThrow().trimojiStatus();
            Assertions.assertThat(softSkillsStatus.endedDate()).isCloseToUtcNow(new TemporalUnitWithinOffset(20, ChronoUnit.MINUTES));
            Assertions.assertThat(softSkillsStatus.startedDate()).isNull();
            Assertions.assertThat(softSkillsStatus.trimojiPdfUrl()).isNull();
        });
    }

    @RepeatedTest(10)
    @WithMockKeycloakUser(roles = Role.CANDIDATE, id = USER_ID)
    @ResetDataAfter
    void markTestAsEndedDoesNotOverridePdfUrl() throws Exception {
        var url = "http://42.fr";
        applicationContext.getBean(UserProfileMotherObject.class).withUserId(USER_ID).buildAndPersist();

        var executor = Executors.newFixedThreadPool(10);
        setUrl(url);
        var result = IntStream.range(0, 10).mapToObj(index ->
                endTest(executor)
        ).toList();
        executor.shutdown();
        Assertions.assertThat(executor.awaitTermination(2, TimeUnit.MINUTES)).isTrue();
        result.forEach(r -> {
            try {
                r.get().andExpect(status().isNoContent());
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        txHelper.doInTransaction(() -> {
            var softSkillsStatus = applicationContext.getBean(UserProfileRepository.class).findByUserId(USER_ID).orElseThrow().trimojiStatus();
            Assertions.assertThat(softSkillsStatus.trimojiPdfUrl()).isEqualTo(url);
            Assertions.assertThat(softSkillsStatus.endedDate()).isCloseToUtcNow(new TemporalUnitWithinOffset(20, ChronoUnit.MINUTES));
        });
    }

    @NotNull
    private Future<ResultActions> endTest(ExecutorService executor) {
        return executor.submit(() -> {
            TestUtils.mockAuthentication("EMAIL", USER_ID, Role.CANDIDATE);
            return performPut(realUrl("/trimoji"), null);
        });
    }

    @SneakyThrows
    private void setUrl(String url) {
        performPost("/public/trimoji", Map.of("result_pdf", url, "metadatas", Map.of("candidate_id", USER_ID))).andExpect(status().isNoContent());
    }

    @ParameterizedTest
    @EnumSource(UserProfileSummaryDTO.SoftSkillsStatusEnum.class)
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void get_profile_compute_soft_skills_status(UserProfileSummaryDTO.SoftSkillsStatusEnum expectedStatus) throws Exception {
        applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID)
                .withSoftSkillsStartedDate(expectedStatus == UserProfileSummaryDTO.SoftSkillsStatusEnum.NOT_STARTED ? null : OffsetDateTime.now())
                .withSoftSkillsEndedDate((expectedStatus == UserProfileSummaryDTO.SoftSkillsStatusEnum.NOT_STARTED || expectedStatus == UserProfileSummaryDTO.SoftSkillsStatusEnum.NOT_FINISHED) ? null : OffsetDateTime.now())
                .withTrimojiPdfUrl(expectedStatus == UserProfileSummaryDTO.SoftSkillsStatusEnum.AVAILABLE ? "url" : null)
                .buildAndPersist();

        mvc.perform(get("/api/odas/user/%s/profileSummary".formatted(USER_ID)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.softSkillsStatus", Matchers.is(expectedStatus.toString())));
    }
}
