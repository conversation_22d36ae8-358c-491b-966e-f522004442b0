package com.erhgo.controller.sourcing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.controller.secured.CustomizedResponseEntityExceptionHandler;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.enums.SubscriptionType;
import com.erhgo.domain.exceptions.InvalidSiretException;
import com.erhgo.domain.exceptions.SiretValidationTechnicalException;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.sourcing.SourcingInvitationMotherObject;
import com.erhgo.domain.sourcing.SourcingSubscription;
import com.erhgo.domain.sourcing.SourcingSubscriptionMotherObject;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.generators.RecruiterMotherObject;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.*;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailingListService;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.notifier.messages.SourcingInvitationUsedMessageDTO;
import com.erhgo.services.notifier.messages.SourcingNewUserMessageDTO;
import com.erhgo.services.organization.OrganizationDataProvider;
import com.erhgo.services.search.UserIndexer;
import com.erhgo.services.sourcing.SourcingScheduler;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.data.TemporalUnitWithinOffset;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Stream;

import static com.erhgo.domain.referential.AbstractOrganization.SiretVerificationStatus.TECHNICAL_ERROR;
import static com.erhgo.domain.referential.AbstractOrganization.SiretVerificationStatus.WRONG_SIRET;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.text.IsEmptyString.emptyOrNullString;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@TestPropertySource(properties = {"sendinblue.templates.sourcing-candidature-proposal=42"})
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
class SourcingSubscriptionControllerTest extends AbstractIntegrationTest {
    private static final String ORGANIZATION_CODE = "S-42";
    private static final String USER_ID = "b1a84b0a-4eea-4980-adff-0853af0f672a";
    private static final String SUPPORT_EMAIL = "<EMAIL>";
    static final String SIRET = "42";
    public static final String EMAIL = "<EMAIL>";
    private static final String FULLNAME = "john";
    public static final long TWO_DAYS_IN_MS = 2 * 24 * 60 * 60 * 1000;
    public static final long TWENTY_FIVE_DAYS_IN_MS = 25 * 24 * 60 * 60 * 1000L;
    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    JobRepository jobRepository;

    @Autowired
    RecruitmentRepository recruitmentRepository;

    @Autowired
    SourcingScheduler sourcingScheduler;

    @Autowired
    SourcingSubscriptionRepository sourcingSubscriptionRepository;
    @Autowired
    RecruitmentProfileRepository recruitmentProfileRepository;
    @Autowired
    CustomizedResponseEntityExceptionHandler handler;

    @Autowired
    AbstractOrganizationRepository organizationRepository;
    @Autowired
    SourcingInvitationRepository sourcingInvitationRepository;

    @Autowired
    CapacityGenerator capacityGenerator;

    @MockitoBean
    UserIndexer userIndexer;
    @MockitoBean
    MailingListService mailingListService;
    @MockitoBean
    Notifier notifier;

    @MockitoBean
    KeycloakMockService keycloakService;
    @MockitoBean
    private OrganizationDataProvider organizationDataProvider;

    private static final String ORGANIZATION_NAME = "acme";

    private Recruitment createRecruitment() {
        return applicationContext.getBean(RecruitmentMotherObject.class)
                .withRecruiterCode(ORGANIZATION_CODE)
                .withState(RecruitmentState.DRAFT)
                .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).withCapacities(applicationContext.getBean(CapacityGenerator.class).createCapacity("CA58-58")).buildAndPersist())
                .buildAndPersist();
    }

    private Recruiter createRecruiter() {
        return applicationContext.getBean(OrganizationGenerator.class)
                .createRecruiter(ORGANIZATION_CODE, AbstractOrganization.OrganizationType.SOURCING);
    }


    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    void recruiter_with_trial_account_cannot_create_another_recruitment() throws Exception {
        var recruitment = createRecruitment();
        var recruiter = recruitment.getRecruiter();
        when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(new UserRepresentation().setId(USER_ID).setGroups(List.of(ORGANIZATION_CODE)));
        applicationContext.getBean(SourcingSubscriptionMotherObject.class).withRecruiter(recruiter).buildAndPersist();
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).withCapacities(applicationContext.getBean(CapacityGenerator.class).createCapacity("CA1-1")).buildAndPersist();
        var location = new LocationDTO().city("Laon").citycode("69320").departmentCode("65").regionName("AA").longitude(5.5F).latitude(5.9F);
        performPost("/sourcing/recruitment", new CreateSourcingJobAndRecruitmentCommandDTO()
                .jobOccupationId(occupation.getId())
                .jobLocation(location)
                .title("osef")
        ).andExpect(status().isPaymentRequired());
    }


    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING})
    @ResetDataAfter
    @Test
    void update_subscription_fails_over_quota() throws Exception {
        var recruiter = createRecruiter();
        when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(new UserRepresentation().setId(USER_ID).setGroups(List.of(ORGANIZATION_CODE)));
        var code = "code";
        applicationContext.getBean(SourcingSubscriptionMotherObject.class).withRecruiter(recruiter).withExpirationDate(OffsetDateTime.now().plus(5, ChronoUnit.DAYS)).buildAndPersist();
        applicationContext.getBean(SourcingInvitationMotherObject.class).withCode(code).withMaxNumberOfGuests(-1).buildAndPersist();

        performPost("/sourcing/subscription", new UpdateSourcingSubscriptionCommandDTO().invitationCode(code))
                .andExpect(status().isPaymentRequired());
        verifyNoInteractions(notifier);
    }

    @SneakyThrows
    @ResetDataAfter
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    void publish_recruitment_updates_subscription_expiration_date(boolean wasPublished) {
        var recruitmentRef = new AtomicReference<Recruitment>();
        var recruiterRef = new AtomicReference<Recruiter>();
        var previousDate = OffsetDateTime.now().plusYears(5);
        when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(new UserRepresentation().setId(USER_ID).setGroups(List.of(ORGANIZATION_CODE)));
        txHelper.doInTransaction(() -> {
            var recruitment = createRecruitment();
            recruitment.setState(wasPublished ? RecruitmentState.PUBLISHED : RecruitmentState.DRAFT, false);
            var recruiter = recruitment.getRecruiter();
            applicationContext.getBean(SourcingSubscriptionMotherObject.class).withRecruiter(recruiter).withExpirationDate(wasPublished ? previousDate : null).buildAndPersist();
            recruitment.getErhgoOccupation().setTechnical(false);
            recruiterRef.set(recruiter);
            recruitmentRef.set(recruitment);
        });
        var recruitment = recruitmentRef.get();
        performPost("/sourcing/recruitment/%s/change-state".formatted(recruitment.getId()), new ChangeSourcingRecruitmentStateCommandDTO().nextState(RecruitmentStateDTO.PUBLISHED).sendNotifications(UsersToNotifySelectionTypeDTO.ALL))
                .andExpect(status().isOk());

        txHelper.doInTransaction(() -> {
            var subscription = sourcingSubscriptionRepository.findOneByRecruiter(recruiterRef.get()).orElseThrow();
            assertThat(subscription.getExpirationDate()).isCloseTo(wasPublished ? previousDate : OffsetDateTime.now().plusDays(30), new TemporalUnitWithinOffset(1, ChronoUnit.DAYS));
        });
    }


    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    void create_subscription_on_account_init() throws Exception {
        when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(new UserRepresentation().setEmail(EMAIL).setId(USER_ID));
        performPost(
                "/sourcing/initialize-account",
                null
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(jsonPath("$.recruitmentId", Matchers.emptyOrNullString()));
        var stringCaptor = ArgumentCaptor.forClass(String.class);
        verify(sourcingKeycloakService).assignUserToSourcingGroup(eq(USER_ID), stringCaptor.capture());
        txHelper.doInTransaction(() -> {
            var subscription = sourcingSubscriptionRepository.findOneByRecruiter((Recruiter) organizationRepository.findOneByCode(stringCaptor.getValue())).orElseThrow();
            assertThat(subscription.getExpirationDate()).isNull();
            assertThat(subscription.getSubscriptionType()).isEqualTo(SubscriptionType.TRIAL);
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    void get_subscription() {
        var recruiter = createRecruiter();
        when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(new UserRepresentation().setId(USER_ID).setGroups(List.of(ORGANIZATION_CODE)));
        applicationContext.getBean(SourcingSubscriptionMotherObject.class)
                .withRecruiter(recruiter)
                .withExpirationDate(OffsetDateTime.of(2422, 5, 5, 5, 5, 5, 5, ZoneOffset.UTC)).buildAndPersist();
        performGetAndExpect("/sourcing/subscription", "subscription", false);
    }


    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    @ResetDataAfter
    @Test
    void update_subscription_with_wrong_invitation_code_returns_403() throws Exception {
        prepareUser().setGroups(List.of(ORGANIZATION_CODE));
        var recruiter = createRecruiter();
        applicationContext.getBean(SourcingSubscriptionMotherObject.class).withRecruiter(recruiter).buildAndPersist();
        performPost("/sourcing/subscription", new UpdateSourcingSubscriptionCommandDTO().invitationCode("BAD"))
                .andExpect(status().isForbidden());
        verifyNoInteractions(notifier);
    }

    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING})
    @ResetDataAfter
    @Test
    void update_subscription_without_invitation_code_returns_400() throws Exception {
        prepareUser();
        performPost("/sourcing/subscription", new UpdateSourcingSubscriptionCommandDTO())
                .andExpect(status().isBadRequest());
        verifyNoInteractions(notifier);
    }

    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING})
    @ResetDataAfter
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void update_subscription_activate_account(boolean secondTime) throws Exception {
        var recruiter = createRecruiter();
        when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(new UserRepresentation().setId(USER_ID).setGroups(List.of(ORGANIZATION_CODE)));
        var code = "code";
        if (!secondTime) {
            applicationContext.getBean(SourcingSubscriptionMotherObject.class).withRecruiter(recruiter).withExpirationDate(OffsetDateTime.now().plus(5, ChronoUnit.DAYS)).buildAndPersist();
        }
        var sourcingInvitationMotherObject = applicationContext.getBean(SourcingInvitationMotherObject.class).withCode(code);
        if (secondTime) {
            sourcingInvitationMotherObject.withGuests(recruiter);
        }
        sourcingInvitationMotherObject.buildAndPersist();
        performPost("/sourcing/subscription", new UpdateSourcingSubscriptionCommandDTO().invitationCode(code))
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var subscription = sourcingSubscriptionRepository.findOneByRecruiter(recruiter).orElseThrow();
            assertThat(subscription.getExpirationDate()).isNull();
            assertThat(subscription.getSubscriptionType()).isEqualTo(SubscriptionType.ACTIVATED);
            assertThat(sourcingInvitationRepository.findOneByCode(code).orElseThrow().getGuests()).anyMatch(r -> r.getId().equals(recruiter.getId()));
            assertThat(subscription.getMailState()).isNull();
        });
        verify(notifier, times(secondTime ? 0 : 1)).sendMessage(any(SourcingInvitationUsedMessageDTO.class));
    }


    private UserRepresentation prepareUser() {
        var userRepresentation = new UserRepresentation()
                .setMiscAttributes(Map.of("siret", List.of(SIRET), "fullname", List.of(FULLNAME)))
                .setEmail(EMAIL)
                .setId(USER_ID);

        when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(userRepresentation);
        return userRepresentation;
    }


    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING})
    @ResetDataAfter
    @Test
    void initialize_account_affects_group_to_authenticated_user_and_initialize_organization() throws Exception {
        prepareUser();
        when(organizationDataProvider.getNameForSiret(SIRET)).thenReturn(ORGANIZATION_NAME);
        var res = performPost(
                "/sourcing/initialize-account",
                null
        ).andExpect(status().isOk());
        res.andExpect(jsonPath("$.jobId", emptyOrNullString()));
        res.andExpect(jsonPath("$.recruitmentId", emptyOrNullString()));
        res.andExpect(jsonPath("$.hasSiretFormatError", Matchers.is(false)));

        verify(keycloakService).createFrontOfficeGroupAndRole(Mockito.anyString());
        verify(keycloakService).createBackOfficeGroupAndRoles(Mockito.anyString());

        var stringCaptor = ArgumentCaptor.forClass(String.class);
        verify(sourcingKeycloakService).assignUserToSourcingGroup(eq(USER_ID), stringCaptor.capture());
        verifyNoInteractions(mailingListService);
        txHelper.doInTransaction(() -> {
            var recruiter = (Recruiter) organizationRepository.findOneByCode(stringCaptor.getValue());
            assertThat(recruiter.getSiretVerificationStatus()).isEqualTo(AbstractOrganization.SiretVerificationStatus.SUCCESS);
            var sourcingSubscription = sourcingSubscriptionRepository.findOneByRecruiter(recruiter).orElseThrow();
            assertThat(sourcingSubscription.getSubscriptionType()).isEqualTo(SubscriptionType.TRIAL);
            assertThat(sourcingSubscription.getExpirationDate()).isNull();
            assertThat(sourcingSubscription.getMailState()).isEqualTo(SourcingSubscription.SourcingMailState.SEND_WELCOME);
        });

    }

    @ParameterizedTest
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING})
    @ResetDataAfter
    @ValueSource(booleans = {true, false})
    void initialize_account_affects_group_to_authenticated_user_and_initialize_organization_with_siret_as_title_on_error_siret(boolean isInvalidSiret) throws Exception {
        prepareUser();
        when(organizationDataProvider.getNameForSiret(SIRET)).thenThrow(isInvalidSiret ? new InvalidSiretException() : new SiretValidationTechnicalException());
        performPost(
                "/sourcing/initialize-account",
                null
        )
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.hasSiretFormatError", Matchers.is(isInvalidSiret)));
        var stringCaptor = ArgumentCaptor.forClass(String.class);
        verify(sourcingKeycloakService).assignUserToSourcingGroup(eq(USER_ID), stringCaptor.capture());

        var recruiter = applicationContext.getBean(RecruiterRepository.class).findOneByCode(stringCaptor.getValue());
        assertThat(recruiter.getTitle()).isEqualTo(SIRET);
        assertThat(recruiter.getSiretVerificationStatus()).isEqualTo(isInvalidSiret ? WRONG_SIRET : TECHNICAL_ERROR);

        verify(notifier).sendMessage(any(SourcingNewUserMessageDTO.class));
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING})
    @ResetDataAfter
    void initialize_account_affects_group_to_authenticated_user_and_initialize_organization_with_blank_if_user_has_no_siret() throws Exception {
        prepareUser().setMiscAttributes(null);
        performPost(
                "/sourcing/initialize-account",
                null
        ).andExpect(status().isOk());

        var stringCaptor = ArgumentCaptor.forClass(String.class);
        verify(sourcingKeycloakService).assignUserToSourcingGroup(eq(USER_ID), stringCaptor.capture());
        var recruiter = applicationContext.getBean(RecruiterRepository.class).findOneByCode(stringCaptor.getValue());
        assertThat(recruiter.getTitle()).isEqualTo(" ");
        assertThat(recruiter.getSiretVerificationStatus()).isEqualTo(WRONG_SIRET);
        verify(notifier).sendMessage(any(SourcingNewUserMessageDTO.class));
        verifyNoInteractions(mailingListService);
        txHelper.doInTransaction(() -> {
            assertThat(sourcingSubscriptionRepository.findOneByRecruiter(recruiter).orElseThrow().getMailState()).isEqualTo(SourcingSubscription.SourcingMailState.SEND_WELCOME);
        });
    }


    @ParameterizedTest
    @ValueSource(strings = "any")
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING})
    @ResetDataAfter
    @NullSource
    void initialize_account_use_user_group_if_exists_and_ignore_invitation_code(String invitationCode) throws Exception {
        applicationContext.getBean(OrganizationGenerator.class).createRecruiter(ORGANIZATION_CODE, AbstractOrganization.OrganizationType.SOURCING);
        prepareUser().setGroups(List.of(ORGANIZATION_CODE));

        performPost(
                "/sourcing/initialize-account",
                null
        ).andExpect(status().isOk());
        verifyNoInteractionsWithMock();
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING})
    @ResetDataAfter
    void initialize_account_with_existing_siret() throws Exception {
        applicationContext.getBean(OrganizationGenerator.class).createRecruiterWithSiretAndTitle("**************", "Pizzaiolo");
        prepareUser();
        performPost(
                "/sourcing/initialize-account",
                null
        ).andExpect(status().isOk());
        applicationContext.getBean(OrganizationGenerator.class).createRecruiterWithSiretAndTitle("**************", "Atos");
        performPost(
                "/sourcing/initialize-account",
                null
        ).andExpect(status().isConflict());
    }

    private void verifyNoInteractionsWithMock() {
        verify(sourcingKeycloakService, never()).assignUserToSourcingGroup(eq(USER_ID), anyString());
        verifyNoInteractions(organizationDataProvider);
        verifyNoInteractions(mailingListService);
        verifyNoInteractions(notifier);
    }

    @SneakyThrows
    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING})
    @ResetDataAfter
    void initializeAccount_does_nothing_for_already_published_recruitment() {
        var organization = applicationContext.getBean(OrganizationGenerator.class).createRecruiter(ORGANIZATION_CODE, AbstractOrganization.OrganizationType.SOURCING);
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withRecruiter(organization)
                .buildAndPersist();
        prepareUser().setGroups(List.of(ORGANIZATION_CODE));
        var res = performPost(
                "/sourcing/initialize-account",
                null
        )
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.recruitmentId", Matchers.is(recruitment.getId().intValue())))
                .andExpect(jsonPath("$.jobId", Matchers.is(recruitment.getJob().getId().toString())));
        verifyNoInteractionsWithMock();
    }

    @SneakyThrows
    @ParameterizedTest
    @EnumSource(SourcingSubscription.SourcingMailState.class)
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    @NullSource
    void sendNoWelcomeMailUpdatesStatus(SourcingSubscription.SourcingMailState initialMailState) {
        var recruiter = applicationContext.getBean(RecruiterMotherObject.class)
                .withCode(ORGANIZATION_CODE)
                .withOrganizationType(AbstractOrganization.OrganizationType.SOURCING)
                .buildAndPersist();

        var subscription = applicationContext.getBean(SourcingSubscriptionMotherObject.class)
                .withRecruiter(recruiter)
                .withMailState(initialMailState)
                .withCreatedDate(new Date())
                .buildAndPersist();

        sourcingScheduler.sendTrialMails();
        verifyNoInteractionsWithMock();
        txHelper.doInTransaction(() -> assertThat(sourcingSubscriptionRepository.getReferenceById(subscription.getUuid()).getMailState()).isEqualTo(initialMailState));
    }

    @SneakyThrows
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void sendWelcomeMailUpdatesStatus() {
        boolean success = true;
        var email11 = "1@1";
        var email12 = "2@1";
        var siret1 = "555";
        var organizationCode1 = "OG1";
        var subscription1 = getSourcingSubscription(siret1, organizationCode1, new Date(new Date().getTime() - TWO_DAYS_IN_MS), SourcingSubscription.SourcingMailState.SEND_WELCOME, true, email11, email12);

        var siret2 = "666";
        var organizationCode2 = "OG2";
        var email21 = "1@2";
        var subscription2 = getSourcingSubscription(siret2, organizationCode2, new Date(new Date().getTime() - TWO_DAYS_IN_MS), SourcingSubscription.SourcingMailState.SEND_WELCOME, false, email21);

        getSourcingSubscription("777", "nope", new Date(), SourcingSubscription.SourcingMailState.SEND_WELCOME, true, "email@ko");

        when(mailingListService.sendMailsForTemplate(ArgumentMatchers.argThat(a -> a != null && a.size() == 2), anyLong(), any(), isNull()))
                .thenReturn(CompletableFuture.completedFuture(success ? Set.of(email11, email12) : new HashSet<>()));
        when(mailingListService.sendMailsForTemplate(ArgumentMatchers.argThat(a -> a != null && a.size() == 1), anyLong(), any(), isNull()))
                .thenReturn(CompletableFuture.completedFuture(success ? Set.of(email21) : new HashSet<>()));

        sourcingScheduler.sendTrialMails();
        verify(mailingListService).sendMailsForTemplate(Set.of(email21), 318L, Map.of(
                "SOURCING_URL", "https://sourcing.erhgo.fr",
                "SIRET", siret2,
                "WRONG_SIRET", "false"
        ), null);
        verify(mailingListService).sendMailsForTemplate(Set.of(email11, email12), 318L, Map.of(
                "SOURCING_URL", "https://sourcing.erhgo.fr",
                "SIRET", siret1,
                "WRONG_SIRET", "true"
        ), null);
        verifyNoMoreInteractions(mailingListService);
        txHelper.doInTransaction(() -> assertThat(sourcingSubscriptionRepository.getReferenceById(subscription1.getUuid()).getMailState()).isEqualTo(success ? SourcingSubscription.SourcingMailState.SEND_TRIAL_END : SourcingSubscription.SourcingMailState.SEND_WELCOME));
        txHelper.doInTransaction(() -> assertThat(sourcingSubscriptionRepository.getReferenceById(subscription2.getUuid()).getMailState()).isEqualTo(success ? SourcingSubscription.SourcingMailState.SEND_TRIAL_END : SourcingSubscription.SourcingMailState.SEND_WELCOME));
    }

    private SourcingSubscription getSourcingSubscription(String siret, String organizationCode, Date createdDate, SourcingSubscription.SourcingMailState sourcingMailState, boolean wrongSiret, String... emails) {
        var recruiter1 = applicationContext.getBean(RecruiterMotherObject.class)
                .withCode(organizationCode)
                .withOrganizationType(AbstractOrganization.OrganizationType.SOURCING)
                .withSiret(siret)
                .withWrongSiret(wrongSiret)
                .buildAndPersist();
        when(sourcingKeycloakService.getEnabledSourcingUsersForGroup(organizationCode)).thenReturn(Stream.of(emails).map(email -> new UserRepresentation().setEmail(email)).toList());
        return applicationContext.getBean(SourcingSubscriptionMotherObject.class)
                .withRecruiter(recruiter1)
                .withCreatedDate(createdDate)
                .withMailState(sourcingMailState)
                .withExpirationDate(sourcingMailState == SourcingSubscription.SourcingMailState.SEND_TRIAL_END ? OffsetDateTime.now().minusDays(7) : null)
                .buildAndPersist();
    }

    @SneakyThrows
    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void sendTrialEnd() {
        var email11 = "1@1";
        var email12 = "2@2";
        var siret1 = "555";
        var organizationCode1 = "OG1";
        var subscription = getSourcingSubscription(siret1, organizationCode1, new Date(new Date().getTime() - TWENTY_FIVE_DAYS_IN_MS), SourcingSubscription.SourcingMailState.SEND_TRIAL_END, true, email11, email12);
        getSourcingSubscription("777", "nope", new Date(new Date().getTime() - TWO_DAYS_IN_MS), SourcingSubscription.SourcingMailState.SEND_TRIAL_END, true, "email@ko");

        when(mailingListService.sendMailsForTemplate(any(), anyLong(), any(), isNull(), anyString())).thenReturn(CompletableFuture.completedFuture(Set.of(email11, email12)));

        sourcingScheduler.sendTrialMails();
        verify(mailingListService).sendMailsForTemplate(Set.of(email11, email12), 312, Map.of(
                "SOURCING_URL", "https://sourcing.erhgo.fr"
        ), null, SUPPORT_EMAIL);
        txHelper.doInTransaction(() -> assertThat(sourcingSubscriptionRepository.getReferenceById(subscription.getUuid()).getMailState()).isNull());
    }
}

