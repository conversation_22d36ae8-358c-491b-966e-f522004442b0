package com.erhgo.controller.sourcing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.openapi.dto.ChangeSourcingRecruitmentStateCommandDTO;
import com.erhgo.openapi.dto.RecruitmentStateDTO;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.mailing.MailingListService;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static org.mockito.ArgumentMatchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class SourcingCloseRecruitmentControllerTest extends AbstractIntegrationTest {
    private static final String ORGANIZATION_CODE = "S-42";
    private static final String USER_ID = "b1a84b0a-4eea-4980-adff-0853af0f672a";

    @Autowired
    ApplicationContext applicationContext;

    @MockitoBean
    MailingListService mailingListService;

    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    @Test
    void sendMailOnRecruitmentClose() {
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withRecruiterCode(ORGANIZATION_CODE)
                .buildAndPersist();

        var profilesToNotify = Stream.of(true, false).flatMap(visible -> Stream.of(GlobalCandidatureState.values())
                        .map(s -> applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                        .withState(s)
                        .withUserProfile(applicationContext.getBean(UserProfileMotherObject.class)
                                .withEmail("%s@erhgo".formatted(s.name()))
                                .withFirstname(s.name().toLowerCase())
                                .buildAndPersist())
                        .withRecruitment(recruitment)
                        .withModifiedByUser(visible)
                        .buildAndPersist())
                        .filter(c -> c.isVisibleForUser() && List.of(
                        GlobalCandidatureState.NOT_TREATED_BY_ERHGO,
                        GlobalCandidatureState.INTERNAL_POSITION,
                        GlobalCandidatureState.INTRODUCE_TO_CLIENT,
                        GlobalCandidatureState.SUMMARY_SHEET_SENT,
                        GlobalCandidatureState.ON_RECRUITMENT_CLIENT,
                        GlobalCandidatureState.RECRUITMENT_VALIDATED,
                        GlobalCandidatureState.NEW,
                        GlobalCandidatureState.STAND_BY
                ).contains(c.getGlobalCandidatureState()))
                        .map(RecruitmentCandidature::getUserProfile))
                .toList();

        performPost("/sourcing/recruitment/%s/change-state".formatted(recruitment.getId()), new ChangeSourcingRecruitmentStateCommandDTO().nextState(RecruitmentStateDTO.CLOSED))
                .andExpect(status().isOk());

        Mockito.verify(mailingListService).sendMailsToProfilesForTemplate(
                eq(new HashSet<>(profilesToNotify)),
                anyString(),
                eq(415L),
                eq(Map.of(
                        "RECRUITER", recruitment.getRecruiterTitle(),
                        "TITLE", recruitment.getJobTitle()
                )),
                isNull());
    }

}
