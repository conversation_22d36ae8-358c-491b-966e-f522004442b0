package com.erhgo.controller.sourcing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.userprofile.SourcingPreferencesMotherObject;
import com.erhgo.openapi.dto.DeactivateUserSourcingCommandDTO;
import com.erhgo.repositories.RecruitmentRepository;
import com.erhgo.repositories.SourcingPreferencesRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.keycloak.SourcingKeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class DeactivateUserSourcingControllerTest extends AbstractIntegrationTest {


    @Autowired
    private SourcingPreferencesRepository sourcingPreferencesRepository;

    @Autowired
    private RecruitmentRepository recruitmentRepository;

    @Autowired
    private SourcingKeycloakService sourcingKeycloakService;

    @Autowired
    ApplicationContext applicationContext;

    private static final String USER_ID_TO_DEACTIVATE = "6cf3b6ca-8157-4bdf-b186-2e71644c032e";
    private static final String REPLACEMENT_USER_ID = "d01cd104-04df-40a4-9348-5033f78fbb03";
    private static final String OTHER_USER_ID = "other-user";

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    @Transactional
    void deactivateUserSourcing() throws Exception {
        var recruitment1 = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withManagerUserId(USER_ID_TO_DEACTIVATE)
                .withSourcingNotifiedUsersIds(USER_ID_TO_DEACTIVATE, OTHER_USER_ID)
                .buildAndPersist();

        var recruitment2 = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withManagerUserId(USER_ID_TO_DEACTIVATE)
                .withSourcingNotifiedUsersIds(USER_ID_TO_DEACTIVATE)
                .buildAndPersist();

        applicationContext.getBean(SourcingPreferencesMotherObject.class)
                .withUserId(USER_ID_TO_DEACTIVATE)
                .buildAndPersist();

        Mockito.when(sourcingKeycloakService.getSourcingUser(USER_ID_TO_DEACTIVATE)).thenReturn(Optional.of(new UserRepresentation().setId(USER_ID_TO_DEACTIVATE)));

        var command = new DeactivateUserSourcingCommandDTO()
                .userId(USER_ID_TO_DEACTIVATE)
                .replacementUserId(REPLACEMENT_USER_ID);

        performPut("/user/deactivate-user-sourcing", command).andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var recruitmentUpdated1 = recruitmentRepository.findById(recruitment1.getId()).orElseThrow();
            assertThat(recruitmentUpdated1.getManagerUserId()).isEqualTo(REPLACEMENT_USER_ID);
            assertThat(recruitmentUpdated1.getSourcingUsersIdToNotify()).containsExactlyInAnyOrder(OTHER_USER_ID, REPLACEMENT_USER_ID);
            assertThat(recruitmentUpdated1.getSourcingUsersIdToNotify()).doesNotContain(USER_ID_TO_DEACTIVATE);

            var recruitmentUpdated2 = recruitmentRepository.findById(recruitment2.getId()).orElseThrow();
            assertThat(recruitmentUpdated2.getManagerUserId()).isEqualTo(REPLACEMENT_USER_ID);
            assertThat(recruitmentUpdated2.getSourcingUsersIdToNotify()).containsExactlyInAnyOrder(REPLACEMENT_USER_ID);
            assertThat(recruitmentUpdated2.getSourcingUsersIdToNotify()).doesNotContain(USER_ID_TO_DEACTIVATE);

            assertThat(sourcingPreferencesRepository.findByUserId(USER_ID_TO_DEACTIVATE)).isEmpty();

            Mockito.verify(sourcingKeycloakService).disableUser(USER_ID_TO_DEACTIVATE);
        });

    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    @Transactional
    void activateUserSourcing() throws Exception {
        Mockito.when(sourcingKeycloakService.getSourcingUser(USER_ID_TO_DEACTIVATE)).thenReturn(Optional.of(new UserRepresentation().setId(USER_ID_TO_DEACTIVATE)));
        mvc.perform(put("/api/odas/user/%s/activate-user-sourcing".formatted(USER_ID_TO_DEACTIVATE))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        Mockito.verify(sourcingKeycloakService).enableUser(USER_ID_TO_DEACTIVATE);
    }


}
