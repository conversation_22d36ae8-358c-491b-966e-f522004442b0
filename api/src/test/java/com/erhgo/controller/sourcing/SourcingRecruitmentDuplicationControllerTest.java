package com.erhgo.controller.sourcing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TestUtils;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.CriteriaQuestionType;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.enums.TypeWorkingTime;
import com.erhgo.domain.job.JobMotherObject;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.CriteriaMotherObject;
import com.erhgo.generators.RecruiterMotherObject;
import com.erhgo.openapi.dto.DuplicateSourcingJobAndRecruitmentCommandDTO;
import com.erhgo.repositories.RecruitmentRepository;
import com.erhgo.repositories.classifications.ErhgoClassificationRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.keycloak.UserRepresentation;
import com.google.common.collect.Sets;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.Assertions;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.time.OffsetDateTime;
import java.util.*;

class SourcingRecruitmentDuplicationControllerTest extends AbstractIntegrationTest {

    static final String ORGA_CODE = "S-42";
    static final String USER_ID = "6b814fc3-c5de-4a6b-99d4-898b72de1843";
    @Autowired
    ApplicationContext applicationContext;

    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.SOURCING, ORGA_CODE}, id = USER_ID)
    void duplicatesSourcingRecruitmentSucceedAndCreatesNewRecruitmentId() {

        // GIVEN
        var jobOccupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withId(UUID.fromString("6b814fc3-c5de-4a6b-99d4-898b72de1843"))
                .withCapacities(applicationContext.getBean(CapacityGenerator.class).createCapacity("CA1-42")).buildAndPersist();

        var criteria11 = applicationContext.getBean(CriteriaMotherObject.class).withQuestionType(CriteriaQuestionType.THRESHOLD).buildAndPersist();
        var criteria12 = applicationContext.getBean(CriteriaMotherObject.class).withQuestionType(CriteriaQuestionType.MULTIPLE).buildAndPersist();
        var criteria3 = applicationContext.getBean(CriteriaMotherObject.class).buildAndPersist();

        Mockito.when(sourcingKeycloakService.getSourcingUser(USER_ID))
                .thenReturn(Optional.of(new UserRepresentation().setId(USER_ID).setEmail("<EMAIL>").setMiscAttributes(Map.of("fullname", List.of("Jean Valjean 1")))));

        var job = applicationContext.getBean(JobMotherObject.class)
                .withOccupation(jobOccupation)
                .withCriteriaValues(criteria11.getCriteriaValues().get(0), criteria12.getCriteriaValues().get(1))
                .withWorkingTimes(TypeWorkingTime.FULL_TIME)
                .withLocation(Location.builder().city("Toulouse").longitude(2.0f).latitude(2.6f).postcode("45").regionName("8989").departmentCode("55").citycode("89").build())
                .withTitle("Title to duplicate")
                .withRecruiter(applicationContext.getBean(RecruiterMotherObject.class).withCode(ORGA_CODE).buildAndPersist())
                .buildAndPersist();

        var recruitmentToDuplicate = applicationContext.getBean(RecruitmentMotherObject.class)
                .withRecruiterDescription("recruiter description")
                .withLocation(Location.builder().city("Lyon").build())
                .withModularWorkingTime(true)
                .withWorkingWeeklyTime(42)
                .withJob(job)
                .withRecruitmentProfileQuestion("Ça va ?")
                .withSalaries(200, 800)
                .withPublicationEndDate(OffsetDateTime.now())
                .withState(RecruitmentState.PUBLISHED)
                .withErhgoClassification(Sets.newHashSet(applicationContext.getBean(ErhgoClassificationRepository.class).findAllById(Set.of("SO-05", "SO-07"))))
                .buildAndPersist();

        // WHEN
        performPost("/sourcing/recruitment/duplicate", new DuplicateSourcingJobAndRecruitmentCommandDTO().recruitmentId(recruitmentToDuplicate.getId()))
                // THEN
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.jobId", Matchers.not(Matchers.is(recruitmentToDuplicate.getJob().getId()))))
                .andExpect(MockMvcResultMatchers.jsonPath("$.recruitmentId", Matchers.not(Matchers.is(recruitmentToDuplicate.getId().intValue()))))
                .andExpect(TestUtils.jsonMatchesContent("sourcingRecruitmentDuplicated"))
        ;

        txHelper.doInTransaction(() -> {
            Assertions.assertThat(applicationContext.getBean(RecruitmentRepository.class).findAll()).noneMatch(r -> StringUtils.isBlank(r.getCode()));
        });
    }

    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.SOURCING, "S-25"})
    void duplicatesSourcingRecruitmentFailsWhenWrongOrga() {
        var recruitmentToDuplicate = applicationContext.getBean(RecruitmentMotherObject.class).buildAndPersist();
        performPost("/sourcing/recruitment/duplicate", new DuplicateSourcingJobAndRecruitmentCommandDTO().recruitmentId(recruitmentToDuplicate.getId()))
                // THEN
                .andExpect(MockMvcResultMatchers.status().isForbidden());
    }


    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.SOURCING})
    void duplicatesSourcingRecruitmentFailsWhenWrongRecruitmentId() {
        var recruitmentToDuplicate = applicationContext.getBean(RecruitmentMotherObject.class).buildAndPersist();
        performPost("/sourcing/recruitment/duplicate", new DuplicateSourcingJobAndRecruitmentCommandDTO().recruitmentId(999L))
                // THEN
                .andExpect(MockMvcResultMatchers.status().isNotFound());
    }
}
