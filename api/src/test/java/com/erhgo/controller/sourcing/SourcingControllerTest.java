package com.erhgo.controller.sourcing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.dto.EmailVerificationResultDTO;
import com.erhgo.domain.enums.ContractType;
import com.erhgo.domain.enums.TypeContractCategory;
import com.erhgo.domain.enums.TypeWorkingTime;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.sourcing.SourcingInvitationMotherObject;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.CriteriaMotherObject;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.*;
import com.erhgo.repositories.classifications.ErhgoClassificationRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.dto.criteria.SourcingCandidatesCriteria;
import com.erhgo.services.generation.OccupationForLabelGenerationService;
import com.erhgo.services.generation.dto.OccupationForLabelGenerationResult;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailingListService;
import com.erhgo.services.mailing.check.EmailVerificationService;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.notifier.OccupationCreationSourceType;
import com.erhgo.services.notifier.messages.SourcingNewUserMessageDTO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.testcontainers.shaded.com.google.common.collect.Sets;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
class SourcingControllerTest extends AbstractIntegrationTest {

    public static final String ORGANIZATION_CODE = "S-42";
    public static final String CAPACITY_CODE = "CA-1";
    public static final String EMAIL = "<EMAIL>";
    private static final String FULLNAME = "john";
    @Autowired
    private ApplicationContext applicationContext;
    @MockitoBean
    private KeycloakMockService keycloakMockService;
    @MockitoBean
    private Notifier notifier;
    @MockitoBean
    private MailingListService mailingListService;
    @Autowired
    UserProfileRepository userProfileRepository;
    @Autowired
    SourcingInvitationRepository sourcingInvitationRepository;
    @MockitoBean
    OccupationForLabelGenerationService occupationForLabelGenerationService;
    static final String USER_ID = "56f88cfe-0dc4-46d6-a0af-6e7c8ad49f7c";
    static final String SIRET = "42";

    @Autowired
    private RecruitmentRepository recruitmentRepository;

    @MockitoBean
    SourcingUserRepository sourcingUserRepository;

    @MockitoBean
    EmailVerificationService emailVerificationService;

    @SneakyThrows
    @Test
    @WithMockKeycloakUser(roles = Role.SOURCING)
    void retrieves_users_count() {
        var longitude = 42.0f;
        var latitude = 24.0f;

        var classification1 = "SO-01";
        var classification2 = "SO-02";

        var occupationId = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withLevel(4)
                .buildAndPersist().getId();

        var expectedCriteria = applicationContext.getBean(SourcingCandidatesCriteria.class)
                .occupationId(occupationId)
                .classifications(List.of(classification1, classification2))
                .longitude(longitude)
                .latitude(latitude)
                .masteryLevelAround(4);
        when(sourcingUserRepository.countCandidates(any())).thenReturn(60);
        mvc.perform(get(realUrl("/sourcing/count-candidates")).queryParam("occupationId", occupationId.toString())
                        .queryParam("workingTimeType", "")
                        .queryParam("typeContractCategory", "")
                        .queryParam("longitude", String.valueOf(longitude))
                        .queryParam("latitude", String.valueOf(latitude))
                        .queryParam("classifications", classification1, classification2))
                .andExpect(jsonPath("$.count", Matchers.is(60)));

        Mockito.verify(sourcingUserRepository).countCandidates(assertArg(c ->
                {
                    assertThat(c.occupationId()).isEqualTo(occupationId);
                    assertThat(c.classifications()).contains(classification1);
                    assertThat(c.classifications()).contains(classification2);
                    assertThat(c.masteryLevelAround()).isEqualTo(4);
                    assertThat(c.masteryLevelMax()).isEqualTo(expectedCriteria.masteryLevelMax());
                    assertThat(c.masteryLevelMin()).isEqualTo(expectedCriteria.masteryLevelMin());
                    assertThat(c.latitude()).isEqualTo(expectedCriteria.latitude());
                    assertThat(c.longitude()).isEqualTo(expectedCriteria.longitude());
                }
        ));
    }

    @Test
    @WithMockKeycloakUser(roles = Role.SOURCING)
    void countCandidates_check_working_type_add_correctly_to_criteria() throws Exception {
        var longitude = 42.0f;
        var latitude = 24.0f;

        var occupationId = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .buildAndPersist()
                .getId();

        var query = new QueryStringBuilder()
                .addParameter("occupationId", occupationId.toString())
                .addParameter("workingTimeType", TypeWorkingTime.PART_TIME.name())
                .addParameter("longitude", String.valueOf(longitude))
                .addParameter("latitude", String.valueOf(latitude))
                .build();

        mvc.perform(get(realUrl("/sourcing/count-candidates%s".formatted(query))))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.SOURCING, ORGANIZATION_CODE})
    void retrieves_new_users_count() {
        var longitude = 42.0f;
        var latitude = 24.0f;
        var classifications = new String[]{"SO-01", "SO-02"};
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).withLevel(4).buildAndPersist();
        var occupationId = occupation.getId();

        var organization = applicationContext.getBean(OrganizationGenerator.class).createRecruiter(ORGANIZATION_CODE, AbstractOrganization.OrganizationType.SOURCING);
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withErhgoClassification(Sets.newHashSet(applicationContext.getBean(ErhgoClassificationRepository.class).findAllById(Set.of(classifications))))
                .withLocation(Location.builder().longitude(longitude).latitude(latitude).build())
                .withOccupation(occupation)
                .withRecruiter(organization)
                .buildAndPersist();

        var expectedCriteria = applicationContext.getBean(SourcingCandidatesCriteria.class)
                .occupationId(occupationId)
                .classifications(List.of(classifications))
                .longitude(longitude)
                .latitude(latitude)
                .capacitiesIds(Collections.emptyList())
                .masteryLevelAround(4)
                .typeContractCategory(TypeContractCategory.PERMANENT)
                .excludingRecruitment(recruitment)
                .criteria(Collections.emptyList());

        when(sourcingUserRepository.countCandidates(Mockito.argThat(a -> a != null && !a.excludesNotified()))).thenReturn(50);
        when(sourcingUserRepository.countCandidates(Mockito.argThat(SourcingCandidatesCriteria::excludesNotified))).thenReturn(60);
        performGetAndExpect("/sourcing/recruitment/%d/count-matching-users".formatted(recruitment.getId()),
                "sourcingCandidatesCount",
                true);
        verify(sourcingUserRepository, times(2)).countCandidates(assertArg(c ->
                {
                    assertThat(c.criteria()).isEqualTo(expectedCriteria.criteria());
                    assertThat(c.classifications()).isEqualTo(expectedCriteria.classifications());
                    assertThat(c.latitude()).isEqualTo(latitude);
                    assertThat(c.longitude()).isEqualTo(longitude);
                    assertThat(c.capacitiesIds()).isEmpty();
                    assertThat(c.criteriaForContracts()).containsExactly(CriteriaValue.getValueCodeForTypeContractCategory(TypeContractCategory.PERMANENT));
                    assertThat(c.excludesRecruitment().getId()).isEqualTo(recruitment.getId());
                    assertThat(c.masteryLevelAround()).isEqualTo(4);
                    assertThat(c.masteryLevelMax()).isEqualTo(expectedCriteria.masteryLevelMax());
                    assertThat(c.masteryLevelMin()).isEqualTo(expectedCriteria.masteryLevelMin());
                }
        ));
    }


    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void list_invitation_codes() {
        var creatorId = UUID.randomUUID().toString();
        when(keycloakMockService.getBackOfficeUserFullnameOrEmpty(creatorId)).thenReturn("Patrick Duchmolle");
        applicationContext.getBean(SourcingInvitationMotherObject.class)
                .withGuests(applicationContext.getBean(OrganizationGenerator.class).createRecruiter("E-045"))
                .withHost(applicationContext.getBean(OrganizationGenerator.class).createRecruiter("E-048"))
                .withCreatedBy(creatorId)
                .withDuration(3)
                .withMaxNumberOfGuests(10)
                .buildAndPersist();
        performGetAndExpect("/sourcing/invitation-codes", "listInvitationCodes", true);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.ODAS_ADMIN})
    void save_invitation_code() throws Exception {
        var code = "CODE";
        txHelper.doInTransaction(() -> {
            applicationContext.getBean(OrganizationGenerator.class)
                    .createRecruiter(ORGANIZATION_CODE, "organization", AbstractOrganization.OrganizationType.SOURCING);
        });
        var organization = applicationContext.getBean(AbstractOrganizationRepository.class).findOneByCode(ORGANIZATION_CODE);
        //WHEN
        performPost("/api/odas/sourcing/invitation-code", new SaveSourcingInvitationCodeDTO().code(code).maxNumberOfGuests(3).duration(6).host(organization.getCode()))
                .andExpect(status().isNoContent());
        // THEN
        txHelper.doInTransaction(() -> {
            var invitationCode = applicationContext.getBean(SourcingInvitationRepository.class).findOneByCode("Code").orElse(null);
            assertThat(invitationCode).isNotNull();
            assertThat(invitationCode.getCode()).isEqualTo("CODE");
            assertThat(invitationCode.getMaxNumberOfGuests()).isEqualTo(3);
            assertThat(invitationCode.getDuration()).isEqualTo(6);
            assertThat(invitationCode.getHost()).isEqualTo(organization);
        });
    }


    private UserRepresentation prepareUser() {
        var userRepresentation = new UserRepresentation()
                .setMiscAttributes(Map.of("siret", List.of(SIRET), "fullname", List.of(FULLNAME)))
                .setEmail(EMAIL)
                .setId(USER_ID);

        when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(userRepresentation);
        return userRepresentation;
    }


    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    @ResetDataAfter
    void set_step() throws Exception {
        var organization = applicationContext.getBean(OrganizationGenerator.class).createRecruiter(ORGANIZATION_CODE, AbstractOrganization.OrganizationType.SOURCING);
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class).withRecruiter(organization).buildAndPersist();
        performPut("/sourcing/" + recruitment.getId() + "/step", new UpdateSourcingStepCommandDTO().step(4)).andExpect(status().isNoContent());
        assertThat(recruitmentRepository.findOneByCode(recruitment.getCode()).getSourcingStep()).isEqualTo(4);
    }

    @SneakyThrows
    @Test
    void sendContact_anonymous() {
        var content = "my content";
        var context = "my context";
        var userEmail = "my userEmail";
        var fullname = "my fullname";
        performPost("/sourcing/contact", new SendContactFormCommandDTO()
                .userEmail(userEmail)
                .content(content)
                .context(context)
                .fullname(fullname))
                .andExpect(status().isNoContent());
        verify(mailingListService).sendMailsForTemplate(anySet(), anyLong(), eq(Map.of(
                "context", context,
                "hasAccount", "non",
                "userEmail", userEmail,
                "fullname", fullname,
                "content", content
        )), isNull());
    }

    @SneakyThrows
    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    void sendContact_authenticated() {
        var content = "my content";
        var context = "my context";
        var userEmail = "my userEmail";
        var fullname = "my fullname";
        when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(new UserRepresentation().setMiscAttributes(Map.of("fullname", List.of(fullname))).setEmail(userEmail));

        performPost("/sourcing/contact", new SendContactFormCommandDTO()
                .context(context)
                .content(content)).andExpect(status().isNoContent());
        verify(mailingListService).sendMailsForTemplate(anySet(), anyLong(), eq(Map.of(
                "context", context,
                "hasAccount", "oui",
                "userEmail", userEmail,
                "fullname", fullname,
                "content", content
        )), isNull());
    }

    @Test
    @WithMockKeycloakUser(roles = Role.SOURCING)
    @ResetDataAfter
    void invite_user_in_keycloak_sourcing_email_forbidden() throws Exception {
        var emailGuest = "<EMAIL>";
        var fullnameGuest = "Jean Valjean";
        var organization = applicationContext.getBean(OrganizationGenerator.class).createRecruiter(ORGANIZATION_CODE, AbstractOrganization.OrganizationType.SOURCING);

        var inviteAndCreateNewUserCommand = new InviteAndCreateNewUserCommandDTO()
                .fullname(fullnameGuest)
                .email(emailGuest)
                .organizationCode(organization.getCode())
                .forceEmail(false);

        when(emailVerificationService.verify(emailGuest)).thenReturn(new EmailVerificationResultDTO().setEmailStatus(EmailVerificationResultDTO.EmailStatus.VALID));

        performPost("/sourcing/invite-and-create-new-user/", inviteAndCreateNewUserCommand).andExpect(status().isForbidden());
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    @ResetDataAfter
    void invite_user_in_keycloak_sourcing_email_error(boolean isForced) throws Exception {
        var emailGuest = "<EMAIL>";
        var emailHost = "<EMAIL>";
        var fullnameGuest = "Jean Valjean";
        var expectedSuggestion = "pipo@pipo";
        var fullnameHost = "Super recruteur";

        var organization = applicationContext.getBean(OrganizationGenerator.class).createRecruiter(ORGANIZATION_CODE, AbstractOrganization.OrganizationType.SOURCING);
        when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(new UserRepresentation().setMiscAttributes(Map.of("fullname", List.of(fullnameHost))).setEmail(emailHost));

        var inviteAndCreateNewUserCommand = new InviteAndCreateNewUserCommandDTO()
                .fullname(fullnameGuest)
                .email(emailGuest)
                .organizationCode(organization.getCode())
                .forceEmail(isForced);

        when(emailVerificationService.verify(emailGuest)).thenReturn(new EmailVerificationResultDTO().setEmailStatus(EmailVerificationResultDTO.EmailStatus.INVALID_MAIL).setSuggestion(expectedSuggestion));

        var result = performPost("/sourcing/invite-and-create-new-user/", inviteAndCreateNewUserCommand);
        if (isForced) {
            result.andExpect(status().isNoContent());
        } else {
            result.andExpect(status().isBadRequest())
                    .andExpect(jsonPath("$.cause", Matchers.is("UNKNOWN_EMAIL")))
                    .andExpect(jsonPath("$.suggestion", Matchers.is(expectedSuggestion)))
            ;
        }

        verify(notifier, times(isForced ? 1 : 0)).sendMessage(any(SourcingNewUserMessageDTO.class));
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    @ResetDataAfter
    void invite_user_in_keycloak_sourcing_and_send_email() throws Exception {
        var emailGuest = "<EMAIL>";
        var emailHost = "<EMAIL>";
        var fullnameGuest = "Lea triboulet";
        var fullnameHost = "Super recruteur";
        var organization = applicationContext.getBean(OrganizationGenerator.class).createRecruiter(ORGANIZATION_CODE, AbstractOrganization.OrganizationType.SOURCING);

        when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(new UserRepresentation().setMiscAttributes(Map.of("fullname", List.of(fullnameHost))).setEmail(emailHost));

        var inviteAndCreateNewUserCommand = new InviteAndCreateNewUserCommandDTO()
                .fullname(fullnameGuest)
                .email(emailGuest)
                .organizationCode(organization.getCode())
                .forceEmail(false);
        when(emailVerificationService.verify(emailGuest)).thenReturn(new EmailVerificationResultDTO().setEmailStatus(EmailVerificationResultDTO.EmailStatus.VALID));

        performPost("/sourcing/invite-and-create-new-user/", inviteAndCreateNewUserCommand)
                .andExpect(status().isNoContent());

        Mockito.verify(mailingListService).sendMailsForTemplate(ArgumentMatchers.argThat(set -> set.contains(emailGuest)),
                ArgumentMatchers.eq(220L),
                ArgumentMatchers.argThat(map ->
                        ((Map) map).get("SENDER_FULLNAME").equals(fullnameHost)
                                && ((Map) map).get("LOGIN").equals(emailGuest)
                                && ((Map) map).get("PASSWORD") != null
                                && ((Map) map).get("ORGANIZATION_TITLE").equals(organization.getTitle())
                                && ((Map) map).get("SOURCING_URL").equals("https://sourcing.erhgo.fr")
                ), isNull());
        verify(notifier).sendMessage(any(SourcingNewUserMessageDTO.class));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.SOURCING, ORGANIZATION_CODE})
    @ResetDataAfter
    void list_users_by_organization() {
        applicationContext.getBean(OrganizationGenerator.class).createRecruiter(ORGANIZATION_CODE, AbstractOrganization.OrganizationType.SOURCING);

        var id1 = "464fa15d-0f9d-48c9-a19a-58c1b576ce63";
        var id2 = "f9e3db22-c36e-4651-9090-9999bcc25ffc";

        var email1 = "<EMAIL>";
        var email2 = "<EMAIL>";

        var fullname1 = "Lea Triboulet";
        var fullname2 = "Jean Dupont";

        var user1 = new UserRepresentation().setId(id1).setMiscAttributes(Map.of(UserRepresentation.FULLNAME_ATTRIBUTE, List.of(fullname1))).setEmail(email1);
        var user2 = new UserRepresentation().setId(id2).setMiscAttributes(Map.of(UserRepresentation.FULLNAME_ATTRIBUTE, List.of(fullname2), UserRepresentation.LAST_LOGIN_TIMESTAMP_ATTRIBUTE, List.of("5555555555555"))).setEmail(email2);

        when(sourcingKeycloakService.getSourcingUsersForGroup(ORGANIZATION_CODE)).thenReturn(List.of(user1, user2));

        performGetAndExpect("/sourcing/users/%s".formatted(ORGANIZATION_CODE), "sourcingListUsers", false);
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.SOURCING, "S-00"})
    @ResetDataAfter
    void list_users_by_organization_ils_forbidden() throws Exception {
        applicationContext.getBean(OrganizationGenerator.class).createRecruiter(ORGANIZATION_CODE, AbstractOrganization.OrganizationType.SOURCING);
        mvc.perform(get(realUrl("/sourcing/users/%s".formatted(ORGANIZATION_CODE)))).andExpect(status().isForbidden());
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    @ResetDataAfter
    void send_correct_status_when_suggestion_is_null() throws Exception {
        var emailGuest = "<EMAIL>";
        var fullnameGuest = "Lea triboulet";
        var emailHost = "<EMAIL>";
        var fullnameHost = "Super recruteur";

        var organization = applicationContext.getBean(OrganizationGenerator.class).createRecruiter(ORGANIZATION_CODE, AbstractOrganization.OrganizationType.SOURCING);

        when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(new UserRepresentation().setMiscAttributes(Map.of("fullname", List.of(fullnameHost))).setEmail(emailHost));

        when(emailVerificationService.verify(emailGuest)).thenReturn(new EmailVerificationResultDTO().setSuggestion(null).setEmailStatus(EmailVerificationResultDTO.EmailStatus.INVALID_MAIL));
        var inviteAndCreateNewUserCommand = new InviteAndCreateNewUserCommandDTO()
                .fullname(fullnameGuest)
                .email(emailGuest)
                .organizationCode(organization.getCode())
                .forceEmail(false);

        performPost("/sourcing/invite-and-create-new-user/", inviteAndCreateNewUserCommand)
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.cause", Matchers.is("UNKNOWN_EMAIL")))
                .andExpect(jsonPath("$.suggestion", Matchers.is("")));
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    @ResetDataAfter
    void create_or_update_recruitment_with_form_informations(boolean recruitmentAlreadyExists) throws Exception {
        var organizationDescription = "McDo!!";
        var erhgoOccupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withCapacities(applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-4254"))
                .buildAndPersist();
        var recruiter = applicationContext.getBean(OrganizationGenerator.class).createRecruiter(ORGANIZATION_CODE, AbstractOrganization.OrganizationType.SOURCING);
        when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(new UserRepresentation().setId(USER_ID).setGroups(List.of(ORGANIZATION_CODE)));

        Long recruitmentId = null;
        if (recruitmentAlreadyExists) {
            var recruitment = applicationContext.getBean(RecruitmentMotherObject.class).withRecruiter(recruiter).withOccupation(erhgoOccupation).buildAndPersist();
            recruitmentId = recruitment.getId();
        }
        var criteria1 = applicationContext.getBean(CriteriaMotherObject.class).buildAndPersist();
        var criteria2 = applicationContext.getBean(CriteriaMotherObject.class).buildAndPersist();
        var typeContractCriteria = applicationContext.getBean(CriteriaMotherObject.class).withTypeContractCriteria().buildAndPersist();
        var workingTimeCriteria = applicationContext.getBean(CriteriaMotherObject.class).withWorkingTimeCriteria().buildAndPersist();

        var criteriaValuesAsString = List.of(
                criteria1.getCriteriaValues().get(0).getCode(),
                criteria2.getCriteriaValues().get(0).getCode()
        );

        var criteriaValues = Set.of(
                criteria1.getCriteriaValues().get(0),
                criteria2.getCriteriaValues().get(0),
                typeContractCriteria.getCriteriaValues()
                        .stream()
                        .filter(c -> Objects.equals(c.getCode(), CriteriaValue.getValueCodeForTypeContractCategory(TypeContractCategory.PERMANENT)))
                        .findFirst().orElseThrow(),
                workingTimeCriteria.getCriteriaValues()
                        .stream()
                        .filter(c -> Objects.equals(c.getCode(), CriteriaValue.getValueCodeForTypeWorkingTime(TypeWorkingTime.FULL_TIME)))
                        .findFirst().orElseThrow()
        );

        var parisLocation = new LocationDTO().city("Paris").latitude(48.85f).longitude(2.35f);
        var title = "bim";
        var createRecruitmentCommand = new CreateOrUpdateFullRecruitmentCommandDTO()
                .recruitmentId(recruitmentId)
                .description("Description du poste")
                .baseSalary(10000)
                .maxSalary(50000)
                .modularWorkingTime(true)
                .typeContractCategory(TypeContractCategoryDTO.PERMANENT)
                .workingTimeType(WorkingTimeDTO.FULL_TIME)
                .workingWeeklyTime(35)
                .jobLocation(parisLocation)
                .jobOccupationId(erhgoOccupation.getId())
                .criteriaValues(criteriaValuesAsString)
                .organizationDescription(organizationDescription)
                .title(title);

        recruitmentId =
                Long.parseLong(performPost("/sourcing/recruitment/form-submission", createRecruitmentCommand)
                        .andExpect(status().isOk())
                        .andReturn().getResponse().getContentAsString());
        var finalId = recruitmentId;
        txHelper.doInTransaction(() -> {
            var recruitmentInDb = recruitmentRepository.findById(finalId).orElseThrow();
            assertThat(recruitmentInDb.getDescription()).isEqualTo("Description du poste");
            assertThat(recruitmentInDb.getBaseSalary()).isEqualTo(10000);
            assertThat(recruitmentInDb.getMaxSalary()).isEqualTo(50000);
            assertThat(recruitmentInDb.getModularWorkingTime()).isTrue();
            assertThat(recruitmentInDb.getTypeContract()).isEqualTo(ContractType.CDI);
            assertThat(recruitmentInDb.getLocation()).isEqualTo(Location.buildLocationFromDTO(parisLocation));
            assertThat(recruitmentInDb.getErhgoOccupation()).isEqualTo(erhgoOccupation);
            assertThat(recruitmentInDb.getWorkingWeeklyTime()).isEqualTo(35);
            assertThat(recruitmentInDb.getJobTitle()).isEqualTo(title);
            assertThat(recruitmentInDb.getJob().getCriteriaValues()).containsExactlyInAnyOrderElementsOf(criteriaValues);
            assertThat(recruitmentInDb.getOrganizationOrRecruiterDescription()).isEqualTo(organizationDescription);
        });
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    @ResetDataAfter
    void create_recruitment_no_occupation() throws Exception {
        var title = "bim";
        var erhgoOccupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withCapacities(applicationContext.getBean(CapacityGenerator.class).createCapacity("CA-4254"))
                .buildAndPersist();
        when(occupationForLabelGenerationService.createOrUpdateOccupation(title, OccupationCreationSourceType.FROM_SOURCING)).thenReturn(new OccupationForLabelGenerationResult(erhgoOccupation.getId(), new ArrayList<>(), false));
        applicationContext.getBean(OrganizationGenerator.class).createRecruiter(ORGANIZATION_CODE, AbstractOrganization.OrganizationType.SOURCING);
        when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(new UserRepresentation().setId(USER_ID).setGroups(List.of(ORGANIZATION_CODE)));
        applicationContext.getBean(CriteriaMotherObject.class).withTypeContractCriteria().buildAndPersist();
        applicationContext.getBean(CriteriaMotherObject.class).withWorkingTimeCriteria().buildAndPersist();
        var parisLocation = new LocationDTO().city("Paris").latitude(48.85f).longitude(2.35f);
        var createRecruitmentCommand = new CreateOrUpdateFullRecruitmentCommandDTO()
                .description("Description du poste")
                .baseSalary(10000)
                .maxSalary(50000)
                .modularWorkingTime(true)
                .typeContractCategory(TypeContractCategoryDTO.PERMANENT)
                .workingTimeType(WorkingTimeDTO.FULL_TIME)
                .workingWeeklyTime(35)
                .jobLocation(parisLocation)
                .organizationDescription("organizationDescription")
                .title(title);

        var recruitmentId =
                Long.parseLong(performPost("/sourcing/recruitment/form-submission", createRecruitmentCommand)
                        .andExpect(status().isOk())
                        .andReturn().getResponse().getContentAsString());
        txHelper.doInTransaction(() -> {
            var recruitmentInDb = recruitmentRepository.findById(recruitmentId).orElseThrow();
            assertThat(recruitmentInDb.getErhgoOccupation()).isEqualTo(erhgoOccupation);
            assertThat(recruitmentInDb.getJobTitle()).isEqualTo(title);
        });
    }
}
