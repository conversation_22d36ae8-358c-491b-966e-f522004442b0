package com.erhgo.controller.sourcing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.CandidatureEmailRefusalState;
import com.erhgo.domain.candidature.job.CandidatureRecruitmentState;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.BehaviorCategory;
import com.erhgo.domain.enums.CandidatureState;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.enums.Situation;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.job.SpontaneousCandidatureMotherObject;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.sourcing.SourcingPreferences;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.SourcingPreferencesMotherObject;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.generators.BehaviorGenerator;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.JobActivityLabelGenerator;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.openapi.dto.SourcingCandidatureSortDTO;
import com.erhgo.openapi.dto.SourcingCandidatureStateDTO;
import com.erhgo.openapi.dto.UpdateSourcingCandidatureStateCommandDTO;
import com.erhgo.repositories.JobRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.SourcingUserRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailingListService;
import com.erhgo.services.sourcing.SourcingMailingService;
import com.erhgo.services.sourcing.SourcingScheduler;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.data.TemporalUnitWithinOffset;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.sql.Date;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
class SourcingCandidatureControllerTest extends AbstractIntegrationTest {
    private static final String ORGANIZATION_CODE = "S-42";
    private static SourcingPreferences.MailFrequency DEFAULT_FREQUENCY;
    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    RecruitmentCandidatureRepository recruitmentCandidatureRepository;

    @Autowired
    CapacityGenerator capacityGenerator;

    @MockitoBean
    MailingListService mailingListService;

    @Autowired
    SourcingMailingService sourcingMailingListService;

    @Autowired
    SourcingScheduler sourcingScheduler;

    @MockitoBean
    SourcingUserRepository sourcingUserRepository;

    @MockitoBean
    private KeycloakMockService keycloakMockService;

    final String USER_ID = "81edc2bc-3633-4787-bb04-e9af5798bd78";
    final String USER_ID_2 = "81edc2bc-3633-4787-bb04-e9af5798bd72";
    final String USER_ID_3 = "81edc2bc-3633-4787-bb04-e9af5798bd73";
    final String USER_ID_4 = "81edc2bc-3633-4787-bb04-e9af5798bd74";

    private Recruitment prepareMultipleCandidaturesOnEveryState(Recruiter recruiterParam, boolean ignoreUserId) {
        var allStates = new GlobalCandidatureState[]{
                GlobalCandidatureState.NOT_TREATED_BY_ERHGO,
                GlobalCandidatureState.NEW,
                GlobalCandidatureState.STAND_BY,
                GlobalCandidatureState.INTERNAL_POSITION,
                GlobalCandidatureState.INTRODUCE_TO_CLIENT,
                GlobalCandidatureState.SUMMARY_SHEET_SENT,
                GlobalCandidatureState.ON_RECRUITMENT_CLIENT,
                GlobalCandidatureState.RECRUITMENT_VALIDATED,
                GlobalCandidatureState.REFUSED_MEETING_CLIENT,
                GlobalCandidatureState.REFUSED_ON_CALL,
                GlobalCandidatureState.REFUSED_BY_CLIENT_WITH_SHEETS,
                GlobalCandidatureState.REFUSED_MEETING_CLIENT,
                GlobalCandidatureState.NOT_FINALIZED,
                GlobalCandidatureState.MISSING_PREREQUISITE,
        };

        var recruiter = recruiterParam == null ?
                applicationContext.getBean(OrganizationGenerator.class).createRecruiter(ORGANIZATION_CODE) : recruiterParam;


        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withRecruiter(recruiter)
                .withTitle("Recruitment title")
                .withState(RecruitmentState.PUBLISHED)
                .withPublicationDate(new java.util.Date(42_000_000_000L))
                .withPublicationEndDate(OffsetDateTime.of(1942, 01, 01, 01, 01, 01, 0, ZoneOffset.UTC))
                .withJobTitle("Job title")
                .withSalaries(19000, 30000)
                .withLocation(Location.builder().citycode("59000").city("Lille").longitude(42f).latitude(24f).postcode("59123").departmentCode("59").regionName("Là-haut").radiusInKm(30).build())
                .withSourcingNotifiedUsersIds(USER_ID, USER_ID_2, "404")
                .buildAndPersist();

        IntStream.rangeClosed(1, allStates.length).forEach(i ->
                buildCandidaturesForState(i, allStates[i - 1], recruitment, ignoreUserId)
        );

        applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withRecruitment(recruitment)
                .withIsArchived(true)
                .buildAndPersist();

        if (recruiterParam == null) {
            var otherRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                    .withRecruiter(recruiter)
                    .withJobTitle("Other recruitment")
                    .withSalaries(19000, 30000)
                    .withLocation(Location.builder().citycode("59000").city("Lille").longitude(42f).latitude(24f).postcode("59123").departmentCode("59").regionName("Là-haut").radiusInKm(30).build())
                    .withState(RecruitmentState.PUBLISHED)
                    .buildAndPersist();
            buildCandidaturesForState(6, GlobalCandidatureState.NOT_TREATED_BY_ERHGO, otherRecruitment, ignoreUserId);
        }

        return recruitment;
    }

    private void buildCandidaturesForState(
            int nbCandidatures,
            GlobalCandidatureState state,
            Recruitment recruitment,
            boolean ignoreUserId) {
        IntStream.rangeClosed(1, nbCandidatures).forEach(i -> {
            var uniqueIndex = i * 100 + 1_000 * nbCandidatures + 100_000 * state.ordinal();
            var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                    .withRecruitment(recruitment)
                    .withSituation(Situation.values()[i % Situation.values().length])
                    .withAnswer("Answer of " + uniqueIndex)
                    .withColor((i % 20) + "")
                    .generated(i == 1 && state == GlobalCandidatureState.NEW)
                    .withAnonymousCode("C " + uniqueIndex)
                    .withPhone("01234567" + uniqueIndex)
                    .withFullnameComputingEmail("prénom " + uniqueIndex, "nom " + uniqueIndex)
                    .withUserId(ignoreUserId ? null : "user-id-%d".formatted(uniqueIndex))
                    .withLastActionDate(OffsetDateTime.ofInstant(Instant.ofEpochMilli(420_000_000L * i - (uniqueIndex * 1_000L)), ZoneId.systemDefault()))
                    .withState(state)
                    .withSubmissionDate(OffsetDateTime.ofInstant(Instant.ofEpochMilli(420_000_000L * i + (uniqueIndex * 1_000L)), ZoneId.systemDefault()));
            if (i % 3 == 1) {
                IntStream.rangeClosed(0, i).forEach(j -> candidature.withNoteAtDate("Note nb " + j + " for " + uniqueIndex, LocalDateTime.of(2010, 12, j + 1, 10, 42)));
            }
            candidature.buildAndPersist();
        });
    }

    @SneakyThrows
    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, ORGANIZATION_CODE})
    @ResetDataAfter
    void getCandidaturesPage_noStates_retrieves_only_finalized() {
        var recruitment = prepareMultipleCandidaturesOnEveryState(null, false);
        performGetAndExpect("/sourcing/candidatures?recruitmentId=%d&page=0&size=70".formatted(recruitment.getId()), "allSourcingCandidaturesPage", true);
    }

    @SneakyThrows
    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, ORGANIZATION_CODE})
    @ResetDataAfter
    void getCandidaturesPage_searchQuery() {
        var recruitment = prepareMultipleCandidaturesOnEveryState(null, false);
        performGetAndExpect("/sourcing/candidatures?recruitmentId=%d&page=0&size=70&searchQuery=rénôm 40".formatted(recruitment.getId()), "sourcingCandidaturesPagesFilteredByQuery", true);
    }


    @SneakyThrows
    @ParameterizedTest
    @EnumSource(SourcingCandidatureSortDTO.class)
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, ORGANIZATION_CODE})
    @ResetDataAfter
    void getCandidaturesPage_sort_retrieves_only_finalized(SourcingCandidatureSortDTO sort) {
        var recruitment = prepareMultipleCandidaturesOnEveryState(null, false);
        performGetAndExpect("/sourcing/candidatures?recruitmentId=%d&page=0&direction=ASC&size=10&sourcingCandidatureSort=%s".formatted(recruitment.getId(), sort.getValue()), "allSourcingCandidaturesPageSortBy_%s_%s".formatted(sort.getValue(), "ASC"), true);
        performGetAndExpect("/sourcing/candidatures?recruitmentId=%d&page=0&direction=DESC&size=10&sourcingCandidatureSort=%s".formatted(recruitment.getId(), sort.getValue()), "allSourcingCandidaturesPageSortBy_%s_%s".formatted(sort.getValue(), "DESC"), true);
    }

    @SneakyThrows
    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, ORGANIZATION_CODE})
    @ResetDataAfter
    void getCandidaturesPage_allStates_retrieves_only_finalized() {
        var recruitment = prepareMultipleCandidaturesOnEveryState(null, false);
        var allStates = Stream.of(SourcingCandidatureStateDTO.values()).map(Enum::name).map("&candidatureState=%s"::formatted).collect(Collectors.joining(""));
        performGetAndExpect("/sourcing/candidatures?recruitmentId=%d&page=0&size=70%s".formatted(recruitment.getId(), allStates), "allSourcingCandidaturesPage", true);
    }

    @SneakyThrows
    @ParameterizedTest
    @CsvSource({"DISMISS,42", "FAVORITE,15", "NEW,6", "CONTACTED,11", "TO_CONTACT,4"})
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, ORGANIZATION_CODE})
    @ResetDataAfter
    void getCandidaturesPage_per_state(SourcingCandidatureStateDTO state, int expectedNb) {
        var recruitment = prepareMultipleCandidaturesOnEveryState(null, false);
        mvc.perform(get(realUrl("/sourcing/candidatures?recruitmentId=%d&page=0&size=42&candidatureState=%s".formatted(recruitment.getId(), state.name()))))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.totalNumberOfElements", Matchers.is(expectedNb)));
    }

    @SneakyThrows
    @ParameterizedTest
    @CsvSource(value = {
            "NOT_TREATED_BY_ERHGO,DISMISS,REFUSED_ON_CALL,REFUSE_CANDIDATURE",
            "NEW,DISMISS,REFUSED_ON_CALL,REFUSE_CANDIDATURE",
            "INTERNAL_POSITION,DISMISS,REFUSED_BY_CLIENT_WITH_SHEETS,REFUSE_CANDIDATURE",
            "SUMMARY_SHEET_SENT,DISMISS,REFUSED_BY_CLIENT_WITH_SHEETS,REFUSE_CANDIDATURE",
            "ON_RECRUITMENT_CLIENT,DISMISS,REFUSED_BY_CLIENT_WITH_SHEETS,REFUSE_CANDIDATURE",
            "NOT_TREATED_BY_ERHGO,TO_CONTACT,INTERNAL_POSITION,MEET_LATER_CANDIDATURE",
            "NOT_TREATED_BY_ERHGO,TO_CONTACT,INTERNAL_POSITION,MEET_LATER_CANDIDATURE",
            "INTERNAL_POSITION,CONTACTED,INTRODUCE_TO_CLIENT,MEET_CANDIDATURE",
            "INTRODUCE_TO_CLIENT,FAVORITE,ON_RECRUITMENT_CLIENT,FAV_CANDIDATURE",
            "REFUSED_BY_CLIENT_WITH_SHEETS,TO_CONTACT,INTERNAL_POSITION,MEET_LATER_CANDIDATURE",
    }, nullValues = "null")
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, Role.SOURCING, ORGANIZATION_CODE})
    @ResetDataAfter
    void update_candidature_state(GlobalCandidatureState initialState, SourcingCandidatureStateDTO nextState, GlobalCandidatureState expectedFinalState, Recruitment.ProcessingType expectedLastProcessingType) {

        var recruiter = applicationContext.getBean(OrganizationGenerator.class).createRecruiter(ORGANIZATION_CODE);
        var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withRecruitment(applicationContext.getBean(RecruitmentMotherObject.class)
                        .withRecruiter(recruiter).buildAndPersist())
                .generated(false)
                .withModifiedByUser(true)
                .withFullnameComputingEmail("ln", "fn")
                .withPhone("0123")
                .withState(initialState)
                .withUserLocation(Location.builder().city("po").postcode("56000").build())
                .buildAndPersist();
        var expectedMatcher = expectedFinalState.isAnonymous() ? Matchers.is(Matchers.emptyOrNullString()) : Matchers.is(Matchers.not(Matchers.emptyOrNullString()));
        when(mailingListService.sendMailsForTemplate(anySet(), anyLong(), any(), isNull())).thenReturn(CompletableFuture.completedFuture(Set.of()));
        var actions = performPut("/sourcing/%d/state".formatted(candidature.getId()), new UpdateSourcingCandidatureStateCommandDTO().nextState(nextState))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.firstName", expectedMatcher))
                .andExpect(jsonPath("$.lastName", expectedMatcher))
                .andExpect(jsonPath("$.phone", expectedMatcher))
                .andExpect(jsonPath("$.email", expectedMatcher));
        if (expectedFinalState.isAnonymous()) {
            actions.andExpect(jsonPath("$.location", Matchers.is(Matchers.nullValue())));
        } else {
            actions.andExpect(jsonPath("$.location.city", expectedMatcher))
                    .andExpect(jsonPath("$.location.postcode", expectedMatcher));
        }

        var freshCandidature = recruitmentCandidatureRepository.findById(candidature.getId()).orElseThrow();
        assertThat(freshCandidature.getGlobalCandidatureState()).isEqualTo(expectedFinalState);
        assertThat(freshCandidature.getRefusalDate()).matches(d -> nextState == SourcingCandidatureStateDTO.DISMISS == (d != null));
        assertThat(freshCandidature.getCandidatureRecruitmentState()).isEqualTo(expectedFinalState.getCandidatureRecruitmentState());
        assertThat(freshCandidature.getRecruitment().getLastProcessingType()).isEqualTo(expectedLastProcessingType);
        assertThat(freshCandidature.getRecruitment().getLastProcessingDate()).isCloseToUtcNow(new TemporalUnitWithinOffset(1, ChronoUnit.DAYS));

        verifyNoInteractions(mailingListService);
        if (nextState == SourcingCandidatureStateDTO.DISMISS) {
            assertThat(freshCandidature.getCandidatureRefusalState().getEmailSent()).isEqualTo(CandidatureEmailRefusalState.WAITING);
            assertThat(freshCandidature.getCandidatureRecruitmentState()).isEqualTo(CandidatureRecruitmentState.WAITING);
        } else {
            assertThat(freshCandidature.getCandidatureRefusalState()).isNull();
        }
    }

    @ParameterizedTest
    @ValueSource(booleans = {false, true})
    @SneakyThrows
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, Role.SOURCING, ORGANIZATION_CODE})
    @ResetDataAfter
    void update_candidature_state_dismiss(boolean withEmail) {
        var recruiter = applicationContext.getBean(OrganizationGenerator.class).createRecruiter(ORGANIZATION_CODE);

        var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withRecruitment(applicationContext.getBean(RecruitmentMotherObject.class)
                        .withRecruiter(recruiter)
                        .withLocation(Location.builder().citycode("59000").city("Lille").longitude(42f).latitude(24f).postcode("59123").departmentCode("59").regionName("Là-haut").radiusInKm(30).build()
                        ).buildAndPersist())
                .withFullnameComputingEmail("ln", "fn")
                .withPhone("0123")
                .generated(!withEmail)
                .withModifiedByUser(withEmail)
                .withState(GlobalCandidatureState.INTRODUCE_TO_CLIENT)
                .buildAndPersist();
        var parameters = Map.of("JOB", candidature.getJobTitle(),
                "LOCATION", "%s (%s)".formatted(candidature.getRecruitment().getLocation().getCity(), candidature.getRecruitment().getLocation().getPostcode()),
                "RECRUITER", candidature.getOrganizationName());
        when(mailingListService.sendMailsForTemplate(anySet(), anyLong(), any(), isNull())).thenReturn(CompletableFuture.completedFuture(new HashSet<>()));
        performPut("/sourcing/%d/state".formatted(candidature.getId()), new UpdateSourcingCandidatureStateCommandDTO().nextState(SourcingCandidatureStateDTO.DISMISS))
                .andExpect(status().isOk());
        assertThat(recruitmentCandidatureRepository.findById(candidature.getId()).orElseThrow().getGlobalCandidatureState()).isEqualTo(GlobalCandidatureState.REFUSED_BY_CLIENT_WITH_SHEETS);
        assertThat(recruitmentCandidatureRepository.findById(candidature.getId()).orElseThrow().getCandidatureRefusalState().getEmailSent()).isEqualTo(withEmail ? CandidatureEmailRefusalState.WAITING : CandidatureEmailRefusalState.NONE);
        verifyNoInteractions(mailingListService);
    }


    @SneakyThrows
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, ORGANIZATION_CODE})
    @ResetDataAfter
    @ParameterizedTest
    @ValueSource(booleans = {false, true})
    void getCandidatureDetail(boolean withPreviousCandidatures) {
        var candidatureId = new AtomicLong();
        var u1 = new UserRepresentation();
        u1.setMiscAttributes(Map.of("fullname", List.of("Jean Valjean 1")));
        var u2 = new UserRepresentation();
        u2.setMiscAttributes(Map.of("fullname", List.of("Jean Valjean 2")));
        when(sourcingKeycloakService.getSourcingUserWithGroups(ArgumentMatchers.anyString()))
                .thenReturn(u1)
                .thenReturn(u2)
                .thenReturn(null);
        Mockito.when(keycloakMockService.getFrontOfficeUserProfile(ArgumentMatchers.anyString()))
                .thenReturn(Optional.of(u1));
        txHelper.doInTransaction(() -> {

            var recruiter = applicationContext.getBean(OrganizationGenerator.class).createRecruiter(ORGANIZATION_CODE);
            var requiredAct1 = applicationContext.getBean(JobActivityLabelGenerator.class).createActivity(UUID.randomUUID(), "Activité1");
            var requiredAct2 = applicationContext.getBean(JobActivityLabelGenerator.class).createActivity(UUID.randomUUID(), "Activité 2");
            var notRequiredAct1 = applicationContext.getBean(JobActivityLabelGenerator.class).createActivity(UUID.randomUUID(), "Nope");
            var xp1Occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                    .withActivity(requiredAct1, true)
                    .withActivity(requiredAct2, true)
                    .withActivity(notRequiredAct1, false)
                    .withTitle("Métier 1")
                    .buildAndPersist();
            var xp2Occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                    .withTitle("Métier 2")
                    .buildAndPersist();
            var xp3Occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                    .withTitle("Métier 3")
                    .buildAndPersist();


            var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                    .withExperience(xp1Occupation, 3)
                    .withExperience(xp2Occupation, 3)
                    .withExperience(xp3Occupation, 9)
                    .withUserId("e89768ad-0bc2-4359-b726-3c1f9bfadc3f")
                    .withLastConnectionDate(LocalDateTime.of(2023, 7, 7, 5, 5))
                    .withAllTypeWorkingTimes(withPreviousCandidatures)
                    .withAllContractTypes(withPreviousCandidatures)
                    .withDrivingLicence(withPreviousCandidatures)
                    .buildAndPersist();
            var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                    .generated(true)
                    .withAnonymousCode("UB40")
                    .withRecruitment(
                            applicationContext.getBean(RecruitmentMotherObject.class)
                                    .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class)
                                            .buildAndPersist())
                                    .withRecruiter(recruiter)
                                    .withJobTitle("Un bon métier")
                                    .withRecruitmentProfileQuestion("Ça va ?")
                                    .withPublicationDate(new java.util.Date(58_000_000_000L))
                                    .withLocation(Location.builder().city("Là").postcode("59012").build())
                                    .withSalaries(200, 500)
                                    .buildAndPersist()
                    )
                    .withColor("gris")
                    .withUserProfile(userProfile)
                    .withNoteAtDate("Une note d'un utilisateur lié à l'organisation", LocalDateTime.of(2001, 1, 1, 0, 0, 0, 0))
                    .withNoteAtDate(
                            """
                                    ATTENTION : ceci est une note d'un utilisateur qui N'EST PAS rattaché à
                                    l'organisation, cf sourcingKeycloakService.getSourcingUserWithGroups en début de test...
                                    Le tri par date fait que ce commentaire, le plus ancien, n'a pas d'utilisateur associé et
                                    est out filtered dans le service.
                                    """
                            , LocalDateTime.of(2000, 1, 1, 0, 0, 0, 0))
                    .withNoteAtDate("Une note d'un autre utilisateur lié à l'organisation", LocalDateTime.of(2022, 1, 1, 0, 0, 0, 0))
                    .buildAndPersist();
            candidatureId.set(candidature.getId());

            var behavior = applicationContext.getBean(BehaviorGenerator.class).createBehavior("A", "Be", "The behavior", BehaviorCategory.CONSTANCY, 1);
            userProfile.updateBehaviors(Set.of(behavior));

            if (withPreviousCandidatures) createCandidatures(userProfile, candidature.getRecruiter());
            createCandidatures(applicationContext.getBean(UserProfileMotherObject.class).buildAndPersist(), candidature.getRecruiter());

        });

        performGetAndExpect("/sourcing/recruitment-candidature/%d/detail".formatted(candidatureId.get()), withPreviousCandidatures ? "sourcingCandidatureDetailPreviousCandidatures" : "sourcingCandidatureDetail", withPreviousCandidatures);
    }

    private void createCandidatures(UserProfile userProfile, Recruiter recruiter) {
        // 2 candidatures sort by date, 1 draft candidature, 1 candidature for other recruiter ignored & 1 archived candidature
        // 2 spontaneous candidature - 1 on recruiter, 1 for other
        applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userProfile)
                .withCandidatureState(CandidatureState.VALIDATED)
                .withRecruiter(recruiter)
                .withJobTitle("Vieille candidature")
                .withSubmissionDate(OffsetDateTime.of(LocalDateTime.of(2000, 10, 10, 10, 10), ZoneOffset.UTC))
                .withState(GlobalCandidatureState.INTRODUCE_TO_CLIENT)
                .buildAndPersist();

        applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userProfile)
                .withCandidatureState(CandidatureState.VALIDATED)
                .withRecruiter(recruiter)
                .withJobTitle("Nouvelle candidature")
                .withSubmissionDate(OffsetDateTime.of(LocalDateTime.of(2010, 10, 10, 10, 10), ZoneOffset.UTC))
                .withState(GlobalCandidatureState.INTRODUCE_TO_CLIENT)
                .buildAndPersist();

        applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userProfile)
                .withCandidatureState(CandidatureState.STARTED)
                .withRecruiter(recruiter)
                .withSubmissionDate(OffsetDateTime.of(LocalDateTime.of(2020, 10, 10, 10, 10), ZoneOffset.UTC))
                .withState(GlobalCandidatureState.NOT_FINALIZED)
                .buildAndPersist();

        applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userProfile)
                .withCandidatureState(CandidatureState.VALIDATED)
                .withState(GlobalCandidatureState.INTRODUCE_TO_CLIENT)
                .buildAndPersist();

        applicationContext.getBean(SpontaneousCandidatureMotherObject.class)
                .withUserProfile(userProfile)
                .withState(GlobalCandidatureState.INTRODUCE_TO_CLIENT)
                .buildAndPersist();

        applicationContext.getBean(SpontaneousCandidatureMotherObject.class)
                .withRecruiter(recruiter)
                .withSubmissionDate(OffsetDateTime.of(LocalDateTime.of(2017, 10, 10, 10, 10), ZoneOffset.UTC))
                .withUserProfile(userProfile)
                .withState(GlobalCandidatureState.INTRODUCE_TO_CLIENT)
                .buildAndPersist();

        applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userProfile)
                .withRecruiter(recruiter)
                .withJobTitle("Candidature archivée")
                .withSubmissionDate(OffsetDateTime.of(LocalDateTime.of(2000, 10, 10, 10, 10), ZoneOffset.UTC))
                .withState(GlobalCandidatureState.REFUSED_BY_CLIENT_WITH_SHEETS)
                .withIsArchived(true)
                .buildAndPersist();

    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING})
    @ResetDataAfter
    void send_mail_to_recruiters_to_remind_of_open_recruitments() {
        // save daily pref
        var email1 = "<EMAIL>";
        var email2 = "<EMAIL>";
        var notifiedImmediatelyUser = "<EMAIL>";
        var notifiedOnlyForSpontaneous = "<EMAIL>";
        var fullname1 = "super recruiter 1";
        var fullname2 = "super recruiter 2";
        var fullname4 = "super recruiter 4";
        var recruiter = applicationContext.getBean(OrganizationGenerator.class).createRecruiter(ORGANIZATION_CODE, AbstractOrganization.OrganizationType.SOURCING);
        // Spontaneous candidature on every state
        Stream.of(GlobalCandidatureState.values()).forEach(s -> applicationContext.getBean(SpontaneousCandidatureMotherObject.class).withRecruiter(recruiter).withState(s).buildAndPersist());

        // recruitment SELECTION + candidature
        var selectionRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.SELECTION)
                .withSourcingNotifiedUsersIds(USER_ID_2, USER_ID_3)
                .withRecruiter(recruiter)
                .withLocation(Location.builder().city("Bilm").build())
                .buildAndPersist();
        var selectionCandidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withState(GlobalCandidatureState.NOT_TREATED_BY_ERHGO)
                .withRecruitment(selectionRecruitment)
                .buildAndPersist();

        var recruitmentWithMultipleCandidatures = prepareMultipleCandidaturesOnEveryState(recruiter, true);
        var recruitmentWithMultipleCandidatures2 = prepareMultipleCandidaturesOnEveryState(recruiter, true);

        // Non published recruitment
        Stream.of(new RecruitmentState[]{RecruitmentState.DRAFT, RecruitmentState.CLOSED, RecruitmentState.UNPUBLISHED})
                .forEach(s -> {
                    var notPublishedRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                            .withState(s)
                            .withRecruiter(recruiter)
                            .withSourcingNotifiedUsersIds(USER_ID, USER_ID_2, USER_ID_3)
                            .buildAndPersist();
                    applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                            .withRecruitment(notPublishedRecruitment)
                            .withState(GlobalCandidatureState.NEW)
                            .buildAndPersist();
                });

        // Recruitment without candidature on same orga
        var noCandidatureRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withSourcingNotifiedUsersIds(USER_ID_3, USER_ID_2, USER_ID)
                .withRecruiter(recruiter)
                .buildAndPersist();

        // Recruitment without candidature on other sourcing orga => no email
        var otherOrgaCode = "S-9898";
        var otherOrgaCodeUserId = "9898";
        var otherRecruiterRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withSourcingNotifiedUsersIds(otherOrgaCodeUserId)
                .withRecruiter(applicationContext.getBean(OrganizationGenerator.class).createRecruiter(otherOrgaCode, AbstractOrganization.OrganizationType.SOURCING))
                .buildAndPersist();

        when(sourcingKeycloakService.getSourcingUser(otherOrgaCodeUserId)).thenReturn(
                Optional.of(new UserRepresentation().setEmail("nop@nop").setMiscAttributes(Map.of("fullname", List.of("nope"))))
        );

        // Recruitment with only not NEW  candidatures on other sourcing orga
        var otherOrgaCode2 = "S-98982";
        var otherOrgaCode2UserId = "98982";
        var otherRecruiterRecruitment2 = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withLocation(Location.builder().city("Bilm").build())
                .withRecruiter(applicationContext.getBean(OrganizationGenerator.class).createRecruiter(otherOrgaCode2, AbstractOrganization.OrganizationType.SOURCING))
                .withSourcingNotifiedUsersIds(otherOrgaCode2UserId)
                .buildAndPersist();
        applicationContext.getBean(RecruitmentCandidatureMotherObject.class).withState(GlobalCandidatureState.INTRODUCE_TO_CLIENT).withRecruitment(otherRecruiterRecruitment2).buildAndPersist();

        var emailNoNewCand = "not-new@not-new";
        var fullname3 = "not-new";
        when(sourcingKeycloakService.getSourcingUser(otherOrgaCode2UserId)).thenReturn(Optional.of(
                new UserRepresentation().setEmail(emailNoNewCand).setMiscAttributes(Map.of("fullname", List.of(fullname3)))
        ));

        // non Sourcing Recruitment
        prepareMultipleCandidaturesOnEveryState(applicationContext.getBean(OrganizationGenerator.class).createRecruiter(ORGANIZATION_CODE, AbstractOrganization.OrganizationType.PROJECT), false);

        // Recruitment with only treated candidatures on other sourcing orga => no email
        var otherOrgaCode3 = "S-98983";
        var otherOrgaCode3UserId = "98983";
        var otherRecruiterRecruitment3 = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withRecruiter(applicationContext.getBean(OrganizationGenerator.class).createRecruiter(otherOrgaCode3, AbstractOrganization.OrganizationType.SOURCING))
                .withSourcingNotifiedUsersIds(otherOrgaCode3UserId)
                .buildAndPersist();
        applicationContext.getBean(RecruitmentCandidatureMotherObject.class).withState(GlobalCandidatureState.REFUSED_BY_CLIENT_WITH_SHEETS).withRecruitment(otherRecruiterRecruitment3).buildAndPersist();
        applicationContext.getBean(RecruitmentCandidatureMotherObject.class).withState(GlobalCandidatureState.ON_RECRUITMENT_CLIENT).withRecruitment(otherRecruiterRecruitment3).buildAndPersist();
        applicationContext.getBean(SpontaneousCandidatureMotherObject.class).withState(GlobalCandidatureState.REFUSED_BY_CLIENT_WITH_SHEETS).withRecruiter(otherRecruiterRecruitment3.getRecruiter()).buildAndPersist();
        applicationContext.getBean(SpontaneousCandidatureMotherObject.class).withState(GlobalCandidatureState.ON_RECRUITMENT_CLIENT).withRecruiter(otherRecruiterRecruitment3.getRecruiter()).buildAndPersist();

        var notNotifiedUser = new UserRepresentation().setEmail("nop3@nop3").setMiscAttributes(Map.of("fullname", List.of("nope3"))).setId("45");
        when(sourcingKeycloakService.getSourcingUser(otherOrgaCode3UserId)).thenReturn(Optional.of(
                notNotifiedUser
        ));

        var candidaturesUser1 = List.of(
                Map.of(
                        "LINK", "https://sourcing.erhgo.fr/#/recruitment-detail/" + recruitmentWithMultipleCandidatures.getId(),
                        "TITLE", recruitmentWithMultipleCandidatures.getJob().getTitle(),
                        "LOCATION", recruitmentWithMultipleCandidatures.getLocation().getCity(),
                        "TOTAL_CANDIDATURES", "78",
                        "NEW_CANDIDATURES", "6"
                ), Map.of(
                        "LINK", "https://sourcing.erhgo.fr/#/recruitment-detail/" + recruitmentWithMultipleCandidatures2.getId(),
                        "TITLE", recruitmentWithMultipleCandidatures2.getJob().getTitle(),
                        "LOCATION", recruitmentWithMultipleCandidatures2.getLocation().getCity(),
                        "TOTAL_CANDIDATURES", "78",
                        "NEW_CANDIDATURES", "6"
                ));
        var parametersUser1 = new HashMap<>(Map.of(
                "ORGANIZATION_NAME", recruitmentWithMultipleCandidatures.getOrganizationName(),
                "CANDIDATURES", candidaturesUser1,
                "FULLNAME", fullname1,
                "NEW_SPONTANEOUS_CANDIDATURES", "3",
                "TOTAL_SPONTANEOUS_CANDIDATURES", "11",
                "SPONTANEOUS_LINK", "https://sourcing.erhgo.fr/#/sourcing"
        ));

        var parametersUser4 = new HashMap<>(Map.of(
                "CANDIDATURES", Collections.emptyList(),
                "ORGANIZATION_NAME", recruitmentWithMultipleCandidatures.getOrganizationName(),
                "FULLNAME", fullname4,
                "NEW_SPONTANEOUS_CANDIDATURES", "3",
                "TOTAL_SPONTANEOUS_CANDIDATURES", "11",
                "SPONTANEOUS_LINK", "https://sourcing.erhgo.fr/#/sourcing"
        ));

        var candidaturesUser2 = new ArrayList<>(candidaturesUser1);
        candidaturesUser2.add(0, Map.of(
                "LINK", "https://sourcing.erhgo.fr/#/recruitment-detail/" + selectionRecruitment.getId(),
                "TITLE", selectionRecruitment.getJob().getTitle(),
                "LOCATION", selectionRecruitment.getLocation().getCity(),
                "TOTAL_CANDIDATURES", "1",
                "NEW_CANDIDATURES", "1"
        ));

        var parametersUser2 = new HashMap<>(Map.of(
                "ORGANIZATION_NAME", recruitmentWithMultipleCandidatures.getOrganizationName(),
                "CANDIDATURES", candidaturesUser2,
                "FULLNAME", fullname2
        ));
        var candidaturesUser3 = List.of(
                Map.of(
                        "LINK", "https://sourcing.erhgo.fr/#/recruitment-detail/" + otherRecruiterRecruitment2.getId(),
                        "TITLE", otherRecruiterRecruitment2.getJob().getTitle(),
                        "LOCATION", otherRecruiterRecruitment2.getLocation().getCity(),
                        "TOTAL_CANDIDATURES", "1",
                        "NEW_CANDIDATURES", "0"
                ));
        var parametersUser3 = new HashMap<>(Map.of(
                "ORGANIZATION_NAME", otherRecruiterRecruitment2.getOrganizationName(),
                "CANDIDATURES", candidaturesUser3,
                "FULLNAME", fullname3
        ));
        var user1 = new UserRepresentation().setEmail(email1).setMiscAttributes(Map.of("fullname", List.of(fullname1))).setId(USER_ID);
        var user2 = new UserRepresentation().setEmail(email2).setMiscAttributes(Map.of("fullname", List.of(fullname2))).setId(USER_ID_2);
        var user3 = new UserRepresentation().setEmail(notifiedImmediatelyUser).setId(USER_ID_3);
        var user4 = new UserRepresentation().setEmail(notifiedOnlyForSpontaneous).setMiscAttributes(Map.of("fullname", List.of(fullname4))).setId(USER_ID_4);

        when(sourcingKeycloakService.getEnabledSourcingUsersForGroup(recruiter.getCode())).thenReturn(List.of(user1, user2, user3, user4));
        when(sourcingKeycloakService.getEnabledSourcingUsersForGroup(otherRecruiterRecruitment3.getRecruiter().getCode())).thenReturn(List.of(notNotifiedUser));

        when(sourcingKeycloakService.getSourcingUser(USER_ID)).thenReturn(Optional.of(user1));
        when(sourcingKeycloakService.getSourcingUser(USER_ID_2)).thenReturn(Optional.of(user2));
        when(sourcingKeycloakService.getSourcingUser(USER_ID_3)).thenReturn(Optional.of(user3));
        when(sourcingKeycloakService.getSourcingUser(USER_ID_4)).thenReturn(Optional.of(user4));

        applicationContext.getBean(SourcingPreferencesMotherObject.class).withUserId(notNotifiedUser.getId()).withMailFrequency(SourcingPreferences.MailFrequency.DAILY).withNotifiedOnSpontaneousCandidatures(true).buildAndPersist();
        applicationContext.getBean(SourcingPreferencesMotherObject.class).withUserId(USER_ID).withMailFrequency(SourcingPreferences.MailFrequency.DAILY).withNotifiedOnSpontaneousCandidatures(true).buildAndPersist();
        applicationContext.getBean(SourcingPreferencesMotherObject.class).withUserId(USER_ID_2).withMailFrequency(SourcingPreferences.MailFrequency.DAILY).withNotifiedOnSpontaneousCandidatures(false).buildAndPersist();
        applicationContext.getBean(SourcingPreferencesMotherObject.class).withUserId(USER_ID_3).withMailFrequency(SourcingPreferences.MailFrequency.IMMEDIATELY).buildAndPersist();
        applicationContext.getBean(SourcingPreferencesMotherObject.class).withUserId(USER_ID_4).withMailFrequency(SourcingPreferences.MailFrequency.DAILY).withNotifiedOnSpontaneousCandidatures(true).buildAndPersist();
        applicationContext.getBean(SourcingPreferencesMotherObject.class).withUserId(otherOrgaCode2UserId).withMailFrequency(SourcingPreferences.MailFrequency.DAILY).withNotifiedOnSpontaneousCandidatures(true).buildAndPersist();

        sourcingScheduler.remindRecruitmentsInfo();

        Mockito.verify(mailingListService).sendMailsForTemplate(Set.of(email1), 221L, parametersUser1, null);
        Mockito.verify(mailingListService).sendMailsForTemplate(Set.of(email2), 221L, parametersUser2, null);
        Mockito.verify(mailingListService).sendMailsForTemplate(Set.of(notifiedOnlyForSpontaneous), 221L, parametersUser4, null);
        Mockito.verify(mailingListService).sendMailsForTemplate(Set.of(emailNoNewCand), 221L, parametersUser3, null);
        Mockito.verifyNoMoreInteractions(mailingListService);
    }

    @Test
    @SneakyThrows
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    @ResetDataAfter
    void getRecruitmentList_empty() {
        when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID))
                .thenReturn(new UserRepresentation().setId(USER_ID).setGroups(List.of(ORGANIZATION_CODE)));
        performGetAndExpect("/sourcing/recruitments", "emptySourcingRecruitments", false)
                .andExpect(status().isOk());
    }


    @Test
    @SneakyThrows
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    @ResetDataAfter
    void getRecruitmentList() {
        when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID))
                .thenReturn(new UserRepresentation().setId(USER_ID).setGroups(List.of(ORGANIZATION_CODE)));
        when(sourcingKeycloakService.getSourcingUser(USER_ID))
                .thenReturn(Optional.of(new UserRepresentation().setId(USER_ID).setEmail("a@a").setFirstName("Phil").setLastName("Lap")));

        var recruitmentWithMultipleCandidatures = prepareMultipleCandidaturesOnEveryState(null, false);

        Stream.of(RecruitmentState.values()).forEach(state -> applicationContext.getBean(RecruitmentMotherObject.class)
                .withRecruiter(recruitmentWithMultipleCandidatures.getJob().getRecruiter())
                .withState(state)
                .withJobTitle("Job title for " + state)
                .withManagerUserId(state == RecruitmentState.PUBLISHED ? USER_ID : null)
                .withPublicationDate(state == RecruitmentState.PUBLISHED ? Date.from(Instant.ofEpochMilli(42_000_000_000l)) : null)
                .withPublicationEndDate(state == RecruitmentState.PUBLISHED ? OffsetDateTime.of(1942, 01, 01, 01, 42, 42, 0, ZoneOffset.UTC) : null)
                .withLocation(Location.builder().citycode("59000").city("Lille").longitude(42f).latitude(24f).postcode("59123").departmentCode("59").regionName("Là-haut").radiusInKm(30).build()
                )
                .withSalaries(19000, 30000)
                .buildAndPersist());

        var jobIds = applicationContext.getBean(JobRepository.class).findByRecruiterCodeInOrEmployerCodeIn(Set.of(ORGANIZATION_CODE), Pageable.unpaged()).stream().map(Job::getId).map(UUID::toString).toArray();
        applicationContext.getBean(RecruitmentMotherObject.class).buildAndPersist();
        performGetAndExpect("/sourcing/recruitments", "sourcingRecruitments", false)
                .andExpect(jsonPath("$[*].jobId", Matchers.containsInAnyOrder(jobIds)))
                .andExpect(status().isOk());

    }
}
