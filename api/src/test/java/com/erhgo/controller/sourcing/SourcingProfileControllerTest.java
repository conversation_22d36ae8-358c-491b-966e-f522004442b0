package com.erhgo.controller.sourcing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.exceptions.InvalidSiretException;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.openapi.dto.UpdateSourcingOrganizationCommandDTO;
import com.erhgo.openapi.dto.UpdateSourcingUserCommandDTO;
import com.erhgo.repositories.AbstractOrganizationRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.organization.OrganizationDataProvider;
import com.erhgo.services.search.UserIndexer;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@Slf4j
class SourcingProfileControllerTest extends AbstractIntegrationTest {

    public static final String ORGANIZATION_CODE = "S-42";
    public static final String CAPACITY_CODE = "CA-1";
    private static final String ORGANIZATION_NAME = "acme";
    @MockitoBean
    private UserIndexer userIndexer;
    @MockitoBean
    private OrganizationDataProvider organizationDataProvider;
    @Autowired
    private ApplicationContext applicationContext;
    @MockitoBean
    private KeycloakMockService keycloakMockService;

    static final UUID OCCUPATION_ID = UUID.fromString("46f88cfe-0dc4-46d6-a0af-6e7c8ad49f7c");
    static final String USER_ID = "56f88cfe-0dc4-46d6-a0af-6e7c8ad49f7c";
    static final String SIRET = "42";

    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.SOURCING, ORGANIZATION_CODE}, id = USER_ID)
    @ParameterizedTest
    @NullSource
    @ValueSource(booleans = {true, false})
    @SneakyThrows
    void getOrganization(Boolean withHttpScheme) {
        var gdprMention = "<p>Mention RGPD</p>";
        txHelper.doInTransaction(() -> {

            var orga = applicationContext.getBean(OrganizationGenerator.class)
                    .createRecruiter(ORGANIZATION_CODE, "orga sourcing", AbstractOrganization.OrganizationType.SOURCING);
            orga.setDescription("Une Description");
            orga.setForcedUrl(withHttpScheme == null ? null : withHttpScheme ? "http://myUrl" : "myUrl");
            orga.setSiret(SIRET);
            orga.setSiretVerificationStatus(AbstractOrganization.SiretVerificationStatus.WRONG_SIRET);
            orga.setGdprMention(gdprMention);
        });
        var user = new UserRepresentation()
                .setMiscAttributes(Map.of("siret", List.of(SIRET)))
                .setEmail("<EMAIL>")
                .setGroups(List.of(Role.SOURCING, ORGANIZATION_CODE))
                .setId(USER_ID);
        Mockito.when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(user);
        performGetAndExpect("/sourcing/organization", "sourcingOrganization", false)
                .andExpect(jsonPath("$.externalUrl", Matchers.is(withHttpScheme == null ? null : withHttpScheme ? "http://myUrl" : "https://myUrl")))
                .andExpect(jsonPath("$.isForcedUrl", Matchers.is(withHttpScheme != null)))
        ;
    }

    @WithMockKeycloakUser(roles = {Role.SOURCING, ORGANIZATION_CODE}, id = USER_ID)
    @Test
    @ResetDataAfter
    void updateOrganization_without_siret_change() {
        txHelper.doInTransaction(() -> {

            applicationContext.getBean(OrganizationGenerator.class)
                    .createRecruiter(ORGANIZATION_CODE, AbstractOrganization.OrganizationType.SOURCING)
                    .setSiret(SIRET);
        });
        var user = new UserRepresentation()
                .setMiscAttributes(Map.of("siret", List.of(SIRET)))
                .setEmail("<EMAIL>")
                .setGroups(List.of(Role.SOURCING, ORGANIZATION_CODE))
                .setId(USER_ID);
        Mockito.when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(user);
        var newName = "new name";
        var newDescr = "new description";
        var newSiret = SIRET;
        var newGdprMention = "<p>RGPD personnalisée</p>";
        performPut("/sourcing/organization", new UpdateSourcingOrganizationCommandDTO().description(newDescr).siret(newSiret).name(newName).gdprMention(newGdprMention));
        var organization = applicationContext.getBean(AbstractOrganizationRepository.class).findOneByCode(ORGANIZATION_CODE);
        Assertions.assertThat(organization.getSiret()).isEqualTo(newSiret);
        Assertions.assertThat(organization.getDescription()).isEqualTo(newDescr);
        Assertions.assertThat(organization.getTitle()).isEqualTo(newName);
        Assertions.assertThat(organization.getGdprMention()).isEqualTo(newGdprMention);
        verify(sourcingKeycloakService, never()).updateUserSiret(USER_ID, newSiret);
        verifyNoInteractions(organizationDataProvider);
    }

    @ResetDataAfter
    @SneakyThrows
    @WithMockKeycloakUser(roles = {Role.SOURCING, ORGANIZATION_CODE}, id = USER_ID)
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void updateOrganization_with_siret_change(boolean success) {
        applicationContext.getBean(OrganizationGenerator.class).createRecruiter(ORGANIZATION_CODE, AbstractOrganization.OrganizationType.SOURCING);
        var user = new UserRepresentation()
                .setMiscAttributes(new HashMap<>(Map.of(UserRepresentation.SIRET_ATTRIBUTE, List.of(SIRET))))
                .setEmail("<EMAIL>")
                .setGroups(List.of(Role.SOURCING, ORGANIZATION_CODE))
                .setId(USER_ID);
        Mockito.when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(user);
        var newName = "new name";
        var newDescr = "new description";
        var newSiret = "new siret";
        var newOrgaName = "new orga name";
        if (success) {
            when(organizationDataProvider.getNameForSiret(newSiret)).thenReturn(newOrgaName);
        } else {
            when(organizationDataProvider.getNameForSiret(newSiret)).thenThrow(InvalidSiretException.class);
        }
        performPut("/sourcing/organization", new UpdateSourcingOrganizationCommandDTO().description(newDescr).siret(newSiret).name(newName))
                .andExpect(jsonPath("$.hasSiretFormatError", Matchers.is(!success)));
        var organization = applicationContext.getBean(AbstractOrganizationRepository.class).findOneByCode(ORGANIZATION_CODE);
        Assertions.assertThat(organization.getSiret()).isEqualTo(newSiret);
        Assertions.assertThat(organization.getDescription()).isEqualTo(newDescr);
        Assertions.assertThat(organization.getTitle()).isEqualTo(newName);
        verify(sourcingKeycloakService).updateUserSiret(USER_ID, newSiret);
    }

    @ResetDataAfter
    @SneakyThrows
    @WithMockKeycloakUser(roles = {Role.SOURCING, ORGANIZATION_CODE}, id = USER_ID)
    @Test
    void updateOrganization_initialize_siret() {
        applicationContext.getBean(OrganizationGenerator.class).createRecruiter(ORGANIZATION_CODE, AbstractOrganization.OrganizationType.SOURCING);
        var user = new UserRepresentation()
                .setMiscAttributes(new HashMap<>(Map.of("firstname", List.of("a"))))
                .setEmail("<EMAIL>")
                .setGroups(List.of(Role.SOURCING, ORGANIZATION_CODE))
                .setId(USER_ID);
        Mockito.when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(user);
        var newName = "new name";
        var newDescr = "new description";
        var newSiret = "new siret";
        var newOrgaName = "new orga name";

        performPut("/sourcing/organization", new UpdateSourcingOrganizationCommandDTO().description(newDescr).siret(newSiret).name(newName))
                .andExpect(MockMvcResultMatchers.status().isOk());
        var organization = applicationContext.getBean(AbstractOrganizationRepository.class).findOneByCode(ORGANIZATION_CODE);
        Assertions.assertThat(organization.getSiret()).isEqualTo(newSiret);
    }

    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.SOURCING, ORGANIZATION_CODE}, id = USER_ID)
    @Test
    void updateUser() {
        applicationContext.getBean(OrganizationGenerator.class).createRecruiter(ORGANIZATION_CODE, AbstractOrganization.OrganizationType.SOURCING);
        var user = new UserRepresentation()
                .setMiscAttributes(Map.of("siret", List.of(SIRET)))
                .setEmail("<EMAIL>")
                .setId(USER_ID);
        var newName = "new name";
        var newPhone = "newPhone";
        var newEmail = "new email";
        Mockito.when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(user);
        performPut("/sourcing/user", new UpdateSourcingUserCommandDTO().fullname(newName).email(newEmail).phone(newPhone));
        verify(sourcingKeycloakService).updateUser(USER_ID, newEmail, newPhone, newName);
    }
}
