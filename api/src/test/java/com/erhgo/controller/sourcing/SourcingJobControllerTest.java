package com.erhgo.controller.sourcing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationState;
import com.erhgo.domain.classifications.rome.RomeOccupation;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.*;
import com.erhgo.domain.job.JobMotherObject;
import com.erhgo.domain.job.JobType;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.recruitment.OptionalActivity;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.sourcing.SourcingInvitationMotherObject;
import com.erhgo.domain.sourcing.SourcingSubscriptionMotherObject;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.SourcingPreferencesMotherObject;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.generators.*;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.*;
import com.erhgo.repositories.classifications.ErhgoClassificationRepository;
import com.erhgo.repositories.dto.CandidateDetailDTO;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.dto.criteria.SourcingCandidatesCriteria;
import com.erhgo.services.generation.OccupationForLabelGenerationService;
import com.erhgo.services.generation.dto.OccupationForLabelGenerationResult;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailingListService;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.notifier.OccupationCreationSourceType;
import com.erhgo.services.notifier.messages.SourcingJobOnOccupationMessage;
import com.erhgo.utils.DateTimeUtils;
import com.erhgo.utils.StringUtils;
import jakarta.persistence.EntityManager;
import jakarta.servlet.ServletException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.within;
import static org.hamcrest.Matchers.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@TestPropertySource(properties = {"sendinblue.templates.sourcing-candidature-proposal=42"})
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
class SourcingJobControllerTest extends AbstractIntegrationTest {
    private static final String ORGANIZATION_CODE = "S-42";
    private static final String USER_ID = "11a84b0a-4eea-4980-adff-0853af0f672a";
    public static final Set<String> INITIAL_USER_IDS_TO_NOTIFY = Set.of(USER_ID, "42");
    private static final String USER_ID_2 = "21a84b0a-4eea-4980-adff-0853af0f6722";
    private static final String USER_ID_3 = "31a84b0a-4eea-4980-adff-0853af0f6722";

    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    JobRepository jobRepository;

    @Autowired
    RecruitmentRepository recruitmentRepository;

    @Autowired
    RecruitmentProfileRepository recruitmentProfileRepository;

    @Autowired
    AbstractOrganizationRepository organizationRepository;

    @Autowired
    CapacityGenerator capacityGenerator;
    UpdateSourcingJobContractCommandDTO command;
    @MockitoBean
    MailingListService mailingListService;
    @MockitoBean
    Notifier notifier;

    @MockitoBean
    KeycloakMockService keycloakService;

    @MockitoBean
    SourcingUserRepository sourcingUserRepository;
    @MockitoBean
    OccupationForLabelGenerationService occupationForLabelGenerationService;
    public static final String CAPACITY_CODE = "CA-1";
    static final UUID OCCUPATION_ID = UUID.fromString("46f88cfe-0dc4-46d6-a0af-6e7c8ad49f7c");
    static final String SIRET = "42";

    public static final String EMAIL = "<EMAIL>";
    private static final String FULLNAME = "john";

    @BeforeEach
    void before() {
        when(sourcingKeycloakService.getSourcingUser(anyString())).thenReturn(Optional.empty());
        when(sourcingKeycloakService.getSourcingUser(USER_ID)).thenReturn(Optional.of(new UserRepresentation().setId(USER_ID).setEmail("<EMAIL>").setMiscAttributes(Map.of(UserRepresentation.FULLNAME_ATTRIBUTE, List.of("Jean Notif")))));
        when(sourcingKeycloakService.getSourcingUser(USER_ID_2)).thenReturn(Optional.of(new UserRepresentation().setId(USER_ID_2).setEmail("no@tify_2.me").setMiscAttributes(Map.of(UserRepresentation.FULLNAME_ATTRIBUTE, List.of("Pat Notif")))));
    }

    @SneakyThrows
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, ORGANIZATION_CODE})
    @ResetDataAfter
    void updateContract(boolean existentRecruitment) {
        applicationContext.getBean(CriteriaMotherObject.class).withTypeContractCriteria().buildAndPersist();
        applicationContext.getBean(CriteriaMotherObject.class).withWorkingTimeCriteria().buildAndPersist();

        var organization = createRecruiter();

        var initialJob = applicationContext.getBean(JobMotherObject.class).withCapacities(capacityGenerator.createCapacity("CA-2"), capacityGenerator.createCapacity("CA-3")).withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist()).withLocation(Location.builder().city("Lille").citycode("59850").build()).withRecruiter(organization).buildAndPersist();

        if (existentRecruitment) {
            applicationContext.getBean(RecruitmentMotherObject.class).withJob(initialJob).buildAndPersist();
        }

        command = new UpdateSourcingJobContractCommandDTO().typeContractCategory(TypeContractCategoryDTO.TEMPORARY).workingTimeType(WorkingTimeDTO.FULL_TIME).baseSalary(32000).workingWeeklyTime(35).maxSalary(54000).modularWorkingTime(true).hideSalary(true);
        var exceptionCaught = false;
        try {
            performPost("/sourcing/job/%s/contract".formatted(initialJob.getId()), command).andExpect(MockMvcResultMatchers.status().isNoContent());

            txHelper.doInTransaction(() -> {
                var job = applicationContext.getBean(JobRepository.class).findById(initialJob.getId()).orElseThrow();
                assertThat(job.getCriteriaValues().stream().map(CriteriaValue::getCode)).containsExactlyInAnyOrder(CriteriaValue.getValueCodeForTypeWorkingTime(TypeWorkingTime.FULL_TIME), CriteriaValue.getValueCodeForTypeContractCategory(TypeContractCategory.TEMPORARY));
                var recruitments = recruitmentRepository.findAll();
                assertThat(recruitments).hasSize(1);
                var recruitment = recruitments.iterator().next();
                assertThat(recruitment.getJob().getId()).isEqualTo(initialJob.getId());

                assertThat(recruitment.getBaseSalary()).isEqualTo(command.getBaseSalary());
                assertThat(recruitment.getMaxSalary()).isEqualTo(command.getMaxSalary());
                assertThat(recruitment.getHideSalary()).isEqualTo(command.getHideSalary());
                assertThat(recruitment.getWorkingWeeklyTime()).isEqualTo(command.getWorkingWeeklyTime());
                assertThat(recruitment.getModularWorkingTime()).isEqualTo(command.getModularWorkingTime());
            });
        } catch (ServletException e) {
            exceptionCaught = true;
        }
        assertThat(exceptionCaught).isEqualTo(!existentRecruitment);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, ORGANIZATION_CODE})
    void add_criteria_filtering_on_sourcing_step_remove_only_related_step() throws Exception {
        var criteria11 = applicationContext.getBean(CriteriaMotherObject.class).withSourcingCriteriaStep(SourcingCriteriaStep.STEP1).buildAndPersist();
        var criteria12 = applicationContext.getBean(CriteriaMotherObject.class).withSourcingCriteriaStep(SourcingCriteriaStep.STEP1).buildAndPersist();
        var criteria3 = applicationContext.getBean(CriteriaMotherObject.class).buildAndPersist();

        var organization = createRecruiter();

        var initialJob = applicationContext.getBean(JobMotherObject.class).withRecruiter(organization).withCriteriaValues(criteria11.getCriteriaValues().get(0), criteria12.getCriteriaValues().get(0), criteria3.getCriteriaValues().get(0)).buildAndPersist();
        performPut("/sourcing/job/" + initialJob.getId() + "/criteria?restrictToSourcingStep=" + SourcingCriteriaStepDTO.STEP1.name(), List.of()).andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var job = jobRepository.findById(initialJob.getId()).orElseThrow();
            assertThat(job.getCriteriaValues().stream().map(CriteriaValue::getCode)).containsExactlyInAnyOrder(criteria3.getCriteriaValues().get(0).getCode());
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, ORGANIZATION_CODE})
    void add_criteria_filtering_on_sourcing_step_add_only_related_step() throws Exception {
        var criteria11 = applicationContext.getBean(CriteriaMotherObject.class).withSourcingCriteriaStep(SourcingCriteriaStep.STEP1).buildAndPersist();
        var criteria12 = applicationContext.getBean(CriteriaMotherObject.class).withSourcingCriteriaStep(SourcingCriteriaStep.STEP1).buildAndPersist();
        var criteria3 = applicationContext.getBean(CriteriaMotherObject.class).buildAndPersist();

        var organization = createRecruiter();

        var initialJob = applicationContext.getBean(JobMotherObject.class).withRecruiter(organization).withCriteriaValues(criteria11.getCriteriaValues().get(1), criteria12.getCriteriaValues().get(0), criteria3.getCriteriaValues().get(0)).buildAndPersist();
        performPut("/sourcing/job/" + initialJob.getId() + "/criteria?restrictToSourcingStep=" + SourcingCriteriaStepDTO.STEP1.name(), List.of(criteria11.getCriteriaValues().get(0).getCode(), criteria12.getCriteriaValues().get(1).getCode(), criteria3.getCriteriaValues().get(1).getCode())).andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var job = jobRepository.findById(initialJob.getId()).orElseThrow();
            assertThat(job.getCriteriaValues().stream().map(CriteriaValue::getCode)).containsExactlyInAnyOrder(criteria11.getCriteriaValues().get(0).getCode(), criteria12.getCriteriaValues().get(1).getCode(), criteria3.getCriteriaValues().get(0).getCode());
        });
    }


    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, ORGANIZATION_CODE})
    void set_question_for_recruitment() throws Exception {
        var organization = createRecruiter();

        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class).withRecruiter(organization).buildAndPersist();

        var question = "new question for recruitment";

        performPut("/sourcing/recruitment/" + recruitment.getId() + "/question", new UpdateSourcingRecruitmentQuestionCommandDTO().question(question)).andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            assertThat(recruitmentProfileRepository.findByJobIdOrderByTitleAsc(recruitment.getJob().getId()).get(0).getCustomQuestion()).isEqualTo(question);
        });
    }


    @SneakyThrows
    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, ORGANIZATION_CODE}, id = USER_ID)
    void get_recruitment_and_job_finished() {
        var recruitment = createRecruitment(false);

        performGetAndExpect("/sourcing/recruitment/%d/detail".formatted(recruitment.getId()), "sourcingJobAndRecruitment", false).andExpect(jsonPath("$.jobId", Matchers.is(recruitment.getJob().getId().toString()))).andExpect(jsonPath("$.recruitmentId", Matchers.is(recruitment.getId().intValue())));
    }

    @SneakyThrows
    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.SOURCING, ORGANIZATION_CODE}, id = USER_ID)
    void should_list_recruitments_for_employer_with_same_location() {
        var mainRecruitmentRef = new AtomicReference<Recruitment>();
        var insideRageRecruitmentRef = new AtomicReference<Recruitment>();
        var outsideRangeRecruitmentRef = new AtomicReference<Recruitment>();
        var locLyon = Location.builder().city("Lyon").citycode("69003").postcode("69003").latitude(45.764043F).longitude(4.849664F).build();
        var locStEtienne = Location.builder().city("Saint Etienne").citycode("42000").postcode("42000").latitude(45.439695F).longitude(4.3871779F).build();

        var locGenas = Location.builder().city("Genas").citycode("69740").postcode("69740").latitude(45.73333F).longitude(5F).build();

        txHelper.doInTransaction(() -> {
            var mainRecruitment = createRecruitment(true);
            initializeSourcingUser(mainRecruitment.getRecruiter());
            mainRecruitment.setLocation(locLyon);

            var outsideRangeRecruitment = applicationContext.getBean(RecruitmentMotherObject.class).withRecruiter(mainRecruitment.getRecruiter()).withOccupation(mainRecruitment.getErhgoOccupation()).withState(RecruitmentState.PUBLISHED).withLocation(locStEtienne).buildAndPersist();

            var insideRangeRecruitment = applicationContext.getBean(RecruitmentMotherObject.class).withRecruiter(mainRecruitment.getRecruiter()).withOccupation(mainRecruitment.getErhgoOccupation()).withState(RecruitmentState.PUBLISHED).withLocation(locGenas).buildAndPersist();

            mainRecruitmentRef.set(mainRecruitment);
            outsideRangeRecruitmentRef.set(outsideRangeRecruitment);
            insideRageRecruitmentRef.set(insideRangeRecruitment);
        });
        var occupationId = mainRecruitmentRef.get().getJob().getErhgoOccupation().getId();

        mvc.perform(get("/api/odas/sourcing/%s/similar-recruitments".formatted(occupationId)).param("longitude", locLyon.getLongitude().toString()).param("latitude", locLyon.getLatitude().toString()).contentType(MediaType.APPLICATION_JSON)).andExpect(status().isOk()).andExpect(jsonPath("$.[*]", hasSize(2))).andExpect(jsonPath("$.[*].recruitmentId", hasItem(mainRecruitmentRef.get().getId().intValue()))).andExpect(jsonPath("$.[*].recruitmentId", hasItem(insideRageRecruitmentRef.get().getId().intValue()))).andExpect(jsonPath("$.[*].recruitmentId", not(hasItem(outsideRangeRecruitmentRef.get().getId().intValue()))));
    }

    private Recruitment createRecruitment(boolean hideSalary) {
        var organization = createRecruiter();
        var occupationId = UUID.fromString("45f3ce6c-f1c9-4356-9d07-bca1b241403");
        var location = Location.builder().city("Lyon").citycode("42123").postcode("42000").build();
        applicationContext.getBean(CriteriaMotherObject.class).withWorkingTimeCriteria().buildAndPersist();
        applicationContext.getBean(CriteriaMotherObject.class).withTypeContractCriteria().buildAndPersist();
        var criteria = applicationContext.getBean(CriteriaMotherObject.class).buildAndPersist();
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).withLevel(3).withCapacities(capacityGenerator.createCapacity("CA42-42")).withId(occupationId)
                .withRomeAndEscoOccupations().buildAndPersist();
        var job = applicationContext.getBean(JobMotherObject.class).withTitle("A sourcing Job").withLocation(location).withRecruiter(organization).withOccupation(occupation).withCriteriaValues(criteria.getCriteriaValues().toArray(new CriteriaValue[0])).withContractCategories(TypeContractCategory.PERMANENT).withJobType(JobType.SOURCING).withBehaviors(applicationContext.getBean(BehaviorGenerator.class).createBehavior("be-1", "be title", "desc", BehaviorCategory.SOCIABILITY, 2)).withWorkingTimes(TypeWorkingTime.FULL_TIME).buildAndPersist();
        return applicationContext.getBean(RecruitmentMotherObject.class)
                .withJob(job)
                .withSalaries(42, 59)
                .withRecruitmentProfileQuestion("content ?")
                .withTypeContract(ContractType.CDI)
                .withHideSalary(true)
                .withLocation(location)
                .withRecruiterDescription("UGC")
                .withState(RecruitmentState.DRAFT)
                .withSourcingStep(4)
                .withModularWorkingTime(true)
                .withExternalUrl("http://yo.lo")
                .withWorkingWeeklyTime(5)
                .withHideSalary(hideSalary)
                .withPublicationEndDate(OffsetDateTime.of(2042, 1, 1, 1, 1, 1, 1, ZoneOffset.UTC))
                .withSalaries(1000, 10000)
                .withSourcingNotifiedUsersIds(INITIAL_USER_IDS_TO_NOTIFY.toArray(new String[]{}))
                .withErhgoClassification(new HashSet<>(applicationContext.getBean(ErhgoClassificationRepository.class).findErhgoClassificationByCodeIn(Set.of("SO-02", "SO-05"))))
                .withManagerUserId(USER_ID)
                .buildAndPersist();
    }

    private Recruiter createRecruiter() {
        var organization = applicationContext.getBean(OrganizationGenerator.class).createRecruiter(ORGANIZATION_CODE, AbstractOrganization.OrganizationType.SOURCING);
        return organization;
    }

    @SneakyThrows
    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, ORGANIZATION_CODE})
    void get_empty_recruitment_and_job_finished() {
        var organization = createRecruiter();
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class).withRecruiter(organization).withState(RecruitmentState.DRAFT).buildAndPersist();

        performGetAndExpect("/sourcing/recruitment/%d/detail".formatted(recruitment.getId()), "sourcingEmptyJobAndRecruitment", false)
                .andExpect(jsonPath("$.jobId", Matchers.any(String.class)));
    }

    @SneakyThrows
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void send_candidature_proposal(boolean hideSalary) {
        var email1 = "a@a";
        var email2 = "a@b";
        var user1ToNotify = applicationContext.getBean(UserProfileMotherObject.class).withEmail(email1).buildAndPersist();
        var user3NotToNotify = applicationContext.getBean(UserProfileMotherObject.class).buildAndPersist();
        var user2ToNotify = applicationContext.getBean(UserProfileMotherObject.class).withEmail(email2).buildAndPersist();
        var recruitment = createRecruitment(hideSalary);

        var expectedCriteria = applicationContext.getBean(SourcingCandidatesCriteria.class).workingTimeType(TypeWorkingTime.FULL_TIME).classifications(List.of("SO-02", "SO-05")).masteryLevelAround(3).excludesRecruitment(recruitment);

        txHelper.doInTransaction(() -> {
            var refreshedRecruitment = recruitmentRepository.findById(recruitment.getId()).orElseThrow();
            expectedCriteria
                    .criteria(refreshedRecruitment.getJob().getCriteriaValuesCodes())
                    .capacitiesIds(new ArrayList<>(refreshedRecruitment.getErhgoOccupation().getAllCapacitiesId()))
                    .latitude(refreshedRecruitment.getLocation().getLatitude())
                    .longitude(refreshedRecruitment.getLocation().getLongitude())
                    .occupationId(refreshedRecruitment.getJob().getErhgoOccupation().getId())
                    .typeContractCategory(refreshedRecruitment.getTypeContract().getTypeContractCategory())
                    .salaryMin(refreshedRecruitment.getBaseSalary())
                    .salaryMax(refreshedRecruitment.getMaxSalary())
                    .excludesRecruitment(refreshedRecruitment);
        });


        var erhgoClassifications = List.of("La relation d’aide, le service public", "Le contact humain, la conversation, le débat");
        var classifications = String.join(", ", erhgoClassifications);
        when(sourcingUserRepository.getCandidates(any())).thenReturn(List.of(user1ToNotify.userId(), user2ToNotify.userId()));
        performPost("/sourcing/invite-to-recruitment", new InviteToRecruitmentCommandDTO().recruitmentId(recruitment.getId())).andExpect(status().isNoContent());
        // NB: Mockito does not tolerate different collections concrete class - if it fails, ensure no Set is compared to a List
        Mockito.verify(sourcingUserRepository).getCandidates(argThat(c -> c.criteria().equals(expectedCriteria.criteria()) &&
                c.classifications().equals(expectedCriteria.classifications()) &&
                c.typeContractCategory().equals(TypeContractCategory.PERMANENT) &&
                c.criteriaForContracts().contains(CriteriaValue.getValueCodeForTypeContractCategory(TypeContractCategory.PERMANENT)) &&
                c.criteriaForContracts().size() == 1 &&
                c.excludesRecruitment().equals(expectedCriteria.excludesRecruitment()) &&
                c.masteryLevelAround().equals(3) &&
                c.masteryLevelMax().equals(expectedCriteria.masteryLevelMax()) &&
                c.masteryLevelMin().equals(expectedCriteria.masteryLevelMin())));
        txHelper.doInTransaction(() -> {
            assertThat(applicationContext.getBean(NotificationRepository.class).findRecruitmentNotificationByUserProfileUuid(user3NotToNotify.uuid())).isEmpty();
            assertThat(applicationContext.getBean(NotificationRepository.class).findRecruitmentNotificationByUserProfileUuid(user1ToNotify.uuid())).hasSize(1);
            assertThat(applicationContext.getBean(NotificationRepository.class).findRecruitmentNotificationByUserProfileUuid(user2ToNotify.uuid())).hasSize(1);
            assertThat(applicationContext.getBean(RecruitmentRepository.class).findNotificationsForRecruitment(Set.of(recruitment.getId()))).hasSize(1);
        });
    }

    @ParameterizedTest
    @CsvSource(value = {"ERROR,true,true,true", "ERROR,false,true,true", ",false,true, false", ",false,false,true", ",false,true,false", ",false,true,true",})
    @WithMockKeycloakUser(id = USER_ID, roles = {ORGANIZATION_CODE, Role.SOURCING})
    @ResetDataAfter
    void publish_recruitment_initialize_mail_state(RecruitmentSendNotificationState initialState, boolean isTechnical, boolean isActivated, boolean isFinalQualificationState) throws Exception {
        prepareUser().setGroups(List.of(ORGANIZATION_CODE));

        AtomicReference<Recruitment> recruitmentRef = new AtomicReference<>();
        var userId1 = "af027019-ce3f-40a5-8d91-18291d964e51";
        var userId2 = "af027019-ce3f-40a5-8d91-18291d964e52";
        txHelper.doInTransaction(() -> {
            var recruitment = createRecruitment(false);
            recruitment.getErhgoOccupation().setTechnical(isTechnical);
            if (isFinalQualificationState) {
                recruitment.getErhgoOccupation().setQualificationState(ErhgoOccupationState.QUALIFIED_V3_CONFIRMED);
            }
            recruitmentRef.set(recruitment);
            if (isActivated) {
                applicationContext.getBean(SourcingInvitationMotherObject.class).withGuests(recruitment.getRecruiter()).buildAndPersist();
            } else {
                applicationContext.getBean(SourcingSubscriptionMotherObject.class).withRecruiter(recruitment.getRecruiter()).withExpirationDate(OffsetDateTime.now().plus(6, ChronoUnit.DAYS)).buildAndPersist();
            }
            applicationContext.getBean(UserProfileMotherObject.class).withUserId(userId1).buildAndPersist();
            applicationContext.getBean(UserProfileMotherObject.class).withUserId(userId2).withMobileToken("token1").withMobileToken("token2").buildAndPersist();
        });
        var recruitment = recruitmentRef.get();
        txHelper.doInTransaction(() -> {
            recruitmentRepository.findById(recruitment.getId()).orElseThrow().setSendNotificationState(initialState);
        });

        when(sourcingUserRepository.getCandidates(any())).thenReturn(List.of(userId1, userId2));

        performPost(
                "/sourcing/recruitment/%d/change-state".formatted(recruitment.getId()),
                new ChangeSourcingRecruitmentStateCommandDTO().nextState(RecruitmentStateDTO.PUBLISHED).sendNotifications(UsersToNotifySelectionTypeDTO.ALL)
        )
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[*]", hasSize(0)));

        assertThat(applicationContext.getBean(RecruitmentCandidatureRepository.class).findByRecruitmentCodeAndValid(recruitment.getCode(), true, Pageable.unpaged()))
                .isEmpty();

        var actual = recruitmentRepository.findById(recruitment.getId()).orElseThrow();
        var sendMailDate = actual.getSendNotificationDate();
        assertThat(actual).matches(r -> r.getState() == RecruitmentState.PUBLISHED &&
                r.getSendNotificationState() == RecruitmentSendNotificationState.WAITING &&
                r.getPublicationDate() != null);

        if (isActivated &&
                isFinalQualificationState &&
                !isTechnical) {
            assertThat(sendMailDate).isBefore(Instant.now().plus(2, ChronoUnit.HOURS));
        } else {
            //assertThat(sendMailDate).isAfter(Instant.now().plus(44, ChronoUnit.HOURS));
            assertThat(sendMailDate).isCloseTo(Instant.now(), within(1, ChronoUnit.MINUTES));
        }

        assertThat(actual.getPublicationEndDate()).isBetween(OffsetDateTime.now().plusDays(27), OffsetDateTime.now().plusDays(29));

        verify(notifier).sendMessage(argThat(messageDTO -> {
            var expected = ":email: l'organisation Entreprise du S-42%s a publié le recrutement A sourcing Job à Lyon (42000) 2 notifications (dont 1 sur mobiles) diffusées après : %s"
                    .formatted(
                            isActivated ? "" : " _(version d'essai)_",
                            StringUtils.formatTimestamp(sendMailDate)
                    );
            var ok = messageDTO.getText().startsWith(expected);
            if (!ok) {
                // Beautiful log message, ugly way
                Assertions.assertThat(messageDTO.getText()).startsWith(expected);
            }
            return ok;
        }));
    }

    @SneakyThrows
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void send_candidature_proposal_forced(boolean forced) {
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class).withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).withCapacities(capacityGenerator.createCapacity("CA-12")).buildAndPersist()).buildAndPersist();
        var userNotified = applicationContext.getBean(UserProfileMotherObject.class).withNotifiedRecruitment(recruitment).withEmail("<EMAIL>").buildAndPersist();
        when(sourcingUserRepository.getCandidates(any())).thenReturn(forced ? List.of(userNotified.userId()) : Collections.emptyList());
        performPost("/sourcing/invite-to-recruitment", new InviteToRecruitmentCommandDTO().recruitmentId(recruitment.getId()).forceResend(forced)).andExpect(status().isNoContent());
    }

    @SneakyThrows
    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void getSourcingCandidatesCriteria_in_inviteToRecruitment_check_working_time_add_correctly_to_criteria() {
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class)
                        .withCapacities(capacityGenerator.createCapacity("CA-12")).buildAndPersist())
                .withJob(applicationContext.getBean(JobMotherObject.class).withWorkingTimes(TypeWorkingTime.PART_TIME).buildAndPersist())
                .buildAndPersist();

        performPost("/sourcing/invite-to-recruitment",
                new InviteToRecruitmentCommandDTO()
                        .recruitmentId(recruitment.getId())
                        .forceResend(true))
                .andExpect(status().isNoContent());
    }


    @SneakyThrows
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.SOURCING, ORGANIZATION_CODE}, id = USER_ID_2)
    void createOrUpdateRecruitment(boolean exists) {
        var recruitmentId = new AtomicLong();
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).withCapacities(applicationContext.getBean(CapacityGenerator.class).createCapacity("CA1-1")).buildAndPersist();
        var location = new LocationDTO().city("Laon").citycode("69320").departmentCode("65").regionName("AA").longitude(5.5F).latitude(5.9F);
        Recruiter recruiter;
        if (exists) {
            var recruitment = createRecruitment(true);
            recruiter = recruitment.getJob().getRecruiter();
            recruitmentId.set(recruitment.getId());
        } else {
            recruiter = createRecruiter();
        }
        initializeSourcingUser(recruiter);
        var title = "bim";
        var response = performPost("/sourcing/recruitment", new CreateSourcingJobAndRecruitmentCommandDTO().recruitmentId(recruitmentId.get() == 0 ? null : recruitmentId.get()).jobOccupationId(occupation.getId()).jobLocation(location).title(title)).andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
        var realRecruitment = objectMapper.readValue(response, SourcingJobAndRecruitmentDTO.class);
        var realRecruitmentId = realRecruitment.getRecruitmentId();
        assertThat(realRecruitment.getUsersToNotify()).map(SourcingUserDTO::getId).containsExactly(exists ? USER_ID : USER_ID_2);
        assertThat(realRecruitment.getManager().getId()).isEqualTo(exists ? USER_ID : USER_ID_2);
        txHelper.doInTransaction(() -> {
            var recruitment = recruitmentRepository.findById(realRecruitmentId).orElseThrow();
            assertThat(recruitment.getErhgoOccupation().getId()).isEqualTo(occupation.getId());
            assertThat(recruitment.getLocation().buildDTO()).isEqualTo(location);
            assertThat(recruitment.getJob().getTitle()).isEqualTo(title);
            assertThat(recruitment.getSourcingUsersIdToNotify()).containsExactlyInAnyOrderElementsOf(exists ? INITIAL_USER_IDS_TO_NOTIFY : Set.of(USER_ID_2));
            assertThat(!exists || realRecruitmentId == recruitmentId.get()).isTrue();
            assertThat(recruitment.getManagerUserId()).isEqualTo(exists ? USER_ID : USER_ID_2);
        });
    }

    @SneakyThrows
    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.SOURCING, ORGANIZATION_CODE}, id = USER_ID_2)
    void createOrUpdateRecruitment_noOccupation() {
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).withCapacities(applicationContext.getBean(CapacityGenerator.class).createCapacity("CA1-1")).buildAndPersist();
        var title = "bim";
        when(occupationForLabelGenerationService.createOrUpdateOccupation(title, OccupationCreationSourceType.FROM_SOURCING)).thenReturn(new OccupationForLabelGenerationResult(occupation.getId(), new ArrayList<>(), false));
        var location = new LocationDTO().city("Laon").citycode("69320").departmentCode("65").regionName("AA").longitude(5.5F).latitude(5.9F);
        var recruiter = createRecruiter();
        initializeSourcingUser(recruiter);
        var response = performPost("/sourcing/recruitment", new CreateSourcingJobAndRecruitmentCommandDTO().jobLocation(location).title(title)).andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
        var realRecruitment = objectMapper.readValue(response, SourcingJobAndRecruitmentDTO.class);
        var realRecruitmentId = realRecruitment.getRecruitmentId();
        txHelper.doInTransaction(() -> {
            var recruitment = recruitmentRepository.findById(realRecruitmentId).orElseThrow();
            assertThat(recruitment.getErhgoOccupation().getId()).isEqualTo(occupation.getId());
            assertThat(recruitment.getLocation().buildDTO()).isEqualTo(location);
            assertThat(recruitment.getJob().getTitle()).isEqualTo(title);
        });
    }

    private void initializeSourcingUser(Recruiter recruiter) {
        txHelper.doInTransaction(() -> {
            when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(new UserRepresentation().setId(USER_ID).setGroups(List.of(ORGANIZATION_CODE)));
            when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID_2)).thenReturn(new UserRepresentation().setId(USER_ID).setGroups(List.of(ORGANIZATION_CODE)));
            applicationContext.getBean(SourcingInvitationMotherObject.class).withUsageForOrganization(applicationContext.getBean(EntityManager.class).merge(recruiter)).buildAndPersist();
        });
    }


    @SneakyThrows
    @ParameterizedTest
    @WithMockKeycloakUser(id = USER_ID, roles = {ORGANIZATION_CODE, Role.SOURCING})
    @ResetDataAfter
    @ValueSource(booleans = {true, false})
    void update_job_refresh_job_if_exists(boolean qualifiedOccupation) {
        var occupation = createOccupation(qualifiedOccupation);
        var organization = applicationContext.getBean(OrganizationGenerator.class).createRecruiter(ORGANIZATION_CODE, AbstractOrganization.OrganizationType.SOURCING);
        prepareUser().setGroups(List.of(ORGANIZATION_CODE));
        var initialJob = applicationContext.getBean(JobMotherObject.class).withCapacities(capacityGenerator.createCapacity("CA-2"), capacityGenerator.createCapacity("CA-3")).withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist()).withLocation(Location.builder().city("Lille").citycode("59850").build()).withRecruiter(organization).buildAndPersist();
        var initialRecruitment = applicationContext.getBean(RecruitmentMotherObject.class).withJob(initialJob).withLocation(Location.builder().city("ailleurs").build()).withState(RecruitmentState.DRAFT).buildAndPersist();
        performPost("/sourcing/recruitment", new CreateSourcingJobAndRecruitmentCommandDTO().recruitmentId(initialRecruitment.getId()).jobOccupationId(occupation.getId()).jobLocation(new LocationDTO().city("Lyon").citycode("69000")).title("title")).andExpect(status().isOk());

        txHelper.doInTransaction(() -> {
            var allJobs = applicationContext.getBean(JobRepository.class).findAll();
            assertThat(allJobs).hasSize(1);
            var job = allJobs.get(0);
            assertThat(job.getRecruiterCode()).isEqualTo(ORGANIZATION_CODE);
            assertThat(job.getLocation().getCity()).isEqualTo("Lyon");
            assertThat(job.getErhgoOccupation().getId()).isEqualTo(OCCUPATION_ID);
            assertThat(job.getAllCapacities().stream().map(Capacity::getCode)).containsExactlyInAnyOrder(CAPACITY_CODE);
            var recruitment = recruitmentRepository.findFirstByRecruitmentProfileJobId(job.getId());
            assertThat(recruitment.getLocation().getCity()).isEqualTo(job.getLocation().getCity());
        });

        verify(notifier).sendMessage(any(SourcingJobOnOccupationMessage.class));
    }

    private UserRepresentation prepareUser() {
        var userRepresentation = new UserRepresentation().setMiscAttributes(Map.of("siret", List.of(SIRET), "fullname", List.of(FULLNAME))).setEmail(EMAIL).setId(USER_ID).setGroups(List.of(ORGANIZATION_CODE));

        when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(userRepresentation);
        return userRepresentation;
    }


    private ErhgoOccupation createOccupation(boolean qualified) {
        return applicationContext.getBean(ErhgoOccupationMotherObject.class).withId(OCCUPATION_ID).withTitle("Caissier / caissière").withDescription("Je suis caissier.").withBehaviorCategory1(BehaviorCategory.RIGOR).withBehaviorCategory2(BehaviorCategory.HONESTY).withBehaviorDescription("Une personne bien").withCapacities(capacityGenerator.createCapacity(CAPACITY_CODE)).qualified(qualified).buildAndPersist();
    }


    @SneakyThrows
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.SOURCING, ORGANIZATION_CODE}, id = USER_ID)
    void updateRecruitmentOccupation(boolean initialRecruitmentHasClassification) {
        var expectedClassificationCodes = new String[]{"SO-06", "SO-07", "SO-04"};
        var updatedOccupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).withCapacities(applicationContext.getBean(CapacityGenerator.class).createCapacity("CA1-2")).withClassificationsCodes(expectedClassificationCodes).buildAndPersist();
        var location = new LocationDTO().city("Laon").citycode("69320").departmentCode("65").regionName("AA").longitude(5.5F).latitude(5.9F);
        var recruitment = createRecruitment(true);
        var recruitmentId = recruitment.getId();
        var recruiter = recruitment.getJob().getRecruiter();

        when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(new UserRepresentation().setId(USER_ID).setGroups(List.of(ORGANIZATION_CODE)));
        txHelper.doInTransaction(() -> applicationContext.getBean(SourcingInvitationMotherObject.class).withUsageForOrganization(applicationContext.getBean(EntityManager.class).merge(recruiter)).buildAndPersist());
        var title = "bim";
        performPost("/sourcing/recruitment", new CreateSourcingJobAndRecruitmentCommandDTO().recruitmentId(recruitmentId).jobOccupationId(updatedOccupation.getId()).jobLocation(location).title(title)).andExpect(status().isOk())
                .andExpect(jsonPath("$.erhgoClassifications[*].code", Matchers.containsInAnyOrder(expectedClassificationCodes)));
        txHelper.doInTransaction(() -> {
            var updatedRecruitment = recruitmentRepository.findById(recruitmentId).orElseThrow();
            assertThat(updatedRecruitment.getErhgoOccupation().getId()).isEqualTo(updatedOccupation.getId());
            assertThat(updatedRecruitment.getErhgoClassifications()).extracting(ErhgoClassification::getCode).containsExactlyInAnyOrder(expectedClassificationCodes);
            assertThat(updatedRecruitment.getRecruitmentProfile().getOptionalActivities()).extracting(OptionalActivity::getActivityLabel).containsExactlyElementsOf(updatedOccupation.getActivities());
        });
    }

    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    @Test
    void createOrUpdateRecruitmentFailsIfOccupationNotFound() {
        createRecruiter();
        when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(new UserRepresentation().setId(USER_ID).setGroups(List.of(ORGANIZATION_CODE)));
        performPost("/sourcing/recruitment", new CreateSourcingJobAndRecruitmentCommandDTO().jobOccupationId(UUID.randomUUID()).title("a").jobLocation(new LocationDTO().city("a"))

        ).andExpect(status().isNotFound());
    }


    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    @Test
    void createOrUpdateRecruitmentFailsIfRecruitmentIsPublished() {
        when(sourcingKeycloakService.getSourcingUserWithGroups(USER_ID)).thenReturn(new UserRepresentation().setId(USER_ID).setGroups(List.of(ORGANIZATION_CODE)));
        var recruitmentId = new AtomicLong();
        txHelper.doInTransaction(() -> {
            var recruitment = createRecruitment(true);
            recruitment.setState(RecruitmentState.PUBLISHED, false);
            recruitmentId.set(recruitment.getId());
            applicationContext.getBean(SourcingInvitationMotherObject.class).withUsageForOrganization(recruitment.getJob().getRecruiter()).buildAndPersist();
        });
        performPost("/sourcing/recruitment", new CreateSourcingJobAndRecruitmentCommandDTO().jobOccupationId(UUID.randomUUID()).title("a").recruitmentId(recruitmentId.get()).jobLocation(new LocationDTO().city("a"))

        ).andExpect(status().isForbidden());
    }

    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void sendMailNowIfOccupationIsNonTechnicalAndQualified(boolean nullLocation) {
        prepareUser().setGroups(List.of(ORGANIZATION_CODE));
        var newTitle = "Nouveau titre !";
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).withTechnical(false).qualified(true).buildAndPersist();
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class).withState(RecruitmentState.DRAFT).withRecruiterCode(ORGANIZATION_CODE).withOccupation(occupation).withLocation(nullLocation ? null : Location.builder().city("Pau").postcode("01230").build()).buildAndPersist();
        applicationContext.getBean(SourcingInvitationMotherObject.class).withGuests(recruitment.getRecruiter()).buildAndPersist();

        performPost("/sourcing/recruitment/%s/change-state".formatted(recruitment.getId()), new ChangeSourcingRecruitmentStateCommandDTO().nextState(RecruitmentStateDTO.PUBLISHED).sendNotifications(UsersToNotifySelectionTypeDTO.ALL).newTitle(nullLocation ? null : newTitle))
                .andExpect(status().isOk());

        var updatedRecruitment = recruitmentRepository.findById(recruitment.getId()).orElseThrow();
        assertThat(updatedRecruitment.getSendNotificationDate()).isCloseTo(Instant.now(), within(2, ChronoUnit.MINUTES));
        assertThat(updatedRecruitment.getSendNotificationState()).isEqualTo(RecruitmentSendNotificationState.WAITING);
        assertThat(updatedRecruitment.getJobTitle()).isEqualTo(nullLocation ? recruitment.getJobTitle() : newTitle);
    }

    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    @Test
    void cancelMailIfRequiredByCommand() {
        prepareUser().setGroups(List.of(ORGANIZATION_CODE));
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class).withState(RecruitmentState.DRAFT).withRecruiterCode(ORGANIZATION_CODE).buildAndPersist();
        applicationContext.getBean(SourcingInvitationMotherObject.class).withGuests(recruitment.getRecruiter()).buildAndPersist();

        performPost("/sourcing/recruitment/%s/change-state".formatted(recruitment.getId()), new ChangeSourcingRecruitmentStateCommandDTO().nextState(RecruitmentStateDTO.PUBLISHED).sendNotifications(UsersToNotifySelectionTypeDTO.NONE)).andExpect(status().isOk());

        var updatedRecruitment = recruitmentRepository.findById(recruitment.getId()).orElseThrow();
        assertThat(updatedRecruitment.getSendNotificationDate()).isNull();
        assertThat(updatedRecruitment.getSendNotificationState()).isEqualTo(RecruitmentSendNotificationState.CANCEL);
    }

    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    @ParameterizedTest
    @EnumSource(UsersToNotifySelectionTypeDTO.class)
    void sendMailOnRepublication(UsersToNotifySelectionTypeDTO sendMailRequired) {
        prepareUser().setGroups(List.of(ORGANIZATION_CODE));

        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).withTechnical(false).qualified(true).buildAndPersist();
        var recruitmentToRepublish = applicationContext.getBean(RecruitmentMotherObject.class).withState(RecruitmentState.SELECTION)
                .withRecruiterCode(ORGANIZATION_CODE).withOccupation(occupation).buildAndPersist();
        when(sourcingUserRepository.getCandidates(any())).thenReturn(List.of(USER_ID_2));
        applicationContext.getBean(UserProfileMotherObject.class).withUserId(USER_ID_2).buildAndPersist();

        performPost("/sourcing/recruitment/%s/change-state".formatted(recruitmentToRepublish.getId()), new ChangeSourcingRecruitmentStateCommandDTO().nextState(RecruitmentStateDTO.PUBLISHED).sendNotifications(sendMailRequired)).andExpect(status().isOk());

        var updatedRecruitment = recruitmentRepository.findById(recruitmentToRepublish.getId()).orElseThrow();
        assertThat(updatedRecruitment.getState()).isEqualTo(RecruitmentState.PUBLISHED);

        if (sendMailRequired != UsersToNotifySelectionTypeDTO.NONE) {
            verify(notifier).sendMessage(assertArg(messageDTO -> {
                var expected = ":recycle: l'organisation Entreprise du S-42 _(version d'essai)_ a republié le recrutement Job with UUID %s à  ????ERREUR???  ( ???Inconnu??? ) - %s - 1 notifications (dont 0 sur mobiles) diffusées immédiatement."
                        .formatted(recruitmentToRepublish.getJob().getId(), sendMailRequired == UsersToNotifySelectionTypeDTO.NEW ? "Notifications envoyées seulement aux nouveaux utilisateurs" : "Notifications renvoyées également aux personnes déjà notifiées");
                Assertions.assertThat(messageDTO.getText()).startsWith(expected);
            }));
            assertThat(updatedRecruitment.getSendNotificationDate()).isNull();
        } else {
            verify(notifier).sendMessage(assertArg(messageDTO -> {
                var expected = ":recycle: l'organisation Entreprise du S-42 _(version d'essai)_ a republié le recrutement Job with UUID %s à  ????ERREUR???  ( ???Inconnu??? ) - L'utilisateur n'a pas souhaité renvoyer de notifications.".formatted(recruitmentToRepublish.getJob().getId());
                Assertions.assertThat(messageDTO.getText()).startsWith(expected);
            }));
            assertThat(updatedRecruitment.getSendNotificationDate()).isNull();
            verifyNoInteractions(mailingListService);
        }
        Mockito.verify(sourcingUserRepository, times(sendMailRequired == UsersToNotifySelectionTypeDTO.NONE ? 1 : 2)).getCandidates(ArgumentMatchers.assertArg(c -> {
            Assertions.assertThat(c.excludesNotified()).isEqualTo(!c.topTen() && sendMailRequired != UsersToNotifySelectionTypeDTO.ALL);
        }));
    }


    @ParameterizedTest
    @ValueSource(booleans = {false, true})
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void getSourcingCandidatesCriteria_includes_occupation_with_rome_and_isco_codes(boolean isTechnical) throws Exception {
        var erhgoOccupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).withTechnical(isTechnical).withRomeAndEscoOccupations().buildAndPersist();

        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class).withOccupation(erhgoOccupation).buildAndPersist();

        var criteria = applicationContext.getBean(SourcingCandidatesCriteria.class).occupationId(erhgoOccupation.getId()).capacitiesIds(new ArrayList<>()).typeContractCategory(TypeContractCategory.PERMANENT).masteryLevelAround(1).addRomeAndIscoCodes(erhgoOccupation).excludesRecruitment(recruitment);

        when(sourcingUserRepository.getCandidates(criteria)).thenReturn(new ArrayList<>());

        performPost("/sourcing/invite-to-recruitment", new InviteToRecruitmentCommandDTO().recruitmentId(recruitment.getId())).andExpect(status().isNoContent());

        Mockito.verify(sourcingUserRepository).getCandidates(any());

    }

    private RomeOccupation generateRomeOccupation(String id) {
        return applicationContext.getBean(ErhgoOccupationGenerator.class).createRomeOccupation("R-%s".formatted(id), "Rome %s".formatted(id));
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void simulateFilters_with_complete_criteria(boolean isTopTen) throws Exception {
        var romeOccupation = generateRomeOccupation("1");
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).withTechnical(true).withRomeOccupations(List.of(romeOccupation)).buildAndPersist();

        var classifications = List.of("SO-01", "SO-09");
        var mandatoryRomeCodes = List.of(romeOccupation.getCode().substring(0, 3));
        var optionalRomeCodes = isTopTen ? List.of(romeOccupation.getCode()) : List.<String>of();
        var masteryLevelAround = 2;
        var masteryMaxCalculated = masteryLevelAround + (isTopTen ? 0.5f : 1d) + SourcingCandidatesCriteria.FLOATING_POINT_PRECISION_ADJUSTMENT;
        var masteryMinCalculated = masteryLevelAround - (isTopTen ? 0.2f : 1d) - SourcingCandidatesCriteria.FLOATING_POINT_PRECISION_ADJUSTMENT;

        var salaryMin = 30000;
        var salaryMax = 40000;

        var salaryMinCalculated = Float.valueOf(salaryMin * 0.7f);
        var salaryMaxCalculated = Float.valueOf(salaryMax * 1.3f);


        var expectedDatetime = LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS);

        var timeStampFromDateTime = expectedDatetime.atZone(ZoneId.of(DateTimeUtils.ZONE)).toEpochSecond();

        var latitude = 43.296482F;
        var longitude = 5.36978F;
        var capacityTolerance = 75.0;

        var criteriaCodes = List.of("REP-3-2", "REP-4-1", "REP-3-4", "REP-5-4", "REP-11-1", "REP-12-1", "REP-12-2", "REP-6-3", "REP-9-1");

        var query = new QueryStringBuilder()
                .addParameter("occupationId", occupation.getId().toString())
                .addParameters("classifications", classifications)
                .addParameters("criteriaCodes", criteriaCodes)
                .addParameter("typeContractCategoryAsString", TypeContractCategory.PERMANENT.name())
                .addParameter("workingTimeTypeAsString", TypeWorkingTime.FULL_TIME.name())
                .addParameter("masteryLevel", Integer.toString(masteryLevelAround))
                .addParameter("salaryMin", Integer.toString(salaryMin))
                .addParameter("salaryMax", Integer.toString(salaryMax))
                .addParameter("longitude", Float.toString(longitude))
                .addParameter("latitude", Float.toString(latitude))
                .addParameter("capacityTolerance", Double.toString(capacityTolerance))
                .addParameter("lastConnectionTimestamp", Long.toString(timeStampFromDateTime))
                .addParameter("activeSearch", "false")
                .addParameter("withDetails", "false")
                .addParameter("forcedTechnical", "true")
                .addParameter("showTopTen", Boolean.toString(isTopTen))
                .build();


        when(sourcingUserRepository.countCandidates(any(SourcingCandidatesCriteria.class))).thenReturn(anyInt());
        mvc.perform(get("/api/odas/sourcing/simulate-filters/%s".formatted(query))).andExpect(status().isOk());

        verify(sourcingUserRepository).countCandidates(assertArg(c ->
                {
                    assertThat(c.occupationId()).isEqualTo(occupation.getId());
                    assertThat(c.classifications()).isEqualTo(classifications);
                    assertThat(c.typeContractCategory()).isEqualTo(TypeContractCategory.PERMANENT);
                    assertThat(c.criteriaForContracts())
                            .containsExactly(CriteriaValue.getValueCodeForTypeContractCategory(TypeContractCategory.PERMANENT));
                    assertThat(c.criteria())
                            .doesNotContain(CriteriaValue.getValueCodeForTypeWorkingTime(TypeWorkingTime.FULL_TIME))
                            .doesNotContain(CriteriaValue.getValueCodeForTypeWorkingTime(TypeWorkingTime.PART_TIME));
                    assertThat(c.criteria()).containsExactlyInAnyOrderElementsOf(criteriaCodes);
                    assertThat(c.salaryMinCalculated()).isEqualTo(salaryMinCalculated);
                    assertThat(c.salaryMaxCalculated()).isEqualTo(salaryMaxCalculated);
                    assertThat(c.latitude()).isEqualTo(latitude);
                    assertThat(c.longitude()).isEqualTo(longitude);
                    assertThat(c.mandatoryRomeCodesPrefix()).containsExactlyInAnyOrderElementsOf(mandatoryRomeCodes);
                    assertThat(c.optionalRomeCodes()).containsExactlyInAnyOrderElementsOf(optionalRomeCodes);
                    assertThat(c.capacityTolerance()).isEqualTo(capacityTolerance);
                    assertThat(c.lastConnectionBefore()).isEqualTo(expectedDatetime);
                    assertThat(c.masteryLevelMax()).isEqualTo(masteryMaxCalculated);
                    assertThat(c.masteryLevelMin()).isEqualTo(masteryMinCalculated);
                    assertThat(c.activeSearch()).isFalse();
                    assertThat(c.withDetails()).isFalse();
                    assertThat(c.topTen()).isEqualTo(isTopTen);
                }
        ));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void simulateFilters_with_empty_criteria() throws Exception {
        when(sourcingUserRepository.countCandidates(any(SourcingCandidatesCriteria.class))).thenReturn(anyInt());
        mvc.perform(get("/api/odas/sourcing/simulate-filters/?withDetails=false&activeSearch=true&forcedTechnical=true&showTopTen=false")).andExpect(status().isOk());
        Mockito.verify(sourcingUserRepository).countCandidates(any(SourcingCandidatesCriteria.class));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void simulateFilters_with_candidate_detail() {
        var user = applicationContext.getBean(UserProfileMotherObject.class).withUserId(USER_ID).withExperience(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist(), 72).withLocation(Location.builder().city("Lyon").build()).withErhgoClassification("SO-01", false).buildAndPersist();
        when(sourcingUserRepository.getCandidatesDetail(any(SourcingCandidatesCriteria.class))).thenReturn(List.of(buildCandidateDetailInterfaceFromUser(user)));
        performGetAndExpect("/sourcing/simulate-filters/?withDetails=true&activeSearch=true&forcedTechnical=true&showTopTen=false", "sourcingCandidateDetails", false);
        verify(sourcingUserRepository).getCandidatesDetail(any(SourcingCandidatesCriteria.class));

    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void simulateFilters_check_working_type_add_correctly_to_criteria() throws Exception {
        var romeCode1 = "A-4242";
        var romeCode2 = "B-5656";

        var rome1 = generateRomeOccupation(romeCode1);
        var rome2 = generateRomeOccupation(romeCode2);
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withTechnical(true)
                .withRomeOccupations(List.of(rome1, rome2))
                .buildAndPersist();


        var query = new QueryStringBuilder()
                .addParameter("occupationId", occupation.getId().toString())
                .addParameter("workingTimeTypeAsString", TypeWorkingTime.PART_TIME.name())
                .addParameter("forcedTechnical", String.valueOf(true))
                .addParameter("activeSearch", String.valueOf(true))
                .addParameter("withDetails", String.valueOf(false))
                .addParameter("showTopTen", String.valueOf(true))
                .build();

        mvc.perform(get("/api/odas/sourcing/simulate-filters/%s".formatted(query)))
                .andExpect(status().isOk());
    }


    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void simulateFilters_with_romes(boolean isTechnical) throws Exception {
        var romeCode1 = "A-4242";
        var romeCode2 = "B-5656";

        var rome1 = generateRomeOccupation(romeCode1);
        var rome2 = generateRomeOccupation(romeCode2);
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withTechnical(isTechnical)
                .withRomeOccupations(List.of(rome1, rome2))
                .buildAndPersist();

        applicationContext.getBean(UserProfileMotherObject.class).buildAndPersist();
        applicationContext.getBean(UserProfileMotherObject.class).buildAndPersist();

        var query = new QueryStringBuilder()
                .addParameter("occupationId", occupation.getId().toString())
                .addParameter("forcedTechnical", String.valueOf(isTechnical))
                .addParameter("activeSearch", String.valueOf(true))
                .addParameter("withDetails", String.valueOf(false))
                .addParameter("showTopTen", String.valueOf(true))
                .build();

        when(sourcingUserRepository.countCandidates(any(SourcingCandidatesCriteria.class))).thenReturn(0);
        mvc.perform(get("/api/odas/sourcing/simulate-filters/%s".formatted(query))).andExpect(status().isOk());

        verify(sourcingUserRepository).countCandidates(assertArg(c ->
                        {
                            assertThat(c.occupationId()).isEqualTo(occupation.getId());
                            assertThat(c.topTen()).isTrue();
                            assertThat(c.mandatoryRomeCodesPrefix()).containsExactlyInAnyOrderElementsOf(
                                    isTechnical ? List.of(rome1.getCode().substring(0, 3), rome2.getCode().substring(0, 3)) : Set.of()
                            );
                            assertThat(c.optionalRomeCodes()).isEqualTo(List.of(rome1.getCode(), rome2.getCode()));
                        }
                )
        );

    }

    @SneakyThrows
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    @ResetDataAfter
    void updateUsersToNotifyOnRecruitment(boolean wasSet) {
        var recruitmentId = applicationContext.getBean(RecruitmentMotherObject.class).withRecruiter(createRecruiter()).withSourcingNotifiedUsersIds(wasSet ? new String[]{USER_ID} : new String[]{}).buildAndPersist().getId();
        performPut("/sourcing/users-to-notify", new UpdateUsersToNotifyCommandDTO(List.of("1", "2", USER_ID_2)).recruitmentId(recruitmentId)).andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            assertThat(recruitmentRepository.findById(recruitmentId).orElseThrow().getSourcingUsersIdToNotify()).containsExactlyInAnyOrder("1", "2", USER_ID_2);
        });
    }

    @SneakyThrows
    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    @ResetDataAfter
    void updateManagerOnRecruitment() {
        var recruitmentId = applicationContext.getBean(RecruitmentMotherObject.class).withRecruiter(createRecruiter()).buildAndPersist().getId();
        performPut("/sourcing/manager", new UpdateManagerCommandDTO(USER_ID_2, recruitmentId)).andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            assertThat(recruitmentRepository.findById(recruitmentId).orElseThrow().getManagerUserId()).isEqualTo(USER_ID_2);
        });
    }

    @SneakyThrows
    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    @ResetDataAfter
    void updateManagerOnRecruitment_unknownUser() {
        var recruitmentId = applicationContext.getBean(RecruitmentMotherObject.class).withRecruiter(createRecruiter()).buildAndPersist().getId();
        performPut("/sourcing/manager", new UpdateManagerCommandDTO("xxx", recruitmentId))
                .andExpect(status().isNotFound());
    }

    @SneakyThrows
    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    @ResetDataAfter
    void updateUsersToNotifyOnOrganization() {
        var users = Stream.of(USER_ID, USER_ID_2, USER_ID_3)
                .map(id -> new UserRepresentation().setId(id).setGroups(List.of(ORGANIZATION_CODE)))
                .peek(u -> when(sourcingKeycloakService.getSourcingUserWithGroups(u.getId())).thenReturn(u))
                .toList();
        when(sourcingKeycloakService.getEnabledSourcingUsersForGroup(ORGANIZATION_CODE)).thenReturn(users);
        createRecruiter();
        applicationContext.getBean(SourcingPreferencesMotherObject.class).withUserId(USER_ID_3).withNotifiedOnSpontaneousCandidatures(false).buildAndPersist();
        applicationContext.getBean(SourcingPreferencesMotherObject.class).withUserId(USER_ID_2).withNotifiedOnSpontaneousCandidatures(true).buildAndPersist();
        performPut("/sourcing/users-to-notify", new UpdateUsersToNotifyCommandDTO(List.of(USER_ID_3, USER_ID))).andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            assertThat(applicationContext.getBean(SourcingPreferencesRepository.class).findAll())
                    .hasSize(3)
                    .anyMatch(p -> p.userId().equals(USER_ID) && p.notifyOnSpontaneousCandidature())
                    .anyMatch(p -> p.userId().equals(USER_ID_2) && !p.notifyOnSpontaneousCandidature())
                    .anyMatch(p -> p.userId().equals(USER_ID_3) && p.notifyOnSpontaneousCandidature())
            ;
        });
    }

    @ResetDataAfter
    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING, ORGANIZATION_CODE})
    void getUsersToNotify() {
        var users = Stream.of(USER_ID, USER_ID_2, USER_ID_3)
                .map(id -> new UserRepresentation().setId(id).setGroups(List.of(ORGANIZATION_CODE)))
                .peek(u -> when(sourcingKeycloakService.getSourcingUserWithGroups(u.getId())).thenReturn(u))
                .toList();
        when(sourcingKeycloakService.getEnabledSourcingUsersForGroup(ORGANIZATION_CODE)).thenReturn(users);
        createRecruiter();

        applicationContext.getBean(SourcingPreferencesMotherObject.class).withUserId(USER_ID_3).withNotifiedOnSpontaneousCandidatures(false).buildAndPersist();
        applicationContext.getBean(SourcingPreferencesMotherObject.class).withUserId(USER_ID_2).withNotifiedOnSpontaneousCandidatures(true).buildAndPersist();
        performGetAndExpect("/sourcing/spontaneous-candidature/users-to-notify", "sourcingUsersToNotifyOnSpontaneousCandidatures", false);
    }


    private CandidateDetailDTO buildCandidateDetailInterfaceFromUser(UserProfile user) {
        return new CandidateDetailDTO() {
            public String getId() {
                return user.userId();
            }

            public String getCity() {
                return user.getCity();
            }

            public Float getMasteryLevelAsFloat() {
                return 2f;
            }

            public Integer getRomeExperiencesCount() {
                return 1;
            }

            public Float getCapacityScore() {
                return 5f;
            }

            public String getRefusedClassifications() {
                return user.getRefusedErhgoClassifications().stream().map(ErhgoClassification::getCode).collect(Collectors.joining(","));
            }
        };

    }
}

class QueryStringBuilder {
    String queryString;

    public QueryStringBuilder addParameter(String key, String value) {
        if (this.queryString != null) {
            this.queryString += "&%s=%s".formatted(key, value);
            return this;
        }
        this.queryString = "?%s=%s".formatted(key, value);
        return this;
    }

    public QueryStringBuilder addParameters(String key, List<String> values) {
        values.forEach(v -> addParameter(key, v));
        return this;
    }

    public String build() {
        return this.queryString;
    }
}
