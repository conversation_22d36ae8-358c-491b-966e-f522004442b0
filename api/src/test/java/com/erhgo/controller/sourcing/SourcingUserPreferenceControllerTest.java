package com.erhgo.controller.sourcing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.enums.CandidatureState;
import com.erhgo.domain.externaloffer.ExternalOfferMotherObject;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.sourcing.SourcingPreferences;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.SourcingPreferencesMotherObject;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.generators.RecruiterMotherObject;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.SourcingPreferencesRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.keycloak.SourcingKeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailingListService;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Stream;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;

class SourcingUserPreferenceControllerTest extends AbstractIntegrationTest {

    static final String USER_ID = "42";
    static final String USER_ID_FO = "342";


    public static final UserRepresentation USER_REPRESENTATION = new UserRepresentation()
            .setMiscAttributes(Map.of(UserRepresentation.FULLNAME_ATTRIBUTE, List.of("Patrick Strom"), UserRepresentation.PHONE_ATTRIBUTE, List.of("0321")))
            .setEmail("A@A")
            .setId(USER_ID);
    @MockitoBean
    MailingListService mailingListService;
    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    SourcingKeycloakService sourcingKeycloakService;
    @Autowired
    SourcingPreferencesRepository sourcingPreferencesRepository;

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING})
    void getUserRetrievesWithoutPreferences() {
        Mockito.when(sourcingKeycloakService.getSourcingUser(USER_ID)).thenReturn(Optional.of(USER_REPRESENTATION));
        performGetAndExpect("/sourcing/user", "sourcingUser", false);
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING})
    @ResetDataAfter
    void getUserRetrievesWithPreferences() {
        applicationContext.getBean(SourcingPreferencesMotherObject.class)
                .withUserId(USER_ID)
                .withMailFrequency(SourcingPreferences.MailFrequency.WEEKLY)
                .withIsoWeekDay(6)
                .buildAndPersist();
        Mockito.when(sourcingKeycloakService.getSourcingUser(USER_ID)).thenReturn(Optional.of(USER_REPRESENTATION));
        performGetAndExpect("/sourcing/user", "sourcingUserWithPreferences", false);
    }

    @SneakyThrows
    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING})
    @ResetDataAfter
    void defineNewUserPreferences() {
        var email = "up@date";
        var phone = "00";
        var fullname = "a v";
        Mockito.when(sourcingKeycloakService.getSourcingUser(USER_ID)).thenReturn(Optional.of(USER_REPRESENTATION));
        performPut("/sourcing/user", new UpdateSourcingUserCommandDTO()
                .email(email)
                .fullname(fullname)
                .phone(phone)
                .preferences(new SourcingPreferencesDTO()
                        .isoWeekDay(5).mailFrequency(SourcingPreferencesDTO.MailFrequencyEnum.WEEKLY)))
                .andExpect(MockMvcResultMatchers.status().isNoContent());
        var preferences = sourcingPreferencesRepository.findByUserId(USER_ID).orElseThrow();
        Assertions.assertThat(preferences.mailFrequency()).isEqualTo(SourcingPreferences.MailFrequency.WEEKLY);
        Assertions.assertThat(preferences.isoWeekDay()).isEqualTo(5);
        Mockito.verify(sourcingKeycloakService).updateUser(USER_ID, email, phone, fullname);
    }

    @SneakyThrows
    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING})
    @ResetDataAfter
    void updateUserPreferences() {
        var email = "up@date";
        var phone = "00";
        var fullname = "a v";
        applicationContext.getBean(SourcingPreferencesMotherObject.class)
                .withUserId(USER_ID)
                .withIsoWeekDay(3)
                .withMailFrequency(SourcingPreferences.MailFrequency.WEEKLY)
                .buildAndPersist();
        Mockito.when(sourcingKeycloakService.getSourcingUser(USER_ID)).thenReturn(Optional.of(USER_REPRESENTATION));
        performPut("/sourcing/user", new UpdateSourcingUserCommandDTO()
                .email(email)
                .fullname(fullname)
                .phone(phone)
                .preferences(new SourcingPreferencesDTO()
                        .isoWeekDay(null).mailFrequency(SourcingPreferencesDTO.MailFrequencyEnum.DAILY)))
                .andExpect(MockMvcResultMatchers.status().isNoContent());
        var preferences = sourcingPreferencesRepository.findByUserId(USER_ID).orElseThrow();
        Assertions.assertThat(preferences.mailFrequency()).isEqualTo(SourcingPreferences.MailFrequency.DAILY);
        Assertions.assertThat(preferences.isoWeekDay()).isNull();
        Mockito.verify(sourcingKeycloakService).updateUser(USER_ID, email, phone, fullname);
    }

    @SneakyThrows
    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = {Role.SOURCING})
    @ResetDataAfter
    void deleteUserPreferences() {
        var email = "up@date";
        var phone = "00";
        var fullname = "a v";
        applicationContext.getBean(SourcingPreferencesMotherObject.class)
                .withUserId(USER_ID)
                .buildAndPersist();
        Mockito.when(sourcingKeycloakService.getSourcingUser(USER_ID)).thenReturn(Optional.of(USER_REPRESENTATION));
        performPut("/sourcing/user", new UpdateSourcingUserCommandDTO()
                .email(email)
                .fullname(fullname)
                .phone(phone))
                .andExpect(MockMvcResultMatchers.status().isNoContent());
        sourcingPreferencesRepository.findByUserId(USER_ID).ifPresent(a -> Assertions.fail("preferences %s should be deleted".formatted(a.toString())));

        Mockito.verify(sourcingKeycloakService).updateUser(USER_ID, email, phone, fullname);
    }

    @SneakyThrows
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.CANDIDATE, id = USER_ID_FO)
    void publishSourcingRecruitmentCandidatureNotifyLaterUser(boolean wasPublished) {
        AtomicReference<Long> candidatureIdRef = new AtomicReference<>();
        txHelper.doInTransaction(() -> {
            var userProfile = applicationContext.getBean(UserProfileMotherObject.class).withUserId(USER_ID_FO).withEmail("a@fo").buildAndPersist();
            var userIdToNotifyImmediately = "1";
            var userIdNotToNotifyBecauseNotOnCandidatureRecruitment = "6";
            var userIdNotToNotifyDaily = "2";
            var userIdNotToNotifyWeekly = "3";
            var userIdNotToNotifyNoPref = "4";
            var userIdNeverNotified = "5";
            var erroneousUserIdNotOnOrganization = "7";

            applicationContext.getBean(SourcingPreferencesMotherObject.class)
                    .withUserId(userIdToNotifyImmediately)
                    .withMailFrequency(SourcingPreferences.MailFrequency.IMMEDIATELY)
                    .buildAndPersist();
            applicationContext.getBean(SourcingPreferencesMotherObject.class)
                    .withUserId(erroneousUserIdNotOnOrganization)
                    .withMailFrequency(SourcingPreferences.MailFrequency.IMMEDIATELY)
                    .buildAndPersist();
            applicationContext.getBean(SourcingPreferencesMotherObject.class)
                    .withUserId(userIdNotToNotifyBecauseNotOnCandidatureRecruitment)
                    .withMailFrequency(SourcingPreferences.MailFrequency.IMMEDIATELY)
                    .buildAndPersist();
            applicationContext.getBean(SourcingPreferencesMotherObject.class)
                    .withUserId(userIdNotToNotifyDaily)
                    .withMailFrequency(SourcingPreferences.MailFrequency.DAILY)
                    .buildAndPersist();
            applicationContext.getBean(SourcingPreferencesMotherObject.class)
                    .withUserId(userIdNotToNotifyWeekly)
                    .withMailFrequency(SourcingPreferences.MailFrequency.WEEKLY)
                    .buildAndPersist();
            applicationContext.getBean(SourcingPreferencesMotherObject.class)
                    .withUserId(userIdNeverNotified)
                    .withMailFrequency(SourcingPreferences.MailFrequency.NEVER)
                    .buildAndPersist();

            var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                    .withLocation(Location.builder().city("Pau").build())
                    .withJobTitle("Pâtissier")
                    .withRecruiterType(AbstractOrganization.OrganizationType.SOURCING)
                    .withSourcingNotifiedUsersIds(userIdToNotifyImmediately, userIdNotToNotifyDaily, userIdNotToNotifyNoPref, userIdNotToNotifyWeekly, userIdNeverNotified, erroneousUserIdNotOnOrganization)
                    .buildAndPersist();

            var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                    .withState(wasPublished ? GlobalCandidatureState.NOT_FINALIZED : GlobalCandidatureState.NOT_TREATED_BY_ERHGO)
                    .withCandidatureState(wasPublished ? CandidatureState.VALIDATED : CandidatureState.STARTED)
                    .withRecruitment(recruitment)
                    .withUserProfile(userProfile)
                    .buildAndPersist();

            Mockito.when(sourcingKeycloakService.getEnabledSourcingUsersForGroup(candidature.getCodeOfRecruiter())).thenReturn(Stream.of(
                    userIdToNotifyImmediately,
                    userIdNotToNotifyBecauseNotOnCandidatureRecruitment,
                    userIdNotToNotifyDaily,
                    userIdNotToNotifyWeekly,
                    userIdNeverNotified,
                    userIdNotToNotifyNoPref
            ).map(id -> new UserRepresentation().setId(id).setEmail("%s@erhgo".formatted(id)).setMiscAttributes(Map.of(UserRepresentation.FULLNAME_ATTRIBUTE, List.of("Marcel %s".formatted(id))))).toList());
            candidatureIdRef.set(candidature.getId());
        });
        performPost("/candidature/publish", new PublishCandidatureCommandDTO().candidatureId(candidatureIdRef.get()))
                .andExpect(MockMvcResultMatchers.status().isNoContent());

        Mockito.verify(mailingListService, Mockito.times(wasPublished ? 0 : 1)).sendMailsForTemplate(anySet(), anyLong(), anyMap(), any());
    }

    @SneakyThrows
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.CANDIDATE, id = USER_ID_FO)
    void publishSourcingRecruitmentCandidatureNotifyLaterUserWithExternalOffer(boolean isExternal) {
        AtomicReference<Long> candidatureIdRef = new AtomicReference<>();
        var userIdToNotifyLater = "1";
        var emailToNotifyLater = userIdToNotifyLater + "@erhgo";

        txHelper.doInTransaction(() -> {
            var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                    .withLocation(Location.builder().city("Pau").build())
                    .withJobTitle("Pâtissier")
                    .withRecruiterType(AbstractOrganization.OrganizationType.SOURCING)
                    .withSourcingNotifiedUsersIds(userIdToNotifyLater)
                    .buildAndPersist();

            if (isExternal) {
                recruitment.setExternalOffer(applicationContext.getBean(ExternalOfferMotherObject.class).withRecruitment(recruitment).buildAndPersist());
            }
            applicationContext.getBean(SourcingPreferencesMotherObject.class)
                    .withUserId(userIdToNotifyLater)
                    .withMailFrequency(SourcingPreferences.MailFrequency.IMMEDIATELY)
                    .buildAndPersist();
            var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                    .withState(GlobalCandidatureState.NOT_FINALIZED)
                    .withCandidatureState(CandidatureState.STARTED)
                    .withRecruitment(recruitment)
                    .withUserProfile(applicationContext.getBean(UserProfileMotherObject.class).withUserId(USER_ID_FO).withEmail("a@fo").buildAndPersist())
                    .buildAndPersist();


            Mockito.when(sourcingKeycloakService.getEnabledSourcingUsersForGroup(candidature.getCodeOfRecruiter())).thenReturn(List.of(
                    new UserRepresentation().setId(userIdToNotifyLater).setEmail(emailToNotifyLater).setMiscAttributes(Map.of(UserRepresentation.FULLNAME_ATTRIBUTE, List.of("Marcel " + userIdToNotifyLater)))
            ));
            candidatureIdRef.set(candidature.getId());
        });

        performPost("/candidature/publish", new PublishCandidatureCommandDTO().candidatureId(candidatureIdRef.get()))
                .andExpect(MockMvcResultMatchers.status().isNoContent());


        Mockito.verify(mailingListService, times(1)).sendMailsForTemplate(anySet(), anyLong(), anyMap(), isNull());

    }

    @SneakyThrows
    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.CANDIDATE, id = USER_ID_FO)
    void publishSourcingSpontaneousCandidatureNotifyImmediatelyUser() {
        AtomicReference<String> orgaCodeRef = new AtomicReference<>();
        txHelper.doInTransaction(() -> {
            var userProfile = applicationContext.getBean(UserProfileMotherObject.class).withUserId(USER_ID_FO).withEmail("a@fo").buildAndPersist();
            var userIdToNotify = "1";
            var userIdNotToNotifyDaily = "2";
            var userIdNotToNotifyWeekly = "3";
            var userIdNotToNotifyNoPref = "4";
            var userIdNeverNotified = "5";
            var userIdNotToNotifyNoSpontaneous = "6";
            applicationContext.getBean(SourcingPreferencesMotherObject.class)
                    .withUserId(userIdToNotify)
                    .withMailFrequency(SourcingPreferences.MailFrequency.IMMEDIATELY)
                    .withNotifiedOnSpontaneousCandidatures(true)
                    .buildAndPersist();
            applicationContext.getBean(SourcingPreferencesMotherObject.class)
                    .withUserId(userIdNotToNotifyNoSpontaneous)
                    .withMailFrequency(SourcingPreferences.MailFrequency.IMMEDIATELY)
                    .withNotifiedOnSpontaneousCandidatures(false)
                    .buildAndPersist();
            applicationContext.getBean(SourcingPreferencesMotherObject.class)
                    .withUserId(userIdNotToNotifyDaily)
                    .withMailFrequency(SourcingPreferences.MailFrequency.DAILY)
                    .buildAndPersist();
            applicationContext.getBean(SourcingPreferencesMotherObject.class)
                    .withUserId(userIdNotToNotifyWeekly)
                    .withMailFrequency(SourcingPreferences.MailFrequency.WEEKLY)
                    .buildAndPersist();
            applicationContext.getBean(SourcingPreferencesMotherObject.class)
                    .withUserId(userIdNeverNotified)
                    .withMailFrequency(SourcingPreferences.MailFrequency.NEVER)
                    .buildAndPersist();
            orgaCodeRef.set(applicationContext.getBean(RecruiterMotherObject.class).withOrganizationType(AbstractOrganization.OrganizationType.SOURCING).buildAndPersist().getCode());
            Mockito.when(sourcingKeycloakService.getEnabledSourcingUsersForGroup(orgaCodeRef.get())).thenReturn(Stream.of(
                    userIdToNotify,
                    userIdNotToNotifyDaily,
                    userIdNotToNotifyWeekly,
                    userIdNeverNotified,
                    userIdNotToNotifyNoPref
            ).map(id -> new UserRepresentation().setId(id).setEmail("%s@erhgo".formatted(id)).setMiscAttributes(Map.of(UserRepresentation.FULLNAME_ATTRIBUTE, List.of("Marcel %s".formatted(id))))).toList());
        });
        performPut("/user/initialize-profile", new InitializeProfileCommandDTO().channelSource(UserChannelSourceDTO.ENTERPRISE_PAGE).value(List.of(orgaCodeRef.get()))).andExpect(MockMvcResultMatchers.status().isOk());
        Mockito.verify(mailingListService, never()).sendMailsForTemplate(
                anySet(),
                anyInt(),
                anyMap(),
                any());
    }
}
