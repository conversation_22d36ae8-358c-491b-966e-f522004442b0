package com.erhgo.controller.sourcing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.job.SpontaneousCandidatureMotherObject;
import com.erhgo.domain.landingpage.LandingPage;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.sector.Sector;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.generators.RecruiterMotherObject;
import com.erhgo.openapi.dto.InitializeProfileCommandDTO;
import com.erhgo.openapi.dto.SourcingCandidatureSortDTO;
import com.erhgo.openapi.dto.UpdateSectorForSpontaneousCandidatureCommandDTO;
import com.erhgo.openapi.dto.UserChannelSourceDTO;
import com.erhgo.repositories.LandingPageRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.utils.DateTimeUtils;
import lombok.SneakyThrows;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.IntStream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.core.Is.is;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class SpontaneousCandidatureControllerTest extends AbstractIntegrationTest {
    private static final String USER_ID = "3c2a6fbf-e563-4b0b-9ae0-af62cfa12f8d";
    static final String ORGA_CODE = "S-4242";

    @MockitoBean
    private KeycloakMockService keycloakService;

    @Autowired
    ApplicationContext applicationContext;

    private void setKeycloakContext() {
        var userRepresentation = createUserRepresentation();

        Mockito.when(keycloakService.getFrontOfficeUserProfileWithGroups(USER_ID)).thenReturn(Optional.of(userRepresentation));
        Mockito.when(keycloakService.getFrontOfficeUserProfile(USER_ID)).thenReturn(Optional.of(userRepresentation));
    }

    @NotNull
    private UserRepresentation createUserRepresentation() {
        final var email = "<EMAIL>";
        final var firstName = "tester";
        final var lastName = "Sogilis";

        var userRepresentation = new UserRepresentation();
        userRepresentation.setEmail(email);
        userRepresentation.setFirstName(firstName);
        userRepresentation.setLastName(lastName);
        userRepresentation.setId(USER_ID);
        return userRepresentation;
    }

    @ParameterizedTest
    @EnumSource
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    void assign_to_sourcing_group_generates_spontaneous_candidature_or_not(UserChannelSourceDTO source) throws Exception {
        setKeycloakContext();
        var sourcingOrgaCode = "S-05";
        txHelper.doInTransaction(() -> {
            var sourcingOrga = applicationContext.getBean(RecruiterMotherObject.class).withOrganizationType(AbstractOrganization.OrganizationType.SOURCING).withCode(sourcingOrgaCode).buildAndPersist();
            applicationContext.getBean(LandingPageRepository.class).save(LandingPage
                    .builder()
                    .id(UUID.randomUUID())
                    .urlKey(sourcingOrgaCode)
                    .organizations(Set.of(sourcingOrga))
                    .content("content")
                    .build());
        });
        performPut("/user/initialize-profile", new InitializeProfileCommandDTO().channelSource(source).value(List.of(sourcingOrgaCode))).andExpect(status().isOk());
        txHelper.doInTransaction(() -> {
            var candidatures = applicationContext.getBean(SpontaneousCandidatureRepository.class).findAll();
            assertThat(candidatures)
                    .hasSize((source == UserChannelSourceDTO.ENTERPRISE_PAGE) ? 1 : 0)
                    .allMatch(c -> c.getCodeOfRecruiter().equals(sourcingOrgaCode))
                    .allMatch(c -> c.getUserId().equals(USER_ID))
                    .allMatch(c -> c.getSubmissionDate() != null)
            ;
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    void assign_to_sourcing_group_updates_previous_candidature_submission_date() throws Exception {
        setKeycloakContext();
        var sourcingOrga = applicationContext.getBean(RecruiterMotherObject.class).withOrganizationType(AbstractOrganization.OrganizationType.SOURCING).buildAndPersist();
        var initialPublicationDate = OffsetDateTime.of(2023, 12, 31, 10, 10, 10, 10, DateTimeUtils.zoneOffset());
        applicationContext.getBean(SpontaneousCandidatureMotherObject.class).withRecruiter(sourcingOrga).withUserId(USER_ID).withSubmissionDate(initialPublicationDate).buildAndPersist();
        performPut("/user/initialize-profile", new InitializeProfileCommandDTO().channelSource(UserChannelSourceDTO.ENTERPRISE_PAGE).value(List.of(sourcingOrga.getCode()))).andExpect(status().isOk());
        txHelper.doInTransaction(() -> {
            var candidatures = applicationContext.getBean(SpontaneousCandidatureRepository.class).findAll();
            assertThat(candidatures)
                    .hasSize(1)
                    .allMatch(c -> c.getCodeOfRecruiter().equals(sourcingOrga.getCode()))
                    .allMatch(c -> c.getUserId().equals(USER_ID))
                    .allMatch(c -> c.getSubmissionDate().isAfter(initialPublicationDate))
            ;
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.SOURCING, ORGA_CODE})
    void list_all_candidates() {
        txHelper.doInTransaction(() -> {
            setKeycloakContext();
            var sourcingOrga = applicationContext.getBean(RecruiterMotherObject.class).withCode(ORGA_CODE).withOrganizationType(AbstractOrganization.OrganizationType.SOURCING).buildAndPersist();
            var user = applicationContext.getBean(UserProfileMotherObject.class).withUserId(USER_ID)
                    .withChannels(ORGA_CODE)
                    .withTrimojiPdfUrl("pdf://trimoji")
                    .withLocation(Location.builder().departmentCode("55").build())
                    .buildAndPersist();

            applicationContext.getBean(SpontaneousCandidatureMotherObject.class)
                    .withAnonymousCode("c")
                    .withColor("a")
                    .withCustomSectors(List.of("B", "C", "D"))
                    .withReferentialSectorCodes(Set.of("C-06", "C-07"))
                    .withRecruiter(sourcingOrga)
                    .withSubmissionDate(OffsetDateTime.of(2020, 10, 3, 5, 5, 5, 5, DateTimeUtils.zoneOffset())).withUserProfile(user)
                    .buildAndPersist();

            applicationContext.getBean(SpontaneousCandidatureMotherObject.class)
                    .withRecruiter(sourcingOrga)
                    .withIsArchived(true)
                    .withSubmissionDate(OffsetDateTime.of(2020, 10, 3, 5, 5, 5, 5, DateTimeUtils.zoneOffset())).withUserProfile(user)
                    .buildAndPersist();
        });
        performGetAndExpect("/sourcing/candidatures?size=42&page=0", "sourcingCandidates", true);
    }


    @ParameterizedTest
    @EnumSource
    @NullSource
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.SOURCING, ORGA_CODE})
    void list_all_candidates_with_recruitment_candidature(SourcingCandidatureSortDTO sort) {
        txHelper.doInTransaction(() -> {
            setKeycloakContext();
            var sourcingOrga = applicationContext.getBean(RecruiterMotherObject.class).withCode(ORGA_CODE).withOrganizationType(AbstractOrganization.OrganizationType.SOURCING).buildAndPersist();
            var u1 = applicationContext.getBean(UserProfileMotherObject.class).withUserId(USER_ID).withChannels(ORGA_CODE).withLocation(Location.builder().departmentCode("66").radiusInKm(600).build()).buildAndPersist();
            var u2 = applicationContext.getBean(UserProfileMotherObject.class).withUserId("u2").withChannels(ORGA_CODE).buildAndPersist();
            var userProfileWithArchivedCandidature = applicationContext.getBean(UserProfileMotherObject.class).withUserId("u3").withChannels(ORGA_CODE).buildAndPersist();

            // u1
            applicationContext.getBean(SpontaneousCandidatureMotherObject.class)
                    .withAnonymousCode("c")
                    .withColor("a")
                    .withRecruiter(sourcingOrga)
                    .withSubmissionDate(OffsetDateTime.of(2020, 10, 3, 5, 5, 5, 5, DateTimeUtils.zoneOffset()))
                    .withUserProfile(u1)
                    .withLastActionDate(OffsetDateTime.of(2020, 5, 5, 5, 5, 5, 5, DateTimeUtils.zoneOffset()))
                    .buildAndPersist();
            applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                    .withAnonymousCode("c3")
                    .withColor("a3")
                    .withUserProfile(u2)
                    .withState(GlobalCandidatureState.NEW)
                    .withRecruiter(sourcingOrga)
                    .withSubmissionDate(OffsetDateTime.of(2023, 10, 3, 5, 5, 5, 5, DateTimeUtils.zoneOffset()))
                    .withUserProfile(u1).buildAndPersist();


            // u2
            applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                    .withAnonymousCode("ignored")
                    .withColor("ignored")
                    .withState(GlobalCandidatureState.NEW)
                    .withRecruiter(sourcingOrga)
                    .withSubmissionDate(OffsetDateTime.of(2022, 10, 3, 5, 5, 5, 5, DateTimeUtils.zoneOffset()))
                    .withUserProfile(u1).buildAndPersist();
            applicationContext.getBean(SpontaneousCandidatureMotherObject.class)
                    .withAnonymousCode("c")
                    .withColor("a")
                    .withRecruiter(sourcingOrga)
                    .withSubmissionDate(OffsetDateTime.of(2021, 10, 3, 5, 5, 5, 5, DateTimeUtils.zoneOffset()))
                    .withUserProfile(u2)
                    .withLastActionDate(OffsetDateTime.of(2019, 10, 3, 5, 5, 5, 5, DateTimeUtils.zoneOffset()))
                    .buildAndPersist();
            applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                    .withAnonymousCode("c2")
                    .withColor("a2")
                    .withState(GlobalCandidatureState.NEW)
                    .withUserProfile(u2)
                    .withRecruiter(sourcingOrga)
                    .withSubmissionDate(OffsetDateTime.of(2022, 10, 3, 5, 5, 5, 5, DateTimeUtils.zoneOffset()))
                    .generated(true)
                    .buildAndPersist();

            // On all other states
            IntStream.range(0, GlobalCandidatureState.values().length).forEach(i -> {
                var user = applicationContext.getBean(UserProfileMotherObject.class).withUserId("ux%d".formatted(i)).withChannels(ORGA_CODE).buildAndPersist();

                applicationContext.getBean(SpontaneousCandidatureMotherObject.class)
                        .withAnonymousCode("an%d".formatted(i))
                        .withColor("c%d".formatted(i))
                        .withState(GlobalCandidatureState.values()[i])
                        .withRecruiter(sourcingOrga)
                        .withSubmissionDate(OffsetDateTime.of(2022 + i, 10, 3, 5, 5, 5, 5, DateTimeUtils.zoneOffset()))
                        .withUserProfile(user)
                        .withLastActionDate(OffsetDateTime.of(2021, 8, 31 - i, 5, 5, 5, 5, DateTimeUtils.zoneOffset()))
                        .buildAndPersist();
            });
            // userProfile with archived candidature
            applicationContext.getBean(SpontaneousCandidatureMotherObject.class)
                    .withRecruiter(sourcingOrga)
                    .withSubmissionDate(OffsetDateTime.now())
                    .withUserProfile(userProfileWithArchivedCandidature)
                    .withIsArchived(true)
                    .buildAndPersist();

        });
        performGetAndExpect("/sourcing/candidatures?size=42&page=0" + (sort == null ? "" : "&sourcingCandidatureSort=%s".formatted(sort.getValue())), "sourcingCandidatesWithRecruitmentCandidature_asc_%s".formatted(sort == null ? "" : sort.getValue()), true);
        performGetAndExpect("/sourcing/candidatures?size=42&page=0" + (sort == null ? "" : "&sourcingCandidatureSort=%s&direction=DESC".formatted(sort.getValue())), "sourcingCandidatesWithRecruitmentCandidature_desc_%s".formatted(sort == null ? "" : sort.getValue()), true);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.SOURCING, ORGA_CODE})
    @SneakyThrows
    void query_search_empty_result_is_OK_ERHGO_1882() {
        setKeycloakContext();
        applicationContext.getBean(RecruiterMotherObject.class).withCode(ORGA_CODE).withOrganizationType(AbstractOrganization.OrganizationType.SOURCING).buildAndPersist();
        mvc.perform(get(realUrl("/sourcing/candidatures?size=42&page=0&searchQuery=yolo")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("numberOfElementsInPage", is(0)));
    }

    @ParameterizedTest
    @ValueSource(booleans = {false, true})
    @ResetDataAfter
    @NullSource
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @SneakyThrows
    void set_custom_sectors(Boolean withPreviousSector) {
        var expectedSectors = List.of("a", "b");
        setKeycloakContext();

        var recruiter = applicationContext.getBean(RecruiterMotherObject.class).withCode(ORGA_CODE).withOrganizationType(AbstractOrganization.OrganizationType.SOURCING).buildAndPersist();
        if (withPreviousSector != null) {
            var builder = applicationContext.getBean(SpontaneousCandidatureMotherObject.class).withRecruiter(recruiter).withUserId(USER_ID);
            if (withPreviousSector) {
                builder.withCustomSectors(List.of("b", "c", "d")).withReferentialSectorCodes(Set.of("C-06", "C-07"));
            }
            builder.buildAndPersist();
        } else {
            applicationContext.getBean(UserProfileMotherObject.class).withUserId(USER_ID).buildAndPersist();
        }
        performPut("/candidature/for-organization/%s/sectors".formatted(ORGA_CODE), new UpdateSectorForSpontaneousCandidatureCommandDTO().customSectors(expectedSectors)).andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var candidatures = applicationContext.getBean(SpontaneousCandidatureRepository.class).findAll();
            assertThat(candidatures).hasSize(1);
            var candidature = candidatures.iterator().next();
            assertThat(candidature.getCustomSectors()).containsExactlyInAnyOrderElementsOf(expectedSectors);
            assertThat(candidature.getReferentialSectors()).isEmpty();
        });
    }

    @ParameterizedTest
    @ValueSource(booleans = {false, true})
    @ResetDataAfter
    @NullSource
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @SneakyThrows
    void set_referential_sectors(Boolean withPreviousSector) {
        var expectedSectors = List.of("C-07", "C-08");
        setKeycloakContext();
        var recruiter = applicationContext.getBean(RecruiterMotherObject.class).withCode(ORGA_CODE).withOrganizationType(AbstractOrganization.OrganizationType.SOURCING).buildAndPersist();

        if (withPreviousSector != null) {
            var builder = applicationContext.getBean(SpontaneousCandidatureMotherObject.class).withRecruiter(recruiter).withUserId(USER_ID);
            if (withPreviousSector) {
                builder.withCustomSectors(List.of("b", "c", "d")).withReferentialSectorCodes(Set.of("C-06", "C-07"));
            }
            builder.buildAndPersist();
        } else {
            applicationContext.getBean(UserProfileMotherObject.class).withUserId(USER_ID).buildAndPersist();
        }
        performPut("/candidature/for-organization/%s/sectors".formatted(ORGA_CODE), new UpdateSectorForSpontaneousCandidatureCommandDTO().referentialSectors(expectedSectors)).andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var candidatures = applicationContext.getBean(SpontaneousCandidatureRepository.class).findAll();
            assertThat(candidatures).hasSize(1);
            var candidature = candidatures.iterator().next();
            assertThat(candidature.getReferentialSectors()).extracting(Sector::getCode).containsExactlyInAnyOrderElementsOf(expectedSectors);
            assertThat(candidature.getCustomSectors()).isEmpty();
        });
    }


    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @SneakyThrows
    void set_referential_sectors_both_and_normalize() {
        var expectedSectors1 = List.of("C-07", "C-08");
        var expectedSectors2 = List.of("aA", "Bb");
        setKeycloakContext();
        var recruiter = applicationContext.getBean(RecruiterMotherObject.class).withCode(ORGA_CODE).withOrganizationType(AbstractOrganization.OrganizationType.SOURCING).buildAndPersist();
        applicationContext.getBean(SpontaneousCandidatureMotherObject.class).withRecruiter(recruiter).withUserId(USER_ID).withCustomSectors(List.of("B", "C", "D")).withReferentialSectorCodes(Set.of("C-06", "C-07")).buildAndPersist();
        performPut("/candidature/for-organization/%s/sectors".formatted(ORGA_CODE), new UpdateSectorForSpontaneousCandidatureCommandDTO().referentialSectors(expectedSectors1).customSectors(expectedSectors2)).andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var candidatures = applicationContext.getBean(SpontaneousCandidatureRepository.class).findAll();
            assertThat(candidatures).hasSize(1);
            var candidature = candidatures.iterator().next();
            assertThat(candidature.getReferentialSectors()).extracting(Sector::getCode).containsExactlyInAnyOrderElementsOf(expectedSectors1);
            assertThat(candidature.getCustomSectors()).containsExactlyInAnyOrderElementsOf(List.of("aa", "bb"));

        });
    }
}
