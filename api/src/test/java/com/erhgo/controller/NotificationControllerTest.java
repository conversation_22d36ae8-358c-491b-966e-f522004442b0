package com.erhgo.controller;

import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.enums.NotificationState;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.userprofile.UserNotificationMotherObject;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.repositories.NotificationRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.assertj.core.api.Assertions;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.web.servlet.MockMvc;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class NotificationControllerTest extends AbstractControllerTest {
    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    NotificationRepository notificationRepository;

    @Autowired
    protected MockMvc mvc;

    private static final String USER_ID = "aa766d1a-7ae8-4376-acd9-54afb22c2409";

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void list_notifications_for_user_successfully() {
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class).withJobTitle("Ouvrier").buildAndPersist();

        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID)
                .buildAndPersist();

        applicationContext.getBean(UserNotificationMotherObject.class)
                .withUserProfile(userProfile)
                .withRecruitment(recruitment)
                .withCreatedDate(OffsetDateTime.of(1981, 10, 8, 14, 5, 36, 25, ZoneOffset.UTC))
                .buildAndPersist();

        applicationContext.getBean(UserNotificationMotherObject.class)
                .withUserProfile(userProfile)
                .withRecruitment(recruitment)
                .buildAndPersistSuspendedRecruitmentNotification();

        applicationContext.getBean(UserNotificationMotherObject.class)
                .withUserProfile(userProfile)
                .withRecruitment(recruitment)
                .withCreatedDate(OffsetDateTime.of(2001, 10, 8, 14, 5, 36, 25, ZoneOffset.UTC))
                .buildAndPersist();

        applicationContext.getBean(UserNotificationMotherObject.class)
                .withUserProfile(userProfile)
                .withLink("http://yo.lo")
                .withSubjectAndContent("Objet de notif", "Contenu de notif")
                .withCreatedDate(OffsetDateTime.of(3001, 10, 8, 14, 5, 36, 25, ZoneOffset.UTC))
                .buildAndPersist();

        performGetAndExpect("/notification/user/%s".formatted(userProfile.userId()), "notificationsList", true);
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void list_notifications_for_specific_user_not_other() throws Exception {
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class).withJobTitle("Ouvrier").buildAndPersist();

        var userProfile1 = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID)
                .buildAndPersist();

        var userProfile2 = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId("other")
                .buildAndPersist();

        applicationContext.getBean(UserNotificationMotherObject.class)
                .withUserProfile(userProfile1)
                .withRecruitment(recruitment)
                .buildAndPersist();

        applicationContext.getBean(UserNotificationMotherObject.class)
                .withUserProfile(userProfile2)
                .withRecruitment(recruitment)
                .buildAndPersist();

        mvc.perform(get(realUrl("/notification/user/%s".formatted(userProfile1.userId()))))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[*]", Matchers.hasSize(1)))
                .andExpect(jsonPath("$[0].userId", Matchers.is(userProfile1.userId())));
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void delete_notification_for_specific_user() throws Exception {
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class).withJobTitle("Ouvrier").buildAndPersist();

        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID)
                .buildAndPersist();

        var notification = applicationContext.getBean(UserNotificationMotherObject.class)
                .withUserProfile(userProfile)
                .withRecruitment(recruitment)
                .buildAndPersist();

        mvc.perform(delete(realUrl("/notification/%s/delete".formatted(notification.getId()))))
                .andExpect(status().isNoContent());

        Assertions.assertThat(notificationRepository.existsById(notification.getId())).isFalse();
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void list_notification_with_data_for_user_successfully() throws Exception {
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class).withJobTitle("Ouvrier").buildAndPersist();

        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID)
                .buildAndPersist();

        applicationContext.getBean(UserNotificationMotherObject.class)
                .withUserProfile(userProfile)
                .withRecruitment(recruitment)
                .buildAndPersist();

        mvc.perform(get(realUrl("/notification/user/%s".formatted(userProfile.userId()))))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].entityData.ORGANIZATION_NAME", Matchers.is(recruitment.getOrganizationName())))
                .andExpect(jsonPath("$[0].entityData.JOB_TITLE", Matchers.is(recruitment.getJob().getTitle())))
                .andExpect(jsonPath("$[0].entityData.CITY", Matchers.emptyString()));
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void mark_notification_as_read_successfully() throws Exception {
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class).withJobTitle("Ouvrier").buildAndPersist();

        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID)
                .buildAndPersist();

        var notification = applicationContext.getBean(UserNotificationMotherObject.class)
                .withUserProfile(userProfile)
                .withRecruitment(recruitment)
                .buildAndPersist();

        mvc.perform(post(realUrl("/notification/%s/read".formatted(notification.getId()))))
                .andExpect(status().isNoContent());

        var updatedNotification = notificationRepository.findById(notification.getId()).orElseThrow();
        Assertions.assertThat(updatedNotification.getState()).isEqualTo(NotificationState.NOT_INTERESTED);
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void mark_all_notifications_as_read_successfully() throws Exception {
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class).withJobTitle("Ouvrier").buildAndPersist();

        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID)
                .buildAndPersist();

        applicationContext.getBean(UserNotificationMotherObject.class)
                .withUserProfile(userProfile)
                .withRecruitment(recruitment)
                .buildAndPersist();

        applicationContext.getBean(UserNotificationMotherObject.class)
                .withUserProfile(userProfile)
                .withRecruitment(recruitment)
                .buildAndPersist();

        applicationContext.getBean(UserNotificationMotherObject.class)
                .withUserProfile(userProfile)
                .withRecruitment(recruitment)
                .buildAndPersist();

        mvc.perform(get(realUrl("/notification/user/%s/read-all".formatted(userProfile.userId()))))
                .andExpect(status().isNoContent());

        var notifications = notificationRepository.findByUserProfileUuidOrderByCreatedDateDesc(userProfile.uuid());
        notifications.forEach(x -> Assertions.assertThat(x.getState()).isEqualTo(NotificationState.NOT_INTERESTED));
    }
}
