package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.AbstractCandidature;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.job.SpontaneousCandidatureMotherObject;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

import static com.erhgo.TestUtils.stringMatchesContent;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class CandidatureExportControllerTest extends AbstractIntegrationTest {

    @Autowired
    ApplicationContext context;


    List<AbstractCandidature> generateCandidatures() {
        var list = new ArrayList<AbstractCandidature>();
        Stream.of(GlobalCandidatureState.values()).map(s -> context.getBean(RecruitmentCandidatureMotherObject.class)
                        .generated(s == GlobalCandidatureState.REFUSED_BY_CLIENT_WITH_SHEETS)
                        .withState(s)
                        .withJobTitle("job for %s".formatted(s))
                        .withFullnameComputingEmail(s.name(), "ln")
                        .withSubmissionDate(OffsetDateTime.of(200 + s.ordinal(), 5, 5, 5, 5, 5, 0, ZoneOffset.UTC))
                        .withLastConnectionDate(LocalDateTime.of(210 + s.ordinal(), 5, 5, 5, 5, 5, 0))
                        .withRecruitmentState(RecruitmentState.values()[s.ordinal() % RecruitmentState.values().length])
                        .withUserLocation(Location.builder().city("city %s".formatted(s.name())).postcode("59000").build())
                        .withRecruiterTitle("orga %s".formatted(s.name()))
                        .withUserId("user %s".formatted(s.name()))
                        .withUserPhone("n°%s".formatted(s.name()))
                        .buildAndPersist())
                .forEach(list::add);

        Stream.of(GlobalCandidatureState.values()).map(s -> context.getBean(SpontaneousCandidatureMotherObject.class)
                        .withState(s)
                        .withFullnameComputingEmail(s.name(), "ln sp")
                        .withSubmissionDate(OffsetDateTime.of(210 + s.ordinal(), 5, 5, 5, 5, 5, 0, ZoneOffset.UTC))
                        .withLastConnectionDate(LocalDateTime.of(220 + s.ordinal(), 5, 5, 5, 5, 5, 0))
                        .withUserLocation(Location.builder().city("sp city %s".formatted(s.name())).postcode("59000").build())
                        .withRecruiterTitle("sp orga %s".formatted(s.name()))
                        .withUserId("sp user %s".formatted(s.name()))
                        .buildAndPersist())
                .forEach(list::add);
        return list;

    }

    @SneakyThrows
    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void exportAllCandidatures() {
        generateCandidatures();
        context.getBean(RecruitmentCandidatureMotherObject.class).withIsArchived(true).buildAndPersist();

        mvc.perform(get(realUrl("/candidature/common/export")))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("text/csv"))
                .andExpect(stringMatchesContent("export_allCandidatures.csv"));
    }

    @SneakyThrows
    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void exportSpontaneousCandidatures() {
        var orga = generateCandidatures().stream().filter(c -> !c.isRecruitmentCandidature()).findFirst().orElseThrow().getRecruiter().getCode();
        context.getBean(RecruitmentCandidatureMotherObject.class).withIsArchived(true).buildAndPersist();

        mvc.perform(get(realUrl("/candidature/common/export?organizationCode=%s".formatted(orga))))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("text/csv"))
                .andExpect(stringMatchesContent("export_spontaneousCandidatures.csv"));
    }

    @SneakyThrows
    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void exportRecruitmentCandidatures() {
        var recruitment = generateCandidatures().stream().filter(AbstractCandidature::isRecruitmentCandidature).findFirst()
                .map(r -> (RecruitmentCandidature) r).orElseThrow().getRecruitment().getCode();
        context.getBean(RecruitmentCandidatureMotherObject.class).withIsArchived(true).buildAndPersist();

        mvc.perform(get(realUrl("/candidature/common/export?recruitmentCode=%s".formatted(recruitment))))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("text/csv"))
                .andExpect(stringMatchesContent("export_recruitmentCandidatures.csv"));
    }
}
