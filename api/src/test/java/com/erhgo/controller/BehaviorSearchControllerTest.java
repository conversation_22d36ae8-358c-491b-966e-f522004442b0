package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.ResultActions;

import static com.erhgo.TestUtils.jsonMatchesContentWithOrderedArray;
import static com.erhgo.generators.TestFixtures.*;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class BehaviorSearchControllerTest extends AbstractIntegrationTestWithFixtures {
    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void should_search_by_code() throws Exception {
        performSearchOk(B_04.getCode())
                .andExpect(jsonPath("$.content", hasSize(1)))
                .andExpect(jsonPath("$.content[0].code", is(B_04.getCode())));
    }

    @Test
    @WithMockKeycloakUser
    void forbidden() throws Exception {
        performSearch(B_04.getCode())
                .andExpect(status().isForbidden());
    }

    @Test
    void unauthorized() throws Exception {
        performSearch(B_04.getCode())
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void should_search_by_title() throws Exception {
        performSearchOk("etre")
                .andExpect(jsonPath("$.content", hasSize(4)))
                .andExpect(jsonPath("$.content[0].title", is(B_01.getTitle())))
                .andExpect(jsonPath("$.content[1].title", is(B_10.getTitle())))
                .andExpect(jsonPath("$.content[2].title", is(B_02.getTitle())))
                .andExpect(jsonPath("$.content[3].title", is(B_03.getTitle())));
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void should_search_ordered_by_title() throws Exception {
        performSearchOk("")
                .andExpect(jsonMatchesContentWithOrderedArray("behaviorPage"));
    }

    private ResultActions performSearchOk(String query) throws Exception {
        return performSearch(query)
                .andExpect(status().isOk());
    }

    private ResultActions performSearch(String query) throws Exception {
        return mvc.perform(get("/api/odas/behavior/list")
                .param("size", "5")
                .param("by", "title")
                .param("direction", "ASC")
                .param("filter", query)
                .param("page", "0"));
    }
}
