package com.erhgo.controller.generation;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.openapi.dto.GeneratedOccupationDescriptionDTO;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.generation.DescriptionGenerationService;
import com.erhgo.services.generation.dto.OpenAIResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.util.ReflectionTestUtils.setField;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class DescriptionGenerationControllerTest extends AbstractIntegrationTest {

    @MockitoBean
    DescriptionGenerationService.OccupationDescriptionService occupationDescriptionService;
    @MockitoBean
    DescriptionGenerationService.BehaviorDescriptionService behaviorDescriptionService;

    @Autowired
    ApplicationContext applicationContext;

    @BeforeEach
    void prepare() {
        setField(DescriptionGenerationService.class, "MIN_DESCRIPTION_WORD_COUNT", 2);
        setField(DescriptionGenerationService.class, "MAX_DESCRIPTION_WORD_COUNT", 10);
        setField(DescriptionGenerationService.class, "MIN_BEHAVIOR_DESCRIPTION_WORD_COUNT", 2);
        setField(DescriptionGenerationService.class, "MAX_BEHAVIOR_DESCRIPTION_WORD_COUNT", 10);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void generateDescription_returnGeneratedDescription() {
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist();
        var occupationId = occupation.getId();
        var generatedDescription = "Generated description for the occupation";
        when(occupationDescriptionService.generateOccupationDescription(occupationId)).thenReturn(new OpenAIResponse<GeneratedOccupationDescriptionDTO>().setResult(new GeneratedOccupationDescriptionDTO(generatedDescription)));

        performGetAndExpect("/erhgo-occupation-generation/generate-description/%s".formatted(occupationId), "generatedDescription", false);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void generateDescription_throwsGenericTechnicalException_returnsServerError() {
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist();
        var occupationId = occupation.getId();
        when(occupationDescriptionService.generateOccupationDescription(occupationId)).thenThrow(GenericTechnicalException.class);
        Exception expectedException = null;
        try {
            performGetAndExpect("/erhgo-occupation-generation/generate-description/%s".formatted(occupationId), "generatedDescription", false)
                    .andExpect(status().isInternalServerError());
        } catch (Exception e) {
            expectedException = e;
        }
        assertThat(expectedException).isNotNull().hasCauseExactlyInstanceOf(GenericTechnicalException.class);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void generateBehaviorsDescription_returnGeneratedDescription() {
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist();
        var occupationId = occupation.getId();
        var generatedDescription = "Generated description for the occupation";
        when(behaviorDescriptionService.generateOccupationBehaviorsDescription(occupationId)).thenReturn(new OpenAIResponse<GeneratedOccupationDescriptionDTO>().setResult(new GeneratedOccupationDescriptionDTO(generatedDescription)));

        performGetAndExpect("/erhgo-occupation-generation/generate-behaviors-description/%s".formatted(occupationId), "generatedDescription", false);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void generateBehaviorsDescription_throwsGenericTechnicalException_returnsServerError() {
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist();
        var occupationId = occupation.getId();
        when(behaviorDescriptionService.generateOccupationBehaviorsDescription(occupationId)).thenThrow(GenericTechnicalException.class);
        Exception expectedException = null;
        try {
            performGetAndExpect("/erhgo-occupation-generation/generate-behaviors-description/%s".formatted(occupationId), "generatedDescription", false)
                    .andExpect(status().isInternalServerError());
        } catch (Exception e) {
            expectedException = e;
        }
        assertThat(expectedException).isNotNull().hasCauseExactlyInstanceOf(GenericTechnicalException.class);
    }


}
