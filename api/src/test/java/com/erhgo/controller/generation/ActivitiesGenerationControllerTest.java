package com.erhgo.controller.generation;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.PromptConfig;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.repositories.ActivityRepository;
import com.erhgo.repositories.CapacityRepository;
import com.erhgo.repositories.JobActivityLabelRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.generation.ActivityGenerationService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.IntStream;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class ActivitiesGenerationControllerTest extends AbstractIntegrationTest {

    @MockitoBean
    GenerationClient generationClient;

    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    CapacityGenerator capacityGenerator;

    @Autowired
    ActivityGenerationService qualificatorActivityService;


    @Autowired
    JobActivityLabelRepository jobActivityLabelRepository;

    @Autowired
    ActivityRepository activityRepository;

    @Autowired
    CapacityRepository capacityRepository;

    @MockitoBean
    KeycloakMockService keycloakService;

    static final String CHAT_COMPLETION_CHOICE = """
            {"capacitiesList": ["CA-08","CA-07","CA-01"],
            "activitiesDetail": [
                {"title": "Transporter des matériaux de construction","capacities": ["CA-08","CA-07"]},
                {"title": "Repérer et traiter des anomalies ou des dysfonctionnements","capacities": ["CA-01"]},
                {"title": "Assembler et fixer des éléments / matériaux entre eux ","capacities": ["CA-07"]}
            ],
            "capacitiesConsistency": "OK",
            "numberOfCapacitiesRule": "OK"}
            """;

    static final ChatCompletionResponse CHAT_COMPLETION_RESPONSE = new ChatCompletionResponse(CHAT_COMPLETION_CHOICE, null);

    @BeforeEach
    void prepare() {
        ReflectionTestUtils.setField(ActivityGenerationService.class, "MINIMUM_TOLERABLE_NUMBER_OF_CAPACITIES", 2);
        ReflectionTestUtils.setField(ActivityGenerationService.class, "MAXIMUM_TOLERABLE_NUMBER_OF_CAPACITIES", 3);
        ReflectionTestUtils.setField(ActivityGenerationService.class, "MINIMUM_TOLERABLE_NUMBER_OF_ACTIVITIES", 1);
        ReflectionTestUtils.setField(ActivityGenerationService.class, "MAXIMUM_TOLERABLE_NUMBER_OF_ACTIVITIES", 3);
    }

    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void generateActivitiesOnOccupationWithoutActivities() {
        var title = "occupation title";
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).withTitle(title).buildAndPersist();
        var occupationId = occupation.getId();
        IntStream.range(0, 9)
                .forEach(index -> capacityGenerator.createCapacity("CA-0" + (index + 1)));

        when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class))).thenReturn(CHAT_COMPLETION_RESPONSE);
        performPut("/erhgo-occupation-generation/generate-activities/%s".formatted(occupationId), "generatedOccupationActivities");
        txHelper.doInTransaction(() -> {
            var updatedOccupation = applicationContext.getBean(ErhgoOccupationRepository.class).findById(occupationId).orElseThrow();
            Assertions.assertThat(updatedOccupation.getActivities())
                    .hasSize(3)
                    .anyMatch(a -> a.getInducedCapacities().stream().map(Capacity::getCode).toList().containsAll(List.of("CA-08", "CA-07")) && "Transporter des matériaux de construction".equals(a.getTitle()))
                    .anyMatch(a -> a.getInducedCapacities().stream().map(Capacity::getCode).toList().contains("CA-01") && "Repérer et traiter des anomalies ou des dysfonctionnements".equals(a.getTitle()))
                    .anyMatch(a -> a.getInducedCapacities().stream().map(Capacity::getCode).toList().contains("CA-07") && "Assembler et fixer des éléments / matériaux entre eux ".equals(a.getTitle()))
                    .allMatch(a -> occupation.equals(updatedOccupation));
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void generateActivitiesOnAlreadyQualifiedOccupation_resetActivities() {
        IntStream.range(0, 9)
                .forEach(index -> capacityGenerator.createCapacity("CA-0" + (index + 1)));

        var oldCapacities = IntStream.range(10, 15)
                .mapToObj(index -> capacityGenerator.createCapacity("CA-0" + (index + 1)))
                .toList();

        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withCapacities(oldCapacities.get(0), oldCapacities.get(1), oldCapacities.get(2))
                .buildAndPersist();

        var occupationId = occupation.getId();

        when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class))).thenReturn(CHAT_COMPLETION_RESPONSE);

        performPut("/erhgo-occupation-generation/generate-activities/%s".formatted(occupationId), "generatedOccupationActivities");

        txHelper.doInTransaction(() -> {
            var updatedOccupation = applicationContext.getBean(ErhgoOccupationRepository.class).findById(occupationId).orElseThrow();
            var updatedActivities = updatedOccupation.getActivities();

            Assertions.assertThat(updatedActivities)
                    .isNotEmpty()
                    .hasSize(3)
                    .extracting("title")
                    .containsExactlyInAnyOrder("Transporter des matériaux de construction",
                            "Repérer et traiter des anomalies ou des dysfonctionnements",
                            "Assembler et fixer des éléments / matériaux entre eux ")
                    .doesNotContain(oldCapacities.get(0).getCode(), oldCapacities.get(1).getCode(), oldCapacities.get(2).getCode());
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void generateActivities_refreshUsersCapacities() {

        AtomicReference<UUID> occupationId = new AtomicReference<>();
        txHelper.doInTransaction(() -> {
            var oldCapacities = IntStream.range(0, 2)
                    .mapToObj(index -> capacityGenerator.createCapacity("CA-0" + index))
                    .toList();
            IntStream.range(3, 10)
                    .forEach(index -> capacityGenerator.createCapacity("CA-0" + index));
            var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                    .withCapacities(oldCapacities.toArray(new Capacity[]{}))
                    .buildAndPersist();

            var otherOccupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                    .withCapacities(capacityGenerator.createCapacity("CA-02"))
                    .buildAndPersist();

            applicationContext.getBean(UserProfileMotherObject.class)
                    .withUserId("u1")
                    .withExperienceOnOccupation(occupation)
                    .buildAndPersist()
                    .refreshProfessionalAndCapacityRelatedQuestionCapacityOccurrences()
            ;

            applicationContext.getBean(UserProfileMotherObject.class)
                    .withUserId("u2")
                    .withExperienceOnOccupation(occupation)
                    .withExperienceOnOccupation(otherOccupation)
                    .buildAndPersist()
                    .refreshProfessionalAndCapacityRelatedQuestionCapacityOccurrences();
            ;

            occupationId.set(occupation.getId());
        });

        when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class))).thenReturn(CHAT_COMPLETION_RESPONSE);

        performPut("/erhgo-occupation-generation/generate-activities/%s".formatted(occupationId.get()), "generatedOccupationActivities");

        txHelper.doInTransaction(() -> {
            var user1 = applicationContext.getBean(UserProfileRepository.class).findByUserId("u1").orElseThrow();
            var user2 = applicationContext.getBean(UserProfileRepository.class).findByUserId("u2").orElseThrow();
            Assertions.assertThat(user1.getAllCapacities()).extracting(Capacity::getCode).containsExactlyInAnyOrder("CA-01", "CA-07", "CA-08");
            Assertions.assertThat(user2.getAllCapacities()).extracting(Capacity::getCode).containsExactlyInAnyOrder("CA-01", "CA-07", "CA-08", "CA-02");
        });
    }


}
