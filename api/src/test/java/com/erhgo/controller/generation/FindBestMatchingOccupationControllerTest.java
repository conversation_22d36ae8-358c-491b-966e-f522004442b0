package com.erhgo.controller.generation;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.PromptConfig;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.openapi.dto.ErhgoOccupationSearchDTO;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import com.erhgo.services.search.ErhgoOccupationFinder;
import com.erhgo.services.search.ErhgoOccupationIndexer;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.List;

import static org.hamcrest.Matchers.equalTo;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class FindBestMatchingOccupationControllerTest extends AbstractIntegrationTest {

    @MockitoBean
    GenerationClient generationClient;

    @Autowired
    ApplicationContext applicationContext;

    @MockitoBean
    KeycloakMockService keycloakService;

    @MockitoBean
    ErhgoOccupationFinder erhgoOccupationFinder;

    @MockitoBean
    ErhgoOccupationIndexer erhgoOccupationIndexer;

    @Autowired
    ErhgoOccupationRepository erhgoOccupationRepository;

    private ChatCompletionResponse chatCompletionResponse(String choice) {
        return new ChatCompletionResponse(choice, null);
    }


    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void findMatchingOccupation_return_occupation() {
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist();
        var occupationId = occupation.getId().toString();
        var title = "occupation title";


        var openAIResponse = String.format("""
                {"uuid": "%s", "title": "%s"}
                """, occupationId, title);

        when(erhgoOccupationFinder.searchOccupations(anyString(), anyBoolean())).thenReturn(List.of(new ErhgoOccupationSearchDTO().title(title).code(occupationId)));
        when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class))).thenReturn(chatCompletionResponse(openAIResponse));

        mvc.perform(MockMvcRequestBuilders.get(realUrl("/erhgo-occupation-generation/find-best-matching-occupation?query=%s".formatted(title))))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", equalTo(occupationId)))
                .andExpect(jsonPath("$.title", equalTo(occupation.getTitle())));

        verify(erhgoOccupationFinder).searchOccupations(title, false);
        verify(generationClient).createChatCompletion(assertArg(r -> Assertions.assertThat(r.getInstructions().get(r.getInstructions().size() - 1).getText()).contains(title)), any(PromptConfig.class));
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void findMatchingOccupation_return_null(boolean emptyAlgolia) {
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist();
        var occupationId = occupation.getId().toString();

        var openAIResponse = """
                {"uuid": null, "title": null}
                """;
        if (emptyAlgolia) {
            when(erhgoOccupationFinder.searchOccupations(anyString(), anyBoolean())).thenReturn(List.of());
        } else {
            when(erhgoOccupationFinder.searchOccupations(anyString(), anyBoolean())).thenReturn(List.of(new ErhgoOccupationSearchDTO().title("nope").code(occupationId)));
        }
        when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class))).thenReturn(chatCompletionResponse(openAIResponse));
        mvc.perform(MockMvcRequestBuilders.get(realUrl("/erhgo-occupation-generation/find-best-matching-occupation?query=occupation")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", Matchers.nullValue()))
                .andExpect(jsonPath("$.title", Matchers.nullValue()));
        if (emptyAlgolia) {
            verifyNoInteractions(generationClient);
        } else {
            verify(generationClient).createChatCompletion(any(Prompt.class), any(PromptConfig.class));
        }
        verify(erhgoOccupationFinder).searchOccupations(anyString(), anyBoolean());
    }


}
