package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.TestUtils;
import com.erhgo.TransactionTestHelper;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.CandidatureRecruitmentState;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.enums.CandidatureState;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.experience.AbstractContextMet;
import com.erhgo.generators.DataGeneratorService;
import com.erhgo.generators.RecruitmentGenerator;
import com.erhgo.generators.TestFixtures;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.RecruitmentService;
import com.erhgo.services.SecurityService;
import com.erhgo.services.mailing.MailingListService;
import com.erhgo.services.mailing.SendEmailValidationCandidatureService;
import com.google.common.collect.Sets;
import jakarta.persistence.EntityManager;
import org.assertj.core.api.Assertions;
import org.assertj.core.data.TemporalUnitWithinOffset;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;

import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.erhgo.controller.UserEmailVerificationControllerTest.NEW_USER_ID;
import static com.erhgo.generators.TestFixtures.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.*;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@TestPropertySource(properties = {"sendinblue.templates.candidature-notification=42"})
class CandidatureControllerTest extends AbstractIntegrationTestWithFixtures {
    @MockitoBean
    private MailingListService mailingListService;

    @MockitoBean
    SendEmailValidationCandidatureService sendEmailValidationCandidatureService;
    @Autowired
    private RecruitmentService recruitmentService;

    @Autowired
    private RecruitmentCandidatureRepository recruitmentCandidatureRepository;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private DataGeneratorService dataGeneratorService;

    @Autowired
    private RecruitmentGenerator recruitmentGenerator;

    @Autowired
    private TransactionTestHelper transactionTestHelper;

    @Autowired
    private SecurityService securityService;

    private RecruitmentDTO recruitment;
    @Autowired
    private EntityManager entityManager;


    @Test
    @ResetDataAfter
    void should_create_candidature_and_save_answer() throws Exception {
        initializeRecruitment();
        var userId = "uuid";
        var answer = "Oui j'ai déjà manger des chips";
        TestUtils.mockAuthentication("candidate", userId, Role.CANDIDATE);
        Mockito.reset(mailingListService);
        doSaveAnswerOnCandidature(recruitment.getId(), answer);
        Mockito.verify(mailingListService, Mockito.never()).updateLastCandidatureDate(Mockito.any(UserProfile.class));
        var candidatures = recruitmentCandidatureRepository.findByUserProfileUserId(userId);
        assertThat(candidatures).hasSize(1);
        assertThat(candidatures.stream().iterator().next().getCustomAnswer()).isEqualTo(answer);
    }


    private MvcResult doSaveAnswerOnCandidature(Long recruitmentId, String answer) throws Exception {
        return mvc.perform(post("/api/odas/candidature/%s/saveCustomAnswer".formatted(recruitmentId))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(answer))
                .andExpect(status().isNoContent())
                .andReturn();
    }

    private RecruitmentCandidature getCandidature(Long candidatureId) {
        return recruitmentCandidatureRepository.findById(candidatureId).orElseThrow();
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = {Role.CANDIDATE})
    @ResetDataAfter
    void publish_matching_candidature_should_update_matches_flag() throws Exception {
        doPublishCandidature(CANDIDATURE_MATCHING, false);
        assertCandidatureMatches();
        Mockito.verify(mailingListService).updateLastCandidatureDate(CANDIDATURE_MATCHING.getUserProfile());
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = {Role.CANDIDATE})
    @ResetDataAfter
    void re_publish_matching_candidature_should_not_modify_candidature_activities() throws Exception {
        doPublishCandidature(CANDIDATURE_MATCHING, false);
        doPublishCandidature(CANDIDATURE_MATCHING, true);
        Mockito.verify(mailingListService, Mockito.times(1)).updateLastCandidatureDate(CANDIDATURE_MATCHING.getUserProfile());
        assertCandidatureMatches();
    }

    private void doPublishCandidature(RecruitmentCandidature candidatureMatching, boolean republish) throws Exception {
        // @formatter:off
        publish(candidatureMatching.getId(),republish)
                .andExpect(status().isNoContent())
                .andReturn();
        // @formatter:on
    }

    private ResultActions publish(Long candidatureMatching, boolean republish) throws Exception {
        if (!republish) {
            txHelper.doInTransaction(() -> {
                var candidature = entityManager.find(RecruitmentCandidature.class, candidatureMatching);
                candidature.setGlobalCandidatureState(GlobalCandidatureState.NOT_FINALIZED);
                candidature.setState(CandidatureState.STARTED);
            });
        }
        return mvc.perform(post("/api/odas/candidature/publish")
                .contentType(MediaType.APPLICATION_JSON)
                .content("""
                        {"candidatureId": "%d"}
                        """.formatted(candidatureMatching)));
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = {Role.CANDIDATE})
    @ResetDataAfter
    void publish_matching_candidature_based_on_recruitment_id() {
        performPost("/candidature/publish", new PublishCandidatureCommandDTO().recruitmentId(CANDIDATURE_MATCHING.getRecruitment().getId()));
        assertCandidatureMatches();
    }

    @Test
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = {Role.CANDIDATE})
    @ResetDataAfter
    void publish_candidature_check_that_mailingListService_is_called() throws Exception {
        initializeRecruitment();
        TestUtils.mockAuthentication("candidate", NEW_USER_ID, Role.CANDIDATE);
        performPost("/candidature/publish", new PublishCandidatureCommandDTO().recruitmentId(recruitment.getId())).andExpect(status().isNoContent());
        verify(mailingListService).updateLastCandidatureDate(Mockito.any(UserProfile.class));
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = {Role.CANDIDATE})
    @ResetDataAfter
    void publish_bad_request() throws Exception {
        performPost("/candidature/publish", new PublishCandidatureCommandDTO()).andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void publish_matching_candidature_as_admin() throws Exception {
        doPublishCandidature(CANDIDATURE_MATCHING, false);
        Mockito.verify(mailingListService).updateLastCandidatureDate(CANDIDATURE_MATCHING.getUserProfile());
        assertCandidatureMatches();
    }


    private void assertCandidatureMatches() {
        transactionTestHelper.doInTransaction(() -> {
            var candidature = getCandidature(CANDIDATURE_MATCHING.getId());
            assertThat(candidature.getValid()).isTrue();
            assertThat(candidature.getEffort()).isEqualTo(40);
            assertThat(candidature.getCandidatureRecruitmentState()).isEqualTo(CandidatureRecruitmentState.NEW);
        });
    }

    @Test
    @WithMockKeycloakUser(username = "Bad boy", roles = {Role.CANDIDATE})
    @ResetDataAfter
    void publish_matching_candidature_of_another_user_should_fail() throws Exception {

        // @formatter:off
        publish(CANDIDATURE_MATCHING.getId(),false)
                .andExpect(status().isForbidden());
        // @formatter:on
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void refresh_matching_should_update_matching_flag() throws Exception {
        transactionTestHelper.doInTransaction(() -> {
            var candidature = recruitmentCandidatureRepository.findById(CANDIDATURE_MATCHING.getId()).orElseThrow();
            candidature.setValid(false);
            candidature.setEffort(0);
            candidature.setState(CandidatureState.VALIDATED);
        });

        // @formatter:off
        mvc.perform(post("/api/odas/recruitment/" + RECRUITMENT_WITH_MATCHING_CANDIDATURE.getId() + "/refreshMatching"))
                .andExpect(status().isNoContent());
        // @formatter:on

        assertCandidatureMatches();
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITHOUT_EXPERIENCE, roles = {Role.CANDIDATE})
    void publish_matching_candidature_with_transitive_induced_capacity_should_update_matches_flag() throws Exception {

        var recruitment = recruitmentGenerator.createRecruitmentWithRequiredCapacity("J-42", E_02_SOGILIS, CA1_12);
        var candidature = dataGeneratorService.createDraftCandidatureWithCapacity(recruitment, TestFixtures.USER_PROFILE_WITHOUT_EXPERIENCE, CA3_12);
        var jobActivityUuid = recruitment.getJob().getAllMissionsActivities().iterator().next().getActivity().getUuid();

        // @formatter:off
        publish(candidature.getId(),false)
                .andExpect(status().isNoContent());
        // @formatter:on

        transactionTestHelper.doInTransaction(() -> {
            var candidatureFetch = getCandidature(candidature.getId());
            assertThat(candidatureFetch.getValid()).isTrue();
            assertThat(candidatureFetch.getEffort()).isZero();
            assertThat(candidatureFetch.getCandidatureRecruitmentState()).isEqualTo(CandidatureRecruitmentState.NEW);
        });
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_EMPTY_EXPERIENCE, roles = {Role.CANDIDATE})
    void publish_not_matching_candidature_should_have_invalid_flag() throws Exception {
        doPublishCandidature(CANDIDATURE_NOT_MATCHING_FOR_USER_WITH_EMPTY_EXPERIENCE, false);
        Mockito.verify(mailingListService).updateLastCandidatureDate(CANDIDATURE_NOT_MATCHING_FOR_USER_WITH_EMPTY_EXPERIENCE.getUserProfile());

        transactionTestHelper.doInTransaction(() -> {
            var candidature = getCandidature(CANDIDATURE_NOT_MATCHING_FOR_USER_WITH_EMPTY_EXPERIENCE.getId());
            assertThat(candidature.getValid()).isFalse();
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void should_create_a_candidature_note() throws Exception {
        createCandidatureNote();
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void should_create_a_candidature_note_for_external() throws Exception {
        createCandidatureNote();
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void should_create_a_candidature_note_for_OT() throws Exception {
        createCandidatureNote();
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void should_fail_to_save_note_when_associated_to_candidature_with_other_id() throws Exception {
        var createdNote = createCandidatureNote();
        mvc.perform(post("/api/odas/candidature/" + CANDIDATURE_MATCHING.getId() + "/candidatureNote/save")
                        .content(objectMapper.writeValueAsBytes(createdNote))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    private SaveCandidatureNoteCommandDTO createCandidatureNote() throws Exception {
        var saveCandidatureNoteCommandDTO = new SaveCandidatureNoteCommandDTO();
        var id = UUID.randomUUID();
        saveCandidatureNoteCommandDTO.setId(id);
        saveCandidatureNoteCommandDTO.setText("A useful note");

        var candidatureId = CANDIDATURE_NOT_MATCHING_FOR_USER_WITH_EMPTY_EXPERIENCE.getId();
        mvc.perform(post("/api/odas/candidature/" + candidatureId + "/candidatureNote/save")
                        .content(objectMapper.writeValueAsBytes(saveCandidatureNoteCommandDTO))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id", notNullValue()))
                .andExpect(jsonPath("$.text", is("A useful note")))
                .andReturn();


        var candidature = recruitmentCandidatureRepository.findById(candidatureId).orElseThrow();
        Assertions.assertThat(candidature.getRecruitment().getLastProcessingType()).isEqualTo(Recruitment.ProcessingType.COMMENT);
        Assertions.assertThat(candidature.getRecruitment().getLastProcessingDate()).isCloseToUtcNow(new TemporalUnitWithinOffset(1, ChronoUnit.DAYS));
        return saveCandidatureNoteCommandDTO;
    }

    @Test
    @WithMockKeycloakUser
    void should_fail_to_create_a_candidature_note_when_text_is_missing() throws Exception {
        var saveCandidatureNoteCommandDTO = new SaveCandidatureNoteCommandDTO();

        mvc.perform(post("/api/odas/candidature/" + CANDIDATURE_NOT_MATCHING_FOR_USER_WITH_EMPTY_EXPERIENCE.getId() + "/candidatureNote/save")
                        .content(objectMapper.writeValueAsBytes(saveCandidatureNoteCommandDTO))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = Role.CANDIDATE)
    @ResetDataAfter
    void should_update_contexts_met() throws Exception {
        var expectedContexts = Sets.union(
                USER_PROFILE_WITH_MATCHING_CANDIDATURE.jobContextsMet().stream().map(AbstractContextMet::getContext).collect(Collectors.toSet()),
                Collections.singleton(MANDATORY_CONTEXT)
        ).immutableCopy();

        var contextsMet = Collections.singletonList(
                (new ContextMetDTO()).contextId(MANDATORY_CONTEXT.getId()).experiencesIds(Collections.singletonList(EXPERIENCE_MATCHING_CANDIDATURE.getUuid())).frequency(FrequencyDTO.HIGH)
        );

        mvc.perform(post("/api/odas/candidature/" + CANDIDATURE_MATCHING.getId() + "/contextsMet")
                        .content(objectMapper.writeValueAsBytes(contextsMet))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        transactionTestHelper.doInTransaction(() -> {
            var profile = userProfileRepository.findById(EXPERIENCE_MATCHING_CANDIDATURE.getUserProfile().uuid()).orElseThrow();
            assertThat(profile.jobContextsMet().stream().map(AbstractContextMet::getContext)).containsExactlyInAnyOrderElementsOf(expectedContexts);
        });
    }

    @Test
    @WithMockKeycloakUser
    void should_not_update_contexts_met_for_wrong_user() throws Exception {
        var contextsMet = Collections.singletonList(
                (new ContextMetDTO()).contextId(MANDATORY_CONTEXT.getId()).experiencesIds(Collections.singletonList(EXPERIENCE_MATCHING_CANDIDATURE.getUuid())).frequency(FrequencyDTO.HIGH)
        );

        mvc.perform(post("/api/odas/candidature/" + CANDIDATURE_MATCHING.getId() + "/contextsMet")
                        .content(objectMapper.writeValueAsBytes(contextsMet))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockKeycloakUser(id = KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, roles = Role.CANDIDATE)
    void should_retrieve_recruitment_profile_mandatory_contexts() throws Exception {
        final var content = TestUtils.toString(this.getClass().getClassLoader().getResourceAsStream("expected/contextsToEvaluatePerCategory.json"));

        mvc.perform(get("/api/odas/candidature/" + CANDIDATURE_MATCHING.getId() + "/contextsToEvaluate"))
                .andExpect(status().isOk())
                .andExpect(content().json(content, false))
                .andExpect(jsonPath("$[0].experiencesIds", hasSize(1)))
                .andExpect(jsonPath("$[0].experiencesIds[0]", isA(String.class)))
                .andExpect(jsonPath("$[1].experiencesIds", hasSize(1)))
                .andExpect(jsonPath("$[1].experiencesIds[0]", isA(String.class)))
        ;
    }

    private void initializeRecruitment() {
        securityService.doAsAdmin(() -> {
            var recruitment = new SaveRecruitmentCommandDTO();
            recruitment.setTitle("Job");
            recruitment.setDescription("");
            recruitment.setRecruitmentProfileUuid(P_03.getUuid());
            recruitment.setState(RecruitmentStateDTO.PUBLISHED);
            recruitment.setTypeContract(TypeContractDTO.CDI);
            recruitment.setUsersIdToNotify(List.of(ADMIN_USER_ID));
            this.recruitment = recruitmentService.create(recruitment);
        });
    }
}
