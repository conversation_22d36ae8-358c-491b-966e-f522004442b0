package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.TestUtils;
import com.erhgo.config.ApiConstants;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.generators.ErhgoOccupationGenerator;
import com.erhgo.openapi.dto.CreateJobForTemplateCommandDTO;
import com.erhgo.openapi.dto.JobTypeDTO;
import com.erhgo.repositories.JobRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.mailing.MailingListService;
import com.jayway.jsonpath.JsonPath;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MvcResult;

import java.io.UnsupportedEncodingException;
import java.util.UUID;

import static com.erhgo.generators.TestFixtures.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class JobForTemplateAPIControllerTest extends AbstractIntegrationTestWithFixtures {

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private ErhgoOccupationGenerator generator;

    @MockitoBean
    MailingListService mailingListService;

    @Test
    @WithMockKeycloakUser(username = "admin", roles = Role.ODAS_ADMIN)
    void shouldNotCreateJobWithWrongErhgoOccupation() throws Exception {

        var query = new CreateJobForTemplateCommandDTO()
                .jobType(JobTypeDTO.OBSERVED)
                .recruiterCode("Test")
                .templateId(UUID.randomUUID());

        mvc.perform(post(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + "for-template")
                        .content(objectMapper.writeValueAsString(query))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());

    }

    @Test
    @WithMockKeycloakUser
    void shouldNotCreateJobWhenNotAllowedUser() throws Exception {

        var query = new CreateJobForTemplateCommandDTO()
                .jobType(JobTypeDTO.OBSERVED)
                .recruiterCode("Test")
                .templateId(UUID.randomUUID());

        mvc.perform(post(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + "for-template")
                        .content(objectMapper.writeValueAsString(query))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isForbidden());

    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(username = "admin", roles = Role.ODAS_ADMIN)
    void shouldCreateJobBasedOnErhgoOccupationIdAsConsultant() throws Exception {
        shouldCreateJobBasedOnErhgoOccupationIdCommon(JobTypeDTO.OBSERVED);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @ResetDataAfter
    @WithMockKeycloakUser(username = "admin", roles = Role.ODAS_ADMIN)
    void shouldCreateJobWithoutContext(boolean withContext) throws Exception {
        var occupation = createOccupation(withContext);

        var query = new CreateJobForTemplateCommandDTO()
                .jobType(JobTypeDTO.SIMPLE)
                .recruiterCode("E-02")
                .templateId(occupation.getId());

        final var creationResult = mvc.perform(post(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + "for-template")
                        .content(objectMapper.writeValueAsString(query))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();

        final var jobId = getJobId(creationResult);
        txHelper.doInTransaction(() -> {
            var job = jobRepository.findById(jobId).orElseThrow();
            assertThat(job.getAllMissionsContexts()).isNullOrEmpty();
        });
    }

    private UUID getJobId(MvcResult creationResult) throws UnsupportedEncodingException {
        return UUID.fromString(JsonPath.parse(creationResult.getResponse().getContentAsString()).read(JsonPath.compile("$.id")));
    }

    @ParameterizedTest
    @EnumSource(JobTypeDTO.class)
    @ResetDataAfter
    @WithMockKeycloakUser(username = "admin", roles = Role.ODAS_ADMIN)
    void shouldCreateJobBasedOnErhgoOccupationIdAsOT(JobTypeDTO jobType) throws Exception {

        shouldCreateJobBasedOnErhgoOccupationIdCommon(jobType);

    }

    private void shouldCreateJobBasedOnErhgoOccupationIdCommon(JobTypeDTO jobType) throws Exception {
        var occupation = createOccupation(true);

        var query = new CreateJobForTemplateCommandDTO()
                .jobType(jobType)
                .recruiterCode("E-02")
                .templateId(occupation.getId());

        final var creationResult = mvc.perform(post(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + "for-template")
                        .content(objectMapper.writeValueAsString(query))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(TestUtils.jsonMatchesContent(jobType.name().toLowerCase() + "JobFromTemplate"))
                .andExpect(jsonPath("$.jobType", Matchers.is(jobType.name())))
                .andReturn();

        final var jobId = getJobId(creationResult);

        txHelper.doInTransaction(() -> {
            var job = jobRepository.findById(jobId).orElseThrow();
            assertThat(job.getTitle()).isNotEmpty();
            assertThat(job.getErhgoOccupation().getId()).isEqualTo(occupation.getId());
            assertThat(job.getMasteryLevel()).isEqualTo(occupation.getLevel());
        });

    }

    private ErhgoOccupation createOccupation(boolean withContext) {
        var motherObject = new ErhgoOccupationMotherObject()
                .withId(UUID.randomUUID())
                .withTitle("title for ERHGO JOB TEMPLATE")
                .withBehavior(B_05);
        if (withContext) {
            motherObject
                    .withContext(CT_21, false)
                    .withContext(CT_29, true);
        }
        var occupation = motherObject
                .withActivity(jobActivityLabelUsedOnlyForEscoSkillClassification, true)
                .withLevel(MasteryLevel.MAX_LEVEL)
                .instance();

        generator.createErhgoOccupation(occupation);
        return occupation;
    }
}
