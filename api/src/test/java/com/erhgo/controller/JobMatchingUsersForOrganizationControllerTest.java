package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ApiConstants;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.JobGenerator;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.generators.UserProfileGenerator;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.Set;
import java.util.UUID;

import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.is;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class JobMatchingUsersForOrganizationControllerTest extends AbstractIntegrationTest {

    @Autowired
    private JobGenerator jobGenerator;

    @Autowired
    private CapacityGenerator capacityGenerator;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Autowired
    private OrganizationGenerator organizationGenerator;

    @Autowired
    private KeycloakMockService keycloakMockService;

    @Autowired
    protected MockMvc mvc;

    Recruiter project;
    Recruiter organization;
    Recruiter otherOrganization;
    Recruiter jobOrganization;
    UserProfile userWithoutChannel;
    UserProfile userInProject;
    UserProfile userInProjectAndOrganization;
    UserProfile userInOrganization;
    UserProfile userInOtherOrganization;
    Job job;

    private MockHttpServletRequestBuilder getMatchingUsersRequestBuilder(Job job) {
        return get(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + job.getId() + "/matching-users")
                .param("page", "0")
                .param("size", "600")
                .contentType(MediaType.APPLICATION_JSON);
    }

    @BeforeEach
    void bootstrapDatas() {
        project = organizationGenerator.createRecruiter("P-01", AbstractOrganization.OrganizationType.PROJECT);
        organization = organizationGenerator.createRecruiter("T-01", AbstractOrganization.OrganizationType.TERRITORIAL);
        otherOrganization = organizationGenerator.createRecruiter("T-02", AbstractOrganization.OrganizationType.TERRITORIAL);
        jobOrganization = organizationGenerator.createRecruiter("T-03", AbstractOrganization.OrganizationType.TERRITORIAL);

        keycloakMockService.createBackOfficeGroupAndRoles(organization.getCode(), project.getCode());
        keycloakMockService.createBackOfficeGroupAndRoles(project.getCode());
        keycloakMockService.createBackOfficeGroupAndRoles(otherOrganization.getCode(), project.getCode());

        var matchingCapacity = capacityGenerator.createCapacity("CA-1");

        userWithoutChannel = userProfileGenerator.createUserProfileWithCapacities(UUID.randomUUID(), 1, matchingCapacity);
        userInProject = userProfileGenerator.createUserProfileWithChannelsAndCapacities(UUID.randomUUID(), 1, Set.of(project.getCode()), matchingCapacity);
        userInProjectAndOrganization = userProfileGenerator.createUserProfileWithChannelsAndCapacities(UUID.randomUUID(), 1, Set.of(project.getCode(), organization.getCode()), matchingCapacity);
        userInOrganization = userProfileGenerator.createUserProfileWithChannelsAndCapacities(UUID.randomUUID(), 1, Set.of(organization.getCode()), matchingCapacity);
        userInOtherOrganization = userProfileGenerator.createUserProfileWithChannelsAndCapacities(UUID.randomUUID(), 1, Set.of(otherOrganization.getCode()), matchingCapacity);
        job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", jobOrganization, MasteryLevel.forLevel(1), matchingCapacity);

    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void get_users_matching_jobs_with_multiple_organizations() throws Exception {
        var expectedEmails = new String[]{userInProjectAndOrganization.uuid() + "@erhgo.fr", userInOrganization.uuid() + "@erhgo.fr", userInOtherOrganization.uuid() + "@erhgo.fr"};

        mvc.perform(getMatchingUsersRequestBuilder(job)
                        .param("organizationCodes", organization.getCode(), otherOrganization.getCode())
                        .param("strictOrganizationFilter", "true"))
                .andExpect(jsonPath("$.totalNumberOfElements", is(3)))
                .andExpect(jsonPath("$.content[*].contactInformation.email", containsInAnyOrder(expectedEmails)))
                .andExpect(status().isOk());
    }


    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void get_users_matching_jobs_without_channel() throws Exception {

        var expectedEmails = new String[]{userWithoutChannel.uuid() + "@erhgo.fr"};

        mvc.perform(getMatchingUsersRequestBuilder(job)
                        .param("isAffectedToNoChannel", "true"))
                .andExpect(jsonPath("$.totalNumberOfElements", is(1)))
                .andExpect(jsonPath("$.content[*].contactInformation.email", containsInAnyOrder(expectedEmails)))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void get_users_matching_jobs_without_channel_or_on_specific() throws Exception {

        var expectedEmails = new String[]{
                userWithoutChannel.uuid() + "@erhgo.fr",
                userInProjectAndOrganization.uuid() + "@erhgo.fr",
                userInProject.uuid() + "@erhgo.fr",

        };

        mvc.perform(getMatchingUsersRequestBuilder(job)
                        .param("isAffectedToNoChannel", "true")
                        .param("organizationCodes", project.getCode()))
                .andExpect(jsonPath("$.totalNumberOfElements", is(3)))
                .andExpect(jsonPath("$.content[*].contactInformation.email", containsInAnyOrder(expectedEmails)))
                .andExpect(status().isOk());
    }

}
