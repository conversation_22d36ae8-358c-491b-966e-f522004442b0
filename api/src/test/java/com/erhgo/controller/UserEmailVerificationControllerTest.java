package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.dto.EmailVerificationResultDTO;
import com.erhgo.domain.userprofile.MailVerification;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.openapi.dto.InitializeProfileCommandDTO;
import com.erhgo.openapi.dto.UserChannelSourceDTO;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailingListService;
import com.erhgo.services.mailing.check.EmailVerificationService;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.notifier.messages.FrontOfficePersonalEmailDomainMessageDTO;
import com.erhgo.services.notifier.messages.FrontofficeNotifierMessageDTO;
import lombok.SneakyThrows;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.mockito.internal.verification.VerificationModeFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.is;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class UserEmailVerificationControllerTest extends AbstractIntegrationTest {

    static final String NEW_USER_ID = "3c2a6fbf-e563-4b0b-9ae0-af62cfa12f8d";
    static final String EMAIL = "a@a";

    @Autowired
    UserProfileRepository userProfileRepository;

    @Autowired
    ApplicationContext applicationContext;

    @MockitoBean
    EmailVerificationService emailVerificationService;

    @MockitoBean
    MailingListService mailingListService;

    @Autowired
    KeycloakMockService keycloakMockService;

    @MockitoBean
    private Notifier notifier;


    @ResetDataAfter
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    @ParameterizedTest
    @EnumSource(value = MailVerification.MailVerificationState.class, names = {"FORCED", "VERIFIED"})
    @DisplayName("""
            Given a user who has already validated its email 
            When initialize-profile is done 
            Then emailVerificationService is not called  
            And user should not confirm its email 
            """)
    void init_profile_for_checked_email(MailVerification.MailVerificationState state) throws Exception {
        applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(NEW_USER_ID)
                .withEmail(EMAIL)
                .withEmailVerificationState(state)
                .buildAndPersist()
        ;

        var command = new InitializeProfileCommandDTO().channelSource(UserChannelSourceDTO.NOTHING);

        performPut("/user/initialize-profile", command)
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.confirmationRequired", is(false)))
                .andExpect(jsonPath("$.shouldAskForMailOptIn", is(true)))
        ;
        Mockito.verifyNoInteractions(emailVerificationService);
        verifyStateNumberOfVerifications(state, 0, true);
    }


    @ResetDataAfter
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    @Test
    @DisplayName("""
            Given a user who has already authenticated with invalid email
            When initialize-profile is done
            Then emailVerificationService is not called
            And user should confirm its email
            """)
    void init_profile_for_previous_invalid_email() throws Exception {
        applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(NEW_USER_ID)
                .withEmail(EMAIL)
                .withEmailVerificationState(MailVerification.MailVerificationState.REQUIRES_VERIFICATION)
                .withJobOfferOptOut(true)
                .buildAndPersist()
        ;

        var command = new InitializeProfileCommandDTO().channelSource(UserChannelSourceDTO.NOTHING);

        performPut("/user/initialize-profile", command)
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.confirmationRequired", is(true)))
                .andExpect(jsonPath("$.shouldAskForMailOptIn", is(false)))
        ;

        Mockito.verifyNoInteractions(emailVerificationService);
        verifyStateNumberOfVerifications(MailVerification.MailVerificationState.REQUIRES_VERIFICATION, 0, true);
    }

    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    void initialize_profile_from_keycloak() {
        keycloakMockService.setUserProfile(NEW_USER_ID, new UserRepresentation().setEmail(EMAIL).setId(NEW_USER_ID));
        when(emailVerificationService.verify(ArgumentMatchers.anyString()))
                .thenReturn(new EmailVerificationResultDTO().setEmailStatus(EmailVerificationResultDTO.EmailStatus.VALID));
        performPut("/user/initialize-profile", new InitializeProfileCommandDTO().channelSource(UserChannelSourceDTO.NOTHING))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.confirmationRequired", is(false)));
        verify(notifier).sendMessage(any(FrontofficeNotifierMessageDTO.class));
        verify(notifier).sendMessage(any(FrontOfficePersonalEmailDomainMessageDTO.class));

        txHelper.doInTransaction(() -> {
            var user = userProfileRepository.findByUserId(NEW_USER_ID).orElseThrow();
            verify(mailingListService).updateLastConnectionDate(user);
        });
    }

    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    void initialize_profile_should_not_send_message_personal_domain() {
        var email = "<EMAIL>";
        keycloakMockService.setUserProfile(NEW_USER_ID, new UserRepresentation().setEmail(email).setId(NEW_USER_ID));
        when(emailVerificationService.verify(ArgumentMatchers.anyString()))
                .thenReturn(new EmailVerificationResultDTO().setEmailStatus(EmailVerificationResultDTO.EmailStatus.VALID));
        performPut("/user/initialize-profile", new InitializeProfileCommandDTO().channelSource(UserChannelSourceDTO.NOTHING))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.confirmationRequired", is(false)));
        verify(notifier, VerificationModeFactory.times(1)).sendMessage(any());
        verifyNoMoreInteractions(notifier);
    }

    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    void initialize_profile_should_not_send_notification_prospect() {
        var email = "<EMAIL>";
        keycloakMockService.setUserProfile(NEW_USER_ID, new UserRepresentation().setEmail(email).setId(NEW_USER_ID));
        when(emailVerificationService.verify(ArgumentMatchers.anyString()))
                .thenReturn(new EmailVerificationResultDTO().setEmailStatus(EmailVerificationResultDTO.EmailStatus.VALID));
        performPut("/user/initialize-profile", new InitializeProfileCommandDTO().channelSource(UserChannelSourceDTO.NOTHING))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.confirmationRequired", is(false)))
        ;
        verify(notifier, VerificationModeFactory.times(1)).sendMessage(any());
    }

    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    void initialize_profile_from_keycloak_for_unknown_email() {
        keycloakMockService.setUserProfile(NEW_USER_ID, new UserRepresentation().setEmail(EMAIL).setId(NEW_USER_ID));
        when(emailVerificationService.verify(ArgumentMatchers.anyString()))
                .thenReturn(new EmailVerificationResultDTO().setEmailStatus(EmailVerificationResultDTO.EmailStatus.INVALID_MAIL));
        performPut("/user/initialize-profile", new InitializeProfileCommandDTO().channelSource(UserChannelSourceDTO.NOTHING))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.confirmationRequired", is(true)))
        ;
        verify(notifier).sendMessage(any(FrontOfficePersonalEmailDomainMessageDTO.class));
    }

    @ResetDataAfter
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    @ParameterizedTest
    @EnumSource(EmailVerificationResultDTO.EmailStatus.class)
    @DisplayName("""
            Given a user whose email has not been validated 
            When initialize-profile is done 
            Then emailVerificationService is called  
            And user should confirm its email depending on email verification service response 
            And verifier counter is increased
            """)
    void init_profile_for_unchecked_email(EmailVerificationResultDTO.EmailStatus verifierStatusMockedResult) throws Exception {

        var expectedEmailConfirmationRequired = verifierStatusMockedResult != EmailVerificationResultDTO.EmailStatus.VALID
                && verifierStatusMockedResult != EmailVerificationResultDTO.EmailStatus.UNKNOWN
                && verifierStatusMockedResult != EmailVerificationResultDTO.EmailStatus.VERIFIER_ERROR;

        var expectedState = switch (verifierStatusMockedResult) {
            case VALID -> MailVerification.MailVerificationState.VERIFIED;
            case UNKNOWN, VERIFIER_ERROR -> MailVerification.MailVerificationState.UNKNOWN;
            default -> MailVerification.MailVerificationState.REQUIRES_VERIFICATION;
        };

        applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(NEW_USER_ID)
                .withEmail(EMAIL)
                .withEmailVerificationState(MailVerification.MailVerificationState.UNKNOWN)
                .buildAndPersist()
        ;

        var expectedEmailSuggestion = "john@smith";
        when(emailVerificationService.verify(ArgumentMatchers.anyString()))
                .thenReturn(new EmailVerificationResultDTO()
                        .setSuggestion(expectedEmailSuggestion)
                        .setEmailStatus(verifierStatusMockedResult)
                );

        var command = new InitializeProfileCommandDTO().channelSource(UserChannelSourceDTO.NOTHING);

        performPut("/user/initialize-profile", command)
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.confirmationRequired", is(expectedEmailConfirmationRequired)))
                .andExpect(jsonPath("$.correctEmailSuggestion", is(expectedEmailSuggestion)));

        verify(emailVerificationService).verify(EMAIL);

        verifyStateNumberOfVerifications(expectedState, 1, false);
    }

    @ResetDataAfter
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    @Test
    @DisplayName("""
            Given a user whose email has not been validated 
            When verification process occurred MailVerification.MAX_NUMBER_OF_VERIFICATION_PER_ACCOUNT - 1 times and initialize-profile is called and email validation remains invalid
            Then emailVerificationService is called  
            And user should not confirm its email anymore due to a FORCED state
            And verifier counter is increased
            """)
    void init_profile_for_too_many_tries_email() throws Exception {

        applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(NEW_USER_ID)
                .withEmail(EMAIL)
                .withEmailVerificationCounter(MailVerification.MAX_NUMBER_OF_VERIFICATION_PER_ACCOUNT - 1)
                .withEmailVerificationState(MailVerification.MailVerificationState.UNKNOWN)
                .buildAndPersist()
        ;

        when(emailVerificationService.verify(EMAIL))
                .thenReturn(new EmailVerificationResultDTO().setEmailStatus(EmailVerificationResultDTO.EmailStatus.INVALID_MAIL));

        var command = new InitializeProfileCommandDTO().channelSource(UserChannelSourceDTO.NOTHING);

        performPut("/user/initialize-profile", command)
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.confirmationRequired", is(false)));

        verify(emailVerificationService).verify(EMAIL);

        verifyStateNumberOfVerifications(MailVerification.MailVerificationState.TOO_MANY_TRIES, MailVerification.MAX_NUMBER_OF_VERIFICATION_PER_ACCOUNT, false);
    }

    @ResetDataAfter
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    @Test
    @DisplayName("""
            Given a user whose email is state is unknown since less than MailVerification.MIN_UNKNOWN_DURATION 
            When verification process occurred initialize-profile is called 
            Then emailVerificationService is not called  
            And email remains UNKNOWN
            """)
    void rate_limit_email_check() throws Exception {

        applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(NEW_USER_ID)
                .withEmail(EMAIL)
                .withEmailVerificationState(MailVerification.MailVerificationState.UNKNOWN)
                .withEmailVerificationLastDate(OffsetDateTime.now())
                .buildAndPersist()
        ;

        var command = new InitializeProfileCommandDTO().channelSource(UserChannelSourceDTO.NOTHING);

        performPut("/user/initialize-profile", command)
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.confirmationRequired", is(false)));

        Mockito.verifyNoInteractions(emailVerificationService);

        verifyStateNumberOfVerifications(MailVerification.MailVerificationState.UNKNOWN, 0, false);
    }

    @ResetDataAfter
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    @Test
    @DisplayName("""
            Given a user whose email is state is unknown since more than MailVerification.MIN_UNKNOWN_DURATION 
            When verification process occurred initialize-profile is called 
            Then emailVerificationService called
            """)
    void rate_limit_unlock_email_check() throws Exception {

        applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(NEW_USER_ID)
                .withEmail(EMAIL)
                .withEmailVerificationState(MailVerification.MailVerificationState.UNKNOWN)
                .withEmailVerificationLastDate(OffsetDateTime.now().minus(1, ChronoUnit.DAYS))
                .buildAndPersist()
        ;

        var command = new InitializeProfileCommandDTO().channelSource(UserChannelSourceDTO.NOTHING);
        when(emailVerificationService.verify(EMAIL))
                .thenReturn(new EmailVerificationResultDTO().setEmailStatus(EmailVerificationResultDTO.EmailStatus.INVALID_MAIL));

        performPut("/user/initialize-profile", command)
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.confirmationRequired", is(true)));

        verify(emailVerificationService).verify(EMAIL);
    }

    void verifyStateNumberOfVerifications(MailVerification.MailVerificationState state, int expected, boolean dateShouldBeNull) {
        txHelper.doInTransaction(() -> {
            var mailVerification = userProfileRepository.findByUserId(NEW_USER_ID).orElseThrow().generalInformation()
                    .getMailVerificationState();
            assertThat(mailVerification).matches(a -> {
                var actualState = (MailVerification.MailVerificationState) ReflectionTestUtils.getField(a, "state");
                return actualState == state && a.getNumberOfVerificationsForAccount() == expected;
            });
            if (dateShouldBeNull) {
                assertThat(mailVerification.getLastVerificationDate()).isNull();
            } else {
                assertThat(mailVerification.getLastVerificationDate()).isAfter(OffsetDateTime.now().minus(7, ChronoUnit.HOURS));
            }
        });
    }

}
