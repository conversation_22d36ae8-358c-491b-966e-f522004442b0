package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.openapi.dto.AtsSyncCommandDTO;
import com.erhgo.openapi.dto.TaskInformationDTO;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.externaloffer.ExternalOfferScheduler;
import com.erhgo.services.sourcing.SourcingMailingService;
import jakarta.servlet.ServletException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


class ManualExecutionControllerTest extends AbstractIntegrationTest {

    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    MockMvc mvc;

    @MockitoBean
    SourcingMailingService service;
    @MockitoBean
    ExternalOfferScheduler externalOfferScheduler;

    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @Test
    void execute_manually_method_from_service_fails() throws Exception {
        var command = new TaskInformationDTO()
                .className("com.erhgo.services.ActivityService")
                .methodName("indexAll");
        Throwable excCaught = null;
        try {
            performPost("/api/odas/manual-execution/execute-task", command);
        } catch (Throwable e) {
            excCaught = e;
        }
        Assertions.assertNotNull(excCaught);
        org.assertj.core.api.Assertions.assertThat(excCaught).isInstanceOf(ServletException.class);
    }

    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @Test
    void execute_manually_method_from_service_succeed() throws Exception {
        var command = new TaskInformationDTO()
                .className("com.erhgo.services.sourcing.SourcingScheduler")
                .methodName("sendTrialMails");

        performPost("/api/odas/manual-execution/execute-task", command)
                .andExpect(status().isNoContent());
        Mockito.verify(service).sendTrialEndMails();

    }

    @WithMockKeycloakUser(roles = {Role.CANDIDATE})
    @Test
    void execute_manually_method_from_service_fails_not_authorized() throws Exception {
        var command = new TaskInformationDTO()
                .className("com.erhgo.services.sourcing.SourcingScheduler")
                .methodName("sendTrialMails");

        performPost("/api/odas/manual-execution/execute-task", command)
                .andExpect(status().isForbidden());
        Mockito.verifyNoInteractions(service);

    }

    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @Test
    void launchAts() throws Exception {

        var command = new AtsSyncCommandDTO().atsCode("42").customCode("55");
        performPost("/api/odas/manual-execution/sync-ats", command)
                .andExpect(status().isNoContent());
        Mockito.verify(externalOfferScheduler).analyzeDataForATS(command.getAtsCode(), command.getCustomCode());

    }

}
