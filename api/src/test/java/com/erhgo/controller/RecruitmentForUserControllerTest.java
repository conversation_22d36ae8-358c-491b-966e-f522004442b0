package com.erhgo.controller;

import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.enums.NotificationType;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

class RecruitmentForUserControllerTest extends AbstractControllerTest {
    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    RecruitmentCandidatureRepository recruitmentCandidatureRepository;

    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    @Test
    void list_recruitment_reports_by_user_successfully() throws Exception {
        var recruitment1 = applicationContext.getBean(RecruitmentMotherObject.class).withJobTitle("Ouvrier en batiment").buildAndPersist();
        var recruitment2 = applicationContext
                .getBean(RecruitmentMotherObject.class)
                .withJobTitle("Agriculteur")
                .withRecruiter(
                        applicationContext
                                .getBean(OrganizationGenerator.class)
                                .createRecruiter("O-42", AbstractOrganization.OrganizationType.SOURCING)
                )
                .buildAndPersist();

        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withNotifiedRecruitment(recruitment2, NotificationType.EMAIL)
                .withNotifiedRecruitment(recruitment1, NotificationType.MOBILE)
                .withNotifiedRecruitment(recruitment1, NotificationType.BOTH)
                .buildAndPersist();

        applicationContext
                .getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userProfile)
                .withState(GlobalCandidatureState.RECRUITMENT_VALIDATED)
                .withRecruitment(recruitment1)
                .withIsArchived(false)
                .buildAndPersist();

        applicationContext
                .getBean(RecruitmentCandidatureMotherObject.class)
                .withState(GlobalCandidatureState.INTERNAL_POSITION)
                .withJobTitle("Chaudronnier")
                .withUserProfile(userProfile)
                .buildAndPersist();

        applicationContext
                .getBean(RecruitmentCandidatureMotherObject.class)
                .withState(GlobalCandidatureState.REFUSED_MEETING_CLIENT)
                .withJobTitle("Artiste")
                .generated(true)
                .withModifiedByUser(true)
                .withUserProfile(userProfile)
                .withIsArchived(false)
                .buildAndPersist();

        applicationContext
                .getBean(RecruitmentCandidatureMotherObject.class)
                .withState(null)
                .withJobTitle("Poulier")
                .generated(true)
                .withModifiedByUser(true)
                .withUserProfile(userProfile)
                .withIsArchived(true)
                .buildAndPersist();

        performGetAndExpect("/recruitment/%s/list-user-recruitment-reports".formatted(userProfile.userId()), "userRecruitmentReportsList", false)
                .andExpect(MockMvcResultMatchers.jsonPath("$[*].publishedAt", Matchers.not(Matchers.emptyOrNullString())));
    }
}
