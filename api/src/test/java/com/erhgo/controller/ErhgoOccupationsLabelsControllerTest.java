package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import java.util.UUID;

import static org.hamcrest.Matchers.hasSize;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class ErhgoOccupationsLabelsControllerTest extends AbstractIntegrationTest {

    @Autowired
    ApplicationContext applicationContext;

    final static String ID = "5c8e3d3e-7031-4970-84bd-04ef3c248a5c";
    final static String ID_Bis = "5c8e3d3e-7031-4970-84bd-04ef3c248a5d";

    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @SneakyThrows
    @Test
    void getOccupationsWithLabelsButNoOccupation() {
        var labels = "toto";
        mvc.perform(get(realUrl("/erhgo-occupation/labels")).queryParam("occupationLabels", labels))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[*]", hasSize(0)));
    }

    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @SneakyThrows
    @Test
    @ResetDataAfter
    void getOccupationsWithLabelsWithOccupation() {
        var labels = "toto";
        var title = "title";
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).withLabels(labels).withId(UUID.fromString(ID)).withTitle(title).buildAndPersist();
        performGetAndExpect("/erhgo-occupation/labels?occupationLabels=" + labels, "expectedOccupationWithLabels", false);
    }

    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @SneakyThrows
    @Test
    @ResetDataAfter
    void getOccupationsWithLabelsWithOccupations() {
        var labels = "toto";
        var title = "title";
        var labelsBis = "toto";
        var titleBis = "titleBis";
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).withLabels(labels).withId(UUID.fromString(ID)).withTitle(title).buildAndPersist();
        var occupationBis = applicationContext.getBean(ErhgoOccupationMotherObject.class).withLabels(labelsBis).withId(UUID.fromString(ID_Bis)).withTitle(titleBis).buildAndPersist();
        performGetAndExpect("/erhgo-occupation/labels?occupationLabels=" + labels, "expectedOccupationWithLabels2", false);
    }

    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @SneakyThrows
    @Test
    @ResetDataAfter
    void getOccupationsToMatchSameLabelOnTitleAndLabels() {
        var alternativeLabel = "different label";
        var title = "different title";
        var sameLabel = "same label";

        var occupationWithSameTitle = applicationContext.getBean(ErhgoOccupationMotherObject.class).withLabels(alternativeLabel).withId(UUID.fromString(ID)).withTitle(sameLabel).buildAndPersist();
        var occupationWithSameAlternativeLabel = applicationContext.getBean(ErhgoOccupationMotherObject.class).withLabels(sameLabel).withId(UUID.fromString(ID_Bis)).withTitle(title).buildAndPersist();
        performGetAndExpect("/erhgo-occupation/labels?occupationLabels=" + sameLabel, "expectedOccupationDuplicatedTitle", false);
    }

    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @SneakyThrows
    @Test
    @ResetDataAfter
    void getOccupationsToMatchSameLabelOnTitleAndLabelsCaseAndTrimInsensitive() {
        var alternativeLabel = "different label";
        var title = "different title";
        var sameLabel = "sAme LaBel";

        var occupationWithSameTitle = applicationContext.getBean(ErhgoOccupationMotherObject.class).withLabels(alternativeLabel, "Other label 1", "Other label 2").withId(UUID.fromString(ID)).withTitle(sameLabel.toUpperCase().trim()).buildAndPersist();
        var occupationWithSameAlternativeLabel = applicationContext.getBean(ErhgoOccupationMotherObject.class).withLabels(sameLabel.toLowerCase(), "Other label 3", "Other label 4").withId(UUID.fromString(ID_Bis)).withTitle(title).buildAndPersist();

        performGetAndExpect("/erhgo-occupation/labels?occupationLabels= " + sameLabel + "  ", "expectedOccupationDuplicatedTitleCaseInsensitive", false);
    }


}


