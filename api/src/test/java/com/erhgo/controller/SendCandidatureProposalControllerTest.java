package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.userprofile.notification.RecruitmentNotification;
import com.erhgo.generators.JobGenerator;
import com.erhgo.generators.RecruitmentGenerator;
import com.erhgo.generators.UserProfileGenerator;
import com.erhgo.repositories.NotificationRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@TestPropertySource(properties = {"sendinblue.templates.classical-candidature-proposal=42"})
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
class SendCandidatureProposalControllerTest extends AbstractIntegrationTest {
    @Autowired
    private RecruitmentGenerator recruitmentGenerator;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Autowired
    private JobGenerator jobGenerator;

    @Autowired
    private NotificationRepository notificationRepository;

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void should_send_proposal_mail_to_candidates() throws Exception {
        var job = jobGenerator.createJob();
        var firstUserProfile = userProfileGenerator.createUserProfile();
        var secondUserProfile = userProfileGenerator.createUserProfile();
        var usersId = Set.of(firstUserProfile.uuid(), secondUserProfile.uuid());
        var recruitment = recruitmentGenerator.createRecruitmentWithNoRequirement(job);

        performPost("/recruitment/" + recruitment.getId() + "/send-candidature-proposal", usersId)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() ->
                assertThat(
                        usersId
                                .stream()
                                .flatMap(uuid -> notificationRepository.findRecruitmentNotificationByUserProfileUuid(uuid).stream()))
                        .extracting(RecruitmentNotification::getRecruitment)
                        .extracting(Recruitment::getId)
                        .containsExactly(recruitment.getId(), recruitment.getId()));
    }
}
