package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.openapi.dto.*;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.search.recruitment.RecruitmentIndexer;
import com.erhgo.services.sourcing.SourcingService;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.time.OffsetDateTime;

class RecruitmentIndexationIntegrationTest extends AbstractIntegrationTest {

    @MockitoBean
    RecruitmentIndexer recruitmentIndexer;

    @MockitoBean
    SourcingService sourcingService;

    @Autowired
    ApplicationContext applicationContext;

    @SneakyThrows
    @ParameterizedTest
    @EnumSource(AbstractOrganization.OrganizationType.class)
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void indexSourcingRecruitmentWhenPublish(AbstractOrganization.OrganizationType organizationType) {
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withRecruiterType(organizationType)
                .withState(RecruitmentState.DRAFT)
                .buildAndPersist();

        performPost("/recruitment/%s/change-state".formatted(recruitment.getCode()), new ChangeRecruitmentStateCommandDTO().nextState(RecruitmentStateDTO.PUBLISHED))
                .andExpect(MockMvcResultMatchers.status().isNoContent());

        Mockito.verify(recruitmentIndexer, Mockito.times(organizationType == AbstractOrganization.OrganizationType.SOURCING ? 1 : 0)).index(Mockito.assertArg(rec -> Assertions.assertThat(rec.getId()).isEqualTo(recruitment.getId())));
        Mockito.verifyNoMoreInteractions(recruitmentIndexer);
    }

    @SneakyThrows
    @ParameterizedTest
    @EnumSource(AbstractOrganization.OrganizationType.class)
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void removeSourcingRecruitmentWhenUnPublish(AbstractOrganization.OrganizationType organizationType) {
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withRecruiterType(organizationType)
                .withState(RecruitmentState.PUBLISHED)
                .buildAndPersist();

        performPost("/recruitment/%s/change-state".formatted(recruitment.getCode()), new ChangeRecruitmentStateCommandDTO().nextState(RecruitmentStateDTO.SELECTION))
                .andExpect(MockMvcResultMatchers.status().isNoContent());

        Mockito.verify(recruitmentIndexer, Mockito.times(organizationType == AbstractOrganization.OrganizationType.SOURCING ? 1 : 0)).remove(Mockito.assertArg(rec -> Assertions.assertThat(rec.getId()).isEqualTo(recruitment.getId())));
        Mockito.verifyNoMoreInteractions(recruitmentIndexer);
    }

    @SneakyThrows
    @ParameterizedTest
    @EnumSource(RecruitmentState.class)
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void indexSourcingRecruitmentOnlyWhenPublish(RecruitmentState recruitmentInitialState) {
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withRecruiterType(AbstractOrganization.OrganizationType.SOURCING)
                .withState(recruitmentInitialState)
                .buildAndPersist();

        performPost("/recruitment/%s/change-state".formatted(recruitment.getCode()), new ChangeRecruitmentStateCommandDTO().nextState(RecruitmentStateDTO.PUBLISHED))
                .andExpect(MockMvcResultMatchers.status().isNoContent());

        Mockito.verify(recruitmentIndexer).index(Mockito.assertArg(rec -> Assertions.assertThat(rec.getId()).isEqualTo(recruitment.getId())));
        Mockito.verifyNoMoreInteractions(recruitmentIndexer);
    }

    @SneakyThrows
    @ParameterizedTest
    @EnumSource(RecruitmentState.class)
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void removeSourcingRecruitmentOnlyWhenUnPublish(RecruitmentState recruitmentInitialState) {
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withRecruiterType(AbstractOrganization.OrganizationType.SOURCING)
                .withState(recruitmentInitialState)
                .buildAndPersist();

        performPost("/recruitment/%s/change-state".formatted(recruitment.getCode()), new ChangeRecruitmentStateCommandDTO().nextState(RecruitmentStateDTO.SELECTION))
                .andExpect(MockMvcResultMatchers.status().isNoContent());

        Mockito.verify(recruitmentIndexer, Mockito.times(recruitmentInitialState == RecruitmentState.PUBLISHED ? 1 : 0)).remove(Mockito.assertArg(rec -> Assertions.assertThat(rec.getId()).isEqualTo(recruitment.getId())));
        Mockito.verifyNoMoreInteractions(recruitmentIndexer);
    }

    @SneakyThrows
    @ParameterizedTest
    @ValueSource(booleans = {false, true})
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void changeSourcingState_populatesEvent(boolean shouldIndex) {
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withRecruiterType(AbstractOrganization.OrganizationType.SOURCING)
                .withState(shouldIndex ? RecruitmentState.DRAFT : RecruitmentState.PUBLISHED)
                .buildAndPersist();

        Mockito.when(sourcingService.getSourcingSubscription()).thenReturn(new SourcingSubscriptionDTO().expirationDate(OffsetDateTime.MAX).organizationCode(recruitment.getRecruiterCode()).subscriptionType(SourcingSubscriptionTypeDTO.ACTIVATED));

        performPost("/sourcing/recruitment/%d/change-state".formatted(recruitment.getId()), new ChangeSourcingRecruitmentStateCommandDTO().sendNotifications(null).nextState(shouldIndex ? RecruitmentStateDTO.PUBLISHED : RecruitmentStateDTO.SELECTION))
                .andExpect(MockMvcResultMatchers.status().isOk());

        if (shouldIndex) {
            Mockito.verify(recruitmentIndexer).index(Mockito.assertArg(rec -> Assertions.assertThat(rec.getId()).isEqualTo(recruitment.getId())));
        } else {
            Mockito.verify(recruitmentIndexer).remove(Mockito.assertArg(rec -> Assertions.assertThat(rec.getId()).isEqualTo(recruitment.getId())));
        }

        Mockito.verifyNoMoreInteractions(recruitmentIndexer);
    }
}
