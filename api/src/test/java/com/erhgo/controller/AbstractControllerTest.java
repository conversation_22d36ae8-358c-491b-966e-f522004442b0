package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.config.ApiConstants;
import com.erhgo.domain.dummy.DummyEntity;
import com.erhgo.repositories.dummy.DummyController;
import com.erhgo.repositories.dummy.DummyService;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.AbstractService;
import org.apache.commons.io.IOUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.ArrayList;
import java.util.Arrays;

import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.iterableWithSize;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class AbstractControllerTest extends AbstractIntegrationTestWithFixtures {

    @MockitoBean
    private DummyService dummyService;

    @Before
    public void setUp() throws Exception {
        final var entity = new DummyEntity();
        entity.setId(1L);
        entity.setCode("D-001");
        entity.setTitle("title");
        entity.setDescription("description");

        final var unsavedEntity = new DummyEntity();
        entity.setId(null);
        entity.setCode("D-001");
        entity.setTitle("title");
        entity.setDescription("description");

        Mockito.when(dummyService.count()).thenReturn(1l);

        Mockito.when(dummyService.findPaginatedAndFilteredByProperty(0, 10, "code", "ASC", "001")).thenReturn(new AbstractService.PageDTOAdapter<>(new PageImpl<>(Arrays.asList(entity))));
        Mockito.when(dummyService.findPaginatedAndFilteredByProperty(0, 10, "title", "ASC", "title")).thenReturn(new AbstractService.PageDTOAdapter<>(new PageImpl<>(Arrays.asList(entity))));
        Mockito.when(dummyService.findPaginatedAndFilteredByProperty(0, 10, "code", "ASC", "XX")).thenReturn(new AbstractService.PageDTOAdapter<>(new PageImpl<>(new ArrayList<>())));
        Mockito.when(dummyService.findPaginatedAndFilteredByProperty(0, 10, "title", "ASC", "XX")).thenReturn(new AbstractService.PageDTOAdapter<>(new PageImpl<>(new ArrayList<>())));

        Mockito.when(dummyService.findAll()).thenReturn(Arrays.asList(entity));

        Mockito.when(dummyService.findOneByCode("D-001")).thenReturn(entity);
        Mockito.when(dummyService.findOneByCode("D-404")).thenReturn(null);

        Mockito.when(dummyService.create(unsavedEntity)).thenReturn(entity);
        Mockito.when(dummyService.update(entity)).thenReturn(entity);
    }

    @After
    public void tearDown() throws Exception {
    }

    @Test
    @WithMockKeycloakUser
    public void should_count_and_return_1l() throws Exception {
        // @formatter:off
        mvc.perform(get(DummyController.API_ABSTRACT_DUMMY_TEST + ApiConstants.SEPARATOR
                + "count").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string("1"));
        // @formatter:on
    }

    @Test
    public void should_count_and_return_unauthorized() throws Exception {
        // @formatter:off
        mvc.perform(get(DummyController.API_ABSTRACT_DUMMY_TEST + ApiConstants.SEPARATOR
                + "count").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized());
        // @formatter:on
    }

    @Test
    @WithMockKeycloakUser
    public void should_find_all_existing_AbstractEntity_by_code_and_return_DummyEntity_list() throws Exception {
        // @formatter:off
        mvc.perform(get(DummyController.API_ABSTRACT_DUMMY_TEST + ApiConstants.SEPARATOR
                + "list/all").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("application/hal+json;charset=UTF-8"))
                .andExpect(jsonPath("$[0]code", is("D-001")))
                .andExpect(jsonPath("$[0]title", is("title")))
                .andExpect(jsonPath("$[0]description", is("description")));
        // @formatter:on
    }

    @Test
    public void should_find_all_existing_AbstractEntity_by_code_and_return_unauthorized() throws Exception {
        // @formatter:off
        mvc.perform(get(DummyController.API_ABSTRACT_DUMMY_TEST + ApiConstants.SEPARATOR
                + "list/all").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized());
        // @formatter:on
    }

    @Test
    @WithMockKeycloakUser
    public void should_find_an_existing_AbstractEntity_by_code_and_return_DummyEntity() throws Exception {
        // @formatter:off
        mvc.perform(get(DummyController.API_ABSTRACT_DUMMY_TEST + ApiConstants.SEPARATOR
                + "list?page=0&size=10&by=code&direction=ASC&filter=001").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("application/hal+json;charset=UTF-8"))
                .andExpect(jsonPath("$.content[0].code", is("D-001")))
                .andExpect(jsonPath("$.content[0].title", is("title")))
                .andExpect(jsonPath("$.pageIndex", is(0)))
                .andExpect(jsonPath("$.numberOfElementsInPage", is(1)))
                .andExpect(jsonPath("$.pageSize", is(1)))
                .andExpect(jsonPath("$.totalPages", is(1)));
        // @formatter:on
    }

    @Test
    public void should_find_an_existing_AbstractEntity_by_code_and_return_unauthorized() throws Exception {
        // @formatter:off
        mvc.perform(get(DummyController.API_ABSTRACT_DUMMY_TEST + ApiConstants.SEPARATOR
                + "list?page=0&size=10&by=code&direction=ASC&filter=001").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized());
        // @formatter:on
    }


    @Test
    @WithMockKeycloakUser
    public void should_find_an_existing_AbstractEntity_by_title_and_return_DummyEntity_list() throws Exception {
        // @formatter:off
        mvc.perform(get(DummyController.API_ABSTRACT_DUMMY_TEST + ApiConstants.SEPARATOR
                + "list?page=0&size=10&by=title&direction=ASC&filter=title").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("application/hal+json;charset=UTF-8"))
                .andExpect(jsonPath("$.content[0].code", is("D-001")))
                .andExpect(jsonPath("$.content[0].title", is("title")))
                .andExpect(jsonPath("$.content[0].description", is("description")));
        // @formatter:on
    }

    @Test
    @WithMockKeycloakUser
    public void should_not_find_an_unexisting_AbstractEntity_by_code_like_and_return_DummyEntity_list() throws Exception {
        // @formatter:off
        mvc.perform(get(DummyController.API_ABSTRACT_DUMMY_TEST + ApiConstants.SEPARATOR
                + "list?page=0&size=10&by=code&direction=ASC&filter=XX").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", iterableWithSize(0)));
        // @formatter:on
    }

    @Test
    @WithMockKeycloakUser
    public void should_not_find_an_unexisting_AbstractEntity_by_title_like() throws Exception {
        // @formatter:off
        mvc.perform(get(DummyController.API_ABSTRACT_DUMMY_TEST + ApiConstants.SEPARATOR
                + "list?page=0&size=10&by=title&direction=ASC&filter=XX").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", iterableWithSize(0)));
        // @formatter:on
    }

    @Test
    @WithMockKeycloakUser
    public void should_not_get_all_AbstractEntities_if_no_filter_specified() throws Exception {
        // @formatter:off
        mvc.perform(get(DummyController.API_ABSTRACT_DUMMY_TEST + ApiConstants.SEPARATOR
                + "list?page=0&size=10&by=title&direction=ASC").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string(""));
        // @formatter:on
    }

    @Test
    @WithMockKeycloakUser
    public void should_find_one_AbstractEntities_by_code_when_entity_exist() throws Exception {
        // @formatter:off
        mvc.perform(get(DummyController.API_ABSTRACT_DUMMY_TEST + ApiConstants.SEPARATOR
                + "D-001").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("application/hal+json;charset=UTF-8"))
                .andExpect(jsonPath("$.code", is("D-001")))
                .andExpect(jsonPath("$.title", is("title")))
                .andExpect(jsonPath("$.description", is("description")));
        // @formatter:on
    }

    @Test
    @WithMockKeycloakUser
    public void should_return_a_404_when_code_does_not_exist() throws Exception {
        // @formatter:off
        mvc.perform(get(DummyController.API_ABSTRACT_DUMMY_TEST + ApiConstants.SEPARATOR
                + "D-404").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
        // @formatter:on
    }

    @Test
    public void should_not_find_one_AbstractEntities_by_code_when_unauthorized() throws Exception {
        // @formatter:off
        mvc.perform(get(DummyController.API_ABSTRACT_DUMMY_TEST + ApiConstants.SEPARATOR
                + "D-001").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized());
        // @formatter:on
    }

    @Test
    @WithMockKeycloakUser
    public void should_create_DummyEntity_and_return_entity_saved() throws Exception {
        final var content = IOUtils.toString(this.getClass().getClassLoader().getResourceAsStream("data/dummyEntityToSave.json"));
        // @formatter:off
        mvc.perform(post(DummyController.API_ABSTRACT_DUMMY_TEST + ApiConstants.SEPARATOR + "create")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("application/hal+json;charset=UTF-8"))
                .andExpect(jsonPath("$.code", is("D-001")))
                .andExpect(jsonPath("$.title", is("title")))
                .andExpect(jsonPath("$.description", is("description")));
        // @formatter:on
    }

    @Test
    public void should_not_create_DummyEntity_and_return_unauthorized() throws Exception {
        final var content = IOUtils.toString(this.getClass().getClassLoader().getResourceAsStream("data/dummyEntityToSave.json"));
        // @formatter:off
        mvc.perform(post(DummyController.API_ABSTRACT_DUMMY_TEST + ApiConstants.SEPARATOR + "create")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized());
        // @formatter:on
    }

    @Test
    @WithMockKeycloakUser
    public void should_update_DummyEntity_and_return_entity_saved() throws Exception {
        final var content = IOUtils.toString(this.getClass().getClassLoader().getResourceAsStream("data/dummyEntityToUpdate.json"));
        // @formatter:off
        mvc.perform(patch(DummyController.API_ABSTRACT_DUMMY_TEST + ApiConstants.SEPARATOR + "update")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("application/hal+json;charset=UTF-8"))
                .andExpect(jsonPath("$.code", is("D-001")))
                .andExpect(jsonPath("$.title", is("title")))
                .andExpect(jsonPath("$.description", is("description")));
        // @formatter:on
    }

    @Test
    public void should_not_update_DummyEntity_and_return_unauthorized() throws Exception {
        final var content = IOUtils.toString(this.getClass().getClassLoader().getResourceAsStream("data/dummyEntityToUpdate.json"));
        // @formatter:off
        mvc.perform(patch(DummyController.API_ABSTRACT_DUMMY_TEST + ApiConstants.SEPARATOR + "update")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized());
        // @formatter:on
    }
}
