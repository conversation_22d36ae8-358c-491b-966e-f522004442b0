package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.TransactionTestHelper;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.openapi.dto.SaveQuestionForContextsCommandDTO;
import com.erhgo.openapi.dto.SuggestedAnswersDTO;
import com.erhgo.repositories.QuestionForContextsRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.UUID;

import static com.erhgo.TestUtils.jsonMatchesContent;
import static com.erhgo.generators.TestFixtures.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasSize;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class QuestionForContextsControllerTest extends AbstractIntegrationTestWithFixtures {

    public static final String NONE = "None";
    public static final String LOW = "Low";
    public static final String MED = "Med";
    public static final String HIGH = "High";
    public static final String TITLE = "What about ?";
    @Autowired
    private QuestionForContextsRepository questionForContextsRepository;

    @Autowired
    private TransactionTestHelper transactionTestHelper;

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void should_create_question_contexts() throws Exception {
        var countBefore = questionForContextsRepository.count();
        SaveQuestionForContextsCommandDTO command = doSaveContextQuestion(UUID.randomUUID(), "");

        transactionTestHelper.doInTransaction(() -> {
            var saveQuestionContexts = questionForContextsRepository.findById(command.getId()).orElseThrow();
            assertThat(saveQuestionContexts.getTitleForNone()).isEqualTo(NONE);
            assertThat(saveQuestionContexts.getTitleForLow()).isEqualTo(LOW);
            assertThat(saveQuestionContexts.getTitleForMedium()).isEqualTo(MED);
            assertThat(saveQuestionContexts.getTitleForHigh()).isEqualTo(HIGH);
            assertThat(saveQuestionContexts.getTitle()).isEqualTo(TITLE);
            assertThat(saveQuestionContexts.getContexts()).containsExactlyInAnyOrder(CONTEXT_USED_IN_JOB_OF_FORMATION, CONTEXT_USED_IN_JOB_OF_FORMATION_WITH_CUSTOM_QUESTION);
            assertThat(questionForContextsRepository.count()).isEqualTo(countBefore + 1);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void should_update_question_contexts() throws Exception {
        var countBefore = questionForContextsRepository.count();

        var commandUUID = UUID.randomUUID();
        doSaveContextQuestion(commandUUID, "");
        var updateSuffix = "update";
        doSaveContextQuestion(commandUUID, updateSuffix);

        transactionTestHelper.doInTransaction(() -> {
            var saveQuestionContexts = questionForContextsRepository.findById(commandUUID).orElseThrow();
            assertThat(saveQuestionContexts.getTitleForNone()).isEqualTo(NONE + updateSuffix);
            assertThat(saveQuestionContexts.getTitleForLow()).isEqualTo(LOW + updateSuffix);
            assertThat(saveQuestionContexts.getTitleForMedium()).isEqualTo(MED + updateSuffix);
            assertThat(saveQuestionContexts.getTitleForHigh()).isEqualTo(HIGH + updateSuffix);
            assertThat(saveQuestionContexts.getTitle()).isEqualTo(TITLE + updateSuffix);
            assertThat(saveQuestionContexts.getContexts()).containsExactlyInAnyOrder(CONTEXT_USED_IN_JOB_OF_FORMATION, CONTEXT_USED_IN_JOB_OF_FORMATION_WITH_CUSTOM_QUESTION);
            assertThat(questionForContextsRepository.count()).isEqualTo(countBefore + 1);
        });
    }

    private SaveQuestionForContextsCommandDTO doSaveContextQuestion(UUID uuid, String suffix) throws Exception {
        var command = getSaveQuestionForContextsCommandDTO(uuid, suffix)
                .contexts(Arrays.asList(CONTEXT_USED_IN_JOB_OF_FORMATION.getId(), CONTEXT_USED_IN_JOB_OF_FORMATION_WITH_CUSTOM_QUESTION.getId()));

        performPut("/api/odas/questionContext/save", command)
                .andExpect(status().isNoContent());
        return command;
    }

    private SaveQuestionForContextsCommandDTO getSaveQuestionForContextsCommandDTO(UUID uuid, String suffix) {
        return new SaveQuestionForContextsCommandDTO()
                .id(uuid)

                .suggestedAnswers(
                        new SuggestedAnswersDTO()
                                .none(NONE + suffix)
                                .low(LOW + suffix)
                                .medium(MED + suffix)
                                .high(HIGH + suffix))
                .title(TITLE + suffix);
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    public void should_get_list_question_contexts_by_context_id() throws Exception {
        mvc.perform(get("/api/odas/questionContext/list/" + CONTEXT_USED_IN_JOB_OF_FORMATION.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonMatchesContent("questionContextListByContextId"));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    public void should_get_page_question_contexts() throws Exception {
        mvc.perform(get("/api/odas/questionContext/list?page=0&size=5&by=title&direction=ASC"))
                .andExpect(status().isOk())
                .andExpect(jsonMatchesContent("questionForContextsPage"));
    }


    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    public void should_get_question_contexts_by_id() throws Exception {
        mvc.perform(get("/api/odas/questionContext/" + QUESTION_FOR_CONTEXTS_1.getUuid()))
                .andExpect(status().isOk())
                .andExpect(jsonMatchesContent("questionForContextsById"));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    public void should_contexts_not_used_in_context_question_return_empty_recruitment_profile_list() throws Exception {
        mvc.perform(get("/api/odas/questionContext/" + QUESTION_FOR_CONTEXTS_1.getUuid() + "/context/recruitmentProfiles/" + CT_08.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[*]", hasSize(0)));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    public void should_contexts_used_in_context_question_return_recruitment_profile_list() throws Exception {
        mvc.perform(get("/api/odas/questionContext/" + P_01.getQuestionForContext(CONTEXT_USED_IN_JOB_WITH_QUESTION).orElseThrow().getUuid() + "/context/recruitmentProfiles/" + CONTEXT_USED_IN_JOB_WITH_QUESTION.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonMatchesContent("recruitmentProfilesByContextQuestionContextId"));
    }
}
