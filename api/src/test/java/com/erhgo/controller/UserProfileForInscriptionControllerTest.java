package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.experience.UserExperience;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.ErhgoOccupationGenerator;
import com.erhgo.generators.JobActivityLabelGenerator;
import com.erhgo.generators.UserProfileGenerator;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.dtobuilder.GeneralInformationDTOBuilder;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailingListService;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Date;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


class UserProfileForInscriptionControllerTest extends AbstractIntegrationTest {

    @MockitoBean
    private KeycloakMockService keycloakMockService;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private ErhgoOccupationGenerator erhgoOccupationGenerator;

    @Autowired
    private CapacityGenerator capacityGenerator;

    @Autowired
    private JobActivityLabelGenerator activityLabelGenerator;

    @MockitoBean
    private MailingListService mailingListService;

    private static final String USER_ID = "d01cd104-04df-40a4-9348-5033f78fbb03";

    @BeforeEach
    public void createUser() {
        txHelper.doInTransaction(() -> userProfileGenerator.createUserProfile(UUID.fromString(USER_ID)));
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void should_update_salary_range() throws Exception {
        var salary = 2500;
        performPost("/api/odas/user/salary", new SetUserSalaryCommandDTO().salary(salary))
                .andExpect(status().isNoContent());
        var persisted = userProfileRepository.findByUserId(USER_ID).map(UserProfile::generalInformation).orElseThrow();

        verify(mailingListService).updateSalary(argThat(up -> up.generalInformation().getSalary() == salary));
        assertThat(persisted.getSalary()).isEqualTo(salary);
        assertThat(persisted.getUserProfile().indexationRequiredDate()).isCloseTo(new Date(), 3_600_000);
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void should_not_index_if_salary_is_not_updated() throws Exception {
        var salary = 2500;
        txHelper.doInTransaction(() -> {
            var user = userProfileRepository.findByUserId(USER_ID).orElseThrow();
            user.generalInformation().setSalary(salary);
            user.indexationRequiredDate(null);
        });
        performPost("/api/odas/user/salary", new SetUserSalaryCommandDTO().salary(salary))
                .andExpect(status().isNoContent());
        var persisted = userProfileRepository.findByUserId(USER_ID).map(UserProfile::generalInformation).orElseThrow();
        assertThat(persisted.getSalary()).isEqualTo(salary);
        assertThat(persisted.getUserProfile().indexationRequiredDate()).isNull();
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void should_update_situation() throws Exception {
        var situation = SituationDTO.EMPLOYEE;
        performPost("/api/odas/user/situation", new SetUserSituationCommandDTO().situation(situation))
                .andExpect(status().isNoContent());
        var persisted = userProfileRepository.findByUserId(USER_ID).map(UserProfile::generalInformation).orElseThrow();

        Mockito.verify(mailingListService).updateSituation(persisted.getUserProfile());

        assertThat(persisted.getSituation()).hasToString(situation.name());
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void should_update_situation_and_delay() throws Exception {
        var situation = SituationDTO.RESEARCHING;
        performPost("/api/odas/user/situation", new SetUserSituationCommandDTO().situation(situation).delayInMonth(6))
                .andExpect(status().isNoContent());
        var persisted = userProfileRepository.findByUserId(USER_ID).map(UserProfile::generalInformation).orElseThrow();
        assertThat(persisted.getSituation()).hasToString(situation.name());
        assertThat(persisted.getDelayInMonth()).isEqualTo(6);
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void should_update_city() throws Exception {
        var command = new SetUserCityCommandDTO().location(GeneralInformationDTOBuilder.buildLocationDTO(Location
                .builder()
                .city("Lille")
                .postcode("59000")
                .citycode("59350")
                .departmentCode("59")
                .regionName("Hauts-de-France")
                .latitude((float) 50.6291)
                .longitude((float) 3.04584)
                .radiusInKm(50)
                .build()));
        var userRepresentation = new UserRepresentation();
        userRepresentation.setId(USER_ID);
        userRepresentation.setEmail("<EMAIL>");
        Mockito.when(keycloakMockService.getFrontOfficeUserProfile(USER_ID)).thenReturn(Optional.of(userRepresentation));
        performPost("/api/odas/user/city", command).andExpect(status().isNoContent());
        var persisted = userProfileRepository.findByUserId(USER_ID).map(UserProfile::generalInformation).orElseThrow();
        Assertions.assertThat(persisted.getLocation().getCity()).hasToString(command.getLocation().getCity());
        Assertions.assertThat(persisted.getLocation().getPostcode()).hasToString(command.getLocation().getPostcode());
        Assertions.assertThat(persisted.getLocation().getCitycode()).hasToString(command.getLocation().getCitycode());
        Assertions.assertThat(persisted.getLocation().getDepartmentCode()).hasToString(command.getLocation().getDepartmentCode());
        Assertions.assertThat(persisted.getLocation().getRegionName()).hasToString(command.getLocation().getRegionName());
        Assertions.assertThat(persisted.getLocation().getLatitude()).hasToString(String.valueOf((command.getLocation().getLatitude())));
        Assertions.assertThat(persisted.getLocation().getLongitude()).hasToString(String.valueOf(command.getLocation().getLongitude()));
        Assertions.assertThat(persisted.getLocation().getRadiusInKm()).isEqualTo(command.getLocation().getRadiusInKm());
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void reset_city() throws Exception {
        var command = new SetUserCityCommandDTO();
        txHelper.doInTransaction(() -> {
            applicationContext.getBean(UserProfileRepository.class).findByUserId(USER_ID).orElseThrow().generalInformation().setLocation(Location.builder().city("Pau").postcode("30000").radiusInKm(50).latitude(42f).longitude(422f).build());
        });
        performPost("/api/odas/user/city", command).andExpect(status().isNoContent());
        var persisted = userProfileRepository.findByUserId(USER_ID).map(UserProfile::generalInformation).orElseThrow();
        Assertions.assertThat(persisted.getLocation()).isNull();
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void should_update_name() throws Exception {
        var command = new SaveUserNameCommandDTO()
                .firstName("Jean")
                .lastName("Dupont");
        performPost("/api/odas/user/name", command).andExpect(status().isNoContent());

        Mockito.verify(keycloakMockService, Mockito.times(1)).setFrontOfficeUserName(USER_ID, command);
        Mockito.verify(mailingListService).updateName(any());
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void select_occupation() throws Exception {
        var capacity = capacityGenerator.createCapacity("CA2-042");
        var title = "occupationtitle";
        var occupation = erhgoOccupationGenerator.createErhgoOccupation(new ErhgoOccupationMotherObject()
                .withLevel(MasteryLevel.MAX_LEVEL)
                .withCapacities(capacity)
                .instance());

        performPost("/api/odas/user/occupation", new SetUserOccupationCommandDTO().title(title).occupationId(occupation.getId()))
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var persisted = userProfileRepository.findByUserId(USER_ID).orElseThrow();
            assertThat(persisted.experiences())
                    .hasSize(1)
                    .extracting(UserExperience::getOccupationId)
                    .containsExactly(occupation.getId());
            assertThat(persisted.capacityOccurrences())
                    .singleElement()
                    .matches(c -> c.getCapacity().equals(capacity) && c.getOccurrence() == 1);
            assertThat(((ErhgoOccupation) ReflectionTestUtils.getField(persisted.userRegistrationState(), "selectedOccupation")).getId()).isEqualTo(occupation.getId());
            assertThat(((String) ReflectionTestUtils.getField(persisted.userRegistrationState(), "jobTitle"))).isEqualTo(title);
            assertThat(persisted.masteryLevel()).extracting(Float::intValue).isEqualTo(MasteryLevel.MAX_LEVEL.getMasteryLevel());
            verify(mailingListService).updateNumberOfExperiences(persisted);
        });
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void tagNoExperienceForUser() throws Exception {
        performPost("/api/odas/user/no-experience", null)
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var persisted = userProfileRepository.findByUserId(USER_ID).orElseThrow();
            assertThat(persisted.experiences()).isEmpty();
            assertThat(persisted.capacityOccurrences()).isEmpty();
        });
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void should_update_phone_number() throws Exception {
        var phoneNumber = "0701020304";
        var command = new SetUserPhoneNumberCommandDTO().phoneNumber(phoneNumber);
        var userRepresentation = new UserRepresentation();
        userRepresentation.setId(USER_ID);
        userRepresentation.setEmail("<EMAIL>");
        Mockito.when(keycloakMockService.getFrontOfficeUserProfile(USER_ID)).thenReturn(Optional.of(userRepresentation));
        performPost("/api/odas/user/phone-number", command).andExpect(status().isNoContent());
        var user = userProfileRepository.findByUserId(USER_ID).orElseThrow();
        var persisted = user.generalInformation();
        Assertions.assertThat(persisted.getPhoneNumber()).hasToString(command.getPhoneNumber());
        Mockito.verify(mailingListService).updatePhoneNumber(user);
    }
}
