package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.TestUtils;
import com.erhgo.config.ApiConstants;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.TypeContractCategory;
import com.erhgo.domain.enums.TypeWorkingTime;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.job.JobMotherObject;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.generators.CriteriaMotherObject;
import com.erhgo.generators.JobGenerator;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;

import java.util.Map;

import static com.erhgo.generators.TestFixtures.*;
import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class JobSearchControllerTest extends AbstractIntegrationTestWithFixtures {

    @Autowired
    private JobGenerator jobGenerator;

    @Autowired
    private OrganizationGenerator organizationGenerator;

    @Autowired
    private KeycloakMockService keycloakMockService;

    @Autowired
    private ApplicationContext applicationContext;

    private static final String EMPLOYER_CODE = "M-02";

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_01_CERA_CODE})
    void should_filter_by_recruiter_and_job_code() throws Exception {
        mvc.perform(get(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + "list?organizationCodes=" + E_01_CERA_CODE + "&page=0&size=10&direction=ASC&by=title&filter=J")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", hasSize(0)));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void should_filter_by_employer() throws Exception {
        filterByEmployer();
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void should_filter_by_employer_and_title() throws Exception {
        filterByEmployerAndTitle();
    }


    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, EMPLOYER_CODE})
    @ResetDataAfter
    void should_filter_by_employer_hr() throws Exception {
        filterByEmployer();
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, EMPLOYER_CODE})
    @ResetDataAfter
    void should_get_employer_published_job() throws Exception {
        var job = jobGenerator.createJobForEmployer(EMPLOYER_CODE);

        mvc.perform(get(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + "published" + ApiConstants.SEPARATOR + EMPLOYER_CODE)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[*].id", contains(job.getId().toString())));
    }

    @ParameterizedTest
    @ValueSource(strings = {"", "title", "service", "recruiter", "employer"})
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void verify_order_by(String sortBy) throws Exception {

        mvc.perform(get("/api/odas/job/list?organizationCodes=" + EMPLOYER_CODE + "&page=0&size=10&direction=ASC&by=" + sortBy + "&filter=")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, EMPLOYER_CODE})
    @ResetDataAfter
    void should_filter_by_employer_and_title_hr() throws Exception {
        filterByEmployerAndTitle();
    }

    private void filterByEmployer() throws Exception {
        var job = jobGenerator.createJobForEmployer(EMPLOYER_CODE);

        mvc.perform(get(ApiConstants.API_ODAS_JOB + "/list?organizationCodes=" + EMPLOYER_CODE + "&page=0&size=10&direction=ASC&by=title&filter=")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].id", contains(job.getId().toString())));
    }

    private void filterByEmployerAndTitle() throws Exception {
        var employerCode = "M-02";
        var jobCode = "J_M1";
        var employer = organizationGenerator.createEmployer(
                employerCode,
                organizationGenerator.createRecruiter("T-05", AbstractOrganization.OrganizationType.TERRITORIAL));
        var job = jobGenerator.createJob(jobCode, employer);
        jobGenerator.createJob("No way", employer);

        mvc.perform(get(ApiConstants.API_ODAS_JOB + "/list?organizationCodes=" + employerCode + "&page=0&size=10&direction=ASC&by=title&filter=J")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].id", contains(job.getId().toString())));
    }


    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    void filterByService() throws Exception {
        var expectedJob = applicationContext.getBean(JobMotherObject.class)
                .withRecruiter(E_02_SOGILIS)
                .withService("serVice")
                .buildAndPersist();

        var otherJob = applicationContext.getBean(JobMotherObject.class)
                .withRecruiter(E_02_SOGILIS)
                .withService("ko")
                .buildAndPersist();

        mvc.perform(get(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + "list?organizationCodes=" + E_02_SOGILIS_CODE + "&page=0&size=10&direction=ASC&by=title&filter=eRv")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].id", contains(expectedJob.getId().toString())))
                .andExpect(jsonPath("$.totalNumberOfElements", is(1)));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    void filterByEmployerName() throws Exception {
        var expectedEmployer = organizationGenerator.createEmployer(
                "M-42",
                E_02_SOGILIS);
        var otherEmployer = organizationGenerator.createEmployer(
                "C-02", E_02_SOGILIS);

        var expectedJob = applicationContext.getBean(JobMotherObject.class)
                .withRecruiter(E_02_SOGILIS)
                .withEmployer(expectedEmployer)
                .buildAndPersist();

        var otherJob = applicationContext.getBean(JobMotherObject.class)
                .withRecruiter(E_02_SOGILIS)
                .withEmployer(otherEmployer)
                .buildAndPersist();

        mvc.perform(get(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + "list?organizationCodes=" + E_02_SOGILIS_CODE + "&page=0&size=10&direction=ASC&by=title").param("filter", "E M-42")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].id", contains(expectedJob.getId().toString())))
                .andExpect(jsonPath("$.content", iterableWithSize(1)));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_01_CERA_CODE})
    void should_allow_no_by_property() throws Exception {
        mvc.perform(get(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + "list?organizationCodes=" + E_01_CERA_CODE + "&page=0&size=10&direction=ASC&filter=J")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", hasSize(0)));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    void should_search_filter_with_uppercase_return_good_job() throws Exception {
        mvc.perform(get(ApiConstants.API_ODAS_JOB + "/list?organizationCodes=" + E_02_SOGILIS_CODE + "&page=0&size=10&direction=ASC&by=title&filter=DEVELOPER fullstack")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", hasSize(1)));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    void should_filter_by_recruiter() throws Exception {

        applicationContext.getBean(JobMotherObject.class)
                .withRecruiter(E_02_SOGILIS)
                .withContractCategories(TypeContractCategory.PERMANENT)
                .withWorkingTimes(TypeWorkingTime.PART_TIME)
                .withTitle("Job with criteria")
                .buildAndPersist();

        mvc.perform(get(ApiConstants.API_ODAS_JOB + "/list?organizationCodes=" + E_02_SOGILIS_CODE + "&page=0&size=10&direction=ASC&by=title")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(TestUtils.jsonMatchesContentWithOrderedArray("jobsList"));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    void should_find_an_existing_job_by_title() throws Exception {
        mvc.perform(get(ApiConstants.API_ODAS_JOB + "/list?organizationCodes=" + E_02_SOGILIS_CODE +
                        "&page=0&size=10&by=title&direction=ASC&filter=" + J_01.getTitle().substring(1, J_01.getTitle().length() - 1)).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[0].title", is(J_01.getTitle())))
                .andExpect(jsonPath("$.content", hasSize(1)));
    }


    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void find_job_for_ot_and_projects_restrict_strictly() throws Exception {
        var otCode = "T-555";
        var jobCodeOfOt = "J-4545";
        var jobCodeOfProject = "J-454545";

        var jobs = createOtAndProjectWithJobs(otCode, jobCodeOfOt, jobCodeOfProject);

        mvc.perform(get(ApiConstants.API_ODAS_JOB + "/list?organizationCodes=" + otCode +
                        "&page=0&size=10&by=title&direction=ASC&filter=Job&strictOrganizationFilter=true")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[0].id", is(jobs.get(jobCodeOfOt).getId().toString())))
                .andExpect(jsonPath("$.content", hasSize(1)));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void find_jobs_for_ot_and_projects_unrestricted() throws Exception {
        var otCode = "T-555";
        var jobCodeOfOt = "J-4545";
        var jobCodeOfProject = "J-454545";

        var jobs = createOtAndProjectWithJobs(otCode, jobCodeOfOt, jobCodeOfProject);

        mvc.perform(get(ApiConstants.API_ODAS_JOB + "/list?organizationCodes=" + otCode +
                        "&page=0&size=10&by=title&direction=ASC&filter=Job&strictOrganizationFilter=false")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].id", containsInAnyOrder(
                        jobs.get(jobCodeOfOt).getId().toString(),
                        jobs.get(jobCodeOfProject).getId().toString()))
                )
                .andExpect(jsonPath("$.content", hasSize(2)));
    }

    private Map<String, Job> createOtAndProjectWithJobs(String otCode, String jobCodeOfOt, String jobCodeOfProject) {
        var projectCode = "P-1212";
        var ot = organizationGenerator.createRecruiter(otCode, AbstractOrganization.OrganizationType.TERRITORIAL);
        var project = organizationGenerator.createRecruiter(projectCode, AbstractOrganization.OrganizationType.PROJECT);
        var other = organizationGenerator.createRecruiter("T-222", AbstractOrganization.OrganizationType.TERRITORIAL);
        keycloakMockService.createBackOfficeGroupAndRoles(otCode, otCode, projectCode);

        var jobOfOt = jobGenerator.createJob(jobCodeOfOt, ot, "Job of ot");
        var jobOfProject = jobGenerator.createJob(jobCodeOfProject, project, "Job of project");
        jobGenerator.createJob("J-22222", other, "Other job");
        return Map.of(jobCodeOfOt, jobOfOt, jobCodeOfProject, jobOfProject);
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void listJobsWithWorkingTime() throws Exception {

        var workingTimeCriteria = applicationContext.getBean(CriteriaMotherObject.class)
                .withValueCodes(CriteriaValue.WORKING_TIME_FOR_CRITERIA_RESPONSE.keySet().toArray(String[]::new))
                .buildAndPersist();

        var jobMotherObject = applicationContext.getBean(JobMotherObject.class);

        var jobSelectedWorkingTime = workingTimeCriteria.getCriteriaValues().stream().filter(c -> c.getCode().equals("REP-2-2")).findFirst().orElseThrow();

        var newJob = jobMotherObject.withCriteriaValues(jobSelectedWorkingTime).buildAndPersist();

        mvc.perform(get(ApiConstants.API_ODAS_JOB + "/list?organizationCodes=" + newJob.getRecruiterCode() + "&page=0&size=10&direction=ASC&by=title&filter=J")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].id", contains(newJob.getId().toString())))
                .andExpect(jsonPath("$.content[*].workingTimes[*]", contains("PART_TIME")));

    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void listJobsWithWorkingTimes() throws Exception {
        var jobMotherObject = applicationContext.getBean(JobMotherObject.class);

        var newJob = jobMotherObject.withWorkingTimes(TypeWorkingTime.FULL_TIME, TypeWorkingTime.PART_TIME).buildAndPersist();
        mvc.perform(get(ApiConstants.API_ODAS_JOB + "/list?organizationCodes=" + newJob.getRecruiterCode() + "&page=0&size=10&direction=ASC&by=title&filter=J")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].id", contains(newJob.getId().toString())))
                .andExpect(jsonPath("$.content[*].workingTimes[*]", containsInAnyOrder("FULL_TIME", "PART_TIME")));

    }
}
