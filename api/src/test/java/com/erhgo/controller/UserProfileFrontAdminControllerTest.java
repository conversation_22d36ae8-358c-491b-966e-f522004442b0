package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.openapi.dto.ResendInitialMailCommandDTO;
import com.erhgo.openapi.dto.SetFrontOfficeUserPasswordCommandDTO;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class UserProfileFrontAdminControllerTest extends AbstractIntegrationTest {

    @MockitoBean
    private KeycloakMockService keycloakService;

    private static final String USER_ID = "d01cd104-04df-40a4-9348-5033f78fbb03";

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void resendEmailShouldSucceed() throws Exception {
        performPost("/user/front-office/resend-initial-mail", new ResendInitialMailCommandDTO().userId(USER_ID))
                .andExpect(status().isNoContent());
        Mockito.verify(keycloakService).resetFOPassword(USER_ID);
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void resetPasswordShouldSucceed() throws Exception {
        var dto = new SetFrontOfficeUserPasswordCommandDTO().userId(USER_ID).password("pipolo");
        performPost("/user/front-office/reset-password", dto)
                .andExpect(status().isNoContent());
        Mockito.verify(keycloakService).setFrontOfficeUserPassword(dto);
    }
}
