package com.erhgo.controller;


import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TestUtils;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.openapi.dto.UserBehaviorDescriptionDTO;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.UserBehaviorDescriptionService;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.generation.HashtagsGenerationService;
import com.erhgo.services.generation.SoftSkillDescriptionGenerationService;
import com.erhgo.services.generation.dto.TitleAndDescription;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import lombok.SneakyThrows;
import org.apache.commons.lang3.BooleanUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.IntStream;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;


class ProfileCompetencesExportControllerTest extends AbstractIntegrationTest {

    private static final String USER_ID = "USER_42";
    @MockitoBean
    UserBehaviorDescriptionService userBehaviorDescriptionService;
    @MockitoBean
    HashtagsGenerationService hashtagsGenerationService;
    @MockitoBean
    SoftSkillDescriptionGenerationService softSkillDescriptionGenerationService;
    @Autowired
    UserProfileCompetencesExportService service;

    static final String CODE_ORGA = "S-042";

    @SneakyThrows
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @NullSource
    @WithMockKeycloakUser(roles = {Role.SOURCING, CODE_ORGA})
    @ResetDataAfter
    void exportEmptyProfile(Boolean isAnonymous) {

        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withCapacities(applicationContext.getBean(CapacityGenerator.class).createCapacity("CA5-44"))
                .buildAndPersist();
        var user = applicationContext.getBean(UserProfileMotherObject.class)
                .withEmail("<EMAIL>")
                .withFirstname(null)
                .withLastname(null)
                .withExperienceOnOccupation(occupation)
                .refreshCapacities()
                .buildAndPersist();
        var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withState(BooleanUtils.isTrue(isAnonymous) ? GlobalCandidatureState.NEW : GlobalCandidatureState.INTRODUCE_TO_CLIENT)
                .withUserProfile(user)
                .withRecruiterCode(CODE_ORGA)
                .withAnonymousCode("A-4242")
                .buildAndPersist();

        var userId = user.userId();
        Mockito.when(userBehaviorDescriptionService.getUserBehaviorDescriptionOrGenerate(userId)).thenReturn(new UserBehaviorDescriptionDTO().description("Un petit gars qui en veut."));
        Mockito.when(hashtagsGenerationService.getHashtagsOrGenerate(userId)).thenReturn(List.of());
        Mockito.when(softSkillDescriptionGenerationService.getSoftSkillsDescriptionsOrGenerate(userId)).thenReturn(List.of(new TitleAndDescription("Action ?", "Action !")));

        var url = "/sourcing/candidature/%d/profile-competences".formatted(candidature.getId());
        if (isAnonymous == null) {
            url += "?forceAnonymous=true";
        } else if (BooleanUtils.isTrue(isAnonymous)) {
            // To ensure when forceAnonymous=false AND candidature is anonymous, anonymized profile is downloaded
            url += "?forceAnonymous=false";
        }
        mvc.perform(get(realUrl(url)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(TestUtils.pdfMatchesContent("profileCompetencesIntegrationTest%s.pdf".formatted((isAnonymous == null || isAnonymous) ? "_anonymous" : "")));
    }

    @SneakyThrows
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @NullSource
    @WithMockKeycloakUser(roles = {Role.CANDIDATE}, id = USER_ID)
    @ResetDataAfter
    void exportFulfilledProfile(Boolean anywhere) {
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withCapacities(applicationContext.getBean(CapacityGenerator.class).createCapacity("CA5-44"))
                .buildAndPersist();
        var user = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID)
                .withFirstname("Pré")
                .withLastname("Nom")
                .withEmail("<EMAIL>")
                .withPhoneNumber("0505050505")
                .refreshCapacities()
                .withSalary(40000)
                .withLocation(Location.builder().city("Pay").postcode("59000").radiusInKm(BooleanUtils.isTrue(anywhere) ? 200 : 150).build())
                .withExperienceOnOccupation(occupation)
                .withAllTypeWorkingTimes(true)
                .withAllContractTypes(true)
                .withDrivingLicence(true)
                .buildAndPersist();

        var userId = user.userId();
        var list = new ArrayList<TitleAndDescription>();
        list.add(new TitleAndDescription("ACTION", "Action !"));
        list.add(new TitleAndDescription("MEASURE", "Orga"));
        list.add(new TitleAndDescription("IMAGINATION", "Imagine"));
        list.add(new TitleAndDescription("DECISION", "Décide"));

        Mockito.when(userBehaviorDescriptionService.getUserBehaviorDescriptionOrGenerate(userId)).thenReturn(new UserBehaviorDescriptionDTO().description("Un petit gars qui en veut."));
        Mockito.when(hashtagsGenerationService.getHashtagsOrGenerate(userId)).thenReturn(IntStream.range(1, 20).mapToObj("#HashTag%d"::formatted).toList());
        Mockito.when(softSkillDescriptionGenerationService.getSoftSkillsDescriptionsOrGenerate(userId)).thenReturn(list);
        var url = "/user/%s/profile-competences".formatted(userId);
        if (anywhere == null) {
            url += "?forceAnonymous=true";
        }
        mvc.perform(get(realUrl(url)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(TestUtils.pdfMatchesContent("profileCompetencesFulfilledIntegrationTest%s.pdf".formatted(anywhere == null ? "Anonymous" : anywhere ? "Anywhere" : "")));
    }

    @SneakyThrows
    @Test
    @WithMockKeycloakUser(roles = {Role.CANDIDATE}, id = USER_ID)
    @ResetDataAfter
    void exportBothPages() {
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withCapacities(applicationContext.getBean(CapacityGenerator.class).createCapacity("CA5-44"))
                .buildAndPersist();


        var user = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID)
                .withFirstname("Pré")
                .withLastname("Nom")
                .withEmail("<EMAIL>")
                .withPhoneNumber("0505050505")
                .withSalary(40000)
                .withLocation(Location.builder().city("Pay").postcode("59000").radiusInKm(50).build())
                .withExperienceOnOccupation(occupation)
                .withAllTypeWorkingTimes(true)
                .withAllContractTypes(true)
                .withDrivingLicence(true)
                .refreshCapacities()
                .buildAndPersist();

        var userId = user.userId();
        var list = new ArrayList<TitleAndDescription>();
        list.add(new TitleAndDescription("ACTION", "Action !"));
        list.add(new TitleAndDescription("MEASURE", "Orga"));
        list.add(new TitleAndDescription("IMAGINATION", "Imagine"));
        list.add(new TitleAndDescription("DECISION", "Décide"));

        Mockito.when(userBehaviorDescriptionService.getUserBehaviorDescriptionOrGenerate(userId)).thenReturn(new UserBehaviorDescriptionDTO().description("Un petit gars qui en veut."));
        Mockito.when(hashtagsGenerationService.getHashtagsOrGenerate(userId)).thenReturn(IntStream.range(1, 20).mapToObj("#HashTag%d"::formatted).toList());
        Mockito.when(softSkillDescriptionGenerationService.getSoftSkillsDescriptionsOrGenerate(userId)).thenReturn(list);

        var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(user)
                .withAnonymousCode("C-4242")
                .buildAndPersist();

        var provider = service.getProfileCompetenceForBatch(candidature.getId(), "Pré Nom", ProfileCompetencesViewObject.AnonymousMode.BOTH);
        TestUtils.assertThatPdfMatchesContent(provider.content(), "profileCompetencesBothPages.pdf");
    }

}
