package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.TestUtils;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.referential.Activity;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.generators.ErhgoOccupationGenerator;
import com.erhgo.generators.JobActivityLabelGenerator;
import com.erhgo.generators.TestFixtures;
import com.erhgo.openapi.dto.ActivityLabelDTO;
import com.erhgo.openapi.dto.MergeActivityCommandDTO;
import com.erhgo.openapi.dto.SaveActivityCommandDTO;
import com.erhgo.repositories.ActivityRepository;
import com.erhgo.repositories.JobActivityLabelRepository;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;

import java.util.Collections;
import java.util.UUID;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.erhgo.generators.TestFixtures.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasSize;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class JobActivityControllerTest extends AbstractIntegrationTestWithFixtures {

    @Autowired
    private JobActivityLabelRepository jobActivityLabelRepository;

    @Autowired
    private ActivityRepository activityRepository;

    @Autowired
    private ErhgoOccupationGenerator erhgoOccupationGenerator;

    @Autowired
    private JobActivityLabelGenerator jobActivityLabelGenerator;

    @Autowired
    private ErhgoOccupationRepository erhgoOccupationRepository;

    @Test
    @WithMockUser
    public void findByTitleWithUnknownQueryShouldRetrieveEmptyResult() throws Exception {
        mvc.perform(get("/api/odas/activityLabel/search?query=ilSAgitDUneActivitéInconnueDuSysteme!"))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("application/json;charset=UTF-8"))
                .andExpect(jsonPath("$[*]", hasSize(0)));
    }

    @Test
    @WithMockUser
    public void findByTitleWithEmptyQueryShouldRetrieveAllActivities() throws Exception {
        mvc.perform(get("/api/odas/activityLabel/search?query="))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("application/json;charset=UTF-8"))
                .andExpect(jsonPath("$[*]", hasSize(57)));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    public void findByTitleWithEmptyQueryAndPaginationShouldRetrieveFirst2Activities() throws Exception {
        mvc.perform(get("/api/odas/activityLabel/JOB/list?page=0&size=2&query=&direction=ASC&by=title"))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("application/json;charset=UTF-8"))
                .andExpect(jsonPath("$.content.[*]", hasSize(2)));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    public void findWithCapacitiesRecursivelyShouldRetrieveActivities() throws Exception {
        searchByIsRecursive(true, "", "activitySearchPerCapacityRecursive");
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    public void findWithCapacitiesNotRecursivelyShouldRetrieveActivities() throws Exception {
        searchByIsRecursive(false, "", "activitySearchPerCapacityNotRecursive");
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    public void findByTitleWithCapacitiesNotRecursivelyShouldRetrieveActivities() throws Exception {
        searchByIsRecursive(false, "jE", "activitySearchPerTitleAndCapacity");
    }

    private void searchByIsRecursive(boolean isRecursive, String query, String expected) throws Exception {
        mvc.perform(get("/api/odas/activityLabel/JOB/list?isCapacityRecursive=" + isRecursive + "&capacityIds=" + CA1_12.getId() + "," + CA2_02.getId() + "&page=0&size=10&filter=" + query + "&direction=ASC&by=title"))
                .andExpect(status().isOk())
                .andExpect(TestUtils.jsonMatchesContentWithOrderedArray(expected));
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void searchShouldRetrieved_level_3_induced_capacities() throws Exception {
        mvc.perform(get("/api/odas/activityLabel/JOB/list?isCapacityRecursive=" + true + "&capacityIds=" + CA1_06.getId() + "&page=0&size=10&filter=&direction=ASC&by=title"))
                .andExpect(status().isOk())
                .andExpect(TestUtils.jsonMatchesContentWithOrderedArray("activitySearchInducedOfInducedCapacities"));
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void searchShouldNotRetrieve_activity_without_both_activity() throws Exception {
        mvc.perform(get("/api/odas/activityLabel/JOB/list?isCapacityRecursive=" + true + "&capacityIds=" + CA1_06.getId() + "," + CA1_27.getId() + "," + CA1_09.getId() + "&page=0&size=10&filter=&direction=ASC&by=title"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content.[*]", hasSize(0)));
    }

    @Test
    @WithMockUser
    public void findByTitleWithUnknownQueryShouldIgnoreCase() throws Exception {
        mvc.perform(get("/api/odas/activityLabel/search?query=GéReR"))
                .andExpect(status().isOk())
                .andExpect(content().json(TestUtils.toString(getClass().getResourceAsStream("/expected/activitySearch.json")), false));
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void mergeNotUsedActivitiesShouldDeleteActivityAndUpdateLabelsReference() throws Exception {
        UUID expectedActivityId = UUID.randomUUID();

        SaveActivityCommandDTO createActivityCommand = getSaveActivityCommandDTO(expectedActivityId, Stream.of(unusedJobActivityLabel_1, unusedJobActivityLabel_2));

        var sourceActivitiesUUID = Lists.newArrayList(unusedJobActivityLabel_1.getActivity().getUuid(), unusedJobActivityLabel_2.getActivity().getUuid());

        MergeActivityCommandDTO mergeActivityCommand = new MergeActivityCommandDTO()
                .sourceActivities(sourceActivitiesUUID)
                .mergedActivity(createActivityCommand);

        mvc.perform(post("/api/odas/activity/merge").content(objectMapper.writeValueAsString(mergeActivityCommand))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        assertThat(activityRepository.findAllById(sourceActivitiesUUID)).isNullOrEmpty();
        assertThat(jobActivityLabelRepository.findAllById(Lists.newArrayList(unusedJobActivityLabel_1.getUuid(), unusedJobActivityLabel_2.getUuid())).stream().map(JobActivityLabel::getActivity).map(Activity::getUuid)).allMatch(Predicate.isEqual(expectedActivityId));

    }

    private SaveActivityCommandDTO getSaveActivityCommandDTO(UUID expectedActivityId, Stream<JobActivityLabel> activitiesTemplateStream) {
        return new SaveActivityCommandDTO().description("merged description")
                .inducedCapacities(Lists.newArrayList(CA1_12.getCode()))
                .id(expectedActivityId)
                .labels(activitiesTemplateStream.map(a -> new ActivityLabelDTO().id(a.getUuid()).position(a.getPosition()).title(a.getTitle())).toList());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void mergeActivityLabel_should_remove_label_and_update_label_used_in_erhgo_occupation() throws Exception {
        UUID expectedActivityId = UUID.randomUUID();

        var deletedActivityLabel1 = jobActivityLabelGenerator.createSecondActivityLabelSharingSameActivity(ACT_15);
        var deletedActivityLabel2 = jobActivityLabelGenerator.createSecondActivityLabelSharingSameActivity(unusedJobActivityLabel_1);
        var manualDeletedActivityLabel = jobActivityLabelGenerator.createSecondActivityLabelSharingSameActivity(unusedJobActivityLabel_1);
        var unmodifiedActivity = jobActivityLabelUsedOnlyForEscoSkillClassification;

        var sourceActivities = Lists.newArrayList(ACT_15.getActivity().getUuid(), unusedJobActivityLabel_1.getActivity().getUuid());
        var targetActivitiesLabels = Stream.of(deletedActivityLabel1, deletedActivityLabel2, manualDeletedActivityLabel, ACT_15, unusedJobActivityLabel_1).collect(Collectors.toSet());
        MergeActivityCommandDTO mergeActivityCommand = new MergeActivityCommandDTO()
                .sourceActivities(sourceActivities)
                .mergedActivity(getSaveActivityCommandDTO(expectedActivityId, targetActivitiesLabels.stream()));

        var occupation = erhgoOccupationGenerator.createErhgoOccupationWithActivities("Erhgo with activities", Collections.emptyList(), Lists.newArrayList(unmodifiedActivity,
                deletedActivityLabel1,
                deletedActivityLabel2,
                manualDeletedActivityLabel
        ));

        mvc.perform(post("/api/odas/activity/merge").content(objectMapper.writeValueAsString(mergeActivityCommand))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        assertThat(activityRepository.findAllById(sourceActivities)).isNullOrEmpty();
        assertThat(jobActivityLabelRepository.findAllById(targetActivitiesLabels.stream().map(JobActivityLabel::getActivity).map(Activity::getUuid).collect(Collectors.toSet()))).allMatch(Predicate.isEqual(expectedActivityId));
        txHelper.doInTransaction(() -> {
            var nextOccupation = erhgoOccupationRepository.findById(occupation.getId()).orElseThrow();
            assertThat(nextOccupation.getOccupationActivities()).hasSize(4);
            assertThat(nextOccupation.getOccupationActivities()).anyMatch(oa -> oa.getActivity().getUuid().equals(unmodifiedActivity.getUuid()));
            var newOccupationActivities = nextOccupation.getOccupationActivities().stream().filter(a -> a.getActivity().getActivity().getUuid().equals(expectedActivityId));
            assertThat(newOccupationActivities).hasSize(3);
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void mergeActivitiesUsedInUserProfileShouldDeleteActivityAndUpdateReferences() throws Exception {
        UUID expectedActivityId = UUID.randomUUID();

        var labels = Lists.newArrayList(TestFixtures.jobActivityLabelUsedInCandidatureExperienceAndJobMission, jobActivityLabelUsedOnlyInJobMission, ACT_01, secondActivityLabelSharingJobActivityUsedInUserExperience);
        SaveActivityCommandDTO createActivityCommand = getSaveActivityCommandDTO(expectedActivityId, labels.stream());

        var sourceActivitiesUUID = labels.stream().map(JobActivityLabel::getActivity).map(Activity::getUuid).distinct().toList();

        MergeActivityCommandDTO mergeActivityCommand = new MergeActivityCommandDTO()
                .sourceActivities(sourceActivitiesUUID)
                .mergedActivity(createActivityCommand);

        mvc.perform(post("/api/odas/activity/merge").content(objectMapper.writeValueAsString(mergeActivityCommand))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        assertThat(activityRepository.findAllById(sourceActivitiesUUID)).isNullOrEmpty();
        assertThat(jobActivityLabelRepository.findAllById(labels.stream().map(JobActivityLabel::getUuid).toList()).stream().map(JobActivityLabel::getActivity).map(Activity::getUuid)).allMatch(Predicate.isEqual(expectedActivityId));
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void isActivityLabelDeletableShouldReturnFalseForSingleActivityLabel() throws Exception {

        mvc.perform(get("/api/odas/activityLabel/" + unusedJobActivityLabel_1.getUuid() + "/isDeletable"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").value(false));

    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void isActivityLabelDeletableShouldReturnFalseForActivityLabelUsedInJobMission() throws Exception {

        addLabelToActivity(jobActivityLabelUsedOnlyInJobMission);
        mvc.perform(get("/api/odas/activityLabel/" + jobActivityLabelUsedOnlyInJobMission.getUuid() + "/isDeletable"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").value(false));

    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void isActivityLabelDeletableShouldReturnFalseForActivityLabelUsedInEsco() throws Exception {

        addLabelToActivity(jobActivityLabelUsedOnlyForEscoSkillClassification);
        mvc.perform(get("/api/odas/activityLabel/" + jobActivityLabelUsedOnlyForEscoSkillClassification.getUuid() + "/isDeletable"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").value(false));

    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void isActivityLabelDeletableShouldReturnFalseForActivityLabelUsedInErhgoOccupation() throws Exception {

        addLabelToActivity(unusedJobActivityLabel_1);
        erhgoOccupationGenerator.createErhgoOccupationWithActivities("Erhgo with activities", Collections.emptyList(), Lists.newArrayList(jobActivityLabelUsedOnlyForEscoSkillClassification, unusedJobActivityLabel_1));
        mvc.perform(get("/api/odas/activityLabel/" + unusedJobActivityLabel_1.getUuid() + "/isDeletable"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").value(false));

    }

    private void addLabelToActivity(JobActivityLabel activityLabel) {
        jobActivityLabelGenerator.createSecondActivityLabelSharingSameActivity(activityLabel);
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void isActivityLabelDeletableShouldReturnTrueForDeletableActivityLabel() throws Exception {

        mvc.perform(get("/api/odas/activityLabel/" + secondActivityLabelSharingJobActivityUsedInUserExperience.getUuid() + "/isDeletable"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").value(true));

    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void isActivityLabelDeletableShouldReturnFalseForActivityLabelUsedInUserExperience() throws Exception {

        mvc.perform(get("/api/odas/activityLabel/" + jobActivityLabelUsedInCandidatureExperienceAndJobMission.getUuid() + "/isDeletable"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").value(false));

    }

}
