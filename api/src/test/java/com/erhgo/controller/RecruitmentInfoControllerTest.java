package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.config.ApiConstants;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.enums.JobEvaluationState;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.enums.TypeWorkingTime;
import com.erhgo.domain.job.JobMotherObject;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.recruitment.RecruitmentProfile;
import com.erhgo.generators.TestFixtures;
import com.erhgo.openapi.dto.RecruitmentDTO;
import com.erhgo.openapi.dto.RecruitmentStateDTO;
import com.erhgo.openapi.dto.SaveRecruitmentCommandDTO;
import com.erhgo.openapi.dto.TypeContractDTO;
import com.erhgo.repositories.RecruitmentProfileRepository;
import com.erhgo.repositories.classifications.ErhgoClassificationRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.RecruitmentService;
import com.erhgo.services.SecurityService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

import static com.erhgo.generators.TestFixtures.J_01;
import static com.erhgo.generators.TestFixtures.M_03_BOTANIC;
import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

class RecruitmentInfoControllerTest extends AbstractIntegrationTestWithFixtures {

    @Autowired
    private RecruitmentProfileRepository recruitmentProfileRepository;

    @Autowired
    private RecruitmentService service;

    private RecruitmentDTO referenceRecruitment;
    private RecruitmentProfile referenceRecruitmentProfile;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private SecurityService securityService;

    @BeforeEach
    void setUp() {

        securityService.doAsAdmin(() -> {
            var profile = RecruitmentProfile.builder()
                    .job(TestFixtures.J_01)
                    .qualified(true)
                    .uuid(UUID.randomUUID())
                    .build();
            referenceRecruitmentProfile = recruitmentProfileRepository.save(profile);
            final var recruitment = createRecruitment();
            referenceRecruitment = service.create(recruitment);
        });
    }

    private SaveRecruitmentCommandDTO createRecruitment() {
        final var recruitment = new SaveRecruitmentCommandDTO();
        recruitment.setTitle("Job");
        recruitment.setDescription("");
        recruitment.setTypeContract(TypeContractDTO.CDI);
        recruitment.setRecruitmentProfileUuid(referenceRecruitmentProfile.getUuid());
        recruitment.setState(RecruitmentStateDTO.PUBLISHED);

        return recruitment;
    }

    @Test
    void should_return_public_recruitment_info_from_existing_recruitment() throws Exception {
        // @formatter:off
        mvc.perform(get("/api/odas/public/recruitment/" + referenceRecruitment.getCode()).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.jobTitle", is(J_01.getTitle())))
                .andExpect(jsonPath("$.organizationName", is(M_03_BOTANIC.getTitle())))
                .andExpect(jsonPath("$.description", is(referenceRecruitment.getDescription())));
        // @formatter:on
    }

    @Test
    @ResetDataAfter
    void serializePubliclationEndDate() {
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withPublicationDate(new Date(54_000_000_000L))
                .withPublicationEndDate(OffsetDateTime.of(2042, 5, 5, 5, 5, 5, 5, ZoneOffset.UTC))
                .withJobTitle("Recruitment public")
                .buildAndPersist();
        performGetAndExpect("/public/recruitment/%s".formatted(recruitment.getCode()), "publicRecruitment", false);
    }

    @Test
    @ResetDataAfter
    void get_public_recruitment_info_with_working_time() throws Exception {

        var job = applicationContext.getBean(JobMotherObject.class)
                .withState(JobEvaluationState.PUBLISHED)
                .withWorkingTimes(TypeWorkingTime.FULL_TIME)
                .buildAndPersist();
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class).withState(RecruitmentState.PUBLISHED).withJob(job).buildAndPersist();

        // @formatter:off
        mvc.perform(get("/api/odas/public/recruitment/" + recruitment.getCode()).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.workingTimes[*]", contains("FULL_TIME")));
        // @formatter:on
    }

    @Test
    @ResetDataAfter
    void get_public_recruitment_info_with_classifications() throws Exception {

        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withErhgoClassification(new HashSet<>(applicationContext.getBean(ErhgoClassificationRepository.class).findErhgoClassificationByCodeIn(Set.of("SO-05", "SO-07"))))
                .buildAndPersist();

        performGetAndExpect("/public/recruitment/%s".formatted(recruitment.getCode()), "recruitment_classifications", false);
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void should_not_create_recruitment_for_not_qualified_profile() throws Exception {
        referenceRecruitmentProfile.setQualified(false);
        recruitmentProfileRepository.save(referenceRecruitmentProfile);
        var createRecruitmentCommandDTO = new SaveRecruitmentCommandDTO();
        createRecruitmentCommandDTO.setRecruitmentProfileUuid(referenceRecruitmentProfile.getUuid());
        createRecruitmentCommandDTO.setTypeContract(TypeContractDTO.CDI);

        // @formatter:off
        mvc.perform(post(ApiConstants.API_ODAS_RECRUITMENT + "/create")
                        .content(objectMapper.writeValueAsString(createRecruitmentCommandDTO))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(content().string(containsString("NOT_QUALIFIED_PROFILE")));
        // @formatter:on
    }

}
