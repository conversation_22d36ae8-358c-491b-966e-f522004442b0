package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.candidature.job.SpontaneousCandidature;
import com.erhgo.domain.enums.CandidatureSynchronizationState;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.externaloffer.ExternalOfferMotherObject;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.job.SpontaneousCandidatureMotherObject;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.generators.JobGenerator;
import com.erhgo.generators.RecruiterMotherObject;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.utils.DateTimeUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

class RecruitmentStatisticsControllerTest extends AbstractIntegrationTest {

    @MockitoBean
    KeycloakMockService keycloakMockService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private JobGenerator jobGenerator;

    @Autowired
    private RecruitmentCandidatureRepository recruitmentCandidatureRepository;
    @Autowired
    private SpontaneousCandidatureRepository SpontaneousCandidatureRepository;

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void get_recruitments_stats() {
        var now = OffsetDateTime.now(DateTimeUtils.zoneOffset());

        txHelper.doInTransaction(() -> {
            var recruiterA = createRecruiter("S-A", "Organisation A", "Projet 1", "Projet 2");
            var recruiterB = createRecruiter("S-B", "Organisation B");
            var recruiterC = createRecruiter("S-C", "Organisation C");

            var jobA = createJob("Job-A", recruiterA);
            var jobB = createJob("Job-B", recruiterB);
            var jobC = createJob("Job-C", recruiterC);

            var recruitmentA1 = createRecruitment(recruiterA, jobA, RecruitmentState.PUBLISHED, now.minusDays(1));
            var recruitmentA2 = createRecruitment(recruiterA, jobA, RecruitmentState.PUBLISHED, now.minusDays(2));
            var recruitmentA3 = createRecruitment(recruiterA, jobA, RecruitmentState.PUBLISHED, now.minusDays(3));
            var recruitmentA4 = createRecruitment(recruiterA, jobA, RecruitmentState.CLOSED, now.minusDays(10));

            createExternalOffer(recruitmentA3, "ATS1");
            createExternalOffer(recruitmentA2, "ATS1");
            createExternalOffer(recruitmentA1, "ATS2");

            var recruitmentB1 = createRecruitment(recruiterB, jobB, RecruitmentState.PUBLISHED, now.minusDays(4));
            var recruitmentC1 = createRecruitment(recruiterC, jobC, RecruitmentState.PUBLISHED, now.minusDays(5));
            var recruitmentC2 = createRecruitment(recruiterC, jobC, RecruitmentState.CLOSED, now.minusDays(15));

            var userA1 = createUser("<EMAIL>", recruiterA.getCode());
            var userA2 = createUser("<EMAIL>", recruiterA.getCode());
            var userA3 = createUser("<EMAIL>", recruiterA.getCode());
            var userA4 = createUser("<EMAIL>", recruiterA.getCode());
            var userB1 = createUser("<EMAIL>", recruiterB.getCode());
            var userB2 = createUser("<EMAIL>", recruiterB.getCode());
            var userC1 = createUser("<EMAIL>", recruiterC.getCode());
            var userC2 = createUser("<EMAIL>", recruiterC.getCode());

            var candidatureA1 = createCandidature(userA1, recruitmentA1, GlobalCandidatureState.RECRUITMENT_VALIDATED);
            var candidatureA2 = createCandidature(userA2, recruitmentA1, GlobalCandidatureState.INTRODUCE_TO_CLIENT);
            createCandidature(userA1, recruitmentA2, GlobalCandidatureState.REFUSED_ON_CALL);
            var candidatureA4 = createCandidature(userA2, recruitmentA4, GlobalCandidatureState.ON_RECRUITMENT_CLIENT);
            var candidatureA5 = createCandidature(userA1, recruitmentA4, GlobalCandidatureState.ON_RECRUITMENT_CLIENT);
            var candidatureA6 = createCandidature(userA4, recruitmentA1, GlobalCandidatureState.REFUSED_ON_CALL);

            setRecruitmentCandidatureArchive(candidatureA2);
            setRecruitmentCandidatureArchive(candidatureA6);
            setRecruitmentCandidatureArchive(candidatureA5);

            setSynchronizationState(candidatureA1);
            setSynchronizationState(candidatureA4);

            var candidatureB1 = createCandidature(userB1, recruitmentB1, GlobalCandidatureState.RECRUITMENT_VALIDATED);
            setSynchronizationState(candidatureB1);

            createCandidature(userC1, recruitmentC1, GlobalCandidatureState.INTRODUCE_TO_CLIENT);
            var candidatureC2 = createCandidature(userC2, recruitmentC2, GlobalCandidatureState.RECRUITMENT_VALIDATED);
            setSynchronizationState(candidatureC2);

            createSpontaneousCandidature(userA1, recruiterA);
            var spontaneousCandidature2 = createSpontaneousCandidature(userA2, recruiterA);
            var spontaneousCandidature3 = createSpontaneousCandidature(userA3, recruiterA);

            setSpontaneousCandidatureArchive(spontaneousCandidature2);
            setSpontaneousCandidatureArchive(spontaneousCandidature3);

            createSpontaneousCandidature(userC1, recruiterC);
        });

        performGetAndExpect("/statistics/recruitments", "recruitmentStats", false);
    }

    private Recruiter createRecruiter(String code, String name, String... projectsTitles) {
        return txHelper.doInTransaction(() -> {
            var projects = new ArrayList<Recruiter>();
            if (projectsTitles.length > 0) {
                Stream.of(projectsTitles).map(p -> applicationContext.getBean(RecruiterMotherObject.class).withTitle(p).withOrganizationType(AbstractOrganization.OrganizationType.PROJECT).buildAndPersist()).forEach(projects::add);
                Mockito.when(keycloakMockService.searchBOGroupsWithRoles("S-")).thenReturn(Map.of(code, projects.stream().map(Recruiter::getCode).collect(Collectors.toSet())));
            }
            return applicationContext.getBean(RecruiterMotherObject.class).withTitle(name).withOrganizationType(AbstractOrganization.OrganizationType.SOURCING).withCode(code).buildAndPersist();

        });
    }

    private Job createJob(String title, Recruiter recruiter) {
        return jobGenerator.createJob(title, recruiter, title + " description");
    }

    private Recruitment createRecruitment(Recruiter recruiter, Job job, RecruitmentState state, OffsetDateTime publicationDate) {
        return applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(state)
                .withPublicationDate(Date.from(publicationDate.toInstant()))
                .withPublicationEndDate(publicationDate.plusDays(30))
                .withRecruiter(recruiter)
                .withJob(job)
                .buildAndPersist();
    }

    private UserProfile createUser(String email, String channelCode) {
        return applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(UUID.randomUUID().toString())
                .withFirstname("User")
                .withLastname("Test")
                .withEmail(email)
                .withChannels(channelCode)
                .buildAndPersist();
    }

    private RecruitmentCandidature createCandidature(UserProfile user, Recruitment recruitment, GlobalCandidatureState state) {
        return applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(user)
                .withRecruitment(recruitment)
                .withState(state)
                .buildAndPersist();
    }

    private void setSynchronizationState(RecruitmentCandidature candidature) {
        txHelper.doInTransaction(() -> {
            candidature.setSynchronizationState(CandidatureSynchronizationState.DONE);
            recruitmentCandidatureRepository.save(candidature);
        });
    }


    private void setRecruitmentCandidatureArchive(RecruitmentCandidature candidature) {
        txHelper.doInTransaction(() -> {
            candidature.setArchived(true);
            recruitmentCandidatureRepository.save(candidature);
        });
    }


    private void setSpontaneousCandidatureArchive(SpontaneousCandidature candidature) {
        txHelper.doInTransaction(() -> {
            candidature.setArchived(true);
            SpontaneousCandidatureRepository.save(candidature);
        });
    }

    private void createExternalOffer(Recruitment recruitment, String atsCode) {
        applicationContext.getBean(ExternalOfferMotherObject.class)
                .withRecruitment(recruitment)
                .withATSCode(atsCode)
                .withRecruiterCode(recruitment.getRecruiterCode())
                .buildAndPersist();
    }

    private SpontaneousCandidature createSpontaneousCandidature(UserProfile user, Recruiter recruiter) {
        return applicationContext.getBean(SpontaneousCandidatureMotherObject.class)
                .withUserProfile(user)
                .withRecruiter(recruiter)
                .buildAndPersist();
    }


}
