package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TestUtils;
import com.erhgo.config.ApiConstants;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.enums.CriteriaQuestionType;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.CriteriaMotherObject;
import com.erhgo.generators.JobGenerator;
import com.erhgo.generators.UserProfileGenerator;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class JobMatchingUsersWithCriteriaControllerTest extends AbstractIntegrationTest {

    @Autowired
    private JobGenerator jobGenerator;

    @Autowired
    private CapacityGenerator capacityGenerator;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Autowired
    private ApplicationContext applicationContext;


    private MockHttpServletRequestBuilder getMatchingUsersRequestBuilder(UUID jobId) {
        return get(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + jobId + "/matching-users")
                .param("page", "0")
                .param("size", "600")
                .contentType(MediaType.APPLICATION_JSON);
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    @DisplayName("" +
            "Given four users having: \n" +
            "   - U1: one capacity C1 and matching criteria value of type multiple CV-M-1 \n" +
            "   - U2: one capacity C1 and matching criteria value of type multiple CV-M-0 and CV-M-1 \n" +
            "   - U3: two capacities C1 & C2 and matching criteria value of type multiple CV-M-2 \n" +
            "   - U4: one capacity C1 and without any criteria value \n" +
            " And a job with same capacities and criteria CV-M-1 & CV-M-0 \n" +
            " When I find matching users \n" +
            " Then users U2, U1, U3, U4 are retrieved with well formed missing/unknown criteria values \n"
    )
    void findMatchingUsersForJob_sort_by_multiple_criteria_enabled_then_capacities() throws Exception {
        var matchingCapacity1 = capacityGenerator.createCapacity("CA-1");
        var matchingCapacity2 = capacityGenerator.createCapacity("CA-2");
        var uuidRef = "3d903206-7ae9-48d6-862b-001e9201599";
        var expectedUuid1 = UUID.fromString(uuidRef + "1");
        var expectedUuid2 = UUID.fromString(uuidRef + "2");
        var expectedUuid3 = UUID.fromString(uuidRef + "3");
        var expectedUuid4 = UUID.fromString(uuidRef + "4");
        var jobIdHolder = new AtomicReference<UUID>();
        txHelper.doInTransaction(() -> {

            var multipleCriteria = applicationContext.getBean(CriteriaMotherObject.class).withQuestionType(CriteriaQuestionType.MULTIPLE).withValueCodes("CV-M-0", "CV-M-1", "CV-M-2").buildAndPersist();

            userProfileGenerator.createUserProfileWithCapacitiesAndCriteriaValues(expectedUuid1, 1, List.of(multipleCriteria.getCriteriaValues().get(1)), Collections.emptyList(), matchingCapacity1);
            userProfileGenerator.createUserProfileWithCapacitiesAndCriteriaValues(expectedUuid2, 1, List.of(multipleCriteria.getCriteriaValues().get(1), multipleCriteria.getCriteriaValues().get(0)), Collections.emptyList(), matchingCapacity1);
            userProfileGenerator.createUserProfileWithCapacitiesAndCriteriaValues(expectedUuid3, 1, List.of(multipleCriteria.getCriteriaValues().get(2)), Collections.emptyList(), matchingCapacity1, matchingCapacity2);
            userProfileGenerator.createUserProfileWithCapacities(expectedUuid4, 1, matchingCapacity1);

            var job = jobGenerator.createJobWithLevelCriteriaAndCapacities("J-1", List.of(multipleCriteria.getCriteriaValues().get(1), multipleCriteria.getCriteriaValues().get(0)), MasteryLevel.MIN_LEVEL, matchingCapacity1, matchingCapacity2);
            jobIdHolder.set(job.getId());
        });
        mvc.perform(getMatchingUsersRequestBuilder(jobIdHolder.get())
                        .param("capacityThreshold", "0.1")
                        .param("criteriaCodes", "CV-M-0", "CV-M-1", "CV-M-2")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(TestUtils.jsonMatchesContent("jobMatchingUsersWithCriteria1"));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    @DisplayName("" +
            "Given three users having: \n" +
            "   - U1: criteria value of type multiple CV-M-0 explicitly unselected\n" +
            "   - U2: criteria value of type multiple CV-M-0 selected \n" +
            "   - U3: without any criteria value \n" +
            " And a job with same capacities and criteria CV-M-0 \n" +
            " When I find matching users \n" +
            " Then users U2, U3, U1 are retrieved with well formed missing/unknown criteria values \n"
    )
    void findMatchingUsersForJob_sort_by_multiple_criteria_enabled_then_unknown_then_disabled() throws Exception {
        var matchingCapacity1 = capacityGenerator.createCapacity("CA-1");
        var uuidRef = "3d903206-7ae9-48d6-862b-001e9201599";
        var expectedUuid1 = UUID.fromString(uuidRef + "1");
        var expectedUuid2 = UUID.fromString(uuidRef + "2");
        var expectedUuid3 = UUID.fromString(uuidRef + "3");
        var jobIdHolder = new AtomicReference<UUID>();
        txHelper.doInTransaction(() -> {

            var multipleCriteria = applicationContext.getBean(CriteriaMotherObject.class).withQuestionType(CriteriaQuestionType.MULTIPLE).withValueCodes("CV-M-0", "CV-M-1", "CV-M-2").buildAndPersist();

            userProfileGenerator.createUserProfileWithCapacitiesAndCriteriaValues(expectedUuid1, 1, Collections.emptyList(), List.of(multipleCriteria.getCriteriaValues().get(0)), matchingCapacity1);
            userProfileGenerator.createUserProfileWithCapacitiesAndCriteriaValues(expectedUuid2, 1, List.of(multipleCriteria.getCriteriaValues().get(0)), Collections.emptyList(), matchingCapacity1);
            userProfileGenerator.createUserProfileWithCapacities(expectedUuid3, 1, matchingCapacity1);

            var job = jobGenerator.createJobWithLevelCriteriaAndCapacities("J-1", List.of(multipleCriteria.getCriteriaValues().get(0)), MasteryLevel.MIN_LEVEL, matchingCapacity1);
            jobIdHolder.set(job.getId());
        });

        mvc.perform(getMatchingUsersRequestBuilder(jobIdHolder.get())
                        .param("capacityThreshold", "0.1")
                        .param("criteriaCodes", "CV-M-0", "CV-M-1", "CV-M-2")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(TestUtils.jsonMatchesContent("jobMatchingUsersWithCriteria2"));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    @DisplayName("" +
            "Given four users having: \n" +
            "   - U1: criteria value of type threshold CV-T-0 and two matching capacities \n" +
            "   - U2: criteria value of type threshold CV-T-1 and two matching capacities \n" +
            "   - U3: criteria value of type threshold CV-T-2 and one matching capacity \n" +
            "   - U4: no criteria value and two matching capacities \n" +
            " And a job with same capacities and criteria CV-T-1 \n" +
            " When I find matching users \n" +
            " Then users U2, U3, U4, U1 are retrieved with well formed missing/unknown criteria values \n"
    )
    void findMatchingUsersForJob_sort_by_criteria_threshold() throws Exception {
        var matchingCapacity1 = capacityGenerator.createCapacity("CA-1");
        var matchingCapacity2 = capacityGenerator.createCapacity("CA-2");
        var uuidRef = "3d903206-7ae9-48d6-862b-001e9201599";
        var expectedUuid1 = UUID.fromString(uuidRef + "1");
        var expectedUuid2 = UUID.fromString(uuidRef + "2");
        var expectedUuid3 = UUID.fromString(uuidRef + "3");
        var expectedUuid4 = UUID.fromString(uuidRef + "4");
        var jobIdHolder = new AtomicReference<UUID>();
        txHelper.doInTransaction(() -> {

            var thresholdCriteria = applicationContext.getBean(CriteriaMotherObject.class).withQuestionType(CriteriaQuestionType.THRESHOLD).withValueCodes("CV-T-0", "CV-T-1", "CV-T-2").buildAndPersist();

            userProfileGenerator.createUserProfileWithCapacitiesAndCriteriaValues(expectedUuid1, 1, List.of(thresholdCriteria.getCriteriaValues().get(0)), Collections.emptyList(), matchingCapacity1, matchingCapacity2);
            userProfileGenerator.createUserProfileWithCapacitiesAndCriteriaValues(expectedUuid2, 1, List.of(thresholdCriteria.getCriteriaValues().get(1)), Collections.emptyList(), matchingCapacity1, matchingCapacity2);
            userProfileGenerator.createUserProfileWithCapacitiesAndCriteriaValues(expectedUuid3, 1, List.of(thresholdCriteria.getCriteriaValues().get(2)), Collections.emptyList(), matchingCapacity1);
            userProfileGenerator.createUserProfileWithCapacities(expectedUuid4, 1, matchingCapacity1, matchingCapacity2);

            var job = jobGenerator.createJobWithLevelCriteriaAndCapacities("J-1", List.of(thresholdCriteria.getCriteriaValues().get(1)), MasteryLevel.MIN_LEVEL, matchingCapacity1, matchingCapacity2);
            jobIdHolder.set(job.getId());
        });

        mvc.perform(getMatchingUsersRequestBuilder(jobIdHolder.get())
                        .param("capacityThreshold", "0.1")
                        .param("criteriaCodes", "CV-T-0", "CV-T-1", "CV-T-2")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(TestUtils.jsonMatchesContent("jobMatchingUsersWithCriteria3"));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    @DisplayName("" +
            "Given three users having: \n" +
            "   - U1: criteria values of type multiple CV-M-0 and unselected CV-M-1, CV-M-2  and two matching capacities \n" +
            "   - U2: criteria value of type multiple CV-M-1 and one matching capacities \n" +
            "   - U3: no criteria value and two matching capacities \n" +
            " And a job with same capacities and criteria CV-M-0,  CV-M-1,  CV-M-2  \n" +
            " When I find matching users \n" +
            " Then users U2, U1, U3 are retrieved with well formed missing/unknown criteria values \n"
    )
    void findMatchingUsersForJob_sort_by_matching_criteria_then_missing_criteria() throws Exception {
        var matchingCapacity1 = capacityGenerator.createCapacity("CA-1");
        var matchingCapacity2 = capacityGenerator.createCapacity("CA-2");
        var uuidRef = "3d903206-7ae9-48d6-862b-001e9201599";
        var expectedUuid1 = UUID.fromString(uuidRef + "1");
        var expectedUuid2 = UUID.fromString(uuidRef + "2");
        var expectedUuid3 = UUID.fromString(uuidRef + "3");
        var jobIdHolder = new AtomicReference<UUID>();
        txHelper.doInTransaction(() -> {

            var multipleCriteria = applicationContext.getBean(CriteriaMotherObject.class).withQuestionType(CriteriaQuestionType.MULTIPLE).withValueCodes("CV-M-0", "CV-M-1", "CV-M-2").buildAndPersist();

            userProfileGenerator.createUserProfileWithCapacitiesAndCriteriaValues(expectedUuid1, 1, List.of(multipleCriteria.getCriteriaValues().get(0)), List.of(multipleCriteria.getCriteriaValues().get(1), multipleCriteria.getCriteriaValues().get(2)), matchingCapacity1, matchingCapacity2);
            userProfileGenerator.createUserProfileWithCapacitiesAndCriteriaValues(expectedUuid2, 1, List.of(multipleCriteria.getCriteriaValues().get(1)), Collections.emptyList(), matchingCapacity1);
            userProfileGenerator.createUserProfileWithCapacities(expectedUuid3, 1, matchingCapacity1, matchingCapacity2);

            var job = jobGenerator.createJobWithLevelCriteriaAndCapacities("J-1", new ArrayList<>(multipleCriteria.getCriteriaValues()), MasteryLevel.MIN_LEVEL, matchingCapacity1, matchingCapacity2);
            jobIdHolder.set(job.getId());
        });

        mvc.perform(getMatchingUsersRequestBuilder(jobIdHolder.get())
                        .param("capacityThreshold", "0.1")
                        .param("criteriaCodes", "CV-M-1", "CV-M-2")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(TestUtils.jsonMatchesContent("jobMatchingUsersWithCriteria4"));
    }
}
