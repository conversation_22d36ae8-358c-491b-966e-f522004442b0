package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.enums.CandidatureState;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.userprofile.UserNotificationMotherObject;
import com.erhgo.repositories.NotificationRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.Map;

class CandidaturePublishEmailSendingIntegrationTest extends AbstractIntegrationTest {
    private static final String USER_ID = "6cf3b6ca-8157-4bdf-b186-2e71644c032e";
    @Autowired
    ApplicationContext applicationContext;

    @Test
    @ResetDataAfter
    @SneakyThrows
    @WithMockKeycloakUser(roles = Role.CANDIDATE, id = USER_ID)
    void publish_candidature_disable_notif_flag() {
        var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withEmail("a@a")
                .withUserId(USER_ID)
                .withCandidatureState(CandidatureState.STARTED)
                .buildAndPersist();
        var notif = applicationContext.getBean(UserNotificationMotherObject.class)
                .withRecruitment(candidature.getRecruitment())
                .withRequiresMailSending(true)
                .withUserProfile(candidature.getUserProfile())
                .buildAndPersist();
        performPost("/candidature/publish", Map.of("candidatureId", candidature.getId()))
                .andExpect(MockMvcResultMatchers.status().isNoContent());
        Assertions.assertThat(applicationContext.getBean(NotificationRepository.class)
                        .findById(notif.getId())
                        .orElseThrow()
                        .isRequiresMailSending())
                .isFalse();
    }

}
