package com.erhgo.generators;

import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.enums.ContractType;
import com.erhgo.domain.enums.JobEvaluationState;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.job.ContextsForCategory;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.recruitment.RecruitmentProfile;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.Category;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.openapi.dto.AcquisitionModalityDTO;
import com.erhgo.repositories.*;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.UUID;

import static java.util.Collections.singletonList;

@Service
@Deprecated
public class RecruitmentGenerator {

    @Autowired
    private RecruitmentRepository recruitmentRepository;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private RecruitmentProfileRepository recruitmentProfileRepository;

    @Autowired
    private JobMissionRepository jobMissionRepository;

    @Autowired
    private JobActivityLabelRepository jobActivityLabelRepository;

    @Autowired
    private CategoryRepository categoryRepository;

    @Autowired
    private JobGenerator jobGenerator;

    @Autowired
    private OrganizationGenerator organizationGenerator;


    public static Recruitment buildRecruitment(RecruitmentProfile profile) {
        return Recruitment
                .builder()
                .managerUserId("42")
                .recruitmentProfile(profile)
                .build();
    }

    @Transactional
    public Recruitment createRecruitmentWithRequiredCapacity(String jobCode, Recruiter recruiter, Capacity capacity) {
        return createRecruitmentWithRequiredCapacityAndMasteryLevel(jobCode, recruiter, MasteryLevel.PROFESSIONAL, capacity);
    }

    @Transactional
    public Recruitment createRecruitmentWithRequiredCapacityAndMasteryLevel(String jobCode, Recruiter recruiter, MasteryLevel masteryLevel, Capacity... capacities) {
        var job = JobGenerator.buildNewJob(jobCode, recruiter, "Title for " + jobCode, "Service for " + jobCode, "Bordeaux", singletonList("Jacques"), JobEvaluationState.PUBLISHED, masteryLevel);
        job = jobRepository.save(job);

        var activityLabel = JobActivityLabelGenerator.buildActivityWithCapacities(capacities[0], Arrays.copyOfRange(capacities, 1, capacities.length));
        activityLabel.setTitle("ActivityLabel for " + jobCode);
        activityLabel = jobActivityLabelRepository.save(activityLabel);

        var category = (TestFixtures.knowledgeCategory == null || categoryRepository.findOneByCode(TestFixtures.knowledgeCategory.getCode()) == null) ? createCategory() : categoryRepository.findOneByCode(TestFixtures.knowledgeCategory.getCode());

        var contextsForCategories = Sets.newHashSet(
                ContextsForCategory.builder()
                        .noContextForCategory(true)
                        .category(category)
                        .build()
        );
        var mission1 = JobGenerator.buildMissionForJobWithActivity(job, activityLabel, contextsForCategories);
        jobMissionRepository.save(mission1);

        return createDefaultRecruitmentForJob(job, RecruitmentState.PUBLISHED);
    }

    private Category createCategory() {
        var category = new Category();
        var code = UUID.randomUUID();
        category.setTitle("Cat " + code.toString());
        category.setCode(code.toString());
        return categoryRepository.save(category);
    }

    @Transactional
    public Recruitment createDefaultRecruitmentForJob(Job job, RecruitmentState state) {
        var profile = RecruitmentProfile.builder()
                .job(job)
                .uuid(UUID.randomUUID())
                .qualified(true)
                .modifiable(false)
                .qualifiedMissions(job.getMissions())
                .title("Profile for " + job.getTitle())
                .build();
        profile = recruitmentProfileRepository.save(profile);

        var recruitment = Recruitment.builder()
                .managerUserId("42")
                .typeContract(ContractType.CDI)
                .recruitmentProfile(profile)
                .state(state)
                .build();
        recruitment = recruitmentRepository.save(recruitment);

        recruitment.updateCodeOnJobCreate();
        return recruitmentRepository.save(recruitment);
    }

    @Transactional
    public Recruitment createRecruitmentWithNoRequirement(Recruiter recruiter, String jobCode, Capacity... capacities) {
        var job = jobGenerator.createJobWithCapacitiesAndLevel(jobCode, recruiter, MasteryLevel.MAX_LEVEL, capacities);
        var recruitment = createDefaultRecruitmentForJob(job, RecruitmentState.PUBLISHED);
        var profile = recruitment.getRecruitmentProfile();
        profile.getMandatoryContexts().forEach(c -> profile.updateOptionalContext(c.getId(), AcquisitionModalityDTO.INTEGRATION_PROCESS));
        profile.getMandatoryActivities().forEach(a -> profile.updateOptionalActivity(a.getUuid(), AcquisitionModalityDTO.INTEGRATION_PROCESS));
        recruitmentProfileRepository.save(profile);

        return recruitment;
    }

    @Deprecated // Prefer using Recruiter param
    @Transactional
    public Recruitment createRecruitmentWithNoRequirement(String jobCode, Capacity... capacities) {
        return createRecruitmentWithNoRequirement(organizationGenerator.createRecruiter(), jobCode, capacities);
    }

    @Transactional
    public Recruitment createRecruitmentWithNoRequirement(Job job) {
        return createRecruitmentWithNoRequirement(RecruitmentState.PUBLISHED, job);
    }

    @Transactional
    public Recruitment createRecruitmentWithNoRequirement(RecruitmentState state, Job job) {
        var recruitment = createDefaultRecruitmentForJob(job, state);
        var profile = recruitment.getRecruitmentProfile();
        profile.getMandatoryContexts().forEach(c -> profile.updateOptionalContext(c.getId(), AcquisitionModalityDTO.INTEGRATION_PROCESS));
        profile.getMandatoryActivities().forEach(a -> profile.updateOptionalActivity(a.getUuid(), AcquisitionModalityDTO.INTEGRATION_PROCESS));
        recruitmentProfileRepository.save(profile);
        return recruitment;
    }
}
