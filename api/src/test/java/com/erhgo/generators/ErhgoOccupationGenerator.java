package com.erhgo.generators;

import com.erhgo.domain.classifications.erhgooccupation.*;
import com.erhgo.domain.classifications.esco.EscoOccupation;
import com.erhgo.domain.classifications.esco.EscoSkill;
import com.erhgo.domain.classifications.rome.RomeOccupation;
import com.erhgo.domain.enums.BehaviorCategory;
import com.erhgo.domain.referential.Behavior;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.repositories.BehaviorRepository;
import com.erhgo.repositories.ContextRepository;
import com.erhgo.repositories.JobActivityLabelRepository;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.repositories.classifications.EscoSkillRepository;
import com.erhgo.repositories.classifications.RomeOccupationRepository;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.erhgo.generators.TestFixtures.*;
import static com.google.common.collect.Sets.newHashSet;

@Service
@Deprecated
public class ErhgoOccupationGenerator {

    @Autowired
    private JobActivityLabelGenerator jobActivityLabelGenerator;

    @Autowired
    private ErhgoOccupationRepository erhgoOccupationRepository;

    @Autowired
    private RomeOccupationRepository romeOccupationRepository;

    @Autowired
    private EscoSkillRepository escoSkillRepository;

    @Autowired
    private JobActivityLabelRepository activityLabelRepository;

    @Autowired
    private ContextRepository contextRepository;

    @Autowired
    private BehaviorRepository behaviorRepository;

    public static int GLOBAL_INDEX = 0;

    public static ErhgoOccupation buildErhgoOccupation(String title,
                                                       MasteryLevel masteryLevel,
                                                       boolean qualifiedState,
                                                       Set<EscoOccupation> escoOccupations,
                                                       Set<EscoSkill> escoSkills,
                                                       RomeOccupation... romeOccupations) {
        var occupation = ErhgoOccupation.builder()
                .id(UUID.randomUUID())
                .title(title)
                .description("Description of " + title)
                .level(masteryLevel)
                .escoOccupations(escoOccupations)
                .skills(Sets.newHashSet(escoSkills))
                .build();
        Stream.of(romeOccupations).forEach(occupation::addRome);
        if (qualifiedState) {
            occupation.qualifyOccupation();
        }

        return occupation.computeQualificationState();
    }

    public ErhgoOccupation createErhgoOccupation(String title,
                                                 MasteryLevel masteryLevel,
                                                 boolean qualifiedState,
                                                 Set<EscoOccupation> escoOccupations,
                                                 Set<EscoSkill> escoSkills,
                                                 RomeOccupation... romeOccupations) {
        return erhgoOccupationRepository.save(buildErhgoOccupation(title, masteryLevel, qualifiedState, escoOccupations, escoSkills, romeOccupations));
    }

    public ErhgoOccupation createErhgoOccupationWithoutReferentialData(UUID uuid) {
        return createErhgoOccupationWithoutReferentialDataLevelAndOccupation(uuid, MasteryLevel.STRATEGIC);
    }

    public EscoSkill createSkillNotQualified(String title) {
        return escoSkillRepository.save(EscoSkill.builder()
                .title(title)
                .uri("URI for " + title)
                .build());
    }

    @Transactional
    public EscoSkill createSkillWithReferentialData(String title, JobActivityLabel jobActivityLabel, Behavior behavior, Context... contexts) {
        return escoSkillRepository.save(EscoSkill.builder()
                .activities(Sets.newHashSet(jobActivityLabel))
                .contexts(newHashSet(contexts))
                .behaviors(behavior == null ? Collections.emptySet() : newHashSet(behavior))
                .title(title)
                .uri("URI for " + title)
                .build());
    }

    public EscoSkill createSkillWithCapacities(String title, Capacity firstCapacity, Capacity... capacities) {
        var activity = jobActivityLabelGenerator.createJobActivityLabelWithCapacities(firstCapacity, capacities);
        return escoSkillRepository.save(EscoSkill.builder()
                .activities(Sets.newHashSet(activity))
                .noActivity(false)
                .noContext(true)
                .noBehavior(true)
                .title(title)
                .uri("URI for " + title)
                .build());
    }

    public EscoSkill createSkillWithoutReferentialData(String title) {
        return escoSkillRepository.save(EscoSkill.builder()
                .title(title)
                .noActivity(true)
                .noContext(true)
                .noBehavior(true)
                .uri("URI for " + title)
                .build());
    }

    @Transactional
    public ErhgoOccupation createErhgoOccupationForUUIDAndSkills(UUID uuid, MasteryLevel level, boolean qualifiedState, EscoSkill... skills) {

        var occupation = erhgoOccupationRepository.save(ErhgoOccupation.builder().title("Title of erhgo occupation " + uuid)
                .level(level)
                .skills(Set.of(skills))
                .qualificationState(ErhgoOccupationState.NONE)
                .id(uuid)
                .build());

        var occupationActivity = Stream.of(skills)
                .flatMap(skill -> skill.getActivities() == null ? Stream.empty() : skill.getActivities().stream())
                .map(a -> OccupationActivity
                        .builder()
                        .activity(a)
                        .occupationQualificationSource(OccupationQualificationSource.MANUAL)
                        .occupation(occupation)
                        .build())
                .collect(Collectors.toSet());

        occupation.setEntities(newHashSet(occupationActivity));
        occupation.getAlternativeLabels().addAll(Arrays.asList("title à", "Title B", "Title c"));
        occupation.setBehaviorsDescription("description des attitudes");

        if (qualifiedState) {
            occupation.qualifyOccupation();
        }

        return occupation.computeQualificationState();
    }

    public RomeOccupation createRomeOccupation(String code, String title) {
        return romeOccupationRepository.save(RomeOccupation
                .builder()
                .code(code)
                .title(title)
                .build());
    }

    public ErhgoOccupation createErhgoOccupationWithCapacities(UUID uuid,
                                                               MasteryLevel level,
                                                               boolean qualifiedState,
                                                               boolean withMandatoryActivity,
                                                               BehaviorCategory behaviorCategory1,
                                                               BehaviorCategory behaviorCategory2,
                                                               BehaviorCategory behaviorCategory3,
                                                               Capacity firstCapacity,
                                                               Capacity... capacities) {
        var activity = jobActivityLabelGenerator.createJobActivityLabelWithCapacities(firstCapacity, capacities);
        var occupation = new ErhgoOccupationMotherObject()
                .withTitle("Esco " + GLOBAL_INDEX++)
                .withId(uuid)
                .withLevel(level)
                .withActivity(activity, withMandatoryActivity)
                .withSkills(createSkillWithReferentialData("SK_0 " + uuid, activity, null),
                        createSkillWithReferentialData("SK_1 " + uuid, activity, null),
                        createSkillWithoutReferentialData("SK_2 " + uuid))
                .withBehaviorCategory1(behaviorCategory1)
                .withBehaviorCategory2(behaviorCategory2)
                .withBehaviorCategory3(behaviorCategory3)
                .instance();

        if (qualifiedState) {
            occupation.qualifyOccupation();
        }

        return erhgoOccupationRepository.save(occupation.computeQualificationState());
    }

    public ErhgoOccupation createErhgoOccupationWithCapacitiesInDifferentActivities(UUID uuid, MasteryLevel level, boolean qualifiedState, Capacity... capacities) {
        var occupation = ErhgoOccupation.builder()
                .id(uuid)
                .level(level)
                .title("Esco with multiple activities for " + uuid)
                .build();

        var occupationActivity = Stream.of(capacities)
                .map(c -> jobActivityLabelGenerator.createJobActivityLabelWithCapacities(c))
                .map(a -> OccupationActivity
                        .builder()
                        .activity(a)
                        .occupationQualificationSource(OccupationQualificationSource.MANUAL)
                        .occupation(occupation)
                        .build())
                .collect(Collectors.toSet());
        occupation.setEntities(newHashSet(occupationActivity));
        if (qualifiedState) {
            occupation.qualifyOccupation();
        }
        return erhgoOccupationRepository.save(occupation.computeQualificationState());
    }

    public ErhgoOccupation createErhgoOccupationWithoutReferentialDataLevelAndOccupation(UUID uuid, MasteryLevel level) {
        return erhgoOccupationRepository.save(ErhgoOccupation.builder()
                .id(uuid)
                .skills(newHashSet(createSkillWithoutReferentialData("SK_3 " + uuid), createSkillNotQualified("SK_4")))
                .qualificationState(ErhgoOccupationState.NONE)
                .level(level)
                .title("title for " + uuid)
                .build()
                .computeQualificationState());
    }

    @Transactional
    public ErhgoOccupation createErhgoOccupationWithActivities(String title, List<JobActivityLabel> skillsLabels, List<JobActivityLabel> manualLabels, RomeOccupation... romeOccupations) {
        var skills = IntStream.range(0, skillsLabels.size()).mapToObj(index -> createSkillWithReferentialData("Skill " + index + "  of " + title, skillsLabels.get(index), B_01, CT_21)).collect(Collectors.toSet());
        var occupation = createErhgoOccupation(title, MasteryLevel.PROFESSIONAL, false, Sets.newHashSet(ESCO_OCCUPATION_WITH_REFERENTIAL_DATA), skills, romeOccupations);

        var skillRelatedToManualActivity = createSkillWithReferentialData("Skill for manual activity of " + title, manualLabels.get(0), B_01, CT_21);
        skillRelatedToManualActivity.computeNoActivity(false);
        skillRelatedToManualActivity.computeNoContext(false);
        skillRelatedToManualActivity.computeNoBehavior(true);
        occupation.getSkills().add(skillRelatedToManualActivity);

        var skillsOccupationActivities = skillsLabels.stream()
                .map(a -> OccupationActivity.builder().activity(a).occupation(occupation).occupationQualificationSource(OccupationQualificationSource.SKILL).build())
                .collect(Collectors.toSet());

        var manualOccupationActivities = manualLabels.stream()
                .map(a -> OccupationActivity.builder().activity(a).occupation(occupation).occupationQualificationSource(OccupationQualificationSource.MANUAL).build())
                .collect(Collectors.toSet());

        occupation.setEntities(Sets.newHashSet(Sets.union(skillsOccupationActivities, manualOccupationActivities)));

        return erhgoOccupationRepository.save(occupation);
    }

    @Transactional
    public ErhgoOccupation createErhgoOccupationWithContexts(String title, List<Context> skillsContexts, List<Context> manualContexts, RomeOccupation... romeOccupations) {
        var skills = IntStream.range(0, skillsContexts.size()).mapToObj(index -> createSkillWithReferentialData("Skill " + index + "  of " + title, ACT_10, B_01, skillsContexts.get(index))).collect(Collectors.toSet());
        var occupation = createErhgoOccupation(title, MasteryLevel.PROFESSIONAL, false, Sets.newHashSet(ESCO_OCCUPATION_WITH_REFERENTIAL_DATA), skills, romeOccupations);

        var skillRelatedToManualContext = createSkillWithReferentialData("Skill for manual context of " + title, ACT_10, B_01, manualContexts.get(0));
        skillRelatedToManualContext.computeNoActivity(false);
        skillRelatedToManualContext.computeNoContext(false);
        skillRelatedToManualContext.computeNoBehavior(true);
        occupation.getSkills().add(skillRelatedToManualContext);

        var skillsOccupationContexts = skillsContexts.stream()
                .map(c -> OccupationContext.builder().context(c).occupation(occupation).occupationQualificationSource(OccupationQualificationSource.SKILL).build())
                .collect(Collectors.toSet());

        var manualOccupationContexts = manualContexts.stream()
                .map(c -> OccupationContext.builder().context(c).occupation(occupation).occupationQualificationSource(OccupationQualificationSource.MANUAL).build())
                .collect(Collectors.toSet());

        occupation.setEntities(Sets.newHashSet(Sets.union(skillsOccupationContexts, manualOccupationContexts)));

        return erhgoOccupationRepository.save(occupation);
    }

    @Transactional
    public ErhgoOccupation createErhgoOccupationForReferentialDatas(UUID uuid,
                                                                    boolean qualifiedState,
                                                                    JobActivityLabel skillMandatoryActivity,
                                                                    JobActivityLabel manualActivity,
                                                                    Context skillContext,
                                                                    Context manualMandatoryContext,
                                                                    Behavior skillBehavior,
                                                                    Behavior manualBehavior,
                                                                    EscoSkill... skills
    ) {
        var occupation = createErhgoOccupationForUUIDAndSkills(uuid, MasteryLevel.PROFESSIONAL, qualifiedState, skills);
        Set<AbstractOccupationEntity> entities = Sets.newHashSet();
        var mandatoryActivity =
                entities.add(OccupationActivity.builder()
                        .activity(activityLabelRepository.getOne(skillMandatoryActivity.getUuid()))
                        .occupationQualificationSource(OccupationQualificationSource.SKILL)
                        .occupation(occupation)
                        .build());
        entities.add(OccupationActivity.builder()
                .activity(activityLabelRepository.getOne(manualActivity.getUuid()))
                .occupationQualificationSource(OccupationQualificationSource.MANUAL)
                .occupation(occupation)
                .build());

        entities.add(OccupationContext.builder()
                .context(contextRepository.getOne(skillContext.getId()))
                .occupationQualificationSource(OccupationQualificationSource.SKILL)
                .occupation(occupation)
                .build());
        entities.add(OccupationContext.builder()
                .context(contextRepository.getOne(manualMandatoryContext.getId()))
                .occupationQualificationSource(OccupationQualificationSource.MANUAL)
                .occupation(occupation)
                .build());

        entities.add(OccupationBehavior.builder()
                .behavior(behaviorRepository.getOne(skillBehavior.getId()))
                .occupationQualificationSource(OccupationQualificationSource.SKILL)
                .occupation(occupation)
                .build());
        entities.add(OccupationBehavior.builder()
                .behavior(behaviorRepository.getOne(manualBehavior.getId()))
                .occupationQualificationSource(OccupationQualificationSource.MANUAL)
                .occupation(occupation)
                .build());

        occupation.setEntities(entities);
        occupation.addRome(romeOccupationRepository.findById(R3.getCode()).orElseThrow());
        occupation.addRome(romeOccupationRepository.findById(R4.getCode()).orElseThrow());
        occupation.setEscoOccupations(Sets.newHashSet(ESCO_OCCUPATION_WITHOUT_REFERENTIAL_DATA));
        occupation.setEntityMandatoryState(skillMandatoryActivity.getUuid(), MandatoryState.ESSENTIAL);
        occupation.setEntityMandatoryState(manualMandatoryContext.getId(), MandatoryState.ESSENTIAL);
        occupation.setDescription("Description of erhgo occupation " + uuid);
        if (qualifiedState) {
            occupation.qualifyOccupation();
        }
        return occupation.computeQualificationState();
    }

    @Transactional
    public ErhgoOccupation createErhgoOccupationWithBehaviors(String title, List<Behavior> skillBehaviors, List<Behavior> manualBehaviors, RomeOccupation... romeOccupations) {
        var skills = IntStream.range(0, skillBehaviors.size()).mapToObj(index -> createSkillWithReferentialData("Skill " + index + "  of " + title, ACT_10, skillBehaviors.get(index), CT_21)).collect(Collectors.toSet());
        var occupation = createErhgoOccupation(title, MasteryLevel.PROFESSIONAL, false, Sets.newHashSet(ESCO_OCCUPATION_WITH_REFERENTIAL_DATA), skills, romeOccupations);

        var skillRelatedToManualBehavior = createSkillWithReferentialData("Skill for manual behavior of " + title, ACT_10, manualBehaviors.get(0));
        skillRelatedToManualBehavior.computeNoActivity(false);
        skillRelatedToManualBehavior.computeNoContext(true);
        skillRelatedToManualBehavior.computeNoBehavior(false);
        occupation.getSkills().add(skillRelatedToManualBehavior);

        var skillsOccupationBehaviors = skillBehaviors.stream()
                .map(b -> OccupationBehavior.builder().behavior(b).occupation(occupation).occupationQualificationSource(OccupationQualificationSource.SKILL).build())
                .collect(Collectors.toSet());

        var manualOccupationBehaviors = manualBehaviors.stream()
                .map(b -> OccupationBehavior.builder().behavior(b).occupation(occupation).occupationQualificationSource(OccupationQualificationSource.MANUAL).build())
                .collect(Collectors.toSet());

        occupation.setEntities(Sets.newHashSet(Sets.union(skillsOccupationBehaviors, manualOccupationBehaviors)));

        return erhgoOccupationRepository.save(occupation);
    }

    @Transactional
    public ErhgoOccupation createErhgoOccupationWithTitleAndLabels(String title, String... expectedLabels) {
        return erhgoOccupationRepository.save(new ErhgoOccupationMotherObject().withLabels(expectedLabels).withTitle(title).instance());
    }

    public ErhgoOccupation createErhgoOccupationWithoutReferentialDataAndLevel(String title, MasteryLevel level) {
        return erhgoOccupationRepository.save(new ErhgoOccupationMotherObject()
                .withTitle(title)
                .withLevel(level)
                .instance());
    }

    @Transactional
    public ErhgoOccupation createErhgoOccupation(ErhgoOccupation occupation) {
        if (occupation.getSkills() != null) {
            escoSkillRepository.saveAll(occupation.getSkills());
        }
        if (occupation.getActivities() != null) {
            activityLabelRepository.saveAll(occupation.getActivities());
        }
        return erhgoOccupationRepository.save(occupation);
    }

    public ErhgoOccupation save(ErhgoOccupation occupation) {
        return erhgoOccupationRepository.save(occupation);
    }
}
