package com.erhgo.generators;

import com.erhgo.domain.KeycloakUserSummary;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.JobEvaluationState;
import com.erhgo.domain.job.ContextsForCategory;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.job.JobType;
import com.erhgo.domain.job.Mission;
import com.erhgo.domain.referential.*;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.repositories.CategoryRepository;
import com.erhgo.repositories.EmployerRepository;
import com.erhgo.repositories.JobMissionRepository;
import com.erhgo.repositories.JobRepository;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

import static com.erhgo.generators.JobActivityLabelGenerator.buildActivityWithCapacities;
import static com.erhgo.generators.TestFixtures.*;
import static com.erhgo.utils.DateTimeUtils.ZONE_ID;
import static com.google.common.collect.Sets.newHashSet;

@Service
@Deprecated
public class JobGenerator {

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private JobMissionRepository missionRepository;

    @Autowired
    private CategoryRepository categoryRepository;

    @Autowired
    private JobActivityLabelGenerator jobActivityLabelGenerator;

    @Autowired
    private ContextGenerator contextGenerator;

    @Autowired
    private OrganizationGenerator organizationGenerator;

    @Autowired
    private EmployerRepository employerRepository;

    public static Long GLOBAL_ID = 1L, JOB_INDEX = 1L;

    public static Mission buildMissionForJobAndActivityWithScore(int score, JobActivityLabel activity, Job job) {
        Context context = buildContextWithScore(score);
        return buildMissionForJobWithContextAndActivity(job, context, activity);
    }

    public static Job buildNewJob() {
        var id = JOB_INDEX++;
        var jobCode = "J-" + JOB_INDEX;
        return buildNewJob(jobCode, null, "Job " + id, "Service for job " + id, null, Lists.newArrayList(), JobEvaluationState.PUBLISHED, MasteryLevel.PROFESSIONAL);
    }

    public static Context buildContextWithScore(int score) {
        CategoryLevel categoryLevel = new CategoryLevel();
        categoryLevel.setScore(score);
        categoryLevel.setId(GLOBAL_ID++);

        Context context = new Context();
        context.setCategoryLevel(categoryLevel);
        context.setIndex(GLOBAL_ID++);
        context.setId(UUID.randomUUID());
        return context;
    }

    public static Job buildJobWithActivityAndNoContext(JobActivityLabel activity) {
        Job job = buildNewJob();
        Mission mission = Mission.builder().activities(newHashSet(activity)).contextsForCategory(newHashSet(ContextsForCategory.builder().noContextForCategory(true).build())).build();
        mission.setId(GLOBAL_ID++);
        job.addMission(mission);
        return job;
    }

    public static Mission buildMissionForJobWithActivity(Job job, JobActivityLabel jobActivityLabel, Set<ContextsForCategory> contextsForCategories) {
        var mission1 = new Mission();
        mission1.setTitle("Single mission of job " + job.getTitle());
        mission1.setActivities(newHashSet(jobActivityLabel));
        mission1.setContextsForCategory(contextsForCategories);
        mission1.setPosition(0);
        job.addMission(mission1);
        return mission1;
    }

    public static Mission buildMissionForJobWithContext(Job job, Context context) {
        Capacity capacity = CapacityGenerator.buildCapacity();
        JobActivityLabel jobActivityLabel = buildActivityWithCapacities(capacity);
        return buildMissionForJobWithContextAndActivity(job, context, jobActivityLabel);
    }

    private static Mission buildMissionForJobWithContextAndActivity(Job job, Context context, JobActivityLabel activity) {
        var mission = new Mission();
        mission.setTitle("Single mission of job " + job.getTitle());
        Capacity capacity = Capacity.builder().build();
        capacity.setId(GLOBAL_ID++);
        mission.setActivities(newHashSet(activity));
        mission.setContextsForCategory(newHashSet(ContextsForCategory.builder().category(context.getCategoryLevel().getCategory()).contexts(newHashSet(context)).build()));
        mission.setPosition(0);
        mission.setId(GLOBAL_ID++);
        job.addMission(mission);
        return mission;
    }

    public static Job buildJobWithOneContextPerMission(Context... contexts) {
        var job = buildNewJob();
        Stream.of(contexts).forEach(context -> buildMissionForJobWithContext(job, context));
        return job;

    }

    public Job createJob(String code, Recruiter recruiter, String title) {
        return jobRepository.save(buildNewJob(code, recruiter, title, "s1", "site", Sets.newHashSet("Jean-Claude"), JobEvaluationState.PUBLISHED, MasteryLevel.PROFESSIONAL));
    }

    public static Job buildNewJob(String code, Recruiter organization, String title, String service, String site, Collection<String> observators, JobEvaluationState state, MasteryLevel masteryLevel) {
        final var job = new Job();
        job.setJobType(JobType.OBSERVED);
        job.setId(UUID.randomUUID());
        job.setTitle(title);
        job.setDescription("Short description of job " + code);
        job.setRecruiter(organization);
        job.setService(service);
        if (site != null) {
            job.setLocation(Location.builder()
                    .city(site)
                    .radiusInKm(30)
                    .build());
        }
        job.setCreatedBy(new KeycloakUserSummary("user_id"));
        job.setObservators(newHashSet(observators));
        job.setObservationDate(OffsetDateTime.now(ZONE_ID));
        job.setPublicationDate(OffsetDateTime.now(ZONE_ID));
        job.setState(state);
        job.setMasteryLevel(masteryLevel);
        return job;
    }

    // Use Recruiter param instead code
    @Deprecated
    public static Job buildEmptyJob(String organizationCode) {
        return Job.builder()
                .jobType(JobType.OBSERVED)
                .id(UUID.randomUUID())
                .recruiter(OrganizationGenerator.buildRecruiter(organizationCode, AbstractOrganization.OrganizationType.ENTERPRISE))
                .title("Another job of " + organizationCode)
                .build();
    }

    public static Job buildEmptyJob(Recruiter recruiter) {
        return Job.builder()
                .jobType(JobType.OBSERVED)
                .id(UUID.randomUUID())
                .recruiter(recruiter)
                .title("Another job of " + recruiter.getCode())
                .state(JobEvaluationState.LISTED)
                .build();
    }

    public static Mission buildEmptyMission() {
        var job = buildEmptyJob("E-042");

        var mission = Mission.builder().job(job).build();
        job.addMission(mission);

        return mission;
    }

    public Mission createMissionWithContextAndActivities(String code, Context context, Job job, JobActivityLabel... activities) {

        var contextsForCategories = StreamSupport.stream(categoryRepository.findAll().spliterator(), false).map(c -> {
            var contextForCategory = ContextsForCategory.builder().category(c).build();
            var noContextForCategory = !c.getCode().equals(context.getCategoryCode());
            contextForCategory.setContexts(noContextForCategory ? Collections.emptySet() : Sets.newHashSet(context));
            contextForCategory.setNoContextForCategory(noContextForCategory);

            return contextForCategory;
        }).collect(Collectors.toSet());

        Mission mission = Mission.builder()
                .activities(newHashSet(activities))
                .contextsForCategory(contextsForCategories)
                .build();
        mission.setTitle("Mission " + code);
        mission.setDescription("Description de la mission " + code);
        mission.setCode(code);
        job.addMission(mission);
        mission = missionRepository.save(mission);
        jobRepository.save(job);
        return mission;
    }

    public static Mission getMissionWithActivity(Job job, Activity activity) {
        return job.getMissions().stream().filter(m -> m.getActivities().stream().map(JobActivityLabel::getActivity).anyMatch(a -> a.equals(activity))).findFirst().orElseThrow();
    }

    public Job createJobWithMission(String code, Recruiter organization, Context context, JobActivityLabel... activities) {
        Job job = createJob(code, organization, "Job with code " + code + " and organization " + organization.getCode());
        createMissionWithContextAndActivities("M-" + code, context, job, activities);
        return job;
    }

    public Job createJobWithMissions(String code, Recruiter organization, String title, String service, String site, String comment, List<String> observators, JobEvaluationState state) {
        var job = JobGenerator.buildNewJob(code, organization, title, service, site, observators, state, MasteryLevel.PROFESSIONAL);
        job = jobRepository.save(job);

        job.getBehaviors().add(B_01);
        job.getBehaviors().add(B_02);
        job.getBehaviors().add(B_03);
        job.getBehaviors().add(B_04);
        job.getBehaviors().add(B_05);
        job.getBehaviors().add(B_06);
        job.getBehaviors().add(B_07);

        var contextsForCategory1 = new ContextsForCategory();
        contextsForCategory1.setCategory(modusOperandisCategory);
        contextsForCategory1.setContexts(newHashSet(CONTEXT_USED_IN_JOB_OF_FORMATION, CONTEXT_USED_IN_JOB_WITH_QUESTION));

        var mission1 = new Mission();
        mission1.setTitle(M_01_TITLE);
        mission1.setActivities(newHashSet(ACT_01, jobActivityLabelUsedOnlyInJobMission));
        mission1.setContextsForCategory(newHashSet(contextsForCategory1));
        mission1.setPosition(0);
        job.addMission(mission1);
        missionRepository.save(mission1);

        var contextsForCategory2 = new ContextsForCategory();
        contextsForCategory2.setCategory(knowledgeCategory);
        contextsForCategory2.setContexts(newHashSet(MANDATORY_CONTEXT, CT_02));

        var mission2 = new Mission();
        mission2.setTitle(M_02_TITLE);
        mission2.setActivities(newHashSet(ACT_05, jobActivityLabelUsedInCandidatureExperienceAndJobMission));
        mission2.setContextsForCategory(newHashSet(contextsForCategory2));
        mission2.setPosition(1);
        job.addMission(mission2);
        missionRepository.save(mission2);

        var contextsForCategory3 = new ContextsForCategory();
        contextsForCategory3.setCategory(knowledgeCategory);
        contextsForCategory3.setContexts(newHashSet(MANDATORY_CONTEXT, CT_02));

        var mission3 = new Mission();
        mission3.setTitle(M_03_TITLE);
        mission3.setActivities(newHashSet(ACT_05, jobActivityLabelUsedInCandidatureExperienceAndJobMission));
        mission3.setContextsForCategory(newHashSet(contextsForCategory3));
        mission3.setPosition(2);
        job.addMission(mission3);
        job.setCreatedBy(USER_ADMIN);
        missionRepository.save(mission3);

        return jobRepository.save(job);
    }

    @Transactional
    public Job createJobWithCapacitiesAndLevel(String code, MasteryLevel level, Capacity... capacities) {
        return createJobWithCapacitiesAndLevel(code, organizationGenerator.createRecruiter(), level, capacities);
    }

    @Transactional
    public Job createJobWithCapacitiesAndLevel(String code, Recruiter organization, MasteryLevel level, Capacity... capacities) {
        return createJobWithCapacitiesAndLevel(JobType.OBSERVED, code, organization, level, capacities);
    }

    @Transactional
    public Job createJobWithCapacitiesAndLevel(JobType jobType, String code, Recruiter organization, MasteryLevel level, Capacity... capacities) {
        var uselessContext = contextGenerator.createContextWithLevel(GLOBAL_ID++, level.getMasteryLevel());
        var job = createJobWithMission(
                code,
                organization,
                uselessContext,
                Stream.of(capacities).map(c -> jobActivityLabelGenerator.createActivity("Act for job " + code, c)).toArray(JobActivityLabel[]::new)
        );
        job.setJobType(jobType);
        job.setMasteryLevel(level);
        return job;
    }

    @Transactional
    public Job createJobWithLevelAndCapacitiesOverMultipleMissions(String code, MasteryLevel level, Capacity firstCapacity, Capacity... capacities) {
        var job = createJobWithCapacitiesAndLevel(code, level, firstCapacity);
        var uselessContext = contextGenerator.createContext();

        IntStream.range(0, capacities.length)
                .forEach(i -> createMissionWithContextAndActivities("Mission of " + code + " - " + i, uselessContext, job, jobActivityLabelGenerator.createActivity("Act for job " + code, capacities[i])));

        return job;
    }

    @Transactional
    public Job createJob() {
        var job = buildEmptyJob(organizationGenerator.createRecruiter());
        return jobRepository.save(job);
    }


    public Job createJob(String code, Employer employer) {
        var job = buildNewJob(code, employer.getRefererRecruiter(), "title with " + employer.getCode() + "-" + code,
                "s1",
                "site",
                Sets.newHashSet("Jean-Claude"),
                JobEvaluationState.PUBLISHED,
                MasteryLevel.PROFESSIONAL);
        job.setJobType(JobType.OBSERVED);
        job.setEmployer(employer);
        return jobRepository.save(job);
    }

    public Job createJobForEmployer(String employerCode) {
        var employer = employerRepository.findOneByCode(employerCode);
        if (employer == null) {
            employer = organizationGenerator.createEmployer(
                    employerCode,
                    organizationGenerator.createRecruiter("T-05" + employerCode, AbstractOrganization.OrganizationType.TERRITORIAL));
        }

        return createJob("J_M" + GLOBAL_ID++, employer);
    }

    @Transactional
    public Job createJobWithLevelCriteriaAndCapacities(String orga, List<CriteriaValue> criteriaValues, MasteryLevel level, Capacity... capacities) {
        var job = createJobWithCapacitiesAndLevel(orga, level, capacities);
        job.resetCriteriaValues(criteriaValues);
        return job;
    }
}
