package com.erhgo.generators;

import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.ExperienceType;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.experience.UserExperience;
import com.erhgo.repositories.CapacityRepository;
import com.erhgo.repositories.UserExperienceRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Stream;

@Service
public class UserExperienceGenerator {

    @Autowired
    private UserExperienceRepository userExperienceRepository;

    @Autowired
    private JobActivityLabelGenerator jobActivityLabelGenerator;

    @Autowired
    private ErhgoOccupationGenerator erhgoOccupationGenerator;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private CapacityRepository capacityRepository;

    public static int JOB_INDEX = 0;

    public static UserExperience generateExperienceWithActivityAndLevel(JobActivityLabel activity, int level) {
        var erhgoOccupation = new ErhgoOccupationMotherObject().withLevel(level).withActivity(activity, false).instance();
        var user = UserProfileGenerator.buildEmptyUserProfile();
        var experience = new UserExperience();
        experience.setUserProfile(user);
        experience.setErhgoOccupation(erhgoOccupation);
        return experience;
    }

    public static UserExperience buildExperienceWithOccupation(ErhgoOccupation occupation, UserProfile userProfile) {
        var experience = UserExperience.builder()
                .type(ExperienceType.JOB)
                .jobTitle("Empty Job Nb " + JOB_INDEX)
                .organizationName("Organization Nb " + JOB_INDEX++)
                .userProfile(userProfile)
                .durationInMonths(9)
                .erhgoOccupation(occupation)
                .build();

        var experiences = userProfile.experiences() == null ? Sets.<UserExperience>newHashSet():userProfile.experiences();
        experiences.add(experience);
        userProfile.experiences(experiences);
        return experience;

    }

    public UserExperience createEmptyExperience(UserProfile userProfile) {
        var experience = UserExperience.builder()
                .type(ExperienceType.JOB)
                .jobTitle("Empty Job Nb " + JOB_INDEX)
                .organizationName("Organization Nb " + JOB_INDEX++)
                .userProfile(userProfile)
                .durationInMonths(9)
                .build();

        return userExperienceRepository.save(experience);
    }

    public static UserExperience generateExperienceWithOccupation(UserProfile userProfile, ErhgoOccupation erhgoOccupation) {
        var xp = UserExperience.builder()
                .type(ExperienceType.JOB)
                .userProfile(userProfile)
                .jobTitle("Job Nb " + JOB_INDEX)
                .organizationName("Organization Nb " + JOB_INDEX)
                .erhgoOccupation(erhgoOccupation)
                .durationInMonths(9)
                .build();
        userProfile.experiences().add(xp);
        return xp;
    }

    public static UserExperience generateExperienceWithoutOccupation(UserProfile userProfile) {
        var xp = UserExperience.builder()
                .type(ExperienceType.JOB)
                .jobTitle("Job Title Nb " + JOB_INDEX)
                .durationInMonths(9)
                .organizationName("Organization Nb " + JOB_INDEX++)
                .userProfile(userProfile)
                .build();
        if (userProfile.experiences() == null) {
            userProfile.experiences(new HashSet<>());
        }
        userProfile.experiences().add(xp);
        return xp;
    }

    public static void buildExperienceWithCapacity(UserProfile userProfile, int level, Capacity capacity) {
        var experience = generateExperienceWithoutOccupation(userProfile);
        var activity = JobActivityLabelGenerator.buildActivityWithCapacities(capacity);
        var erhgo = new ErhgoOccupationMotherObject().withLevel(level).withActivity(activity, false).instance();
        experience.setErhgoOccupation(erhgo);
        userProfile.addCapacities(Set.of(capacity));
    }

    @Transactional
    public void createExperienceWithCapacities(UserProfile userProfile, int level, Capacity... capacities) {
        var experience = generateExperienceWithoutOccupation(userProfile);
        JobActivityLabel activityLabel = null;
        if (capacities.length > 0) {
            activityLabel = jobActivityLabelGenerator.createJobActivityLabelWithCapacities(capacities[0], Arrays.copyOfRange(capacities, 1, capacities.length));
        }
        var erhgo = new ErhgoOccupationMotherObject()
                .withTitle("Uri For experience " + JOB_INDEX)
                .withLevel(level)
                .withActivity(activityLabel, false)
                .instance();
        erhgo = erhgoOccupationGenerator.createErhgoOccupation(erhgo);
        experience.setErhgoOccupation(erhgo);
        fixUserProfileCapacities(userProfile, capacities);
    }

    private void fixUserProfileCapacities(UserProfile userProfile, Capacity... capacities) {
        var capacityIds = Stream.of(capacities).map(Capacity::getId).toList();
        userProfile.addCapacities(Sets.newHashSet(capacityRepository.findAllById(capacityIds)));
        userProfileRepository.save(userProfile);
    }
}
