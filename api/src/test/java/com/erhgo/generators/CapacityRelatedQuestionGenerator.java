package com.erhgo.generators;

import com.erhgo.domain.enums.QuestionType;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.CapacityRelatedQuestion;
import com.erhgo.domain.referential.CapacityRelatedQuestionResponse;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.capacityrelatedquestion.AnswerForCapacityRelatedQuestion;
import com.erhgo.repositories.AnswerForCapacityRelatedQuestionRepository;
import com.erhgo.repositories.CapacityRelatedQuestionRepository;
import jakarta.persistence.EntityManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
public class CapacityRelatedQuestionGenerator {

    @Autowired
    private CapacityRelatedQuestionRepository repository;

    @Autowired
    private AnswerForCapacityRelatedQuestionRepository answerRepository;

    @Autowired
    private EntityManager entityManager;

    @Transactional
    public CapacityRelatedQuestion createQuestion(Capacity capacity1, Capacity capacity2, int questionIndex) {
        var question = CapacityRelatedQuestion.builder()
                .title("Question title for " + questionIndex + ":")
                .id(UUID.randomUUID())
                .questionIndex(questionIndex)
                .questionType(QuestionType.EXTRAPROFESSIONAL)
                .build();
        CapacityRelatedQuestionResponse
                .builder()
                .position(1)
                .title("A 2...for q" + questionIndex + "!")
                .question(question)
                .capacities(Set.of(capacity2))
                .build();
        CapacityRelatedQuestionResponse
                .builder()
                .position(0)
                .title("A 1 for q" + questionIndex + "!")
                .question(question)
                .capacities(Set.of(capacity1, capacity2))
                .build();
        return repository.save(question);
    }

    @Transactional
    public CapacityRelatedQuestion createEmptyQuestion(int questionIndex) {
        var question = CapacityRelatedQuestion.builder()
                .title("Question " + questionIndex)
                .questionType(QuestionType.EXTRAPROFESSIONAL)
                .id(UUID.randomUUID())
                .questionIndex(questionIndex)
                .build();
        return repository.save(question);
    }

    @Transactional
    public CapacityRelatedQuestion createQuestion(int questionIndex, UUID... answerUuids) {
        var question = CapacityRelatedQuestion.builder()
                .title("Question title for " + questionIndex + ":")
                .id(UUID.randomUUID())
                .questionType(QuestionType.EXTRAPROFESSIONAL)
                .questionIndex(questionIndex)
                .build();

        IntStream.range(0, answerUuids.length)
                .forEach(index -> CapacityRelatedQuestionResponse
                        .builder()
                        .id(answerUuids[index])
                        .position(index)
                        .title("A " + index + " for q" + questionIndex + "!")
                        .question(question)
                        .capacities(Collections.emptySet())
                        .build());

        return repository.save(question);
    }

    @Transactional
    public CapacityRelatedQuestion createExtraProQuestionWithSelectedAnswer(int questionIndex, UserProfile userProfile, UUID... answerUuids) {
        return createQuestionWithSelectedAnswer(questionIndex, entityManager.find(UserProfile.class, userProfile.uuid()), answerUuids);
    }

    @Transactional
    public CapacityRelatedQuestion createQuestionWithSelectedAnswer(int questionIndex, UserProfile userProfile, UUID... answerUuids) {
        var question = createQuestion(questionIndex, answerUuids);
        var firstResponse = question.getResponses().first();

        var answer = AnswerForCapacityRelatedQuestion.builder()
                .userProfile(userProfile)
                .response(firstResponse)
                .build();

        answerRepository.save(answer);
        return question;
    }


    @Transactional
    public CapacityRelatedQuestion createQuestionWithAllSelectedAnswers(int questionIndex, UserProfile userProfile, UUID... answerUuids) {
        var question = createQuestion(questionIndex, answerUuids);
        var answers = question.getResponses().stream().map(r -> AnswerForCapacityRelatedQuestion.builder()
                        .userProfile(userProfile)
                        .response(r)
                        .build())
                .collect(Collectors.toSet());

        answerRepository.saveAll(answers);
        return question;

    }

    @Transactional
    public CapacityRelatedQuestion createQuestionAndAnswerForCandidateWithCapacities(int index, UserProfile userProfile, Capacity... inputCapacities) {
        var question = createQuestion(index, UUID.randomUUID(), UUID.randomUUID());

        var firstResponse = question.getResponses().first();

        var responseCapacities = (Set<Capacity>) ReflectionTestUtils.getField(firstResponse, "capacities");
        var capacities = Set.of(inputCapacities).stream().map(c -> entityManager.find(Capacity.class, c.getId())).collect(Collectors.toSet());

        assert responseCapacities != null;
        responseCapacities.addAll(capacities);

        var answer = AnswerForCapacityRelatedQuestion.builder()
                .userProfile(userProfile)
                .response(firstResponse)
                .build();

        answerRepository.save(answer);
        entityManager.find(UserProfile.class, userProfile.uuid()).addCapacities(capacities);
        return question;
    }

    private CapacityRelatedQuestionResponse buildResponse(int index, CapacityRelatedQuestion question, Capacity... capacities) {
        return CapacityRelatedQuestionResponse
                .builder()
                .id(UUID.randomUUID())
                .position(index)
                .title("A " + index + " for q" + question.getQuestionIndex() + "!")
                .question(question)
                .capacities(Set.of(capacities))
                .build();
    }

    @Transactional
    public void answerFirstResponse(CapacityRelatedQuestion formationQuestion, UserProfile userProfileParam) {
        var response = repository.getOne(formationQuestion.getId()).getResponses().first();
        var userProfile = entityManager.find(UserProfile.class, userProfileParam.uuid());
        answerRepository.save(AnswerForCapacityRelatedQuestion.builder()
                .response(response)
                .userProfile(userProfile)
                .build());
        userProfile.addCapacities(response.getCapacities());
    }
}
