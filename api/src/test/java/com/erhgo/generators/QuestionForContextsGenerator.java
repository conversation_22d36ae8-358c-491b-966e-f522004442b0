package com.erhgo.generators;

import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.QuestionForContexts;
import com.erhgo.repositories.QuestionForContextsRepository;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
public class QuestionForContextsGenerator {

    @Autowired
    private QuestionForContextsRepository questionForContextsRepository;

    public static QuestionForContexts buildQuestionForContexts(String title, Context... contexts) {
        return QuestionForContexts.builder()
                .uuid(UUID.randomUUID())
                .title(title)
                .titleForNone("None " + " - " + title)
                .titleForLow("Low " + " - " + title)
                .titleForMedium("Medium " + " - " + title)
                .titleForHigh("High " + " - " + title)
                .contexts(Sets.newHashSet(contexts))
                .build();
    }

    public QuestionForContexts createQuestionForContexts(QuestionForContexts questionForContexts) {
        return questionForContextsRepository.save(questionForContexts);
    }

    public QuestionForContexts createQuestionForContexts(String title, Context... contexts) {
        return createQuestionForContexts(buildQuestionForContexts(title, contexts));
    }
}