package com.erhgo.generators;

import com.erhgo.domain.candidature.job.CandidatureRecruitmentState;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.enums.CandidatureState;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import jakarta.persistence.EntityManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;

@Service
@Deprecated
public class CandidatureGenerator {

    @Autowired
    private EntityManager entityManager;

    @Autowired
    private RecruitmentCandidatureRepository recruitmentCandidatureRepository;

    public static RecruitmentCandidature buildCandidature(UserProfile user, CandidatureRecruitmentState candidatureState, String organizationCode, boolean privateJobsOrganization) {

        var recruiter = OrganizationGenerator.buildRecruiter(organizationCode, AbstractOrganization.OrganizationType.ENTERPRISE);
        recruiter.setPrivateJobs(privateJobsOrganization);
        var job = JobGenerator.buildEmptyJob(recruiter);
        var profile = RecruitmentProfileGenerator.buildRecruitmentProfile(job);
        var recruitment = RecruitmentGenerator.buildRecruitment(profile);

        return RecruitmentCandidature.builder()
                .candidatureRecruitmentState(candidatureState)
                .userProfile(user)
                .recruitment(recruitment)
                .build();
    }

    @Transactional
    public RecruitmentCandidature createCandidature(UserProfile user, Recruitment recruitment) {

        entityManager.joinTransaction();
        recruitment = entityManager.merge(recruitment);
        user = entityManager.merge(user);

        return recruitmentCandidatureRepository.save(RecruitmentCandidature.builder()
                .userProfile(user)
                .recruitment(recruitment)
                .state(CandidatureState.VALIDATED)
                .globalCandidatureState(GlobalCandidatureState.NOT_TREATED_BY_ERHGO)
                .candidatureRecruitmentState(CandidatureRecruitmentState.SELECTED)
                .valid(true)
                .modifiedByUser(true)
                .build());
    }

    @Transactional
    public void createCandidatureNotFinalized(UserProfile user, Recruitment recruitment) {
        recruitmentCandidatureRepository.save(RecruitmentCandidature.builder()
                .userProfile(entityManager.merge(user))
                .recruitment(entityManager.merge(recruitment))
                .globalCandidatureState(GlobalCandidatureState.NOT_FINALIZED)
                .candidatureRecruitmentState(CandidatureRecruitmentState.SELECTED)
                .valid(true)
                .build());
    }

    @Transactional
    public void createCandidatureWithSubmissionDate(UserProfile user, Recruitment recruitment, OffsetDateTime submissionDate) {
        var candidature = recruitmentCandidatureRepository.save(RecruitmentCandidature.builder()
                .userProfile(user)
                .recruitment(recruitment)
                .state(CandidatureState.VALIDATED)
                .globalCandidatureState(GlobalCandidatureState.NOT_TREATED_BY_ERHGO)
                .candidatureRecruitmentState(CandidatureRecruitmentState.SELECTED)
                .valid(true)
                .modifiedByUser(true)
                .build());
        candidature.setSubmissionDate(submissionDate);
    }

}
