package com.erhgo.generators;

import com.erhgo.domain.criteria.Criteria;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.CriteriaQuestionType;
import com.erhgo.domain.enums.SourcingCriteriaStep;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.criteria.UserCriteriaValue;
import com.erhgo.repositories.CriteriaRepository;
import com.erhgo.repositories.UserProfileRepository;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Service
@Scope("prototype")
@NoArgsConstructor
public class CriteriaMotherObject {

    @Autowired
    private CriteriaRepository criteriaRepository;
    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    private Criteria.CriteriaBuilder criteriaBuilder = Criteria.builder();

    private String criteriaCode;
    private CriteriaQuestionType questionType;
    private boolean required;
    private List<CriteriaValue> criteriaValues = new ArrayList<>();
    private Integer criteriaIndex;

    private static int INDEX = 0;
    private SourcingCriteriaStep sourcingCriteriaStep;

    @Transactional
    public Criteria buildAndPersist() {
        var criteria = build();
        return criteriaRepository.save(criteria);
    }

    public Criteria build() {
        if (criteriaIndex == null) {
            criteriaIndex = criteriaRepository == null ? INDEX++ : criteriaRepository.findAll().size();
        }
        if (criteriaCode == null) {
            criteriaCode = "CR" + criteriaIndex;
        }
        if (questionType == null) {
            questionType = CriteriaQuestionType.THRESHOLD;
        }
        if (criteriaValues.isEmpty()) {
            criteriaValues = List.of(
                    getDefaultCriteriaValueBuilder().code(criteriaCode + "V1").titleForBO("title BO V1").titleStandalone("title V1").titleForQuestion("question V1").valueIndex(0).build(),
                    getDefaultCriteriaValueBuilder().code(criteriaCode + "V2").titleForBO("title BO V2").titleStandalone("title V2").titleForQuestion("question V2").valueIndex(1).build()
            );
        }
        criteriaValues.forEach(cv -> cv.setSourcingCriteriaStep(sourcingCriteriaStep));
        return initializeCriteria();
    }

    private Criteria initializeCriteria() {
        return criteriaBuilder
                .criteriaValues(criteriaValues)
                .questionLabel("Question for " + questionType)
                .questionType(questionType)
                .title("Title for " + questionType)
                .code(criteriaCode)
                .criteriaIndex(criteriaIndex)
                .required(required)
                .build();
    }

    public CriteriaMotherObject withCriteriaCode(String code) {
        this.criteriaCode = code;
        return this;
    }

    public CriteriaMotherObject withQuestionType(CriteriaQuestionType questionType) {
        this.questionType = questionType;
        return this;
    }

    public CriteriaMotherObject withValueAtIndex(int index) {
        this.criteriaValues.add(getDefaultCriteriaValueBuilder().code(UUID.randomUUID().toString()).valueIndex(index).build());
        return this;
    }

    private CriteriaValue.CriteriaValueBuilder getDefaultCriteriaValueBuilder() {
        return CriteriaValue.builder()
                .code("CRV_XX")
                .icon("smile")
                .titleForQuestion("La nuit")
                .titleStandalone("Travailler la nuit")
                .titleForBO("Il travaille la nuit")
                .valueIndex(0);
    }

    public CriteriaMotherObject withCriteriaIndex(int index) {
        this.criteriaIndex = index;
        return this;
    }

    public CriteriaMotherObject withRequired(Boolean required) {
        this.required = required;
        return this;
    }

    public CriteriaMotherObject withValueCodes(String... codes) {
        IntStream.range(0, codes.length).forEach(index -> criteriaValues.add(
                getDefaultCriteriaValueBuilder()
                        .code(codes[index])
                        .valueIndex(index)
                        .titleStandalone("Title for " + codes[index])
                        .titleForBO("Title BO for " + codes[index])
                        .build()));
        return this;
    }


    public static final String multipleCriteriaCode = "CR-1";
    public static final String thresholdCriteriaCode = "CR-2";

    public static final String selectedMultipleValue1 = "CRV-1-1";
    public static final String unselectedMultipleValue1 = "CRV-1-2";
    public static final String selectedMultipleValue2 = "CRV-1-3";
    public static final String absentCriteria1Value = "CRV-1-4";
    public static final String selectedThresholdValue = "CRV-2-1";
    public static final String absentThresholdValue = "CRV-2-2";

    @Transactional
    public UserProfile prepareMultipleCriteriaAndAnswers(String userId) {

        var multipleCriteria = applicationContext.getBean(CriteriaMotherObject.class)
                .withCriteriaCode(multipleCriteriaCode)
                .withValueCodes(selectedMultipleValue1, unselectedMultipleValue1, selectedMultipleValue2, absentCriteria1Value)
                .withQuestionType(CriteriaQuestionType.MULTIPLE)
                .withCriteriaIndex(0)
                .withRequired(true)
                .buildAndPersist();

        var thresholdCriteria = applicationContext.getBean(CriteriaMotherObject.class)
                .withValueCodes(selectedThresholdValue, absentThresholdValue)
                .withQuestionType(CriteriaQuestionType.THRESHOLD)
                .withCriteriaCode(thresholdCriteriaCode)
                .withCriteriaIndex(1)
                .withRequired(false)
                .buildAndPersist();

        var user = userProfileGenerator.createUserProfile(UUID.fromString(userId));
        var otherUser = userProfileGenerator.createUserProfile(UUID.randomUUID());

        var previousSelectedCriteria1 = Set.of(
                new UserCriteriaValue(user, multipleCriteria.getCriteriaValues().get(0), true),
                new UserCriteriaValue(user, multipleCriteria.getCriteriaValues().get(1), false),
                new UserCriteriaValue(user, multipleCriteria.getCriteriaValues().get(2), true)
        );
        var previousSelectedCriteria2 = Set.of(
                new UserCriteriaValue(user, thresholdCriteria.getCriteriaValues().get(0), true)
        );

        var userCriteriaValues = (Set<UserCriteriaValue>) ReflectionTestUtils.getField(user, "userCriteriaValues");
        userCriteriaValues.addAll(previousSelectedCriteria1);
        userCriteriaValues.addAll(previousSelectedCriteria2);

        var otherUserCriteriaValues = (Set<UserCriteriaValue>) ReflectionTestUtils.getField(otherUser, "userCriteriaValues");
        otherUserCriteriaValues.addAll(Set.of(new UserCriteriaValue(otherUser, multipleCriteria.getCriteriaValues().get(0), true)));
        otherUserCriteriaValues.addAll(Set.of(new UserCriteriaValue(otherUser, thresholdCriteria.getCriteriaValues().get(0), true)));
        return user;
    }


    @Transactional
    public void prepareCriteriaAnswers(String userId) {

        var user = this.prepareMultipleCriteriaAndAnswers(userId);
        var multipleCriteria1 = applicationContext.getBean(CriteriaMotherObject.class)
                .withQuestionType(CriteriaQuestionType.MULTIPLE)
                .withValueCodes("C4-1", "C4-2", "C4-3")
                .withCriteriaIndex(3)
                .buildAndPersist();

        var multipleCriteria2 = applicationContext.getBean(CriteriaMotherObject.class)
                .withQuestionType(CriteriaQuestionType.MULTIPLE)
                .withValueCodes("C3-1", "C3-2", "C3-3")
                .withCriteriaIndex(2)
                .buildAndPersist();
        var userCriteriaValues = (Set<UserCriteriaValue>) ReflectionTestUtils.getField(user, "userCriteriaValues");
        userCriteriaValues.add(new UserCriteriaValue(user, multipleCriteria1.getCriteriaValues().get(0), true));
        userCriteriaValues.addAll(Stream.of(
                        multipleCriteria2.getCriteriaValues().get(1),
                        multipleCriteria1.getCriteriaValues().get(1),
                        multipleCriteria2.getCriteriaValues().get(0))
                .map(c -> new UserCriteriaValue(user, c, false)).toList());

    }

    @Transactional
    public void prepareFilteredCriteriaAnswers(UserProfile userin) {
        var user = userProfileRepository.findByUserId(userin.userId()).orElseThrow();
        var criteria = prepareFilteredCriteriaNoAnswers();

        var userCriteriaValues = (Set<UserCriteriaValue>) ReflectionTestUtils.getField(user, "userCriteriaValues");
        var cr1 = criteria.get(0);
        var cr2 = criteria.get(1);
        var cr10 = criteria.get(2);

        userCriteriaValues.addAll(Stream.of(
                        cr1.getCriteriaValues().get(0),
                        cr10.getCriteriaValues().get(0)
                )
                .map(c -> new UserCriteriaValue(user, c, true)).toList());
        userCriteriaValues.addAll(Stream.of(
                        cr1.getCriteriaValues().get(1),
                        cr2.getCriteriaValues().get(0),
                        cr2.getCriteriaValues().get(1),
                        cr10.getCriteriaValues().get(1)
                )
                .map(c -> new UserCriteriaValue(user, c, false)).toList());

    }

    public CriteriaMotherObject forDriverLicence() {
        this.criteriaCode = Criteria.DRIVER_LICENCE_CRITERIA_CODE;
        var driverLicenceCriteriaValues = CriteriaValue.DRIVER_LICENCE_FOR_CRITERIA_RESPONSE.keySet().toArray(new String[]{});
        return withValueCodes(driverLicenceCriteriaValues);
    }

    public CriteriaMotherObject withWorkingTimeCriteria() {
        return withValueCodes(CriteriaValue.WORKING_TIME_FOR_CRITERIA_RESPONSE.keySet().toArray(String[]::new))
                .withQuestionType(CriteriaQuestionType.MULTIPLE)
                .withCriteriaCode(Criteria.TYPE_WORKING_TIME);
    }

    public CriteriaMotherObject withRemoteWorkCriteria() {
        return withValueCodes(IntStream.rangeClosed(1, 3)
                .mapToObj(a -> CriteriaValue.REMOTE_WORK_CRITERIA_VALUE_CODE_PREFIX + "-" + a)
                .toArray(String[]::new))
                .withQuestionType(CriteriaQuestionType.MULTIPLE);
    }

    public CriteriaMotherObject withWeekendAndNightWork() {
        return withValueCodes(new String[]{CriteriaValue.WEEKEND_WORK_CRITERIA_VALUE_CODE, CriteriaValue.NIGHT_WORK_CRITERIA_VALUE_CODE})
                .withQuestionType(CriteriaQuestionType.MULTIPLE);
    }

    public CriteriaMotherObject withTypeContractCriteria() {
        return withValueCodes(CriteriaValue.TYPE_CONTRACT_FOR_CRITERIA_RESPONSE.keySet().toArray(String[]::new))
                .withQuestionType(CriteriaQuestionType.MULTIPLE)
                .withCriteriaCode(Criteria.CONTRACT_TYPE_CRITERIA_CODE);
    }

    public CriteriaMotherObject withSourcingCriteriaStep(SourcingCriteriaStep step) {
        this.sourcingCriteriaStep = step;
        return this;
    }

    public List<Criteria> prepareFilteredCriteriaNoAnswers() {
        return List.of(applicationContext.getBean(CriteriaMotherObject.class)
                        .withCriteriaCode("CR-1")
                        .withQuestionType(CriteriaQuestionType.MULTIPLE)
                        .withValueCodes("C1-1", "C1-2", "C1-3")
                        .withCriteriaIndex(1)
                        .buildAndPersist(),
                applicationContext.getBean(CriteriaMotherObject.class)
                        .withCriteriaCode("CR-2")
                        .withQuestionType(CriteriaQuestionType.MULTIPLE)
                        .withValueCodes("C2-1", "C2-2", "C2-3")
                        .withCriteriaIndex(2)
                        .buildAndPersist(),
                applicationContext.getBean(CriteriaMotherObject.class)
                        .withCriteriaCode("CR-10")
                        .withQuestionType(CriteriaQuestionType.MULTIPLE)
                        .withValueCodes("C10-1", "C10-2", "C10-3")
                        .withCriteriaIndex(10)
                        .buildAndPersist());
    }
}
