package com.erhgo.generators;

import com.erhgo.domain.referential.Activity;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.repositories.JobActivityLabelRepository;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.google.common.collect.Sets.newHashSet;

@Service
@Deprecated
public class JobActivityLabelGenerator {

    @Autowired
    private JobActivityLabelRepository jobActivityLabelRepository;

    @Autowired
    private CapacityGenerator capacityGenerator;

    private int GLOBAL_INDEX = 0, GLOBAL_CAPACITY_INDEX = 0;

    public JobActivityLabel createJobActivityLabelWithCapacities(Capacity firstCapacity, Capacity... capacities) {
        return jobActivityLabelRepository.save(buildActivityLabel("Title for job activity " + GLOBAL_INDEX++, firstCapacity, capacities));
    }

    public static JobActivityLabel buildActivityWithCapacities(Capacity firstCapacity, Capacity... capacities) {
        var allCapacities = new HashSet<Capacity>();
        allCapacities.add(firstCapacity);
        Stream.of(capacities).forEach(allCapacities::add);
        return JobActivityLabel.builder()
                .uuid(UUID.randomUUID())
                .title(UUID.randomUUID().toString())
                .activity(Activity.builder().uuid(UUID.randomUUID()).inducedCapacities(allCapacities).build())
                .build();
    }

    @Transactional
    public JobActivityLabel createSecondActivityLabelSharingSameActivity(JobActivityLabel jobActivityLabel) {

        var activity = jobActivityLabel.getActivity();

        var secondActivityLabel = JobActivityLabel.builder().activity(activity).position(1).title("Second activityLabel of " + jobActivityLabel.getTitle()).build();
        return jobActivityLabelRepository.save(secondActivityLabel);
    }

    public JobActivityLabel createNewActivity(String title, String description, Capacity firstCapacity, Capacity... attachedCapacities) {
        final var activityLabel = buildActivityWithCapacities(firstCapacity, attachedCapacities);
        activityLabel.setTitle(title);
        activityLabel.setPosition(0);
        activityLabel.getActivity().setDescription(description);
        return jobActivityLabelRepository.save(activityLabel);
    }

    public static JobActivityLabel buildActivityLabel(String title, Capacity firstCapacity, Capacity... capacities) {
        var allCapacities = Sets.newHashSet(capacities);
        allCapacities.add(firstCapacity);
        return JobActivityLabel.builder()
                .uuid(UUID.randomUUID())
                .title(title)
                .position(0)
                .activity(Activity.builder().uuid(UUID.randomUUID()).inducedCapacities(newHashSet(allCapacities)).build())
                .build();
    }

    public JobActivityLabel createActivity(String title, Capacity ca1, Capacity... capacities) {

        return jobActivityLabelRepository.save(createActivity(UUID.randomUUID(), title, ca1, capacities));
    }

    public JobActivityLabel createActivity(UUID uuid, String title, Capacity ca1, Capacity... capacities) {

        Set<Capacity> allCapacities = Stream.concat(Stream.of(ca1), Stream.of(capacities)).collect(Collectors.toSet());
        JobActivityLabel activity = JobActivityLabel.builder()
                .activity(Activity.builder().uuid(UUID.randomUUID()).inducedCapacities(allCapacities).description(title).build())
                .title(title)
                .uuid(uuid)
                .position(1)
                .build();
        return jobActivityLabelRepository.save(activity);
    }

    public JobActivityLabel createActivity(UUID uuid, String title) {
        var capacity = capacityGenerator.createCapacity("CA1-20" + GLOBAL_CAPACITY_INDEX++);
        return createActivity(uuid, title, capacity);

    }
}
