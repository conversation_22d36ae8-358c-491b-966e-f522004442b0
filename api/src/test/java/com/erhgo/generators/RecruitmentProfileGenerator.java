package com.erhgo.generators;

import com.erhgo.domain.enums.AcquisitionModality;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.recruitment.OptionalActivity;
import com.erhgo.domain.recruitment.OptionalContext;
import com.erhgo.domain.recruitment.RecruitmentProfile;
import com.erhgo.domain.referential.JobActivityLabel;
import org.springframework.stereotype.Service;

import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class RecruitmentProfileGenerator {

    public static RecruitmentProfile buildRecruitmentProfile(Job job, JobActivityLabel... optionalActivities) {
        RecruitmentProfile profile = RecruitmentProfile.builder()
                .job(job)
                .uuid(UUID.randomUUID())
                .qualified(true)
                .modifiable(false)
                .build();

        profile.setOptionalActivities(Stream.of(optionalActivities).map(a -> OptionalActivity.builder().activityLabel(a).recruitmentProfile(profile).acquisitionModality(AcquisitionModality.TRAINING).build()).collect(Collectors.toSet()));
        profile.setOptionalContexts(job.getAllMissionsContexts().stream().map(c -> OptionalContext.builder().context(c).recruitmentProfile(profile).acquisitionModality(AcquisitionModality.TRAINING).build()).collect(Collectors.toSet()));
        return profile;
    }


}
