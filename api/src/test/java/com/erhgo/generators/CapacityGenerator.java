package com.erhgo.generators;

import com.erhgo.domain.referential.Capacity;
import com.erhgo.repositories.CapacityRepository;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
@Service
public class CapacityGenerator {

    private static int index = 0;

    @Autowired
    private CapacityRepository capacityRepository;

    public static Capacity buildCapacity() {
        var capacity = Capacity.builder().build();
        capacity.setTitle("Dummy capacity" + index);
        capacity.setCode("CA2-000" + index);
        capacity.setId((long) index);
        ++index;
        return capacity;
    }

    public static Capacity buildCapacityWithInducedCapacity(Capacity inducedCapacity) {
        var capacity = buildCapacity();
        capacity.setInducedCapacities(Sets.newHashSet(inducedCapacity));
        return capacity;
    }

    public Capacity createCapacity(String code, String title, String description, Capacity... attachedCapacities) {
        final var capacity = new Capacity();
        capacity.setCode(code);
        capacity.setTitle(title);
        capacity.setDescription(description);
        capacity.setInducedCapacities(new HashSet<>());
        for (var attachedCapacity : attachedCapacities) {
            capacity.getInducedCapacities().add(attachedCapacity);
        }
        return capacityRepository.save(capacity);
    }

    public Capacity createCapacity(String code, Capacity...inducedCapacities) {
        return createCapacity(code, "title  " + code, "description " + code, inducedCapacities);
    }
}
