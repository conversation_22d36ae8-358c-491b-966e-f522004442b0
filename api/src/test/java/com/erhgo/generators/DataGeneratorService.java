package com.erhgo.generators;

import com.erhgo.config.KeycloakMockService;
import com.erhgo.domain.KeycloakUserSummary;
import com.erhgo.domain.candidature.job.CandidatureRecruitmentState;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.*;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.recruitment.RecruitmentProfile;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.domain.userprofile.GeneralInformation;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.experience.UserExperience;
import com.erhgo.repositories.*;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.services.keycloak.UserRepresentation;
import com.github.javafaker.Faker;
import com.google.common.collect.Sets;
import jakarta.persistence.EntityManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtClaimNames;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.*;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Stream;

import static com.erhgo.generators.TestFixtures.P_03;
import static com.erhgo.utils.DateTimeUtils.ZONE_ID;

@Service
public class DataGeneratorService {

    @Autowired
    private RecruitmentCandidatureRepository recruitmentCandidatureRepository;

    @Autowired
    private RecruitmentRepository recruitmentRepository;

    @Autowired
    private RecruitmentProfileRepository recruitmentProfileRepository;

    @Autowired
    private UserExperienceRepository userExperienceRepository;

    @Autowired
    private JobActivityLabelRepository jobActivityLabelRepository;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private KeycloakMockService keycloakMockService;

    @Deprecated
    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Autowired
    private GeneralInformationRepository generalInformationRepository;

    @Autowired
    private EntityManager entityManager;

    @Autowired
    private ErhgoOccupationRepository erhgoOccupationRepository;

    private Faker faker = new Faker();

    @Transactional
    public RecruitmentCandidature createNotMatchingCandidature(Recruitment recruitment, UserProfile candidate) {
        return createNotMatchingCandidature(recruitment, candidate, null);
    }

    @Transactional
    public RecruitmentCandidature createNotMatchingCandidature(Recruitment recruitment, UserProfile candidate, String customAnswer) {
        var candidature = new RecruitmentCandidature();
        candidature.setRecruitment(recruitment);
        candidature.setUserProfile(candidate);
        candidature = recruitmentCandidatureRepository.save(candidature);
        candidature.setCreatedBy(new KeycloakUserSummary(candidate.userId()));
        candidature.updateCodeOnJobCreate();
        candidature.setCustomAnswer(customAnswer);
        candidature.setSubmissionDate(OffsetDateTime.now(ZONE_ID));
        candidature.setValid(false);
        candidature.setState(CandidatureState.VALIDATED);
        candidature.setGlobalCandidatureState(GlobalCandidatureState.MISSING_PREREQUISITE);
        return candidature;
    }

    @Transactional
    public RecruitmentCandidature createDraftCandidatureWithCapacity(Recruitment recruitment, UserProfile userProfile, Capacity capacity) {

        var candidature = new RecruitmentCandidature();
        candidature.setRecruitment(recruitment);
        candidature.setUserProfile(userProfile);
        candidature = recruitmentCandidatureRepository.save(candidature);
        candidature.setCreatedBy(new KeycloakUserSummary(userProfile.userId()));
        candidature.updateCodeOnJobCreate();
        candidature = recruitmentCandidatureRepository.save(candidature);

        var experience = buildUserExperience(userProfile, capacity);

        userExperienceRepository.save(experience);

        candidature.setCandidatureRecruitmentState(CandidatureRecruitmentState.NEW);
        candidature.setState(CandidatureState.COMPLETED_WITH_FIRST_XP);
        return candidature;
    }

    private UserExperience buildUserExperience(UserProfile userProfile, Capacity capacity) {
        var experience = new UserExperience();
        experience.setUserProfile(userProfile);
        experience.setUuid(UUID.randomUUID());
        experience.setJobTitle("Job Nb 1");
        experience.setOrganizationName("E");
        if (capacity != null) {
            var activityLabel = JobActivityLabelGenerator.buildActivityWithCapacities(capacity);
            activityLabel.setTitle("ActivityLabel for user with capacity " + capacity.getCode());
            activityLabel = jobActivityLabelRepository.save(activityLabel);
            var occupation = new ErhgoOccupationMotherObject()
                    .withActivity(activityLabel, false)
                    .instance();
            erhgoOccupationRepository.save(occupation);
            experience.setErhgoOccupation(occupation);
        }

        experience.setType(ExperienceType.JOB);
        return experience;
    }

    public UserExperience buildExperienceWithContext(UserProfile userProfile) {
        return buildUserExperience(userProfile, null);
    }

    @Transactional
    public Recruitment createRecruitment(RecruitmentState state) {
        return recruitmentRepository.save(buildRecruitment(state, P_03));
    }

    public Recruitment buildRecruitment(RecruitmentState state, RecruitmentProfile recruitmentProfile) {
        var recruitment = Recruitment.builder()
                .managerUserId("42")
                .recruitmentProfile(recruitmentProfile)
                .typeContract(ContractType.CDI)
                .state(state)
                .build();
        recruitment.setCode(faker.random().hex());
        return recruitment;
    }

    public RecruitmentProfile buildEmptyRecruitmentProfile(String organizationCode) {
        return RecruitmentProfile.builder()
                .job(JobGenerator.buildEmptyJob(organizationCode))
                .build();
    }

    @Transactional
    public RecruitmentCandidature createCandidature(Recruitment publishedRecruitment, UserProfile userProfile, CandidatureRecruitmentState candidatureRecruitmentState) {
        return recruitmentCandidatureRepository.save(buildCandidature(entityManager.merge(publishedRecruitment), entityManager.merge(userProfile), candidatureRecruitmentState));
    }

    public RecruitmentCandidature buildCandidature(Recruitment publishedRecruitment, UserProfile userProfile, CandidatureRecruitmentState candidatureRecruitmentState) {
        return RecruitmentCandidature.builder()
                .modifiedByUser(true)
                .valid(candidatureRecruitmentState != null ? true : null)
                .recruitment(publishedRecruitment)
                .state(candidatureRecruitmentState == null ? CandidatureState.STARTED : CandidatureState.VALIDATED)
                .candidatureRecruitmentState(candidatureRecruitmentState)
                .userProfile(userProfile)
                .build();
    }

    @Deprecated
    public UserProfile buildUserProfile() {
        return UserProfileGenerator.buildEmptyUserProfile();
    }

    // Use directly userProfileGenerator.createUserProfile() instead of this method
    @Deprecated
    public UserProfile createUserProfile() {
        return userProfileGenerator.createUserProfile();
    }

    public Authentication authenticateForIdAndRole(String id, String... roles) {
        var jwt = new Jwt("---generated by admin mock---", Instant.now(), null, Map.of("", ""),
                Map.of(
                        JwtClaimNames.SUB, id
                )
        );
        var authorities = Stream.of(roles).map(r -> new SimpleGrantedAuthority("ROLE_" + r)).toList();
        Authentication authentication = new UsernamePasswordAuthenticationToken(jwt, null, authorities);
        SecurityContextHolder.getContext().setAuthentication(authentication);

        return authentication;
    }

    public RecruitmentProfile createRecruitmentProfileWithOptionalActivitiesAndAllContextsOptional(Job job, JobActivityLabel... optionalActivities) {
        RecruitmentProfile profile = RecruitmentProfileGenerator.buildRecruitmentProfile(job, optionalActivities);

        return recruitmentProfileRepository.save(profile);
    }

    public Recruitment createRecruitment(String code, RecruitmentProfile profile) {
        Recruitment recruitment = Recruitment.builder()
                .managerUserId("42")
                .typeContract(ContractType.CDI)
                .recruitmentProfile(profile).state(RecruitmentState.PUBLISHED).build();
        recruitment.setCode(code);
        return recruitmentRepository.save(recruitment);
    }

    public RecruitmentCandidature createCandidatureForRecruitmentAndCapacityMap(Recruitment recruitment, String userId, Map<String, Integer> userLevelPerActivityCode, Map<String, JobActivityLabel> activitiesPerCode, JobActivityLabel inOwnActivity) {

        var userProfile = createUserProfile(userId, UUID.randomUUID(), Sets.newHashSet());
        var location = Location.builder().city("Lille").postcode("59000").citycode("59350").longitude((float) 3.04584).latitude((float) 50.629113).build();
        createGeneralInformation("01-23-45-67-89",
                location,
                ContactTime.ALL_DAY,
                LocalDate.of(1991, Month.MARCH, 6),
                userProfile, null);

        RecruitmentCandidature candidatureTmp = RecruitmentCandidature.builder()
                .recruitment(recruitment)
                .valid(true)
                .userProfile(userProfile)
                .candidatureNotes(Collections.emptyList())
                .state(CandidatureState.VALIDATED)
                .candidatureRecruitmentState(CandidatureRecruitmentState.NEW)
                .submissionDate(OffsetDateTime.of(2019, 5, 5, 5, 5, 0, 0, ZoneOffset.UTC))
                .build();

        candidatureTmp.setCode(userId);
        RecruitmentCandidature candidature = recruitmentCandidatureRepository.save(candidatureTmp);
        return recruitmentCandidatureRepository.save(candidature);
    }

    public UserExperience createExperience(UserExperience userExperience) {
        return userExperienceRepository.save(userExperience);
    }

    public UserProfile createUserProfile(String userId, UUID userProfileUuid, Set<Capacity> userCapacities) {
        var userProfile = new UserProfile().userId(userId).uuid(userProfileUuid).addCapacities(userCapacities);
        userProfile = userProfileRepository.save(userProfile);
        initializeUserInKeycloak(userId);
        return userProfile;
    }

    public GeneralInformation createGeneralInformation(String phoneNumber,
                                                       Location location,
                                                       ContactTime contactTime,
                                                       LocalDate birthDate,
                                                       UserProfile userProfile, Object o) {
        var generalInformation = GeneralInformation.builder()
                .phoneNumber(phoneNumber)
                .location(location)
                .userId(userProfile.userId())
                .userProfile(userProfile)
                .contactTime(contactTime)
                .birthDate(birthDate)
                .build();

        return generalInformationRepository.save(generalInformation);
    }

    public void initializeUserInKeycloak(String userId) {
        var userRepresentation = new UserRepresentation();
        userRepresentation.setEmail(userId + "@erhgo.fr");
        userRepresentation.setFirstName("Jean-" + userId);
        userRepresentation.setLastName("Dupont Du " + userId);
        userRepresentation.setId(userId);

        keycloakMockService.setUserProfile(userId, userRepresentation);
    }
}
