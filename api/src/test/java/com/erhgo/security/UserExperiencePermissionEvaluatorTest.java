package com.erhgo.security;

import com.erhgo.generators.DataGeneratorService;
import com.erhgo.security.evaluator.UserExperiencePermissionEvaluator;
import com.erhgo.security.evaluator.UserProfilePermissionEvaluator;
import com.erhgo.services.SecurityService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.Optional;

public class UserExperiencePermissionEvaluatorTest {

    private static DataGeneratorService dataGeneratorService = new DataGeneratorService();

    private UserProfilePermissionEvaluator userProfilePermissionEvaluator = Mockito.mock(UserProfilePermissionEvaluator.class);
    private UserExperiencePermissionEvaluator userExperiencePermissionEvaluator = new UserExperiencePermissionEvaluator(new SecurityService(null, null, Optional.empty()), userProfilePermissionEvaluator);

    @Test
    public void should_delegate_to_user_pprofile_permission_evaluator() {
        var userProfile = dataGeneratorService.buildUserProfile();
        var userExperience = dataGeneratorService.buildExperienceWithContext(userProfile);
        var authentication = dataGeneratorService.authenticateForIdAndRole("me", Role.CANDIDATE);

        Assertions.assertFalse(userExperiencePermissionEvaluator.hasPermission(authentication, userExperience, PermissionLevel.READ));
        Mockito.verify(userProfilePermissionEvaluator).hasPermission(authentication, userProfile, PermissionLevel.READ);
    }

}
