package com.erhgo.security;

import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.generators.DataGeneratorService;
import com.erhgo.security.evaluator.RecruitmentPermissionEvaluator;
import com.erhgo.services.SecurityService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.Optional;

public class RecruitmentPermissionEvaluatorTest {

    private static DataGeneratorService dataGeneratorService = new DataGeneratorService();

    private RecruitmentPermissionEvaluator recruitmentPermissionEvaluator = new RecruitmentPermissionEvaluator(new SecurityService(null, null, Optional.empty()));

    private static final String CODE = "E-042";

    @Test
    public void should_not_permit_user_not_in_organization() {
        var recruitment = dataGeneratorService.buildRecruitment(RecruitmentState.PUBLISHED, dataGeneratorService.buildEmptyRecruitmentProfile(CODE));
        var authentication = dataGeneratorService.authenticateForIdAndRole("not me", Role.ODAS_ADMIN);
        Assertions.assertFalse(recruitmentPermissionEvaluator.hasPermission(authentication, recruitment, PermissionLevel.READ));
    }

    @Test
    public void should_not_permit_user_without_RH_or_external_consultant_role() {
        var recruitment = dataGeneratorService.buildRecruitment(RecruitmentState.PUBLISHED, dataGeneratorService.buildEmptyRecruitmentProfile(CODE));
        var authentication = dataGeneratorService.authenticateForIdAndRole("not me", CODE);
        Assertions.assertFalse(recruitmentPermissionEvaluator.hasPermission(authentication, recruitment, PermissionLevel.READ));
    }

}
