package com.erhgo.security;

import com.erhgo.domain.utils.EventPublisherUtils;
import com.erhgo.generators.DataGeneratorService;
import com.erhgo.generators.UserProfileGenerator;
import com.erhgo.security.evaluator.UserProfilePermissionEvaluator;
import com.erhgo.services.SecurityService;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class UserProfilePermissionEvaluatorTest {

    private static DataGeneratorService dataGeneratorService = new DataGeneratorService();

    private UserProfilePermissionEvaluator userProfilePermissionEvaluator = new UserProfilePermissionEvaluator(new SecurityService(null, null, Optional.empty()));

    @BeforeAll
    static void setUp() {
        EventPublisherUtils.resetPublisher(null);
    }

    @Test
    void should_not_permit_user_with_different_id() {
        var userProfile = dataGeneratorService.buildUserProfile();

        var authentication = dataGeneratorService.authenticateForIdAndRole("not me", Role.CANDIDATE);

        assertFalse(userProfilePermissionEvaluator.hasPermission(authentication, userProfile, PermissionLevel.READ));
    }

    @Test
    void should_permit_user_with_same_id() {
        var userProfile = dataGeneratorService.buildUserProfile();

        var authentication = dataGeneratorService.authenticateForIdAndRole(userProfile.userId(), Role.CANDIDATE);

        assertTrue(userProfilePermissionEvaluator.hasPermission(authentication, userProfile, PermissionLevel.READ));
    }

    @Test
    void should_not_permit_bo_user_without_channel_as_role() {
        var channel = "P-550";
        var userProfile = UserProfileGenerator.buildEmptyUserProfile(channel, "E-042");

        var authentication = dataGeneratorService.authenticateForIdAndRole("admin_user", Role.ODAS_ADMIN, "P-055");

        assertFalse(userProfilePermissionEvaluator.hasPermission(authentication, userProfile, PermissionLevel.READ));
    }
}
