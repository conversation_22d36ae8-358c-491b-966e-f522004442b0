package com.erhgo.security;

import com.erhgo.domain.candidature.job.CandidatureRecruitmentState;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.generators.DataGeneratorService;
import com.erhgo.security.evaluator.RecruitmentCandidaturePermissionEvaluator;
import com.erhgo.services.SecurityService;
import org.junit.jupiter.api.Test;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class CandidaturePermissionEvaluatorTest {

    private static DataGeneratorService dataGeneratorService = new DataGeneratorService();

    private RecruitmentCandidaturePermissionEvaluator candidaturePermissionEvaluator = new RecruitmentCandidaturePermissionEvaluator(new SecurityService(null, null, Optional.empty()));

    @Test
    void should_not_permit_not_owner_of_candidature() {
        var candidature = buildCandidature();
        var authentication = dataGeneratorService.authenticateForIdAndRole("not me", Role.CANDIDATE);

        assertFalse(candidaturePermissionEvaluator.hasPermission(authentication, candidature, PermissionLevel.READ));
    }

    @Test
    void should_permit_owner_of_candidature() {
        var candidature = buildCandidature();
        var authentication = dataGeneratorService.authenticateForIdAndRole(candidature.getUserProfile().userId(), Role.CANDIDATE);

        assertTrue(candidaturePermissionEvaluator.hasPermission(authentication, candidature, PermissionLevel.WRITE));
    }

    private RecruitmentCandidature buildCandidature() {
        var userProfile = dataGeneratorService.buildUserProfile();
        var recruitment = dataGeneratorService.buildRecruitment(RecruitmentState.PUBLISHED, dataGeneratorService.buildEmptyRecruitmentProfile("E"));

        return dataGeneratorService.buildCandidature(recruitment, userProfile, CandidatureRecruitmentState.NEW);
    }


}
