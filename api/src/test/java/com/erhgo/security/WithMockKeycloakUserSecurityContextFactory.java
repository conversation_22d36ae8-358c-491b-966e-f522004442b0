package com.erhgo.security;

import com.erhgo.TestUtils;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.test.context.support.WithSecurityContextFactory;

public class WithMockKeycloakUserSecurityContextFactory implements WithSecurityContextFactory<WithMockKeycloakUser> {
    @Override
    public SecurityContext createSecurityContext(WithMockKeycloakUser withKeycloakUser) {

        return TestUtils.getSecurityContext(withKeycloakUser.roles(), withKeycloakUser.username(), withKeycloakUser.id());
    }

}
