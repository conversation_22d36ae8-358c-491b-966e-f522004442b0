package com.erhgo.security;

import com.erhgo.generators.DataGeneratorService;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.security.evaluator.RecruiterPermissionEvaluator;
import com.erhgo.services.SecurityService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.Optional;

class RecruiterPermissionEvaluatorTest {

    private static DataGeneratorService dataGeneratorService = new DataGeneratorService();

    private RecruiterPermissionEvaluator recruiterPermissionEvaluator = new RecruiterPermissionEvaluator(new SecurityService(null, null, Optional.empty()));

    @Test
    void should_not_permit_user_without_organization_code_as_role() {
        var organization = OrganizationGenerator.buildRecruiter();
        var authentication = dataGeneratorService.authenticateForIdAndRole("not me", Role.SOURCING);
        Assertions.assertFalse(recruiterPermissionEvaluator.hasPermission(authentication, organization, PermissionLevel.READ));
    }

    @Test
    void should_permit_user_with_organization_code_as_role() {
        var organization = OrganizationGenerator.buildRecruiter();
        var authentication = dataGeneratorService.authenticateForIdAndRole("me!", organization.getCode(), Role.SOURCING);
        Assertions.assertTrue(recruiterPermissionEvaluator.hasPermission(authentication, organization, PermissionLevel.READ));
    }
}
