package com.erhgo.security;

import com.erhgo.domain.userprofile.UserNotificationMotherObject;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.generators.DataGeneratorService;
import com.erhgo.security.evaluator.UserNotificationPermissionEvaluator;
import com.erhgo.services.SecurityService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.Optional;

class UserNotificationPermissionEvaluatorTest {
    private static final DataGeneratorService dataGeneratorService = new DataGeneratorService();
    private final UserNotificationPermissionEvaluator userNotificationPermissionEvaluator = new UserNotificationPermissionEvaluator(new SecurityService(null, null, Optional.empty()));

    private static final String USER_ID = "aa766d1a-7ae8-4376-acd9-54afb22c2409";

    @Test
    void should_not_permit_other_user() {
        var userProfile = new UserProfileMotherObject()
                .withUserId(USER_ID)
                .build();

        var notification = new UserNotificationMotherObject()
                .buildAbstractNotification(userProfile);

        var authentication = dataGeneratorService.authenticateForIdAndRole("not me", Role.CANDIDATE);
        Assertions.assertFalse(userNotificationPermissionEvaluator.hasPermission(authentication, notification, PermissionLevel.READ));
    }
}
