package com.erhgo.migrations.changes;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.BehaviorCategory;
import com.erhgo.generators.ErhgoOccupationGenerator;
import com.erhgo.generators.EscoOccupationGenerator;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import jakarta.persistence.EntityManager;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.transaction.support.TransactionTemplate;

import static org.assertj.core.api.Assertions.assertThat;

public class ImportErhgoOccupationsCategoriesTest extends AbstractIntegrationTest {

    private ImportErhgoOccupationsCategories importErhgoOccupationsCategoriesService;

    @Autowired
    private EscoOccupationGenerator escoOccupationGenerator;

    @Autowired
    private ErhgoOccupationGenerator erhgoOccupationGenerator;

    @Autowired
    private ErhgoOccupationRepository erhgoOccupationRepository;

    @Autowired
    private EntityManager entityManager;

    @Value("classpath:migration/erhgo_categories.csv")
    private Resource csvFile;

    @Autowired
    private TransactionTemplate transactionTemplate;

    private static final String uri1 = "http://data.europa.eu/esco/occupation/1b4e795d-6e49-4b7b-bb34-585edfd6eb18";
    private static final String uri2 = "http://data.europa.eu/esco/occupation/fe4a72af-6206-4d86-90c1-b5fb4b5ba2b2";
    private static final String uri3 = "http://data.europa.eu/esco/occupation/4552095e-3396-4831-86ad-4fc34ec39a19";
    private static final String uri4 = "http://data.europa.eu/esco/occupation/11695786-9fb7-4895-b161-0480419107e4";

    @Before
    public void setup() {
        this.importErhgoOccupationsCategoriesService = new ImportErhgoOccupationsCategories(entityManager, transactionTemplate, csvFile);
    }

    @Test
    public void updateCategories() {

        var isco = escoOccupationGenerator.createIscoOccupation(42, "test isco");

        var esco1 = escoOccupationGenerator.createEscoOccupationForUriIscoGroupAndSkills(uri1, isco, null); //expecting Rigor & Constancy
        var esco2 = escoOccupationGenerator.createEscoOccupationForUriIscoGroupAndSkills(uri2, isco, null); //expecting Rigor & Constancy
        var esco3 = escoOccupationGenerator.createEscoOccupationForUriIscoGroupAndSkills(uri3, isco, null); //expecting Pragmatism & Tenacity
        var esco4 = escoOccupationGenerator.createEscoOccupationForUriIscoGroupAndSkills(uri4, isco, null); //expecting Sociability & Constancy

        var erhgoUnmodified = erhgoOccupationGenerator.createErhgoOccupation(new ErhgoOccupationMotherObject()
                .withBehaviorCategory1(BehaviorCategory.RIGOR, false)
                .withBehaviorCategory2(BehaviorCategory.CONSTANCY, false)
                .withEscoOccupationCascadingSkills(esco1)
                .instance());

        var erhgoModifiedCat1 = erhgoOccupationGenerator.createErhgoOccupation(new ErhgoOccupationMotherObject()
                .withBehaviorCategory1(BehaviorCategory.HONESTY, false)
                .withBehaviorCategory2(BehaviorCategory.CONSTANCY, false)
                .withEscoOccupationCascadingSkills(esco2)
                .instance());

        var erhgoModifiedCat2 = erhgoOccupationGenerator.createErhgoOccupation(new ErhgoOccupationMotherObject()
                .withBehaviorCategory1(BehaviorCategory.PRAGMATISM, false)
                .withBehaviorCategory2(BehaviorCategory.CONSTANCY, false)
                .withEscoOccupationCascadingSkills(esco3)
                .instance());


        var erhgoModifiedBothCat = erhgoOccupationGenerator.createErhgoOccupation(new ErhgoOccupationMotherObject()
                .withBehaviorCategory1(BehaviorCategory.HONESTY, false)
                .withBehaviorCategory2(BehaviorCategory.RIGOR, false)
                .withEscoOccupationCascadingSkills(esco4)
                .instance());


        importErhgoOccupationsCategoriesService.executeUpdate();
        assertThat(importErhgoOccupationsCategoriesService.erroneousJobs).hasSize(329);
        assertThat(importErhgoOccupationsCategoriesService.category1UpdatedJobs).containsExactlyInAnyOrder(erhgoModifiedCat1.getId().toString(), erhgoModifiedBothCat.getId().toString());
        assertThat(importErhgoOccupationsCategoriesService.category2UpdatedJobs).containsExactlyInAnyOrder(erhgoModifiedCat2.getId().toString(), erhgoModifiedBothCat.getId().toString());
        assertThat(importErhgoOccupationsCategoriesService.notModifiedJobs).containsExactlyInAnyOrder(erhgoUnmodified.getId().toString());
        txHelper.doInTransaction(() -> {
            assertThat(erhgoOccupationRepository.getOne(erhgoUnmodified.getId())).matches(e ->
                    e.getBehaviorCategory1() == BehaviorCategory.RIGOR &&
                            e.getBehaviorCategory2() == BehaviorCategory.CONSTANCY &&
                            !e.isBehaviorCategory1Overloaded() &&
                            !e.isBehaviorCategory2Overloaded()
            );
            assertThat(erhgoOccupationRepository.getOne(erhgoModifiedCat1.getId())).matches(e ->
                    e.getBehaviorCategory1() == BehaviorCategory.RIGOR &&
                            e.getBehaviorCategory2() == BehaviorCategory.CONSTANCY &&
                            e.isBehaviorCategory1Overloaded() &&
                            !e.isBehaviorCategory2Overloaded()
            );
            assertThat(erhgoOccupationRepository.getOne(erhgoModifiedCat2.getId())).matches(e ->
                    e.getBehaviorCategory1() == BehaviorCategory.PRAGMATISM &&
                            e.getBehaviorCategory2() == BehaviorCategory.TENACITY &&
                            !e.isBehaviorCategory1Overloaded() &&
                            e.isBehaviorCategory2Overloaded()
            );
            assertThat(erhgoOccupationRepository.getOne(erhgoModifiedBothCat.getId())).matches(e ->
                    e.getBehaviorCategory1() == BehaviorCategory.SOCIABILITY &&
                            e.getBehaviorCategory2() == BehaviorCategory.CONSTANCY &&
                            e.isBehaviorCategory1Overloaded() &&
                            e.isBehaviorCategory2Overloaded()
            );
        });
    }

}
