package com.erhgo;

import com.erhgo.config.ResetDatabaseTestExecutionListener;
import com.erhgo.config.SimpleTestSecurityConfig;
import com.erhgo.generators.DataGeneratorService;
import com.erhgo.generators.TestFixtures;
import com.erhgo.services.keycloak.SourcingKeycloakService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrint;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@ContextConfiguration(classes = {ErhgoTestApplication.class, SimpleTestSecurityConfig.class})
@AutoConfigureMockMvc(print = MockMvcPrint.LOG_DEBUG)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, /*we prefer properties instead of `@ActiveProfile` to ensure Liquibase's contexts are well initialized*/ properties = {"spring.profiles.active=test"})
@TestExecutionListeners(
        mergeMode = TestExecutionListeners.MergeMode.MERGE_WITH_DEFAULTS,
        listeners = {ResetDatabaseTestExecutionListener.class}
)
public abstract class AbstractIntegrationTestTimeDependant {

    @Autowired
    protected TransactionTestHelper txHelper;

    @Autowired
    protected ObjectMapper objectMapper;

    @Autowired
    protected MockMvc mvc;

    @Autowired
    private DataGeneratorService dataGeneratorService;

    @MockitoBean
    protected SourcingKeycloakService sourcingKeycloakService;

    private static final String BASE_URL = "/api/odas";

    // Ensure minimal information about default admin user are known by KeycloakService
    @Before
    public void setup() {
        dataGeneratorService.initializeUserInKeycloak(TestFixtures.ADMIN_USER_ID);
    }

    protected String realUrl(String url) {
        return url.startsWith(BASE_URL) ? url : (BASE_URL + url);
    }

    @SneakyThrows
    private ResultActions performAction(MockHttpServletRequestBuilder builder, Object object, Map<String, List<String>> params) {
        builder = builder.params(CollectionUtils.toMultiValueMap(params));
        if (object != null) {
            builder = builder.contentType(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(object));
        }
        return mvc.perform(builder);
    }

    @SneakyThrows
    public ResultActions performPut(String url, Object object) {
        return performPut(url, object, new HashMap<>());
    }

    @SneakyThrows
    public ResultActions performPut(String url, Object object, Map<String, List<String>> params) {
        var builder = put(realUrl(url)).contentType(MediaType.APPLICATION_JSON);
        return performAction(builder, object, params);
    }


    @SneakyThrows
    public ResultActions performPost(String url, Object command) {
        return performPost(url, command, new HashMap<>());
    }

    @SneakyThrows
    public ResultActions performPost(String url, Object object, Map<String, List<String>> params) {
        var builder = post(realUrl(url)).contentType(MediaType.APPLICATION_JSON);
        return performAction(builder, object, params);
    }

    @SneakyThrows
    public ResultActions performDelete(String url, Object object) {
        return performDelete(url, object, new HashMap<>());
    }

    @SneakyThrows
    public ResultActions performDelete(String url, Object object, Map<String, List<String>> params) {
        var builder = delete(realUrl(url));
        return performAction(builder, object, params);
    }

    @SneakyThrows
    public ResultActions performPatch(String url, Object object) {
        return performPatch(url, object, new HashMap<>());
    }

    @SneakyThrows
    public ResultActions performPatch(String url, Object object, Map<String, List<String>> params) {
        var builder = patch(realUrl(url)).contentType(MediaType.APPLICATION_JSON);
        return performAction(builder, object, params);
    }

    @SneakyThrows
    public ResultActions performGetAndExpect(String url, String expectedJsonFile, boolean strictJsonCheck) {
        return performGetAndExpect(url, expectedJsonFile, strictJsonCheck, null, null);
    }

    @SneakyThrows
    public ResultActions performGetAndExpect(String url,
                                             String expectedJsonFile,
                                             boolean strictJsonCheck,
                                             Function<MockHttpServletRequestBuilder, MockHttpServletRequestBuilder> requestModifier,
                                             Runnable preCheck
    ) {
        var request = get(realUrl(url));
        if (requestModifier != null) {
            request = requestModifier.apply(request);
        }
        var resultActions = mvc.perform(request)
                .andExpect(status().isOk());
        if (preCheck != null) {
            preCheck.run();
        }
        return resultActions
                .andExpect(strictJsonCheck ? TestUtils.jsonMatchesContentWithOrderedArray(expectedJsonFile) : TestUtils.jsonMatchesContent(expectedJsonFile));
    }
}
