package com.erhgo;

import com.erhgo.services.trimoji.TrimojiPDFAdapter;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;

import java.io.ByteArrayInputStream;

class TrimojiPDFAdapterTest {

    @SneakyThrows
    @Test
    void ensureTrimojiIsOK() {
        try (var initial = TestUtils.class.getResourceAsStream("/data/trimoji_src.pdf")) {
            var actual = TrimojiPDFAdapter.processTrimojiPDF(
                    new ByteArrayInputStream(initial.readAllBytes()),
                    "<PERSON><PERSON><PERSON><PERSON>",
                    "Dupont de la Garçonnière des Lilas"
            );
            TestUtils.assertThatPdfMatchesContent("trimoji_updated.pdf", actual.toByteArray());
        }
    }
}
