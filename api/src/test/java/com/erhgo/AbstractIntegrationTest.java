package com.erhgo;

import com.erhgo.services.search.GeoService;
import com.erhgo.utils.DateTimeUtils;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.auditing.AuditingHandler;
import org.springframework.data.auditing.DateTimeProvider;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.time.LocalDateTime;
import java.util.Optional;

public abstract class AbstractIntegrationTest extends AbstractIntegrationTestTimeDependant {

    @Autowired
    private AuditingHandler auditingHandler;

    @Mock
    DateTimeProvider dateTimeProvider;

    @Autowired
    public ApplicationContext applicationContext;

    @MockBean
    public GeoService geoService;

    @BeforeEach
    void resetTimeProvider() {
        Mockito.when(dateTimeProvider.getNow()).thenReturn(Optional.of(LocalDateTime.of(2020, 2, 2, 0, 0, 0)));
        auditingHandler.setDateTimeProvider(dateTimeProvider);
        DateTimeUtils.ZONE = "UTC";
        DateTimeUtils.refreshZoneId();
    }

    <A> A getBean(Class<A> clazz) {
        return applicationContext.getBean(clazz);
    }

}
