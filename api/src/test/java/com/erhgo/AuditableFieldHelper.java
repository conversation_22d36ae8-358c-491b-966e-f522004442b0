package com.erhgo;

import com.erhgo.domain.AbstractAuditableEntity;
import jakarta.persistence.EntityManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
public class AuditableFieldHelper {

    @Autowired
    JdbcTemplate jdbcTemplate;
    @Autowired
    EntityManager entityManager;

    @Transactional
    public void updateCreatedDate(AbstractAuditableEntity entity, Date date) {
        entityManager.flush();
        Object id;
        var idField = "id";
        try {
            id = ReflectionTestUtils.getField(entity, "id");
        } catch (IllegalArgumentException e) {
            id = ReflectionTestUtils.getField(entity, "uuid");
            idField = "BinToUuid(uuid)";
        }
        jdbcTemplate.update("UPDATE %s SET createdDate=? WHERE %s=?".formatted(entity.getClass().getSimpleName(), idField), date, id.toString());

    }
}
