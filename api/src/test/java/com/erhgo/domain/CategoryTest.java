package com.erhgo.domain;

import com.erhgo.domain.referential.Category;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import static org.assertj.core.api.Assertions.assertThat;

@ActiveProfiles("test")
class CategoryTest {

    @Test
    void getPrefix_shouldReturnCCT() {
        // given
        var category = new Category();

        // when
        final var prefix = category.getPrefix();

        // then
        assertThat(prefix).isEqualTo("CCT");
    }

    @Test
    void getSuffixLeftPadCount_shouldReturn3() {
        // given
        var category = new Category();

        // when
        final var leftPadCount = category.getSuffixLeftPadCount();

        // then
        assertThat(leftPadCount).isEqualTo(2);
    }
}
