package com.erhgo.domain;

import com.erhgo.domain.referential.Capacity;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import static org.assertj.core.api.Assertions.assertThat;

@ActiveProfiles("test")
class CapacityTest {

    @Test
    void getPrefix_shouldReturnCA() {
        // given
        final var capacity = new Capacity();

        // when
        final var prefix = capacity.getPrefix();

        // then
        assertThat(prefix).isEqualTo("CA");
    }

    @Test
    void getSuffixFixLeftPadCount_shouldReturn3() {
        // given
        final var capacity = new Capacity();

        // when
        final var leftPadCount = capacity.getSuffixLeftPadCount();

        // then
        assertThat(leftPadCount).isEqualTo(3);
    }
}
