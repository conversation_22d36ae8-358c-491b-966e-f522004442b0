package com.erhgo.domain;

import com.erhgo.domain.job.ContextsForCategory;
import com.erhgo.domain.job.Mission;
import com.erhgo.domain.referential.Context;
import com.erhgo.generators.JobGenerator;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class MissionTest {

    @Test
    void getLevel_shouldReturn1WhenNoContextDefine() {

        Mission mission = Mission.builder().build();

        assertThat(mission.computeLevel()).isEqualTo(1);
    }

    @Test
    void getLevel_shouldReturn1WhenNoContextForAllCategories() {

        ContextsForCategory contextsForCategory = new ContextsForCategory();
        contextsForCategory.setNoContextForCategory(true);

        Mission mission = Mission.builder().contextsForCategory(Sets.newHashSet(contextsForCategory)).build();

        assertThat(mission.computeLevel()).isEqualTo(1);
    }

    @Test
    void getLevel_shouldReturn2WhenOneContextOfScore22AndNoOtherContext() {

        int score = 22;
        Context context = JobGenerator.buildContextWithScore(score);

        ContextsForCategory contextsForCategory = new ContextsForCategory();
        contextsForCategory.setContexts(Sets.newHashSet(context));

        Mission mission = Mission.builder().contextsForCategory(Sets.newHashSet(contextsForCategory)).build();

        assertThat(mission.computeLevel()).isEqualTo(2);
    }

    @Test
    void getLevel_shouldReturn5WhenOneContextOfScore91AndNoOtherContext() {

        int score = 91;
        Context context = JobGenerator.buildContextWithScore(score);

        ContextsForCategory contextsForCategory = new ContextsForCategory();
        contextsForCategory.setContexts(Sets.newHashSet(context));

        Mission mission = Mission.builder().contextsForCategory(Sets.newHashSet(contextsForCategory)).build();

        assertThat(mission.computeLevel()).isEqualTo(5);
    }

    @Test
    void getLevel_shouldReturn3WhenOneContextOfLevel20AndOneContextOfLevel30() {

        Context context2 = JobGenerator.buildContextWithScore(20);
        Context context3 = JobGenerator.buildContextWithScore(30);

        ContextsForCategory contextsForCategory1 = new ContextsForCategory();
        contextsForCategory1.setContexts(Sets.newHashSet(context2));

        ContextsForCategory contextsForCategory2 = new ContextsForCategory();
        contextsForCategory2.setContexts(Sets.newHashSet(context3));

        Mission mission = Mission.builder().contextsForCategory(Sets.newHashSet(contextsForCategory1, contextsForCategory2)).build();

        assertThat(mission.computeLevel()).isEqualTo(3);
    }

    @Test
    void getLevel_shouldReturn4WhenTwoContextsOfScore36AndOneContextOfScore21() {

        Context context2 = JobGenerator.buildContextWithScore(21);
        Context context31 = JobGenerator.buildContextWithScore(36);
        Context context32 = JobGenerator.buildContextWithScore(36);

        ContextsForCategory contextsForCategory1 = new ContextsForCategory();
        contextsForCategory1.setContexts(Sets.newHashSet(context2, context31));

        ContextsForCategory contextsForCategory2 = new ContextsForCategory();
        contextsForCategory2.setContexts(Sets.newHashSet(context32));

        ContextsForCategory emptyContextsForCategory = new ContextsForCategory();
        emptyContextsForCategory.setNoContextForCategory(true);

        Mission mission = Mission.builder().contextsForCategory(Sets.newHashSet(contextsForCategory1, contextsForCategory2, emptyContextsForCategory)).build();

        assertThat(mission.computeLevel()).isEqualTo(4);
    }

}
