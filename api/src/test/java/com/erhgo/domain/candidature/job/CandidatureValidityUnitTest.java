package com.erhgo.domain.candidature.job;

import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.referential.Activity;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.domain.userprofile.UserProfile;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.UUID;

class CandidatureValidityUnitTest {

    @Test
    void changeCandidatureValidityDoesNotChangeHashcode() {
        var candidature = new RecruitmentCandidatureMotherObject()
                .withActivity(new JobActivityLabel(UUID.randomUUID(), Activity.builder().uuid(UUID.randomUUID()).build(), "o", 0))
                .withRecruitment(new RecruitmentMotherObject().build())
                .withUserProfile(new UserProfile().uuid(UUID.randomUUID()))
                .withValid(true)
                .build();

        var expectedHashcode = candidature.hashCode();

        candidature.setValid(!candidature.getValid());

        Assertions.assertThat(candidature.hashCode()).isEqualTo(expectedHashcode);

    }

}
