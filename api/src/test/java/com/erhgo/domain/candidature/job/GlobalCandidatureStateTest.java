package com.erhgo.domain.candidature.job;

import com.erhgo.openapi.dto.CandidatureStateDTO;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.RepeatedTest;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import static org.junit.jupiter.api.Assertions.*;

class GlobalCandidatureStateTest {

    @ParameterizedTest
    @EnumSource(GlobalCandidatureState.class)
    void ensureAllValuesExistsInDTO(GlobalCandidatureState val) {
        Assertions.assertThat(CandidatureStateDTO.valueOf(val.name())).isNotNull();
    }

}
