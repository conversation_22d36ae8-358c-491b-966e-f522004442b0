package com.erhgo.domain.dummy;

import com.erhgo.domain.AbstractEntity;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.voodoodyne.jackson.jsog.JSOGGenerator;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.test.context.ActiveProfiles;

@Data
@NoArgsConstructor
@JsonIdentityInfo(generator = JSOGGenerator.class)
@EqualsAndHashCode
@ActiveProfiles("test")
@Entity
public class DummyEntity extends AbstractEntity {

    @Override
    public String getPrefix() {
        return "D";
    }

    @Override
    public int getSuffixLeftPadCount() {
        return 2;
    }

    @Override
    public void updateCodeOnEntityCreate() {

    }
}
