package com.erhgo.domain.userprofile;

import com.erhgo.TransactionTestHelper;
import com.erhgo.domain.enums.NotificationState;
import com.erhgo.domain.enums.NotificationType;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.userprofile.notification.AbstractNotification;
import com.erhgo.domain.userprofile.notification.DefaultNotification;
import com.erhgo.domain.userprofile.notification.RecruitmentNotification;
import com.erhgo.domain.userprofile.notification.SuspendedRecruitmentNotification;
import com.erhgo.repositories.NotificationRepository;
import jakarta.persistence.EntityManager;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Scope;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.annotation.Transactional;

import java.net.URI;
import java.time.OffsetDateTime;
import java.util.concurrent.atomic.AtomicReference;

@Service
@Scope("prototype")
@NoArgsConstructor
public class UserNotificationMotherObject {
    @Autowired
    private NotificationRepository notificationRepository;
    @Autowired
    private TransactionTestHelper txHelper;
    @Autowired
    private EntityManager entityManager;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private ApplicationContext applicationContext;

    private RecruitmentNotification recruitmentNotification;

    private Recruitment recruitment;
    private UserProfile userProfile;
    private NotificationState notificationState = NotificationState.NEW;
    private NotificationType notificationType = NotificationType.BOTH;

    private String link;
    private OffsetDateTime createdDate;
    private String subject, content;
    private Boolean requiresMailSending;

    public AbstractNotification buildAndPersist() {
        var atomicNotification = new AtomicReference<AbstractNotification>();
        txHelper.doInTransaction(() -> {
            if (this.userProfile == null) {
                this.userProfile = applicationContext.getBean(UserProfileMotherObject.class).buildAndPersist();
            }
            var notification = link == null ? buildRecruitmentNotification() : buildDefaultNotification();
            var persistedNotification = notificationRepository.save(notification);
            atomicNotification.set(persistedNotification);
        });
        if (this.createdDate != null) {
            txHelper.doInTransaction(() -> {
                jdbcTemplate.update("UPDATE Notification SET createdDate = ? WHERE binToUuid(id) = ?", this.createdDate, atomicNotification.get().getId().toString());
            });
        }

        return atomicNotification.get();
    }

    private DefaultNotification buildDefaultNotification() {
        return DefaultNotification.builder()
                .uri(URI.create(link))
                .userProfile(entityManager.find(UserProfile.class, userProfile.uuid()))
                .subject(subject == null ? "sujet par défaut" : subject)
                .content(content == null ? "contenu par défaut" : content)
                .type(this.notificationType)
                .build();
    }

    public RecruitmentNotification buildRecruitmentNotification() {
        var notif = RecruitmentNotification.builder()
                .recruitment(entityManager.find(Recruitment.class, recruitment.getId()))
                .userProfile(entityManager.find(UserProfile.class, userProfile.uuid()))
                .state(this.notificationState)
                .type(this.notificationType)
                .build();
        if (requiresMailSending != null) {
            ReflectionTestUtils.setField(notif, "requiresMailSending", requiresMailSending);
        }
        return notif;
    }

    @Transactional
    public SuspendedRecruitmentNotification buildAndPersistSuspendedRecruitmentNotification() {
        var notif = SuspendedRecruitmentNotification.builder()
                .recruitment(entityManager.find(Recruitment.class, recruitment.getId()))
                .userProfile(entityManager.find(UserProfile.class, userProfile.uuid()))
                .state(this.notificationState)
                .type(this.notificationType)
                .build();

        return notificationRepository.save(notif);
    }

    public AbstractNotification buildAbstractNotification(UserProfile userProfile) {
        this.recruitmentNotification = RecruitmentNotification.builder()
                .userProfile(userProfile)
                .state(NotificationState.NEW)
                .build();
        return this.recruitmentNotification;
    }

    public UserNotificationMotherObject withRecruitment(Recruitment recruitment) {
        this.recruitment = recruitment;
        return this;
    }

    public UserNotificationMotherObject withUserProfile(UserProfile userProfile) {
        this.userProfile = userProfile;
        return this;
    }

    public UserNotificationMotherObject withState(NotificationState state) {
        this.notificationState = state;
        return this;
    }

    public UserNotificationMotherObject withNotificationType(NotificationType notificationType) {
        this.notificationType = notificationType;
        return this;
    }

    public UserNotificationMotherObject withLink(String url) {
        this.link = url;
        return this;
    }

    public UserNotificationMotherObject withCreatedDate(OffsetDateTime createdDate) {
        this.createdDate = createdDate;
        return this;
    }

    public UserNotificationMotherObject withSubjectAndContent(String subject, String content) {
        this.subject = subject;
        this.content = content;
        return this;
    }

    public UserNotificationMotherObject withRequiresMailSending(boolean requiresMailSending) {
        this.requiresMailSending = requiresMailSending;
        return this;
    }
}
