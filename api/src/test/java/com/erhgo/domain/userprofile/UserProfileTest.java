package com.erhgo.domain.userprofile;

import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.dto.event.UserAffectedToChannelsEvent;
import com.erhgo.domain.enums.CriteriaQuestionType;
import com.erhgo.domain.enums.ExperienceType;
import com.erhgo.domain.exceptions.ThresholdValueCanNotBeUnselected;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.userprofile.criteria.UserCriteriaValue;
import com.erhgo.domain.userprofile.experience.UserExperience;
import com.erhgo.domain.utils.EventPublisherUtils;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.CriteriaMotherObject;
import com.erhgo.generators.UserProfileGenerator;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.*;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mockito;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.within;

class UserProfileTest {

    private static Capacity CA1_01, CA1_02, CA1_03, CA2_01, CA3_01;
    private static Long id = 0L;

    ApplicationEventPublisher eventPublisher;

    @BeforeAll
    static void setupAll() {
        CA1_01 = createNewCapacity("CA1-01", "Regarder avec attention", "foo");
        CA1_02 = createNewCapacity("CA1-02", "Regarder sans attention", "bar");
        CA1_03 = createNewCapacity("CA1-03", "Regarder avec un peu d'attention", "foo foo");
        CA2_01 = createNewCapacity("CA2-01", "Communiquer (des informations)", "foo bar", CA1_01);
        CA3_01 = createNewCapacity("CA3-01", "Communiquer (des informations)", "foo bar", CA1_01);
    }

    @BeforeEach
    void setup() {
        eventPublisher = Mockito.mock(ApplicationEventPublisher.class);
        EventPublisherUtils.resetPublisher(eventPublisher);
    }

    @AfterAll
    static void tearDown() {
        EventPublisherUtils.resetPublisher(null);
    }

    @Test
    void deleteCapacities_should_update_capacity_occurrences_according_to_occurrences() {
        var userProfile = getUserProfileWith2Capacities(CA1_01, CA1_03);

        var toDelete = userProfile.removeCapacities(Lists.newArrayList(CA1_01, CA1_01));

        assertThat(toDelete).isEmpty();
    }

    @Test
    void deleteCapacities_should_update_capacity_occurrences_according_to_level() {
        var userProfile = getUserProfileWith3Capacities(CA3_01, CA2_01, CA1_01);

        var toDelete = userProfile.removeCapacities(Lists.newArrayList(CA3_01));

        assertThat(toDelete).extracting(CapacityOccurrence::getCapacity).containsExactly(CA3_01);
    }

    private UserProfile getUserProfileWith3Capacities(Capacity strong, Capacity weak, Capacity weakest) {
        var userProfile = new UserProfile();
        userProfile.capacityOccurrences(Sets.newHashSet(
                CapacityOccurrence.builder().userProfile(userProfile).capacity(strong).occurrence(1).build(),
                CapacityOccurrence.builder().userProfile(userProfile).capacity(weak).occurrence(1).build(),
                CapacityOccurrence.builder().userProfile(userProfile).capacity(weakest).occurrence(2).build()
        ));
        return userProfile;
    }

    private UserProfile getUserProfileWith2Capacities(Capacity strongest, Capacity weaker) {
        var userProfile = new UserProfile();
        userProfile.capacityOccurrences(Sets.newHashSet(
                CapacityOccurrence.builder().userProfile(userProfile).capacity(strongest).occurrence(3).build(),
                CapacityOccurrence.builder().userProfile(userProfile).capacity(weaker).occurrence(2).build()
        ));
        return userProfile;
    }

    private static Capacity createNewCapacity(String code, String title, String description, Capacity... attachedCapacities) {
        final var capacity = new Capacity();
        capacity.setCode(code);
        capacity.setTitle(title);
        capacity.setDescription(description);
        capacity.setInducedCapacities(new HashSet<>());
        capacity.setId(id++);
        for (var attachedCapacity : attachedCapacities) {
            capacity.getInducedCapacities().add(attachedCapacity);
        }
        return capacity;
    }

    @Test
    void compute_user_level_for_empty_user_should_return_0() {
        var userProfile = new UserProfile();
        assertThat(userProfile.masteryLevel()).isEqualTo(0f);
    }

    @Test
    void compute_user_level_for_single_capacity_with_level_5_should_return_5() {
        var expectedLevel = 5f;

        var userProfile = UserProfileGenerator.buildUserProfileWithCapacitiesAndLevels(Collections.singletonList(CapacityGenerator.buildCapacity()), Collections.singletonList((int) expectedLevel));
        assertThat(userProfile.masteryLevel()).isEqualTo(5f);
    }

    @Test
    void compute_user_level_for_two_capacities_with_levels_5_and_1_should_return_3() {
        var capacities = Arrays.asList(CapacityGenerator.buildCapacity(), CapacityGenerator.buildCapacity());
        var levels = Arrays.asList(5, 1);

        var userProfile = UserProfileGenerator.buildUserProfileWithCapacitiesAndLevels(capacities, levels);
        assertThat(userProfile.masteryLevel()).isEqualTo(3f);
    }

    @Test
    void compute_user_level_for_two_capacities_with_levels_5_2_and_3_should_return_3_dot_33() {
        var ca1 = CapacityGenerator.buildCapacity();
        var capacities = Arrays.asList(ca1, CapacityGenerator.buildCapacity(), ca1);
        var levels = Arrays.asList(5, 2, 3);

        var userProfile = UserProfileGenerator.buildUserProfileWithCapacitiesAndLevels(capacities, levels);
        assertThat(userProfile.masteryLevel()).isCloseTo(3.33f, within(0.01f));
    }

    @Test
    void update_registration_state() {
        var userProfile = new UserProfile();
        assertThat(userProfile.userRegistrationState().getRegistrationStep()).isEqualTo(UserRegistrationState.RegistrationStep.CREATED_ACCOUNT);
        userProfile.updateRegistrationState(UserRegistrationState.RegistrationStep.CONFIRMED_SITUATION);
        assertThat(userProfile.userRegistrationState().getRegistrationStep()).isEqualTo(UserRegistrationState.RegistrationStep.CONFIRMED_SITUATION);
    }

    @Test
    @DisplayName("Given a user " +
            "When user defines experience without activity or erhgo occupation " +
            "Then a new experience is created and user capacities remains empty")
    void define_experience_with_only_title() {
        var title = "Selected title for occupation";
        var userProfile = new UserProfile();

        userProfile.defineUserExperience(null, title);

        assertThat(userProfile.experiences())
                .hasSize(1)
                .allMatch(e -> e.getErhgoOccupation() == null)
                .allMatch(e -> e.getJobTitle().equals(title));

        assertThat(userProfile.getAllCapacities()).isEmpty();
    }

    @Test
    @DisplayName("Given an occupation with one activity " +
            "When user selects this occupation " +
            "Then a new experience is created and user capacities is up to date")
    void define_experience_with_occupation() {
        var title = "Selected title for occupation";
        var userProfile = new UserProfile();
        var capacity = CapacityGenerator.buildCapacity();
        var occupation = new ErhgoOccupationMotherObject().withCapacities(capacity).instance();

        userProfile.defineUserExperience(occupation, title);

        assertThat(userProfile.experiences())
                .hasSize(1)
                .allMatch(e -> e.getJobTitle().equals(title))
                .hasSize(1);

        assertThat(userProfile.getAllCapacities()).containsExactly(capacity);
    }

    @Test
    @DisplayName("Given an occupation without activity " +
            "When user selects this occupation " +
            "Then a new experience is created and user capacities remains empty")
    void define_experience_with_occupation_activity() {
        var title = "occupation title";
        var userProfile = new UserProfile();
        var occupation = new ErhgoOccupationMotherObject()
                .instance();

        userProfile.defineUserExperience(occupation, title);

        assertThat(userProfile.experiences())
                .hasSize(1)
                .allMatch(e -> e.getJobTitle().equals(title))
                .hasSize(1);

        assertThat(userProfile.getAllCapacities()).isEmpty();
    }

    @Test
    @DisplayName("" +
            "Given a UserProfile " +
            "When answer to criteria is empty " +
            "Then user answers for criteria is empty")
    void update_answer_to_criteria_empty() {
        var userProfile = new UserProfile();

        userProfile.updateAnswerToCriteria(Collections.emptySet(), Collections.emptySet());
        var userCriteriaValues = (Set<UserCriteriaValue>) ReflectionTestUtils.getField(userProfile, "userCriteriaValues");
        assertThat(userCriteriaValues).isEmpty();
    }

    @Test
    @DisplayName("" +
            "Given a UserProfile " +
            "When answer to criteria contains one value " +
            "Then value is associated to selecte answers for criteria is empty selected for user")
    void update_answer_to_criteria_single_answer() {
        var userProfile = new UserProfile();
        var criteria = new CriteriaMotherObject().build();
        var value = criteria.getCriteriaValues().get(0);

        userProfile.updateAnswerToCriteria(Set.of(value), Collections.emptySet());
        var userCriteriaValues = (Set<UserCriteriaValue>) ReflectionTestUtils.getField(userProfile, "userCriteriaValues");

        assertThat(userCriteriaValues)
                .allMatch(UserCriteriaValue::isSelected)
                .extracting(UserCriteriaValue::getValue)
                .containsExactly(value);
    }

    @ParameterizedTest
    @ValueSource(booleans = {false, true})
    @DisplayName("" +
            "Given a UserProfile with a criteria value" +
            "When answer to criteria change selection status  " +
            "Then value has target status")
    void update_answer_to_criteria_update_answer(boolean initialSelection) {
        var criteria = new CriteriaMotherObject().withQuestionType(CriteriaQuestionType.MULTIPLE).build();
        var value = criteria.getCriteriaValues().get(0);
        var userProfile = new UserProfile();
        userProfile.userCriteriaValues(Sets.newHashSet(new UserCriteriaValue(userProfile, value, initialSelection)));

        var selectedValues = initialSelection ? Collections.<CriteriaValue>emptySet() : Set.of(value);
        var unselectedValues = initialSelection ? Set.of(value) : Collections.<CriteriaValue>emptySet();

        userProfile.updateAnswerToCriteria(selectedValues, unselectedValues);
        var userCriteriaValues = (Set<UserCriteriaValue>) ReflectionTestUtils.getField(userProfile, "userCriteriaValues");

        assertThat(userCriteriaValues)
                .allMatch(u -> u.isSelected() == !initialSelection)
                .extracting(UserCriteriaValue::getValue)
                .containsExactly(value);
    }

    @Test
    @DisplayName("" +
            "Given a UserProfile with an answer to a threshold criteria " +
            "When submitting another answer to the same threshold criteria  " +
            "Then previous value is removed ")
    void update_answer_to_threshold_criteria_update_answer() {
        var criteria = new CriteriaMotherObject()
                .withQuestionType(CriteriaQuestionType.THRESHOLD)
                .withValueCodes("CV-1", "CV-2")
                .build();
        var value = criteria.getCriteriaValues().get(0);
        var expectedValue = criteria.getCriteriaValues().get(1);
        var userProfile = new UserProfile();
        userProfile.userCriteriaValues(Sets.newHashSet(new UserCriteriaValue(userProfile, value, true)));

        userProfile.updateAnswerToCriteria(Set.of(expectedValue), Collections.emptySet());
        var userCriteriaValues = (Set<UserCriteriaValue>) ReflectionTestUtils.getField(userProfile, "userCriteriaValues");

        assertThat(userCriteriaValues)
                .extracting(UserCriteriaValue::getValue)
                .containsExactly(expectedValue);
    }

    @Test
    @DisplayName("" +
            "Given a UserProfile " +
            "When submitting an unselected threshold value  " +
            "Then error is thrown ")
    void update_answer_no_unselected_threshold_value() {
        var criteria = new CriteriaMotherObject()
                .withQuestionType(CriteriaQuestionType.THRESHOLD)
                .build();
        var value = criteria.getCriteriaValues().get(0);
        var userProfile = new UserProfile();

        ThresholdValueCanNotBeUnselected exception = null;
        try {
            userProfile.updateAnswerToCriteria(Collections.emptySet(), Set.of(value));
        } catch (ThresholdValueCanNotBeUnselected e) {
            exception = e;
        }
        assertThat(exception).isNotNull();
    }


    @Test
    void experienceOccupationUpdateDirtiesUser() {
        var user = new UserProfileMotherObject().build();
        user.setUpdatedDate(null);
        user.defineUserExperience(new ErhgoOccupationMotherObject().instance(), "a");
        assertThat(user.indexationRequiredDate()).isCloseTo(new Date(), 10_000);
    }

    @Test
    void experienceOccupationUpdateDirtiesUser2() {
        var user = new UserProfileMotherObject().withExperienceOnOccupation(new ErhgoOccupationMotherObject().instance()).build();
        user.setUpdatedDate(null);
        user.experiences().iterator().next().setErhgoOccupation(new ErhgoOccupationMotherObject().instance());
        assertThat(user.indexationRequiredDate()).isCloseTo(new Date(), 10_000);
    }

    @Test
    void experienceRemovalDirtiesContext() {
        var user = new UserProfileMotherObject().withExperienceOnOccupation(new ErhgoOccupationMotherObject().instance()).build();
        user.setUpdatedDate(null);
        user.removeExperience(user.experiences().iterator().next());
        assertThat(user.indexationRequiredDate()).isCloseTo(new Date(), 10_000);
    }

    @Test
    void channelsUpdateDirtiesUserAndPublishEvent() {
        var user = new UserProfileMotherObject().build();
        var code = "A";
        user.updatedChannels(Set.of(code), UserChannel.ChannelSourceType.UNKNOWN);
        assertThat(user.indexationRequiredDate()).isCloseTo(new Date(), 10_000);
        Mockito.verify(eventPublisher).publishEvent(UserAffectedToChannelsEvent.forChannels(Set.of(code), user, UserChannel.ChannelSourceType.UNKNOWN));
    }


    @Test
    void channelsNotModifiedDoesNotDirtiesUserButrPublishEvent() {
        var code = "A";
        var user = new UserProfileMotherObject().withChannels(code).withPrescriber(code, UserChannel.ChannelSourceType.CANDIDATURE).withIndexationRequiredDate(null).build();
        Mockito.reset(eventPublisher);
        user.updatedChannels(Set.of(code), UserChannel.ChannelSourceType.CANDIDATURE);
        assertThat(user.indexationRequiredDate()).isNull();
        Mockito.verify(eventPublisher).publishEvent(UserAffectedToChannelsEvent.forChannels(Set.of(code), user, UserChannel.ChannelSourceType.CANDIDATURE));
    }

    @Test
    void locationUpdateDirtiesUser() {
        var user = new UserProfileMotherObject().withLocation(Location.builder().build()).build();
        user.generalInformation().setLocation(Location.builder().city("Saint-Pol").build());

        assertThat(user.indexationRequiredDate()).isCloseTo(new Date(), 10_000);
    }

    @Test
    void criteriaUpdateDirtiesUser() {
        var user = new UserProfileMotherObject().build();
        user.updateAnswerToCriteria(Collections.emptySet(), Collections.emptySet());
        assertThat(user.indexationRequiredDate()).isCloseTo(new Date(), 10_000);
    }


    @Test
    void markIndexedUpdatesDates() {
        var user = new UserProfileMotherObject().withIndexationRequiredDate(new Date()).build();
        user.indexed();
        assertThat(user.indexationRequiredDate()).isNull();
        assertThat(user.lastIndexationDate()).isCloseTo(new Date(), 10000);

    }

    @Test
    void getSendersOptOut() {
        var user = new UserProfileMotherObject().withOptOutMail("a@a").build();
        var mailConfiguration = user.mailConfiguration();
        ReflectionTestUtils.setField(mailConfiguration, "sendersOptOut", "a@a,b@b,a@a");
        assertThat(user.getSendersOptOut()).hasSize(2).contains("a@a", "b@b");
    }

    private void generateExperience(UserProfile user, ExperienceType type, ErhgoOccupation occupation) {
        var experience = new UserExperience();
        experience.setUuid(UUID.randomUUID());
        experience.setUserProfile(user);
        experience.setType(type);
        experience.setJobTitle(occupation.title);
        experience.setErhgoOccupation(occupation);
        user.experiences().add(experience);
    }

    @Test
    void computerMasteryLevelAccordingToExperiencesLevel() {
        var occupationWithLevel2 = new ErhgoOccupationMotherObject()
                .withTitle("titre métier niv.2")
                .withLevel(2)
                .build();
        var occupationWithLevel4 = new ErhgoOccupationMotherObject()
                .withTitle("titre métier niv.4")
                .withLevel(4)
                .build();
        var occupationWithoutLevel = new ErhgoOccupationMotherObject()
                .withTitle("titre métier sans niveau")
                .withLevel(null)
                .build();

        var userWithOnlyInternship = new UserProfileMotherObject().build();

        generateExperience(userWithOnlyInternship, ExperienceType.INTERNSHIP, occupationWithLevel4);

        userWithOnlyInternship.refreshMasteryLevelAndMarkAsIndexationRequired();
        assertThat(userWithOnlyInternship.masteryLevel()).isEqualTo(1f);


        var userWithInternshipAndJob = new UserProfileMotherObject().build();

        generateExperience(userWithInternshipAndJob, ExperienceType.INTERNSHIP, occupationWithLevel4);
        generateExperience(userWithInternshipAndJob, ExperienceType.JOB, occupationWithLevel4);
        generateExperience(userWithInternshipAndJob, ExperienceType.JOB, occupationWithLevel2);

        userWithInternshipAndJob.refreshMasteryLevelAndMarkAsIndexationRequired();
        assertThat(userWithInternshipAndJob.masteryLevel()).isEqualTo(3f);


        var userWithoutExperience = new UserProfileMotherObject().build();

        userWithoutExperience.refreshMasteryLevelAndMarkAsIndexationRequired();
        assertThat(userWithoutExperience.masteryLevel()).isZero();


        var userWithExperienceWithoutLevel = new UserProfileMotherObject().build();

        generateExperience(userWithExperienceWithoutLevel, ExperienceType.JOB, occupationWithoutLevel);

        userWithExperienceWithoutLevel.refreshMasteryLevelAndMarkAsIndexationRequired();
        assertThat(userWithExperienceWithoutLevel.masteryLevel()).isZero();
    }
}
