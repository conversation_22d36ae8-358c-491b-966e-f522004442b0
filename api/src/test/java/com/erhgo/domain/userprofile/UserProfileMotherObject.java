package com.erhgo.domain.userprofile;

import com.erhgo.TransactionTestHelper;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.*;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.Behavior;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.userprofile.criteria.UserCriteriaValue;
import com.erhgo.domain.userprofile.experience.UserExperience;
import com.erhgo.domain.userprofile.notification.RecruitmentNotification;
import com.erhgo.domain.utils.EventPublisherUtils;
import com.erhgo.generators.BehaviorGenerator;
import com.erhgo.generators.CriteriaMotherObject;
import com.erhgo.repositories.CapacityRepository;
import com.erhgo.repositories.UserMobileTokenRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.services.keycloak.UserRepresentation;
import com.google.common.base.Strings;
import jakarta.persistence.EntityManager;
import lombok.NoArgsConstructor;
import org.mockito.Mockito;
import org.mockito.internal.util.MockUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Scope;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.IntStream;

@Service
@Scope("prototype")
@NoArgsConstructor
public class UserProfileMotherObject {

    @Autowired
    private UserProfileRepository userProfileRepository;
    @Autowired
    private CapacityRepository capacityRepository;
    @Autowired
    private UserMobileTokenRepository userMobileTokenRepository;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private BehaviorGenerator behaviorGenerator;
    private final UserProfile userProfile = new UserProfile();

    @Autowired
    KeycloakMockService keycloakMockService;
    private String email;
    private String firstname;
    private String lastname;

    private Location location;
    private Date creationDate;
    @Autowired
    private TransactionTestHelper txHelper;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private EntityManager entityManager;

    private List<RecruitmentNotification> notifiedRecruitments = new ArrayList<>();

    private Map<String, Boolean> erhgoClassifications = new HashMap<>();
    private boolean withSomeCriteriaAnswers;
    private LocalDateTime userNoteDate;
    private Collection<String> mobileTokens = new ArrayList<String>();
    private Collection<CapacityOccurrence> capacityOccurrences;
    private boolean refreshCapacities;

    public UserProfile buildAndPersist() {
        var atomicUser = new AtomicReference<UserProfile>();
        txHelper.doInTransaction(() -> {
            var userProfileLoc = build();
            if (email != null || firstname != null || lastname != null) {
                var userRepresentation = new UserRepresentation();
                userRepresentation.setEmail(email == null ? "%s@%s".formatted(firstname, lastname) : email);
                userRepresentation.setId(userProfileLoc.userId());
                userRepresentation.setFirstName(firstname);
                userRepresentation.setLastName(lastname);
                userRepresentation.setCreatedTimestamp(System.currentTimeMillis());
                if (MockUtil.isMock(keycloakMockService))
                    Mockito.when(keycloakMockService.getFrontOfficeUserProfile(userProfileLoc.userId())).thenReturn(Optional.of(userRepresentation));
                else keycloakMockService.setUserProfile(userProfileLoc.userId(), userRepresentation);
            }
            userProfileLoc.getUserCriteriaValues().forEach(ucv -> {
                var criteriaValueCode = ((CriteriaValue) ReflectionTestUtils.getField(ucv, "value")).getCode();
                var criteriaValue = entityManager.find(CriteriaValue.class, criteriaValueCode);
                ReflectionTestUtils.setField(ucv, "value", criteriaValue);
            });
            if (refreshCapacities) {
                this.userProfile.addCapacities(userProfileLoc.experiences().stream().map(UserExperience::getErhgoOccupation).filter(Objects::nonNull).flatMap(o -> o.getAllCapacitiesWithDuplicates().stream().map(c -> capacityRepository.findOneByCode(c.getCode()))).toList());
            }
            var persistedUser = userProfileRepository.save(userProfileLoc);
            if (capacityOccurrences != null) {
                capacityOccurrences.forEach(co ->
                        persistedUser.addCapacities(
                                IntStream.of(co.getOccurrence())
                                        .mapToObj(useless -> entityManager.find(Capacity.class, co.getCapacity().getId()))
                                        .toList())
                );
            }
            notifiedRecruitments
                    .stream()
                    .map(n -> {
                        var persisted = RecruitmentNotification.builder()
                                .recruitment(entityManager.find(Recruitment.class, n.getRecruitment().getId()))
                                .state(n.getState())
                                .type(n.getType())
                                .userProfile(persistedUser)
                                .build();
                        entityManager.persist(persisted);
                        return persisted;
                    })
                    .forEach(entityManager::persist);
            var classifications = (Set<UserErhgoClassification>) ReflectionTestUtils.getField(persistedUser, "erhgoClassifications");
            erhgoClassifications.entrySet().stream()
                    .map(a -> new UserErhgoClassification(persistedUser, entityManager.find(ErhgoClassification.class, a.getKey()), a.getValue()))
                    .forEach(classifications::add);
            if (withSomeCriteriaAnswers) {
                var criteria = applicationContext.getBean(CriteriaMotherObject.class)
                        .withQuestionType(CriteriaQuestionType.MULTIPLE)
                        .buildAndPersist();

                persistedUser.updateAnswerToCriteria(criteria.getCriteriaValues(), Collections.emptyList());
            }
            mobileTokens.stream()
                    .map(t -> UserMobileToken.builder().token(t).userProfile(persistedUser).timestamp(OffsetDateTime.of(2042, 5, 5, 5, 8, 8, 8, ZoneOffset.UTC)).build())
                    .forEach(entityManager::persist);
            atomicUser.set(persistedUser);
        });

        if (userNoteDate != null) {
            txHelper.doInTransaction(() -> {
                var sql = "UPDATE UserNote SET updatedDate = :date WHERE id = :id";
                entityManager.joinTransaction();
                entityManager.createNativeQuery(sql)
                        .setParameter("date", userNoteDate)
                        .setParameter("id", atomicUser.get().userNotes().stream().findFirst().orElseThrow().getId())
                        .executeUpdate();
                ;
            });
        }

        return atomicUser.get();
    }

    public UserProfile build() {
        initGeneralInformation();
        this.userProfile.generalInformation().setLocation(this.location);
        if (creationDate != null) {
            ReflectionTestUtils.setField(this.userProfile, "createdDate", creationDate);
        }
        return this.userProfile;
    }

    private void initGeneralInformation() {
        if (Strings.isNullOrEmpty(this.userProfile.userId())) {
            this.userProfile.userId((this.userProfile.uuid() == null ? UUID.randomUUID() : this.userProfile.uuid()).toString());
        }
        if (this.userProfile.generalInformation() == null) {
            this.userProfile.generalInformation(GeneralInformation.builder()
                    .userId(this.userProfile.userId())
                    .userProfile(this.userProfile)
                    .mailVerificationState(new MailVerification())
                    .build());
        }
    }


    public UserProfileMotherObject withUserId(String userId) {
        this.userProfile.userId(userId);
        if (userId != null && userProfile.generalInformation() != null) {
            userProfile.generalInformation().setUserId(userId);
        }
        return this;
    }

    public UserProfileMotherObject withUuid(UUID uuid) {
        this.userProfile.uuid(uuid);
        return this;
    }

    public UserProfileMotherObject withLocation(Location location) {
        this.location = location;
        return this;
    }
    public UserProfileMotherObject withExperienceOnOccupation(ErhgoOccupation occupation) {
        var experience = UserExperience.builder().jobTitle(occupation.getTitle()).type(ExperienceType.JOB).userProfile(userProfile).build();
        this.userProfile.experiences().add(experience);
        experience.setErhgoOccupation(occupation);
        return this;
    }

    public UserProfileMotherObject withIndexationRequiredDate(Date date) {
        this.userProfile.indexationRequiredDate(date);
        return this;
    }

    public UserProfileMotherObject withLastIndexationDate(Date from) {
        this.userProfile.lastIndexationDate(from);
        return this;
    }

    public UserProfileMotherObject withPhoneNumber(String phone) {
        initGeneralInformation();
        this.userProfile.generalInformation().setPhoneNumber(phone);
        return this;
    }

    public UserProfileMotherObject withEmail(String email) {
        this.email = email;
        return this;
    }

    public UserProfileMotherObject withRegistrationStep(UserRegistrationState.RegistrationStep step) {
        this.userProfile.updateRegistrationState(step);
        return this;
    }

    public UserProfileMotherObject withEmailVerificationState(MailVerification.MailVerificationState verified) {
        initGeneralInformation();
        this.userProfile.generalInformation().getMailVerificationState().setState(verified);
        return this;
    }

    public UserProfileMotherObject withEmailVerificationCounter(int numberOfVerificationsForAccount) {
        initGeneralInformation();
        this.userProfile.generalInformation().getMailVerificationState().setNumberOfVerificationsForAccount(numberOfVerificationsForAccount);
        return this;
    }

    public UserProfileMotherObject withEmailVerificationLastDate(OffsetDateTime date) {
        initGeneralInformation();
        ReflectionTestUtils.setField(userProfile.generalInformation().getMailVerificationState(), "lastVerificationDate", date);
        return this;
    }

    public UserProfileMotherObject withChannels(String... channels) {
        var before = EventPublisherUtils.applicationEventPublisher;
        EventPublisherUtils.applicationEventPublisher = null;
        this.userProfile.updatedChannels(Set.of(channels), UserChannel.ChannelSourceType.ADMIN);
        EventPublisherUtils.applicationEventPublisher = before;
        return this;
    }

    public UserProfileMotherObject withPrescriber(String channel, UserChannel.ChannelSourceType sourceType) {
        this.userProfile.prescriber(new UserChannel().channel(channel).channelSourceType(sourceType));
        this.userProfile.updatedChannels(Set.of(channel), sourceType);
        return this;
    }

    public UserProfileMotherObject withExperience(ErhgoOccupation occupation, Integer durationInMonths) {
        userProfile.experiences().add(
                new UserExperience()
                        .setUserProfile(userProfile)
                        .setType(ExperienceType.JOB)
                        .setDurationInMonths(durationInMonths)
                        .setJobTitle("xp " + occupation.getTitle())
                        .setErhgoOccupation(occupation));
        userProfile.refreshProfessionalAndCapacityRelatedQuestionCapacityOccurrences();

        return this;
    }

    public UserProfileMotherObject withRegistrationSelectedOccupation(ErhgoOccupation occupation) {
        userProfile.userRegistrationState().setSelectedOccupation(occupation);
        return this;
    }

    public UserProfileMotherObject withBehaviors(Set<Behavior> behaviors) {
        userProfile.updateBehaviors(behaviors);
        return this;
    }

    public UserProfileMotherObject withJobOfferOptOut(Boolean transactionalBlacklisted) {
        userProfile.updateJobOfferOptOut(transactionalBlacklisted);
        return this;
    }

    public UserProfileMotherObject withOptOutMail(String optOutMail) {
        userProfile.sendersOptOut(Set.of(optOutMail));
        return this;
    }

    public UserProfileMotherObject withCreationDate(Date date) {
        this.creationDate = date;
        return this;
    }

    public UserProfileMotherObject withNotifiedRecruitment(Recruitment recruitment) {
        return withNotifiedRecruitment(recruitment, NotificationType.EMAIL);
    }

    public UserProfileMotherObject withNotifiedRecruitment(Recruitment recruitment, NotificationType type) {
        this.notifiedRecruitments.add(RecruitmentNotification.builder().recruitment(recruitment).state(NotificationState.NEW).type(type).userProfile(userProfile).build());
        return this;
    }

    public UserProfileMotherObject withSituation(Situation situation) {
        initGeneralInformation();
        this.userProfile.generalInformation().setSituation(situation);
        return this;
    }

    public UserProfileMotherObject withErhgoClassification(String code, boolean isAccepted) {
        erhgoClassifications.put(code, isAccepted);
        return this;
    }

    public UserProfileMotherObject withFirstname(String firstname) {
        this.firstname = firstname;
        return this;
    }

    public UserProfileMotherObject withLastname(String lastname) {
        this.lastname = lastname;
        return this;
    }

    public UserProfileMotherObject withSalary(Integer salary) {
        initGeneralInformation();
        this.userProfile.generalInformation().setSalary(salary);
        return this;
    }

    public UserProfileMotherObject withLastConnectionDateLessThan(int numberOfDays) {
        this.userProfile.lastConnectionDate(LocalDateTime.now().minusDays(numberOfDays));
        return this;
    }

    public UserProfileMotherObject withLastConnectionDate(LocalDateTime date) {
        this.userProfile.lastConnectionDate(date);
        return this;
    }

    public UserProfileMotherObject withSomeCriteriaAnswers() {
        this.withSomeCriteriaAnswers = true;
        return this;
    }

    public UserProfileMotherObject buildPartiallyCompletedUserProfile(String userId) {
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withTitle("Agriculteur")
                .buildAndPersist();

        return applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(userId)
                .withFirstname("Jean")
                .withLastname("Dupont")
                .withPhoneNumber("0123456789")
                .withLocation(Location.builder().city("Lille").citycode("59850").build())
                .withSituation(Situation.RESEARCHING)
                .withSalary(20000)
                .withBehaviors(Set.of(behaviorGenerator.createBehaviorForCategory(BehaviorCategory.SOCIABILITY)))
                .withSomeCriteriaAnswers()
                .withExperience(occupation, 9);
    }

    public UserProfileMotherObject withCriteriaAnswers(Collection<CriteriaValue> acceptedAnswers, Collection<CriteriaValue> refusedAnswers) {
        userProfile.updateAnswerToCriteria(acceptedAnswers, refusedAnswers);
        return this;
    }

    public UserProfileMotherObject withDelayInMonth(Integer delayInMonth) {
        initGeneralInformation();
        this.userProfile.generalInformation().setDelayInMonth(delayInMonth);
        return this;
    }

    public UserProfileMotherObject withNoteAtDate(String content, LocalDateTime date) {
        userNoteDate = date;
        if (userProfile.userNotes() == null) {
            userProfile.userNotes(new HashSet<>());
        }
        userProfile.userNotes().add(
                UserNote.builder()
                        .userProfile(userProfile)
                        .content(content)
                        .build()
        );
        return this;
    }

    public UserProfileMotherObject withMobileToken(String mobileToken) {
        if (mobileToken != null) this.mobileTokens.add(mobileToken);
        return this;
    }

    public UserProfileMotherObject withBlacklistedOccupation(ErhgoOccupation occupation) {
        if (occupation != null) userProfile.addOccupationToBlacklist(occupation);
        return this;
    }

    public UserProfileMotherObject withTrimojiPdfUrl(String url) {
        userProfile.trimojiPdfUrl(url);
        return this;
    }

    public UserProfileMotherObject withSoftSkillsStartedDate(OffsetDateTime offsetDateTime) {
        getTrimojiStatus().startedDate(offsetDateTime);
        return this;
    }

    public UserProfileMotherObject withSoftSkillsEndedDate(OffsetDateTime offsetDateTime) {
        getTrimojiStatus().endedDate(offsetDateTime);
        return this;
    }

    private TrimojiStatus getTrimojiStatus() {
        var softSkillsStatus = userProfile.trimojiStatus();
        if (softSkillsStatus == null) {
            softSkillsStatus = new TrimojiStatus();
            userProfile.trimojiStatus(softSkillsStatus);
        }
        return softSkillsStatus;
    }

    public UserProfileMotherObject withAttitudeText(String description, boolean forced) {
        var data = getUserExperienceGeneratedData();
        if (forced) {
            data.setForcedAttitude(description);
        } else {
            data.setGeneratedAttitude(description);
        }
        return this;
    }

    public UserProfileMotherObject withBehaviorDescriptionChecksum(String checksum) {
        ReflectionTestUtils.setField(getUserExperienceGeneratedData(), "checksum", checksum);
        return this;
    }

    public UserProfileMotherObject withBehaviorDescriptionModifiedDate(OffsetDateTime offsetDateTime) {
        ReflectionTestUtils.setField(getUserExperienceGeneratedData(), "attitudeModifiedByUserInstant", offsetDateTime);
        return this;
    }

    private UserExperienceGeneratedData getUserExperienceGeneratedData() {
        var userBehaviorDescription = userProfile.userExperienceGeneratedData();
        if (userBehaviorDescription == null) {
            userBehaviorDescription = new UserExperienceGeneratedData();
            userProfile.userExperienceGeneratedData(userBehaviorDescription);
        }
        return userBehaviorDescription;
    }

    public UserProfileMotherObject withAllTypeWorkingTimes(boolean enabled) {
        if (enabled)
            applicationContext.getBean(CriteriaMotherObject.class).withWorkingTimeCriteria().buildAndPersist().getCriteriaValues().stream().map(c -> new UserCriteriaValue(userProfile, c, true)).forEach(cv -> userProfile.userCriteriaValues().add(cv));
        return this;
    }

    public UserProfileMotherObject withAllContractTypes(boolean enabled) {
        if (enabled)
            applicationContext.getBean(CriteriaMotherObject.class).withTypeContractCriteria().buildAndPersist().getCriteriaValues().stream().map(c -> new UserCriteriaValue(userProfile, c, true)).forEach(cv -> userProfile.userCriteriaValues().add(cv));
        return this;
    }

    public UserProfileMotherObject withDrivingLicence(boolean enabled) {
        if (enabled) {
            var value = applicationContext.getBean(CriteriaMotherObject.class).forDriverLicence().buildAndPersist().getCriteriaValues().stream().filter(cv -> cv.getCode().equals("REP-6-3")).findFirst().orElseThrow();
            userProfile.userCriteriaValues().add(new UserCriteriaValue(userProfile, value, true));
        }
        return this;
    }

    public UserProfileMotherObject withLastMobileConnectionDate(LocalDateTime date) {
        initGeneralInformation();
        ReflectionTestUtils.setField(this.userProfile.generalInformation(), "lastMobileConnectionDate", date);
        return this;
    }

    public UserProfileMotherObject refreshCapacities() {
        this.refreshCapacities = true;
        return this;
    }

    public UserProfileMotherObject withIsFromHandicap(boolean isFromHandicap) {
        this.userProfile.isFromHandicap(isFromHandicap);
        return this;
    }

    public UserProfileMotherObject withHandicapModeEnabled(boolean handicapModeEnabled) {
        this.userProfile.handicapModeEnabled(handicapModeEnabled);
        return this;
    }

}
