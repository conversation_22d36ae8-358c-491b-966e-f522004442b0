package com.erhgo.domain.userprofile;

import com.erhgo.domain.sourcing.SourcingPreferences;
import com.erhgo.repositories.SourcingPreferencesRepository;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Scope("prototype")
@NoArgsConstructor
public class SourcingPreferencesMotherObject {

    @Autowired
    SourcingPreferencesRepository sourcingPreferencesRepository;

    SourcingPreferences sourcingPreferences = new SourcingPreferences();

    public SourcingPreferences build() {
        if (sourcingPreferences.mailFrequency() == null)
            sourcingPreferences.mailFrequency(SourcingPreferences.MailFrequency.DAILY);
        if (sourcingPreferences.userId() == null) sourcingPreferences.userId("4242");
        return sourcingPreferences;
    }

    @Transactional
    public SourcingPreferences buildAndPersist() {
        return sourcingPreferencesRepository.save(build());
    }

    public SourcingPreferencesMotherObject withIsoWeekDay(Integer isoWeekDay) {
        sourcingPreferences.isoWeekDay(isoWeekDay);
        return this;
    }

    public SourcingPreferencesMotherObject withUserId(String userId) {
        sourcingPreferences.userId(userId);
        return this;
    }

    public SourcingPreferencesMotherObject withMailFrequency(SourcingPreferences.MailFrequency mailFrequency) {
        sourcingPreferences.mailFrequency(mailFrequency);
        return this;
    }

    public SourcingPreferencesMotherObject withNotifiedOnSpontaneousCandidatures(boolean b) {
        sourcingPreferences.notifyOnSpontaneousCandidature(b);
        return this;
    }
}
