package com.erhgo.domain.userprofile;

import com.erhgo.domain.sector.Sector;
import com.erhgo.repositories.SectorRepository;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Service
@Scope("prototype")
@NoArgsConstructor
public class SectorMotherObject {

    @Autowired
    SectorRepository repository;

    Sector sector = new Sector();

    public Sector build() {
        if (sector.getCode() == null) {
            ReflectionTestUtils.setField(sector, "code", UUID.randomUUID().toString());
        }
        if (sector.getLabel() == null) {
            ReflectionTestUtils.setField(sector, "label", "Label for sector %s".formatted(sector.getCode()));
        }
        if (sector.getAbbreviation() == null) {
            ReflectionTestUtils.setField(sector, "abbreviation", "Abbr. %s".formatted(sector.getCode()));
        }
        return sector;
    }

    SectorMotherObject withCode(String code) {
        ReflectionTestUtils.setField(sector, "code", code);
        return this;
    }

    SectorMotherObject withId(UUID uuid) {
        ReflectionTestUtils.setField(sector, "uuid", uuid);
        return this;
    }

    @Transactional
    public Sector buildAndPersist() {
        return repository.save(build());
    }

}
