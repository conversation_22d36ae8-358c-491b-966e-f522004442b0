package com.erhgo.domain.userprofile.experience;

import com.erhgo.domain.enums.ExperienceType;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.JobActivityLabelGenerator;
import com.erhgo.generators.UserExperienceGenerator;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import java.time.LocalDate;
import java.util.UUID;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;

class UserExperienceTest {

    @Test
    void sort_experience_consider_duration() {

        UserExperience ux1 = UserExperience.builder().uuid(UUID.randomUUID()).durationInMonths(3).build();
        UserExperience ux2 = UserExperience.builder().uuid(UUID.randomUUID()).durationInMonths(9).build();

        assertThat(ux2.getDurationInMonths()).isGreaterThan(ux1.getDurationInMonths());
    }

    @Test
    void sort_experience_is_null_proof() {

        UserExperience ux1 = UserExperience.builder().uuid(UUID.randomUUID()).durationInMonths(3).build();
        UserExperience ux2 = UserExperience.builder().uuid(UUID.randomUUID()).durationInMonths(null).build();

        assertThat(ux2).isLessThan(ux1);
    }


    @Test
    void getActivitiesWithCapacitiesMatchingActivityAndMinimumLevel_should_retrieve_empty_list_for_empty_experience() {
        var ux = new UserExperience();
        var activity = JobActivityLabelGenerator.buildActivityWithCapacities(CapacityGenerator.buildCapacity());
        assertThat(ux.getActivitiesWithCapacitiesMatchingActivityAndMinimumLevel(activity, 2)).isEmpty();
    }

    @Test
    void getActivitiesWithCapacitiesMatchingActivityAndMinimumLevel_should_retrieve_empty_list_for_experience_with_lower_level() {
        var activity = JobActivityLabelGenerator.buildActivityWithCapacities(CapacityGenerator.buildCapacity());
        var ux = UserExperienceGenerator.generateExperienceWithActivityAndLevel(activity, 1);
        assertThat(ux.getActivitiesWithCapacitiesMatchingActivityAndMinimumLevel(activity, 2)).isEmpty();
    }

    @Test
    void getActivitiesWithCapacitiesMatchingActivityAndMinimumLevel_should_retrieve_activity_when_no_level() {
        var capacity = CapacityGenerator.buildCapacity();
        var userActivity = JobActivityLabelGenerator.buildActivityWithCapacities(capacity);

        var ux = UserExperienceGenerator.generateExperienceWithActivityAndLevel(userActivity, 1);

        assertThat(ux.getActivitiesWithCapacitiesMatchingActivityAndMinimumLevel(JobActivityLabelGenerator.buildActivityWithCapacities(capacity), null)).contains(userActivity);
    }

    @Test
    void getActivitiesWithCapacitiesMatchingActivityAndMinimumLevel_should_retrieve_activity_when_no_level_regarding_induced_capacity() {
        var inducedCapacity = CapacityGenerator.buildCapacity();
        var capacity = CapacityGenerator.buildCapacityWithInducedCapacity(inducedCapacity);
        var userActivity = JobActivityLabelGenerator.buildActivityWithCapacities(capacity);

        var ux = UserExperienceGenerator.generateExperienceWithActivityAndLevel(userActivity, 1);

        assertThat(ux.getActivitiesWithCapacitiesMatchingActivityAndMinimumLevel(JobActivityLabelGenerator.buildActivityWithCapacities(inducedCapacity), null)).contains(userActivity);
    }

    @Test
    void getActivitiesWithCapacitiesMatchingActivityAndMinimumLevel_should_not_retrieve_activity_due_to_induced_capacity() {
        var inducedCapacity = CapacityGenerator.buildCapacity();
        var capacity = CapacityGenerator.buildCapacityWithInducedCapacity(inducedCapacity);
        var userActivity = JobActivityLabelGenerator.buildActivityWithCapacities(capacity);

        var ux = UserExperienceGenerator.generateExperienceWithActivityAndLevel(userActivity, 2);

        assertThat(ux.getActivitiesWithCapacitiesMatchingActivityAndMinimumLevel(JobActivityLabelGenerator.buildActivityWithCapacities(inducedCapacity), 2)).isEmpty();
    }


    @Test
    void getActivitiesWithCapacitiesMatchingActivityAndMinimumLevel_should_retrieve_activity_due_to_induced_capacity_for_low_level() {
        var inducedCapacity = CapacityGenerator.buildCapacity();
        var capacity = CapacityGenerator.buildCapacityWithInducedCapacity(inducedCapacity);
        var userActivity = JobActivityLabelGenerator.buildActivityWithCapacities(capacity);

        var ux = UserExperienceGenerator.generateExperienceWithActivityAndLevel(userActivity, 1);

        assertThat(ux.getActivitiesWithCapacitiesMatchingActivityAndMinimumLevel(JobActivityLabelGenerator.buildActivityWithCapacities(inducedCapacity), 1)).contains(userActivity);
    }

    @Test
    void getActivitiesWithCapacitiesMatchingActivityAndMinimumLevel_should_retrieve_activity_when_level_match() {
        var capacity = CapacityGenerator.buildCapacity();
        var userActivity = JobActivityLabelGenerator.buildActivityWithCapacities(capacity);

        var ux = UserExperienceGenerator.generateExperienceWithActivityAndLevel(userActivity, 2);

        assertThat(ux.getActivitiesWithCapacitiesMatchingActivityAndMinimumLevel(JobActivityLabelGenerator.buildActivityWithCapacities(capacity), 2)).contains(userActivity);
    }

    @Test
    void sortByRelevance_toleratesEmptyUX() {
        var ux1 = UserExperience.builder().build();
        var ux2 = UserExperience.builder().build();
        Assertions.assertThat(Stream.of(ux1, ux2).sorted(UserExperience.RELEVANCE_COMPARATOR).toList()).hasSize(2);
    }

    @ParameterizedTest
    @CsvSource(value = {
            "JOB,true,72,MISSION,false,30,-1",
            "MISSION,false,30,JOB,true,72,1",
            "JOB,true,72,JOB,true,72,0",
            "JOB,true,72,JOB,false,72,-1",
            "JOB,false,72,JOB,true,72,1",
            "JOB,true,30,JOB,true,72,1",
            "JOB,true,72,JOB,true,30,-1",
            "JOB,true,72,null,null,null,-1",
            "null,null,null,JOB,true,72,1",
            "JOB,null,72,JOB,true,72,1",
            "JOB,true,null,JOB,true,72,1",
            "null,true,72,JOB,true,72,1",
            "JOB,null,null,JOB,true,72,1",
            "null,null,72,null,null,30,-1",
            "null,null,null,null,null,null,0"
    }, nullValues = "null")
    void testRelevanceComparator(String type1, String isOngoing1, String durationInMonths1,
                                 String type2, String isOngoing2, String durationInMonths2,
                                 int expected) {
        var expType1 = type1 != null ? ExperienceType.valueOf(type1) : null;
        var ongoing1 = isOngoing1 != null ? Boolean.valueOf(isOngoing1) : null;
        var duration1 = durationInMonths1 != null ? Integer.valueOf(durationInMonths1) : null;
        var expType2 = type2 != null ? ExperienceType.valueOf(type2) : null;
        var ongoing2 = isOngoing2 != null ? Boolean.valueOf(isOngoing2) : null;
        var duration2 = durationInMonths2 != null ? Integer.valueOf(durationInMonths2) : null;

        var ux1 = UserExperience.builder()
                .type(expType1)
                .durationInMonths(duration1)
                .startDate(ongoing1 != null && ongoing1 ? LocalDate.now().minusMonths(2) : null)
                .endDate(ongoing1 != null && ongoing1 ? null : LocalDate.now().minusMonths(1))
                .build();

        var ux2 = UserExperience.builder()
                .type(expType2)
                .durationInMonths(duration2)
                .startDate(ongoing2 != null && ongoing2 ? LocalDate.now().minusMonths(2) : null)
                .endDate(ongoing2 != null && ongoing2 ? null : LocalDate.now().minusMonths(1))
                .build();
        ux1.setUuid(null);
        ux2.setUuid(null);
        int result = UserExperience.RELEVANCE_COMPARATOR.compare(ux1, ux2);
        Assertions.assertThat(result).isEqualTo(expected);
    }
}
