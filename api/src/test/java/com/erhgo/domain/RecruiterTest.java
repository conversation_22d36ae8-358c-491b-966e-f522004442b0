package com.erhgo.domain;

import com.erhgo.domain.referential.Recruiter;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class RecruiterTest {

    @Test
    void getPrefix_shouldReturnE() {
        // given
        var organization = Recruiter.recruiterBuilder().build();

        // when
        final var prefix = organization.getPrefix();

        // then
        assertThat(prefix).isEqualTo("E");
    }

    @Test
    void getSuffixLeftPadCount_shouldReturn4() {
        // given
        var organization = Recruiter.recruiterBuilder().build();

        // when
        final var leftPadCount = organization.getSuffixLeftPadCount();

        // then
        assertThat(leftPadCount).isEqualTo(4);
    }
}
