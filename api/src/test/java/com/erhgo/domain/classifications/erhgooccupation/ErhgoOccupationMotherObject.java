package com.erhgo.domain.classifications.erhgooccupation;

import com.erhgo.TransactionTestHelper;
import com.erhgo.domain.classifications.esco.EscoOccupation;
import com.erhgo.domain.classifications.esco.EscoSkill;
import com.erhgo.domain.classifications.isco.IscoOccupation;
import com.erhgo.domain.classifications.rome.RomeOccupation;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.BehaviorCategory;
import com.erhgo.domain.referential.Behavior;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.generators.JobActivityLabelGenerator;
import com.erhgo.repositories.JobActivityLabelRepository;
import com.erhgo.repositories.classifications.*;
import com.google.common.collect.Sets;
import org.junit.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Scope("prototype")
public class ErhgoOccupationMotherObject {

    public static final String ROME = "D1102";
    public static final int ISCO_GROUP = 7512;
    private final ErhgoOccupation erhgoOccupation;

    private ErhgoOccupationState qualificationState;
    @Autowired
    private WorkEnvironmentRepository workEnvironmentRepository;
    @Autowired
    private ErhgoOccupationRepository erhgoOccupationRepository;
    @Autowired
    private JobActivityLabelRepository jobActivityLabelRepository;
    @Autowired
    private ErhgoClassificationRepository erhgoClassificationRepository;
    @Autowired
    private RomeOccupationRepository romeOccupationRepository;
    @Autowired
    private EscoOccupationRepository escoOccupationRepository;
    @Autowired
    private IscoOccupationRepository iscoOccupationRepository;
    @Autowired
    private TransactionTestHelper txHelper;

    public ErhgoOccupationMotherObject() {
        this.erhgoOccupation = ErhgoOccupation.builder().build();
    }


    public ErhgoOccupation build() {
        return erhgoOccupation;
    }

    public ErhgoOccupationMotherObject withTitle(String title) {
        erhgoOccupation.setTitle(title);
        return this;
    }

    public ErhgoOccupationMotherObject withLabels(String... labels) {
        erhgoOccupation.getAlternativeLabels().addAll(Set.of(labels));
        return this;
    }


    @Transactional
    public ErhgoOccupation buildAndPersist() {
        addRequiredFields();
        if (!erhgoOccupation.getActivities().isEmpty()) {
            jobActivityLabelRepository.saveAll(erhgoOccupation.getActivities());
        }
        erhgoOccupation.getRomeOfErhgoOccupations().forEach(r -> {
            ReflectionTestUtils.setField(r, "romeOccupation", romeOccupationRepository.findById(r.getRomeCode()).orElseThrow());
        });
        return erhgoOccupationRepository.save(erhgoOccupation);
    }

    public ErhgoOccupation instance() {
        return addRequiredFields();
    }

    private ErhgoOccupation addRequiredFields() {
        if (erhgoOccupation.getTitle() == null) {
            erhgoOccupation.setTitle("Random title " + UUID.randomUUID());
        }
        if (qualificationState == null) {
            erhgoOccupation.computeQualificationState();
        } else {
            erhgoOccupation.setQualificationState(qualificationState);
        }
        return erhgoOccupation;
    }

    public ErhgoOccupationMotherObject withLevel(MasteryLevel level) {
        erhgoOccupation.updateLevel(level);
        return this;
    }

    public ErhgoOccupationMotherObject withLevel(int level) {
        erhgoOccupation.updateLevel(MasteryLevel.values()[level - 1]);
        return this;
    }

    public ErhgoOccupationMotherObject withId(UUID id) {
        erhgoOccupation.setId(id);
        return this;
    }

    public ErhgoOccupationMotherObject withDescription(String description) {
        erhgoOccupation.setDescription(description);
        return this;
    }

    public ErhgoOccupationMotherObject withState(ErhgoOccupationState state) {
        qualificationState = state;
        return this;
    }

    public ErhgoOccupationMotherObject computeState() {
        erhgoOccupation.computeQualificationState();
        return this;
    }

    public ErhgoOccupationMotherObject withEscoOccupationCascadingSkills(EscoOccupation escoOccupation) {
        if (erhgoOccupation.getEntities() == null) {
            erhgoOccupation.setEntities(new HashSet<>());
        }
        erhgoOccupation.addEscoOccupation(escoOccupation);
        return this;
    }

    public ErhgoOccupationMotherObject withSkills(EscoSkill... skills) {
        erhgoOccupation.setSkills(Sets.newHashSet(skills));
        return this;
    }

    public ErhgoOccupationMotherObject withRomeOccupations(Collection<RomeOccupation> romeOccupations) {
        romeOccupations.forEach(erhgoOccupation::addRome);
        return this;
    }

    public ErhgoOccupationMotherObject withOptionalActivities(JobActivityLabel... activities) {
        var occupationActivities = Stream.of(activities)
                .map(a -> OccupationActivity.builder()
                        .activity(a)
                        .occupation(erhgoOccupation)
                        .occupationQualificationSource(OccupationQualificationSource.MANUAL)
                        .build()
                )
                .map(AbstractOccupationEntity.class::cast)
                .collect(Collectors.toSet());

        if (erhgoOccupation.getEntities() == null) {
            erhgoOccupation.setEntities(new HashSet<>());
        }
        erhgoOccupation.getEntities().addAll(occupationActivities);
        return this;
    }

    public ErhgoOccupationMotherObject withActivity(JobActivityLabel jobActivityLabel, boolean mandatory) {
        if (jobActivityLabel != null) {
            erhgoOccupation.addActivity(jobActivityLabel);
            erhgoOccupation.setEntityMandatoryState(jobActivityLabel.getUuid(), mandatory ? MandatoryState.ESSENTIAL : MandatoryState.OPTIONAL);
        }
        return this;
    }

    public ErhgoOccupationMotherObject withContext(Context context, boolean mandatory) {
        erhgoOccupation.addContext(context);
        erhgoOccupation.setEntityMandatoryState(context.getId(), mandatory ? MandatoryState.ESSENTIAL : MandatoryState.OPTIONAL);
        return this;
    }

    public ErhgoOccupationMotherObject withBehavior(Behavior behavior) {
        erhgoOccupation.addBehavior(behavior);
        return this;
    }

    public ErhgoOccupationMotherObject withBehaviorCategory1(BehaviorCategory behaviorCategory1) {
        erhgoOccupation.setBehaviorCategory1(behaviorCategory1);
        return this;
    }

    public ErhgoOccupationMotherObject withBehaviorCategory2(BehaviorCategory behaviorCategory2) {
        erhgoOccupation.setBehaviorCategory2(behaviorCategory2);
        return this;
    }

    public ErhgoOccupationMotherObject withBehaviorCategory1(BehaviorCategory behaviorCategory1, boolean overloaded) {
        withBehaviorCategory1(behaviorCategory1);
        erhgoOccupation.setBehaviorCategory1Overloaded(overloaded);
        return this;
    }

    public ErhgoOccupationMotherObject withBehaviorCategory2(BehaviorCategory behaviorCategory2, boolean overloaded) {
        withBehaviorCategory2(behaviorCategory2);
        erhgoOccupation.setBehaviorCategory2Overloaded(overloaded);
        return this;
    }
    public ErhgoOccupationMotherObject withCapacities(Capacity... capacities) {
        var activities = Stream.of(capacities).map(JobActivityLabelGenerator::buildActivityWithCapacities).toArray(JobActivityLabel[]::new);
        return withOptionalActivities(activities);
    }

    public ErhgoOccupationMotherObject withTechnical(boolean isTechnical) {
        this.erhgoOccupation.setTechnical(isTechnical);
        return this;
    }

    public ErhgoOccupationMotherObject withWorkEnvironmentCodes(String... workEnvironmentCodes) {
        var env = workEnvironmentRepository.findByCodeIn(Set.of(workEnvironmentCodes));
        if (env.size() != workEnvironmentCodes.length) {
            Assert.fail("Some unknown environment codes");
        }
        if (erhgoOccupation.getWorkEnvironments() == null) {
            erhgoOccupation.setWorkEnvironments(new HashSet<>());
        }
        erhgoOccupation.getWorkEnvironments().addAll(env);
        return this;
    }

    public ErhgoOccupationMotherObject withCriteriaValues(Set<CriteriaValue> expectedCriteriaValues) {
        erhgoOccupation.setCriteriaValues(expectedCriteriaValues);
        return this;
    }

    public ErhgoOccupationMotherObject withBehaviorDescription(String behaviorDescription) {
        erhgoOccupation.setBehaviorsDescription(behaviorDescription);
        return this;
    }

    public ErhgoOccupationMotherObject qualified(boolean qualified) {
        erhgoOccupation.setQualificationState(qualified ? ErhgoOccupationState.QUALIFIED_V3_CONFIRMED : ErhgoOccupationState.LOW);
        return this;
    }

    public ErhgoOccupationMotherObject withBehaviorCategory3(BehaviorCategory behaviorCategory3) {
        erhgoOccupation.setBehaviorCategory3(behaviorCategory3);
        return this;
    }

    public ErhgoOccupationMotherObject withAlternativeLabel(String label) {
        if (erhgoOccupation.getAlternativeLabels() == null) {
            erhgoOccupation.setAlternativeLabels(new HashSet<>());
        }
        erhgoOccupation.getAlternativeLabels().add(label);
        return this;
    }

    public ErhgoOccupationMotherObject withClassificationsCodes(String... codes) {
        erhgoOccupation.getErhgoClassifications().addAll(erhgoClassificationRepository.findErhgoClassificationByCodeIn(List.of(codes)));
        return this;
    }

    public ErhgoOccupationMotherObject withRomeAndEscoOccupations() {
        var occupationTitle = "Boulanger";
        var romeOccupation = RomeOccupation.builder().title(occupationTitle).code(ROME).build();
        var iscoOccupation = IscoOccupation.builder().title(occupationTitle).iscoGroup(ISCO_GROUP).build();
        EscoOccupation escoOccupation;

        if (hasPersistence()) {
            romeOccupation = romeOccupationRepository.save(romeOccupation);
            iscoOccupation = iscoOccupationRepository.save(iscoOccupation);
            escoOccupation = escoOccupationRepository.save(EscoOccupation.builder().uri("uri").title(occupationTitle).iscoOccupation(iscoOccupation).build());

        } else {
            escoOccupation = EscoOccupation.builder().uri("uri").title(occupationTitle).iscoOccupation(iscoOccupation).build();
        }

        erhgoOccupation.addRome(romeOccupation);
        erhgoOccupation.setEscoOccupations(Set.of(escoOccupation));
        return this;
    }

    private boolean hasPersistence() {
        return romeOccupationRepository != null;
    }

    private RomeOccupation createRomeOccupation(String r) {
        var occupation = RomeOccupation.builder().title("rome %s".formatted(r)).code(r).build();
        if (hasPersistence()) {
            occupation = romeOccupationRepository.save(occupation);
        }

        return occupation;
    }

}
