package com.erhgo.domain.classifications.erhgooccupation;

import com.erhgo.domain.enums.BehaviorCategory;
import com.erhgo.domain.enums.CriteriaQuestionType;
import com.erhgo.domain.exceptions.LabelDuplicatesTitleException;
import com.erhgo.domain.exceptions.MultipleThresholdValuesException;
import com.erhgo.generators.BehaviorGenerator;
import com.erhgo.generators.CriteriaMotherObject;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.*;

import java.util.Collection;
import java.util.Collections;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.fail;

class ErhgoOccupationTest {

    @Test
    void addLabel_same_as_title_should_fail() {
        var title = "DO NOT REPEAT";
        var occupation = new ErhgoOccupationMotherObject().withTitle(title).instance();
        var singleton = Collections.singleton(title);
        Assertions.assertThrows(LabelDuplicatesTitleException.class, () -> occupation.setTitleAndAlternativeLabels(title, singleton));
    }

    @Test
    void addLabel_same_as_title_should_fail_ignoring_blank() {
        var title = "DO NOT REPEAT";

        var occupation = new ErhgoOccupationMotherObject().withTitle(title).instance();
        var singleton = Collections.singleton("  " + title + " ");
        Assertions.assertThrows(LabelDuplicatesTitleException.class, () -> occupation.setTitleAndAlternativeLabels(title, singleton));
    }

    @Test
    void addLabel_with_empty_title_should_fail() {
        var occupation = new ErhgoOccupationMotherObject().withTitle("previous").instance();
        var singleton = Collections.singleton("next");
        Assertions.assertThrows(IllegalArgumentException.class, () -> occupation.setTitleAndAlternativeLabels(" ", singleton));
    }

    @Test
    void addLabel_with_empty_label_should_fail() {
        var occupation = new ErhgoOccupationMotherObject().withTitle("previous").instance();
        var singleton = Collections.singleton(" ");
        Assertions.assertThrows(IllegalArgumentException.class, () -> occupation.setTitleAndAlternativeLabels("next", singleton));
    }

    @Test
    void addLabel_should_succeed() {
        var expectedTitle = "NewTitle";
        var expectedLabels = Lists.newArrayList("label 1", "Label 2");
        var occupation = new ErhgoOccupationMotherObject().withTitle("previous").instance();
        occupation.setTitleAndAlternativeLabels(expectedTitle, expectedLabels);
        assertThat(occupation.getTitle()).isEqualTo(expectedTitle);
        assertThat(occupation.getAlternativeLabels()).containsExactly("Label 1", "Label 2");
    }

    @Test
    void updateBehaviorCategories_should_update_BehaviorCategory1_succeed() {
        var occupation = ErhgoOccupation.builder().build();
        var behavior = BehaviorGenerator.buildBehavior("B1", "Title1", null, BehaviorCategory.CONSTANCY, 1);
        occupation.addBehavior(behavior);
        assertThat(occupation.getBehaviorCategory1()).isEqualTo(BehaviorCategory.CONSTANCY);
        assertThat(occupation.getBehaviorCategory2()).isNull();
    }

    @Test
    void updateBehaviorCategories_should_update_BehaviorCategories_succeed() {
        var occupation = ErhgoOccupation.builder().build();
        var behavior = BehaviorGenerator.buildBehavior("B1", "Title1", null, BehaviorCategory.CONSTANCY, 1);
        var behavior2 = BehaviorGenerator.buildBehavior("B2", "Title2", null, BehaviorCategory.HONESTY, 2);
        occupation.addBehavior(behavior);
        occupation.addBehavior(behavior2);
        assertThat(occupation.getBehaviorCategory1()).isEqualTo(BehaviorCategory.CONSTANCY);
        assertThat(occupation.getBehaviorCategory2()).isEqualTo(BehaviorCategory.HONESTY);
    }

    @Test
    void updateBehaviorCategories_should_update_BehaviorCategories_with_ordered_occurrencePriority_succeed() {
        var occupation = ErhgoOccupation.builder().build();
        for (int i = 0; i < 6; i++) {
            occupation.addBehavior(BehaviorGenerator.buildBehavior("B" + i, "Title" + +i, null, BehaviorCategory.values()[i], 1));
        }
        assertThat(occupation.getBehaviorCategory1()).isEqualTo(BehaviorCategory.CONSTANCY);
        assertThat(occupation.getBehaviorCategory2()).isEqualTo(BehaviorCategory.HONESTY);
    }

    @Test
    void updateBehaviorCategories_should_update_BehaviorCategories_with_multiple_same_BehaviorCategory_succeed() {
        var occupation = ErhgoOccupation.builder().build();
        for (int i = 0; i < 6; i++) {
            occupation.addBehavior(BehaviorGenerator.buildBehavior("B" + i, "Title" + +i, null, BehaviorCategory.values()[i], 1));
        }
        occupation.addBehavior(BehaviorGenerator.buildBehavior("B6", "Title7", null, BehaviorCategory.HONESTY, 1));
        assertThat(occupation.getBehaviorCategory1()).isEqualTo(BehaviorCategory.HONESTY);
        assertThat(occupation.getBehaviorCategory2()).isEqualTo(BehaviorCategory.CONSTANCY);
    }

    @Test
    void updateBehaviorCategories_should_update_BehaviorCategories_with_isBehaviorCategory1Overloaded_succeed() {
        var occupation = ErhgoOccupation.builder().build();
        var behavior = BehaviorGenerator.buildBehavior("B1", "Title1", null, BehaviorCategory.CONSTANCY, 1);
        var behavior2 = BehaviorGenerator.buildBehavior("B2", "Title2", null, BehaviorCategory.HONESTY, 2);
        occupation.setBehaviorCategory1Overloaded(true);
        occupation.addBehavior(behavior);
        occupation.addBehavior(behavior2);
        assertThat(occupation.getBehaviorCategory1()).isNull();
        assertThat(occupation.getBehaviorCategory2()).isEqualTo(BehaviorCategory.CONSTANCY);
    }

    @Test
    void updateBehaviorCategories_should_update_BehaviorCategories_with_isBehaviorCategory2Overloaded_succeed() {
        var occupation = ErhgoOccupation.builder().build();
        var behavior = BehaviorGenerator.buildBehavior("B1", "Title1", null, BehaviorCategory.CONSTANCY, 1);
        var behavior2 = BehaviorGenerator.buildBehavior("B2", "Title2", null, BehaviorCategory.HONESTY, 2);
        occupation.setBehaviorCategory2Overloaded(true);
        occupation.addBehavior(behavior);
        occupation.addBehavior(behavior2);
        assertThat(occupation.getBehaviorCategory1()).isEqualTo(BehaviorCategory.CONSTANCY);
        assertThat(occupation.getBehaviorCategory2()).isNull();
    }

    @Test
    void updateBehaviorCategories_should_not_update_BehaviorCategories_succeed() {
        var occupation = ErhgoOccupation.builder().build();
        occupation.setBehaviorCategory1(BehaviorCategory.RIGOR);
        occupation.setBehaviorCategory2(BehaviorCategory.PRAGMATISM);
        var behavior = BehaviorGenerator.buildBehavior("B1", "Title1", null, BehaviorCategory.CONSTANCY, 1);
        var behavior2 = BehaviorGenerator.buildBehavior("B2", "Title2", null, BehaviorCategory.HONESTY, 2);
        occupation.setBehaviorCategory1Overloaded(true);
        occupation.setBehaviorCategory2Overloaded(true);
        occupation.addBehavior(behavior);
        occupation.addBehavior(behavior2);
        assertThat(occupation.getBehaviorCategory1()).isEqualTo(BehaviorCategory.RIGOR);
        assertThat(occupation.getBehaviorCategory2()).isEqualTo(BehaviorCategory.PRAGMATISM);
    }

    @Test
    void updateBehaviorCategories_after_remove_OccupationBehavior_succeed() {
        var occupation = ErhgoOccupation.builder().build();
        var behavior = BehaviorGenerator.buildBehavior("B1", "Title1", null, BehaviorCategory.CONSTANCY, 1);
        var behavior2 = BehaviorGenerator.buildBehavior("B2", "Title2", null, BehaviorCategory.HONESTY, 2);

        occupation.addBehavior(behavior);
        occupation.addBehavior(behavior2);
        assertThat(occupation.getBehaviorCategory1()).isEqualTo(BehaviorCategory.CONSTANCY);
        assertThat(occupation.getBehaviorCategory2()).isEqualTo(BehaviorCategory.HONESTY);

        occupation.removeEntityById(behavior.getId());
        assertThat(occupation.getBehaviorCategory1()).isEqualTo(BehaviorCategory.HONESTY);
        assertThat(occupation.getBehaviorCategory2()).isNull();

        occupation.setBehaviorCategory1Overloaded(true);
        occupation.removeEntityById(behavior2.getId());
        assertThat(occupation.getBehaviorCategory1()).isEqualTo(BehaviorCategory.HONESTY);
        assertThat(occupation.getBehaviorCategory2()).isNull();
    }

    @Test
    void updateBehaviorCategory_should_allow_null_value() {
        var occupation = ErhgoOccupation.builder().build();
        var behavior = BehaviorGenerator.buildBehavior("B1", "Title1", null, BehaviorCategory.CONSTANCY, 1);
        var behavior2 = BehaviorGenerator.buildBehavior("B2", "Title2", null, BehaviorCategory.HONESTY, 2);
        occupation.addBehavior(behavior);
        occupation.addBehavior(behavior2);
        occupation.setBehaviorCategory1Overloaded(true);
        occupation.setBehaviorCategory1(BehaviorCategory.RIGOR);

        occupation.updateBehaviorCategory(null, 0);

        assertThat(occupation.getBehaviorCategory1()).isEqualTo(BehaviorCategory.CONSTANCY);
        assertThat(occupation.isBehaviorCategory1Overloaded()).isFalse();
    }

    @Test
    void updateBehaviorCategory_with_null_category_should_exclude_already_used_category_from_computing() {
        var occupation = ErhgoOccupation.builder().build();
        var behavior = BehaviorGenerator.buildBehavior("B1", "Title1", null, BehaviorCategory.CONSTANCY, 1);
        var behavior2 = BehaviorGenerator.buildBehavior("B2", "Title2", null, BehaviorCategory.HONESTY, 2);
        occupation.addBehavior(behavior);
        occupation.addBehavior(behavior2);
        occupation.setBehaviorCategory1Overloaded(true);
        occupation.setBehaviorCategory1(BehaviorCategory.RIGOR);
        occupation.setBehaviorCategory2Overloaded(true);
        occupation.setBehaviorCategory2(BehaviorCategory.CONSTANCY);

        occupation.updateBehaviorCategory(null, 0);

        assertThat(occupation.getBehaviorCategory1()).isEqualTo(BehaviorCategory.HONESTY);
        assertThat(occupation.isBehaviorCategory1Overloaded()).isFalse();
    }

    @Test
    void updateBehaviorCategory_second_category() {
        var occupation = ErhgoOccupation.builder().build();
        occupation.updateBehaviorCategory(BehaviorCategory.HONESTY, 1);
        assertThat(occupation.getBehaviorCategory2()).isEqualTo(BehaviorCategory.HONESTY);
        assertThat(occupation.isBehaviorCategory2Overloaded()).isTrue();

    }

    @Test
    void updateQualificationState() {
        var occupation = ErhgoOccupation.builder().build();
        assertThat(occupation.getQualificationState()).isEqualTo(ErhgoOccupationState.NONE);
        occupation.qualifyOccupation();
        assertThat(occupation.getQualificationState()).isEqualTo(ErhgoOccupationState.QUALIFIED_V3_CONFIRMED);
        occupation.unqualifyOccupation();
        assertThat(occupation.getQualificationState()).isEqualTo(ErhgoOccupationState.TO_CONFIRM);
    }


    @ParameterizedTest
    @ArgumentsSource(ErhgoOccupationStateArgumentProvider.class)
    void getByActivitiesAndQualifiedSkills(long activitiesCount, double qualifiedSkillsPercent, ErhgoOccupationState expected) {
        assertThat(ErhgoOccupationState.getByActivitiesAndQualifiedSkills(activitiesCount, qualifiedSkillsPercent)).isEqualTo(expected);
    }

    public static class ErhgoOccupationStateArgumentProvider implements ArgumentsProvider {

        @Override
        public Stream<? extends Arguments> provideArguments(ExtensionContext context) {

            return Stream.of(
                    Arguments.of(1L, 1d, ErhgoOccupationState.NONE),
                    Arguments.of(10L, 1d, ErhgoOccupationState.VERY_HIGH),
                    Arguments.of(7L, 1d, ErhgoOccupationState.HIGH),
                    Arguments.of(7L, 0.3d, ErhgoOccupationState.MEDIUM),
                    Arguments.of(4L, 1d, ErhgoOccupationState.MEDIUM),
                    Arguments.of(4L, 0.2d, ErhgoOccupationState.LOW),
                    Arguments.of(2L, 1d, ErhgoOccupationState.LOW),
                    Arguments.of(2L, 0.01d, ErhgoOccupationState.NONE)
            );
        }
    }

    @Test
    void addLabel_should_sanitized_label() {
        var occupation = new ErhgoOccupationMotherObject().withTitle("previous").instance();
        occupation.setTitleAndAlternativeLabels("a/b", Lists.newArrayList("c/d", "e/f"));
        assertThat(occupation.getTitle()).isEqualTo("A / B");
        assertThat(Lists.newArrayList(occupation.getAlternativeLabels())).containsExactlyInAnyOrder("C / D", "E / F");
    }

    @ParameterizedTest
    @CsvSource({
            "boulanger,Boulanger",
            "boulanger/boulangere,Boulanger / Boulangere",
            "  boulanger /     boulangere ,Boulanger / Boulangere",
            " toiletteur canin/  toiletteuse canin  ,Toiletteur canin / Toiletteuse canin",

    })
    void fixTitle_upperCaseFirstLetter(String title, String expectedTitle) {
        var occupation = new ErhgoOccupationMotherObject().withTitle(title).instance();
        assertThat(occupation.fixTitles()).isTrue();
        assertThat(occupation.getTitle()).isEqualTo(expectedTitle);
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "Opérateur de centre de données de calcul",
            "Plombier / Plombière",
    })
    void isNotFixingTitle(String title) {
        var occupation = new ErhgoOccupationMotherObject().withTitle(title).instance();
        assertThat(occupation.fixTitles()).isFalse();
        assertThat(occupation.getTitle()).isEqualTo(title);
    }

    @ParameterizedTest
    @CsvSource({
            "opérateur de centre de données de calcul,Opérateur de centre de données de calcul",
            "opérateur de centre de données de calcul /Opérateur de centre de données de calcul,Opérateur de centre de données de calcul",
            "opérateur de centre de Données de calcul,Opérateur de centre de Données de calcul",
            "/opérateur de centre de données de calcul / opératrice// ,Opérateur de centre de données de calcul / Opératrice",
    })
    void fixLabel(String title, String expectedTitle) {
        var occupation = new ErhgoOccupationMotherObject().withLabels(title).instance();
        assertThat(occupation.fixTitles()).isTrue();
        assertLabelsContains(occupation.getAlternativeLabels(), expectedTitle);
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "Opérateur de centre de données de calcul",
            "Plombier / Plombière",
    })
    void isNotFixingLabel(String title) {
        var occupation = new ErhgoOccupationMotherObject().withLabels(title).instance();
        assertThat(occupation.fixTitles()).isFalse();
        assertLabelsContains(occupation.getAlternativeLabels(), title);
    }

    // Next method to erase alternativeLabel SortedSet custom comparator (ignoring spaces, cases & diatribes) behavior
    private void assertLabelsContains(Collection<String> alternativeLabels, String... labels) {
        assertThat(Lists.newArrayList(alternativeLabels)).containsExactly(labels);
    }

    @Test
    void add_criteria_does_not_tolerate_multiple_threshold_values_on_same_criteria() {
        var occupation = new ErhgoOccupationMotherObject().instance();
        var criteria = new CriteriaMotherObject().withValueCodes("A", "B").withQuestionType(CriteriaQuestionType.THRESHOLD).build();
        var values = criteria.getCriteriaValues();
        try {
            occupation.resetCriteriaValues(values);
            fail("Exception should be thrown");
        } catch (MultipleThresholdValuesException e) {
            // OK
        }
    }
}
