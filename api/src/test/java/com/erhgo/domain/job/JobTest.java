package com.erhgo.domain.job;

import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.JobActivityLabelGenerator;
import com.erhgo.generators.JobGenerator;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class JobTest {

    private static final Capacity capacity = CapacityGenerator.buildCapacity();

    static {
        capacity.setId(45L);
    }

    @Test
    void getLevelsForActivities_should_return_empty_map_for_empty_job() {
        Job job = new Job();
        var levels = job.getLevelsForActivities();
        assertThat(levels).isEmpty();
    }

    @Test
    void getLevelsForActivities_for_no_context_job_and_one_activity_should_return_single_activity_with_level_1() {
        JobActivityLabel activity = JobActivityLabelGenerator.buildActivityWithCapacities(capacity);
        Job job = JobGenerator.buildJobWithActivityAndNoContext(activity);
        var levels = job.getLevelsForActivities();
        assertThat(levels).containsOnlyKeys(activity.getActivity());
        assertThat(levels).containsValues(Job.LevelForActivity.builder().level(1).referenceMission(job.getMissions().first()).build());
    }

    @Test
    void getLevelsForActivities_should_keep_highest_level_for_activity() {
        JobActivityLabel activity = JobActivityLabelGenerator.buildActivityWithCapacities(capacity);
        Job job = JobGenerator.buildNewJob();

        JobGenerator.buildMissionForJobAndActivityWithScore(10, activity, job);
        Mission expectedMission = JobGenerator.buildMissionForJobAndActivityWithScore(91, activity, job);

        var levels = job.getLevelsForActivities();
        assertThat(levels).containsOnlyKeys(activity.getActivity());
        assertThat(levels).containsValues(Job.LevelForActivity.builder().level(5).referenceMission(expectedMission).build());
    }

    @Test
    void refreshLevel_should_set_level_to_null_for_empty_job() {
        var job = JobGenerator.buildNewJob();
        job.refreshLevel();
        assertThat(job.getMasteryLevel()).isNull();
    }

    @Test
    void refreshLevel_should_set_level_to_mission_level_for_single_mission_job() {
        var expectedLevel = MasteryLevel.COMPLEX;
        var job = JobGenerator.buildJobWithOneContextPerMission(JobGenerator.buildContextWithScore(42));
        job.refreshLevel();
        assertThat(job.getMasteryLevel()).isEqualTo(expectedLevel);
    }

    @Test
    void refreshLevel_should_set_level_to_max_mission_level_for_multiple_missions_job() {
        var expectedLevel = MasteryLevel.COMPLEX;
        var job = JobGenerator.buildJobWithOneContextPerMission(JobGenerator.buildContextWithScore(42), JobGenerator.buildContextWithScore(12));
        job.refreshLevel();
        assertThat(job.getMasteryLevel()).isEqualTo(expectedLevel);
    }
}
