package com.erhgo.domain.job;

import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.JobEvaluationState;
import com.erhgo.domain.enums.TypeContractCategory;
import com.erhgo.domain.enums.TypeWorkingTime;
import com.erhgo.domain.referential.*;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.CriteriaMotherObject;
import com.erhgo.generators.JobActivityLabelGenerator;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.repositories.JobMissionRepository;
import com.erhgo.repositories.JobRepository;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Stream;

@Service
@Scope("prototype")
@NoArgsConstructor
public class JobMotherObject {

    @Autowired
    private JobRepository jobRepository;
    @Autowired
    private JobMissionRepository jobMissionRepository;
    @Autowired
    private OrganizationGenerator organizationGenerator;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private CapacityGenerator capacityGenerator;
    private final Set<CriteriaValue> criteriaValues = new HashSet<>();
    private final Set<JobActivityLabel> activities = new HashSet<>();
    private final Job job = new Job();


    @Transactional
    public Job buildAndPersist() {
        var job = build();
        var missions = Sets.newHashSet(job.getMissions());
        job.getMissions().clear();
        job = jobRepository.save(job);
        jobMissionRepository.saveAll(missions);
        missions.forEach(job::addMission);
        return job;
    }

    public Job build() {
        if (job.getId() == null) {
            job.setId(UUID.randomUUID());
        }
        if (Strings.isNullOrEmpty(job.getTitle())) {
            job.setTitle("Job with UUID " + job.getId().toString());
        }
        if (job.getRecruiter() == null && organizationGenerator != null) {
            // TODO : remplacer cet appel à organizationGenerator par un RecruiterMotherObject
            job.setRecruiter(organizationGenerator.createRecruiterWithGeneratedCode());
        }
        if (job.getJobType() == null) {
            job.setJobType(JobType.SIMPLE);
        }
        if (!criteriaValues.isEmpty()) {
            job.resetCriteriaValues(criteriaValues);
        }
        if (job.getMissions().isEmpty() && capacityGenerator != null) {
            withCapacities(capacityGenerator.createCapacity("CA1-42" + UUID.randomUUID().toString()));
        }
        return job;
    }

    public JobMotherObject withEmployerTitle(String title) {
        this.job.getEmployer().setTitle(title);
        return this;
    }

    public JobMotherObject withTitle(String title) {
        this.job.setTitle(title);
        return this;
    }

    public JobMotherObject withService(String service) {
        this.job.setService(service);
        return this;
    }

    public JobMotherObject withRecruiter(Recruiter recruiter) {
        this.job.setRecruiter(recruiter);
        return this;
    }

    public JobMotherObject withEmployer(Employer employer) {
        this.job.setEmployer(employer);
        return this;
    }

    public JobMotherObject withJobType(JobType jobType) {
        this.job.setJobType(jobType);
        return this;
    }

    public JobMotherObject withCriteriaValues(CriteriaValue... criteriaValues) {
        this.criteriaValues.addAll(Arrays.asList(criteriaValues));
        return this;
    }

    public JobMotherObject withState(JobEvaluationState state) {
        this.job.setState(state);
        return this;
    }

    public JobMotherObject withWorkingTimes(TypeWorkingTime... workingTimes) {
        var workingTimeCriteria = applicationContext.getBean(CriteriaMotherObject.class)
                .withWorkingTimeCriteria()
                .buildAndPersist();

        withCriteriaValues(
                workingTimeCriteria
                        .getCriteriaValues()
                        .stream()
                        .filter(cv -> Stream.of(workingTimes).anyMatch(c -> c == CriteriaValue.WORKING_TIME_FOR_CRITERIA_RESPONSE.get(cv.getCode())))
                        .toArray(CriteriaValue[]::new));
        return this;
    }

    public JobMotherObject withLevel(MasteryLevel level) {
        this.job.setMasteryLevel(level);
        return this;
    }

    public JobMotherObject withCapacities(Capacity... capacities) {
        var jobActivityLabelGenerator = applicationContext.getBean(JobActivityLabelGenerator.class);
        new Mission(
                job,
                "Mission title",
                Sets.newHashSet(jobActivityLabelGenerator.createActivity("Act for job ", capacities[0], Arrays.copyOfRange(capacities, 1, capacities.length))),
                new HashSet<>());
        return this;
    }

    public JobMotherObject withType(JobType jobType) {
        job.setJobType(jobType);
        return this;
    }

    public JobMotherObject withContractCategories(TypeContractCategory... typeContracts) {
        var typeContractCriteria = applicationContext.getBean(CriteriaMotherObject.class)
                .withTypeContractCriteria()
                .buildAndPersist();

        withCriteriaValues(
                typeContractCriteria
                        .getCriteriaValues()
                        .stream()
                        .filter(cv -> Stream.of(typeContracts).anyMatch(c -> c == CriteriaValue.TYPE_CONTRACT_FOR_CRITERIA_RESPONSE.get(cv.getCode())))
                        .toArray(CriteriaValue[]::new));
        return this;
    }

    public JobMotherObject withLocation(Location location) {
        job.setLocation(location);
        return this;
    }

    public JobMotherObject withOccupation(ErhgoOccupation occupation) {
        job.setErhgoOccupation(occupation);
        return this;
    }

    public JobMotherObject withBehaviors(Behavior... behaviors) {
        job.setBehaviors(Set.of(behaviors));
        return this;
    }

    public JobMotherObject withMissionActivities(JobActivityLabel... activities) {
        new Mission(
                job,
                "Mission with activities title ",
                Set.of(activities),
                new HashSet<>());
        return this;
    }
}
