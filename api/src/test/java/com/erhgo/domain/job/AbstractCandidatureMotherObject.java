package com.erhgo.domain.job;

import com.erhgo.TransactionTestHelper;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.candidature.job.*;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.CandidatureSynchronizationState;

import com.erhgo.domain.enums.Situation;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.utils.DateTimeUtils;
import jakarta.annotation.PostConstruct;
import jakarta.persistence.EntityManager;
import org.junit.jupiter.api.Assertions;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.repository.CrudRepository;
import org.springframework.test.util.ReflectionTestUtils;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

public abstract class AbstractCandidatureMotherObject<A extends AbstractCandidature, B extends AbstractCandidatureMotherObject<A, B>> {

    @Autowired
    TransactionTestHelper txHelper;
    @Autowired
    EntityManager entityManager;
    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    KeycloakMockService keycloakMockService;
    CrudRepository<A, Long> repository;

    private Situation situation;
    private String firstName, lastName, phone;
    Recruiter recruiter;
    private OffsetDateTime lastActionDate;
    private String userId;
    private Location userLocation;
    protected String recruiterCode;

    A candidature;
    private String userphone;
    private LocalDateTime lastConnectionDate;
    private String email;
    private boolean checkXp;
    private CandidatureSynchronizationState synchronizationState;
    private static int capacityIndex = 0;
    AbstractCandidatureMotherObject(A candidature) {
        this.candidature = candidature;
    }

    @PostConstruct
    void initBean() {
        this.repository = getRepository();
    }

    protected abstract <C extends CrudRepository<A, Long>> C getRepository();

    public A build() {
        return candidature;
    }

    public A buildAndPersist() {
        var ref = new AtomicReference<A>();
        Map<String, Date> dateForNote = candidature.getCandidatureNotes().stream().filter(n -> n.getUpdatedDate() != null).collect(Collectors.toMap(CandidatureNote::getText, AbstractAuditableEntity::getUpdatedDate));
        txHelper.doInTransaction(() -> {
            var candidatureToPersist = build();
            customBuildAndPersist(candidatureToPersist);
            if (candidatureToPersist.getUserProfile() != null && checkXp) {
                Assertions.fail();
            }
            if (candidatureToPersist.getUserProfile() == null) {
                var userMotherObject = applicationContext.getBean(UserProfileMotherObject.class);
                var userProfileMotherObject = userMotherObject.withUserId(userId).withEmail(email);
                if (checkXp) {
                    userMotherObject.withExperience(applicationContext.getBean(ErhgoOccupationMotherObject.class).withCapacities(applicationContext.getBean(CapacityGenerator.class).createCapacity("CA5-%d".formatted(capacityIndex++))).buildAndPersist(), 3);
                }
                candidatureToPersist.setUserProfile(userProfileMotherObject.buildAndPersist());
            } else if (userId != null) {
                candidatureToPersist.getUserProfile().userId(userId);
            }
            if (situation != null) {
                candidatureToPersist.getUserProfile().generalInformation().setSituation(situation);
            }
            if (phone != null) {
                candidatureToPersist.getUserProfile().generalInformation().setPhoneNumber(phone);
            }
            if (firstName != null) {
                var userIdLocal = candidatureToPersist.getUserProfile().userId();
                var userRepresentation = new UserRepresentation()
                        .setEmail(firstName + "@" + lastName)
                        .setFirstName(firstName)
                        .setLastName(lastName)
                        .setId(userIdLocal);
                // Handle bad keycloak interface - either Mockito mock, either our manual mock
                if (Mockito.mockingDetails(keycloakMockService).isMock()) {
                    Mockito.when(keycloakMockService.getFrontOfficeUserProfile(userIdLocal))
                            .thenReturn(Optional.of(userRepresentation));
                } else {
                    keycloakMockService.setUserProfile(userIdLocal, userRepresentation);
                }

            }
            if (candidatureToPersist.getGlobalCandidatureState() == null) {
                candidature.setGlobalCandidatureState(GlobalCandidatureState.NEW);
            }
            if (recruiterCode != null) {
                candidature.getRecruiter().setCode(recruiterCode);
            }
            if (userLocation != null) {
                candidatureToPersist.getUserProfile().generalInformation().setLocation(userLocation);
            }
            if (userphone != null) {
                candidatureToPersist.getUserProfile().generalInformation().setPhoneNumber(userphone);
            }
            if (lastConnectionDate != null) {
                candidature.getUserProfile().lastConnectionDate(lastConnectionDate);
            }
            if (synchronizationState != null) {
                candidature.setSynchronizationState(synchronizationState);
            }
            ref.set(repository.save(candidatureToPersist));
        });

        dateForNote.forEach((k, v) -> txHelper.doInTransaction(() -> {
            var sql = "UPDATE CandidatureNote SET updatedDate = :date, createdDate = :date WHERE text = :text";
            entityManager.joinTransaction();
            entityManager.createNativeQuery(sql)
                    .setParameter("date", v)
                    .setParameter("text", k)
                    .executeUpdate();
        }));

        if (lastActionDate != null) {
            txHelper.doInTransaction(() -> {
                var sql = "UPDATE %sCandidature SET updatedDate = :date WHERE id = :id".formatted(candidature instanceof SpontaneousCandidature ? "Spontaneous" : "Recruitment");
                entityManager.joinTransaction();
                entityManager.createNativeQuery(sql)
                        .setParameter("id", candidature.getId())
                        .setParameter("date", lastActionDate)
                        .executeUpdate();
            });
            candidature.setUpdatedDate(Timestamp.from(lastActionDate.toInstant().truncatedTo(ChronoUnit.SECONDS)));
        }

        return ref.get();
    }

    abstract void customBuildAndPersist(A candidature);

    public B withUserProfile(UserProfile userProfile) {
        candidature.setUserProfile(userProfile);
        return (B) this;
    }


    public B withState(GlobalCandidatureState state) {
        if (state != null && state.name().contains("REFUSED")) {
            candidature.markAsRefused(CandidatureEmailRefusalState.NONE, "me");
        }
        candidature.setGlobalCandidatureState(state);
        return (B) this;
    }

    public B withRefusalData(CandidatureEmailRefusalState state, String who) {
        if (state != null) candidature.markAsRefused(state, who);
        return (B) this;
    }

    public B withNoteAtDate(String note, LocalDateTime date) {
        if (this.candidature.getCandidatureNotes() == null) {
            this.candidature.setCandidatureNotes(new ArrayList<>());
        }
        var candidatureNote = CandidatureNote.builder()
                .candidature(this.candidature)
                .text(note)
                .build();
        this.candidature.getCandidatureNotes().add(candidatureNote);
        candidatureNote.setUpdatedDate(DateTimeUtils.localDateToDate(date));
        return (B) this;
    }

    public B withSituation(Situation value) {
        this.situation = value;
        return (B) this;
    }


    public B withColor(String color) {
        candidature.setColor(color);
        return (B) this;
    }

    public B withAnonymousCode(String code) {
        candidature.setAnonymousCode(code);
        return (B) this;
    }

    public B withFullnameComputingEmail(String firstName, String lastName) {
        this.firstName = firstName;
        this.lastName = lastName;
        return (B) this;
    }

    public B withPhone(String phone) {
        this.phone = phone;
        return (B) this;
    }

    public B withNote(String content) {
        var note = CandidatureNote.builder()
                .candidature(candidature)
                .text(content)
                .build();
        candidature.getCandidatureNotes().add(note);
        return (B) this;
    }


    public B withSubmissionDate(OffsetDateTime submissionDate) {
        candidature.setSubmissionDate(submissionDate);
        return (B) this;
    }


    public B withRecruiter(Recruiter recruiter) {
        this.recruiter = recruiter;
        return (B) this;
    }

    public B withLastActionDate(OffsetDateTime offsetDateTime) {
        this.lastActionDate = offsetDateTime;
        return (B) this;
    }

    public B withUserId(String userId) {
        this.userId = userId;
        return (B) this;
    }

    public B withRecruiterCode(String orgaCode) {
        this.recruiterCode = orgaCode;
        return (B) this;
    }
    public B withIsArchived(boolean isArchived) {
        this.candidature.setArchived(isArchived);
        return (B) this;
    }

    public B withRefusalDate(int numberOfMonth) {
        var date = OffsetDateTime.now().minusMonths(numberOfMonth);
        ReflectionTestUtils.setField(this.candidature.getCandidatureRefusalState(), "refusalDate", date);
        withLastActionDate(date);
        return (B) this;
    }

    public B withUserLocation(Location location) {
        this.userLocation = location;
        return (B) this;
    }

    public B withUserPhone(String phone) {
        this.userphone = phone;
        return (B) this;
    }

    public B withLastConnectionDate(LocalDateTime lastConnectionDate) {
        this.lastConnectionDate = lastConnectionDate;
        return (B) this;
    }

    public B withEmail(String email) {
        this.email = email;
        return (B) this;
    }

    public B withCandidatureSynchronizationState(CandidatureSynchronizationState s) {
        this.synchronizationState = s;
        return (B) this;
    }

    public B withAtLeastOneXp() {
        this.checkXp = true;
        return (B) this;
    }

}
