package com.erhgo.domain.job;

import com.erhgo.domain.recruitment.RecruitmentProfile;
import com.erhgo.repositories.RecruitmentProfileRepository;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Scope("prototype")
@NoArgsConstructor
public class RecruitmentProfileMotherObject {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private RecruitmentProfileRepository recruitmentProfileRepository;

    private Job job;

    @Transactional
    public RecruitmentProfile buildAndPersist() {
        var recruitmentProfile = build();
        return recruitmentProfileRepository.save(recruitmentProfile);
    }

    public RecruitmentProfile build() {
        if (this.job == null) {
            this.job = (applicationContext == null ? new JobMotherObject().build() : applicationContext.getBean(JobMotherObject.class).buildAndPersist());
        }
        return RecruitmentProfile.buildWithAllOptional(this.job, null);
    }

    public RecruitmentProfileMotherObject withJob(Job job) {
        this.job = job;
        return this;
    }

}
