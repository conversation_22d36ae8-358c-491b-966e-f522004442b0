package com.erhgo.domain;

import com.erhgo.domain.enums.RecruitmentSendNotificationState;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.recruitment.Recruitment;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import java.time.Instant;

class RecruitmentTest {

    @ParameterizedTest
    @CsvSource({
            "PUBLISHED,WAITING,WAITING",
            "DRAFT,ERROR,ERROR",
            "DRAFT,DONE,DONE",
            "PUBLISHED,,",
            "SELECTION,WAITING,CANCEL",
            "CLOSED,WAITING,CANCEL",
            "UNPUBLISHED,WAITING,CANCEL",
            "DRAFT,WAITING,CANCEL",
            "CLOSED,,"
    })
    void setState_verifySendNotificationState(RecruitmentState recruitmentState, RecruitmentSendNotificationState initialMailState, RecruitmentSendNotificationState expectedState) {
        var recruitment = new Recruitment();
        recruitment.setSendNotificationState(initialMailState);
        var now = Instant.now();
        recruitment.setSendNotificationDate(initialMailState == null ? null : now);
        recruitment.setState(recruitmentState, false);
        Assertions.assertEquals(expectedState, recruitment.getSendNotificationState());
        Assertions.assertEquals((recruitmentState != RecruitmentState.PUBLISHED && initialMailState == RecruitmentSendNotificationState.WAITING) || initialMailState == null ? null : now, recruitment.getSendNotificationDate());
    }
}
