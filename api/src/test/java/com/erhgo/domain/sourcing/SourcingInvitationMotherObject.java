package com.erhgo.domain.sourcing;

import com.erhgo.TransactionTestHelper;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.repositories.SourcingInvitationRepository;
import jakarta.persistence.EntityManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;

@Service
@Scope("prototype")
public class SourcingInvitationMotherObject {

    @Autowired
    private SourcingInvitationRepository sourcingInvitationRepository;

    @Autowired
    private TransactionTestHelper txHelper;

    @Autowired
    private EntityManager entityManager;

    @Autowired
    private ApplicationContext applicationContext;

    private final SourcingInvitation invitation;

    private final Set<Recruiter> guests = new HashSet<>();
    private String creatorUserId;

    public SourcingInvitationMotherObject() {
        this.invitation = SourcingInvitation.builder().build();
    }


    public SourcingInvitation build() {
        if (invitation.getCode() == null) {
            withCode("42");
        }
        if (invitation.getHost() == null) {
            withHost(applicationContext.getBean(OrganizationGenerator.class).createRecruiter("host"));
        }
        if (invitation.getMaxNumberOfGuests() == 0) {
            withMaxNumberOfGuests(5);
        }
        if (invitation.getDuration() == 0) {
            withDuration(6);
        }

        guests.stream().map(g -> SourcingSubscription.builder().recruiter(g).build()).forEach(s -> s.acceptInvitation(invitation));
        return invitation;
    }

    public SourcingInvitationMotherObject withHost(Recruiter host) {
        ReflectionTestUtils.setField(invitation, "host", host);
        return this;
    }


    public SourcingInvitation buildAndPersist() {
        var ref = new AtomicReference<SourcingInvitation>();
        txHelper.doInTransaction(() -> {
            var invitation = build();
            ref.set(sourcingInvitationRepository.save(invitation));
            ref.get().getSubscriptions().forEach(entityManager::persist);
        });
        if (creatorUserId != null) {
            txHelper.doInTransaction(() -> {
                entityManager.createNativeQuery("UPDATE SourcingInvitation SET createdBy_keycloakId = :userId")
                        .setParameter("userId", creatorUserId)
                        .executeUpdate();
            });
        }
        return ref.get();
    }

    public SourcingInvitationMotherObject withMaxNumberOfGuests(int quota) {
        ReflectionTestUtils.setField(invitation, "maxNumberOfGuests", quota);
        return this;
    }

    public SourcingInvitationMotherObject withCreatedBy(String creatorUserId) {
        this.creatorUserId = creatorUserId;
        return this;
    }

    public SourcingInvitationMotherObject withDuration(int duration) {
        ReflectionTestUtils.setField(invitation, "duration", duration);
        return this;
    }

    public SourcingInvitationMotherObject withGuests(Recruiter... recruiters) {
        guests.addAll(Arrays.asList(recruiters));
        return this;
    }

    public SourcingInvitationMotherObject withCode(String code) {
        ReflectionTestUtils.setField(invitation, "code", code);
        return this;
    }

    public SourcingInvitationMotherObject withUsageForOrganization(AbstractOrganization organization) {
        guests.add((Recruiter) organization);
        return this;
    }
}
