package com.erhgo.domain.sourcing;

import com.erhgo.TransactionTestHelper;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.repositories.SourcingSubscriptionRepository;
import jakarta.persistence.EntityManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.OffsetDateTime;
import java.util.Date;
import java.util.concurrent.atomic.AtomicReference;

@Service
@Scope("prototype")
public class SourcingSubscriptionMotherObject {

    @Autowired
    private SourcingSubscriptionRepository sourcingSubscriptionRepository;

    @Autowired
    private TransactionTestHelper txHelper;

    @Autowired
    private ApplicationContext applicationContext;

    private final SourcingSubscription subscription;
    private Date createdDate;

    public SourcingSubscriptionMotherObject() {
        this.subscription = SourcingSubscription.builder().build();
    }


    public SourcingSubscription build() {
        if (subscription.getRecruiter() == null) {
            withRecruiter(applicationContext.getBean(OrganizationGenerator.class).createRecruiter("host"));
        }
        if (subscription.getExpirationDate() == null) {
            withExpirationDate(null);
        }
        return subscription;
    }


    public SourcingSubscription buildAndPersist() {
        var ref = new AtomicReference<SourcingSubscription>();
        txHelper.doInTransaction(() -> {
            var subscription = build();
            ref.set(sourcingSubscriptionRepository.save(subscription));
        });
        if (createdDate != null) {
            txHelper.doInTransaction(() -> {
                applicationContext.getBean(EntityManager.class)
                        .createNativeQuery("UPDATE SourcingSubscription SET createdDate = :date WHERE uuid = :id")
                        .setParameter("date", createdDate)
                        .setParameter("id", ref.get().getUuid())
                        .executeUpdate();
            });
        }
        return ref.get();
    }

    public SourcingSubscriptionMotherObject withExpirationDate(OffsetDateTime expirationDate) {
        ReflectionTestUtils.setField(subscription, "expirationDate", expirationDate);
        return this;
    }

    public SourcingSubscriptionMotherObject withRecruiter(Recruiter recruiter) {
        ReflectionTestUtils.setField(subscription, "recruiter", recruiter);
        return this;
    }

    public SourcingSubscriptionMotherObject withOrganizationCode(String organizationCode) {
        ReflectionTestUtils.setField(subscription, "organizationCode", organizationCode);
        return this;
    }

    public SourcingSubscriptionMotherObject withOrganizationId(Long organizationId) {
        ReflectionTestUtils.setField(subscription, "organizationId", organizationId);
        return this;
    }

    public SourcingSubscriptionMotherObject withMailState(SourcingSubscription.SourcingMailState mailState) {
        ReflectionTestUtils.setField(subscription, "mailState", mailState);
        return this;
    }

    public SourcingSubscriptionMotherObject withCreatedDate(Date date) {
        createdDate = date;
        return this;
    }
}
