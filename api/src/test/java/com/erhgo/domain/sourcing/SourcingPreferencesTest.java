package com.erhgo.domain.sourcing;

import com.erhgo.domain.userprofile.SourcingPreferencesMotherObject;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.temporal.ChronoField;

class SourcingPreferencesTest {

    @Test
    void isActiveToday_daily() {
        Assertions.assertThat(new SourcingPreferencesMotherObject().withMailFrequency(SourcingPreferences.MailFrequency.DAILY).build().isActiveToday()).isTrue();
    }

    @Test
    void isActiveToday_immediately() {
        Assertions.assertThat(new SourcingPreferencesMotherObject().withMailFrequency(SourcingPreferences.MailFrequency.IMMEDIATELY).build().isActiveToday()).isFalse();
    }

    @Test
    void isActiveToday_activeWeekDay() {
        Assertions.assertThat(new SourcingPreferencesMotherObject().withMailFrequency(SourcingPreferences.MailFrequency.WEEKLY).withIsoWeekDay(LocalDate.now().get(ChronoField.DAY_OF_WEEK)).build().isActiveToday()).isTrue();
    }

    @Test
    void isActiveToday_inactiveWeekDay() {
        Assertions.assertThat(new SourcingPreferencesMotherObject().withMailFrequency(SourcingPreferences.MailFrequency.WEEKLY).withIsoWeekDay((LocalDate.now().get(ChronoField.DAY_OF_WEEK) + 1) % 7).build().isActiveToday()).isFalse();
    }

}
