package com.erhgo.domain.referential;

import com.erhgo.generators.OrganizationGenerator;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class EmployerTest {

    @DisplayName("Create employer with wrong referer type")
    @Test
    void createEmployer_fail() {
        var builder = Employer.employerBuilder()
                .title("t")
                .description("d")
                .refererRecruiter(OrganizationGenerator.buildRecruiter(AbstractOrganization.OrganizationType.ENTERPRISE));
        Assertions.assertThrows(IllegalArgumentException.class, builder::build);
    }

    @DisplayName("Create employer")
    @Test
    void createEmployer() {
        var employer = Employer.employerBuilder()
                .title("t")
                .description("d")
                .refererRecruiter(OrganizationGenerator.buildRecruiter(AbstractOrganization.OrganizationType.CONSORTIUM))
                .build();
        assertThat(employer).isInstanceOf(Employer.class);
    }
}
