package com.erhgo.domain.referential;

import com.erhgo.domain.enums.QuestionType;
import com.google.common.base.Strings;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Set;
import java.util.UUID;

public class CapacityRelatedQuestionMotherObject {

    private CapacityRelatedQuestion question;

    public CapacityRelatedQuestionMotherObject() {
        this.question = CapacityRelatedQuestion.builder().id(UUID.randomUUID()).build();
    }

    public CapacityRelatedQuestion instance() {
        addRequiredFields();
        return question;
    }

    private void addRequiredFields() {
        if (question.getResponses() == null || question.getResponses().isEmpty()) {
            CapacityRelatedQuestionResponse.builder()
                    .title("Answer 1")
                    .position(0)
                    .question(question)
                    .id(UUID.randomUUID())
                    .build();
        }
        if (question.getResponses().size() < 2) {
            CapacityRelatedQuestionResponse.builder()
                    .title("Answer 2")
                    .position(1)
                    .question(question)
                    .id(UUID.randomUUID())
                    .build();
        }
        if (Strings.isNullOrEmpty(question.getTitle())) {
            ReflectionTestUtils.setField(question, "title", "Title of " + question.getId());
        }
        if (question.getQuestionType() == null) {
            ReflectionTestUtils.setField(question, "questionType", QuestionType.EXTRAPROFESSIONAL);
        }
    }

    public CapacityRelatedQuestionMotherObject withAnswerForCapacity(Capacity capacity) {

        CapacityRelatedQuestionResponse.builder()
                .title("Answer on " + capacity.getCode())
                .id(UUID.randomUUID())
                .position(0)
                .question(question)
                .capacities(Set.of(capacity))
                .build();
        return this;
    }
}
