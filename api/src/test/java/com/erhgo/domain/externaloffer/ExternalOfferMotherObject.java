package com.erhgo.domain.externaloffer;

import com.erhgo.TransactionTestHelper;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.repositories.ExternalOfferRepository;
import jakarta.persistence.EntityManager;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.IntStream;

@Service
@Scope("prototype")
@NoArgsConstructor
public class ExternalOfferMotherObject {
    private final ExternalOffer externalOffer = new ExternalOffer()
            .setRecruitmentCreationState(RecruitmentCreationState.PROCESSING);
    @Autowired
    private ExternalOfferRepository repository;
    @Autowired
    private EntityManager entityManager;
    @Autowired
    private TransactionTestHelper txHelper;

    public ExternalOffer buildAndPersist() {
        List<String> historyItemsIds = new ArrayList<>();
        AtomicReference<ExternalOffer> result = new AtomicReference<>();
        txHelper.doInTransaction(() -> {
            var rec = externalOffer.getRecruitment();
            var persisted = repository.save(externalOffer);
            historyItemsIds.addAll(entityManager.createNativeQuery("SELECT BinToUuid(uuid) FROM ExternalOfferContentHistoryItem WHERE externalOffer_id = :id").setParameter("id", persisted.getUuid()).getResultList());
            result.set(persisted);
        });
        txHelper.doInTransaction(() ->
                // A bit complex: ensure no element in the treeset have same updatedDate value as any other
                // Otherwise : treeset would remove one of the elements at the same date
                IntStream.range(0, historyItemsIds.size()).forEach(i -> {
                    var sql = "UPDATE ExternalOfferContentHistoryItem SET updatedDate = :date WHERE BinToUuid(uuid) = :id";
                    entityManager.joinTransaction();
                    entityManager.createNativeQuery(sql)
                            .setParameter("date", new Date(100_000_000_000L * i))
                            .setParameter("id", historyItemsIds.get(i))
                            .executeUpdate();
                    entityManager.flush();
                })
        );

        return result.get();
    }

    public ExternalOfferMotherObject withRemoteId(String id) {
        this.externalOffer.setRemoteId(id);
        return this;
    }

    public ExternalOfferMotherObject withRecruitment(Recruitment recruitment) {
        externalOffer.setRecruitment(recruitment);
        return this;
    }


    public ExternalOfferMotherObject withRemoteModificationDate(LocalDateTime dateTime) {
        externalOffer.setRemoteLastModificationDate(dateTime);
        return this;
    }

    public ExternalOfferMotherObject withRawContent(String xml) {
        ReflectionTestUtils.setField(externalOffer, "lastRawContent", xml);
        return this;
    }

    public ExternalOfferMotherObject withLastEventType(ExternalOfferEventType lastEventType) {
        externalOffer.setLastEventType(lastEventType);
        return this;
    }

    public ExternalOfferMotherObject withUuid(String uuid) {
        externalOffer.setUuid(UUID.fromString(uuid));
        return this;
    }

    public ExternalOfferMotherObject withPreviousXmls(String... xmls) {
        IntStream.range(0, xmls.length).mapToObj(i -> {
            var o = new ExternalOfferContentHistoryItem(xmls[i]);
            o.setUpdatedDate(new Date(i * 10_000_000_000L));
            return o;
        }).forEach(e -> externalOffer.getExternalOfferContentHistory().add(e));
        return this;
    }

    public ExternalOfferMotherObject withATSCode(String atsCode) {
        externalOffer.setAtsCode(atsCode);
        return this;
    }

    public ExternalOfferMotherObject withOfferTitle(String title) {
        externalOffer.setOfferTitle(title);
        return this;
    }

    public ExternalOfferMotherObject withOfferLocation(String location) {
        externalOffer.setOfferLocation(location);
        return this;
    }

    public ExternalOfferMotherObject withRelatedUserNames(String relatedUserNames) {
        externalOffer.setRelatedUsernames(relatedUserNames);
        return this;
    }

    public ExternalOfferMotherObject withRecruiterCode(String recruiterCode) {
        externalOffer.setComputedRecruiterCode(recruiterCode);
        return this;
    }

    public ExternalOfferMotherObject withConfigCode(String configCode) {
        externalOffer.setConfigCode(configCode);
        return this;
    }

    public ExternalOfferMotherObject withCandidatureEmail(String externalOfferEmailToNotify) {
        externalOffer.setCandidatureEmail(externalOfferEmailToNotify);
        return this;
    }

    public ExternalOfferMotherObject withRecruitmentCreationState(RecruitmentCreationState state) {
        externalOffer.setRecruitmentCreationState(state);
        return this;
    }
}
