package com.erhgo.domain.externaloffer;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TransactionTestHelper;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.repositories.RecruitmentRepository;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import static org.assertj.core.api.Assertions.assertThat;

class ExternalOfferCascadeTest extends AbstractIntegrationTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private ExternalOfferRepository externalOfferRepository;

    @Autowired
    private RecruitmentRepository recruitmentRepository;

    @Autowired
    private TransactionTestHelper txHelper;

    @Test
    @ResetDataAfter
    @SneakyThrows
    void externalOfferWithNewRecruitmentShouldPersistSuccessfully() {
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class).build();

        var externalOffer = applicationContext.getBean(ExternalOfferMotherObject.class)
                .withRemoteId("test-remote-id")
                .withATSCode("test-ats")
                .withRecruiterCode("S-12345")
                .withRecruitment(recruitment)
                .buildAndPersist();

        txHelper.doInTransaction(() -> {
            var persistedOffer = externalOfferRepository.findById(externalOffer.getUuid());
            assertThat(persistedOffer).isPresent();

            var persistedRecruitment = persistedOffer.get().getRecruitment();
            assertThat(persistedRecruitment).isNotNull();
            assertThat(persistedRecruitment.getId()).isNotNull(); // Should have been auto-generated

            // Verify bidirectional relationship
            assertThat(persistedRecruitment.getExternalOffer()).isEqualTo(persistedOffer.get());

            // Verify the recruitment was actually persisted in the database
            var recruitmentFromDb = recruitmentRepository.findById(persistedRecruitment.getId());
            assertThat(recruitmentFromDb).isPresent();
            assertThat(recruitmentFromDb.get().getExternalOffer()).isEqualTo(persistedOffer.get());
        });
    }

    @Test
    @ResetDataAfter
    @SneakyThrows
    void externalOfferWithExistingRecruitmentShouldPersistSuccessfully() {
        var existingRecruitment = applicationContext.getBean(RecruitmentMotherObject.class).buildAndPersist();

        var externalOffer = applicationContext.getBean(ExternalOfferMotherObject.class)
                .withRemoteId("test-remote-id-2")
                .withATSCode("test-ats")
                .withRecruiterCode("S-12345")
                .withRecruitment(existingRecruitment)
                .buildAndPersist();

        txHelper.doInTransaction(() -> {
            var persistedOffer = externalOfferRepository.findById(externalOffer.getUuid());
            assertThat(persistedOffer).isPresent();

            var linkedRecruitment = persistedOffer.get().getRecruitment();
            assertThat(linkedRecruitment).isNotNull();
            assertThat(linkedRecruitment.getId()).isEqualTo(existingRecruitment.getId());

            assertThat(linkedRecruitment.getExternalOffer()).isEqualTo(persistedOffer.get());

            var recruitmentFromDb = recruitmentRepository.findById(existingRecruitment.getId());
            assertThat(recruitmentFromDb).isPresent();
            assertThat(recruitmentFromDb.get().getExternalOffer()).isEqualTo(persistedOffer.get());
        });
    }

    @Test
    @ResetDataAfter
    @SneakyThrows
    void externalOfferWithoutRecruitmentShouldPersistSuccessfully() {
        var externalOffer = applicationContext.getBean(ExternalOfferMotherObject.class)
                .withRemoteId("test-remote-id-3")
                .withATSCode("test-ats")
                .withRecruiterCode("S-12345")
                .buildAndPersist();

        txHelper.doInTransaction(() -> {
            var persistedOffer = externalOfferRepository.findById(externalOffer.getUuid());
            assertThat(persistedOffer).isPresent();
            assertThat(persistedOffer.get().getRecruitment()).isNull();
        });
    }
}
