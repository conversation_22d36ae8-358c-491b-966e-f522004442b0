package com.erhgo;

import com.erhgo.services.exporter.PdfExporterService;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
class ProfileCompetencesPdfExportUnitTest {

    private final PdfExporterService pdfExporterService = new PdfExporterService();

    @Test
    void generatePDF() throws IOException {
        var stream = new ByteArrayOutputStream();
        pdfExporterService.generatePdf("profileCompetences",
                new ProfileCompetencesViewObject(
                        "temps plein",
                        true,
                        "CDD, CDI, Alternance",
                        "Marseille (07000)",
                        new ArrayList<>(List.of(List.of("#StratégieCommerciale",
                                        "#VeilleConcurrentielle",
                                        "#BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB"
                                ),
                                List.of(
                                        "#EspritAnalyse",
                                        "#QualiData",
                                        "#MarchésFinanciers"
                                ),
                                List.of(
                                        "#SynergieÉquipe",
                                        "#ConformitéRéglementaire",
                                        "#ServiceQualité"
                                ),
                                List.of(
                                        StringUtils.addWordBreaksInHashtag("#InnovationRelationClientClientChouette"),
                                        "#SolutionsSurMesure",
                                        "#GestionVente"
                                ))),
                        " Je suis un professionnel polyvalent, doté d'un excellent sens du relationnel, de rigueur et de précision dans mon travail. Ma capacité d'analyse et ma réactivité me permettent de résoudre efficacement les problèmes et de répondre aux besoins des clients. Je suis organisé, persévérant et orienté résultats, contribuant ainsi au développement et à la satisfaction des parties prenantes.",
                        new ArrayList<>(List.of(
                                new ProfileCompetencesViewObject.SkillViewObject$("COOPERATION", "En tant que technico-commercial stagiaire, j'ai dû rapidement évaluer les besoins des clients pour proposer des solutions adaptées, optimisant ainsi les ventes et la satisfaction client."),
                                new ProfileCompetencesViewObject.SkillViewObject$("LEARNING", "Lors de mon stage en tant que négociateur de contrats à terme, convaincre était essentiel. J'ai développé l'art de persuader les partenaires commerciaux en présentant des arguments solides pour sécuriser les meilleures conditions."),
                                new ProfileCompetencesViewObject.SkillViewObject$("ANALYZE", "En tant que stagiaire responsable service clientèle, la coopération avec l'équipe était primordiale pour résoudre efficacement les problématiques clients et améliorer l'expérience globale."),
                                new ProfileCompetencesViewObject.SkillViewObject$("ACTION", "En poste de stagiaire analyste qualité de données, j'imaginais des processus d'amélioration continue en analysant les données pour anticiper les besoins futurs et augmenter l'efficacité opérationnelle"))),

                        new ArrayList<>(List.of()),
                        "Michèle",
                        "Dufrand",
                        "<EMAIL>",
                        "01 23 45 67 89", "", null, false),
                stream);

        TestUtils.assertThatPdfMatchesContent("profileCompetencesUnitTest.pdf", stream.toByteArray());
    }


    @Test
    void generatePDFWithSkills() throws IOException {
        var stream = new ByteArrayOutputStream();
        pdfExporterService.generatePdf("profileCompetences",
                new ProfileCompetencesViewObject(
                        "temps plein",
                        true,
                        "CDD, CDI, Alternance",
                        "Marseille (07000)<br/>+/- 50km",
                        new ArrayList<>(List.of(List.of(
                                        StringUtils.addWordBreaksInHashtag("#StratégieCommerciale"),
                                        StringUtils.addWordBreaksInHashtag("#VeilleConcurrentielle"),
                                        StringUtils.addWordBreaksInHashtag("#EspritAnalyse")
                                ),
                                List.of(
                                        StringUtils.addWordBreaksInHashtag("#EspritAnalyse"),
                                        StringUtils.addWordBreaksInHashtag("#QualiData"),
                                        StringUtils.addWordBreaksInHashtag("#MarchésFinanciers")
                                ),
                                List.of(
                                        StringUtils.addWordBreaksInHashtag("#SynergieÉquipe"),
                                        StringUtils.addWordBreaksInHashtag("#ConformitéRéglementaire"),
                                        StringUtils.addWordBreaksInHashtag("#ServiceQualité")
                                ),
                                List.of(
                                        StringUtils.addWordBreaksInHashtag("#InnovationRelationClientClientChouette"),

                                        StringUtils.addWordBreaksInHashtag("#SolutionsSurMesure"),
                                        StringUtils.addWordBreaksInHashtag("#GestionVente")
                                ))),
                        " Je suis un professionnel polyvalent, doté d'un excellent sens du relationnel, de rigueur et de précision dans mon travail. Ma capacité d'analyse et ma réactivité me permettent de résoudre efficacement les problèmes et de répondre aux besoins des clients. Je suis organisé, persévérant et orienté résultats, contribuant ainsi au développement et à la satisfaction des parties prenantes.",
                        new ArrayList<>(List.of(
                                new ProfileCompetencesViewObject.SkillViewObject$("Mon esprit d'initiative", "En tant que technico-commercial stagiaire, j'ai dû rapidement évaluer les besoins des clients pour proposer des solutions adaptées, optimisant ainsi les ventes et la satisfaction client."),
                                new ProfileCompetencesViewObject.SkillViewObject$("Ma perspicacité", "Chargé de maintenance chez Sandvick, j'ai su identifier rapidement les sources de pannes complexes, proposant des solutions efficaces pour éviter des temps d'arrêt prolongés. Cette capacité à analyser et à comprendre des problèmes techniques est un atout majeur dans le domaine de la maintenance."),
                                new ProfileCompetencesViewObject.SkillViewObject$("Ma recherche de l'efficacité", "Mon poste de technicien de maintenance chez Gindre Composant m'a appris à privilégier des interventions rapides et ciblées, garantissant une remise en service efficace des équipements défectueux. Je m'efforce toujours d'atteindre le meilleur résultat en un minimum de temps.")
                        )),
                        new ArrayList<>(List.of(
                                new ProfileCompetencesViewObject.SkillViewObject$("Connaissances", "Appui technique en électricité, gaz, hydraulique, pneumatique, optimisation de la gestion du parc outillage et consommable, GMAO, diagnostic et planification d'entretien"),
                                new ProfileCompetencesViewObject.SkillViewObject$("Langues", "Espagnol : bilingue, Anglais : courant"),
                                new ProfileCompetencesViewObject.SkillViewObject$("Habilitations et autorisations associées", "Habilitation électrique B0/H0V, B2V/H2V, HTA, BR, BC/HC, BT<=1000V, BC/HC et HTA, CACES"),
                                new ProfileCompetencesViewObject.SkillViewObject$("Gestes professionnels et matériel associé", "Logiciels bureautiques, Office 365, L.D.I.A, GMAO, Horoquartz, N2R, Webvue")
                        )),
                        "Michèle",
                        "Dufrand",
                        "Jan.ojyhtujojyhtujojyhtuj<wbr/>@gmail.loi",
                        "01 23 45 67 89", "", null, false),
                stream
        );

        TestUtils.assertThatPdfMatchesContent("profileCompetencesWithSkillsUnitTest.pdf", stream.toByteArray());
    }
}
