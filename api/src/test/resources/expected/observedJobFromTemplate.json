{"recruiterCode": "E-02", "employerCode": null, "title": "title for ERHGO JOB TEMPLATE", "description": null, "service": null, "createdBy": "<PERSON><PERSON><PERSON><PERSON>", "observators": [], "observationDate": null, "publicationDate": null, "state": "LISTED", "behaviors": [{"title": "Fibre commerciale", "code": "B-05"}], "recommendation": null, "missions": [{"title": "Mission principale", "activities": [{"title": "Je réponds à des demandes", "description": "", "inducedCapacities": [{"title": "Regarder avec attention", "description": "capacité à observer son environnement en identifiant les informations pertinentes pour l'action en cours", "code": "CA1-01"}]}], "contextsForCategory": [{"contexts": [], "category": {"code": "CCT-02", "title": "Gestes pros et matériel associé", "description": "Le geste professionnel est un savoir-faire spécifique attaché à un corps de métier. Il peut être lié à l’utilisation de matériels dédiés."}, "noContextForCategory": true}, {"contexts": [], "category": {"code": "CCT-03", "title": "R<PERSON>hme et conditions de travail", "description": "Permet d’identifier l’intensité et les variations de cadence de l’activité décrite. La répétition des tâches exécutées est également concernée. On peut parler de pénibilité."}, "noContextForCategory": true}, {"contexts": [], "category": {"code": "CCT-04", "title": "Modes de communication", "description": "Permet de décrire les types de communication (orale, écrite), leurs supports (face-à-face, électronique, téléphonique, filmé) et leur registre (familier, courant, soutenu) en fonction des types de public."}, "noContextForCategory": true}, {"contexts": [], "category": {"code": "CCT-01", "title": "Connaissances", "description": "Permet de décrire ce qu’il faut savoir et comprendre pour réaliser le travail. Les connaissances s'acquièrent par l’étude, la pratique ou les recherches."}, "noContextForCategory": true}, {"contexts": [{"title": "Règles de sécurité (utilisation de machines)", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-05", "title": "Procédures et modes opératoires", "description": "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues."}}, "origin": null}], "category": {"code": "CCT-05", "title": "Procédures et modes opératoires", "description": "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues."}, "noContextForCategory": false}, {"contexts": [], "category": {"code": "CCT-06", "title": "Expositions aux risques", "description": "Permet de décrire des événements exceptionnels et facteurs de risque au regard de leur probabilité et de leur dangerosité pour les personnes et pour les équipements."}, "noContextForCategory": true}, {"contexts": [], "category": {"code": "CCT-07", "title": "Habilitations et autorisations associés", "description": "Permet de décrire les niveaux d’habilitations souhaités ou rendus obligatoires dans l’exercice de l’activité. "}, "noContextForCategory": true}, {"contexts": [], "category": {"code": "CCT-08", "title": "Configurations du lieu de travail", "description": "Permet de définir le ou les lieux où s’exerce habituellement l’activité. L’ampleur et/ou le nombre de sites permet d’apprécier l’impact sur le niveau de maitrise."}, "noContextForCategory": true}, {"contexts": [{"title": "Français", "description": "", "categoryLevel": {"title": "A1", "description": "Peut comprendre et utiliser des expressions familières et quotidiennes ainsi que des énoncés très simples qui visent à satisfaire des besoins concrets. Peut se présenter ou présenter quelqu'un et poser à une personne des questions la concernant - par exemple, sur son lieu d'habitation, ses relations, ce qui lui appartient, etc. - et peut répondre au même type de questions. Peut communiquer de façon simple si l'interlocuteur parle lentement et distinctement et se montre coopératif.", "score": 1, "category": {"code": "CCT-09", "title": "<PERSON><PERSON>", "description": "Permet d’identifier la ou les langues mobilisées dans l’activité. Chaque langue utilisée peut l’être à un niveau d’exigence différent dans une même activité. La langue principale est évaluée, y compris s’il s’agit de la langue officielle (français). Dans le monde professionnel, la norme de maîtrise du français se situe généralement entre B1 et B2, selon les domaines d’activités. On se réfère ici à la classification européenne de maîtrise des langues."}}, "origin": null}], "category": {"code": "CCT-09", "title": "<PERSON><PERSON>", "description": "Permet d’identifier la ou les langues mobilisées dans l’activité. Chaque langue utilisée peut l’être à un niveau d’exigence différent dans une même activité. La langue principale est évaluée, y compris s’il s’agit de la langue officielle (français). Dans le monde professionnel, la norme de maîtrise du français se situe généralement entre B1 et B2, selon les domaines d’activités. On se réfère ici à la classification européenne de maîtrise des langues."}, "noContextForCategory": false}]}], "erhgoOccupation": {"title": "title for ERHGO JOB TEMPLATE"}, "level": "STRATEGIC", "modifiable": true}