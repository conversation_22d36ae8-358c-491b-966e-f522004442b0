{"pageIndex": 0, "numberOfElementsInPage": 3, "pageSize": 600, "totalNumberOfElements": 3, "totalPages": 1, "content": [{"id": "3d903206-7ae9-48d6-862b-001e92015991", "matchingRateInPercent": 100, "candidatureId": null, "contactInformation": {"creationDate": null, "firstName": "Jean-3d903206-7ae9-48d6-862b-001e92015991", "lastName": "<PERSON><PERSON> Du 3d903206-7ae9-48d6-862b-001e92015991", "email": "<EMAIL>", "transactionalBlacklisted": null, "location": {"city": null, "citycode": null, "postcode": null, "departmentCode": null, "regionName": null, "longitude": null, "latitude": null}, "contactTime": "AFTERNOON", "channels": ["Organization for channel T-1"], "isPrivate": false, "birthDate": null, "salary": null, "situation": null}, "userProfileProgress": {"userRegistrationState": {"userRegistrationStateStep": "CREATED_ACCOUNT", "situation": null, "salary": null, "selectedActivity": null, "selectedActivityId": null, "selectedOccupation": null, "selectedOccupationId": null, "jobTitle": null, "city": null}, "capacitiesCount": 1, "experiencesCount": 1, "hasBehaviors": false, "candidaturesCount": 0, "masteryLevel": 1.0}, "alreadyReceivedProposalEmail": false, "missingCriteria": [], "unknownCriteria": []}, {"id": "3d903206-7ae9-48d6-862b-001e92015992", "matchingRateInPercent": 100, "candidatureId": null, "contactInformation": {"creationDate": null, "firstName": "Jean-3d903206-7ae9-48d6-862b-001e92015992", "lastName": "<PERSON><PERSON> Du 3d903206-7ae9-48d6-862b-001e92015992", "email": "<EMAIL>", "transactionalBlacklisted": null, "location": {"city": null, "citycode": null, "postcode": null, "departmentCode": null, "regionName": null, "longitude": null, "latitude": null}, "contactTime": "AFTERNOON", "channels": [], "isPrivate": false, "birthDate": null, "salary": null, "situation": null}, "userProfileProgress": {"userRegistrationState": {"userRegistrationStateStep": "CREATED_ACCOUNT", "situation": null, "salary": null, "selectedActivity": null, "selectedActivityId": null, "selectedOccupation": null, "selectedOccupationId": null, "jobTitle": null, "city": null}, "capacitiesCount": 1, "experiencesCount": 1, "hasBehaviors": false, "candidaturesCount": 0, "masteryLevel": 1.0}, "alreadyReceivedProposalEmail": false, "missingCriteria": [], "unknownCriteria": []}, {"id": "3d903206-7ae9-48d6-862b-001e92015993", "matchingRateInPercent": 100, "candidatureId": null, "contactInformation": {"creationDate": null, "firstName": "Jean-3d903206-7ae9-48d6-862b-001e92015993", "lastName": "<PERSON><PERSON> Du 3d903206-7ae9-48d6-862b-001e92015993", "email": "<EMAIL>", "transactionalBlacklisted": null, "location": {"city": null, "citycode": null, "postcode": null, "departmentCode": null, "regionName": null, "longitude": null, "latitude": null}, "contactTime": "AFTERNOON", "channels": ["Organization for channel T-2"], "isPrivate": true, "birthDate": null, "salary": null, "situation": null}, "userProfileProgress": {"userRegistrationState": {"userRegistrationStateStep": "CREATED_ACCOUNT", "situation": null, "salary": null, "selectedActivity": null, "selectedActivityId": null, "selectedOccupation": null, "selectedOccupationId": null, "jobTitle": null, "city": null}, "capacitiesCount": 1, "experiencesCount": 1, "hasBehaviors": false, "candidaturesCount": 0, "masteryLevel": 1.0}, "alreadyReceivedProposalEmail": false, "missingCriteria": [], "unknownCriteria": []}]}