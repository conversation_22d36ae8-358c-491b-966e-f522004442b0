{"sourcingCandidatureType": "sourcingCandidatureDetail", "generated": true, "candidatureId": 1, "anonymousCode": "UB40", "color": "gris", "firstName": null, "lastName": null, "phone": null, "email": null, "lastActionDate": "2020-02-02T00:00:00+01:00", "submissionDate": null, "answer": null, "state": "NEW", "lastNote": "Une note d'un autre utilisateur lié à l'organisation", "userId": "e89768ad-0bc2-4359-b726-3c1f9bfadc3f", "numberOfOfferCandidatures": null, "hasSoftSkillPdf": null, "location": null, "sectors": [], "numberOfExperiences": 3, "contracts": [], "workingTimes": [], "hasDrivingLicense": false, "candidatureNotes": [{"userFullname": "<PERSON> 1", "content": "Une note d'un autre utilisateur lié à l'organisation", "noteDateTime": "2022-01-01T01:00:00+01:00"}, {"userFullname": "<PERSON> 2", "content": "Une note d'un utilisateur lié à l'organisation", "noteDateTime": "2001-01-01T01:00:00+01:00"}], "completionScore": 7, "lastConnectionDate": "2023-07-07T05:05:00Z", "recruitment": {"customQuestion": "Ça va ?", "recruitmentState": "PUBLISHED", "recruitmentId": 1, "publicationDate": "1971-11-03T08:06:40+01:00", "title": "Un bon métier", "location": {"city": "Là", "citycode": null, "postcode": "59012", "departmentCode": null, "regionName": null, "longitude": null, "latitude": null, "radiusInKm": null}, "baseSalary": 200, "maxSalary": 500}, "previousCandidatures": []}