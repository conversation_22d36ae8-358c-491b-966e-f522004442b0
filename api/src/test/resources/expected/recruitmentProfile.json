{"title": "Junior", "modifiable": false, "optionalActivities": [{"activity": {"title": "J'assure le suivi des mails", "description": "", "inducedCapacities": [{"title": "E<PERSON>rire", "description": "capacité à rédiger un message (manuscrit ou saisi) compréhensible", "code": "CA1-04", "inducedCapacities": []}, {"title": "Reconnaitre les formes", "description": "capacité à identifier et comprendre les différents symboles de l'environnement en fonction de leur forme, leur couleur et leur graphisme", "code": "CA1-02", "inducedCapacities": []}, {"title": "<PERSON><PERSON>", "description": "capacité à déchiffrer un texte et à en comprendre le sens", "code": "CA1-03", "inducedCapacities": []}]}, "acquisitionModality": "INTEGRATION_PROCESS"}], "optionalContexts": [{"context": {"title": "Règles de sécurité (règles de circulation)", "description": "Ceci est une règle de sécurité", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-05", "title": "Procédures et modes opératoires", "description": "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues."}}, "origin": null}, "acquisitionModality": "TRAINING"}, {"context": {"title": "Vocabulaire spécifique", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-01", "title": "Connaissances", "description": "Permet de décrire ce qu’il faut savoir et comprendre pour réaliser le travail. Les connaissances s'acquièrent par l’étude, la pratique ou les recherches."}}, "origin": null}, "acquisitionModality": "TRAINING"}], "contextQuestions": [{"question": {"title": "Title for Titre custom de contexte ?", "suggestedAnswers": {"none": "None  - Title for Titre custom de contexte ?", "low": "Low  - Title for Titre custom de contexte ?", "medium": "Medium  - Title for Titre custom de contexte ?", "high": "High  - Title for Titre custom de contexte ?"}}}], "qualifiedMissionIds": [], "customQuestion": null}