{"recruitmentId": 2, "typeContractCategory": "PERMANENT", "workingTimeType": "FULL_TIME", "baseSalary": 200, "maxSalary": 800, "hideSalary": null, "location": {"city": "Lyon", "citycode": null, "postcode": null, "departmentCode": null, "regionName": null, "longitude": null, "latitude": null, "radiusInKm": null}, "title": "Title to duplicate", "occupationId": "6b814fc3-c5de-4a6b-99d4-898b72de1843", "criteriaValues": ["CR0V1", "CR1V2"], "organization": {"name": "Default title", "description": "recruiter description", "siret": null, "hasSiretError": false, "externalUrl": null, "isForcedUrl": false}, "customQuestion": "Ça va ?", "step": 0, "workingWeeklyTime": 42, "modularWorkingTime": true, "organizationDescription": "recruiter description", "externalUrl": null, "recruitmentState": "DRAFT", "publicationEndDate": null, "erhgoClassifications": [{"code": "SO-05", "title": "La relation d’aide, le service public", "orderIndex": 5, "icon": "handshake-angle", "highPriority": false}, {"code": "SO-07", "title": "Les enfants, l’éducation, l’orientation", "orderIndex": 7, "icon": "baby", "highPriority": false}], "isForcedUrl": false, "usersToNotify": [{"id": "6b814fc3-c5de-4a6b-99d4-898b72de1843", "email": "<EMAIL>", "fullname": "<PERSON> 1", "lastConnectionDate": null}], "manager": {"id": "6b814fc3-c5de-4a6b-99d4-898b72de1843", "email": "<EMAIL>", "fullname": "<PERSON> 1", "lastConnectionDate": null}}