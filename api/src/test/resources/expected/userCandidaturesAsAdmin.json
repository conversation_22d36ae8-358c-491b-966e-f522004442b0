[{"organizationCode": "T-048", "candidatureId": 1, "jobTitle": "Job with code J-043 and organization T-048", "employerTitle": null, "recruiterTitle": "Entreprise du T-048", "refusedCandidature": true, "lastCandidatureNoteDate": "2020-05-25T02:05:00Z", "candidatureState": "REFUSED_ON_CALL", "visibleForUser": false}, {"organizationCode": "E-044", "candidatureId": 2, "jobTitle": "Job with code J-045 and organization E-044", "employerTitle": null, "recruiterTitle": "Entreprise du E-044", "refusedCandidature": true, "lastCandidatureNoteDate": "2020-05-25T02:05:00Z", "candidatureState": "REFUSED_ON_CALL", "visibleForUser": true}, {"organizationCode": "E-044", "candidatureId": 3, "jobTitle": "Job with code J-044 and organization E-044", "employerTitle": null, "recruiterTitle": "Entreprise du E-044", "refusedCandidature": false, "lastCandidatureNoteDate": null, "candidatureState": "NOT_TREATED_BY_ERHGO", "visibleForUser": true}]