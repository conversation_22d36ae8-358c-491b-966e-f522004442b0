{"escoOccupations": [{"uri": "http://3data.odas.app", "title": "Title of esco occupation http://3data.odas.app", "isco": {"iscoGroup": 999, "title": "Title for ISCO 999"}}, {"uri": "http://data.odas.app", "title": "title for ESCO_OCCUPATION_WITH_REFERENTIAL_DATA/ other title remove for job creation", "isco": {"iscoGroup": 666, "title": "Title for ISCO 666"}}], "title": "title", "description": null, "alternativeLabels": [], "romeOccupations": [], "accessibleRomeOccupations": [], "accessibleFromRomeOccupations": [], "level": null, "erhgoOccupationState": "MEDIUM", "skills": [{"uri": "URI for à Skill not qualified 3", "title": "<PERSON> <PERSON><PERSON> not qualified 3", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [], "behaviors": [], "activities": [], "noActivity": null, "noBehavior": null, "noContext": null, "updatedDate": "2020-02-01T23:00:00Z"}, {"uri": "URI for Ä Skill not qualified 4", "title": "<PERSON> Skill not qualified 4", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [], "behaviors": [], "activities": [], "noActivity": null, "noBehavior": null, "noContext": null, "updatedDate": "2020-02-01T23:00:00Z"}, {"uri": "URI for a Skill not qualified 5", "title": "a Skill not qualified 5", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [], "behaviors": [], "activities": [], "noActivity": null, "noBehavior": null, "noContext": null, "updatedDate": "2020-02-01T23:00:00Z"}, {"uri": "URI for a skill qualified 3", "title": "a skill qualified 3", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [], "behaviors": [], "activities": [], "noActivity": true, "noBehavior": true, "noContext": true, "updatedDate": "2020-02-01T23:00:00Z"}, {"uri": "URI for Â Skill qualified 4", "title": "<PERSON> Skill qualified 4", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [], "behaviors": [], "activities": [], "noActivity": true, "noBehavior": true, "noContext": true, "updatedDate": "2020-02-01T23:00:00Z"}, {"uri": "URI for à Skill qualified 5", "title": "<PERSON><PERSON> qualified 5", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [], "behaviors": [], "activities": [], "noActivity": true, "noBehavior": true, "noContext": true, "updatedDate": "2020-02-01T23:00:00Z"}, {"uri": "URI for SK_0 http://data.odas.app", "title": "SK_0 http://data.odas.app", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [{"auditedType": "context", "code": "CT-21", "title": "Règles de sécurité (utilisation de machines)", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-05", "title": "Procédures et modes opératoires", "description": "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues."}}, "origin": null}, {"auditedType": "context", "code": "CT-29", "title": "Français", "description": "", "categoryLevel": {"title": "A1", "description": "Peut comprendre et utiliser des expressions familières et quotidiennes ainsi que des énoncés très simples qui visent à satisfaire des besoins concrets. Peut se présenter ou présenter quelqu'un et poser à une personne des questions la concernant - par exemple, sur son lieu d'habitation, ses relations, ce qui lui appartient, etc. - et peut répondre au même type de questions. Peut communiquer de façon simple si l'interlocuteur parle lentement et distinctement et se montre coopératif.", "score": 1, "category": {"code": "CCT-09", "title": "<PERSON><PERSON>", "description": "Permet d’identifier la ou les langues mobilisées dans l’activité. Chaque langue utilisée peut l’être à un niveau d’exigence différent dans une même activité. La langue principale est évaluée, y compris s’il s’agit de la langue officielle (français). Dans le monde professionnel, la norme de maîtrise du français se situe généralement entre B1 et B2, selon les domaines d’activités. On se réfère ici à la classification européenne de maîtrise des langues."}}, "origin": null}], "behaviors": [{"title": "Fibre commerciale", "description": "", "code": "B-05", "behaviorCategory": "SOCIABILITY", "categoryIndex": 2}], "activities": [{"auditedType": "activityLabelWithCapacities", "title": "Je réponds à des demandes", "description": "", "inducedCapacities": [{"title": "Regarder avec attention", "description": "capacité à observer son environnement en identifiant les informations pertinentes pour l'action en cours", "code": "CA1-01", "inducedCapacities": []}]}], "noActivity": null, "noBehavior": null, "noContext": null}, {"uri": "URI for SK_1 http://data.odas.app", "title": "SK_1 http://data.odas.app", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [{"auditedType": "context", "code": "CT-21", "title": "Règles de sécurité (utilisation de machines)", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-05", "title": "Procédures et modes opératoires", "description": "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues."}}, "origin": null}, {"auditedType": "context", "code": "CT-29", "title": "Français", "description": "", "categoryLevel": {"title": "A1", "description": "Peut comprendre et utiliser des expressions familières et quotidiennes ainsi que des énoncés très simples qui visent à satisfaire des besoins concrets. Peut se présenter ou présenter quelqu'un et poser à une personne des questions la concernant - par exemple, sur son lieu d'habitation, ses relations, ce qui lui appartient, etc. - et peut répondre au même type de questions. Peut communiquer de façon simple si l'interlocuteur parle lentement et distinctement et se montre coopératif.", "score": 1, "category": {"code": "CCT-09", "title": "<PERSON><PERSON>", "description": "Permet d’identifier la ou les langues mobilisées dans l’activité. Chaque langue utilisée peut l’être à un niveau d’exigence différent dans une même activité. La langue principale est évaluée, y compris s’il s’agit de la langue officielle (français). Dans le monde professionnel, la norme de maîtrise du français se situe généralement entre B1 et B2, selon les domaines d’activités. On se réfère ici à la classification européenne de maîtrise des langues."}}, "origin": null}], "behaviors": [{"title": "Fibre commerciale", "description": "", "code": "B-05", "behaviorCategory": "SOCIABILITY", "categoryIndex": 2}], "activities": [{"auditedType": "activityLabelWithCapacities", "title": "Je réponds à des demandes", "description": "", "inducedCapacities": [{"title": "Regarder avec attention", "description": "capacité à observer son environnement en identifiant les informations pertinentes pour l'action en cours", "code": "CA1-01", "inducedCapacities": []}]}], "noActivity": null, "noBehavior": null, "noContext": null}, {"uri": "URI for SK_2 http://data.odas.app", "title": "SK_2 http://data.odas.app", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [], "behaviors": [], "activities": [], "noActivity": true, "noBehavior": true, "noContext": true}], "activities": [{"objectType": "occupationActivity", "source": "SKILL", "updatedDate": "2020-02-01T23:00:00Z", "skills": [{"uri": "URI for SK_0 http://data.odas.app", "title": "SK_0 http://data.odas.app"}, {"uri": "URI for SK_1 http://data.odas.app", "title": "SK_1 http://data.odas.app"}], "state": "OPTIONAL", "activity": {"auditedType": "activityLabelWithCapacities", "title": "Je réponds à des demandes", "description": "", "inducedCapacities": [{"title": "Regarder avec attention", "description": "capacité à observer son environnement en identifiant les informations pertinentes pour l'action en cours", "code": "CA1-01", "inducedCapacities": []}]}}, {"objectType": "occupationActivity", "source": "MANUAL", "updatedDate": "2020-02-01T23:00:00Z", "skills": [], "state": "ESSENTIAL", "activity": {"auditedType": "activityLabelWithCapacities", "updatedDate": "2020-02-01T23:00:00Z", "title": "Je traite des mails entrants", "description": null, "inducedCapacities": [{"title": "Regarder avec attention", "description": "capacité à observer son environnement en identifiant les informations pertinentes pour l'action en cours", "code": "CA1-01", "inducedCapacities": []}]}}], "contexts": [{"objectType": "occupationContext", "source": "SKILL", "updatedDate": "2020-02-01T23:00:00Z", "skills": [{"uri": "URI for SK_0 http://data.odas.app", "title": "SK_0 http://data.odas.app"}, {"uri": "URI for SK_1 http://data.odas.app", "title": "SK_1 http://data.odas.app"}], "state": "OPTIONAL", "context": {"auditedType": "context", "code": "CT-29", "title": "Français", "description": "", "categoryLevel": {"title": "A1", "description": "Peut comprendre et utiliser des expressions familières et quotidiennes ainsi que des énoncés très simples qui visent à satisfaire des besoins concrets. Peut se présenter ou présenter quelqu'un et poser à une personne des questions la concernant - par exemple, sur son lieu d'habitation, ses relations, ce qui lui appartient, etc. - et peut répondre au même type de questions. Peut communiquer de façon simple si l'interlocuteur parle lentement et distinctement et se montre coopératif.", "score": 1, "category": {"code": "CCT-09", "title": "<PERSON><PERSON>", "description": "Permet d’identifier la ou les langues mobilisées dans l’activité. Chaque langue utilisée peut l’être à un niveau d’exigence différent dans une même activité. La langue principale est évaluée, y compris s’il s’agit de la langue officielle (français). Dans le monde professionnel, la norme de maîtrise du français se situe généralement entre B1 et B2, selon les domaines d’activités. On se réfère ici à la classification européenne de maîtrise des langues."}}, "origin": null}}, {"objectType": "occupationContext", "source": "MANUAL", "updatedDate": "2020-02-01T23:00:00Z", "skills": [], "state": "ESSENTIAL", "context": {"auditedType": "context", "code": "CT-21", "title": "Règles de sécurité (utilisation de machines)", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-05", "title": "Procédures et modes opératoires", "description": "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues."}}, "origin": null}}], "behaviors": [{"objectType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "MANUAL", "updatedDate": "2020-02-01T23:00:00Z", "skills": [], "behavior": {"title": "Etre avenant/souriant", "description": "Faculté à sourire tout le temps", "code": "B-01", "behaviorCategory": "CONSTANCY", "categoryIndex": 1}}, {"objectType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "SKILL", "updatedDate": "2020-02-01T23:00:00Z", "skills": [{"uri": "URI for SK_0 http://data.odas.app", "title": "SK_0 http://data.odas.app"}, {"uri": "URI for SK_1 http://data.odas.app", "title": "SK_1 http://data.odas.app"}], "behavior": {"title": "Fibre commerciale", "description": "", "code": "B-05", "behaviorCategory": "SOCIABILITY", "categoryIndex": 2}}], "updatedDate": "2020-02-01T23:00:00Z", "behaviorsCategories": {"behaviorCategory1": "CONSTANCY", "behaviorCategory2": "SOCIABILITY", "behaviorCategory3": null, "isBehaviorCategory1Overloaded": false, "isBehaviorCategory2Overloaded": false, "isBehaviorCategory3Overloaded": false}, "behaviorsDescription": null, "isVisibleForOrientation": true, "workEnvironments": [], "isTechnical": true, "criteriaValues": [], "erhgoClassifications": [], "occupationCreationReason": null}