{"question": "Question personnalisée", "answer": null, "requiresExperience": true, "recruiterTitle": "Nom Entreprise", "gdprMention": "<p>Mention RGPD</p>", "condition": {"criteria": [{"selectedValuesCodesInRecruitment": ["TC1-2"], "selectedValuesCodesByUser": [], "criteria": {"code": "CR0", "title": "Title for THRESHOLD", "questionLabel": "Question for THRESHOLD", "criteriaValues": [{"code": "TC1-1", "titleForQuestion": "La nuit", "titleStandalone": "Title for TC1-1", "titleForBO": "Title BO for TC1-1", "icon": "smile", "valueIndex": 0, "sourcingCriteriaStep": null}, {"code": "TC1-2", "titleForQuestion": "La nuit", "titleStandalone": "Title for TC1-2", "titleForBO": "Title BO for TC1-2", "icon": "smile", "valueIndex": 1, "sourcingCriteriaStep": null}, {"code": "TC1-3", "titleForQuestion": "La nuit", "titleStandalone": "Title for TC1-3", "titleForBO": "Title BO for TC1-3", "icon": "smile", "valueIndex": 2, "sourcingCriteriaStep": null}], "questionType": "THRESHOLD", "required": false}}, {"selectedValuesCodesInRecruitment": ["TC2-3"], "selectedValuesCodesByUser": [], "criteria": {"code": "CR1", "title": "Title for THRESHOLD", "questionLabel": "Question for THRESHOLD", "criteriaValues": [{"code": "TC2-1", "titleForQuestion": "La nuit", "titleStandalone": "Title for TC2-1", "titleForBO": "Title BO for TC2-1", "icon": "smile", "valueIndex": 0, "sourcingCriteriaStep": null}, {"code": "TC2-2", "titleForQuestion": "La nuit", "titleStandalone": "Title for TC2-2", "titleForBO": "Title BO for TC2-2", "icon": "smile", "valueIndex": 1, "sourcingCriteriaStep": null}, {"code": "TC2-3", "titleForQuestion": "La nuit", "titleStandalone": "Title for TC2-3", "titleForBO": "Title BO for TC2-3", "icon": "smile", "valueIndex": 2, "sourcingCriteriaStep": null}], "questionType": "THRESHOLD", "required": false}}, {"selectedValuesCodesInRecruitment": ["MC1-1"], "selectedValuesCodesByUser": [], "criteria": {"code": "CR2", "title": "Title for MULTIPLE", "questionLabel": "Question for MULTIPLE", "criteriaValues": [{"code": "MC1-1", "titleForQuestion": "La nuit", "titleStandalone": "Title for MC1-1", "titleForBO": "Title BO for MC1-1", "icon": "smile", "valueIndex": 0, "sourcingCriteriaStep": null}], "questionType": "MULTIPLE", "required": false}}, {"selectedValuesCodesInRecruitment": ["MC2-3", "MC2-1"], "selectedValuesCodesByUser": [], "criteria": {"code": "CR3", "title": "Title for MULTIPLE", "questionLabel": "Question for MULTIPLE", "criteriaValues": [{"code": "MC2-1", "titleForQuestion": "La nuit", "titleStandalone": "Title for MC2-1", "titleForBO": "Title BO for MC2-1", "icon": "smile", "valueIndex": 0, "sourcingCriteriaStep": null}, {"code": "MC2-3", "titleForQuestion": "La nuit", "titleStandalone": "Title for MC2-3", "titleForBO": "Title BO for MC2-3", "icon": "smile", "valueIndex": 2, "sourcingCriteriaStep": null}], "questionType": "MULTIPLE", "required": false}}, {"selectedValuesCodesInRecruitment": ["MC3-1", "MC3-2", "MC3-3"], "selectedValuesCodesByUser": [], "criteria": {"code": "CR4", "title": "Title for MULTIPLE", "questionLabel": "Question for MULTIPLE", "criteriaValues": [{"code": "MC3-1", "titleForQuestion": "La nuit", "titleStandalone": "Title for MC3-1", "titleForBO": "Title BO for MC3-1", "icon": "smile", "valueIndex": 0, "sourcingCriteriaStep": null}, {"code": "MC3-2", "titleForQuestion": "La nuit", "titleStandalone": "Title for MC3-2", "titleForBO": "Title BO for MC3-2", "icon": "smile", "valueIndex": 1, "sourcingCriteriaStep": null}, {"code": "MC3-3", "titleForQuestion": "La nuit", "titleStandalone": "Title for MC3-3", "titleForBO": "Title BO for MC3-3", "icon": "smile", "valueIndex": 2, "sourcingCriteriaStep": null}], "questionType": "MULTIPLE", "required": false}}, {"selectedValuesCodesInRecruitment": ["TC3-2"], "selectedValuesCodesByUser": [], "criteria": {"code": "CR5", "title": "Title for THRESHOLD", "questionLabel": "Question for THRESHOLD", "criteriaValues": [{"code": "TC3-1", "titleForQuestion": "La nuit", "titleStandalone": "Title for TC3-1", "titleForBO": "Title BO for TC3-1", "icon": "smile", "valueIndex": 0, "sourcingCriteriaStep": null}, {"code": "TC3-2", "titleForQuestion": "La nuit", "titleStandalone": "Title for TC3-2", "titleForBO": "Title BO for TC3-2", "icon": "smile", "valueIndex": 1, "sourcingCriteriaStep": null}, {"code": "TC3-3", "titleForQuestion": "La nuit", "titleStandalone": "Title for TC3-3", "titleForBO": "Title BO for TC3-3", "icon": "smile", "valueIndex": 2, "sourcingCriteriaStep": null}], "questionType": "THRESHOLD", "required": false}}], "availabilityForCandidature": {"isAvailable": false, "availabilityDelayInMonth": null}, "salary": {"recruitmentMin": 10000, "recruitmentMax": 20000, "desiredByUser": null}}, "contact": {"phoneNumber": null, "firstname": null, "lastname": null, "requiresLocation": true, "requiresCommunicationPreferences": true}}