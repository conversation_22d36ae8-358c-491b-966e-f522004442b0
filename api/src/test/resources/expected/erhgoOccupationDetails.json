{"escoOccupations": [{"uri": "http://nodata.odas.app", "title": "title for http:nodata.odas.app", "isco": {"iscoGroup": 777, "title": "Title for ISCO 777"}}], "title": "Title of erhgo occupation ac68c0bc-c83a-4c56-8393-db7d72644be0", "description": "Description of erhgo occupation ac68c0bc-c83a-4c56-8393-db7d72644be0", "alternativeLabels": ["title à", "Title B", "Title c"], "romeOccupations": [{"code": "R3", "title": "Title for ROME R3"}, {"code": "R4", "title": "Title for ROME R4"}], "accessibleRomeOccupations": [{"code": "R1", "title": "Title for ROME R1"}, {"code": "R2", "title": "Title for ROME R2"}], "accessibleFromRomeOccupations": [{"code": "R2", "title": "Title for ROME R2"}], "level": "PROFESSIONAL", "erhgoOccupationState": "QUALIFIED_V3_CONFIRMED", "skills": [{"uri": "URI for Sk 1", "title": "Sk 1", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [{"auditedType": "context", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": null, "code": "CT-21", "title": "Règles de sécurité (utilisation de machines)", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-05", "title": "Procédures et modes opératoires", "description": "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues."}}, "origin": null}], "behaviors": [{"title": "Etre pédagogue", "description": "", "code": "B-02", "behaviorCategory": "PRAGMATISM", "categoryIndex": 2}], "activities": [{"auditedType": "activityLabelWithCapacities", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": null, "title": "Je prends en charge les appels entrants et/ou sortants.", "description": "", "inducedCapacities": [{"title": "Regarder avec attention", "description": "capacité à observer son environnement en identifiant les informations pertinentes pour l'action en cours", "code": "CA1-01", "inducedCapacities": []}]}], "noActivity": null, "noBehavior": null, "noContext": null, "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": "<PERSON><PERSON><PERSON><PERSON>"}, {"uri": "URI for Sk 2", "title": "Sk 2", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [{"auditedType": "context", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": null, "code": "CT-21", "title": "Règles de sécurité (utilisation de machines)", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-05", "title": "Procédures et modes opératoires", "description": "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues."}}, "origin": null}], "behaviors": [{"title": "Etre pédagogue", "description": "", "code": "B-02", "behaviorCategory": "PRAGMATISM", "categoryIndex": 2}], "activities": [{"auditedType": "activityLabelWithCapacities", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": null, "title": "Je prends en charge les appels entrants et/ou sortants.", "description": "", "inducedCapacities": [{"title": "Regarder avec attention", "description": "capacité à observer son environnement en identifiant les informations pertinentes pour l'action en cours", "code": "CA1-01", "inducedCapacities": []}]}], "noActivity": null, "noBehavior": null, "noContext": null, "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": "<PERSON><PERSON><PERSON><PERSON>"}], "activities": [{"objectType": "occupationActivity", "source": "MANUAL", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": "<PERSON><PERSON><PERSON><PERSON>", "skills": [], "state": "OPTIONAL", "activity": {"auditedType": "activityLabelWithCapacities", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": null, "title": "Je démarre un RDV Client ", "description": "ça démarre ?", "inducedCapacities": [{"title": "Regarder avec attention", "description": "capacité à observer son environnement en identifiant les informations pertinentes pour l'action en cours", "code": "CA1-01", "inducedCapacities": []}]}}, {"objectType": "occupationActivity", "source": "SKILL", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": "<PERSON><PERSON><PERSON><PERSON>", "skills": [{"uri": "URI for Sk 1", "title": "Sk 1"}, {"uri": "URI for Sk 2", "title": "Sk 2"}], "state": "ESSENTIAL", "activity": {"auditedType": "activityLabelWithCapacities", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": null, "title": "Je prends en charge les appels entrants et/ou sortants.", "description": "", "inducedCapacities": [{"title": "Regarder avec attention", "description": "capacité à observer son environnement en identifiant les informations pertinentes pour l'action en cours", "code": "CA1-01", "inducedCapacities": []}]}}], "contexts": [{"objectType": "occupationContext", "source": "MANUAL", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": "<PERSON><PERSON><PERSON><PERSON>", "skills": [], "state": "ESSENTIAL", "context": {"auditedType": "context", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": null, "code": "CT-1", "title": "Forte charge cognitive", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-01", "title": "Connaissances", "description": "Permet de décrire ce qu’il faut savoir et comprendre pour réaliser le travail. Les connaissances s'acquièrent par l’étude, la pratique ou les recherches."}}, "origin": null}}, {"objectType": "occupationContext", "source": "SKILL", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": "<PERSON><PERSON><PERSON><PERSON>", "skills": [{"uri": "URI for Sk 1", "title": "Sk 1"}, {"uri": "URI for Sk 2", "title": "Sk 2"}], "state": "OPTIONAL", "context": {"auditedType": "context", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": null, "code": "CT-21", "title": "Règles de sécurité (utilisation de machines)", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-05", "title": "Procédures et modes opératoires", "description": "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues."}}, "origin": null}}], "behaviors": [{"objectType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "SKILL", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": "<PERSON><PERSON><PERSON><PERSON>", "skills": [{"uri": "URI for Sk 1", "title": "Sk 1"}, {"uri": "URI for Sk 2", "title": "Sk 2"}], "behavior": {"title": "Etre pédagogue", "description": "", "code": "B-02", "behaviorCategory": "PRAGMATISM", "categoryIndex": 2}}, {"objectType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "MANUAL", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": "<PERSON><PERSON><PERSON><PERSON>", "skills": [], "behavior": {"title": "<PERSON><PERSON><PERSON> son stress", "description": "Faculté à résister à la pression", "code": "B-04", "behaviorCategory": "RIGOR", "categoryIndex": 3}}], "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": "<PERSON><PERSON><PERSON><PERSON>", "behaviorsCategories": {"behaviorCategory1": null, "behaviorCategory2": null, "behaviorCategory3": null, "isBehaviorCategory1Overloaded": false, "isBehaviorCategory2Overloaded": false, "isBehaviorCategory3Overloaded": false}, "behaviorsDescription": "Behaviors Description of erhgo occupation ac68c0bc-c83a-4c56-8393-db7d72644be0", "workEnvironments": [{"code": "ENV-05", "title": "Logistique", "description": "Le cadre des activités artistiques et de divertissement : salles de spectacle, musées, parcs à thèmes, stades, équipements sportifs..."}, {"code": "ENV-02", "title": "Culture & Loisirs", "description": "Le cadre des activités de production à grande échelle : usines, installations soumises à des normes et des réglementations spécifiques..."}], "isTechnical": true, "criteriaValues": [{"criteriaValueType": "CriteriaValueDTO", "code": "1-2", "titleForQuestion": "La nuit", "titleStandalone": "Title for 1-2", "titleForBO": "Title BO for 1-2", "icon": "smile", "valueIndex": 1, "sourcingCriteriaStep": null}, {"criteriaValueType": "CriteriaValueDTO", "code": "1-3", "titleForQuestion": "La nuit", "titleStandalone": "Title for 1-3", "titleForBO": "Title BO for 1-3", "icon": "smile", "valueIndex": 2, "sourcingCriteriaStep": null}, {"criteriaValueType": "CriteriaValueDTO", "code": "2-1", "titleForQuestion": "La nuit", "titleStandalone": "Title for 2-1", "titleForBO": "Title BO for 2-1", "icon": "smile", "valueIndex": 0, "sourcingCriteriaStep": null}], "erhgoClassifications": [{"code": "SO-03", "title": "Les réseaux, la publicité, les médias", "orderIndex": 3, "icon": "megaphone", "highPriority": true}, {"code": "SO-05", "title": "La relation d’aide, le service public", "orderIndex": 5, "icon": "handshake-angle", "highPriority": false}]}