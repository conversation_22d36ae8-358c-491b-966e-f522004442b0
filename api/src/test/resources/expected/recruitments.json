{"objectType": "recruitmentPage", "pageIndex": 0, "numberOfElementsInPage": 3, "pageSize": 10, "totalNumberOfElements": 3, "totalPages": 1, "content": [{"code": "R-1", "jobTitle": "Developer Fullstack", "profileTitle": "Junior", "recruiterTitle": "<PERSON><PERSON><PERSON>", "recruiterCode": "E-02", "employerTitle": "title M-03", "city": "Lyon", "state": "PUBLISHED", "publicationDate": "1992-09-07T00:00:00+02:00", "sourcingHost": "", "sendNotificationState": null, "sendNotificationDate": null, "mailNotificationCount": 0, "mobileNotificationsCount": 0, "candidaturesCount": {"newCandidatureCount": {"fromUser": 1, "generated": 0}, "totalCandidatureCount": {"fromUser": 1, "generated": 0}, "toContactCandidatureCount": 0, "contactedCandidatureCount": 0, "refusedCandidatureCount": 0, "newMatchingCandidatureCount": 1}, "lastProcessingType": null}, {"id": 2, "code": "R-2", "jobTitle": "Developer Fullstack", "profileTitle": "Junior", "recruiterTitle": "<PERSON><PERSON><PERSON>", "recruiterCode": "E-02", "employerTitle": "title M-03", "city": "Lyon", "state": "PUBLISHED", "publicationDate": "1992-09-07T00:00:00+02:00", "sourcingHost": "", "sendNotificationState": null, "sendNotificationDate": null, "mailNotificationCount": 0, "mobileNotificationsCount": 0, "candidaturesCount": {"newCandidatureCount": {"fromUser": 0, "generated": 0}, "totalCandidatureCount": {"fromUser": 0, "generated": 0}, "toContactCandidatureCount": 0, "contactedCandidatureCount": 0, "refusedCandidatureCount": 0, "newMatchingCandidatureCount": 0}, "lastProcessingType": "REPUBLISH"}, {"code": "R-3", "jobTitle": "Developer Backend", "profileTitle": "Senior C", "recruiterTitle": "<PERSON><PERSON><PERSON>", "recruiterCode": "E-02", "employerTitle": "", "city": "Lyon", "state": "PUBLISHED", "publicationDate": "1992-09-07T00:00:00+02:00", "sourcingHost": "", "sendNotificationState": null, "sendNotificationDate": null, "mailNotificationCount": 0, "mobileNotificationsCount": 0, "candidaturesCount": {"newCandidatureCount": {"fromUser": 0, "generated": 0}, "totalCandidatureCount": {"fromUser": 0, "generated": 0}, "toContactCandidatureCount": 0, "contactedCandidatureCount": 0, "refusedCandidatureCount": 0, "newMatchingCandidatureCount": 0}, "lastProcessingType": "REPUBLISH"}]}