[{"recruitmentId": 7, "recruiterName": "Entreprise du E-6", "recruiterCode": "E-6", "recruitmentTitle": "Artiste", "recruiterType": "ENTERPRISE", "notifications": [], "candidature": {"id": 7, "state": "REFUSED_MEETING_CLIENT", "generatedFromSourcing": true, "visibleForUser": true, "archived": false}}, {"recruitmentId": 8, "recruiterName": "Entreprise du E-7", "recruiterCode": "E-7", "recruitmentTitle": "<PERSON><PERSON><PERSON>", "recruiterType": "ENTERPRISE", "notifications": [], "candidature": {"id": 8, "state": "NEW", "generatedFromSourcing": true, "visibleForUser": true, "archived": true}}, {"recruitmentId": 6, "recruiterName": "Entreprise du E-5", "recruiterCode": "E-5", "recruitmentTitle": "<PERSON><PERSON><PERSON><PERSON>", "recruiterType": "ENTERPRISE", "notifications": [], "candidature": {"id": 6, "state": "INTERNAL_POSITION", "generatedFromSourcing": false, "visibleForUser": false, "archived": false}}, {"recruitmentId": 4, "recruiterName": "Entreprise du E-2", "recruiterCode": "E-2", "recruitmentTitle": "Ouvrier en batiment", "recruiterType": "ENTERPRISE", "notifications": [{"type": "MOBILE", "createdAt": "2020-02-02"}, {"type": "BOTH", "createdAt": "2020-02-02"}], "candidature": {"id": 5, "state": "RECRUITMENT_VALIDATED", "generatedFromSourcing": false, "visibleForUser": false, "archived": false}}, {"recruitmentId": 5, "recruiterName": "Entreprise du O-42", "recruiterCode": "O-42", "recruitmentTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruiterType": "SOURCING", "notifications": [{"type": "EMAIL", "createdAt": "2020-02-02"}], "candidature": null}]