{"uri": "http://data.odas.app", "title": "title for ESCO_OCCUPATION_WITH_REFERENTIAL_DATA/ other title remove for job creation", "descriptionEN": "Description EN of title for ESCO_OCCUPATION_WITH_REFERENTIAL_DATA/ other title remove for job creation", "descriptionFR": "Description FR of title for ESCO_OCCUPATION_WITH_REFERENTIAL_DATA/ other title remove for job creation", "alternativeLabels": ["Alt 1 title for ESCO_OCCUPATION_WITH_REFERENTIAL_DATA/ other title remove for job creation", "Alt 2 title for ESCO_OCCUPATION_WITH_REFERENTIAL_DATA/ other title remove for job creation"], "iscoOccupation": {"iscoGroup": 666, "title": "Title for ISCO 666"}, "skills": [{"uri": "URI for SK_0 http://data.odas.app", "title": "SK_0 http://data.odas.app", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [{"lastModifiedBy": null, "code": "CT-21", "title": "Règles de sécurité (utilisation de machines)", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-05", "title": "Procédures et modes opératoires", "description": "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues."}}, "origin": null}, {"lastModifiedBy": null, "title": "Français", "description": "", "categoryLevel": {"title": "A1", "description": "Peut comprendre et utiliser des expressions familières et quotidiennes ainsi que des énoncés très simples qui visent à satisfaire des besoins concrets. Peut se présenter ou présenter quelqu'un et poser à une personne des questions la concernant - par exemple, sur son lieu d'habitation, ses relations, ce qui lui appartient, etc. - et peut répondre au même type de questions. Peut communiquer de façon simple si l'interlocuteur parle lentement et distinctement et se montre coopératif.", "score": 1, "category": {"code": "CCT-09", "title": "<PERSON><PERSON>", "description": "Permet d’identifier la ou les langues mobilisées dans l’activité. Chaque langue utilisée peut l’être à un niveau d’exigence différent dans une même activité. La langue principale est évaluée, y compris s’il s’agit de la langue officielle (français). Dans le monde professionnel, la norme de maîtrise du français se situe généralement entre B1 et B2, selon les domaines d’activités. On se réfère ici à la classification européenne de maîtrise des langues."}}, "origin": null}], "behaviors": [{"title": "Fibre commerciale", "description": "", "code": "B-05", "behaviorCategory": "SOCIABILITY", "categoryIndex": 2}], "activities": [{"lastModifiedBy": null, "title": "Je réponds à des demandes", "description": "", "inducedCapacities": [{"title": "Regarder avec attention", "description": "capacité à observer son environnement en identifiant les informations pertinentes pour l'action en cours", "code": "CA1-01", "inducedCapacities": []}]}], "noActivity": null, "noBehavior": null, "noContext": null, "lastModifiedBy": null}, {"uri": "URI for SK_1 http://data.odas.app", "title": "SK_1 http://data.odas.app", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [{"lastModifiedBy": null, "code": "CT-21", "title": "Règles de sécurité (utilisation de machines)", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-05", "title": "Procédures et modes opératoires", "description": "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues."}}, "origin": null}, {"lastModifiedBy": null, "code": "CT-29", "title": "Français", "description": "", "categoryLevel": {"title": "A1", "description": "Peut comprendre et utiliser des expressions familières et quotidiennes ainsi que des énoncés très simples qui visent à satisfaire des besoins concrets. Peut se présenter ou présenter quelqu'un et poser à une personne des questions la concernant - par exemple, sur son lieu d'habitation, ses relations, ce qui lui appartient, etc. - et peut répondre au même type de questions. Peut communiquer de façon simple si l'interlocuteur parle lentement et distinctement et se montre coopératif.", "score": 1, "category": {"code": "CCT-09", "title": "<PERSON><PERSON>", "description": "Permet d’identifier la ou les langues mobilisées dans l’activité. Chaque langue utilisée peut l’être à un niveau d’exigence différent dans une même activité. La langue principale est évaluée, y compris s’il s’agit de la langue officielle (français). Dans le monde professionnel, la norme de maîtrise du français se situe généralement entre B1 et B2, selon les domaines d’activités. On se réfère ici à la classification européenne de maîtrise des langues."}}, "origin": null}], "behaviors": [{"title": "Fibre commerciale", "description": "", "code": "B-05", "behaviorCategory": "SOCIABILITY", "categoryIndex": 2}], "activities": [{"lastModifiedBy": null, "title": "Je réponds à des demandes", "description": "", "inducedCapacities": [{"title": "Regarder avec attention", "description": "capacité à observer son environnement en identifiant les informations pertinentes pour l'action en cours", "code": "CA1-01", "inducedCapacities": []}]}], "noActivity": null, "noBehavior": null, "noContext": null, "lastModifiedBy": null}, {"uri": "URI for SK_2 http://data.odas.app", "title": "SK_2 http://data.odas.app", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [], "behaviors": [], "activities": [], "noActivity": true, "noBehavior": true, "noContext": true, "lastModifiedBy": null}]}