{"id": "rnon0d986b", "offerTitle": "Test", "remoteRecruiterCode": "Partenaire Talent Picker", "creationDate": "2024-05-06 15:58:09 +0000", "lastModificationDateInput": "2024-05-07 17:26:44 +0000", "applicationEmail": "<EMAIL>", "descriptionInput": {"profile": "<p style=\"text-align:left\">Test</p>", "mission": "<p style=\"text-align:left\">mission ici</p>", "company": "<p style=\"text-align:left\">Test</p>", "joinedDescription": "<p style=\"text-align:left\">mission ici</p>\n<p style=\"text-align:left\">Test</p>"}, "location": {"countryCode": "FR", "postCode": "33115", "city": "La Teste-de-Buch"}, "contract": {"rythm": {"hoursPerWeek": "35.0", "nbHoursPerWeek": 35.0}, "duration": null, "type": {"code": "PERMANENT"}, "hoursPerWeek": 35, "typeCategory": "PERMANENT"}, "salary": {"value": 33333.0, "duration": "YEAR"}, "studyLevels": [{"code": "ALL", "criteriaCode": "REP-8-1"}, {"code": "BAC", "criteriaCode": "REP-8-4"}], "inFrance": true, "typeContractCategory": "PERMANENT", "locationIndication": "La Teste-de-Buch (33115)", "criterias": ["REP-8-1"], "workingTimeWeeklyDuration": 35, "salaryValues": [33333], "organizationDescription": "<p style=\"text-align:left\">Test</p>", "lastModificationDate": "2024-05-07T17:26", "builtinLocation": null, "hideSalary": null, "workContractDurationUnit": null, "workContractDuration": null, "insideAURA": false, "relatedUsernames": [], "workingTimeType": "FULL_TIME", "candidatureEmail": null}