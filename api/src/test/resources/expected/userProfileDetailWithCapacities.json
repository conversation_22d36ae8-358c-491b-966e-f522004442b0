{"id": "Normal_GOOD_user", "generalInformation": {"creationDate": null, "lastConnectionDate": "2004-12-25T13:11:00Z", "firstName": "Jean-<PERSON>_GOOD_user", "lastName": "<PERSON><PERSON> Du Normal_GOOD_user", "email": "<EMAIL>", "transactionalBlacklisted": null, "phoneNumber": "06-07-08-09-10", "location": {"city": "Lyon", "citycode": "69123", "postcode": "69001", "departmentCode": "69", "regionName": "Auvergne-Rhône-Alpes", "longitude": 4.835, "latitude": 45.758, "radiusInKm": 20}, "contactTime": "AFTERNOON", "birthDate": "1990-07-20", "smsBlacklisted": null, "salary": null, "situation": null}, "experiences": [{"jobTitle": "Empty Job Nb 0", "organizationName": "Organization Nb 0", "experienceType": "JOB", "activities": [], "erhgoOccupationId": null, "erhgoOccupationTitle": null, "erhgoOccupationTotalCapacities": null, "erhgoOccupationMasteryLevel": null, "durationInMonths": 9}, {"jobTitle": "Job Nb 1", "organizationName": "Organization Nb 1", "experienceType": "JOB", "activities": [{"lastModifiedBy": null, "title": "Je traite des mails entrants", "description": null, "inducedCapacities": [{"title": "Regarder avec attention", "description": "capacité à observer son environnement en identifiant les informations pertinentes pour l'action en cours", "code": "CA1-01", "inducedCapacities": []}]}, {"lastModifiedBy": null, "title": "J'organise la logistique de mon rdv", "description": "", "inducedCapacities": [{"title": "Convaincre", "description": "capacité à emporter l'adhésion et faire changer les comportements d'autrui avec persuasion", "code": "CA3-05", "inducedCapacities": [{"title": "Suggérer/proposer", "description": "capacité à proposer un conseil, un bien, un service adapté au besoin exprimé par son interlocuteur", "code": "CA2-06", "inducedCapacities": [{"title": "Ecouter avec attention", "description": "capacité à se concentrer sur un message oral ou une source sonore (bruit, son, musique) dans le but de le comprendre et l'analyser", "code": "CA1-06", "inducedCapacities": []}, {"title": "S'exprimer à l'oral", "description": "capacité à se faire comprendre de son interlocuteur par la parole", "code": "CA1-05", "inducedCapacities": []}, {"title": "E<PERSON>rire", "description": "capacité à rédiger un message (manuscrit ou saisi) compréhensible", "code": "CA1-04", "inducedCapacities": []}]}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "capacité à développer une opinion, une décision dans le but de convaincre ses interlocuteurs", "code": "CA2-03", "inducedCapacities": [{"title": "Décrire/formuler", "description": "capacité à énoncer, représenter une observation, une idée, un problème de manière à la rendre compréhensible pour son interlocuteur", "code": "CA1-16", "inducedCapacities": []}, {"title": "Trier (des objets, des informations)", "description": "capacité à identifier différents éléments dans un ensemble, les distinguer, les organiser et les classer", "code": "CA1-13", "inducedCapacities": []}, {"title": "S'exprimer à l'oral", "description": "capacité à se faire comprendre de son interlocuteur par la parole", "code": "CA1-05", "inducedCapacities": []}, {"title": "E<PERSON>rire", "description": "capacité à rédiger un message (manuscrit ou saisi) compréhensible", "code": "CA1-04", "inducedCapacities": []}]}, {"title": "S'adapter à un interlocuteur", "description": "capacité à modifier son niveau de langage, ses explications, ses questions, en fonction de la personne et du contexte de la conversation", "code": "CA2-02", "inducedCapacities": [{"title": "Contextualiser", "description": "capacité à se rappeler d'un contexte, y replacer les événements évoqué par soi ou un tiers et en tenir compte", "code": "CA1-27", "inducedCapacities": []}, {"title": "Identifier un interlocuteur", "description": "capacité à distinguer la ou les personnes pertinente(s) pour une situation donnée", "code": "CA1-07", "inducedCapacities": []}, {"title": "Ecouter avec attention", "description": "capacité à se concentrer sur un message oral ou une source sonore (bruit, son, musique) dans le but de le comprendre et l'analyser", "code": "CA1-06", "inducedCapacities": []}, {"title": "S'exprimer à l'oral", "description": "capacité à se faire comprendre de son interlocuteur par la parole", "code": "CA1-05", "inducedCapacities": []}]}]}]}, {"lastModifiedBy": null, "title": "Je prépare un RDV Client", "description": "", "inducedCapacities": [{"title": "S'adapter à un interlocuteur", "description": "capacité à modifier son niveau de langage, ses explications, ses questions, en fonction de la personne et du contexte de la conversation", "code": "CA2-02", "inducedCapacities": [{"title": "Contextualiser", "description": "capacité à se rappeler d'un contexte, y replacer les événements évoqué par soi ou un tiers et en tenir compte", "code": "CA1-27", "inducedCapacities": []}, {"title": "Identifier un interlocuteur", "description": "capacité à distinguer la ou les personnes pertinente(s) pour une situation donnée", "code": "CA1-07", "inducedCapacities": []}, {"title": "Ecouter avec attention", "description": "capacité à se concentrer sur un message oral ou une source sonore (bruit, son, musique) dans le but de le comprendre et l'analyser", "code": "CA1-06", "inducedCapacities": []}, {"title": "S'exprimer à l'oral", "description": "capacité à se faire comprendre de son interlocuteur par la parole", "code": "CA1-05", "inducedCapacities": []}]}, {"title": "Analyser (une situation, une information)", "description": "capacité à décomposer une situation (ou une information) et ses éléments essentiels pour en saisir les liens et les implications afin de permettre une prise de décision", "code": "CA1-12", "inducedCapacities": []}]}], "erhgoOccupationId": "cb8a2922-6824-43d6-9c51-dad73d8cba11", "erhgoOccupationTitle": "Occupation for xp", "erhgoOccupationTotalCapacities": 4, "erhgoOccupationMasteryLevel": "PROFESSIONAL", "durationInMonths": 9}, {"jobTitle": "Job Title Nb 1", "organizationName": "Organization Nb 1", "experienceType": "JOB", "activities": [], "erhgoOccupationId": null, "erhgoOccupationTitle": null, "erhgoOccupationTotalCapacities": null, "erhgoOccupationMasteryLevel": null, "durationInMonths": 9}]}