{"firstName": "Jean-<PERSON>_GOOD_user", "lastName": "<PERSON><PERSON> Du Normal_GOOD_user", "email": "<EMAIL>", "phoneNumber": "06-07-08-09-10", "contactTime": "AFTERNOON", "birthDate": "1990-07-20", "salary": null, "delayInMonth": null, "location": {"city": "Lyon", "citycode": "69123", "postcode": "69001", "departmentCode": "69", "regionName": "Auvergne-Rhône-Alpes", "longitude": 4.835, "latitude": 45.758, "radiusInKm": 20}, "isSourceInitialized": false, "lastCandidatures": [{"title": "Developer Fullstack", "organization": "title M-03", "recruitmentCode": "R-1", "state": "VALIDATED"}, {"title": "Developer Backend", "organization": "<PERSON><PERSON><PERSON>", "recruitmentCode": "R-3", "state": "WAITING"}, {"title": "Developer Fullstack", "organization": "title M-03", "recruitmentCode": "R-2", "state": "CLOSED"}], "lastExperiences": [{"jobTitle": "Job Title Nb 1", "erhgoOccupationTitle": null, "organizationName": "Organization Nb 1", "durationInMonths": 9}, {"jobTitle": "Job Nb 1", "erhgoOccupationTitle": "Occupation for xp", "organizationName": "Organization Nb 1", "durationInMonths": 9}, {"jobTitle": "Empty Job Nb 0", "erhgoOccupationTitle": null, "organizationName": "Organization Nb 0", "durationInMonths": 9}], "behaviors": [{"title": "Title of BE-421", "description": "Descr of BE-421", "code": "BE-421", "behaviorCategory": "SOCIABILITY", "categoryIndex": 421}, {"title": "Title of BE-422", "description": "Descr of BE-422", "code": "BE-422", "behaviorCategory": "CONSTANCY", "categoryIndex": 422}], "criteria": [], "includesMenus": ["job_candidatures", "jobs_matching"], "isExportable": true, "incompleteInformations": ["SALARY", "LAST_CONNECTION_DATE"], "unreadNotificationsCount": 0, "blacklistedOccupations": [{"title": "<PERSON><PERSON><PERSON> refusé", "id": "00000000-0000-0000-0000-000000000000"}], "softSkillsStatus": "AVAILABLE"}