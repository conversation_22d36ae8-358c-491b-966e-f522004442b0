{"recruiterCode": "E-02", "jobType": "OBSERVED", "employerCode": "M-03", "title": "Developer Fullstack", "description": "Short description of job J-01", "service": "Web", "location": {"city": "Lyon", "radiusInKm": 30}, "createdBy": "<PERSON><PERSON><PERSON><PERSON>", "observators": ["<PERSON>"], "state": "PUBLISHED", "criteriaValues": [{"code": "CM-1", "titleForQuestion": "La nuit", "titleStandalone": "Title for CM-1", "titleForBO": "Title BO for CM-1", "icon": "smile", "valueIndex": 0}, {"code": "CT-1", "titleForQuestion": "La nuit", "titleStandalone": "Title for CT-1", "titleForBO": "Title BO for CT-1", "icon": "smile", "valueIndex": 0}], "behaviors": [{"title": "Goût du challenge", "code": "B-06"}, {"title": "Aisance réda<PERSON>nelle", "code": "B-07"}, {"title": "Etre avenant/souriant", "code": "B-01"}, {"title": "Etre pédagogue", "code": "B-02"}, {"title": "Etre résilient/capacité à rebondir", "code": "B-03"}, {"title": "<PERSON><PERSON><PERSON> son stress", "code": "B-04"}, {"title": "Fibre commerciale", "code": "B-05"}], "recommendation": null, "missions": [{"title": "M_03_TITLE", "activities": [{"title": "Je prépare un RDV Client", "description": "", "inducedCapacities": [{"title": "S'adapter à un interlocuteur", "description": "capacité à modifier son niveau de langage, ses explications, ses questions, en fonction de la personne et du contexte de la conversation", "code": "CA2-02"}, {"title": "Analyser (une situation, une information)", "description": "capacité à décomposer une situation (ou une information) et ses éléments essentiels pour en saisir les liens et les implications afin de permettre une prise de décision", "code": "CA1-12"}]}, {"title": "J'organise la logistique de mon rdv", "description": "", "inducedCapacities": [{"title": "Convaincre", "description": "capacité à emporter l'adhésion et faire changer les comportements d'autrui avec persuasion", "code": "CA3-05"}]}], "contextsForCategory": [{"contexts": [{"title": "Forte charge cognitive", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-01", "title": "Connaissances", "description": "Permet de décrire ce qu’il faut savoir et comprendre pour réaliser le travail. Les connaissances s'acquièrent par l’étude, la pratique ou les recherches."}}, "origin": null}, {"title": "Vocabulaire spécifique", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-01", "title": "Connaissances", "description": "Permet de décrire ce qu’il faut savoir et comprendre pour réaliser le travail. Les connaissances s'acquièrent par l’étude, la pratique ou les recherches."}}, "origin": null}], "category": {"code": "CCT-01", "title": "Connaissances", "description": "Permet de décrire ce qu’il faut savoir et comprendre pour réaliser le travail. Les connaissances s'acquièrent par l’étude, la pratique ou les recherches."}, "noContextForCategory": false}]}, {"title": "M_01_TITLE", "activities": [{"title": "J'assure le suivi des mails", "description": "", "inducedCapacities": [{"title": "Reconnaitre les formes", "description": "capacité à identifier et comprendre les différents symboles de l'environnement en fonction de leur forme, leur couleur et leur graphisme", "code": "CA1-02"}, {"title": "<PERSON><PERSON>", "description": "capacité à déchiffrer un texte et à en comprendre le sens", "code": "CA1-03"}, {"title": "E<PERSON>rire", "description": "capacité à rédiger un message (manuscrit ou saisi) compréhensible", "code": "CA1-04"}]}, {"title": "Je traite des mails entrants", "description": null, "inducedCapacities": [{"title": "Regarder avec attention", "description": "capacité à observer son environnement en identifiant les informations pertinentes pour l'action en cours", "code": "CA1-01"}]}], "contextsForCategory": [{"contexts": [{"title": "Règles de sécurité (règles de circulation)", "description": "Ceci est une règle de sécurité", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-05", "title": "Procédures et modes opératoires", "description": "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues."}}, "origin": null}, {"title": "Règles de tri des déchets", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-05", "title": "Procédures et modes opératoires", "description": "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues."}}, "origin": null}], "category": {"code": "CCT-05", "title": "Procédures et modes opératoires", "description": "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues."}, "noContextForCategory": false}]}, {"title": "M_02_TITLE", "activities": [{"title": "Je prépare un RDV Client", "description": "", "inducedCapacities": [{"title": "S'adapter à un interlocuteur", "description": "capacité à modifier son niveau de langage, ses explications, ses questions, en fonction de la personne et du contexte de la conversation", "code": "CA2-02"}, {"title": "Analyser (une situation, une information)", "description": "capacité à décomposer une situation (ou une information) et ses éléments essentiels pour en saisir les liens et les implications afin de permettre une prise de décision", "code": "CA1-12"}]}, {"title": "J'organise la logistique de mon rdv", "description": "", "inducedCapacities": [{"title": "Convaincre", "description": "capacité à emporter l'adhésion et faire changer les comportements d'autrui avec persuasion", "code": "CA3-05"}]}], "contextsForCategory": [{"contexts": [{"title": "Forte charge cognitive", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-01", "title": "Connaissances", "description": "Permet de décrire ce qu’il faut savoir et comprendre pour réaliser le travail. Les connaissances s'acquièrent par l’étude, la pratique ou les recherches."}}, "origin": null}, {"title": "Vocabulaire spécifique", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-01", "title": "Connaissances", "description": "Permet de décrire ce qu’il faut savoir et comprendre pour réaliser le travail. Les connaissances s'acquièrent par l’étude, la pratique ou les recherches."}}, "origin": null}], "category": {"code": "CCT-01", "title": "Connaissances", "description": "Permet de décrire ce qu’il faut savoir et comprendre pour réaliser le travail. Les connaissances s'acquièrent par l’étude, la pratique ou les recherches."}, "noContextForCategory": false}]}], "modifiable": false, "isPrivate": false}