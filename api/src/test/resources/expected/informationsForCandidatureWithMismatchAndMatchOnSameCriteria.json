{"question": "Question personnalisée", "answer": null, "requiresExperience": true, "condition": {"criteria": [{"selectedValuesCodesInRecruitment": ["MC3-2", "MC3-3"], "selectedValuesCodesByUser": [], "criteria": {"code": "CR4", "title": "Title for MULTIPLE", "questionLabel": "Question for MULTIPLE", "criteriaValues": [{"criteriaValueType": "CriteriaValueDTO", "code": "MC3-2", "titleForQuestion": "La nuit", "titleStandalone": "Title for MC3-2", "titleForBO": "Title BO for MC3-2", "icon": "smile", "valueIndex": 1, "sourcingCriteriaStep": null}, {"criteriaValueType": "CriteriaValueDTO", "code": "MC3-3", "titleForQuestion": "La nuit", "titleStandalone": "Title for MC3-3", "titleForBO": "Title BO for MC3-3", "icon": "smile", "valueIndex": 2, "sourcingCriteriaStep": null}], "questionType": "MULTIPLE", "required": false}}], "availabilityForCandidature": {"isAvailable": false, "availabilityDelayInMonth": 2}, "salary": {"recruitmentMin": 10000, "recruitmentMax": 20000, "desiredByUser": 30000}}, "contact": {"phoneNumber": null, "firstname": null, "lastname": null, "requiresLocation": true, "requiresCommunicationPreferences": true}}