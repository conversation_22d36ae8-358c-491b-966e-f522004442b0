{"escoOccupations": [{"uri": "http://2data.odas.app", "title": "Title of esco occupation http://2data.odas.app", "isco": {"iscoGroup": 888, "title": "Title for ISCO 888"}}, {"uri": "http://3data.odas.app", "title": "Title of esco occupation http://3data.odas.app", "isco": {"iscoGroup": 999, "title": "Title for ISCO 999"}}], "title": "title for base occupation", "description": null, "alternativeLabels": ["label for base occupation 1", "label for base occupation 2", "label for merged occupation 1", "label for merged occupation 2", "title for merged occupation"], "romeOccupations": [{"code": "R1", "title": "Title for ROME R1"}, {"code": "R2", "title": "Title for ROME R2"}, {"code": "R3", "title": "Title for ROME R3"}], "accessibleRomeOccupations": [{"code": "R1", "title": "Title for ROME R1"}, {"code": "R4", "title": "Title for ROME R4"}], "accessibleFromRomeOccupations": [{"code": "R3", "title": "Title for ROME R3"}, {"code": "R4", "title": "Title for ROME R4"}], "level": "STRATEGIC", "erhgoOccupationState": "MEDIUM", "skills": [{"uri": "URI for à Skill not qualified 3", "title": "<PERSON> <PERSON><PERSON> not qualified 3", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [], "behaviors": [], "activities": [], "noActivity": null, "noBehavior": null, "noContext": null, "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": null}, {"uri": "URI for Ä Skill not qualified 4", "title": "<PERSON> Skill not qualified 4", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [], "behaviors": [], "activities": [], "noActivity": null, "noBehavior": null, "noContext": null, "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": null}, {"uri": "URI for a Skill not qualified 5", "title": "a Skill not qualified 5", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [], "behaviors": [], "activities": [], "noActivity": null, "noBehavior": null, "noContext": null, "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": null}, {"uri": "URI for a skill qualified 3", "title": "a skill qualified 3", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [], "behaviors": [], "activities": [], "noActivity": true, "noBehavior": true, "noContext": true, "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": null}, {"uri": "URI for Â Skill qualified 4", "title": "<PERSON> Skill qualified 4", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [], "behaviors": [], "activities": [], "noActivity": true, "noBehavior": true, "noContext": true, "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": null}, {"uri": "URI for à Skill qualified 5", "title": "<PERSON><PERSON> qualified 5", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [], "behaviors": [], "activities": [], "noActivity": true, "noBehavior": true, "noContext": true, "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": null}, {"uri": "URI for Skill not qualified 1", "title": "Skill not qualified 1", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [], "behaviors": [], "activities": [], "noActivity": null, "noBehavior": null, "noContext": null, "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": null}, {"uri": "URI for Skill not qualified 2", "title": "Skill not qualified 2", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [], "behaviors": [], "activities": [], "noActivity": null, "noBehavior": null, "noContext": null, "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": null}, {"uri": "URI for Skill qualified 1", "title": "Skill qualified 1", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [], "behaviors": [], "activities": [], "noActivity": true, "noBehavior": true, "noContext": true, "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": null}, {"uri": "URI for Skill qualified 2", "title": "Skill qualified 2", "skillType": null, "descriptionEN": null, "descriptionFR": null, "alternativeLabels": [], "contexts": [], "behaviors": [], "activities": [], "noActivity": true, "noBehavior": true, "noContext": true, "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": null}], "activities": [{"objectType": "occupationActivity", "source": "MANUAL", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": "<PERSON><PERSON><PERSON><PERSON>", "skills": [], "state": "ESSENTIAL", "activity": {"auditedType": "activityLabelWithCapacities", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": null, "title": "Je réponds à des demandes", "description": "", "inducedCapacities": [{"title": "Regarder avec attention", "description": "capacité à observer son environnement en identifiant les informations pertinentes pour l'action en cours", "code": "CA1-01", "inducedCapacities": []}]}}, {"objectType": "occupationActivity", "source": "MANUAL", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": "<PERSON><PERSON><PERSON><PERSON>", "skills": [], "state": "ESSENTIAL", "activity": {"auditedType": "activityLabelWithCapacities", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": null, "title": "Je traite des mails entrants", "description": null, "inducedCapacities": [{"title": "Regarder avec attention", "description": "capacité à observer son environnement en identifiant les informations pertinentes pour l'action en cours", "code": "CA1-01", "inducedCapacities": []}]}}], "contexts": [{"objectType": "occupationContext", "source": "MANUAL", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": "<PERSON><PERSON><PERSON><PERSON>", "skills": [], "state": "OPTIONAL", "context": {"auditedType": "context", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": null, "code": "CT-4", "title": "Différentes lignes / différents services", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-01", "title": "Connaissances", "description": "Permet de décrire ce qu’il faut savoir et comprendre pour réaliser le travail. Les connaissances s'acquièrent par l’étude, la pratique ou les recherches."}}, "origin": null}}, {"objectType": "occupationContext", "source": "MANUAL", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": "<PERSON><PERSON><PERSON><PERSON>", "skills": [], "state": "ESSENTIAL", "context": {"auditedType": "context", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": null, "code": "CT-21", "title": "Règles de sécurité (utilisation de machines)", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-05", "title": "Procédures et modes opératoires", "description": "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues."}}, "origin": null}}], "behaviors": [{"objectType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "MANUAL", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": "<PERSON><PERSON><PERSON><PERSON>", "skills": [], "behavior": {"title": "Etre avenant/souriant", "description": "Faculté à sourire tout le temps", "code": "B-01", "behaviorCategory": "CONSTANCY", "categoryIndex": 1}}, {"objectType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "MANUAL", "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": "<PERSON><PERSON><PERSON><PERSON>", "skills": [], "behavior": {"title": "Goût du challenge", "description": "", "code": "B-06", "behaviorCategory": "TENACITY", "categoryIndex": 2}}], "updatedDate": "2020-02-01T23:00:00Z", "lastModifiedBy": "<PERSON><PERSON><PERSON><PERSON>", "behaviorsCategories": {"behaviorCategory1": "CONSTANCY", "behaviorCategory2": null, "behaviorCategory3": null, "isBehaviorCategory1Overloaded": false, "isBehaviorCategory2Overloaded": false, "isBehaviorCategory3Overloaded": false}, "behaviorsDescription": null, "isVisibleForOrientation": true, "workEnvironments": [], "isTechnical": true, "criteriaValues": [], "erhgoClassifications": [], "occupationCreationReason": null}