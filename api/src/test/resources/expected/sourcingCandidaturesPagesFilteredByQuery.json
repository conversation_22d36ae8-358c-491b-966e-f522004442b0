{"objectType": "sourcingCandidaturePage", "pageIndex": 0, "numberOfElementsInPage": 4, "pageSize": 4, "totalNumberOfElements": 4, "totalPages": 1, "content": [{"sourcingCandidatureType": "SourcingCandidatureItemDTO", "generated": false, "candidatureId": 7, "anonymousCode": "C 404100", "color": "1", "situation": "RESEARCHING", "firstName": "Prénom 404100", "lastName": "NOM 404100", "phone": "01234567404100", "email": "prénom 404100@nom 404100", "lastActionDate": "1970-01-01T05:25:00+01:00", "submissionDate": "1970-01-10T13:55:00+01:00", "answer": "Answer of 404100", "state": "TO_CONTACT", "lastNote": "Note nb 1 for 404100", "userId": "user-id-404100"}, {"sourcingCandidatureType": "SourcingCandidatureItemDTO", "generated": false, "candidatureId": 8, "anonymousCode": "C 404200", "color": "2", "situation": "STANDBY", "firstName": "Prénom 404200", "lastName": "NOM 404200", "phone": "01234567404200", "email": "prénom 404200@nom 404200", "lastActionDate": "1970-01-06T02:03:20+01:00", "submissionDate": "1970-01-15T10:36:40+01:00", "answer": "Answer of 404200", "state": "TO_CONTACT", "lastNote": "", "userId": "user-id-404200"}, {"sourcingCandidatureType": "SourcingCandidatureItemDTO", "generated": false, "candidatureId": 9, "anonymousCode": "C 404300", "color": "3", "situation": "EMPLOYEE", "firstName": "Prénom 404300", "lastName": "NOM 404300", "phone": "01234567404300", "email": "prénom 404300@nom 404300", "lastActionDate": "1970-01-10T22:41:40+01:00", "submissionDate": "1970-01-20T07:18:20+01:00", "answer": "Answer of 404300", "state": "TO_CONTACT", "lastNote": "", "userId": "user-id-404300"}, {"sourcingCandidatureType": "SourcingCandidatureItemDTO", "generated": false, "candidatureId": 10, "anonymousCode": "C 404400", "color": "4", "situation": "RESEARCHING", "firstName": "Prénom 404400", "lastName": "NOM 404400", "phone": "01234567404400", "email": "prénom 404400@nom 404400", "lastActionDate": "1970-01-15T19:20:00+01:00", "submissionDate": "1970-01-25T04:00:00+01:00", "answer": "Answer of 404400", "state": "TO_CONTACT", "lastNote": "Note nb 4 for 404400", "userId": "user-id-404400"}]}