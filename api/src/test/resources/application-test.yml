spring:
  data:
    jpa:
      repositories:
        bootstrap-mode: default
  jpa:
    properties:
      hibernate:
        cache:
          use_second_level_cache: false
          use_query_cache: false
  main:
    banner-mode: LOG
  datasource:
    url: jdbc:tc:mariadb:11.4.4://somehostname:?TC_TMPFS=/var/lib/mysql:rw&TC_DAEMON=true
    driver-class-name: org.testcontainers.jdbc.ContainerDatabaseDriver
    hikari:
      maxLifetime: 30000
      maximumPoolSize: 20
      minimumIdle: 5
keycloak:
  enabled: false
keycloak-realms:
  back_office_base_url: https://bo.erhgo.fr
  front_office_base_url: https://fo.erhgo.fr
  sourcing_base_url: https://sourcing.erhgo.fr

algolia:
  applicationId: ""
  apiKey: ""

logging:
  config: classpath:logback-test.xml

sendinblue:
  templates:
    confirm-account: 1
    confirm-account-fo-out-sector: 5
    sourcing-spontaneous-candidature-notification: 666
  forcedSourcingRecipients: [ ]

application:
  mobile:
    notificationTesters: ********-cf61-4885-beae-e6c24a7ef6a9,cae1c269-acbf-405c-8f68-cd47390f7a4d
trimoji:
  requestJsonTemplate:
    { "candidate_email": "%USER_EMAIL%","candidate_first_name": "%USER_FIRSTNAME%","candidate_last_name": "%USER_LASTNAME%","customer_token": "f3b06b20-a889-460b-b51d-7c5f415d6ded","is_trimoji_sending": false,"callback_url": "%CALLBACK_URL%","no_result": true,"test_duration": "short","metadatas": { "candidate_id": "%USER_ID%" } }
  urlJsonPath: $.data.link
  url: https://erhgo.fr
  callbackUrl: https://callback.erhgo.fr

