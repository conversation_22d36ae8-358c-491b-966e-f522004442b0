package com.erhgo.services;

import com.erhgo.domain.KeycloakUserSummary;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.repositories.AbstractOrganizationRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.google.common.base.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.config.core.GrantedAuthorityDefaults;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtClaimNames;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
@RequiredArgsConstructor
public class SecurityService {
    private static final String DEFAULT_ROLE_PREFIX = "ROLE_";
    private static final String SYSTEM_USER_NAME = "System user";

    private final UserProfileRepository userProfileRepository;
    private final AbstractOrganizationRepository organizationRepository;
    private final Optional<GrantedAuthorityDefaults> grantedAuthorityDefaults;

    public Optional<String> getKeycloakUserId() {
        return getKeycloakUserId(getAuthentication());
    }


    public boolean hasRole(Authentication authentication, String role) {
        return hasAnyRole(authentication, role);
    }

    public boolean hasAnyRole(Authentication authentication, String... roles) {
        var rolesWithPrefix = getRoles(roles);
        return rolesWithPrefix.stream().anyMatch(getAuthenticationRoles(authentication)::contains);
    }

    public boolean hasAllRoles(Authentication authentication, String... roles) {
        var rolesWithPrefix = getRoles(roles);
        return getAuthenticationRoles(authentication).containsAll(rolesWithPrefix);
    }


    public Set<String> getGroupsRelatedToAuthenticatedUser() {
        return getAuthenticationRoles(getAuthentication())
                .stream()
                .map(r -> r.replace(getRolePrefix(), ""))
                .filter(Role::isOrganizationRoleOrGroup)
                .collect(Collectors.toSet());
    }


    private Set<String> getAuthenticationRoles(Authentication authentication) {
        return Optional.ofNullable(authentication)
                .map(Authentication::getAuthorities)
                .map(Collection::stream)
                .orElse(Stream.empty())
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toSet());
    }

    private Set<String> getRoles(String... roles) {
        var rolePrefix = getRolePrefix();

        return Stream.of(roles)
                .filter(Predicate.not(Strings::isNullOrEmpty))
                .map(r -> rolePrefix + r)
                .collect(Collectors.toSet());
    }

    private String getRolePrefix() {
        return grantedAuthorityDefaults.map(GrantedAuthorityDefaults::getRolePrefix).orElse(DEFAULT_ROLE_PREFIX);
    }

    public String getAuthenticatedUserId() {
        return getKeycloakUserId().orElse(null);
    }

    public Optional<String> getKeycloakUserId(Authentication authentication) {
        return getJWTToken(authentication).map(t -> t.getSubject().intern());
    }

    public Optional<KeycloakUserSummary> getKeycloakUserSummary() {
        var keycloakUserId = getKeycloakUserId();
        return keycloakUserId.map(KeycloakUserSummary::new);
    }

    private Optional<Jwt> getJWTToken(Authentication authentication) {
        return Optional.ofNullable(authentication)
                .filter(Authentication::isAuthenticated)
                .map(Authentication::getPrincipal)
                .filter(Jwt.class::isInstance)
                .map(Jwt.class::cast);
    }

    public Authentication getAuthentication() {
        return SecurityContextHolder.getContext() == null ? null : SecurityContextHolder.getContext().getAuthentication();
    }

    public boolean isAuthenticated() {
        return SecurityContextHolder.getContext() != null
                && SecurityContextHolder.getContext().getAuthentication() != null
                && SecurityContextHolder.getContext().getAuthentication().isAuthenticated();
    }

    public boolean isAdmin() {
        return hasRole(getAuthentication(), Role.ODAS_ADMIN);
    }

    public void doAsAdmin(Runnable action) {
        var jwt = new Jwt("---generated by admin mock---", Instant.now(), null, Map.of("", ""), Map.of(JwtClaimNames.SUB, "ADMIN(BATCH)"));

        var authorities = Collections.singletonList(new SimpleGrantedAuthority(getRolePrefix() + Role.ODAS_ADMIN));
        var authentication = new UsernamePasswordAuthenticationToken(jwt, null, authorities);
        try {
            SecurityContextHolder.clearContext();
            SecurityContextHolder.getContext().setAuthentication(authentication);
            action.run();
        } finally {
            SecurityContextHolder.clearContext();
        }
    }

    public UserProfile getAuthenticatedUserProfile() {
        return getKeycloakUserId().flatMap(userProfileRepository::findByUserId)
                .orElseThrow(() -> new IllegalStateException("User not authenticated"));
    }

    public boolean hasAuthenticatedUserRole(String code) {
        return hasRole(getAuthentication(), code);
    }

    public boolean isAdminOrOTAdmin() {
        return hasAnyRole(getAuthentication(), Role.ODAS_ADMIN);
    }

    public boolean isOneChannelPrivate(Collection<String> channels) {
        var organizations = organizationRepository.findByCodeIn(channels);
        return organizations.stream().anyMatch(AbstractOrganization::isPrivateUsers);
    }

    public boolean hasAuthenticatedUserPrivateUserChannel() {
        var channels = getAuthenticatedUserProfile().channels();
        return isOneChannelPrivate(channels);
    }

    public Collection<String> intersectGroupsWithAuthenticatedUserAuthorizedGroups(Collection<String> groups) {
        return isAdmin() ? groups : groups.stream().filter(this::hasAuthenticatedUserRole).collect(Collectors.toSet());
    }

    public boolean hasAllRoles(String... roles) {
        return hasAllRoles(getAuthentication(), roles);
    }

    public boolean hasAuthenticatedUserAnyRole(String... roles) {
        return Stream.of(roles).anyMatch(this::hasAuthenticatedUserRole);
    }

    public boolean isCandidate() {
        return hasAuthenticatedUserAnyRole(Role.CANDIDATE);
    }

}
