package com.erhgo.services;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class MatchingJobCriteria {
    private final String userId;
    private final Integer page;
    private final Integer size;
    private final Float capacityThreshold;
    private final Boolean applyJobStateRestriction;
    private final List<String> organizationCodes;
    private final Boolean strictOrganizationFilter;
    private final Float masteryLevelRange;
    private final Boolean hasToFilterOnSourcingRecruitment;
}
