package com.erhgo.services;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.enums.ExperienceType;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.InvalidJobTitleException;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.event.UserExperienceUpdatedEvent;
import com.erhgo.domain.userprofile.experience.UserExperience;
import com.erhgo.domain.utils.EventPublisherUtils;
import com.erhgo.openapi.dto.ExperienceDetailsDTO;
import com.erhgo.openapi.dto.SaveExperienceCommandDTO;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.UserExperienceRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.security.AuthorizeExpression;
import com.erhgo.services.dtobuilder.UserExperienceDTOBuilder;
import com.erhgo.services.generation.OccupationForLabelGenerationService;
import com.erhgo.services.matching.MatchingService;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.notifier.OccupationCreationSourceType;
import com.erhgo.services.notifier.messages.ExperienceOnUnqualifiedOccupationMessageDTO;
import com.erhgo.services.userprofile.UserProfileProvider;
import com.google.common.collect.Sets;
import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserExperienceService {

    private final ErhgoOccupationRepository erhgoOccupationRepository;
    private final UserExperienceRepository experienceRepository;
    private final UserProfileProvider userProfileProvider;
    private final UserProfileRepository userProfileRepository;
    private final OccupationForLabelGenerationService occupationForLabelGenerationService;
    private final RecruitmentCandidatureRepository recruitmentCandidatureRepository;
    private final Notifier notifier;
    private final MatchingService matchingService;

    @Transactional
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_OF_COMMAND_WRITE)
    public ExperienceDetailsDTO saveExperience(SaveExperienceCommandDTO command, boolean generateOccupationIfNoneSet) {
        var userId = command.getUserId();
        var userProfile = userProfileProvider.getUserProfileOrCreateSilently(userId);

        var capacitiesBefore = new HashSet<>(userProfile.getAllCapacities());

        final var experience = getOrCreateExperienceForUser(command.getExperienceId(), userProfile)
                .setType(ExperienceType.valueOf(command.getExperienceType().toString()))
                .setJobTitle(command.getJobTitle())
                .setOrganizationName(command.getOrganizationName());

        experience.setDurationInMonths(command.getDurationInMonths())
                .setStartDate(command.getStartDate())
                .setEndDate(command.getEndDate());

        if (command.getErhgoOccupationId() != null || generateOccupationIfNoneSet) {
            var erhgoOccupation = getOrGenerateOccupation(command.getErhgoOccupationId(), experience);
            handleOccupationUpdate(experience, erhgoOccupation, userProfile);
        } else {
            if (experience.getErhgoOccupation() != null) {
                userProfile.removeCapacities(experience.getErhgoOccupation().getAllCapacitiesWithDuplicates());
                experience.setErhgoOccupation(null);
            }
        }
        // Fetch persisting user profile to update experiences to get the correct number when synchronising with Brevo
        var persistedExperience = experienceRepository.save(experience);
        var fetchedUser = userProfileRepository.findByUserId(userProfile.userId()).orElseThrow();
        fetchedUser.experiences().add(persistedExperience);
        // Quickfix due to wrong init of experiences in UserProfile - should use `refreshRemindState()` instead
        userProfile = experience.getUserProfile();
        if (userProfile != null && !userProfile.experiences().isEmpty()) {
            if (!Sets.difference(capacitiesBefore, userProfile.getAllCapacities()).isEmpty()
                    || !Sets.difference(userProfile.getAllCapacities(), capacitiesBefore).isEmpty()) {
                matchingService.refreshMatching(userId);
            }
        }
        EventPublisherUtils.publish(new UserExperienceUpdatedEvent(fetchedUser));
        return UserExperienceDTOBuilder.buildExperienceDetailsDTO(persistedExperience);
    }

    private void handleOccupationUpdate(UserExperience experience, ErhgoOccupation erhgoOccupation, UserProfile userProfile) {
        var occupationUpdated = experience.getErhgoOccupation() == null || !experience.getErhgoOccupation().getId().equals(erhgoOccupation.getId());
        if (occupationUpdated) {
            userProfile.addCapacities(erhgoOccupation.getAllCapacitiesWithDuplicates());
            if (experience.getErhgoOccupation() != null) {
                userProfile.removeCapacities(experience.getErhgoOccupation().getAllCapacitiesWithDuplicates());
            }
            if (!erhgoOccupation.isQualificationInFinalState() && isRelatedToSourcing(userProfile)) {
                notifier.sendMessage(new ExperienceOnUnqualifiedOccupationMessageDTO(erhgoOccupation.getId(), erhgoOccupation.getTitle(), userProfile.uuid()));
            }
        }
        experience.setErhgoOccupation(erhgoOccupation);
    }

    private ErhgoOccupation getOrGenerateOccupation(UUID commandOccupationUuid, UserExperience experience) {
        var actualOccupationId = new AtomicReference<>(commandOccupationUuid);
        if (actualOccupationId.get() == null) {
            var result = occupationForLabelGenerationService.createOrUpdateOccupation(experience.getJobTitle(), OccupationCreationSourceType.FROM_FRONT_OFFICE);
            if (result.inError()) {
                throw new InvalidJobTitleException(experience.getJobTitle());
            }
            actualOccupationId.set(result.uuid());
        }
        return erhgoOccupationRepository.findById(actualOccupationId.get()).orElseThrow(() -> new EntityNotFoundException(actualOccupationId.get(), ErhgoOccupation.class));
    }

    private boolean isRelatedToSourcing(UserProfile userProfile) {
        return recruitmentCandidatureRepository.findByUserProfileUserId(userProfile.userId()).stream()
                .anyMatch(RecruitmentCandidature::isSourcing);
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.USER_EXPERIENCE.USER_EXPERIENCE_WRITE)
    public void deleteExperience(UUID experienceId) {
        var experience = experienceRepository.findById(experienceId).orElseThrow(() -> new EntityNotFoundException(experienceId, UserExperience.class));
        var userProfile = experience.getUserProfile();
        // Updating manually the number of experiences because transaction is not fully committed
        userProfile.experiences().remove(experience);
        experience.delete();
        EventPublisherUtils.publish(new UserExperienceUpdatedEvent(userProfile));
        matchingService.refreshMatching(userProfile.userId());
    }

    private UserExperience getOrCreateExperienceForUser(UUID experienceId, UserProfile userProfile) {
        return experienceRepository.findById(experienceId)
                .orElseGet(() -> UserExperience.builder().uuid(experienceId)
                        .userProfile(userProfile)
                        .build());
    }

    private @Nonnull
    UserExperience getExperienceOrThrow(UUID experienceId) {
        var experienceOpt = experienceRepository.findById(experienceId);
        return experienceOpt.orElseThrow(() -> new EntityNotFoundException(experienceId, UserExperience.class));
    }

    @Transactional(readOnly = true)
    public ExperienceDetailsDTO getExperience(UUID experienceId) {
        return UserExperienceDTOBuilder.buildExperienceDetailsDTO(getExperienceOrThrow(experienceId));
    }
}
