package com.erhgo.services.matching;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.enums.CandidatureState;
import com.erhgo.domain.enums.Frequency;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.recruitment.RecruitmentProfile;
import com.erhgo.domain.referential.Activity;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.domain.userprofile.experience.JobContextMet;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MatchingService {

    private final RecruitmentCandidatureRepository candidatureRepository;

    public MatchingResult computeMatchingResult(RecruitmentCandidature candidature) {

        var recruitmentProfile = candidature.getRecruitmentProfile();
        var userExperiences = candidature.getUserProfile().experiences();
        var levelForCapacityMap = candidature.getUserProfile().buildLevelForCapacityMap();

        var controlledContexts = candidature.getUserProfile().jobContextsMet().stream()
                .filter(cm -> cm.getFrequency().equals(Frequency.HIGH) || cm.getFrequency().equals(Frequency.MEDIUM))
                .map(JobContextMet::getContext)
                .collect(Collectors.toSet());

        var controlledActivities = userExperiences == null ? Collections.<Activity>emptySet() : userExperiences.stream().flatMap(ux -> ux.getAllActivities(true).stream()).map(JobActivityLabel::getActivity).collect(Collectors.toSet());

        var matchingActivities = recruitmentProfile.getJob().getAllMissionsActivities().stream()
                .map(a -> buildMatchingActivity(a, recruitmentProfile, controlledActivities, levelForCapacityMap))
                .collect(Collectors.toSet());

        var matchingContexts = recruitmentProfile.getJob().getAllMissionsContexts().stream()
                .map(a -> buildMatchingContext(a, recruitmentProfile, controlledContexts))
                .collect(Collectors.toSet());

        return MatchingResult.builder().activities(matchingActivities).contexts(matchingContexts).build();
    }

    @Transactional
    @Async
    public void refreshMatching(String userId) {
        var candidatures = candidatureRepository.findByUserProfileUserId(userId);
        candidatures.stream().filter(RecruitmentCandidature::isSubmitted).forEach(this::refreshMatchingInternal);
    }

    public void processMatching(RecruitmentCandidature candidature) {
        candidature.matches(getMatchingResult(candidature));
    }

    MatchingResult getMatchingResult(RecruitmentCandidature candidature) {
        return computeMatchingResult(candidature);
    }

    @Transactional
    @Async
    public void refreshMatching(Recruitment recruitment) {
        candidatureRepository.findByRecruitmentCodeAndState(recruitment.getCode(), CandidatureState.VALIDATED).forEach(this::refreshMatchingInternal);
    }

    @Transactional
    @Async
    public void refreshMatchingAsync(RecruitmentCandidature candidatureIn) {
        var candidature = candidatureRepository.findById(candidatureIn.getId()).orElseThrow();
        refreshMatchingInternal(candidature);
    }

    private void refreshMatchingInternal(RecruitmentCandidature candidature) {
        processMatching(candidature);
        candidatureRepository.save(candidature);
    }

    public void refreshMatching(RecruitmentCandidature candidatureIn) {
        refreshMatchingInternal(candidatureIn);
    }

    private MatchingResult.MatchingContext buildMatchingContext(Context context, RecruitmentProfile recruitmentProfile, Set<Context> controlledContexts) {
        var acquisitionModality = recruitmentProfile.getAcquisitionModality(context);

        return MatchingResult.MatchingContext.builder()
                .context(context)
                .acquisitionModality(acquisitionModality)
                .mandatory(acquisitionModality == null)
                .matches(controlledContexts.contains(context))
                .build();
    }

    private MatchingResult.MatchingActivity buildMatchingActivity(JobActivityLabel jobActivityLabel, RecruitmentProfile recruitmentProfile, Set<Activity> controlledActivities, Map<Capacity, Integer> levelForCapacity) {
        var acquisitionModality = recruitmentProfile.getAcquisitionModality(jobActivityLabel);

        var level = getActivityLevel(jobActivityLabel, levelForCapacity);
        var matches = level != null;

        return MatchingResult.MatchingActivity.builder()
                .jobActivityLabel(jobActivityLabel)
                .acquisitionModality(acquisitionModality)
                .mandatory(acquisitionModality == null)
                .matches(matches)
                .level(level)
                .inOwn(matches ? controlledActivities.contains(jobActivityLabel.getActivity()) : null)
                .build();
    }

    public static Integer getActivityLevel(JobActivityLabel jobActivityLabel, Map<Capacity, Integer> levelForCapacity) {
        var activityLevel = levelForCapacity.entrySet().stream().filter(e -> jobActivityLabel.getInducedCapacities().contains(e.getKey())).map(Map.Entry::getValue).toList();
        Integer level = null;
        if (!activityLevel.isEmpty() && activityLevel.size() == jobActivityLabel.getInducedCapacities().size()) {
            level = activityLevel.stream().mapToInt(a -> a).min().orElseThrow();
        }
        return level;
    }


}
