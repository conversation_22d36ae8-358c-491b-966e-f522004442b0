package com.erhgo.services.matching;

import com.erhgo.domain.enums.AcquisitionModality;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.JobActivityLabel;
import lombok.Builder;
import lombok.Data;

import java.util.Set;

@Builder
@Data
public class MatchingResult {

    public static final int EFFORT_FOR_ACTIVITY = 20;
    private Set<MatchingActivity> activities;
    private Set<MatchingContext> contexts;

    public boolean matches() {
        return activities.stream().allMatch(a -> !a.mandatory || a.matches)
                && contexts.stream().allMatch(c -> !c.mandatory || c.matches);
    }

    public int getEffort() {
        return getContextEffort() + getActivityEffort();
    }

    private int getActivityEffort() {
        return activities == null ? 0 :
                activities.stream().filter(a -> !a.mandatory && !a.matches)
                        .mapToInt(a -> EFFORT_FOR_ACTIVITY * a.acquisitionModality.getEffortFactor())
                        .reduce(0, Integer::sum);

    }

    private int getContextEffort() {
        return contexts == null ? 0 :
                contexts.stream().filter(c -> !c.mandatory && !c.matches)
                        .mapToInt(c -> c.getContext().getCategoryLevel().getScore() * c.acquisitionModality.getEffortFactor())
                        .reduce(0, Integer::sum);
    }

    @Builder
    @Data
    public static class MatchingActivity {
        private JobActivityLabel jobActivityLabel;
        private boolean mandatory;
        private boolean matches;
        private Boolean inOwn;
        private Integer level;
        private AcquisitionModality acquisitionModality;
    }

    @Builder
    @Data
    public static class MatchingContext {
        private Context context;
        private boolean mandatory;
        private boolean matches;
        private AcquisitionModality acquisitionModality;
    }
}
