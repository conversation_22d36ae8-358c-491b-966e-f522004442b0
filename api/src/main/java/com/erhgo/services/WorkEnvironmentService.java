package com.erhgo.services;

import com.erhgo.openapi.dto.WorkEnvironmentDTO;
import com.erhgo.repositories.classifications.WorkEnvironmentRepository;
import com.erhgo.services.dtobuilder.WorkEnvironmentDTOBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class WorkEnvironmentService {
    private final WorkEnvironmentRepository workEnvironmentRepository;
    private final WorkEnvironmentDTOBuilder workEnvironmentDTOBuilder;

    @Transactional(readOnly = true)
    public List<WorkEnvironmentDTO> listWorkEnvironments() {
        return workEnvironmentRepository.findAllByOrderByCode().stream().map(workEnvironmentDTOBuilder::buildWorkEnvironmentDTO).toList();
    }
}
