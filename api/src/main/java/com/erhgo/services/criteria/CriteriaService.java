package com.erhgo.services.criteria;

import com.erhgo.domain.criteria.Criteria;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.CriteriaRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.AuthorizeExpression;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import com.erhgo.services.dtobuilder.CriteriaDTOBuilder;
import com.erhgo.services.dtobuilder.SimpleCriteriaDTOBuilder;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CriteriaService {

    private final CriteriaRepository criteriaRepository;
    private final UserProfileRepository userProfileRepository;

    private final CriteriaDTOBuilder criteriaDTOBuilder;
    private final SimpleCriteriaDTOBuilder simpleCriteriaDTOBuilder;
    private final SecurityService securityService;

    private static final List<String> CRITERIA_TO_FILTER_LIST = List.of("CR-1", "CR-2", "CR-3", "CR-4", "CR-6", "CR-8", "CR-12");

    @Transactional(readOnly = true)
    public List<CriteriaDTO> getCriteria() {
        return criteriaRepository.findAllByOrderByCriteriaIndex()
                .stream()
                .map(criteriaDTOBuilder::buildDTO)
                .toList();
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.CANDIDATE)
    public List<SimpleCriteriaDTO> getSimpleUserCriteria() {
        var userId = securityService.getAuthenticatedUserId();
        return criteriaRepository.findCriteriaValuesByUserIdAndCriteriaCodes(userId, CRITERIA_TO_FILTER_LIST)
                .stream()
                .collect(Collectors.groupingBy(x -> x.getCriteriaValue().getCriteria()))
                .entrySet()
                .stream()
                .sorted(Comparator.comparing(a -> a.getKey().getCriteriaIndex()))
                .map(simpleCriteriaDTOBuilder::buildDTO)
                .toList();
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_OF_COMMAND_WRITE)
    public void setUserCriteria(SaveUserCriteriasCommandDTO command) {
        var userId = command.getUserId();
        var userProfile = userProfileRepository.findByUserId(userId).orElseThrow(() -> new EntityNotFoundException(userId, UserProfile.class));

        updateCriteriaAnswer(command.getSelectedValueCodes(), command.getUnselectedValueCodes(), userProfile);
    }

    @Transactional
    @RolesAllowed(Role.CANDIDATE)
    public void resetAuthenticatedUserSimpleCriteria() {
        var user = securityService.getAuthenticatedUserProfile();
        var criteriaValuesToRemove = criteriaRepository.findCriteriaValuesByUserIdAndCriteriaCodes(user.userId(), CRITERIA_TO_FILTER_LIST);
        user.resetCriteria(criteriaValuesToRemove);
    }

    private void updateCriteriaAnswer(List<String> selectedValueCodes, List<String> unselectedValueCodes, UserProfile userProfile) {
        var selectedValuesCodes = new HashSet<>(selectedValueCodes);
        var unselectedValuesCodes = new HashSet<>(unselectedValueCodes);

        userProfile.updateAnswerToCriteria(
                criteriaRepository.findCriteriaValuesByCriteriaValuesCodeIn(selectedValuesCodes),
                criteriaRepository.findCriteriaValuesByCriteriaValuesCodeIn(unselectedValuesCodes)
        );
    }


    private void updateCriterionValue(EditCriteriaCommandDTO command, CriteriaValue criteriaValue) {
        command.getCriteriaValues()
                .stream()
                .filter(c -> c.getCode().equals(criteriaValue.getCode()))
                .findFirst()
                .ifPresent(editCriteriaValueCommand -> this.updateCriterionFields(editCriteriaValueCommand, criteriaValue));
    }

    private void updateCriterionFields(EditCriteriaValueCommandDTO editCriteriaValueCommand, CriteriaValue criteriaValue) {
        criteriaValue.setTitleForBO(editCriteriaValueCommand.getTitleForBO())
                .setTitleForQuestion(editCriteriaValueCommand.getTitleForQuestion())
                .setTitleStandalone(editCriteriaValueCommand.getTitleStandalone())
                .setIcon(editCriteriaValueCommand.getIcon());
    }

    @Transactional
    public void updateCriterion(String code, EditCriteriaCommandDTO command) {
        var criterion = criteriaRepository.findById(code).orElseThrow(() -> new EntityNotFoundException(code, Criteria.class));
        criterion
                .setTitle(command.getTitle())
                .setQuestionLabel(command.getQuestionLabel());
        criterion.getCriteriaValues().forEach(c -> this.updateCriterionValue(command, c));
    }
}
