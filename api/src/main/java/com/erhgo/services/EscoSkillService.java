package com.erhgo.services;

import com.erhgo.domain.classifications.esco.EscoSkill;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.referential.Behavior;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.BehaviorRepository;
import com.erhgo.repositories.ContextRepository;
import com.erhgo.repositories.JobActivityLabelRepository;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.repositories.classifications.EscoSkillRepository;
import com.erhgo.security.Role;
import com.erhgo.services.dtobuilder.EscoSkillDTOBuilder;
import com.google.common.collect.Sets;
import jakarta.annotation.security.RolesAllowed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

import static org.apache.commons.lang3.BooleanUtils.isTrue;

@Service
@RolesAllowed(Role.ODAS_ADMIN)
@Slf4j
public class EscoSkillService {

    @Autowired
    private EscoSkillRepository escoSkillRepository;
    @Autowired
    private JobActivityLabelRepository jobActivityLabelRepository;
    @Autowired
    private ContextRepository contextRepository;
    @Autowired
    private BehaviorRepository behaviorRepository;
    @Autowired
    private EscoSkillDTOBuilder escoSkillDTOBuilder;

    @Autowired
    private ErhgoOccupationRepository erhgoOccupationRepository;

    @Transactional(readOnly = true)
    public SkillDTO getByUri(String uri) {
        return escoSkillDTOBuilder.buildSkillDTO(findByUri(uri));
    }

    private EscoSkill findByUri(String uri) {
        return escoSkillRepository.findById(uri)
                .orElseThrow(() -> new EntityNotFoundException(uri, EscoSkill.class));
    }

    @Transactional
    public void updateDescriptionFRForSkill(UpdateSkillDescriptionCommandDTO updateSkillDescriptionCommandDTO) {
        var skill = findByUri(updateSkillDescriptionCommandDTO.getUri());
        skill.setDescriptionFR(updateSkillDescriptionCommandDTO.getDescription());
        escoSkillRepository.save(skill);
    }

    @Transactional
    public void linkWithActivity(SkillLinkToActivityCommandDTO command) {
        var skill = findByUri(command.getUri());
        var activityLabel = getActivityOrThrow(command.getActivityId());
        skill.linkActivity(activityLabel);
        escoSkillRepository.save(skill);
        erhgoOccupationRepository.findBySkills(skill).forEach(occupation -> occupation.addQualifiedActivity(activityLabel));
    }

    @Transactional
    public void deleteLinkToActivity(SkillLinkToActivityCommandDTO command) {
        var skill = findByUri(command.getUri());
        var activity = getActivityOrThrow(command.getActivityId());
        var removed = skill.removeActivity(activity);
        if (!removed) {
            log.warn("Unable to remove {} from {}", activity, skill);
        }
        escoSkillRepository.save(skill);
        erhgoOccupationRepository.findBySkills(skill).forEach(occupation -> occupation.removeQualifiedEntity(activity.getUuid()));
    }

    private JobActivityLabel getActivityOrThrow(UUID uuid) {
        return jobActivityLabelRepository.findById(uuid)
                .orElseThrow(() -> new EntityNotFoundException(uuid, JobActivityLabel.class));
    }

    @Transactional
    public void linkWithContext(SkillLinkToContextCommandDTO command) {
        var skill = findByUri(command.getUri());
        var context = getContextOrThrow(command);
        skill.addContext(context);
        escoSkillRepository.save(skill);
        erhgoOccupationRepository.findBySkills(skill).forEach(occupation -> occupation.addQualifiedContext(context));
    }

    private Context getContextOrThrow(SkillLinkToContextCommandDTO command) {
        return contextRepository.findById(command.getContextId()).orElseThrow(() -> new EntityNotFoundException(command.getContextId(), Context.class));
    }

    @Transactional
    public void deleteLinkToContext(SkillLinkToContextCommandDTO command) {
        var skill = findByUri(command.getUri());
        skill.removeContext(command.getContextId());
        escoSkillRepository.save(skill);
        var context = getContextOrThrow(command);
        erhgoOccupationRepository.findBySkills(skill).forEach(occupation -> occupation.removeQualifiedEntity(context.getId()));

    }

    @Transactional
    public void linkWithBehavior(SkillLinkToBehaviorCommandDTO skillLinkToBehaviorCommandDTO) {
        var skill = findByUri(skillLinkToBehaviorCommandDTO.getUri());
        var behavior = behaviorRepository.findById(skillLinkToBehaviorCommandDTO.getBehaviorId()).orElseThrow(() -> new EntityNotFoundException(skillLinkToBehaviorCommandDTO.getBehaviorId(), Behavior.class));
        skill.addBehavior(behavior);
        escoSkillRepository.save(skill);
        erhgoOccupationRepository.findBySkills(skill).forEach(occupation -> occupation.addQualifiedBehavior(behavior));

    }

    @Transactional
    public void deleteLinkToBehavior(SkillLinkToBehaviorCommandDTO command) {
        var skill = findByUri(command.getUri());
        skill.removeBehavior(command.getBehaviorId());
        escoSkillRepository.save(skill);
        var behavior = getBehaviorOrThrow(command.getBehaviorId());
        erhgoOccupationRepository.findBySkills(skill).forEach(occupation -> occupation.removeQualifiedEntity(behavior.getId()));
    }

    private Behavior getBehaviorOrThrow(UUID behaviorId) {
        return behaviorRepository.findById(behaviorId).orElseThrow(() -> new EntityNotFoundException(behaviorId, Behavior.class));
    }

    @Transactional
    public void setNoActivity(SkillSetBooleanValueCommandDTO skillSetBooleanValueCommandDTO) {
        var skill = findByUri(skillSetBooleanValueCommandDTO.getUri());
        var previousActivities = Sets.newHashSet(skill.getActivities());
        skill.computeNoActivity(skillSetBooleanValueCommandDTO.getValue());
        escoSkillRepository.save(skill);
        if (isTrue(skill.getNoActivity())) {
            erhgoOccupationRepository.findBySkills(skill).forEach(occupation -> previousActivities.stream().map(JobActivityLabel::getUuid).forEach(occupation::removeQualifiedEntity));
        }
    }

    @Transactional
    public void setNoBehavior(SkillSetBooleanValueCommandDTO skillSetBooleanValueCommandDTO) {
        var skill = findByUri(skillSetBooleanValueCommandDTO.getUri());
        var previousBehaviors = Sets.newHashSet(skill.getBehaviors());

        skill.computeNoBehavior(skillSetBooleanValueCommandDTO.getValue());
        escoSkillRepository.save(skill);
        if (isTrue(skill.getNoBehavior())) {
            erhgoOccupationRepository.findBySkills(skill).forEach(occupation -> previousBehaviors.stream().map(Behavior::getId).forEach(occupation::removeQualifiedEntity));
        }
    }

    @Transactional
    public void setNoContext(SkillSetBooleanValueCommandDTO command) {
        var skill = findByUri(command.getUri());
        var previousContexts = Sets.newHashSet(skill.getContexts());

        skill.computeNoContext(command.getValue());
        escoSkillRepository.save(skill);
        if (isTrue(skill.getNoContext())) {
            erhgoOccupationRepository.findBySkills(skill).forEach(occupation -> previousContexts.stream().map(Context::getId).forEach(occupation::removeQualifiedEntity));
        }
    }

}
