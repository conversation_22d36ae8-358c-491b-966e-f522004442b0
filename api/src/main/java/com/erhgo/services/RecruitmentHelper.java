package com.erhgo.services;

import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.repositories.RecruitmentProfileRepository;
import com.erhgo.repositories.RecruitmentRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RecruitmentHelper {

    private final RecruitmentProfileRepository recruitmentProfileRepository;
    private final RecruitmentRepository recruitmentRepository;

    public Collection<Recruitment> findRecruitments(UUID jobId) {
        var profiles = recruitmentProfileRepository.findByJobIdOrderByTitleAsc(jobId);
        Collection<Recruitment> selectedRecruitments;
        if (profiles.isEmpty()) {
            selectedRecruitments = Collections.emptyList();
        } else {
            selectedRecruitments = recruitmentRepository
                    .findByRecruitmentProfileIn(new HashSet<>(profiles))
                    .stream()
                    .filter(Recruitment::isSelectableForCandidatureGeneration)
                    .collect(Collectors.toSet());
        }
        return selectedRecruitments;
    }
}
