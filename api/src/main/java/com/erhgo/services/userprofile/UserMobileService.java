package com.erhgo.services.userprofile;

import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.userprofile.UserMobileToken;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.event.UserMobileTokenUpdatedEvent;
import com.erhgo.domain.utils.EventPublisherUtils;
import com.erhgo.openapi.dto.SaveUserMobileTokenCommandDTO;
import com.erhgo.repositories.UserMobileTokenRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.AuthorizeExpression;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.util.stream.Collectors;

import static com.erhgo.utils.DateTimeUtils.ZONE_ID;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserMobileService {
    private final UserProfileRepository userProfileRepository;
    private final UserMobileTokenRepository userMobileRepository;

    @Value("${application.mobile.tokenLifetimeInMonths}")
    private int tokenLifetimeInMonths;

    private UserProfile getUserProfileOrThrow(String userId) {
        return userProfileRepository.findByUserId(userId).orElseThrow(() -> new EntityNotFoundException(userId, UserProfile.class));
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_WRITE)
    public void setUserMobileToken(SaveUserMobileTokenCommandDTO command) {
        var userMobileTokens = userMobileRepository.findByUserProfileUserId(command.getUserId());
        var userProfile = getUserProfileOrThrow(command.getUserId());
        userProfile.markNewMobileConnection();
        userMobileTokens.stream().filter(n -> n.getToken().equals(command.getToken())).findFirst()
                .ifPresentOrElse(t -> t.setTimestamp(OffsetDateTime.now(ZONE_ID)),
                        () -> {
                            var existantToken = UserMobileToken.builder()
                                    .token(command.getToken())
                                    .timestamp(OffsetDateTime.now(ZONE_ID))
                                    .userProfile(userProfile)
                                    .build();

                            userMobileRepository.save(existantToken);
                        });

        EventPublisherUtils.publish(new UserMobileTokenUpdatedEvent(userProfile));
    }


    @Transactional
    public void cleanUsersMobileTokens() {
        var oldestTokenDate = OffsetDateTime.now().minusMonths(tokenLifetimeInMonths);
        var obsoleteTokens = userMobileRepository.findObsoleteUserMobileTokens(oldestTokenDate);

        userMobileRepository.deleteAll(obsoleteTokens);

        obsoleteTokens.stream()
                .map(UserMobileToken::getUserProfile)
                .collect(Collectors.toSet())
                .stream()
                .map(UserMobileTokenUpdatedEvent::new)
                .forEach(EventPublisherUtils::publish);

        if (!obsoleteTokens.isEmpty()) {
            log.info("Successfully cleaned {} obsolete user mobile tokens", obsoleteTokens.size());
        } else {
            log.debug("No obsolete mobile token");
        }
    }
}
