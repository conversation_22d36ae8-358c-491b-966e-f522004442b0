package com.erhgo.services.userprofile;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.openapi.dto.BulkCVProcessingResultDTO;
import com.erhgo.security.Role;
import com.erhgo.services.HandicapAccountService;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.recruitmentimporter.OfferCsvParserService;
import com.erhgo.services.userprofile.dto.BulkCVProcessingRowDTO;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.security.RolesAllowed;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class BulkCVProcessingService {

    private final OfferCsvParserService csvParserService;
    private final HandicapAccountService handicapAccountService;
    private final KeycloakService keycloakService;
    private final UserProfileProvider userProfileProvider;
    private final ApplicationContext applicationContext;
    private final TransactionTemplate transactionTemplate;

    @RolesAllowed(Role.ODAS_ADMIN)
    public BulkCVProcessingResultDTO createOrUpdateProfilesForCVs(MultipartFile csvFile, boolean excludeExistingUsers) {
        try {
            var csvRows = csvParserService.parseOffersFromCsv(csvFile.getInputStream(), BulkCVProcessingRowDTO.class);

            var validationResult = validateAndPrepareRows(csvRows, excludeExistingUsers);

            var filteredRows = validationResult.getValue();
            if (filteredRows.isEmpty()) {
                log.info("No line to process");
            } else {
                log.info("Launching async processing of {} lines", filteredRows.size());
                applicationContext.getBean(BulkCVProcessingService.class).processRowsSequentially(filteredRows, excludeExistingUsers);
                log.info("Async processing of {} lines launch", filteredRows.size());
            }

            return validationResult.getKey();

        } catch (RuntimeException | IOException e) {
            log.error("Error reading CSV file", e);
            throw new GenericTechnicalException("Error reading csv file: " + e.getMessage());
        }
    }

    private Map.Entry<BulkCVProcessingResultDTO, List<BulkCVProcessingRowDTO>> validateAndPrepareRows(List<BulkCVProcessingRowDTO> csvRows, boolean excludeExistingUsers) {
        var validRows = csvRows.stream()
                .filter(BulkCVProcessingRowDTO::isValid)
                .filter(row -> !excludeExistingUsers || shouldIncludeUser(row.email()))
                .toList();
        var validEmails = validRows.stream().map(row -> row.email().trim()).toList();
        var invalidEmails = Sets.difference(csvRows.stream().map(row -> row.email().trim()).collect(Collectors.toSet()), Sets.newHashSet(validEmails));
        var totalRowsNb = csvRows.size();
        var validRowsNb = validRows.size();

        var invalidRows = totalRowsNb - validRowsNb;

        var message = String.format("CSV validé: %d lignes au total, %d en cours de traitement, %d ignorées. Traitement en cours, voir le support pour la suite...",
                totalRowsNb, validRowsNb, invalidRows);

        log.info("CSV validation completed - Total: {}, Valid: {}, Invalid: {}, Emails: {}",
                totalRowsNb, validRowsNb, invalidRows, validEmails);

        return Map.of(new BulkCVProcessingResultDTO()
                .totalRows(totalRowsNb)
                .validRows(validRowsNb)
                .invalidRows(invalidRows)
                .emails(validEmails)
                .invalidEmails(new ArrayList<>(invalidEmails))
                .message(message), validRows).entrySet().stream().findFirst().get();
    }

    @Async
    public void processRowsSequentially(List<BulkCVProcessingRowDTO> csvRows, boolean excludeExistingUsers) {
        log.info("Starting sequential processing of {} lines", csvRows.size());

        csvRows.stream()
                .map(row -> new ProcessingData(row.email().trim(), row.url().trim()))
                .forEach(data -> processRow(data.email(), data.url(), excludeExistingUsers));

        log.info("Sequential processing completed for all lines");
    }

    private void processRow(String email, String url, boolean excludeExistingUsers) {
        log.info("Starting processing - email: {}, url: {}", email, url);

        try {
            var request = Optional.ofNullable(url)
                    .map(u -> new Request.Builder().url(u).get().build());
            var result = handicapAccountService.createOrUpdateUserForFileURL(email, request, excludeExistingUsers);
            var experienceCount = getUserExperienceCount(email);

            log.info("Processing completed - email: {}, status: {}, experience count: {}",
                    email, result, experienceCount);

        } catch (RuntimeException e) {
            log.warn("Processing completed - email: {}, error: {}", email, e.getMessage(), e);
        }
    }

    private record ProcessingData(String email, String url) {
    }

    private boolean shouldIncludeUser(String email) {
        try {
            var kcUser = keycloakService.getFOUserRepresentationByEmail(email);
            if (kcUser == null) {
                log.info("User excluded - does not exist in Keycloak: {}", email);
                return false;
            }
            return transactionTemplate.execute(status -> {
                var userProfile = userProfileProvider.getUserProfileOrCreateForHandicap(kcUser.getId());
                if (userProfile.experiences() != null && userProfile.experiences().size() > 1) {
                    log.info("User excluded - already has {} experiences: {}", userProfile.experiences().size(), email);
                    return false;
                }
                if (!userProfile.canImportFile()) {
                    log.info("User excluded - CV import running ({}): {}", userProfile.userFileImportState(), email);
                    return false;
                }

                return true;
            });
        } catch (RuntimeException e) {
            log.warn("Error during exclusion check for {}: {}", email, e.getMessage());
            return true;
        }
    }

    private int getUserExperienceCount(String email) {
        return transactionTemplate.execute((_status) -> {
                    try {
                        var kcUser = keycloakService.getFOUserRepresentationByEmail(email);
                        if (kcUser != null) {
                            var userProfile = userProfileProvider.getUserProfileOrCreateForHandicap(kcUser.getId());
                            return userProfile.experiences() != null ? userProfile.experiences().size() : 0;
                        }
                    } catch (RuntimeException e) {
                        log.debug("Unable to retrieve experience count for {}: {}", email, e.getMessage());
                    }
                    return 0;
                }
        );
    }


}
