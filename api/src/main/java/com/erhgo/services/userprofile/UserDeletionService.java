package com.erhgo.services.userprofile;

import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.NotificationRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.AuthorizeExpression;
import com.erhgo.services.SecurityService;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailingListService;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.notifier.messages.FrontofficeAccountDeletionNotifierMessageDTO;
import com.erhgo.services.search.UserIndexer;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Predicate;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserDeletionService {
    private final NotificationRepository notificationRepository;

    private final EntityManager entityManager;

    private final AbstractCandidatureRepository candidatureRepository;
    private final UserProfileRepository userProfileRepository;
    private final KeycloakService keycloakService;
    private final MailingListService mailingListService;

    private final UserIndexer userIndexer;
    private final Notifier notifier;

    @Autowired
    private SecurityService securityService;

    @Value("${sendinblue.templates.delete-account}")
    private Long confirmDeleteAccountTemplate;


    @Transactional
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_WRITE)
    public void deleteUser(String userId) {
        var userProfileOptional = userProfileRepository.findByUserId(userId);
        String msg;
        userProfileOptional.ifPresentOrElse(this::deleteUserInDb, () -> log.warn("No user with userId {} found in db'", userId));
        var keycloakUser = keycloakService.getFrontOfficeUserProfile(userId);
        if (Objects.equals(securityService.getAuthenticatedUserId(), userId)) {
            msg = "Un utilisateur FO a supprimé son compte";
            sendEmailForDeletionUser(userId);
        } else {
            var author = keycloakService.getBackOfficeUserProfile(securityService.getAuthenticatedUserId())
                    .map(UserRepresentation::getEmail)
                    .orElse("inconnu");
            msg = "Suppression d'un utilisateur FO depuis le BO par %s".formatted(author);
        }
        var optionalEmail = keycloakUser.map(UserRepresentation::getEmail);
        optionalEmail.ifPresentOrElse(email -> {
            if (!keycloakService.deleteFrontOfficeUser(userId)) {
                log.warn("User {} was already deleted in keycloak", userId);
            }
        }, () -> log.warn("No user with userId {} found in keycloak'", userId));

        var email = optionalEmail.orElse("<Email inconnu>");
        var fullname = keycloakUser.map(UserRepresentation::getFullname)
                .map(String::trim)
                .filter(Predicate.not(String::isEmpty))
                .orElse("<Nom inconnu>");
        var message = FrontofficeAccountDeletionNotifierMessageDTO
                .builder()
                .text("%s%n*%s* (%s) / id : %s".formatted(msg, email, fullname, userId))
                .build();
        notifier.sendMessage(message);
        userIndexer.delete(userId);
    }

    public void sendEmailForDeletionUser(String userId) {
        if (confirmDeleteAccountTemplate == null) {
            log.error("Ignoring confirmation deletion send, no mail template id provided for deletion {}", userId);
            return;
        }
        var email = keycloakService.getFrontOfficeUserProfile(userId)
                .map(UserRepresentation::getEmail)
                .orElseThrow(() -> new EntityNotFoundException(userId, UserRepresentation.class));
        var parameters = Map.of("EMAIL", email);
        mailingListService.sendMailsForTemplate(Set.of(email), confirmDeleteAccountTemplate, parameters, null);
    }

    private void deleteUserInDb(UserProfile userProfile) {
        mailingListService.blacklistFromAllSenders(userProfile);
        deleteCandidature(userProfile);
        deleteNotifications(userProfile);
        deleteUserProfile(userProfile);
        deleteUserMobileToken(userProfile);
        ensureUserWasCorrectlyDeleted(userProfile.userId());
    }

    private void deleteUserMobileToken(UserProfile userProfile) {
        entityManager.createQuery("DELETE UserMobileToken WHERE userProfile = :userProfile")
                .setParameter("userProfile", userProfile)
                .executeUpdate();
    }


    private void ensureUserWasCorrectlyDeleted(String userId) {
        entityManager.flush();
        if (userProfileRepository.findByUserId(userId).isPresent()) {
            throw new GenericTechnicalException("Unable to delete UserProfile " + userId);
        }
    }

    private void deleteUserProfile(UserProfile userProfile) {
        entityManager.remove(userProfile);
    }

    private void deleteCandidature(UserProfile userProfile) {
        candidatureRepository.findByUserProfileUserId(userProfile.userId()).forEach(entityManager::remove);
    }

    private void deleteNotifications(UserProfile userProfile) {
        notificationRepository.findByUserProfileUuid(userProfile.uuid()).forEach(entityManager::remove);
    }
}
