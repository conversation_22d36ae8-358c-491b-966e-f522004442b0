package com.erhgo.services.userprofile;

import com.erhgo.services.notifications.NotificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserMobileScheduler {
    private final NotificationService notificationService;
    private final UserMobileService userMobileService;

    @Scheduled(cron = "${application.mobile.tokensRefreshCron}", zone = "Europe/Paris")
    @SchedulerLock(name = "launchMobileTokensRefresh")
    public void scheduleCleanUsersMobileTokens() {
        log.info("Starting to clean user mobile tokens...");
        userMobileService.cleanUsersMobileTokens();
    }

    @Scheduled(cron = "${application.mobile.notificationTestCron}", zone = "Europe/Paris")
    @SchedulerLock(name = "launchMobileNotificationsTest")
    public void scheduleSendTestNotifications() {
        log.info("Starting to send test mobile notifications...");
        notificationService.sendTestNotifications();
    }
}
