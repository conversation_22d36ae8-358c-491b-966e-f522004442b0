package com.erhgo.services.recruitmentimporter.converters;

import com.erhgo.utils.StringUtils;
import com.opencsv.bean.AbstractBeanField;
import com.opencsv.exceptions.CsvConstraintViolationException;
import com.opencsv.exceptions.CsvDataTypeMismatchException;
import lombok.NoArgsConstructor;

@NoArgsConstructor
public class CustomBooleanConverter extends AbstractBeanField<Boolean, String> {
    @Override
    protected Boolean convert(String value) throws CsvDataTypeMismatchException, CsvConstraintViolationException {
        return StringUtils.trimQuote(value).map("Oui"::equalsIgnoreCase).orElse(null);
    }
}
