package com.erhgo.services.recruitmentimporter.converters;

import com.erhgo.utils.StringUtils;
import com.opencsv.bean.AbstractBeanField;
import com.opencsv.exceptions.CsvConstraintViolationException;
import com.opencsv.exceptions.CsvDataTypeMismatchException;

public class StringQuoteTrimConverter extends AbstractBeanField<String, String> {

    @Override
    protected String convert(String value) throws CsvDataTypeMismatchException, CsvConstraintViolationException {
        if (value == null) {
            return null;
        }
        return StringUtils.trimQuote(value).orElse(null);
    }
}
