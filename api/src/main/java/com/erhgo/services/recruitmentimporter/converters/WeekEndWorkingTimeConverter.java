package com.erhgo.services.recruitmentimporter.converters;

import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.utils.StringUtils;
import com.opencsv.bean.AbstractBeanField;
import com.opencsv.exceptions.CsvConstraintViolationException;
import com.opencsv.exceptions.CsvDataTypeMismatchException;
import lombok.NoArgsConstructor;

@NoArgsConstructor
public class WeekEndWorkingTimeConverter extends AbstractBeanField<String, String> {
    @Override
    protected String convert(String rawValue) throws CsvDataTypeMismatchException, CsvConstraintViolationException {
        var value = StringUtils.trimQuote(rawValue).orElse(null);
        if ("oui".equalsIgnoreCase(value)) {
            return CriteriaValue.WEEKEND_WORK_CRITERIA_VALUE_CODE;
        }
        return null;
    }
}
