package com.erhgo.services.recruitmentimporter.converters;

import com.erhgo.openapi.dto.WorkingTimeDTO;
import com.erhgo.utils.StringUtils;
import com.opencsv.bean.AbstractBeanField;
import com.opencsv.exceptions.CsvConstraintViolationException;
import com.opencsv.exceptions.CsvDataTypeMismatchException;
import lombok.NoArgsConstructor;

@NoArgsConstructor
public class WorkingTimeConverter extends AbstractBeanField<WorkingTimeDTO, String> {
    @Override
    protected WorkingTimeDTO convert(String rawValue) throws CsvDataTypeMismatchException, CsvConstraintViolationException {
        var value = StringUtils.trimQuote(rawValue).orElse(null);
        if ("non".equalsIgnoreCase(value)) {
            return WorkingTimeDTO.PART_TIME;
        }
        return WorkingTimeDTO.FULL_TIME;
    }
}
