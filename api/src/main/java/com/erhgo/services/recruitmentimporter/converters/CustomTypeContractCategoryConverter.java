package com.erhgo.services.recruitmentimporter.converters;

import com.erhgo.openapi.dto.TypeContractCategoryDTO;
import com.erhgo.utils.StringUtils;
import com.opencsv.bean.AbstractBeanField;
import com.opencsv.exceptions.CsvConstraintViolationException;
import com.opencsv.exceptions.CsvDataTypeMismatchException;
import lombok.NoArgsConstructor;

@NoArgsConstructor
public class CustomTypeContractCategoryConverter extends AbstractBeanField<String, TypeContractCategoryDTO> {
    @Override
    protected TypeContractCategoryDTO convert(String rawValue) throws CsvDataTypeMismatchException, CsvConstraintViolationException {
        var value = StringUtils.trimQuote(rawValue).orElse(null);
        if ("CDD".equalsIgnoreCase(value)) {
            return TypeContractCategoryDTO.TEMPORARY;
        } else if ("Alternance".equalsIgnoreCase(value)) {
            return TypeContractCategoryDTO.PRO;
        }

        return TypeContractCategoryDTO.PERMANENT;
    }
}
