package com.erhgo.services.recruitmentimporter;

import com.erhgo.domain.exceptions.AbstractFunctionalException;
import com.opencsv.CSVReaderBuilder;
import com.opencsv.RFC4180ParserBuilder;
import com.opencsv.bean.CsvToBeanBuilder;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@NoArgsConstructor
public class OfferCsvParserService {
    public <A> List<A> parseOffersFromCsv(InputStream inputStream, Class<A> clazz) {
        log.info("Parsing new recruitment offers from csv...");
        try (InputStreamReader reader = new InputStreamReader(inputStream)) {
            var rfc4180Parser = new RFC4180ParserBuilder()
                    .build();

            var csvReader = new CSVReaderBuilder(reader)
                    .withCSVParser(rfc4180Parser)
                    .withSkipLines(1)
                    .build();

            var csvToBean = new CsvToBeanBuilder<A>(csvReader)
                    .withType(clazz)
                    .withThrowExceptions(false)
                    .withIgnoreLeadingWhiteSpace(true)
                    .withIgnoreEmptyLine(true)
                    .build();

            var result = new ArrayList<>(csvToBean.parse());

            if (!csvToBean.getCapturedExceptions().isEmpty()) {
                var firstException = csvToBean.getCapturedExceptions().get(0);
                log.error("Unable to parse offers CSV data {}", csvToBean.getCapturedExceptions().stream().map(Exception::getMessage).collect(Collectors.joining(",")), firstException);
                throw new AbstractFunctionalException("Unable to parse offers CSV data", firstException) { };
            }

            log.info("Parsed {} offers from csv", result.size());

            return result;
        } catch (IllegalStateException | IOException e) {
            log.error("Unable to parse offers CSV data", e);
            throw new AbstractFunctionalException("Unable to parse offers CSV data", e) { };
        }
    }
}
