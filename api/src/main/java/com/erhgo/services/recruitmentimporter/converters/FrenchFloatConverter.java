package com.erhgo.services.recruitmentimporter.converters;

import com.erhgo.utils.StringUtils;
import com.opencsv.bean.AbstractBeanField;
import com.opencsv.exceptions.CsvConstraintViolationException;
import com.opencsv.exceptions.CsvDataTypeMismatchException;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@NoArgsConstructor
public class FrenchFloatConverter extends AbstractBeanField<Float, String> {
    @Override
    protected Float convert(String rawValue) throws CsvDataTypeMismatchException, CsvConstraintViolationException {
        var value = com.erhgo.utils.StringUtils.trimQuote(rawValue);
        return value.map(StringUtils::parseFloat).orElse(null);
    }
}
