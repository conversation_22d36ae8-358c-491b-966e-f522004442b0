package com.erhgo.services.eventlistener;

import com.erhgo.domain.job.Job;
import com.erhgo.domain.job.Mission;
import com.erhgo.domain.referential.CategoryLevel;
import com.erhgo.domain.referential.Context;
import jakarta.annotation.PostConstruct;
import jakarta.persistence.EntityManagerFactory;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.event.spi.EventType;
import org.hibernate.event.spi.PostUpdateEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import static com.erhgo.repositories.JobMissionRepository.QUERY_MISSION_WITH_CONTEXT;

@Service
@Slf4j
public class ContextEventListener extends ErhgoEventListenerAdapter<Context> {

    private final transient EntityManagerFactory entityManagerFactory;

    private static final String CATEGORY_LEVEL_PROPERTY_NAME = "categoryLevel";

    @PostConstruct
    private void init() {
        getEventListenerRegistry(entityManagerFactory).getEventListenerGroup(EventType.POST_UPDATE).appendListener(this);
    }

    public ContextEventListener(EntityManagerFactory entityManagerFactory, ApplicationContext applicationContext) {
        super(Context.class, applicationContext);
        this.entityManagerFactory = entityManagerFactory;
    }

    @Override
    protected void handlePostUpdate(Context entity, PostUpdateEvent event) {
        CategoryLevel oldCategoryLevel = getOldValue(event, CATEGORY_LEVEL_PROPERTY_NAME);
        // oldCategoryLevel == null means categoryLevel was not modified
        if (oldCategoryLevel != null) {
            this.handleCategoryUpdateInJobs(entity, oldCategoryLevel);
        }
    }

    private void handleCategoryUpdateInJobs(Context context, CategoryLevel oldCategoryLevel) {
        var oldCategoryId = oldCategoryLevel.getCategory().getId();
        var oldScore = oldCategoryLevel.getScore();
        var newCategoryId = context.getCategoryLevel().getCategory().getId();
        var newScore = context.getCategoryLevel().getScore();
        var categoryChanged = !oldCategoryId.equals(newCategoryId);
        var scoreChanged = oldScore != newScore;

        if (categoryChanged || scoreChanged) {
            var impactedMissions = createQuery(Mission.class, QUERY_MISSION_WITH_CONTEXT)
                    .setParameter("id", context.getId())
                    .getResultList();

            if (categoryChanged) {
                log.info("Updating category from {} to {} for context {}", oldCategoryId, newCategoryId, context.getId());
                impactedMissions.forEach(m -> m.replaceContextCategory(context));
            } else {
                log.info("Updating score from {} to {} for context {}", oldScore, newScore, context.getId());
                impactedMissions.stream().map(Mission::getJob).distinct().forEach(Job::refreshLevel);
            }
        }
    }

}
