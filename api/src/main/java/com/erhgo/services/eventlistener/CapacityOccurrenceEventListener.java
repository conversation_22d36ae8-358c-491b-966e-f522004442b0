package com.erhgo.services.eventlistener;

import com.erhgo.domain.userprofile.CapacityOccurrence;
import jakarta.annotation.PostConstruct;
import jakarta.persistence.EntityManagerFactory;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.event.spi.EventType;
import org.hibernate.event.spi.PreInsertEvent;
import org.hibernate.event.spi.PreUpdateEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CapacityOccurrenceEventListener extends ErhgoEventListenerAdapter<CapacityOccurrence> {

    private final transient EntityManagerFactory entityManagerFactory;

    @PostConstruct
    private void init() {
        getEventListenerRegistry(entityManagerFactory).getEventListenerGroup(EventType.PRE_UPDATE).appendListener(this);
        getEventListenerRegistry(entityManagerFactory).getEventListenerGroup(EventType.PRE_INSERT).appendListener(this);
    }

    public CapacityOccurrenceEventListener(EntityManagerFactory entityManagerFactory, ApplicationContext applicationContext) {
        super(CapacityOccurrence.class, applicationContext);
        this.entityManagerFactory = entityManagerFactory;
    }

    @Override
    boolean handlePreUpdate(CapacityOccurrence entity, PreUpdateEvent event) {
        return true;
    }

    @Override
    boolean handlePreInsert(CapacityOccurrence entity, PreInsertEvent event) {
        return true;
    }

}
