package com.erhgo.services.eventlistener;

import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.event.*;
import com.erhgo.services.mailing.MailingListService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;

@Service
@Slf4j
public class SendInBlueAttributesUpdater {

    MailingListService mailingListService;
    Map<Class<? extends UserRelatedEvent>, Consumer<UserProfile>> actionForEvent;

    @Autowired
    public SendInBlueAttributesUpdater(MailingListService mailingListService) {
        this.mailingListService = mailingListService;
        actionForEvent = new HashMap<>();
        actionForEvent.put(UserContactInfoUpdatedEvent.class, mailingListService::updateContactInfo);
        actionForEvent.put(UserExperienceUpdatedEvent.class, mailingListService::updateNumberOfExperiences);
        actionForEvent.put(UserNewCandidaturePublishedEvent.class, mailingListService::updateLastCandidatureDate);
        actionForEvent.put(UserSalaryUpdatedEvent.class, mailingListService::updateSalary);
        actionForEvent.put(UserLastConnectionEvent.class, mailingListService::updateLastConnectionDate);
        actionForEvent.put(UserLocationUpdatedEvent.class, mailingListService::updateLocation);
        actionForEvent.put(UserPhoneNumberUpdatedEvent.class, mailingListService::updatePhoneNumber);
        actionForEvent.put(UserNameUpdatedEvent.class, mailingListService::updateName);
        actionForEvent.put(UserJobOffersOptInUpdatedEvent.class, mailingListService::updateJobOffersOptIn);
        actionForEvent.put(UserSituationUpdatedEvent.class, mailingListService::updateSituation);
        actionForEvent.put(UserMobileTokenUpdatedEvent.class, mailingListService::updateUserMobileUsage);

    }

    @EventListener(UserRelatedEvent.class)
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void onEventTriggered(UserRelatedEvent event) {
        var userId = Optional.ofNullable(event.getUserProfile()).map(UserProfile::userId).orElse("Unknown");

        Consumer<UserProfile> logConsumer = up -> log.error("Event of type {} for user {} ignored by SendInBlue handler",
                event.getClass().getName(),
                userId
        );
        actionForEvent.getOrDefault(
                event.getClass(),
                logConsumer
        ).accept(event.getUserProfile());
        log.debug("Event of type {} for user {} processed by SendInBlue Handler", event.getClass().getName(), userId);
    }
}
