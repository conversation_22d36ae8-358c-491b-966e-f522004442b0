package com.erhgo.services.eventlistener;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import org.hibernate.HibernateException;
import org.hibernate.event.service.spi.EventListenerRegistry;
import org.hibernate.event.spi.*;
import org.hibernate.internal.SessionFactoryImpl;
import org.hibernate.persister.entity.EntityPersister;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import java.util.stream.IntStream;

@AllArgsConstructor
public class ErhgoEventListenerAdapter<E> implements
        PostUpdateEventListener,
        PreDeleteEventListener,
        PreUpdateEventListener,
        PreInsertEventListener,
        FlushEntityEventListener {

    private Class<E> listenedClass;

    @Autowired
    private final transient ApplicationContext applicationContext;

    EventListenerRegistry getEventListenerRegistry(EntityManagerFactory entityManagerFactory) {
        SessionFactoryImpl sessionFactory = entityManagerFactory.unwrap(SessionFactoryImpl.class);
        return sessionFactory.getServiceRegistry().getService(EventListenerRegistry.class);
    }

    protected <A> TypedQuery<A> createQuery(Class<A> clazz, String query) {
        return applicationContext.getBean(EntityManager.class)
                .createQuery(query, clazz)
                // Flush mode set to commit to ensure no flush is done during field update - provocating stackoverflow
                // if updated entity is referenced during service processing
                .setFlushMode(FlushModeType.COMMIT);
    }

    Query createNativeQuery(String sqlQuery) {
        return getEntityManager()
                .createNativeQuery(sqlQuery)
                // Flush mode set to commit to ensure no flush is done during field update - provocating stackoverflow
                // if updated entity is referenced during service processing
                .setFlushMode(FlushModeType.COMMIT);
    }

    EntityManager getEntityManager() {
        return applicationContext.getBean(EntityManager.class);
    }

    @Override
    public final void onPostUpdate(PostUpdateEvent event) {
        if (isAssignableFrom(event.getEntity())) {
            this.handlePostUpdate((E) event.getEntity(), event);
        }
    }

    protected void handlePostUpdate(E entity, PostUpdateEvent event) {
        // no-op default implementation
    }

    @Override
    public final boolean onPreDelete(PreDeleteEvent event) {
        if (isAssignableFrom(event.getEntity())) {
            this.handlePreDelete((E) event.getEntity(), event);
        }
        return false;
    }

    private boolean isAssignableFrom(Object entity) {
        return entity.getClass().isAssignableFrom(listenedClass);
    }

    protected void handlePreDelete(E entity, PreDeleteEvent event) {
        // no-op default implementation
    }

    @Override
    public final boolean onPreInsert(PreInsertEvent event) {
        if (isAssignableFrom(event.getEntity())) {
            return this.handlePreInsert((E) event.getEntity(), event);
        }
        return false;
    }

    boolean handlePreInsert(E entity, PreInsertEvent event) {
        return false;
    }

    @Override
    public final boolean onPreUpdate(PreUpdateEvent event) {
        if (isAssignableFrom(event.getEntity())) {
            return this.handlePreUpdate((E) event.getEntity(), event);
        }
        return false;
    }

    @Override
    public void onFlushEntity(FlushEntityEvent event) throws HibernateException {
        if (isAssignableFrom(event.getEntity())) {
            this.handleFlushEntity((E) event.getEntity(), event);
        }
    }

    public void handleFlushEntity(E entity, FlushEntityEvent event) {
        // no-op default implementation
    }

    boolean handlePreUpdate(E entity, PreUpdateEvent event) {
        return false;
    }

    <V> V getOldValue(PostUpdateEvent event, String propertyName) {
        var propertyNames = event.getPersister().getPropertyNames();
        var propertyCategoryLevelIndex = getPropertyValue(propertyNames, propertyName);
        return (V) event.getOldState()[propertyCategoryLevelIndex];
    }

    <V> V getOldValue(PreUpdateEvent event, String propertyName) {
        var propertyNames = event.getPersister().getPropertyNames();
        var propertyCategoryLevelIndex = getPropertyValue(propertyNames, propertyName);
        return (V) event.getOldState()[propertyCategoryLevelIndex];
    }

    private int getPropertyValue(String[] propertyNames, String property) {
        return IntStream.range(0, propertyNames.length)
                .filter(i -> property.equals(propertyNames[i]))
                .findFirst()
                .orElseThrow();
    }

    @Override
    public boolean requiresPostCommitHandling(EntityPersister entityPersister) {
        return false;
    }
}
