package com.erhgo.services.eventlistener;

import com.erhgo.domain.dto.event.RemoveCapacityOccurrencesEvent;
import com.erhgo.domain.dto.event.UpdateCapacityOccurrencesEvent;
import com.erhgo.domain.userprofile.CapacityOccurrence;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
@Slf4j
@RequiredArgsConstructor
public class CapacityOccurrenceUpdater {

    public static final int AFTER_DELETION_PRECEDENCE = Ordered.LOWEST_PRECEDENCE;
    public static final int DELETION_PRECEDENCE = Ordered.LOWEST_PRECEDENCE - 1;

    private final EntityManager entityManager;

    private void updateCapacityOccurrences(List<CapacityOccurrence> entities) {
        var query = "INSERT INTO CapacityOccurrence(" +
                "   occurrence, " +
                "   recursiveOccurrence, " +
                "   capacity_id, " +
                "   userProfile_uuid) " +
                "VALUES " +
                entities.stream().map(a -> "(?,?,?,?)").collect(Collectors.joining(",")) +
                "ON DUPLICATE KEY UPDATE occurrence = occurrence + VALUES(occurrence), recursiveOccurrence = recursiveOccurrence + VALUES(recursiveOccurrence)";

        var result = createNativeQuery(query);
        IntStream.range(0, entities.size()).forEach(i -> result.setParameter(4 * i + 1, entities.get(i).getOccurrence())
                .setParameter(4 * i + 2, entities.get(i).getRecursiveOccurrence())
                .setParameter(4 * i + 3, entities.get(i).getCapacity().getId())
                .setParameter(4 * i + 4, entities.get(i).getUserProfile().uuid()));

        var update = result.executeUpdate();
        log.debug("Updated {} CapacityOccurrences", update);
    }


    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = UpdateCapacityOccurrencesEvent.class)
    @Order(AFTER_DELETION_PRECEDENCE)
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Modifying
    public void updateCapacityOccurrences(UpdateCapacityOccurrencesEvent updateCapacityOccurrencesEvent) {
        if (TransactionSynchronizationManager.isActualTransactionActive() && !updateCapacityOccurrencesEvent.getCapacityOccurrences().isEmpty()) {
            try {
                var entities = updateCapacityOccurrencesEvent.getCapacityOccurrences();
                this.updateCapacityOccurrences(entities);
                createNativeQuery("DELETE FROM CapacityOccurrence WHERE occurrence = 0 AND recursiveOccurrence = 0").executeUpdate();
            } catch (RuntimeException e) {
                log.error("Error on capacityOccurrences update", e);
                throw e;
            }
        } else {
            log.error("Capacity occurrence update ignored - no transaction");
        }
    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = RemoveCapacityOccurrencesEvent.class)
    @Order(DELETION_PRECEDENCE)
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Modifying
    public void deleteCapacityOccurrences(RemoveCapacityOccurrencesEvent removeCapacityOccurrencesEvent) {
        if (TransactionSynchronizationManager.isActualTransactionActive() && !removeCapacityOccurrencesEvent.getCapacityOccurrences().isEmpty()) {
            try {
                var entities = removeCapacityOccurrencesEvent.getCapacityOccurrences();
                var query =
                        "DELETE FROM CapacityOccurrence WHERE (userProfile_uuid, capacity_id) in (" +
                                entities.stream().map(a -> "(?,?)").collect(Collectors.joining(",")) + ")";
                var result = createNativeQuery(query);
                IntStream.range(0, entities.size()).forEach(i ->
                        result.setParameter(2 * i + 1, entities.get(i).getUserProfile().uuid())
                                .setParameter(2 * i + 2, entities.get(i).getCapacity().getId()));
                var update = result.executeUpdate();
                log.trace("Removed {} CapacityOccurrences", update);

            } catch (RuntimeException e) {
                log.error("Error on capacityOccurrences removal", e);
                throw e;
            }
        } else if (!removeCapacityOccurrencesEvent.getCapacityOccurrences().isEmpty()) {
            log.warn("Capacity occurrence removal ignored - no transaction");
        }
    }

    private Query createNativeQuery(String query) {
        return entityManager.createNativeQuery(query);
    }

}
