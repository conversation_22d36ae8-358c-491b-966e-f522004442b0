package com.erhgo.services.eventlistener;

import com.erhgo.domain.referential.CapacityRelatedQuestionResponse;
import com.erhgo.repositories.AnswerForCapacityRelatedQuestionRepository;
import jakarta.annotation.PostConstruct;
import jakarta.persistence.EntityManagerFactory;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.event.service.spi.EventListenerRegistry;
import org.hibernate.event.spi.EventType;
import org.hibernate.event.spi.PreDeleteEvent;
import org.hibernate.event.spi.PreDeleteEventListener;
import org.hibernate.internal.SessionFactoryImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CapacityRelatedQuestionResponseEventListener extends ErhgoEventListenerAdapter<CapacityRelatedQuestionResponse> {

    private final transient EntityManagerFactory entityManagerFactory;

    @Autowired
    private transient AnswerForCapacityRelatedQuestionRepository repository;

    @PostConstruct
    private void init() {
        var eventType = EventType.PRE_DELETE;
        registerForEventType(eventType);
    }

    void registerForEventType(EventType<PreDeleteEventListener> eventType) {
        SessionFactoryImpl sessionFactory = entityManagerFactory.unwrap(SessionFactoryImpl.class);
        EventListenerRegistry registry = sessionFactory.getServiceRegistry().getService(EventListenerRegistry.class);
        registry.getEventListenerGroup(eventType).appendListener(this);
    }

    public CapacityRelatedQuestionResponseEventListener(EntityManagerFactory entityManagerFactory, ApplicationContext applicationContext) {
        super(CapacityRelatedQuestionResponse.class, applicationContext);
        this.entityManagerFactory = entityManagerFactory;
    }

    @Override
    protected void handlePreDelete(CapacityRelatedQuestionResponse entity, PreDeleteEvent event) {
        repository.deleteAllByResponse(entity);
    }
}
