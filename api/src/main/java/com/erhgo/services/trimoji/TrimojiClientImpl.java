package com.erhgo.services.trimoji;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.services.SecurityService;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.annotations.VisibleForTesting;
import com.jayway.jsonpath.JsonPath;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

@Service
@ConditionalOnExpression("!'${trimoji.apiKey}'.isEmpty()")
@RequiredArgsConstructor
@Slf4j
public class TrimojiClientImpl implements TrimojiClient {

    private final KeycloakService keycloakService;
    private final SecurityService securityService;
    private final RestTemplateBuilder restTemplateBuilder;
    private final ObjectMapper objectMapper;

    private RestTemplate restTemplate;

    @Value("${trimoji.requestJsonTemplate}")
    private String trimojiRequestTemplateJson;
    @Value("${trimoji.apiKey}")
    private String trimojiApiKey;
    @Value("${trimoji.urlJsonPath}")
    private String trimojiUrlJsonPath;
    @Value("${trimoji.url}")
    private String trimojiUrl;
    @Value("${trimoji.callbackUrl}")
    private String callbackUrl;
    private Map<String, Function<UserRepresentation, String>> jsonFormatter;

    @PostConstruct
    void initializeTemplate() {
        log.info("Initializing Trimoji service");
        this.restTemplate = restTemplateBuilder
                .defaultHeader(HttpHeaders.AUTHORIZATION, trimojiApiKey)
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .setReadTimeout(Duration.ofSeconds(30))
                .setConnectTimeout(Duration.ofSeconds(30))
                .build();
        initializeJsonFormatter();
        try {
            objectMapper.readValue(trimojiRequestTemplateJson, Map.class);
            log.debug("Trimoji conf is valid");
        } catch (JsonProcessingException e) {
            throw new GenericTechnicalException("JSON used for Trimoji conf is not valid", e);
        }
    }

    @VisibleForTesting
    public void initializeJsonFormatter() {
        jsonFormatter = Map.of(
                "%USER_EMAIL%", up -> "%EMAIL%",
                "%USER_FIRSTNAME%", up -> "%FN%",
                "%USER_LASTNAME%", up -> "%LN%",
                "%USER_ID%", UserRepresentation::getId,
                "%CALLBACK_URL%", up -> this.callbackUrl
        );
    }

    @Override
    public String getNewUrl() {
        var command = keycloakService
                .getFrontOfficeUserProfile(securityService.getAuthenticatedUserId())
                .map(this::formatJson)
                .orElseThrow();
        try {
            var response = restTemplate.postForObject(trimojiUrl, command, String.class);
            return JsonPath.read(response, trimojiUrlJsonPath);
        } catch (RestClientException e) {
            log.error("Unable to build Trimoji request", e);
            throw new GenericTechnicalException("Unable to build Trimoji request", e);
        }
    }

    // Trimoji result is not stable - here we can give json as varenv, allowing changes without release
    private String formatJson(UserRepresentation userRepresentation) {
        var json = jsonFormatter
                .entrySet()
                .stream()
                .reduce(

                        trimojiRequestTemplateJson,
                        (curVal, curEntry) -> curVal.replaceAll(curEntry.getKey(), Optional.ofNullable(curEntry.getValue().apply(userRepresentation)).orElse("")),
                        // No parallelism
                        (a, b) -> {
                            throw new UnsupportedOperationException();
                        }
                );
        log.debug("Using {} as command for Trimoji", json);
        return json;
    }

}
