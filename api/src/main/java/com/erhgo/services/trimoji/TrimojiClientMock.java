package com.erhgo.services.trimoji;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Service;

@Service
@ConditionalOnExpression("'${trimoji.apiKey}'.isEmpty()")
@Slf4j
public class TrimojiClientMock implements TrimojiClient {
    @Override
    public String getNewUrl() {
        log.error("Trimoji mocked");
        return "https://assess.trimoji.fr/test/integration/55499ff4-d3f7-4dd8-af99-236e65b10799/noresult/short";
    }

}
