package com.erhgo.services.trimoji;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.itextpdf.io.font.constants.StandardFonts;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.stream.IntStream;

import static com.erhgo.utils.StringUtils.abbreviateFirstAndConcatenate;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TrimojiPDFAdapter {
    public static final long FONT_SIZE_BIG = 14L;
    public static final long FONT_SIZE_NORMAL = 12L;
    public static final String ELLIPSIS = "...";
    private static final int PERSONALITY_PAGE_INDEX = 2;
    public static final int FONT_SIZE_HUGE = 20;

    public static ByteArrayOutputStream processTrimojiPDF(InputStream inputStream, String firstname, String lastnameOrAnonymousCode) {
        try (
                var outputStream = new ByteArrayOutputStream();
                var pdfDoc = new PdfDocument(new PdfReader(inputStream), new PdfWriter(outputStream))
        ) {
            replaceNameOnLeftColumn(pdfDoc, firstname, lastnameOrAnonymousCode);
            hideHeader(pdfDoc);
            handlePersonalityPage(pdfDoc, abbreviateFirstAndConcatenate(firstname, lastnameOrAnonymousCode));
            return outputStream;
        } catch (IOException e) {
            log.error("Unable to handle PDF", e);
            throw new GenericTechnicalException("Unable to handle PDF", e);
        }
    }

    private static void replaceNameOnLeftColumn(PdfDocument pdfDoc, String firstname, String lastname) throws IOException {
        var canvas = new PdfCanvas(pdfDoc.getFirstPage());
        var fontBold = PdfFontFactory.createFont(StandardFonts.HELVETICA_BOLD);

        var whiteRectWidth = 200f;
        var whiteRectHeight = -300f;

        var x = 50.234f;
        var yFirstname = 530;
        var offset = 5;

        var yLastname = yFirstname - FONT_SIZE_NORMAL - offset;
        canvas.saveState()
                .setFillColor(ColorConstants.WHITE)
                .rectangle(x, yFirstname + 4d * offset, whiteRectWidth, whiteRectHeight)
                .fill()
                .restoreState();

        canvas.beginText()
                .setFontAndSize(fontBold, FONT_SIZE_BIG)
                .setColor(ColorConstants.BLACK, true)
                .moveText(x, yFirstname)
                .showText(truncateText(fontBold, FONT_SIZE_BIG, StringUtils.capitalize(StringUtils.defaultIfBlank(firstname, lastname)), 200))
                .endText();

        if (!StringUtils.isBlank(firstname)) {
            canvas.beginText()
                    .setFontAndSize(fontBold, FONT_SIZE_BIG)
                    .setColor(ColorConstants.BLACK, true)
                    .moveText(x, yLastname)
                    .showText(truncateText(fontBold, FONT_SIZE_BIG, StringUtils.toRootUpperCase(lastname), 200))
                    .endText();
        }
    }

    private static void hideHeader(PdfDocument pdfDoc) {
        var width = 200f;
        var height = 40f;
        var x = (595f - width) / 2;
        var y = 842f - height;
        var nbPages = pdfDoc.getNumberOfPages();
        IntStream.rangeClosed(2, nbPages).mapToObj(pdfDoc::getPage).map(PdfCanvas::new)
                .forEach(canvas -> canvas.saveState()
                        .setFillColor(ColorConstants.WHITE)
                        .rectangle(x, y, width, height)
                        .fill()
                        .restoreState());
    }

    private static String truncateText(PdfFont font, float fontSize, String text, float maxWidth) {
        if (text == null) {
            return "";
        }
        float textWidth = font.getWidth(text, fontSize);
        if (textWidth <= maxWidth) {
            return text;
        }
        var ellipsisWidth = font.getWidth(ELLIPSIS, fontSize);
        var truncatedText = new StringBuilder();
        var width = 0f;
        for (char c : text.toCharArray()) {
            var charWidth = font.getWidth(String.valueOf(c), fontSize);
            if (width + charWidth + ellipsisWidth > maxWidth) {
                break;
            }
            truncatedText.append(c);
            width += charWidth;
        }
        truncatedText.append(ELLIPSIS);
        return truncatedText.toString();
    }

    private static void handlePersonalityPage(PdfDocument pdfDoc, String personalityName) throws IOException {
        var canvas = new PdfCanvas(pdfDoc.getPage(PERSONALITY_PAGE_INDEX));
        var fontBold = PdfFontFactory.createFont(StandardFonts.HELVETICA_BOLD);
        var personalityXPosition = 175f;
        var personalityYPosition = 750f;
        canvas.saveState()
                .setFillColor(ColorConstants.WHITE)
                .rectangle(personalityXPosition, personalityYPosition, 200, 40)
                .fill()
                .restoreState();

        canvas.beginText()
                .setFontAndSize(fontBold, FONT_SIZE_HUGE)
                .setColor(ColorConstants.BLACK, true)
                .moveText(personalityXPosition, personalityYPosition + 10)
                .showText(truncateText(fontBold, FONT_SIZE_HUGE, StringUtils.trimToEmpty(personalityName), 400))
                .endText();
    }
}
