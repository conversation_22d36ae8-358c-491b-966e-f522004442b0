package com.erhgo.services;

import com.erhgo.domain.referential.Behavior;
import com.erhgo.openapi.dto.BehaviorPageDTO;
import com.erhgo.openapi.dto.SortDirectionDTO;
import com.erhgo.repositories.BehaviorRepository;
import com.erhgo.security.Role;
import com.erhgo.services.dtobuilder.BehaviorDTOBuilder;
import com.erhgo.services.dtobuilder.PageDTOBuilder;
import com.google.common.base.Strings;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class BehaviorService {

    @Autowired
    private BehaviorRepository behaviorRepository;

    @Transactional(readOnly = true)
    @RolesAllowed({Role.ODAS_ADMIN, Role.CANDIDATE})
    public BehaviorPageDTO searchBehavior(int page, int size, String sortProperty, SortDirectionDTO sortDirection, String filter) {

        var safeBy = Strings.isNullOrEmpty(sortProperty) ? "title" : sortProperty;

        final Pageable pageable = PageRequest.of(page, size, Sort.Direction.valueOf(sortDirection.toString()), safeBy);
        Page<Behavior> result;
        if (filter != null && !filter.isEmpty()) {
            result = behaviorRepository.findBehavior(filter, pageable);
        } else {
            result = behaviorRepository.findAll(pageable);
        }
        return PageDTOBuilder
                .buildBehaviorPage(result.map(BehaviorDTOBuilder::buildDTO));
    }
}
