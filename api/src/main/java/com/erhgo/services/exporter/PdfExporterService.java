package com.erhgo.services.exporter;

import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import com.openhtmltopdf.slf4j.Slf4jLogger;
import com.openhtmltopdf.util.XRLog;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;
import org.thymeleaf.templatemode.TemplateMode;
import org.thymeleaf.templateresolver.ClassLoaderTemplateResolver;

import java.io.IOException;
import java.io.OutputStream;
import java.util.Locale;


/**
 * see https://sandbox.openhtmltopdf.com/ to validate & adjust HTML more easily
 */
@Service
@Slf4j
public class PdfExporterService {

    private final SpringTemplateEngine templateEngine;

    public PdfExporterService() {
        XRLog.setLoggerImpl(new Slf4jLogger());
        var templateResolver = new ClassLoaderTemplateResolver();
        templateResolver.setSuffix(".html");
        templateResolver.setTemplateMode(TemplateMode.HTML);
        templateEngine = new SpringTemplateEngine();
        templateEngine.setEnableSpringELCompiler(true);
        templateEngine.setTemplateResolver(templateResolver);
        var messageSource = new ReloadableResourceBundleMessageSource();
        messageSource.setBasename("classpath:/pdf-messages");
        messageSource.setDefaultEncoding("UTF-8");
        messageSource.setFallbackToSystemLocale(false);

        templateEngine.setMessageSource(messageSource);
    }

    public void generatePdf(String templateName, Object data, OutputStream outputStream) throws IOException {
        var context = new Context();
        context.setVariable("data", data);
        context.setLocale(Locale.FRANCE);
        generatePdfFromHtml(templateEngine.process("/templates/%s".formatted(templateName), context), outputStream);
    }

    private void generatePdfFromHtml(String html, OutputStream outputStream) throws IOException {
        var parsedHtmlDocument = Jsoup.parse(html);
        parsedHtmlDocument.outputSettings().syntax(Document.OutputSettings.Syntax.xml);
        var parsedHtml = parsedHtmlDocument.html();
        if (log.isDebugEnabled()) {
            log.debug("Generated HTML for PDF export: {}", parsedHtml);
        }
        var builder = new PdfRendererBuilder();
        builder.useFastMode()
                .usePdfUaAccessibility(true)
                .withHtmlContent(parsedHtml, "https://app.jenesuispasuncv.fr/")
                .toStream(outputStream)
                .run();
        log.trace("PDF generated");
    }

}
