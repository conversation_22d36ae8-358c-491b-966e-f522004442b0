package com.erhgo.services.exporter;

import com.erhgo.services.generation.dto.TitleAndDescription;
import lombok.Builder;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Builder
public record ProfileCompetencesViewObject(
        String workingDuration,
        boolean hasDrivingLicence,
        String contracts,
        String location,
        List<List<String>> hashtags,
        String attitude,
        List<SkillViewObject$> softSkills,
        List<SkillViewObject$> hardSkills,
        String firstName,
        String lastName,
        String email,
        String phone,
        String anonymousCode,
        AnonymousMode anonymousMode,
        boolean handicap
) {
    public boolean isFullfilled() {
        return hashtags != null && !hashtags.isEmpty() && !hashtags.getFirst().isEmpty()
                && StringUtils.isNotBlank(attitude)
                && !softSkills.isEmpty() && StringUtils.isNotBlank(softSkills.getFirst().description);
    }


    public record SkillViewObject$(String type, String description) {
        public SkillViewObject$(TitleAndDescription titleAndDescription) {
            this(titleAndDescription.title(), titleAndDescription.description());
        }
    }

    public ProfileCompetencesViewObjectBuilder clonedBuilder() {
        return new ProfileCompetencesViewObjectBuilder()
                .workingDuration(workingDuration)
                .hasDrivingLicence(hasDrivingLicence)
                .contracts(contracts)
                .location(location)
                .hashtags(new ArrayList<>(hashtags))
                .attitude(attitude)
                .softSkills(new ArrayList<>(softSkills))
                .hardSkills(new ArrayList<>(hardSkills))
                .firstName(firstName)
                .lastName(lastName)
                .email(email)
                .phone(phone)
                .anonymousCode(anonymousCode)
                .anonymousMode(anonymousMode)
                .handicap(handicap)
                ;
    }

    public enum AnonymousMode {
        NOMINATIVE, ANONYMOUS, BOTH
    }
}

