package com.erhgo.services;

import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.InvalidCommandException;
import com.erhgo.domain.exceptions.InvalidEntityException;
import com.erhgo.domain.exceptions.MissionNotModifiableException;
import com.erhgo.domain.job.ContextsForCategory;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.job.Mission;
import com.erhgo.domain.referential.Category;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.openapi.dto.ContextsForCategoryDTO;
import com.erhgo.openapi.dto.CreateMissionCommandDTO;
import com.erhgo.openapi.dto.MissionDTO;
import com.erhgo.openapi.dto.UpdateMissionCommandDTO;
import com.erhgo.repositories.*;
import com.erhgo.security.Role;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MissionService {
    private final JobActivityLabelRepository jobActivityLabelRepository;
    private final ContextRepository contextRepository;
    private final CategoryRepository categoryRepository;
    private final JobRepository jobRepository;
    private final ContextsForCategoryRepository contextsForCategoryRepository;
    private final JobMissionRepository repository;
    private final RecruitmentCandidatureRepository recruitmentCandidatureRepository;

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void delete(Long missionId) {
        var mission = repository.findById(missionId).orElseThrow(
                () -> new EntityNotFoundException(missionId, Mission.class));

        ensureJobIsModifiable(mission.getJob());

        repository.delete(mission);
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public MissionDTO create(CreateMissionCommandDTO command) {
        var optionalJob = jobRepository.findById(command.getJobId());
        var job = optionalJob.orElseThrow(() -> new EntityNotFoundException(command.getJobId(), Job.class));

        var activities = getActivities(command.getActivitiesIds());
        var contexts = getContextsForCategory(command.getContextsForCategory());


        ensureJobIsModifiable(job);
        var mission = new Mission(job, command.getTitle(), activities, contexts);
        return buildMissionDTO(repository.save(mission));
    }

    private Set<ContextsForCategory> getContextsForCategory(List<ContextsForCategoryDTO> contextsForCategory) {
        // TODO: bulk load contexts & categories instead of 1 request per "contextsForCategory" ?
        return contextsForCategory.stream().map(this::buildContextsForCategory).collect(Collectors.toSet());
    }

    private ContextsForCategory buildContextsForCategory(ContextsForCategoryDTO contextsForCategory) {
        var category = categoryRepository.findById(contextsForCategory.getCategoryId());
        Set<Context> contexts = Sets.newHashSet(contextRepository.findAllById(contextsForCategory.getContextsIds()));
        var invalidContexts = contexts.stream()
                .filter(c -> !c.getCategoryLevel()
                        .getCategory()
                        .getId().equals(contextsForCategory.getCategoryId()))
                .toList();
        if (!invalidContexts.isEmpty()) {
            throw new InvalidEntityException("Contexts " + invalidContexts.stream().map(Context::getCode).collect(Collectors.joining(", ")) + " are not associated to category " + contextsForCategory.getCategoryId());
        }

        return ContextsForCategory.builder()
                .category(category.orElseThrow(() -> new EntityNotFoundException(contextsForCategory.getCategoryId(), Category.class)))
                .contexts(contexts)
                .noContextForCategory(contextsForCategory.getNoContextForCategory())
                .build();
    }

    private Set<JobActivityLabel> getActivities(List<UUID> activitiesId) {
        var activities = Sets.newHashSet(jobActivityLabelRepository.findAllById(new HashSet<>(activitiesId)));
        var missingActivities = activitiesId.stream()
                .filter(a -> activities.stream().noneMatch(o -> o.getUuid().equals(a)))
                .toList();
        if (!missingActivities.isEmpty()) {
            throw new InvalidCommandException("UNKNOWN_ACTIVITIES", missingActivities.stream().map(String::valueOf).collect(Collectors.joining(",")), "mission create or update");
        }
        return activities;
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public MissionDTO update(Long missionId, UpdateMissionCommandDTO updateMissionCommand) {
        var optionalMission = repository.findById(missionId);
        var mission = optionalMission.orElseThrow(() -> new EntityNotFoundException(missionId, Mission.class));

        return buildMissionDTO(updateMissionWith(mission, updateMissionCommand));
    }

    private MissionDTO buildMissionDTO(Mission mission) {
        var missionDTO = new MissionDTO();
        missionDTO.setActivitiesIds(mission.getActivities().stream().map(JobActivityLabel::getUuid).toList());
        missionDTO.setId(mission.getId());
        missionDTO.setTitle(mission.getTitle());
        missionDTO.setContextsForCategory(mission.getContextsForCategory().stream().map(this::buildContextsForCategoryDTO).toList());
        missionDTO.setJobId(mission.getJob().getId());
        return missionDTO;
    }

    private ContextsForCategoryDTO buildContextsForCategoryDTO(ContextsForCategory contextsForCategory) {
        var dto = new ContextsForCategoryDTO();
        dto.setCategoryId(contextsForCategory.getCategory().getId());
        dto.setContextsIds(contextsForCategory.getContexts() == null ? Lists.newArrayList() :
                contextsForCategory.getContexts().stream().map(Context::getId).toList());
        dto.setNoContextForCategory(contextsForCategory.getNoContextForCategory());
        return dto;
    }

    private Mission updateMissionWith(Mission mission, UpdateMissionCommandDTO updateMissionCommand) {
        mission.setTitle(updateMissionCommand.getTitle());

        if (jobHasNoCandidature(mission.getJob().getId())) {
            mission.resetActivitiesIds(getActivities(updateMissionCommand.getActivitiesIds()));
            Collection<ContextsForCategory> toDelete = mission.resetContexts(getContextsForCategory(updateMissionCommand.getContextsForCategory()));
            if (!toDelete.isEmpty()) {
                contextsForCategoryRepository.deleteAll(toDelete);
            }
        }
        repository.save(mission);
        return mission;
    }

    private boolean jobHasNoCandidature(UUID jobId) {
        return recruitmentCandidatureRepository.findDistinctByRecruitmentRecruitmentProfileJobId(jobId).isEmpty();
    }

    private void ensureJobIsModifiable(Job job) {
        if (!jobHasNoCandidature(job.getId())) {
            throw new MissionNotModifiableException(job.getId());
        }
    }
}
