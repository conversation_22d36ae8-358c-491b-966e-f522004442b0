package com.erhgo.services;

import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.openapi.dto.LocationDTO;
import com.erhgo.repositories.AbstractOrganizationRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.services.keycloak.KeycloakService;
import com.opencsv.CSVParserBuilder;
import com.opencsv.CSVReaderBuilder;
import com.opencsv.exceptions.CsvException;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.representations.idm.CredentialRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
@Profile("!master")
@RequiredArgsConstructor
@Slf4j
public class AnonymizerService {

    public static final String ANONYMIZED_EMAIL_EXTENSION = "@example.com";
    private final KeycloakService keycloakService;
    private final UserProfileRepository userProfileRepository;
    private final Keycloak keycloak;
    private final AbstractOrganizationRepository organizationRepository;
    private final TransactionTemplate transactionTemplate;

    private static final Random RANDOM = new Random();

    @Value("${keycloak-realms.front_office_realm_id:''}")
    private String frontOfficeRealm;

    private List<String> names;
    private static final List<LocationDTO> locations = List.of(
            new LocationDTO().city("Craponne").postcode("69290").citycode("69094").longitude(4.68951f).latitude(45.7477f),
            new LocationDTO().city("Agen").postcode("47000").citycode("47001").longitude(0.625584f).latitude(44.2028f),
            new LocationDTO().city("Troyes").postcode("10000").citycode("10387").longitude(4.07828f).latitude(48.2967f),
            new LocationDTO().city("Vallant-Saint-Georges").postcode("10170").citycode("10043").longitude(4.02489f).latitude(48.5414f),
            new LocationDTO().city("Feyzin").postcode("69320").citycode("69276").longitude(4.85726f).latitude(45.6731f),
            new LocationDTO().city("Bron").postcode("69500").citycode("69029").longitude(4.91168f).latitude(45.7345f),
            new LocationDTO().city("Mions").postcode("69780").citycode("69283").longitude(4.94915f).latitude(45.6647f),
            new LocationDTO().city("Saint-Pierre-de-l'Isle").postcode("17330").citycode("17124").longitude(-0.587835f).latitude(46.0375f),
            new LocationDTO().city("Paris 10e Arrondissement").postcode("75010").citycode("75110").longitude(2.36111f).latitude(48.876f),
            new LocationDTO().city("Bernac-Dessus").postcode("65360").citycode("65313").longitude(0.0813794f).latitude(43.1744f),
            new LocationDTO().city("Oullins").postcode("69600").citycode("69149").longitude(4.80299f).latitude(45.715f),
            new LocationDTO().city("Pleyben").postcode("29190").citycode("29062").longitude(-3.97241f).latitude(48.1701f),
            new LocationDTO().city("Verniolle").postcode("09340").citycode("09332").longitude(1.66101f).latitude(43.0809f),
            new LocationDTO().city("Trappes").postcode("78190").citycode("78621").longitude(1.99344f).latitude(48.775f),
            new LocationDTO().city("Bougneau").postcode("17800").citycode("17027").longitude(-0.508678f).latitude(45.5514f),
            new LocationDTO().city("Grenade").postcode("31330").citycode("31232").longitude(1.28039f).latitude(43.764f),
            new LocationDTO().city("Colomiers").postcode("31770").citycode("31149").longitude(1.327f).latitude(43.6116f),
            new LocationDTO().city("Biscarrosse").postcode("40600").citycode("40046").longitude(-1.17736f).latitude(44.4091f),
            new LocationDTO().city("Pamiers").postcode("09100").citycode("09081").longitude(1.69416f).latitude(43.1308f),
            new LocationDTO().city("Strasbourg").postcode("67000").citycode("67482").longitude(7.76753f).latitude(48.5713f),
            new LocationDTO().city("Lyon 6e Arrondissement").postcode("69006").citycode("69386").longitude(4.85208f).latitude(45.7729f),
            new LocationDTO().city("Paris 10e Arrondissement").postcode("75010").citycode("75110").longitude(2.36111f).latitude(48.876f),
            new LocationDTO().city("Oullins").postcode("69600").citycode("69149").longitude(4.80299f).latitude(45.715f),
            new LocationDTO().city("Pleyben").postcode("29190").citycode("29062").longitude(-3.97241f).latitude(48.1701f)
    );


    @RolesAllowed(Role.ODAS_ADMIN)
    @Async
    public void anonymizeAll() {
        try (var inputStream = new URL("https://www.data.gouv.fr/fr/datasets/r/55cd803a-998d-4a5c-9741-4cd0ee0a7699").openStream(); var reader = new InputStreamReader(inputStream, StandardCharsets.ISO_8859_1)) {
            // To use locally served file: replace URL inputStream with Files.newInputStream(Path.of("/home/<USER>/Downloads/Prenoms.csv")
            names = new CSVReaderBuilder(reader)
                    .withSkipLines(1)
                    .withCSVParser(new CSVParserBuilder().withSeparator(';').build())
                    .build()
                    .readAll()
                    .stream()
                    // Next line removes some trailing (1)
                    .map(n -> n[0].split(" ")[0])
                    .toList();
        } catch (CsvException | IOException e) {
            log.error("Unable to anonymize", e);
        }
        anonymizeUsers();
        anonymizeOrga();
        log.info("Anonymization ended successfully");
    }

    private void anonymizeOrga() {
        transactionTemplate.execute(useless -> {
            organizationRepository.findAll().forEach(o -> {
                var newTitle = StringUtils.capitalize(randomName());
                if (o.getDescription() != null) {
                    o.setDescription(o.getDescription().replaceAll("(?i)" + o.getTitle(), newTitle));
                }
                o.setTitle(newTitle);
            });
            return null;
        });
        log.info("Organizations were successfully anonymized");
    }

    private String randomName() {
        return names.get(RANDOM.nextInt(names.size()));
    }

    private void anonymizeUsers() {
        var allKeycloakUsers = keycloakService.findAllFrontOfficeUsersForAnonymizationOnly()
                .stream()
                .filter(u -> !u.getEmail().endsWith(ANONYMIZED_EMAIL_EXTENSION)).toList();
        var foDBUsers = userProfileRepository
                .findByUserIdIn(allKeycloakUsers.stream().map(UserRepresentation::getId).collect(Collectors.toSet()))
                .stream()
                .collect(Collectors.toMap(UserProfile::userId, Function.identity()));

        IntStream.range(0, allKeycloakUsers.size())
                .forEach(index -> {
                    if (index % 100 == 0) {
                        log.info("Anonymize user {} over {}", index + 1, allKeycloakUsers.size());
                    }
                    var userRepresentation = allKeycloakUsers.get(index);
                    anonymize(userRepresentation, foDBUsers.get(userRepresentation.getId()));
                });
        log.info("{} users anonymized successfully", allKeycloakUsers.size());
    }


    private void anonymize(UserRepresentation userRepresentation, UserProfile userProfile) {
        if (!userRepresentation.getEmail().contains("candidate")) {
            transactionTemplate.execute(useless -> {
                var firstname = StringUtils.capitalize(randomName());
                var lastname = "Du" + randomName();
                userRepresentation.setFirstName(firstname);
                userRepresentation.setLastName(lastname);
                userRepresentation.setEmail(firstname.toLowerCase() + '.' + lastname.toLowerCase() + ANONYMIZED_EMAIL_EXTENSION);
                var userId = userRepresentation.getId();
                try {
                    keycloak.realm(frontOfficeRealm).users().get(userId).update(userRepresentation);
                } catch (Exception e) {
                    userRepresentation.setEmail(firstname.toLowerCase() + '.' + lastname.toLowerCase() + UUID.randomUUID() + ANONYMIZED_EMAIL_EXTENSION);
                    keycloak.realm(frontOfficeRealm).users().get(userId).update(userRepresentation);
                }
                resetFrontOfficeUserPassword(userId);
                if (userProfile != null && userProfile.generalInformation() != null) {
                    var generalInfo = userProfile.generalInformation();
                    generalInfo.setPhoneNumber("0" + IntStream.range(0, 9).map(r -> RANDOM.nextInt(10)).mapToObj(String::valueOf).collect(Collectors.joining("")));
                    var location = locations.get(RANDOM.nextInt(locations.size()));
                    generalInfo.setBirthDate(LocalDate.of(1900 + RANDOM.nextInt(100), RANDOM.nextInt(11) + 1, RANDOM.nextInt(27) + 1));
                    generalInfo.setLocation(Location.builder().latitude(location.getLatitude()).longitude(location.getLongitude()).city(location.getCity()).postcode(location.getPostcode()).citycode(location.getCitycode()).build());
                    userProfileRepository.save(userProfile);
                }
                log.trace("New user anonymized {}", userRepresentation.getEmail());
                return null;
            });
        } else {
            log.info("Preserve {}", userRepresentation.getEmail());
        }
    }

    private void resetFrontOfficeUserPassword(String userId) {
        var userResource = keycloak.realm(frontOfficeRealm).users().get(userId);
        final var passwordCred = new CredentialRepresentation();
        passwordCred.setTemporary(false);
        passwordCred.setType(CredentialRepresentation.PASSWORD);
        passwordCred.setValue("Test:123");
        userResource.resetPassword(passwordCred);
    }


}
