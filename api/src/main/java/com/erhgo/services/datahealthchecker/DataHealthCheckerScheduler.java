package com.erhgo.services.datahealthchecker;

import com.erhgo.services.SecurityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class DataHealthCheckerScheduler {

    private final DataHealthCheckerService service;
    private final SecurityService securityService;

    @Scheduled(cron = "${application.executeDataHealthCheckQueries.cron}", zone = "Europe/Paris")
    @SchedulerLock(name = "executeDataHealthCheckQueriesScheduler")
    public void remindToCheckDataHealth() {
        log.info("Executing data health check queries");
        securityService.doAsAdmin(service::executeAllDataHealthChecker);
        log.info("Queries executed successfully");
    }

}

