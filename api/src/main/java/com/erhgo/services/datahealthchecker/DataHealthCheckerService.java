package com.erhgo.services.datahealthchecker;

import com.erhgo.domain.configuration.DataHealthChecker;
import com.erhgo.domain.exceptions.HealthCheckerQueryNotExecutableException;
import com.erhgo.repositories.DataHealthCheckerRepository;
import com.erhgo.security.Role;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.notifier.messages.DataHealthCheckMessage;
import jakarta.annotation.security.RolesAllowed;
import jakarta.persistence.EntityManager;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@RequiredArgsConstructor
@Slf4j
public class DataHealthCheckerService {
    private final EntityManager em;
    private final DataHealthCheckerRepository repository;
    private final Notifier notifier;


    @RolesAllowed(Role.ODAS_ADMIN)
    public void executeAllDataHealthChecker() throws HealthCheckerQueryNotExecutableException {
        var testResults =
                repository
                        .findAll()
                        .stream()
                        .collect(Collectors.toMap(Function.identity(), this::executeSafelyQuery));

        logTestResults(testResults);
        var testOutputMessage = new DataHealthCheckMessage(testResults);

        if (!testOutputMessage.getText().isEmpty()) {
            notifier.sendMessage(testOutputMessage);
        }

    }

    private HealthCheckResultDTO executeSafelyQuery(DataHealthChecker dhc) {
        var healthCheckResult = new HealthCheckResultDTO();
        try {
            var result = executeUnsafelyQuery(dhc.query());
            healthCheckResult.setSuccessful(result[0].equals(1));
            healthCheckResult.setFailedTestData(String.valueOf(result[1]));
        } catch (RuntimeException e) {
            healthCheckResult.setSuccessful(false);
            healthCheckResult.setException(e);
        }
        return healthCheckResult;
    }

    private Object[] executeUnsafelyQuery(String query) {
        if (!query.toUpperCase().startsWith("SELECT") || query.contains(";")) {
            var cause = "contains %s".formatted(query.contains(";") ? "';'" : query.split(" ")[0]);
            throw new HealthCheckerQueryNotExecutableException(cause);
        }
        var result = em.createNativeQuery(query).getSingleResult();
        if (!(result instanceof Object[] objects) || objects.length != 2) {
            throw new HealthCheckerQueryNotExecutableException("SQL query must return two columns: {boolean - true if test succeeds; any value - the obtained wrong value making this test fail}");
        }
        return objects;
    }


    private void logTestResults(Map<DataHealthChecker, HealthCheckResultDTO> testResults) {
        testResults.forEach((dhc, result) -> {
            if (result.hasExecutionProblem()) {
                log.warn("[ %s ]: Test not ran: REASON: %s".formatted(dhc.title(), result.getException()));
            } else if (result.isSuccessful) {
                log.info("[ %s ]: Test succeed !".formatted(dhc.title()));
            } else {
                log.warn("[ %s ]: Test failed: REASON: %s [CURRENT DATA: %s]"
                        .formatted(dhc.title(), dhc.errorMessage(), result.getFailedTestData()));
            }
        });

    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Getter
    @Setter
    public static class HealthCheckResultDTO {

        private boolean isSuccessful;

        private RuntimeException exception;

        private String failedTestData;

        public boolean hasExecutionProblem() {
            return exception != null;
        }

    }
}
