package com.erhgo.services;

import com.erhgo.domain.classifications.esco.EscoOccupation;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.classifications.EscoOccupationRepository;
import com.erhgo.security.Role;
import com.erhgo.services.dtobuilder.CapacityDTOBuilder;
import com.erhgo.services.dtobuilder.EscoDataDTOBuilder;
import com.erhgo.services.dtobuilder.PageDTOBuilder;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
public class EscoOccupationService {
    private final EscoOccupationRepository escoOccupationRepository;
    private final EscoDataDTOBuilder escoDataDTOBuilder;

    @Transactional(readOnly = true)
    public EscoOccupationDetailDTO getByUri(String uri) {
        var esco = findByUri(uri);
        return escoDataDTOBuilder.buildOccupationDTO(esco);
    }

    private EscoOccupation findByUri(String uri) {
        return getEscoOccupation(uri);
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional(readOnly = true)
    public CapacitiesResultDTO findCapacities(String uri) {
        var escoOccupation = getEscoOccupation(uri);
        List<CapacityAndLevelDTO> occurrences = CapacityDTOBuilder.buildCapacityAndLevelDTOS(escoOccupation);
        return new CapacitiesResultDTO().capacities(occurrences).fullyQualified(escoOccupation.isFullyQualified());
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional(readOnly = true)
    public EscoOccupationPageDTO getOccupationPage(Integer size, Integer page, String query) {
        var pageable = PageRequest.of(page, size);
        var occupationPage = escoOccupationRepository.search(query, pageable);

        return PageDTOBuilder.buildEscoOccupationPage(occupationPage.map(escoDataDTOBuilder::buildOccupationSummaryDTO));
    }

    private EscoOccupation getEscoOccupation(String uri) {
        return escoOccupationRepository.findById(uri)
                .orElseThrow(() -> new EntityNotFoundException(uri, EscoOccupation.class));
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional(readOnly = true)
    public List<EscoOccupationSummaryDTO> findByQuery(String query) {

        return escoOccupationRepository.search(query, PageRequest.of(0, 200))
                .getContent()
                .stream()
                .map(escoDataDTOBuilder::buildOccupationSummaryDTO)
                .toList();
    }
}
