package com.erhgo.services;

import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.openapi.dto.ErhgoClassificationDTO;
import com.erhgo.openapi.dto.SetUserErhgoClassificationCommandDTO;
import com.erhgo.openapi.dto.UserErhgoClassificationsDTO;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.repositories.classifications.ErhgoClassificationRepository;
import com.erhgo.security.AuthorizeExpression;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserErhgoClassificationService {

    private final UserProfileRepository userProfileRepository;
    private final ErhgoClassificationRepository erhgoClassificationRepository;

    private UserProfile getUserProfileOrThrow(String userId) {
        return userProfileRepository.findByUserId(userId).orElseThrow(() -> new EntityNotFoundException(userId, UserProfile.class));
    }

    @Transactional(readOnly = true)
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_READ)
    public UserErhgoClassificationsDTO getUserErhgoClassifications(String userId) {
        var userProfile = getUserProfileOrThrow(userId);
        return new UserErhgoClassificationsDTO()
                .acceptedErhgoClassifications(userProfile.getAcceptedErhgoClassifications().stream().map(this::toDTO).toList())
                .refusedErhgoClassifications(userProfile.getRefusedErhgoClassifications().stream().map(this::toDTO).toList());
    }

    private ErhgoClassificationDTO toDTO(ErhgoClassification erhgoClassification) {
        return new ErhgoClassificationDTO().code(erhgoClassification.getCode()).title(erhgoClassification.getTitle()).orderIndex(erhgoClassification.getOrderIndex()).icon(erhgoClassification.getIcon()).highPriority(erhgoClassification.isHighPriority());
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_OF_COMMAND_WRITE)
    public void setUserErhgoClassification(SetUserErhgoClassificationCommandDTO command) {
        var userProfile = getUserProfileOrThrow(command.getUserId());
        var erhgoClassification = erhgoClassificationRepository.findById(command.getErhgoClassificationCode())
                .orElseThrow(() -> new EntityNotFoundException(command.getErhgoClassificationCode(), ErhgoClassification.class));
        userProfile.updateErhgoClassificationAcceptedStatus(erhgoClassification, command.getIsAccepted());
    }
}

