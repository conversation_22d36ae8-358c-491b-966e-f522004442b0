package com.erhgo.services;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.openapi.dto.TaskInformationDTO;
import com.erhgo.security.Role;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
@Slf4j
public class AdminService {

    private final ApplicationContext applicationContext;

    @RolesAllowed(Role.ODAS_ADMIN)
    public void executeManuallyTask(TaskInformationDTO command) {
        try {
            var clazz = Class.forName(command.getClassName());
            var method = clazz.getDeclaredMethod(command.getMethodName());
            var serviceInstance = applicationContext.getBean(clazz);
            if (mayBeLaunch(method, clazz)) {
                method.invoke(serviceInstance);
            } else {
                throw new GenericTechnicalException("Only scheduled services may be launched this way");
            }
        } catch (InvocationTargetException | IllegalAccessException | NoSuchMethodException |
                 ClassNotFoundException e) {
            log.error("Unable to launch task {}", command, e);
            throw new GenericTechnicalException("Unable to launch task %s".formatted(command), e);
        }
    }

    private static boolean mayBeLaunch(Method method, Class<?> clazz) {
        return
                clazz.getName().toLowerCase().contains("indexer") ||
                        (method.getAnnotations() != null &&
                                Stream.of(method.getAnnotations()).anyMatch(a -> Scheduled.class.isAssignableFrom(a.annotationType())));
    }
}
