package com.erhgo.services;

import com.erhgo.domain.AbstractEntity;
import com.erhgo.openapi.dto.AbstractItemPageDTO;
import com.erhgo.repositories.AbstractRepository;
import com.google.common.collect.Lists;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

public abstract class AbstractService<E extends AbstractEntity, R extends AbstractRepository<E>> {

    @Autowired
    protected R repository;

    @Transactional(readOnly = true)
    public E findOneByCode(String code) {
        return repository.findOneByCode(code);
    }

    @Transactional(readOnly = true)
    public Long count() {
        return repository.count();
    }

    @Transactional(readOnly = true)
    public PageDTOAdapter<E> findPaginatedAndFilteredByProperty(
            int offset,
            int limit,
            String sortProperty,
            String sortDirection,
            String filter) {

        var repository = this.repository;
        return fetchPageDTOAdapter(offset, limit, sortProperty, sortDirection, filter, repository);
    }

    protected <A extends E, B extends AbstractRepository<A>> PageDTOAdapter<A> fetchPageDTOAdapter(int offset, int limit, String sortProperty, String sortDirection, String filter, B repository) {
        var safeSortProperty = Strings.isBlank(sortProperty) ? "code" : sortProperty;
        final Pageable pageable = limit < 0 ? Pageable.unpaged() : PageRequest.of(offset, limit, Sort.Direction.valueOf(sortDirection), safeSortProperty);
        if (filter != null && !filter.isEmpty()) {
            return new PageDTOAdapter<>(repository.findByCodeContainingIgnoreCaseOrTitleContainingIgnoreCase(filter, filter, pageable));
        } else {
            return new PageDTOAdapter<>(repository.findAll(pageable));
        }
    }

    @Transactional(readOnly = true)
    public Iterable<E> findAll() {
        return repository.findAll();
    }

    @Transactional
    public E create(E origin) {
        origin.setId(null);
        origin = repository.save(origin);
        origin.updateCodeOnEntityCreate();
        // 2nd save to persist new generated code
        return repository.save(origin);
    }

    @Transactional
    public E updateIgnoringFields(E origin, String... ignoringFields) {
        final Optional<E> optionalE = repository.findById(origin.getId());
        final E entityInDb;
        if (optionalE.isPresent()) {
            List<String> fieldsToIgnore = Lists.newArrayList(ignoringFields);
            fieldsToIgnore.add("id");
            fieldsToIgnore.add("code");
            entityInDb = optionalE.get();
            BeanUtils.copyProperties(origin, entityInDb, fieldsToIgnore.toArray(new String[0]));
        } else {
            entityInDb = create(origin);
        }
        return repository.save(entityInDb);
    }

    @Transactional
    public E update(E origin) {
        return updateIgnoringFields(origin);
    }

    @Transactional(readOnly = true)
    public List<E> findByIds(List<Long> ids) {
        return Lists.newArrayList(repository.findAllById(ids));
    }

    @EqualsAndHashCode(callSuper = true)
    public static class PageDTOAdapter<E> extends AbstractItemPageDTO {

        @Getter
        private final List<E> content;

        public PageDTOAdapter(Page<E> page) {
            super();
            pageSize(page.getSize())
                    .numberOfElementsInPage(page.getNumberOfElements())
                    .pageIndex(page.getNumber())
                    .totalNumberOfElements(page.getTotalElements())
                    .totalPages(page.getTotalPages());

            this.content = page.getContent();
        }
    }
}
