package com.erhgo.services.scrapeduser;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.exceptions.InvalidScrapingException;
import com.erhgo.services.generation.UserInfosFromCVGenerationService;
import com.erhgo.services.generation.UserInfosFromImageCVGenerationService;
import com.erhgo.services.generation.dto.UserInfosExtractionResponse;
import com.erhgo.services.scrapeduser.dto.ScrapedCandidate;
import com.erhgo.services.userprofile.FilePartProvider;
import com.erhgo.utils.ImageUtils;
import com.erhgo.utils.PDFUtils;
import com.erhgo.utils.dto.ImageFileData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;

@Slf4j
@Service
@RequiredArgsConstructor
public class CvProcessingService {

    private final AgefiphScraper agefiphScraper;
    private final UserInfosFromCVGenerationService userInfosFromCVGenerationService;
    private final UserInfosFromImageCVGenerationService userInfosImageExtractorService;

    public UserInfosExtractionResponse downloadAndExtractCvData(ScrapedCandidate candidate) throws InvalidScrapingException {
        try {
            var fileData = agefiphScraper.downloadCv(candidate.getCvDownloadLink());
            if (fileData == null) {
                log.info("No CV download for candidate {}, skipping data extraction", candidate.getCandidateId());
                return null;
            }
            var inputStream = new ByteArrayInputStream(fileData);
            var fileProvider = new FilePartProvider(inputStream.readAllBytes(), candidate.getCvDownloadLink());

            var contentType = fileProvider.contentType();
            var isPdf = ImageUtils.isPdf(fileProvider);
            var isImage = ImageUtils.isImage(fileProvider);
            log.debug("File detection for candidate {}: isPdf={}, isImage={}, contentType={}",
                    candidate.getCandidateId(), isPdf, isImage, fileProvider.contentType());

            if (isPdf) {
                log.info("Processing PDF file for candidate {}, size: {}", candidate.getCandidateId(), fileProvider.getSize());
                return processPdfContent(fileProvider);
            } else if (isImage) {
                log.info("Processing image file for candidate {} with content type {} and size {}", candidate.getCandidateId(), contentType, fileProvider.getSize());
                return processImageContent(candidate, fileProvider);
            } else {
                log.warn("Unsupported file type for candidate {}: {}", candidate.getCandidateId(), contentType);
                return null;
            }

        } catch (InvalidScrapingException e) {
            log.error("CV download failed for candidate {}, skipping data extraction", candidate.getCandidateId(), e);
            return null;
        } catch (IOException e) {
            log.error("Error processing CV for candidate {}", candidate.getCandidateId(), e);
            throw new GenericTechnicalException("Error processing CV file", e);
        }
    }

    private UserInfosExtractionResponse processPdfContent(FilePartProvider fileProvider) {
        var extractedText = PDFUtils.extractTextFromPDF(fileProvider);
        if (!extractedText.trim().isEmpty()) {
            var aiResponse = userInfosFromCVGenerationService.generateUserInfos(extractedText);
            return aiResponse != null ? aiResponse.getResult() : null;
        }
        return null;
    }

    private UserInfosExtractionResponse processImageContent(ScrapedCandidate candidate, FilePartProvider fileProvider) throws IOException {
        try {
            var compressedImage = ImageUtils.compressImage(fileProvider);
            var imageData = new ImageFileData(compressedImage, MediaType.parseMediaType("image/jpeg"));
            var aiResponse = userInfosImageExtractorService.generateUserInfos(imageData);
            return aiResponse != null ? aiResponse.getResult() : null;
        } catch (IOException e) {
            log.error("Error processing image content for candidate {}: {}", candidate.getCandidateId(), e.getMessage(), e);
            throw new GenericTechnicalException("Error processing image file", e);
        }
    }

}
