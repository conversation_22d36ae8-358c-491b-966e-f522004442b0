package com.erhgo.services.scrapeduser;

import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.InvalidScrapingException;
import com.erhgo.domain.userprofile.ScrapedUser;
import com.erhgo.domain.userprofile.UserProfileCreationState;
import com.erhgo.openapi.dto.ScrapedUserDTO;
import com.erhgo.repositories.ScrapedUserRepository;
import com.erhgo.services.generation.dto.UserInfosExtractionResponse;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.scrapeduser.dto.ScrapedCandidate;
import com.opencsv.CSVWriter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.OutputStreamWriter;
import java.time.OffsetDateTime;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

import static com.erhgo.utils.StringUtils.CSV_FIELD_SEPARATOR;
import static com.opencsv.ICSVWriter.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class ScrapedUserService {

    @Value("${agefiph.scraping.batch-candidates-limit}")
    private long batchCandidateLimit;

    @Value("${agefiph.scraping.processing-delay-seconds}")
    private long processingDelaySeconds;

    private final AgefiphScraper agefiphScraper;
    private final ScrapedUserRepository scrapedUserRepository;
    private final ScrapedUserAccountService scrapedUserAccountService;
    private final CvProcessingService cvProcessingService;


    private final KeycloakService keycloakService;

    public void processCandidates() throws InvalidScrapingException {
        log.info("Starting batch candidate processing");

        var candidates = agefiphScraper.searchCandidates();
        if (candidates.isEmpty()) {
            log.warn("No candidates found from AGEFIPH API");
            return;
        }

        var processedCount = new AtomicInteger(0);

        candidates.stream()
                .filter(candidate -> candidate.getCvDownloadLink() != null && !candidate.getCvDownloadLink().isEmpty())
                .filter(candidate -> !scrapedUserRepository.existsByCandidateId(candidate.getCandidateId()))
                .takeWhile(candidate -> processedCount.get() < batchCandidateLimit)
                .forEach(candidate -> {
                    try {
                        Thread.sleep(processingDelaySeconds * 1000);
                        var scrapedUser = processCandidate(candidate);
                        if (scrapedUser != null) {
                            processedCount.incrementAndGet();
                            log.info("Successfully processed candidate {}", candidate.getCandidateId());
                        }
                    } catch (InterruptedException e) {
                        log.error("Error processing candidate {}: {}", candidate.getCandidateId(), e.getMessage(), e);
                        Thread.currentThread().interrupt();
                    }
                });

        log.info("Completed batch candidate processing. Processed {} candidates", processedCount.get());
    }


    private ScrapedUser processCandidate(ScrapedCandidate candidate) throws InvalidScrapingException {
        try {
            var scrapedUser = buildBaseScrapedUser(candidate);

            processCvContent(candidate, scrapedUser);
            var savedUser = scrapedUserRepository.save(scrapedUser);

            if (!StringUtils.isBlank(savedUser.getEmail())) {
                scrapedUserAccountService.addCandidateToBrevoList(savedUser);
            }

            return savedUser;
        } catch (RuntimeException e) {
            log.error("Unexpected error processing candidate {}: {}", candidate.getCandidateId(), e.getMessage(), e);
            return null;
        }
    }


    private ScrapedUser buildBaseScrapedUser(ScrapedCandidate candidate) {
        return ScrapedUser.builder()
                .candidateId(candidate.getCandidateId())
                .profileUrl(candidate.getProfileUrl())
                .cvDownloadLink(candidate.getCvDownloadLink())
                .creationState(UserProfileCreationState.PENDING)
                .build();
    }

    private void processCvContent(ScrapedCandidate candidate, ScrapedUser scrapedUser) throws InvalidScrapingException {
        var userInfos = cvProcessingService.downloadAndExtractCvData(candidate);
        generateUserInfos(scrapedUser, userInfos);
    }


    private void generateUserInfos(ScrapedUser scrapedUser, UserInfosExtractionResponse userInfos) {
        if (userInfos != null) {
            scrapedUser.setEmail(userInfos.getEmail());
        }
    }

    public List<ScrapedUserDTO> getAllScrapedUsers() {
        return scrapedUserRepository.findAll()
                .stream()
                .map(this::convertToDTO)
                .toList();
    }

    public ScrapedUserDTO getScrapedUserDetail(UUID uuid) {
        var scrapedUser = scrapedUserRepository.findById(uuid)
                .orElseThrow(() -> new EntityNotFoundException(uuid, ScrapedUser.class));
        return convertToDTO(scrapedUser);
    }

    private ScrapedUserDTO convertToDTO(ScrapedUser scrapedUser) {
        return new ScrapedUserDTO()
                .uuid(scrapedUser.getUuid())
                .candidateId(scrapedUser.getCandidateId())
                .email(scrapedUser.getEmail())
                .firstName(scrapedUser.getFirstName())
                .lastName(scrapedUser.getLastName())
                .jobTitle(scrapedUser.getJobTitle())
                .location(scrapedUser.getLocation())
                .cvContent(scrapedUser.getCvContent())
                .errorMessage(scrapedUser.getErrorMessage())
                .cvDownloadLink(scrapedUser.getCvDownloadLink())
                .profileUrl(scrapedUser.getProfileUrl())
                .creationState(scrapedUser.getCreationState() != null ? ScrapedUserDTO.CreationStateEnum.valueOf(scrapedUser.getCreationState().getLabel()) : null);
    }

    @Transactional(readOnly = true)
    public void writeScrapedUsersCsv(OutputStreamWriter writer) {
        var scrapedUsers = scrapedUserRepository.findAll();

        var scrapedUsersStream = scrapedUsers.stream()
                .map(this::buildScrapedUserExportLine);

        var headers = new String[]{"ID candidat", "Prénom", "Nom", "Job recherché", "Localisation", "Email", "LienCv", "UrlProfil"};

        try (var csvWriter = new CSVWriter(
                writer,
                CSV_FIELD_SEPARATOR,
                DEFAULT_QUOTE_CHARACTER,
                DEFAULT_ESCAPE_CHARACTER,
                DEFAULT_LINE_END)) {
            csvWriter.writeNext(headers);
            csvWriter.writeAll(scrapedUsersStream::iterator);
        } catch (IOException e) {
            log.error("Unable to generate CSV", e);
        }
    }

    private String[] buildScrapedUserExportLine(ScrapedUser scrapedUser) {
        return new String[]{
                scrapedUser.getCandidateId(),
                scrapedUser.getFirstName(),
                scrapedUser.getLastName(),
                scrapedUser.getJobTitle(),
                scrapedUser.getLocation(),
                scrapedUser.getEmail(),
                scrapedUser.getCvDownloadLink(),
                scrapedUser.getProfileUrl()
        };
    }

    public void updateOldScrapedUsersToNotInterested() {
        var oneMonthAgo = Date.from(OffsetDateTime.now().minusMonths(1).toInstant());
        var oldScrapedUsers = scrapedUserRepository.findByCreatedDateBeforeAndCreationState(
                oneMonthAgo, UserProfileCreationState.PENDING);

        var updatedCount = new AtomicInteger(0);

        oldScrapedUsers.stream()
                .filter(scrapedUser -> scrapedUser.getEmail() != null)
                .forEach(scrapedUser -> {
                    try {
                        var keycloakUser = keycloakService.getFOUserRepresentationByEmail(scrapedUser.getEmail());
                        if (keycloakUser == null) {
                            scrapedUser.setCreationState(UserProfileCreationState.NOT_INTERESTED);
                            scrapedUserRepository.save(scrapedUser);
                            updatedCount.incrementAndGet();
                            log.debug("Updated scraped user {} to NOT_INTERESTED", scrapedUser.getCandidateId());
                        }
                    } catch (RuntimeException e) {
                        log.error("Error updating scraped user {}: {}", scrapedUser.getCandidateId(), e.getMessage(), e);
                    }
                });

        log.info("Completed update of old scraped users. Updated {} users to NOT_INTERESTED status", updatedCount.get());
    }
}
