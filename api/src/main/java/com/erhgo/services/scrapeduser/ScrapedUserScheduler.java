package com.erhgo.services.scrapeduser;

import com.erhgo.services.SecurityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "agefiph.scraping.enabled", havingValue = "true")
public class ScrapedUserScheduler {

    private final ScrapedUserService scrapedUserService;
    private final SecurityService securityService;

    @Scheduled(cron = "${agefiph.scraping.cron}", zone = "Europe/Paris")
    @SchedulerLock(name = "candidateScrapingScheduler", lockAtLeastFor = "PT30M")
    public void processDailyCandidates() {
        log.info("Starting daily candidate scraping job");
        securityService.doAsAdmin(scrapedUserService::processCandidates);
        log.info("Daily candidate scraping job completed successfully");
    }

    @Scheduled(cron = "0 0 10 * * *", zone = "Europe/Paris")
    @SchedulerLock(name = "updateOldScrapedUsersScheduler", lockAtLeastFor = "PT30M")
    public void updateOldScrapedUsersToNotInterested() {
        log.info("Starting update of old scraped users to NOT_INTERESTED status");
        securityService.doAsAdmin(scrapedUserService::updateOldScrapedUsersToNotInterested);
        log.info("Update of old scraped users completed successfully");
    }
}
