package com.erhgo.services.search;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.openapi.dto.LocationDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.erhgo.utils.StringUtils.extractPostcode;

@Service
@Slf4j
@RequiredArgsConstructor
public class GeoService {
    private static final int MIN_SIZE_FOR_BAN = 3;

    @Data
    public static class GeoResponseDTO {
        @JsonProperty
        private List<FeatureDTO> features;
    }

    @Data
    public static class FeatureDTO {
        @JsonProperty
        private GeometryDTO geometry;

        @JsonProperty
        private PropertiesDTO properties;
    }

    @Data
    public static class PropertiesDTO {
        private String city;
        private String postcode;
        private String citycode;
    }

    @Data
    public static class GeometryDTO {
        @JsonProperty
        private List<Double> coordinates;
    }

    private static final long TIMEOUT_IN_SECONDS = 30;

    private final RestTemplateBuilder restTemplateBuilder;
    private RestTemplate restTemplate;

    @Value("${geo.url}")
    private String remoteGeoUrl;

    private static final String SEARCH_QUERY_PARAM = "q";

    @PostConstruct
    void initializeTemplate() {
        log.info("Initializing geo client");
        this.restTemplate = restTemplateBuilder
                .setReadTimeout(Duration.ofSeconds(TIMEOUT_IN_SECONDS))
                .setConnectTimeout(Duration.ofSeconds(TIMEOUT_IN_SECONDS))
                .build();
    }

    @Retryable(
            maxAttempts = 5,
            retryFor = GenericTechnicalException.class,
            backoff = @org.springframework.retry.annotation.Backoff(delay = 300L, multiplier = 1.3),
            label = "Fetch Geo Coordinates"
    )
    public LocationDTO fetchGeoCoordinates(String cityAndPostcodeIn, String source) {
        var cityAndPostcode = com.erhgo.utils.StringUtils.normalizeForBAN(cityAndPostcodeIn);
        var location = new LocationDTO().city(cityAndPostcode);
        try {
            var result = fetchGeoResult(cityAndPostcode, source);
            result.ifPresentOrElse(
                    f -> location
                            .city(f.getProperties().getCity())
                            .longitude(f.getGeometry().getCoordinates().get(0).floatValue())
                            .latitude(f.getGeometry().getCoordinates().get(1).floatValue())
                            .postcode(f.getProperties().getPostcode())
                            .citycode(f.getProperties().getCitycode()),
                    () -> log.error("No answer found in BAN for {} - using as is, without postcode or coordinates (context: {})", cityAndPostcode, source)
            );
        } catch (RestClientException e) {
            log.warn("Unable to fetch geo data at URL {} (query: {}, message: {}, context: {})", remoteGeoUrl, cityAndPostcode, e.getMessage(), source, e);
            throw new GenericTechnicalException("Unable to fetch geo data");
        }
        log.debug("Geocoding: got result {} for city and postcode {} - context: {}", location, cityAndPostcode, source);
        return location;
    }

    private Optional<FeatureDTO> fetchGeoResult(String cityAndPostcode, String source) {
        var result = fetchFromBAN(cityAndPostcode, source, false);
        if (result.isEmpty()) {
            var postcode = extractPostcode(cityAndPostcode);
            if (postcode != null) {
                result = fetchFromBAN(postcode, source, false);
            }
            if (result.isEmpty()) {
                var cityOnly = (postcode != null ? cityAndPostcode.replaceAll(postcode, " ") : cityAndPostcode).replaceAll("[()]", "").trim();
                result = fetchFromBAN(cityOnly, source, false);
            }
            if (result.isEmpty()) {
                result = fetchFromBAN(cityAndPostcode, source, true);
            }
        }
        return result;
    }

    private Optional<FeatureDTO> fetchFromBAN(String query, String source, boolean plainAddress) {
        if (StringUtils.isBlank(query) || query.trim().length() < MIN_SIZE_FOR_BAN) {
            log.warn("Query is blank or too small for BAN {} - context: {}", query, source);
            return Optional.empty();
        }
        var response = restTemplate.getForEntity(
                remoteGeoUrl,
                GeoResponseDTO.class,
                Map.of(
                        SEARCH_QUERY_PARAM, query,
                        "type", plainAddress ? "" : "municipality"
                ));
        if (!response.getStatusCode().is2xxSuccessful()) {
            log.error("Unable to fetch geo data at URL {} (invalid status in response, got: {}, query: {}, context: {})", remoteGeoUrl, response.getStatusCode(), query, source);
            throw new GenericTechnicalException("Unable to fetch geo data, bad remote response");
        }
        var responseBody = response.getBody();
        if (responseBody == null || responseBody.getFeatures() == null) {
            return Optional.empty();
        }
        return responseBody.getFeatures().stream().findFirst();
    }
}
