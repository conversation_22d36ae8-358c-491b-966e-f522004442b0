package com.erhgo.services.search;

import com.algolia.search.models.indexing.BatchIndexingResponse;
import com.algolia.search.models.indexing.DeleteResponse;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;

import java.util.concurrent.CompletableFuture;

public interface ErhgoOccupationIndexer {

    CompletableFuture<BatchIndexingResponse> updateOccupationIndexation(ErhgoOccupation occupations);

    void replaceAllOccupationsWith(Iterable<ErhgoOccupation> occupations);

    CompletableFuture<DeleteResponse> removeOccupation(ErhgoOccupation occupationToDelete);
}
