package com.erhgo.services.search;

import com.algolia.search.models.indexing.BatchIndexingResponse;
import com.algolia.search.models.indexing.DeleteResponse;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.openapi.dto.ErhgoOccupationSearchDTO;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.services.dtobuilder.ErhgoOccupationDataDTOBuilder;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.CompletableFuture;

@Service
@ConditionalOnMissingBean(Algolia.class)
@Slf4j
@RequiredArgsConstructor
public class DatabaseErhgoOccupationSearch implements ErhgoOccupationFinder, ErhgoOccupationIndexer {
    private final ErhgoOccupationRepository erhgoOccupationRepository;

    @VisibleForTesting
    @Override
    @Transactional
    public List<ErhgoOccupationSearchDTO> searchOccupations(String query, boolean highlights) {
        return Lists.newArrayList(erhgoOccupationRepository
                .search(query, null, PageRequest.of(0, 20))
                .map(o -> ErhgoOccupationDataDTOBuilder.buildErhgoSearchDTO(o.getOccupation()))
                .getContent());
    }

    @Override
    public CompletableFuture<BatchIndexingResponse> updateOccupationIndexation(ErhgoOccupation occupation) {
        doWarn();
        // No op.
        return CompletableFuture.completedFuture(null);
    }

    private static void doWarn() {
        log.warn("Ignoring indexation due to lack of Algolia configuration");
    }

    @Override
    public void replaceAllOccupationsWith(Iterable<ErhgoOccupation> occupations) {
        doWarn();
        // No op.
    }

    @Override
    public CompletableFuture<DeleteResponse> removeOccupation(ErhgoOccupation occupationToDelete) {
        doWarn();
        // No op.
        return CompletableFuture.completedFuture(null);
    }
}
