package com.erhgo.services.search;

import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.openapi.dto.AlgoliaQueryDTO;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
@ConditionalOnMissingBean(AlgoliaUserIndexer.class)
public class DefaultUserIndexer implements UserIndexer {

    @PostConstruct
    public void alertMock() {
        log.warn("No algolia configuration found - user indexation disabled");
    }

    @Override
    public void indexAllFOUsers() {
        log.warn("No Algolia conf - do nothing");
    }

    @Override
    public void index(UserProfile entity) {
        log.warn("User {} not indexed - no user indexation configured", entity);
    }

    @Override
    public void indexModifiedUsers() {
        log.warn("No user indexation configured - modified users won't be indexed");
    }

    @Override
    public void delete(String userId) {
        log.warn("No user indexation configured - no deletion of {}", userId);
    }

    @Override
    public List<String> findMatchingUserIds(AlgoliaQueryDTO query) {
        log.warn("No user indexation configured no userIds found");
        return Collections.emptyList();
    }

}
