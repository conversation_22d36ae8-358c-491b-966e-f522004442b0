package com.erhgo.services.search;

import com.algolia.search.SearchClient;
import com.algolia.search.SearchIndex;
import com.algolia.search.exceptions.AlgoliaRuntimeException;
import com.algolia.search.models.indexing.AroundRadius;
import com.algolia.search.models.indexing.Query;
import com.algolia.search.models.indexing.SearchResult;
import com.algolia.search.models.settings.IndexSettings;
import com.algolia.search.models.settings.RemoveStopWords;
import com.algolia.search.models.settings.TypoTolerance;
import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.Situation;
import com.erhgo.domain.referential.Behavior;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.domain.userprofile.GeneralInformation;
import com.erhgo.domain.userprofile.UserErhgoClassification;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.openapi.dto.AlgoliaQueryDTO;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.utils.DateTimeUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.annotations.VisibleForTesting;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.security.RolesAllowed;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@ConditionalOnExpression("!'${algolia.applicationId}'.isEmpty() and !'${algolia.adminApiKey}'.isEmpty()")
@Component
@Slf4j
@RequiredArgsConstructor
public class AlgoliaUserIndexer implements UserIndexer {
    private static final int DEFAULT_SALARY_VALUE = 0;
    @Value("${algolia.maxCandidates}")
    private int maxCandidates;
    private final UserProfileRepository userProfileRepository;
    private final AlgoliaSearchConfigService algoliaSearchConfigService;
    private SearchIndex<UserDTO> userIndex;

    private IndexSettings commonIndexSettings() {
        return new IndexSettings()
                .setQueryLanguages(Collections.singletonList("fr"))
                .setIgnorePlurals(true)
                .setTypoTolerance(TypoTolerance.of(true))
                .setRemoveStopWords(RemoveStopWords.of(true))
                .setRemoveWordsIfNoResults("allOptional")
                .setPaginationLimitedTo(1_000_000)
                ;
    }

    @PostConstruct
    public void initialize() {
        initUserIndex(algoliaSearchConfigService.getAdminClient());
    }

    private void initUserIndex(SearchClient client) {
        userIndex = client.initIndex(algoliaSearchConfigService.getUserIndexName(), UserDTO.class);
        userIndex.setSettings(
                commonIndexSettings()
                        .setSearchableAttributes(Arrays.asList("city", "erhgoOccupations", "activities", "experienceTitles"))
                        .setAttributesForFaceting(Arrays.asList("rome", "isco", "refusedClassifications", "capacities", "searchable(erhgoOccupations)", "searchable(refusedCriteria)", "searchable(criteria)", "masteryLevel", "channels", "mainBehaviors", "city", "softSkills", "blacklisted", "activeSearch", "salary", "blacklistedOccupations"))
                        .setReplicas(Collections.emptyList()));
    }

    @Override
    @RolesAllowed(Role.ODAS_ADMIN)
    @Async
    @Transactional
    public void indexAllFOUsers() {
        log.info("Index all users launched");
        var userProfiles = userProfileRepository.findAllFOUsers();
        var userProfilesSize = userProfiles.size();
        // peek is OK for log
        @SuppressWarnings("squid:S3864")
        var usersToIndex = IntStream
                .range(0, userProfilesSize)
                .peek(i -> {
                    if (i % 100 == 0) {
                        log.info("Indexing user {} over {}", i, userProfilesSize);
                    }
                })
                .mapToObj(userProfiles::get)
                .map(this::buildProfile);
        try {
            userIndex.replaceAllObjects(usersToIndex::iterator, true);
            log.info("Users indexing ended successfully");
        } catch (AlgoliaRuntimeException e) {
            log.error("Unable to index users", e);
        }
    }

    @Override
    @Async
    @Transactional
    public void index(UserProfile entity) {
        log.debug("Indexing user {}", entity);
        var user = userProfileRepository.getReferenceById(entity.uuid());
        userIndex.saveObject(buildProfile(user)).waitTask();
        user.indexed();
        log.debug("User {} indexed", entity);
    }

    @Override
    @Async
    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void indexModifiedUsers() {
        var users = userProfileRepository.findUsersToIndex(
                new Date(),
                PageRequest.of(0, 1000)
        );
        log.info("Update {} users indexation (over a total of {})", users.getNumberOfElements(), users.getTotalElements());
        users.forEach(this::index);
        log.info("Modified users indexation terminated");
    }

    @Override
    public void delete(String userId) {
        log.debug("Deleting user {} from Algolia", userId);
        try {
            userIndex.deleteObject(userId);
        } catch (AlgoliaRuntimeException e) {
            log.error("Unable to remove user {} from Algolia index", userId, e);
        }
    }

    private Stream<UserDTO> getCandidatesUnpaginated(Query query) {
        query.setHitsPerPage(maxCandidates);
        var stop = new AtomicBoolean(false);
        return IntStream.iterate(0, i -> i + 1)
                .limit(1000)
                .mapToObj(query::setPage)
                .takeWhile(s -> !stop.get())
                .map(q -> {
                    var res = this.search(q);
                    stop.set(res.getPage() >= res.getNbPages() - 1);
                    return res;
                })
                .peek(i -> {
                    if (log.isDebugEnabled() && i.getPage() == 0) {
                        log.debug("Algolia user search query params: filters = {}, optionalFilters = {}, aroundRadius = {}, aroundLatLng = {}, nb hits before score related eviction: {}, global: {} ", query.getFilters(), query.getOptionalFilters(), query.getAroundRadius(), query.getAroundLatLng(), i.getNbHits(), query.toParam());
                    }
                    log.debug("Req nb {}", i.getPage());
                })
                .map(SearchResult::getHits)
                .flatMap(Collection::stream);
    }

    private SearchResult<UserDTO> search(Query query) {
        return userIndex.search(query);
    }


    @Override
    public List<String> findMatchingUserIds(AlgoliaQueryDTO query) {
        return getCandidatesUnpaginated(
                new Query(query.getQuery())
                        .setAroundRadius(query.getAroundRadius() == null ? null : AroundRadius.of(query.getAroundRadius()))
                        .setAroundLatLng(query.getAroundLatLng())
                        .setFilters(query.getFilters())
        ).map(UserDTO::getObjectID).toList();
    }

    // Every indexed field must dirty UserProfile hibernate entity to ensure user is well re-indexed - see UserProfile.dirtiesUserProfile() usage
    @VisibleForTesting
    public UserDTO buildProfile(UserProfile userProfile) {
        return UserDTO.builder()
                .objectID(userProfile.userId())
                .erhgoOccupations(new HashSet<>(userProfile.getOccupationsTitle()))
                .activities(userProfile.getAllActivities().stream().map(JobActivityLabel::getTitle).collect(Collectors.toSet()))
                .masteryLevel((int) userProfile.masteryLevel())
                .masteryLevelAsFloat(userProfile.masteryLevel())
                .channels(new HashSet<>(userProfile.channels()))
                .mainBehaviors(userProfile.behaviors().stream().map(Behavior::getTitle).collect(Collectors.toSet()))
                .refusedCriteria(userProfile.getRefusedCriteriaValues().stream().map(CriteriaValue::getCode).collect(Collectors.toSet()))
                .criteria(userProfile.getAcceptedCriteriaValues().stream().map(CriteriaValue::getCode).collect(Collectors.toSet()))
                .city(userProfile.getCity())
                .softSkills(userProfile.getSoftSkillsTitles())
                .capacities(userProfile.getAllCapacities().stream().map(Capacity::getCode).collect(Collectors.toSet()))
                .geolocation(Optional.ofNullable(userProfile.generalInformation())
                        .map(GeneralInformation::getLocation)
                        .map(AlgoliaUtils.AlgoliaGeoloc::forLocation)
                        .orElse(null))
                .experienceTitles(userProfile.getExperienceTitles())
                .numberOfExperiences(userProfile.experiences().size())
                .lastConnectionDate(Optional.ofNullable(userProfile.lastConnectionDate()).map(d -> Date.from(d.atZone(DateTimeUtils.zoneOffset()).toInstant())).orElse(null))
                .lastConnectionTimestamp(Optional.ofNullable(userProfile.lastConnectionDate()).map(l -> l.toEpochSecond(DateTimeUtils.zoneOffset())).orElse(null))
                .creationDate(userProfile.getCreatedDate())
                .creationTimestamp(Optional.ofNullable(userProfile.getCreatedDate()).map(d -> d.getTime() / 1000).orElse(null))
                .situation(Optional.ofNullable(userProfile.generalInformation()).map(GeneralInformation::getSituation).map(Enum::name).orElse(null))
                .blacklisted(Optional.ofNullable(userProfile.generalInformation()).map(GeneralInformation::getTransactionalBlacklisted).orElse(false))
                .activeSearch(Optional.ofNullable(userProfile.generalInformation()).map(GeneralInformation::getSituation).map(s -> s == Situation.STANDBY || s == Situation.RESEARCHING).orElse(true))
                .salary(Optional.ofNullable(userProfile.generalInformation()).map(GeneralInformation::getSalary).orElse(DEFAULT_SALARY_VALUE))
                .delayInMonth(Optional.ofNullable(userProfile.generalInformation().getDelayInMonth()).orElse(0))
                .refusedClassifications(userProfile.erhgoClassifications().stream().filter(UserErhgoClassification::isRefused).map(UserErhgoClassification::getErhgoClassification).map(ErhgoClassification::getCode).collect(Collectors.toSet()))
                .isco(userProfile.getIscoOccupationCodes())
                .rome(userProfile.getRomeOccupationCodes())
                .radiusInKm(Optional.ofNullable(userProfile.generalInformation()).map(GeneralInformation::getRadiusInKm).orElse(null))
                .blacklistedOccupations(userProfile.getBlacklistedOccupations()
                        .stream()
                        .map(ErhgoOccupation::getId)
                        .map(UUID::toString)
                        .collect(Collectors.toSet()))
                .build();
    }


    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    @Builder
    public static class UserDTO {

        private String objectID;

        private Set<String> erhgoOccupations;

        private Set<String> experienceTitles;

        private Set<String> activities;

        private int masteryLevel;
        private float masteryLevelAsFloat;

        private Set<String> channels;

        private Set<String> mainBehaviors;

        private Set<String> refusedCriteria;

        private Set<String> criteria;

        private String city;

        private List<String> softSkills;

        private Set<String> hardSkills;

        private Set<String> capacities;

        private Long lastConnectionTimestamp;

        private Date lastConnectionDate;

        private Long creationTimestamp;

        private Date creationDate;

        private int numberOfExperiences;

        private String situation;

        private boolean blacklisted;

        private boolean activeSearch;

        private int salary;

        private int delayInMonth;

        private Set<String> refusedClassifications;

        private Set<String> blacklistedOccupations;

        private Set<String> rome;

        private Set<Integer> isco;

        private Integer radiusInKm;

        @JsonProperty("_rankingInfo")
        private RankingInfo rankingInfo;

        @JsonProperty("_geoloc")
        private AlgoliaUtils.AlgoliaGeoloc geolocation;

        private static final Comparator<UserDTO> topTenSort = Comparator.<UserDTO, Integer>comparing(u -> (u.rankingInfo == null || u.rankingInfo.filters == null) ? 0 : u.rankingInfo.filters).reversed();

        @AllArgsConstructor
        @NoArgsConstructor
        public static class RankingInfo {
            @JsonProperty("filters")
            private Integer filters;
            @JsonProperty("userScore")
            private Integer userScore;

        }
    }
}
