package com.erhgo.services.search;

import com.algolia.search.models.indexing.BatchIndexingResponse;
import com.algolia.search.models.indexing.MultiResponse;
import com.algolia.search.models.settings.IndexSettings;
import com.algolia.search.models.settings.RemoveStopWords;
import com.algolia.search.models.settings.TypoTolerance;
import com.erhgo.domain.userprofile.Location;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class AlgoliaUtils {
    public static final BiConsumer<BatchIndexingResponse, Throwable> UNABLE_TO_INDEX_ENTITY_HANDLER = (a, e) -> {
        if (e == null) {
            var tasksAsString = (a != null && a.getResponses() != null) ? a.getResponses().stream().map(r -> String.valueOf(r.getTaskID())).collect(Collectors.joining()) : "Unknown";
            log.debug("Task(s) {} ended successfully", tasksAsString);
        } else {
            log.error("Indexation error", e);
        }
    };
    public static final BiConsumer<MultiResponse, Throwable> UNABLE_TO_INDEX_MULTIPLE_ENTITY_HANDLER = (a, e) -> {
        if (e == null) {
            log.debug("Bulk indexation ended successfully");
        } else {
            log.error("Bulk indexation error", e);
        }
    };
    public static final int ALGOLIA_SEARCH_QUERY_MAX_LENGTH = 500;
    public static final String HIGHLIGHT_OPEN_TAG = "<strong>";
    public static final String HIGHLIGHT_CLOSE_TAG = "</strong>";

    public static IndexSettings commonIndexSettings() {
        var supportedLanguages = Collections.singletonList("fr");

        return new IndexSettings()
                .setIndexLanguages(supportedLanguages)
                .setQueryLanguages(supportedLanguages)
                .setIgnorePlurals(true)
                .setTypoTolerance(TypoTolerance.of(true))
                .setRemoveStopWords(RemoveStopWords.of(true))
                .setRemoveWordsIfNoResults("allOptional");
    }

    @NoArgsConstructor
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public static class AlgoliaGeoloc {

        @JsonProperty("lng")
        private float longitude;
        @JsonProperty("lat")
        private float latitude;

        public static AlgoliaGeoloc forLocation(Location location) {
            if (location == null || location.getLongitude() == null || location.getLatitude() == null) return null;
            return new AlgoliaGeoloc(location.getLongitude(), location.getLatitude());
        }
    }
}
