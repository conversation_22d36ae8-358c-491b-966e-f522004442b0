package com.erhgo.services.notifications;

import com.erhgo.domain.enums.NotificationType;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.userprofile.UserMobileToken;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.notification.*;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.NotificationRepository;
import com.erhgo.repositories.RecruitmentRepository;
import com.erhgo.repositories.UserMobileTokenRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.repositories.dto.CountForUserDTO;
import com.erhgo.security.AuthorizeExpression;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import com.erhgo.services.search.UserIndexer;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationService {
    @VisibleForTesting
    public static int MAX_LENGTH_OF_IN = 500;

    private final NotificationRepository notificationRepository;
    private final UserProfileRepository userProfileRepository;
    private final UserMobileTokenRepository userMobileRepository;
    private final UserMobileTokenRepository userMobileTokenRepository;
    private final RecruitmentRepository recruitmentRepository;
    private final NotificationSenderInterface notificationSenderService;
    private final UserIndexer userIndexer;
    private final SecurityService securityService;

    @Value("${application.mobile.notificationTesters}")
    private List<String> notificationTestersUsersId;

    @Value("${application.mobile.forcedNotificationRecipientUserId}")
    private String forcedNotificationRecipientUserId;

    @RequiredArgsConstructor
    private static class NotificationVisitorImpl implements NotificationVisitor {
        private Map<String, String> entityData;
        private final RecruitmentRepository recruitmentRepository;

        private Recruitment getRecruitmentOrThrow(String recruitmentId) {
            return recruitmentRepository.findById(Long.valueOf(recruitmentId)).orElseThrow(() -> new EntityNotFoundException(recruitmentId, Recruitment.class));
        }

        @Override
        public void visit(RecruitmentNotification notification) {
            var recruitment = getRecruitmentOrThrow(notification.getEntityId().toString());
            entityData = Map.of("ORGANIZATION_NAME", recruitment.getOrganizationName(),
                    "JOB_TITLE", recruitment.getJob().getTitle(),
                    "CITY", recruitment.getLocation() != null
                            ? recruitment.getLocation().getCity() : "");
        }

        @Override
        public void visit(DefaultNotification notification) {
            entityData = Map.of(
                    "URL", Strings.nullToEmpty(notification.getLink()),
                    "TITLE", notification.getSubject(),
                    "CONTENT", notification.getContent()
            );
        }

        @Override
        public void visit(SuspendedRecruitmentNotification notification) {
            var recruitment = getRecruitmentOrThrow(notification.getEntityId().toString());
            entityData = Map.of("ORGANIZATION_NAME", recruitment.getOrganizationName(),
                    "JOB_TITLE", recruitment.getJob().getTitle(),
                    "CITY", recruitment.getLocation() != null
                            ? recruitment.getLocation().getCity() : "");
        }
    }


    private UserProfile getUserProfileOrThrow(String userId) {
        return userProfileRepository.findByUserId(userId).orElseThrow(() -> new EntityNotFoundException(userId, UserProfile.class));
    }

    @Transactional(readOnly = true)
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_READ)
    public List<UserNotificationDTO> listNotifications(String userId) {
        var userProfile = getUserProfileOrThrow(userId);
        var notifications = notificationRepository.findByUserProfileUuidOrderByCreatedDateDesc(userProfile.uuid());
        return notifications.stream().map(x -> {
                    var visitor = new NotificationVisitorImpl(recruitmentRepository);
                    x.accept(visitor);
                    return new UserNotificationDTO()
                            .id(x.getId())
                            .userId(userId)
                            .createdDate(Optional.ofNullable(x.getCreatedDate()).map(i -> i.toInstant().atOffset(ZoneOffset.UTC)).orElse(null))
                            .type(x.getType().name())
                            .state(UserNotificationStateDTO.fromValue(x.getState().name()))
                            .entityId(x.getEntityId() == null ? null : String.valueOf(x.getEntityId()))
                            .entityType(getEntityType(x))
                            .entityData(visitor.entityData);
                }
        ).toList();
    }

    @NotNull
    private static UserNotificationEntityTypeDTO getEntityType(AbstractNotification x) {
        return Stream.of(UserNotificationEntityTypeDTO.values())
                .filter(a -> a.name().equals(x.getEntityType()))
                .findFirst()
                .orElse(UserNotificationEntityTypeDTO.DEFAULT);
    }


    @Transactional
    @PreAuthorize(AuthorizeExpression.USER_NOTIFICATION.NOTIFICATION_WRITE)
    public void markNotificationAsRead(UUID notificationId) {
        getNotificationOrThrow(notificationId).markAsRead();
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.USER_NOTIFICATION.NOTIFICATION_WRITE)
    public void deleteNotification(UUID notificationId) {
        notificationRepository.delete(getNotificationOrThrow(notificationId));
    }

    private AbstractNotification getNotificationOrThrow(UUID notificationId) {
        return notificationRepository.findById(notificationId).orElseThrow(() -> new EntityNotFoundException(notificationId, AbstractNotification.class));
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_WRITE)
    public void markAllNotificationsAsRead(String userId) {
        var userProfile = getUserProfileOrThrow(userId);
        notificationRepository.findByUserProfileUuidOrderByCreatedDateDesc(userProfile.uuid())
                .forEach(AbstractNotification::markAsRead);
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public NotifiableUsersCountDTO countNotifiableUsers(AlgoliaQueryDTO query) {
        var usersIds = userIndexer.findMatchingUserIds(query);
        var usersTokenCount = (int) (usersIds.isEmpty() ? 0 : fetchUsersToken(usersIds).count());

        return new NotifiableUsersCountDTO()
                .notifiableUsers(usersTokenCount)
                .totalUsers(usersIds.size())
                ;
    }

    private Stream<UserMobileToken> fetchUsersToken(List<String> usersIds) {
        return Lists.partition(usersIds, MAX_LENGTH_OF_IN)
                .stream()
                .map(userMobileTokenRepository::findByUserProfileUserIdIn)
                .flatMap(Collection::stream);
    }

    private Stream<UserProfile> fetchUsersProfiles(List<String> usersIds) {
        return Lists.partition(usersIds, MAX_LENGTH_OF_IN)
                .stream()
                .map(userProfileRepository::findByUserIdIn)
                .flatMap(Collection::stream);
    }

    @Transactional
    @RolesAllowed({Role.ODAS_ADMIN, Role.SOURCING})
    public List<UserProfile> sendNotificationToUsers(UsersMobileNotificationDTO command) {
        var tokens = userMobileRepository.findByUserProfileUserIdIn(Set.copyOf(command.getUsersId()));
        if (!tokens.isEmpty()) {
            var tokensUnreadNotificationsMap = createTokensAndUnreadNotificationsMap(tokens);
            notificationSenderService.sendNotificationToSpecificDevices(
                    tokensUnreadNotificationsMap,
                    command.getSubject(),
                    command.getContent(),
                    command.getData());
        }

        return tokens.stream().map(UserMobileToken::getUserProfile).toList();
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public void sendNotificationsToUsersSelection(SendNotificationsToUsersSelectionCommandDTO command) {
        var usersIds = new ArrayList<>(userIndexer.findMatchingUserIds(command.getUsersSelectionQuery()));
        usersIds.add(forcedNotificationRecipientUserId);
        var usersToken = fetchUsersToken(usersIds).toList();
        var tokensPerUserId = usersToken.stream().collect(Collectors.groupingBy(u -> u.getUserProfile().userId()));
        var usersWithToken = tokensPerUserId.keySet();
        if (!tokensPerUserId.isEmpty()) {
            var tokensUnreadNotificationsMap = createTokensAndUnreadNotificationsMap(usersToken);
            notificationSenderService.sendNotificationToSpecificDevices(tokensUnreadNotificationsMap, command.getSubject(), command.getContent(), null);
        }
        if (!usersIds.isEmpty()) {
            var allNotifications = fetchUsersProfiles(usersIds)
                    .map(user -> createNotification(command, user, usersWithToken.contains(user.userId())))
                    .toList();
            notificationRepository.saveAll(allNotifications);
        }
    }


    @Transactional(readOnly = true)
    public void sendTestNotifications() {
        var usersToken = fetchUsersToken(notificationTestersUsersId).toList();
        var tokensPerUserId = usersToken.stream().collect(Collectors.groupingBy(u -> u.getUserProfile().userId()));
        if (!tokensPerUserId.isEmpty()) {
            var tokensUnreadNotificationsMap = createTokensAndUnreadNotificationsMap(usersToken);
            securityService.doAsAdmin(() -> notificationSenderService.sendNotificationToSpecificDevices(tokensUnreadNotificationsMap, "Notification Test", "Tout fonctionne comme prévu.", null));
        }
    }

    private Map<String, Long> createTokensAndUnreadNotificationsMap(Collection<UserMobileToken> tokens) {
        var unreadNotificationsCountForUserUuid = notificationRepository
                .getUnreadNotificationsCount(tokens.stream().map(UserMobileToken::getUserProfileUUID).collect(Collectors.toSet()))
                .stream()
                .collect(Collectors.toMap(CountForUserDTO::getUserId, CountForUserDTO::getCountForUser));

        Map<String, Long> unreadNotificationsForTokenCount = new HashMap<>();
        tokens.forEach(t -> unreadNotificationsForTokenCount.put(t.getToken(), unreadNotificationsCountForUserUuid.getOrDefault(t.getUserProfileUUID(), 0L) + 1L /* +1 because new notification hasn't been created at this point*/));

        return unreadNotificationsForTokenCount;
    }

    private DefaultNotification createNotification(SendNotificationsToUsersSelectionCommandDTO command, UserProfile user, boolean hasToken) {
        var subject = user.userId().equals(forcedNotificationRecipientUserId) ? "Exemplaire: %s".formatted(command.getSubject()) : command.getSubject();
        return DefaultNotification.builder()
                .subject(subject)
                .content(command.getContent())
                .uri(command.getLink())
                .type(hasToken ? NotificationType.MOBILE : NotificationType.NONE)
                .userProfile(user)
                .build();
    }
}
