package com.erhgo.services.notifications;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Iterables;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.google.firebase.messaging.*;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@ConditionalOnExpression("!'${firebase.apiKey}'.isEmpty()")
public class FirebaseNotificationSender implements NotificationSenderInterface {
    @VisibleForTesting
    public static int MAX_TOKEN_PER_MESSAGES_REQUEST = 500;
    @Value("${firebase.apiKey}")
    private String firebaseKey;

    @PostConstruct
    void init() {
        try {
            var serviceAccount = IOUtils.toInputStream(firebaseKey, Charset.defaultCharset());

            var options = FirebaseOptions.builder()
                    .setCredentials(GoogleCredentials.fromStream(serviceAccount))
                    .build();

            FirebaseApp.initializeApp(options);
        } catch (IOException e) {
            log.error("Failed to initialize Firebase SDK.", e);
            throw new GenericTechnicalException(e.getMessage(), e);
        }
    }

    @Override
    public void sendNotificationToSpecificDevices(Map<String, Long> deviceTokensAndUnreadNotificationCountMap,
                                                  String subject,
                                                  String content,
                                                  Map<String, String> data) {
        var entries = deviceTokensAndUnreadNotificationCountMap.entrySet();
        var partitions = Iterables.partition(entries, MAX_TOKEN_PER_MESSAGES_REQUEST);

        partitions.forEach(partition -> {
            var partitionMap = partition.stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

            doSendNotifications(partitionMap, subject ,content, data);
        });
    }

    private static void doSendNotifications(Map<String, Long> deviceTokensAndUnreadNotificationCountMap, String subject, String content, Map<String, String> data) {
        var notification = Notification.builder()
                .setTitle(subject)
                .setBody(content)
                .build();

        var messages = deviceTokensAndUnreadNotificationCountMap.entrySet().stream().map(entry -> {
            var token = entry.getKey();
            var unreadCount = Math.toIntExact(entry.getValue());

            // https://developer.apple.com/documentation/usernotifications/setting_up_a_remote_notification_server/generating_a_remote_notification
            // seems like content-available and priority: high are required to ensure badge is displayed no matter what
            return Message.builder()
                    .setToken(token)
                    .setNotification(notification)
                    .setApnsConfig(ApnsConfig.builder()
                            .setAps(Aps.builder().setContentAvailable(true).setBadge(unreadCount).build())
                            .build())
                    .setAndroidConfig(AndroidConfig.builder()
                            .setPriority(AndroidConfig.Priority.HIGH)
                            .setNotification(AndroidNotification.builder().setPriority(AndroidNotification.Priority.HIGH).setNotificationCount(unreadCount).build())
                            .build())
                    .putAllData(data != null ? data : new TreeMap<>())
                    .build();
        }).toList();

        try {
            var response = FirebaseMessaging.getInstance().sendEach(messages);
            var successCount = response.getSuccessCount();
            var failureCount = response.getFailureCount();

            if (log.isTraceEnabled()) {
                log.trace("Sent notifications ({} success and {} failures) to device tokens {} ; title: {},  body: {}", successCount, failureCount, deviceTokensAndUnreadNotificationCountMap.keySet(), subject, content);
            }
        } catch (FirebaseMessagingException e) {
            log.error("Failed to send notification to device tokens {} ; title: {},  body: {}", deviceTokensAndUnreadNotificationCountMap.keySet(), subject, content, e);
        }
    }
}
