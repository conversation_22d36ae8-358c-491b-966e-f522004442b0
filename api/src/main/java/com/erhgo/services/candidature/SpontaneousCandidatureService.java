package com.erhgo.services.candidature;

import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.candidature.job.SpontaneousCandidature;
import com.erhgo.domain.dto.event.UserAffectedToChannelsEvent;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.userprofile.UserChannel;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.RecruiterRepository;
import com.erhgo.repositories.SectorRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import com.erhgo.services.dtobuilder.CandidaturesDTOBuilder;
import com.erhgo.services.dtobuilder.PageDTOBuilder;
import com.erhgo.services.sourcing.SourcingMailingService;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.JpaSort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class SpontaneousCandidatureService extends AbstractCandidatureService {

    private final SpontaneousCandidatureRepository spontaneousCandidatureRepository;
    private final RecruiterRepository recruiterRepository;
    private final SectorRepository sectorRepository;
    private final SecurityService securityService;
    private final CandidaturesDTOBuilder candidaturesDTOBuilder;
    private final SourcingMailingService sourcingMailingService;
    private final UserProfileRepository userProfileRepository;

    @SuppressWarnings("java:S1192")
    private static final Map<SourcingCandidatureSortDTO, String> sortProperty = Map.of(
            SourcingCandidatureSortDTO.LAST_ACTION_DATE, "spontaneousCandidature.updatedDate",
            SourcingCandidatureSortDTO.CANDIDATURE_SOURCE, "recruitmentCandidature.generatedForSourcing",
            SourcingCandidatureSortDTO.SUBMISSION_DATE, "spontaneousCandidature.submissionDate",
            SourcingCandidatureSortDTO.CANDIDATURE_STATE, "spontaneousCandidature.globalCandidatureState",
            SourcingCandidatureSortDTO.CANDIDATURES_COUNT, "count(distinct recruitmentCandidature.id)"
    );
    public static final String DEFAULT_SORT = sortProperty.get(SourcingCandidatureSortDTO.LAST_ACTION_DATE);

    private Sort computeSort(SourcingCandidatureSortDTO sourcingCandidatureSort, SortDirectionDTO direction) {
        var orders = new ArrayList<String>();
        var prop = Optional.ofNullable(sourcingCandidatureSort).map(s -> sortProperty.getOrDefault(s, DEFAULT_SORT)).orElse(DEFAULT_SORT);
        orders.add(prop);
        if (orders.stream().noneMatch(DEFAULT_SORT::equals)) {
            orders.add(DEFAULT_SORT);
        }
        return JpaSort.unsafe(direction == SortDirectionDTO.ASC ? Sort.Direction.ASC : Sort.Direction.DESC, orders);
    }

    @EventListener(UserAffectedToChannelsEvent.class)
    @Transactional
    public void createSpontaneousCandidature(UserAffectedToChannelsEvent event) {
        var userProfile = userProfileRepository.findByUserId(event.getUserProfile().userId()).orElse(event.getUserProfile());
        createOrRetrieveCandidaturesOnChannels(event.getChannels(), userProfile, event.getChannelSourceType());
    }

    private Set<SpontaneousCandidature> createOrRetrieveCandidaturesOnChannels(Set<String> codes, UserProfile userProfile, UserChannel.ChannelSourceType channelSourceType) {
        var sourcingRecruiters = recruiterRepository.findByCodeIn(new ArrayList<>(codes))
                .stream()
                .filter(o -> o.getOrganizationType() == AbstractOrganization.OrganizationType.SOURCING)
                .toList();
        var candidatures = new HashSet<SpontaneousCandidature>();
        if (!sourcingRecruiters.isEmpty()
                && Optional.ofNullable(channelSourceType)
                .map(UserChannel.ChannelSourceType::requiresSpontaneousCandidature)
                .orElse(false)) {

            var existentCandidatures = spontaneousCandidatureRepository
                    .findByRecruiterInAndUserProfile(sourcingRecruiters, userProfile);

            var createdCandidatures = sourcingRecruiters.stream()
                    .filter(r -> existentCandidatures.stream().noneMatch(c -> c.getCodeOfRecruiter().equals(r.getCode())))
                    .map(r -> createSpontaneousCandidature(userProfile, r))
                    .toList();
            log.debug("spontaneous candidatures created: {}", createdCandidatures);
            existentCandidatures.forEach(c -> c.setSubmissionDate(OffsetDateTime.now()));
            candidatures.addAll(existentCandidatures);
            candidatures.addAll(createdCandidatures);
        } else {
            log.debug("No sourcing recruiter found for {}: spontaneous candidatures handling is ignored for user {}", codes, userProfile.userId());
        }
        return candidatures;
    }

    @NotNull
    private SpontaneousCandidature createSpontaneousCandidature(UserProfile userProfile, Recruiter r) {
        return spontaneousCandidatureRepository.save(SpontaneousCandidature.builder()
                .recruiter(r)
                .userProfile(userProfile)
                .build());
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.SOURCING)
    public SourcingCandidaturePageDTO getSourcingCandidates(Integer size, Integer page, SourcingCandidatureSortDTO sourcingCandidatureSort, SortDirectionDTO direction, List<SourcingCandidatureStateDTO> candidatureStates, String searchQuery) {
        var relatedStates = candidatureStates == null ? GlobalCandidatureState.finalizedValues() : candidatureStates.stream().flatMap(CandidaturesDTOBuilder::getGlobalStatesFromSourcingState).toList();
        var pageRequest = StringUtils.isBlank(searchQuery) ? PageRequest.of(page, size, computeSort(sourcingCandidatureSort, direction)) : Pageable.unpaged();
        var recruiters = recruiterRepository.findByCodeIn(securityService.getGroupsRelatedToAuthenticatedUser());
        var candidates = spontaneousCandidatureRepository.findAllCandidates(recruiters, relatedStates, pageRequest);
        return PageDTOBuilder.buildSourcingCandidaturesPage(filterCandidateQuery(candidates.map(candidaturesDTOBuilder::buildSourcingCandidatureItem), searchQuery));
    }

    @Transactional
    @RolesAllowed(Role.CANDIDATE)
    public void updateSectors(String organizationCode, UpdateSectorForSpontaneousCandidatureCommandDTO command) {
        var candidature = createOrRetrieveCandidaturesOnChannels(Set.of(organizationCode), securityService.getAuthenticatedUserProfile(), UserChannel.ChannelSourceType.ENTERPRISE_PAGE)
                .stream()
                .findFirst()
                .orElseThrow(() -> new EntityNotFoundException(organizationCode, Recruiter.class));

        var referentialSectors = sectorRepository.findByCodeIn(command.getReferentialSectors());
        candidature.resetSectors(referentialSectors, command.getCustomSectors());
    }
}
