package com.erhgo.services.candidature;

import com.erhgo.services.SecurityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CandidatureArchiverScheduler {
    private final CandidatureArchiverService service;
    private final SecurityService securityService;

    @Scheduled(cron = "${application.archiving.cron}", zone = "Europe/Paris")
    @SchedulerLock(name = "archivingRefusedCandidatureAfterThreeMonths")
    public void archiveCandidatures() {
        log.info("Archiving refused candidature");
        securityService.doAsAdmin(service::archiveCandidatures);
        log.debug("Refused candidature archiving ended");
    }

}

