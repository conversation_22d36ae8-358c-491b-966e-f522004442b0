package com.erhgo.services.candidature;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.candidature.job.*;
import com.erhgo.domain.criteria.Criteria;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.CandidatureState;
import com.erhgo.domain.enums.Frequency;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.InvalidEntityException;
import com.erhgo.domain.exceptions.NoRecruitmentAvailableForCandidatureGeneration;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.userprofile.GeneralInformation;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.criteria.UserCriteriaValue;
import com.erhgo.domain.userprofile.event.UserNewCandidaturePublishedEvent;
import com.erhgo.domain.userprofile.experience.JobContextMet;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.*;
import com.erhgo.security.AuthorizeExpression;
import com.erhgo.security.Role;
import com.erhgo.services.ChannelAssignationHelper;
import com.erhgo.services.GeneralInformationService;
import com.erhgo.services.RecruitmentHelper;
import com.erhgo.services.SecurityService;
import com.erhgo.services.dtobuilder.*;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.mailing.SendCandidatureNotificationEmailService;
import com.erhgo.services.mailing.SendCandidatureRefusalEmailService;
import com.erhgo.services.mailing.SendEmailValidationCandidatureService;
import com.erhgo.services.matching.MatchingService;
import com.erhgo.services.sourcing.SourcingMailingService;
import com.erhgo.utils.SalaryUtils;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static com.erhgo.domain.utils.EventPublisherUtils.publish;

@Service
@RequiredArgsConstructor
@Slf4j
public class RecruitmentCandidatureService extends AbstractCandidatureService {

    private final MatchingService matchingService;
    private final UserExperienceRepository userExperienceRepository;
    private final RecruitmentRepository recruitmentRepository;
    private final CandidatureNoteRepository candidatureNoteRepository;
    private final UserProfileRepository userProfileRepository;
    private final ContextRepository contextRepository;
    private final KeycloakService keycloakService;
    private final RecruitmentHelper recruitmentHelper;
    private final RecruitmentCandidatureRepository recruitmentCandidatureRepository;
    private final AbstractCandidatureRepository abstractCandidatureRepository;
    private final ChannelAssignationHelper channelAssignationHelper;
    private final SendCandidatureRefusalEmailService sendCandidatureRefusalEmailService;
    private final SendCandidatureNotificationEmailService sendCandidatureNotificationEmailService;
    private final SendEmailValidationCandidatureService sendEmailValidationCandidatureService;
    private final SecurityService securityService;
    private final GeneralInformationService generalInformationService;
    private final CandidaturesDTOBuilder candidaturesDTOBuilder;
    private final CriteriaDTOBuilder criteriaDTOBuilder;
    private final SalaryUtils salaryUtils;
    private final SourcingMailingService sourcingMailingService;

    private static final String DEFAULT_SORT = "submissionDate";
    @SuppressWarnings("java:S1192")
    private static final Map<SourcingCandidatureSortDTO, String> sortProperty = Map.of(
            SourcingCandidatureSortDTO.LAST_ACTION_DATE, "updatedDate",
            SourcingCandidatureSortDTO.CANDIDATURE_SOURCE, "generatedForSourcing",
            SourcingCandidatureSortDTO.SUBMISSION_DATE, "submissionDate",
            SourcingCandidatureSortDTO.CANDIDATURE_STATE, "globalCandidatureState"
    );

    private Sort computeSort(SourcingCandidatureSortDTO sourcingCandidatureSort, SortDirectionDTO direction) {
        var orders = new ArrayList<Sort.Order>();
        var prop = Optional.ofNullable(sourcingCandidatureSort).map(s -> sortProperty.getOrDefault(s, DEFAULT_SORT)).orElse(DEFAULT_SORT);
        orders.add(direction == SortDirectionDTO.ASC ? Sort.Order.asc(prop) : Sort.Order.desc(prop));
        if (orders.stream().noneMatch(o -> DEFAULT_SORT.equals(o.getProperty()))) {
            orders.add(Sort.Order.desc(DEFAULT_SORT));
        }
        return Sort.by(orders);
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.CANDIDATURE.CANDIDATURE_WRITE)
    public RecruitmentCandidature changeStateTo(Long candidatureId, CandidatureState state) {
        var candidature = (RecruitmentCandidature) getCandidatureOrThrow(candidatureId);
        return changeStateTo(candidature, state);
    }

    @NotNull
    private RecruitmentCandidature changeStateTo(RecruitmentCandidature candidature, CandidatureState state) {

        var stateChanged = candidature.setState(state);
        if (state == CandidatureState.VALIDATED) {
            matchingService.refreshMatching(candidature);
            if (stateChanged) {
                sendEmailValidationCandidatureService.sendValidationEmailCandidatureTemplate(candidature);
                publish(new UserNewCandidaturePublishedEvent(candidature));
                candidature.postPublish();
            }
        }
        return candidature;
    }

    AbstractCandidature getCandidatureOrThrow(Long candidatureId) {
        return abstractCandidatureRepository.findById(candidatureId).orElseThrow(() -> new EntityNotFoundException(candidatureId, AbstractCandidature.class));
    }


    @RolesAllowed({Role.CANDIDATE, Role.ODAS_ADMIN})
    @Transactional
    public RecruitmentCandidature createOrRetrieve(String recruitmentCode, String userId) {
        var entity = recruitmentCandidatureRepository.findByRecruitmentCodeAndUserProfileUserId(recruitmentCode, userId);
        RecruitmentCandidature candidature;

        if (entity.isEmpty()) {
            var recruitment = recruitmentRepository.findOneByCode(recruitmentCode);
            if (recruitment == null) {
                throw new EntityNotFoundException(recruitmentCode, Recruitment.class);
            }
            var userProfile = getUserProfileForUserIdOrCreate(userId);
            var created = RecruitmentCandidature.builder()
                    .recruitment(recruitment)
                    .userProfile(userProfile)
                    .state(CandidatureState.STARTED)
                    .build();
            candidature = create(created);
            channelAssignationHelper.assignOnCandidature(recruitment.getRecruiterCode(), recruitment.getOrganizationName(), userProfile);

            sendEmailNotificationToRelatedUsers(candidature);
        } else {
            candidature = entity.get();
        }
        candidature.setModifiedByUser(true);
        return candidature;
    }

    @Transactional
    @RolesAllowed({Role.CANDIDATE})
    public void saveCustomAnswerCandidature(Long recruitmentId, String answer) {
        var candidature = createOrRetrieveCandidatureForAuthenticatedUser(recruitmentId);
        candidature.setCustomAnswer(answer);
    }

    private RecruitmentCandidature createOrRetrieveCandidatureForAuthenticatedUser(Long recruitmentId) {
        var userId = securityService.getAuthenticatedUserId();
        var recruitmentCode = recruitmentRepository.findById(recruitmentId).map(Recruitment::getCode).orElseThrow(() -> new EntityNotFoundException(recruitmentId, Recruitment.class));
        return createOrRetrieve(recruitmentCode, userId);
    }

    private void sendEmailNotificationToRelatedUsers(RecruitmentCandidature candidature) {
        if (securityService.isCandidate()) {
            var recruitment = candidature.getRecruitment();
            sendCandidatureNotificationEmailService.sendCandidatureNotificationEmails(recruitment.getUsersIdToNotify(),
                    candidature.getId(),
                    candidature.getJobTitle(),
                    recruitment.getOrganizationName(),
                    candidature.getCodeOfRecruiter(),
                    recruitment.getId());
        }
    }

    private RecruitmentCandidature create(RecruitmentCandidature origin) {
        origin.setId(null);
        origin = recruitmentCandidatureRepository.save(origin);
        origin.updateCodeOnJobCreate();
        return origin;
    }

    private UserProfile getUserProfileForUserIdOrCreate(String userId) {
        var profile = userProfileRepository.findByUserId(userId);
        if (profile.isEmpty()) {
            profile = Optional.of(userProfileRepository.save(new UserProfile().userId(userId).uuid(UUID.randomUUID())));
        }
        return profile.get();
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.CANDIDATURE.CANDIDATURE_WRITE)
    public CandidatureNoteDTO saveNote(Long candidatureId, SaveCandidatureNoteCommandDTO saveCandidatureNoteCommandDTO) {
        var optionalNote = candidatureNoteRepository.findById(saveCandidatureNoteCommandDTO.getId());
        if (optionalNote.isPresent() && !optionalNote.get().getCandidature().getId().equals(candidatureId)) {
            throw new InvalidEntityException("Note is not associated to candidature");
        }

        var candidature = getCandidatureOrThrow(candidatureId);
        candidature.updateLastProcessingData(Recruitment.ProcessingType.COMMENT);

        var note = CandidatureNote.builder()
                .uuid(saveCandidatureNoteCommandDTO.getId())
                .text(saveCandidatureNoteCommandDTO.getText())
                .candidature(candidature)
                .build();
        return JobCandidatureDTOBuilder.buildNote(candidatureNoteRepository.save(note));
    }


    @Transactional
    @PreAuthorize(AuthorizeExpression.CANDIDATURE.CANDIDATURE_WRITE)
    public void updateContextsMet(Long candidatureId, List<ContextMetDTO> contextsMetDTOs) {
        var candidature = getCandidatureOrThrow(candidatureId);
        var userProfile = candidature.getUserProfile();
        var experiences = userExperienceRepository.findByUserProfileUuid(userProfile.uuid());
        var contextsMetMap = userProfile.jobContextsMet().stream()
                .map(contextMet -> new AbstractMap.SimpleEntry<>(contextMet.getContext().getId(), contextMet))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        contextsMetDTOs.forEach(contextMetDTO -> {
            var contextId = contextMetDTO.getContextId();

            if (contextMetDTO.getExperiencesIds().isEmpty()) {
                userProfile.removeJobContextMetById(contextId);
                return;
            }

            var contextMet = Optional.ofNullable(contextsMetMap.get(contextId))
                    .orElseGet(() -> {

                        JobContextMet newContextMet = JobContextMet.builder()
                                .userProfile(userProfile)
                                .context(contextRepository.findById(contextId).orElseThrow())
                                .build();

                        userProfile.contextsMet().add(newContextMet);

                        return newContextMet;
                    });

            contextMet.setFrequency(Frequency.valueOf(contextMetDTO.getFrequency().name()));
            contextMet.resetUserExperiences(experiences.stream().filter(experience -> contextMetDTO.getExperiencesIds().contains(experience.getUuid())).collect(Collectors.toSet()));
        });
        // FIXME: next line is required to persist 'well' UserProfile's ContextMet
        userProfileRepository.save(userProfile);
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.CANDIDATURE.CANDIDATURE_READ)
    public List<ContextToEvaluateReferencingExperiencesDTO> getMandatoryContexts(Long candidatureId) {
        var candidature = (RecruitmentCandidature) getCandidatureOrThrow(candidatureId);

        return candidature.getMandatoryContexts().stream()
                .map(e -> ContextDTOBuilder.buildForRequirement(e, candidature))
                .sorted(Comparator.comparing(ContextToEvaluateReferencingExperiencesDTO::getTitle))
                .toList();
    }

    private Recruitment findRecruitment(UUID jobId) {
        return recruitmentHelper.findRecruitments(jobId)
                .stream()
                .max(Comparator.comparing(AbstractAuditableEntity::getUpdatedDate))
                .orElseThrow(() -> new NoRecruitmentAvailableForCandidatureGeneration(jobId))
                ;
    }


    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public List<GeneratedCandidaturesDTO> generateCandidaturesOnRecruitments(String userId, List<Long> recruitmentsId) {
        var recruitments = StreamSupport.stream(recruitmentRepository.findAllById(recruitmentsId).spliterator(), false).toList();
        if (recruitments.size() < recruitmentsId.size()) {
            throw new EntityNotFoundException(recruitmentsId, Recruitment.class);
        }

        var candidatures = recruitments.stream()
                .map(r -> createOrRetrieve(r.getCode(), userId))
                .map(c -> changeStateTo(c.getId(), CandidatureState.VALIDATED));

        return candidatures
                .map(candidature -> new GeneratedCandidaturesDTO().candidatureId(candidature.getId())
                        .jobId(candidature.getJob().getId())
                        .recruitmentId(candidature.getRecruitment().getId())).toList();
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public void markCandidatureAsRefusedFromBO(Long candidatureId, CustomEmailTemplateDTO customEmailTemplate) {
        var candidature = getCandidatureOrThrow(candidatureId);
        boolean specifiedEmail = customEmailTemplate != null;
        candidature.markAsRefused(specifiedEmail ? CandidatureEmailRefusalState.WAITING : CandidatureEmailRefusalState.NONE, securityService.getAuthenticatedUserId());

        if (specifiedEmail) {
            sendCandidatureRefusalEmailService.sendCustomEmail(candidature, customEmailTemplate);
        }
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public CandidaturePreviewDTO previewCandidature(UUID jobId, String userId) {
        var recruitment = findRecruitment(jobId);

        var candidature = RecruitmentCandidature.builder()
                .recruitment(recruitment)
                .userProfile(getUserProfileForUserIdOrCreate(userId))
                .build();
        matchingService.processMatching(candidature);
        return JobCandidatureDTOBuilder.buildForPreview(candidature);
    }

    @Transactional(readOnly = true)
    @PreAuthorize(AuthorizeExpression.RECRUITMENT.RECRUITMENT_READ)
    public SourcingCandidaturePageDTO getSourcingCandidaturesPage(
            Integer size,
            Integer page,
            Long recruitmentId,
            List<SourcingCandidatureStateDTO> candidatureStates,
            SourcingCandidatureSortDTO sourcingCandidatureSort,
            SortDirectionDTO direction,
            String searchQuery
    ) {
        var relatedStates = candidatureStates == null ? GlobalCandidatureState.finalizedValues() : candidatureStates.stream().flatMap(CandidaturesDTOBuilder::getGlobalStatesFromSourcingState).toList();
        var pageRequest = StringUtils.isBlank(searchQuery) ? PageRequest.of(page, size, computeSort(sourcingCandidatureSort, direction)) : Pageable.unpaged();
        var candidates = recruitmentCandidatureRepository.findByRecruitmentIdAndGlobalCandidatureStateInAndArchivedIsFalse(recruitmentId, pageRequest, relatedStates);
        return PageDTOBuilder.buildSourcingCandidaturesPage(filterCandidateQuery(candidates.map(candidaturesDTOBuilder::buildSourcingCandidatureItem), searchQuery));
    }


    @Transactional
    @RolesAllowed(Role.CANDIDATE)
    public void updateAvailabilityForCandidature(Long recruitmentId, UpdateAvailabilityForCandidatureCommandDTO command) {
        var userId = securityService.getAuthenticatedUserId();
        var recruitment = recruitmentRepository.findById(recruitmentId).orElseThrow(() -> new EntityNotFoundException(recruitmentId, Recruitment.class));
        var candidature = createOrRetrieve(recruitment.getCode(), userId);

        candidature.updateAvailability(command.getIsAvailable(), command.getAvailabilityDelayInMonth());
    }

    @Transactional(readOnly = true)
    public CandidatureInitializationDataDTO initializeCandidatureData(Long recruitmentId) {
        var recruitment = recruitmentRepository.findById(recruitmentId).orElseThrow();
        var user = securityService.getAuthenticatedUserProfile();
        var candidature = recruitmentCandidatureRepository.findByRecruitmentCodeAndUserProfileUserId(recruitment.getCode(), user.userId()).orElse(null);
        var recruiter = recruitment.getRecruiter();
        return new CandidatureInitializationDataDTO()
                .question(StringUtils.trimToEmpty(recruitment.getRecruitmentProfile().getCustomQuestion()))
                .answer(Objects.nonNull(candidature) ? candidature.getCustomAnswer() : null)
                .contact(getCandidatureUserContact(user))
                .condition(getCandidatureCondition(recruitment, user, candidature))
                .requiresExperience(user.experiences().isEmpty())
                .gdprMention(recruiter.getGdprMention())
                .recruiterTitle(recruiter.getTitle())
                ;
    }

    private CandidatureInitializationDataConditionDTO getCandidatureCondition(Recruitment recruitment, UserProfile user, RecruitmentCandidature candidature) {
        var acceptedUserCriteriaValues = user.getAcceptedCriteriaValues();
        var userCriteriaValues = user.getUserCriteriaValues();
        var recruitmentCriteriaValues = recruitment.getJob().getCriteriaValues();

        var listOfConditionCriteria = new ArrayList<CriteriaItemForCandidatureDTO>();
        recruitmentCriteriaValues.stream()
                .filter(rcv -> !acceptedUserCriteriaValues.contains(rcv))
                .filter(rcv -> !rcv.isLowestThresholdValue())
                .sorted(Comparator.comparing(cv -> cv.getCriteria().getCriteriaIndex()))
                .forEach(recruitmentCriteriaValue ->
                        enhanceCriteriaItemForCandidature(
                                userCriteriaValues,
                                listOfConditionCriteria,
                                recruitmentCriteriaValue)
                );

        removeCriteriaValueNotUsedInRecruitmentOrValidatedByUser(listOfConditionCriteria, recruitmentCriteriaValues, acceptedUserCriteriaValues);
        boolean isAvailableNow = Optional.ofNullable(candidature).map(RecruitmentCandidature::getIsAvailable).orElse(user.isAvailableNow());
        var desiredSalary = Optional.ofNullable(user.generalInformation()).map(GeneralInformation::getSalary).orElse(null);
        return new CandidatureInitializationDataConditionDTO()
                .salary(salaryUtils.userMatchesSalary(desiredSalary, recruitment.getBaseSalary(), recruitment.getMaxSalary()) ? null :
                        new CandidatureInitializationDataConditionSalaryDTO()
                                .desiredByUser(desiredSalary)
                                .recruitmentMin(recruitment.getBaseSalary())
                                .recruitmentMax(recruitment.getMaxSalary()
                                )
                ).availabilityForCandidature(isAvailableNow ? null :
                        new AvailabilityForCandidatureDTO()
                                .isAvailable(false)
                                .availabilityDelayInMonth(Optional.ofNullable(candidature).map(RecruitmentCandidature::getAvailabilityDelayInMonth).orElse(user.getDelayInMonth()))
                ).criteria(listOfConditionCriteria)
                ;
    }

    private void removeCriteriaValueNotUsedInRecruitmentOrValidatedByUser(
            Collection<CriteriaItemForCandidatureDTO> listOfConditionCriteria,
            Set<CriteriaValue> recruitmentCriteriaValues,
            Collection<CriteriaValue> acceptedByUserCriteriaValues) {
        listOfConditionCriteria.forEach(c -> {
            var criteria = c.getCriteria();
            var filteredCriteriaValuesDTO = criteria.getCriteriaValues().stream()
                    .filter(cv ->
                            criteria.getQuestionType() == CriteriaQuestionTypeDTO.THRESHOLD
                                    || (recruitmentCriteriaValues.stream().anyMatch(rc -> rc.getCode().equals(cv.getCode()))
                                    && acceptedByUserCriteriaValues.stream().noneMatch(rc -> rc.getCode().equals(cv.getCode())))
                    )
                    .sorted(Comparator.comparing(CriteriaValueDTO::getValueIndex))
                    .toList();
            criteria.setCriteriaValues(filteredCriteriaValuesDTO);
        });
    }

    private void enhanceCriteriaItemForCandidature(
            Set<UserCriteriaValue> userCriteriaValues,
            ArrayList<CriteriaItemForCandidatureDTO> previousCriteriaItemsForCandidature,
            CriteriaValue recruitmentCriteriaValue
    ) {
        var parentCriteria = recruitmentCriteriaValue.getCriteria();
        var criteriaItemForCandidature = getOrCreateCriteriaItemForCandidature(previousCriteriaItemsForCandidature, parentCriteria);
        criteriaItemForCandidature.addSelectedValuesCodesInRecruitmentItem(recruitmentCriteriaValue.getCode());
        if (parentCriteria.isThreshold()) {
            userCriteriaValues
                    .stream()
                    .map(UserCriteriaValue::getValue)
                    .filter(cv -> cv.getCriteria().equals(recruitmentCriteriaValue.getCriteria()))
                    .findFirst()
                    .map(CriteriaValue::getCode)
                    .ifPresent(criteriaItemForCandidature::addSelectedValuesCodesByUserItem);
        }
    }

    private CriteriaItemForCandidatureDTO getOrCreateCriteriaItemForCandidature(ArrayList<CriteriaItemForCandidatureDTO> listOfConditionCriteria, Criteria parentCriteria) {
        return listOfConditionCriteria.stream()
                .filter(cc -> cc.getCriteria().getCode().equals(parentCriteria.getCode()))
                .findFirst()
                .orElseGet(() -> {
                    var criteriaItemDTO = new CriteriaItemForCandidatureDTO()
                            .criteria(criteriaDTOBuilder.buildDTO(parentCriteria));
                    listOfConditionCriteria.add(criteriaItemDTO);
                    return criteriaItemDTO;
                });
    }

    private CandidatureInitializationDataContactDTO getCandidatureUserContact(UserProfile user) {
        var keycloakUser = keycloakService.getFrontOfficeUserProfile(user.userId()).orElseThrow();
        return new CandidatureInitializationDataContactDTO()
                .firstname(keycloakUser.getFirstName())
                .lastname(keycloakUser.getLastName())
                .phoneNumber(user.generalInformation().getPhoneNumber())
                .requiresLocation(user.generalInformation().getLocation() == null)
                .requiresCommunicationPreferences(BooleanUtils.isNotFalse(user.getJobOfferOptOut()));
    }

    @Transactional
    @RolesAllowed(Role.CANDIDATE)
    public void publishForAuthenticatedUser(Long recruitmentId) {
        changeStateTo(createOrRetrieveCandidatureForAuthenticatedUser(recruitmentId), CandidatureState.VALIDATED);
    }


}
