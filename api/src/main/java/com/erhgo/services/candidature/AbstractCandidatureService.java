package com.erhgo.services.candidature;

import com.erhgo.openapi.dto.SourcingCandidatureItemDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class AbstractCandidatureService {

    Page<SourcingCandidatureItemDTO> filterCandidateQuery(Page<SourcingCandidatureItemDTO> page, String searchQuery) {
        if (StringUtils.isBlank(searchQuery)) {
            return page;
        }
        var filteredList = page.stream().filter(c ->
                StringUtils.containsIgnoreCase(Stream.of(c.getAnonymousCode(), c.getFirstName(), c.getLastName(), c.getEmail())
                        .filter(Predicate.not(StringUtils::isBlank))
                        .map(com.erhgo.utils.StringUtils::normalizeLowerCase)
                        .collect(Collectors.joining(" ")), com.erhgo.utils.StringUtils.normalizeLowerCase(searchQuery))
        ).toList();
        return new PageImpl<>(filteredList, Pageable.unpaged(), filteredList.size());
    }

}
