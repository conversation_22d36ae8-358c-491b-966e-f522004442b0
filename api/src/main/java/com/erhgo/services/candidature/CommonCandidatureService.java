package com.erhgo.services.candidature;

import com.erhgo.domain.candidature.job.AbstractCandidature;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.userprofile.GeneralInformation;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.openapi.dto.CandidatureDTO;
import com.erhgo.openapi.dto.SimpleCandidatureDTO;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.security.AuthorizeExpression;
import com.erhgo.security.Role;
import com.erhgo.services.dtobuilder.CandidaturesDTOBuilder;
import com.erhgo.services.dtobuilder.CommonCandidatureDTOBuilder;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.utils.DateTimeUtils;
import com.erhgo.utils.StringUtils;
import com.opencsv.CSVWriter;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.OutputStreamWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.opencsv.ICSVWriter.*;
import static org.apache.commons.lang3.StringUtils.trimToEmpty;

@Service
@RequiredArgsConstructor
@Slf4j
public class CommonCandidatureService {

    private static final String[] HEADERS = new String[]{"Nom", "Prénom", "Email", "Téléphone", "Ville", "Code postal", "État", "Est anonyme ?", "Date de candidature", "Date de dernière connexion", "Lien profil (BO)", "Lien candidature (sourcing)", "Expéditeurs blacklistés", "Type", "Organisation", "Recrutement", "État recrutement"};
    private static final String UNKNOWN = "<Inconnu>";

    private final AbstractCandidatureRepository commonCandidatureRepository;
    private final RecruitmentCandidatureRepository recruitmentCandidatureRepository;
    private final SpontaneousCandidatureRepository spontaneousCandidatureRepository;
    private final CommonCandidatureDTOBuilder candidatureDTOBuilder;
    private final KeycloakService keycloakService;
    @Value("${keycloak-realms.back_office_base_url}${application.profile-relative-url}")
    private String profilePath;
    @Value("${keycloak-realms.sourcing_base_url}${application.sourcing-candidature-relative-url}")
    private String candidaturePath;

    @Transactional(readOnly = true)
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_READ)
    public List<CandidatureDTO> getUserCandidatures(String userId, boolean includesSpontaneous) {
        var candidatures = commonCandidatureRepository.findByUserProfileUserIdAndModifiedByUserIsTrueAndArchivedIsFalse(userId);
        // TODO: includesSpontaneous (uglyly handled here) is useful for backwards compatibility vs. legacy mobile app - may be removed "in the futur"
        return candidatures.stream()
                .map(candidatureDTOBuilder::buildUserCandidatureDTO)
                .filter(c -> c.getRecruitment() != null || includesSpontaneous)
                .toList();
    }

    @Transactional(readOnly = true)
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_READ)
    public List<SimpleCandidatureDTO> getSimpleUserCandidatures(String userId) {
        var candidatures = commonCandidatureRepository.findByUserProfileUserIdAndModifiedByUserIsTrueAndArchivedIsFalse(userId);
        return candidatures.stream()
                .map(candidatureDTOBuilder::buildSimpleUserCandidatureDTO)
                .toList();
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public void writeCandidaturesCsv(OutputStreamWriter writer, String organizationCode, String recruitmentCode) {

        List<? extends AbstractCandidature> candidatures;
        if (recruitmentCode != null) {
            candidatures = recruitmentCandidatureRepository.findByRecruitmentCodeAndArchivedIsFalse(recruitmentCode);
        } else if (organizationCode != null) {
            candidatures = spontaneousCandidatureRepository.findByRecruiterCodeAndArchivedIsFalse(organizationCode);
        } else {
            candidatures = commonCandidatureRepository.findByArchivedIsFalse();
        }

        var candidaturesStream = candidatures.stream().map(this::toCSVLine);

        try (var csvWriter = new CSVWriter(
                writer,
                StringUtils.CSV_FIELD_SEPARATOR,
                DEFAULT_QUOTE_CHARACTER,
                DEFAULT_ESCAPE_CHARACTER,
                DEFAULT_LINE_END)) {
            csvWriter.writeNext(HEADERS);
            csvWriter.writeAll(candidaturesStream::iterator);
        } catch (IOException e) {
            log.error("Unable to generate CSV", e);
        }
    }

    private String[] toCSVLine(AbstractCandidature candidature) {
        var result = new ArrayList<String>();

        keycloakService.getFrontOfficeUserProfile(candidature.getUserId())
                .ifPresentOrElse(
                        user -> result.addAll(List.of(trimToEmpty(user.getLastName()), trimToEmpty(user.getFirstName()), trimToEmpty(user.getEmail()))),
                        () -> result.addAll(List.of(UNKNOWN, UNKNOWN, UNKNOWN))
                );

        var infoOpt = Optional.ofNullable(candidature.getUserProfile().generalInformation());
        infoOpt.map(GeneralInformation::getPhoneNumber)
                .ifPresentOrElse(
                        result::add,
                        () -> result.add(UNKNOWN)
                );

        infoOpt.map(GeneralInformation::getLocation)
                .ifPresentOrElse(
                        l -> result.addAll(List.of(trimToEmpty(l.getCity()), trimToEmpty(l.getPostcode()))),
                        () -> result.addAll(List.of(UNKNOWN, UNKNOWN))
                );

        result.addAll(List.of(
                getCandidatureLabel(candidature),
                candidature.isAnonymous() ? "Oui" : "Non",
                DateTimeUtils.formatDate(candidature.getSubmissionDate()),
                DateTimeUtils.formatDate(candidature.getUserProfile().lastConnectionDate()),
                profilePath.formatted(candidature.getUserId()),
                candidaturePath.formatted(candidature.getId()),
                trimToEmpty(candidature.getUserProfile().sendersBlacklisted()),
                candidature.isRecruitmentCandidature() ? "Recrutement (%s)".formatted(candidature.isModifiedByUser() ? "sur offre" : "proposée") : "Spontanée",
                candidature.getRecruiterTitle(),
                getRecruitmentLabel(candidature),
                getRecruitmentStateLabel(candidature)
        ));

        return result.toArray(String[]::new);
    }

    private String getRecruitmentLabel(AbstractCandidature candidature) {
        if (candidature.isRecruitmentCandidature()) {
            var recruitment = ((RecruitmentCandidature) candidature).getRecruitment();
            return "%s (%s)".formatted(recruitment.getJobTitle(), Optional.ofNullable(recruitment.getLocation()).map(Location::getCity).orElse(""));
        }
        return "-";
    }

    private String getCandidatureLabel(AbstractCandidature candidature) {
        if (candidature.getGlobalCandidatureState() == GlobalCandidatureState.MISSING_PREREQUISITE) {
            return "Candidature non valide";
        }
        if (candidature.getGlobalCandidatureState() == GlobalCandidatureState.NOT_FINALIZED) {
            return "Candidature non soumise";
        }
        var state = CandidaturesDTOBuilder.getSourcingStateFromGlobalState(candidature.getGlobalCandidatureState());
        if (state == null) {
            return UNKNOWN;
        }
        return switch (state) {
            case TO_CONTACT -> "À contacter";
            case CONTACTED -> "Contactée";
            case NEW -> "Nouvelle";
            case FAVORITE -> "Favorite";
            case DISMISS -> "Refusée";
        };
    }

    private String getRecruitmentStateLabel(AbstractCandidature candidature) {
        var state = candidature.isRecruitmentCandidature() ? ((RecruitmentCandidature) candidature).getRecruitment().getState() : null;
        if (state == null) return "-";
        return switch (state) {
            case DRAFT -> "Brouillon";
            case PUBLISHED -> "Publié";
            case UNPUBLISHED, SELECTION -> "Suspendu";
            case CLOSED -> "Terminé";
        };
    }


}
