package com.erhgo.services.dtobuilder;

import com.erhgo.domain.sourcing.SourcingSubscription;
import com.erhgo.openapi.dto.SourcingSubscriptionDTO;
import com.erhgo.openapi.dto.SourcingSubscriptionTypeDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class SourcingSubscriptionDTOBuilder {
    public SourcingSubscriptionDTO buildSourcingSubscriptionDTO(SourcingSubscription subscription) {
        return new SourcingSubscriptionDTO()
                .subscriptionType(getSubscriptionType(subscription))
                .expirationDate(subscription == null ? null : subscription.getExpirationDate())
                .organizationCode(subscription == null ? null : subscription.getOrganizationCode())
                .organizationId(subscription == null ? null : subscription.getOrganizationId());
    }

    private SourcingSubscriptionTypeDTO getSubscriptionType(SourcingSubscription subscription) {
        if (subscription == null) {
            return SourcingSubscriptionTypeDTO.NONE;
        }
        return SourcingSubscriptionTypeDTO.fromValue(subscription.getSubscriptionType().name());
    }
}
