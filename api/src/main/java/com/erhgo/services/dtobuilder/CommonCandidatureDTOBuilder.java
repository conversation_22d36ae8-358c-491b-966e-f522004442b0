package com.erhgo.services.dtobuilder;

import com.erhgo.domain.candidature.job.AbstractCandidature;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.openapi.dto.CandidatureDTO;
import com.erhgo.openapi.dto.SimpleCandidatureDTO;
import com.erhgo.openapi.dto.TypeContractDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CommonCandidatureDTOBuilder {
    private final RecruitmentDTOBuilder recruitmentDTOBuilder;
    private final OrganizationDTOBuilder organizationDTOBuilder;

    public CandidatureDTO buildUserCandidatureDTO(AbstractCandidature candidature) {
        var dto = new CandidatureDTO()
                .id(candidature.getId())
                .submissionDate(candidature.getSubmissionDate())
                .state(JobCandidatureDTOBuilder.getCandidatureState(candidature))
                .refusalDate(candidature.getRefusalDate());
        if (candidature.isRecruitmentCandidature()) {
            var recruitment = ((RecruitmentCandidature) candidature).getRecruitment();
            dto.recruitment(recruitmentDTOBuilder.buildDetail(recruitment));
        } else {
            dto.organization(organizationDTOBuilder.buildOrganizationSummary(candidature.getRecruiter()));
        }

        return dto;
    }

    public SimpleCandidatureDTO buildSimpleUserCandidatureDTO(AbstractCandidature candidature) {
        var dto = new SimpleCandidatureDTO()
                .id(candidature.getId())
                .submissionDate(candidature.getSubmissionDate())
                .state(JobCandidatureDTOBuilder.getCandidatureState(candidature))
                .refusalDate(candidature.getRefusalDate());
        if (candidature.isRecruitmentCandidature()) {
            var recruitment = ((RecruitmentCandidature) candidature).getRecruitment();
            dto.recruitmentCode(recruitment.getCode());
            dto.jobTitle(recruitment.getJobTitle());
            dto.city(recruitment.getCity());
            dto.baseSalary(recruitment.getHideSalary() != null && recruitment.getHideSalary() ? null : recruitment.getBaseSalary());
            dto.maxSalary(recruitment.getHideSalary() != null && recruitment.getHideSalary() ? null : recruitment.getMaxSalary());
            dto.typeContract(recruitment.getTypeContract() == null ? null : TypeContractDTO.valueOf(recruitment.getTypeContract().name()));
            dto.organizationName(recruitment.getOrganizationName());
        } else {
            var organization = candidature.getRecruiter();
            dto.organizationName(organization.getTitle());
        }

        return dto;
    }
}
