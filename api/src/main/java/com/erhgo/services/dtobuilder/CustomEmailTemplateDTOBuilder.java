package com.erhgo.services.dtobuilder;

import com.erhgo.domain.referential.CustomEmailTemplate;
import com.erhgo.openapi.dto.CustomEmailTemplateDTO;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CustomEmailTemplateDTOBuilder {
    private final ModelMapper modelMapper;

    public CustomEmailTemplateDTO buildDTO(CustomEmailTemplate customEmailTemplate) {
        return customEmailTemplate == null ? null : modelMapper.map(customEmailTemplate, CustomEmailTemplateDTO.class);
    }

    public CustomEmailTemplate getCustomEmailTemplate(CustomEmailTemplateDTO customEmailTemplateDTO) {
        return CustomEmailTemplate.builder()
                .subject(customEmailTemplateDTO.getSubject())
                .content(customEmailTemplateDTO.getContent())
                .emailFrom(customEmailTemplateDTO.getEmailFrom())
                .authorAlias(customEmailTemplateDTO.getAuthorAlias()).build();
    }
}
