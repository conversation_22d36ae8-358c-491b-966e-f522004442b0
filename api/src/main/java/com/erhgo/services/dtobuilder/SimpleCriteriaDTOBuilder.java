package com.erhgo.services.dtobuilder;

import com.erhgo.domain.criteria.Criteria;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.dto.CriteriaValueForUserDTO;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class SimpleCriteriaDTOBuilder {
    private final ModelMapper modelMapper;

    public SimpleCriteriaDTO buildDTO(Map.Entry<Criteria, List<CriteriaValueForUserDTO>> tuple) {
        return modelMapper.map(tuple.getKey(), SimpleCriteriaDTO.class).criteriaValues(tuple.getValue().stream().map(this::buildSimpleCriteriaSummaryDTO).toList());
    }

    public SimpleUserCriteriaValueDTO buildSimpleCriteriaSummaryDTO(CriteriaValueForUserDTO criteria) {
        return new SimpleUserCriteriaValueDTO()
                .selected(criteria.getSelected())
                .code(criteria.getCriteriaValue().getCode());
    }
}
