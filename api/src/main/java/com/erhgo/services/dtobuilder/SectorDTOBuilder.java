package com.erhgo.services.dtobuilder;

import com.erhgo.domain.sector.Sector;
import com.erhgo.openapi.dto.SectorDTO;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Set;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SectorDTOBuilder {

    public static final String CODE_FOR_CUSTOM_SECTOR = "C-XX";
    public static final String ABBREVIATION_FOR_CUSTOM_SECTOR = "Autres";

    public static SectorDTO buildSectorDTO(Sector s) {
        return new SectorDTO()
                .code(s.getCode())
                .id(s.getUuid())
                .abbreviation(s.getAbbreviation())
                .label(s.getLabel());
    }


    public static SectorDTO buildCustomSectorDTO(Set<String> customSectors) {
        return new SectorDTO()
                .code(CODE_FOR_CUSTOM_SECTOR)
                .abbreviation(ABBREVIATION_FOR_CUSTOM_SECTOR)
                .label(String.join(", ", customSectors));
    }
}
