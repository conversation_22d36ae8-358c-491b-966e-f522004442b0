package com.erhgo.services.dtobuilder;

import com.erhgo.openapi.dto.UserSummaryDTO;
import com.erhgo.services.keycloak.UserRepresentation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserSummaryDTOBuilder {

    public UserSummaryDTO buildUserSummary(UserRepresentation userProfile) {
        return new UserSummaryDTO()
                .firstName(userProfile.getFirstName())
                .lastName(userProfile.getLastName())
                .id(userProfile.getId())
                .email(userProfile.getEmail())
                .enabled(userProfile.getEnabled());
    }
}
