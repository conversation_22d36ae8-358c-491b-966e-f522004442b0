package com.erhgo.services.dtobuilder;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.enums.CriteriaQuestionType;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.userprofile.GeneralInformation;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.criteria.UserCriteriaValue;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.*;
import com.erhgo.repositories.dto.CountForUserDTO;
import com.erhgo.repositories.dto.UserProfileProgress;
import com.erhgo.services.RecruitmentService;
import com.erhgo.services.SecurityService;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.google.common.collect.Lists;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;

import static com.google.common.collect.Sets.intersection;
import static org.apache.commons.lang3.StringUtils.isBlank;

@Service
@RequiredArgsConstructor
public class UserProfileDTOBuilder {
    private static final int MAX_NUMBER_OF_DAYS_FOR_ACTIVE_CANDIDATURE = 30;
    private static final int MAX_NUMBER_OF_DAYS_FOR_CONNECTION_DATE = 7;

    private final RecruitmentCandidatureRepository recruitmentCandidatureRepository;
    private final AbstractCandidatureRepository abstractCandidatureRepository;
    private final RecruiterRepository recruiterRepository;
    private final UserExperienceRepository userExperienceRepository;
    private final UserExperienceDTOBuilder userExperienceDTOBuilder;
    private final GeneralInformationDTOBuilder generalInformationDTOBuilder;
    private final ErhgoOccupationDataDTOBuilder erhgoOccupationBuilder;
    private final NotificationRepository notificationRepository;

    private final KeycloakService keycloakService;
    private final SecurityService securityService;
    private final ApplicationContext applicationContext;

    private Map<OptionalMenuDTO, Predicate<UserProfile>> optInMenus;

    @PostConstruct
    public void initOptInMenusMap() {
        optInMenus = Map.of(
                OptionalMenuDTO.JOB_CANDIDATURES, up -> !abstractCandidatureRepository.findByUserProfileUserIdAndModifiedByUserIsTrueAndArchivedIsFalse(up.userId()).isEmpty(),
                OptionalMenuDTO.MATCHING_RECRUITMENTS, up -> applicationContext.getBean(RecruitmentService.class).getRecruitmentsCount(null).getValue() > 0L,
                OptionalMenuDTO.JOBS_MATCHING, up -> !securityService.hasAuthenticatedUserPrivateUserChannel()
        );
    }


    public UserProfileSummaryDTO buildSummary(UserProfile userProfile) {
        var experiences = userExperienceRepository.findByUserProfileUserId(userProfile.userId());

        var generalInformation = userProfile.generalInformation();

        var userRepresentation = keycloakService.getFrontOfficeUserProfile(userProfile.userId());

        var userProfileSummary = new UserProfileSummaryDTO()
                .firstName(userRepresentation.map(UserRepresentation::getFirstName).orElse(""))
                .lastName(userRepresentation.map(UserRepresentation::getLastName).orElse(""))
                .email(userRepresentation.map(UserRepresentation::getEmail).orElse(""))
                .lastCandidatures(buildLastCandidatureDTO(userProfile))
                .behaviors(userProfile.behaviors().stream().map(BehaviorDTOBuilder::buildDTO).sorted(Comparator.comparing(BehaviorDTO::getTitle)).toList())
                .isSourceInitialized(userProfile.isSourceInitialized())
                .isExportable(userProfile.isExportable())
                .lastExperiences(experiences
                        .stream()
                        .sorted(Collections.reverseOrder())
                        .limit(5)
                        .map(UserExperienceDTOBuilder::buildExperienceSummary)
                        .toList())
                .criteria(
                        userProfile.getUserCriteriaValues() == null ? null : userProfile
                                .getUserCriteriaValues()
                                .stream()
                                .filter(c -> c.getValue().getCriteria().getQuestionType() != CriteriaQuestionType.THRESHOLD)
                                .sorted(Comparator
                                        .comparing((Function<UserCriteriaValue, Integer>) c -> c.getValue().getCriteria().getCriteriaIndex())
                                        .thenComparing(c -> c.getValue().getValueIndex()))
                                .map(c -> (UserCriteriaValueDTO) new UserCriteriaValueDTO()
                                        .selected(c.isSelected())
                                        .titleForQuestion(c.getValue().getTitleForQuestion())
                                        .titleStandalone(c.getValue().getTitleStandalone())
                                        .icon(c.getValue().getIcon())
                                        .code(c.getValue().getCode())
                                        .valueIndex(c.getValue().getValueIndex()))
                                .toList()
                )
                .incompleteInformations(userRepresentation.map(u -> buildIncompleteInformations(userProfile, u)).orElse(null))
                .unreadNotificationsCount(notificationRepository.getUnreadNotificationsCount(List.of(userProfile.uuid())).stream().findFirst().map(CountForUserDTO::getCountForUser).map(Long::intValue).orElse(0))
                .blacklistedOccupations(userProfile.getBlacklistedOccupations().stream().map(erhgoOccupationBuilder::buildOccupationMininumInfo).toList())
                .softSkillsStatus(computeSoftSkillStatus(userProfile));

        var generalInformationOpt = Optional.ofNullable(generalInformation);

        userProfileSummary
                .phoneNumber(generalInformationOpt.map(GeneralInformation::getPhoneNumber).orElse(null))
                .location(generalInformationOpt.map(GeneralInformation::getLocation).map(GeneralInformationDTOBuilder::buildLocationDTO).orElse(null))
                .birthDate(generalInformationOpt.map(GeneralInformation::getBirthDate).orElse(null))
                .contactTime(generalInformationOpt.map(GeneralInformation::getContactTime).map(Enum::name).map(ContactTimeDTO::fromValue).orElse(null))
                .salary(generalInformationOpt.map(GeneralInformation::getSalary).orElse(null))
                .situation(generalInformationOpt.map(GeneralInformation::getSituation).map(Enum::name).map(SituationDTO::fromValue).orElse(null))
                .delayInMonth(generalInformationOpt.map(GeneralInformation::getDelayInMonth).orElse(null))
                .includesMenus(optInMenus.entrySet().stream()
                        .filter(kv -> kv.getValue().test(userProfile))
                        .map(Map.Entry::getKey)
                        .toList());

        return userProfileSummary;
    }

    private UserProfileSummaryDTO.SoftSkillsStatusEnum computeSoftSkillStatus(UserProfile userProfile) {
        var status = userProfile.trimojiStatus();
        if (status != null && StringUtils.isNotBlank(status.trimojiPdfUrl())) {
            return UserProfileSummaryDTO.SoftSkillsStatusEnum.AVAILABLE;
        }
        if (status == null || status.startedDate() == null) {
            return UserProfileSummaryDTO.SoftSkillsStatusEnum.NOT_STARTED;
        }
        if (status.endedDate() == null) {
            return UserProfileSummaryDTO.SoftSkillsStatusEnum.NOT_FINISHED;
        }
        return UserProfileSummaryDTO.SoftSkillsStatusEnum.WAITING;
    }

    private List<CandidatureForProfileDTO> buildLastCandidatureDTO(UserProfile userProfile) {
        return recruitmentCandidatureRepository.findTop5ByUserProfileAndArchivedIsFalseOrderByCreatedDateDesc(userProfile)
                .stream()
                .map(JobCandidatureDTOBuilder::buildForProfile)
                .toList();
    }


    public List<IncompleteInformationDTO> buildIncompleteInformations(UserProfile userProfile, UserRepresentation userRepresentation) {
        var candidatures = recruitmentCandidatureRepository.findByUserProfileUserId(userProfile.userId());
        var lastCandidature = candidatures.stream().map(RecruitmentCandidature::getSubmissionDate)
                .filter(Objects::nonNull)
                .max(OffsetDateTime::compareTo)
                .orElse(null);
        var generalInformation = Optional.ofNullable(userProfile.generalInformation());
        Map<IncompleteInformationDTO, Predicate<UserProfile>> incompleteInformationMap = Map.of(
                IncompleteInformationDTO.PHONE, up -> isBlank(up.getPhoneNumber()),
                IncompleteInformationDTO.SITUATION, up -> false,
                IncompleteInformationDTO.SALARY, up -> generalInformation.map(GeneralInformation::getSalary).isEmpty(),
                IncompleteInformationDTO.LOCATION, up -> generalInformation.map(GeneralInformation::getLocation).isEmpty(),
                IncompleteInformationDTO.CRITERIA, up -> up.getUserCriteriaValues() == null || up.getUserCriteriaValues().isEmpty(),
                IncompleteInformationDTO.BEHAVIOR, up -> up.getBehaviorsKeys() == null || up.getBehaviorsKeys().isEmpty(),
                IncompleteInformationDTO.EXPERIENCE, up -> up.experiences() == null || up.experiences().isEmpty(),
                IncompleteInformationDTO.LAST_CONNECTION_DATE, up -> up.lastConnectionDate() == null || up.lastConnectionDate().isBefore(LocalDateTime.now().minusDays(MAX_NUMBER_OF_DAYS_FOR_CONNECTION_DATE)),
                IncompleteInformationDTO.INACTIVE_CANDIDATURE, up -> lastCandidature == null || lastCandidature.isBefore(OffsetDateTime.now().minusDays(MAX_NUMBER_OF_DAYS_FOR_ACTIVE_CANDIDATURE)),
                IncompleteInformationDTO.NAME, up -> StringUtils.isBlank(userRepresentation.getLastName()) || StringUtils.isBlank(userRepresentation.getFirstName())
        );
        return incompleteInformationMap
                .keySet()
                .stream()
                .filter(k -> incompleteInformationMap.get(k).test(userProfile))
                .sorted()
                .toList();
    }

    public UserProfileDetailWithCapacitiesDTO buildDetailedWithCapacities(UserProfile userProfile, UserRepresentation userRepresentation) {
        return new UserProfileDetailWithCapacitiesDTO()
                .id(userProfile.userId())
                .generalInformation(generalInformationDTOBuilder.buildGeneralInformationDTO(userProfile.generalInformation(), userRepresentation))
                .experiences(userExperienceDTOBuilder.getExperiencesWithCapacities(userProfile));
    }

    public static UserProfileProgressDTO buildUserProfileProgress(UserProfileProgress progressDto) {
        return new UserProfileProgressDTO()
                .userRegistrationState(buildUserRegistrationState(progressDto.getUserProfile()))
                .capacitiesCount(progressDto.getCapacitiesCount())
                .experiencesCount(progressDto.getExperiencesCount())
                .hasBehaviors(progressDto.isHasBehaviors())
                .masteryLevel(progressDto.getMasteryLevel())
                .candidaturesCount(progressDto.getCandidaturesCount());
    }

    public static UserRegistrationStateDTO buildUserRegistrationState(UserProfile userProfile) {
        var generalInformation = userProfile.generalInformation();
        var userRegistrationState = userProfile.userRegistrationState();
        var dto = new UserRegistrationStateDTO()
                .userRegistrationStateStep(UserRegistrationStateStepDTO.fromValue(userProfile.registrationStep().toString()));
        if (generalInformation != null) {
            if (generalInformation.getLocation() != null) {
                dto.setCity(generalInformation.getLocation().getCity());
            }
            if (generalInformation.getSituation() != null) {
                dto.setSituation(SituationDTO.fromValue(generalInformation.getSituation().name()));
            }
            dto.setSalary(generalInformation.getSalary());

        }
        if (userRegistrationState != null) {
            dto.jobTitle(userRegistrationState.getJobTitle());
            if (userRegistrationState.getSelectedOccupation() != null) {
                var selectedOccupation = userRegistrationState.getSelectedOccupation();
                dto.setSelectedOccupation(selectedOccupation.getTitle());
                dto.setSelectedOccupationId(selectedOccupation.getId());

            }
        }
        return dto;
    }

    public UserContactInfoDTO buildUserContactDtoWithUserProgress(UserProfileProgress userProfileProgress, Set<String> roles) {
        var dto = new UserContactInfoDTO();
        return feedUserContactInfoDTO(userProfileProgress, roles, dto);
    }

    private <A extends UserContactInfoDTO> A feedUserContactInfoDTO(UserProfileProgress userProfileProgress, Set<String> roles, A dto) {
        var userRepresentation = keycloakService.getFrontOfficeUserProfile(userProfileProgress.getUserId());
        if (userRepresentation.isPresent()) {
            var groupsOfUserAndOrganization = Lists.newArrayList(intersection(roles, userProfileProgress.getUserProfile().channels()));
            userRepresentation.get().setGroups(groupsOfUserAndOrganization);
        }
        generalInformationDTOBuilder.feedUserContactInfo(
                dto,
                userProfileProgress.getUserProfile().generalInformation(),
                userRepresentation.orElse(null)
        );
        dto.setUserProfileProgress(UserProfileDTOBuilder.buildUserProfileProgress(userProfileProgress));

        return dto;
    }

    public AlreadyAppliedUserDTO buildAlreadyAppliedDTO(UserProfile userProfile, Set<String> targetRoles, Optional<UserRepresentation> keycloakUser, Long candidatureId, boolean refusedCandidature, CandidatureStateDTO state) {
        var channels = intersection(targetRoles, userProfile.channels());
        return new AlreadyAppliedUserDTO()
                .channels(getChannelLabels(channels))
                .firstName(keycloakUser.map(UserRepresentation::getFirstName).orElse(""))
                .lastName(keycloakUser.map(UserRepresentation::getLastName).orElse(""))
                .location(Optional.ofNullable(userProfile.generalInformation()).map(GeneralInformation::getLocation).map(GeneralInformationDTOBuilder::buildLocationDTO).orElse(null))
                .candidatureId(candidatureId)
                .refusedCandidature(refusedCandidature)
                .candidatureState(state)
                ;
    }

    private List<String> getChannelLabels(Collection<String> groups) {
        return recruiterRepository.findByCodeIn(groups)
                .stream()
                .map(Recruiter::getTitle)
                .toList();
    }

}
