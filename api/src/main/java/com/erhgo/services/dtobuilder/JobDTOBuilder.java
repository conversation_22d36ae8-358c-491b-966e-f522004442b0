package com.erhgo.services.dtobuilder;

import com.erhgo.domain.enums.JobEvaluationState;
import com.erhgo.domain.enums.TypeContractCategory;
import com.erhgo.domain.enums.TypeWorkingTime;
import com.erhgo.domain.job.ContextsForCategory;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.job.Mission;
import com.erhgo.domain.recruitment.RecruitmentProfile;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.EmployerRepository;
import com.erhgo.repositories.RecruiterRepository;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.HashSet;
import java.util.List;

import static com.erhgo.services.dtobuilder.DTOBuilderUtils.prettyList;
import static com.erhgo.services.dtobuilder.DTOBuilderUtils.prettyMappedList;

@Service
@RequiredArgsConstructor
public class JobDTOBuilder {

    private final ActivityDTOBuilder activityDTOBuilder;
    private final ContextDTOBuilder contextDTOBuilder;
    private final ErhgoOccupationDataDTOBuilder erhgoOccupationDataDTOBuilder;
    private final ModelMapper modelMapper;
    private final RecruiterRepository recruiterRepository;
    private final EmployerRepository employerRepository;
    private final CriteriaDTOBuilder criteriaDTOBuilder;

    public void updateJobWithCommand(Job job, SaveJobCommandDTO saveJobCommandDTO) {

        var locationDTO = saveJobCommandDTO.getLocation();

        job.setTitle(saveJobCommandDTO.getTitle());
        job.setService(saveJobCommandDTO.getService());
        job.setObservationDate(saveJobCommandDTO.getObservationDate());
        job.setDescription(saveJobCommandDTO.getDescription());
        job.setObservators(saveJobCommandDTO.getObservators() != null ? new HashSet<>(saveJobCommandDTO.getObservators()) : null);
        job.setLocation(Location.buildLocationFromDTO(locationDTO));
        job.setEmployer(employerRepository.findOneByCode(saveJobCommandDTO.getEmployerCode()));
        job.setRecruiter(recruiterRepository.findOneByCode(saveJobCommandDTO.getRecruiterCode()));
        if (job.isModifiable() || saveJobCommandDTO.getState() == JobEvaluationStateDTO.LISTED) {
            job.setState(JobEvaluationState.valueOf(saveJobCommandDTO.getState().toString()));
        }
    }

    public JobSummaryDTO buildSummary(Job job) {
        var summary = modelMapper.map(job, JobSummaryDTO.class);
        var recruiter = job.getRecruiter().getTitle();
        var employer = job.getEmployer() == null ? null : job.getEmployer().getTitle();
        var jobWorkingTimes = job
                .getWorkingTimes()
                .stream()
                .map(TypeWorkingTime::name)
                .map(WorkingTimeDTO::fromValue)
                .toList();

        var jobTypeContractCategories = job
                .getContractTypes()
                .stream()
                .map(TypeContractCategory::name)
                .map(TypeContractCategoryDTO::fromValue)
                .toList();

        summary.recruiterTitle(recruiter)
                .employerTitle(employer)
                .workingTimes(jobWorkingTimes)
                .typeContractCategories(jobTypeContractCategories);

        var erhgoOccupation = job.getErhgoOccupation();
        if (erhgoOccupation != null) {
            summary.setErhgoOccupationId(erhgoOccupation.getId());
        }
        return summary;
    }

    public JobSummaryWithRecruitmentProfileDTO buildSummaryWithRecruitmentProfile(Job job, List<RecruitmentProfile> recruitmentProfiles) {
        var jobWithProfiles = modelMapper.map(buildSummary(job), JobSummaryWithRecruitmentProfileDTO.class);
        jobWithProfiles.setRecruitmentProfiles(recruitmentProfiles.stream()
                .sorted(Comparator.comparing(RecruitmentProfile::getTitle))
                .map(RecruitmentProfileDTOBuilder::buildSummary)
                .toList());
        return jobWithProfiles;
    }

    public JobDetailDTO buildJobDetail(Job jobDetail, String createdBy, long recruitmentProfileCount, boolean isModifiable) {
        var location = jobDetail.getLocation();
        return new JobDetailDTO()
                .id(jobDetail.getId())
                .createdBy(createdBy)
                .title(jobDetail.getTitle())
                .description(jobDetail.getDescription())
                .recruiterCode(jobDetail.getRecruiterCode())
                .employerCode(jobDetail.getEmployerCode())
                .observationDate(jobDetail.getObservationDate())
                .observators(prettyList(jobDetail.getObservators()))
                .publicationDate(jobDetail.getPublicationDate())
                .recommendation(jobDetail.getRecommendation())
                .service(jobDetail.getService())
                .location(location == null ? null : location.buildDTO())
                .state(jobDetail.getState().name())
                .level(jobDetail.getMasteryLevel() != null
                        ? MasteryLevelDTO.valueOf(jobDetail.getMasteryLevel().name())
                        : null)
                .erhgoOccupation(jobDetail.getErhgoOccupation() != null
                        ? erhgoOccupationDataDTOBuilder.buildOccupationOTSummaryDTO(jobDetail.getErhgoOccupation())
                        : null)
                .modifiable(isModifiable)
                .behaviors(prettyMappedList(jobDetail.getBehaviors(), BehaviorDTOBuilder::buildDTO))
                .missions(prettyMappedList(jobDetail.getMissions(), this::toMissionDetail))
                .jobType(JobTypeDTO.valueOf(jobDetail.getJobType().name()))
                .recruitmentProfileCount(recruitmentProfileCount)
                .criteriaValues(jobDetail.getCriteriaValues().stream().map(criteriaDTOBuilder::buildCriteriaValueDTO).toList())
                .isPrivate(jobDetail.isPrivate())
                ;

    }

    private MissionDetailDTO toMissionDetail(Mission mission) {
        return new MissionDetailDTO()
                .activities(prettyMappedList(mission.getActivities(), activityDTOBuilder::buildActivityLabelWithCapacitiesDTO))
                .contextsForCategory(prettyMappedList(mission.getContextsForCategory(), this::toContextsForCategory))
                .id(mission.getId())
                .jobId(mission.getJob().getId())
                .title(mission.getTitle());
    }


    private ContextsForCategoryDetailDTO toContextsForCategory(ContextsForCategory contextsForCategory) {
        return new ContextsForCategoryDetailDTO()
                .category(ContextDTOBuilder.toCategoryDTO(contextsForCategory.getCategory()))
                .noContextForCategory(contextsForCategory.getNoContextForCategory())
                .contexts(
                        prettyMappedList(contextsForCategory.getContexts(), contextDTOBuilder::buildContextDTO));
    }

}
