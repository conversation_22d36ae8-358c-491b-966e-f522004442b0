package com.erhgo.services.dtobuilder;

import com.erhgo.domain.classifications.erhgooccupation.*;
import com.erhgo.domain.classifications.esco.EscoOccupation;
import com.erhgo.domain.classifications.esco.EscoSkill;
import com.erhgo.domain.classifications.rome.RomeOccupation;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@RequiredArgsConstructor
public class ErhgoOccupationDataDTOBuilder {
    private final ModelMapper modelMapper;
    private final KeycloakService keycloakService;
    private final EscoSkillDTOBuilder escoSkillDTOBuilder;
    private final ActivityDTOBuilder activityDTOBuilder;
    private final ContextDTOBuilder contextDTOBuilder;
    private final CriteriaDTOBuilder criteriaDTOBuilder;
    private final ErhgoClassificationDTOBuilder erhgoClassificationDTOBuilder;

    public static ErhgoOccupationSearchDTO buildErhgoSearchDTO(ErhgoOccupation occupation) {
        return new ErhgoOccupationSearchDTO()
                .code(occupation.getId().toString())
                .title(occupation.getTitle())
                .activities(occupation
                        .getActivities()
                        .stream()
                        .map(ActivityDTOBuilder::buildSummary)
                        .toList());
    }


    public ErhgoOccupationMinimumInfoDTO buildOccupationMininumInfo(ErhgoOccupation occupation) {
        return new ErhgoOccupationMinimumInfoDTO()
                .id(occupation.getId())
                .title(occupation.title)
                ;
    }

    public ErhgoOccupationDetailDTO buildOccupationDTO(ErhgoOccupation occupation) {
        var erhgoOccupationDetailDTO = modelMapper.map(occupation, ErhgoOccupationDetailDTO.class);
        erhgoOccupationDetailDTO.setIsTechnical(occupation.isTechnical());
        erhgoOccupationDetailDTO.setLastModifiedBy(occupation.getLastModifiedBy() != null ? keycloakService.getBackOfficeUserFullnameOrEmpty(occupation.getLastModifiedBy().getKeycloakId()) : null);
        erhgoOccupationDetailDTO.setUpdatedDate(DTOBuilderUtils.normalizeDate(occupation));
        erhgoOccupationDetailDTO.setSkills(occupation
                .getSkills()
                .stream()
                .sorted(Comparator.comparing(EscoSkill::getTitle, StringUtils.DIATRIC_AND_CASE_INSENSITIVE_COMPARATOR.INSTANCE))
                .map(escoSkillDTOBuilder::buildSkillDTO)
                .toList()
        );
        erhgoOccupationDetailDTO
                .activities(createActivitiesDTO(occupation.getOccupationActivities(), occupation.getSkills()))
                .contexts(createContextsDTO(occupation.getOccupationContexts(), occupation.getSkills()))
                .behaviors(createBehaviorsDTO(occupation.getOccupationBehaviors(), occupation.getSkills()))
                .alternativeLabels(occupation.getAlternativeLabels().stream().sorted(StringUtils.DIATRIC_AND_CASE_INSENSITIVE_COMPARATOR.INSTANCE).toList())
                .escoOccupations(buildEscoOccupationsDTOs(occupation.getEscoOccupations()))
                .behaviorsCategories(buildOccupationBehaviorsCategories(occupation))
                .erhgoOccupationState(ErhgoOccupationStateDTO.valueOf(occupation.getQualificationState().name()))
                .occupationCreationReason(Optional.ofNullable(occupation.getOccupationCreationReason()).map(o -> OccupationCreationReasonDTO.valueOf(o.name())).orElse(null))
                .criteriaValues(occupation.getCriteriaValues().stream().sorted(Comparator.comparing(CriteriaValue::getCode)).map(criteriaDTOBuilder::buildCriteriaValueDTO).toList())
                .erhgoClassifications(occupation.getErhgoClassifications().stream().sorted().map(erhgoClassificationDTOBuilder::buildErhgoClassificationDTO).toList())
                .romeOccupations(occupation.getRomeOccupations().stream().sorted().map(this::buildRomeSummaryDTO).toList())
                .accessibleRomeOccupations(occupation.getAccessibleRomeOccupations().stream().sorted().map(this::buildRomeSummaryDTO).toList())
                .accessibleFromRomeOccupations(occupation.getAccessibleFromRomeOccupations().stream().sorted().map(this::buildRomeSummaryDTO).toList())
                .isVisibleForOrientation(true)
        ;

        return erhgoOccupationDetailDTO;
    }


    public static ErhgoOccupationsForLabelDTO buildOccupationForLabelDTO(ErhgoOccupation occupation) {
        return new ErhgoOccupationsForLabelDTO()
                .title(occupation.getTitle())
                .id(occupation.getId())
                .alternativeLabels(occupation.getAlternativeLabels().stream().toList());
    }

    public List<ErhgoOccupationsForLabelDTO> buildErhgoOccupationsDTO(List<ErhgoOccupation> occupationsLabel) {
        return occupationsLabel.stream().map(ErhgoOccupationDataDTOBuilder::buildOccupationForLabelDTO).toList();
    }

    public ErhgoOccupationSumUpDTO buildOccupationSumUpDTO(ErhgoOccupation occupation) {
        return new ErhgoOccupationSumUpDTO()
                .id(occupation.getId())
                .description(occupation.getDescription())
                .title(occupation.getTitle())
                .otherTitles(new ArrayList<>(occupation.getAlternativeLabels()))
                .attitude(occupation.getBehaviorsDescription())
                .romeCodes(occupation.getRomeOccupations().stream()
                        .map(RomeOccupation::getCode)
                        .toList())
                ;
    }

    private List<EscoOccupationWithIscoOccaptionSummaryDTO> buildEscoOccupationsDTOs(Set<EscoOccupation> escoOccupations) {
        return escoOccupations.stream().sorted(Comparator.comparing(EscoOccupation::getTitle)).map(this::buildEscoWithIscoDTO).toList();
    }

    private EscoOccupationWithIscoOccaptionSummaryDTO buildEscoWithIscoDTO(EscoOccupation m) {
        return new EscoOccupationWithIscoOccaptionSummaryDTO()
                .title(m.getTitle())
                .uri(m.getUri())
                .isco(m.getIscoOccupation() != null ? modelMapper.map(m.getIscoOccupation(), IscoSummaryDTO.class) : null);
    }

    private List<OccupationBehaviorDTO> createBehaviorsDTO(Set<OccupationBehavior> behaviors, Set<EscoSkill> skills) {
        Comparator<OccupationBehaviorDTO> comparator = Comparator.comparing(o -> o.getBehavior().getTitle());
        return behaviors.stream().map(c -> createBehaviorDTO(c, skills)).sorted(comparator).toList();
    }

    private OccupationBehaviorDTO createBehaviorDTO(OccupationBehavior occupationBehavior, Set<EscoSkill> skills) {
        var dto = new OccupationBehaviorDTO()
                .behavior(BehaviorDTOBuilder.buildDTO(occupationBehavior.getBehavior()))
                .source(OccupationQualificationSourceDTO.fromValue(occupationBehavior.getSource().name()))
                .updatedDate(DTOBuilderUtils.normalizeDate(occupationBehavior))
                .lastModifiedBy(occupationBehavior.getLastModifiedBy() != null ? keycloakService.getBackOfficeUserFullnameOrEmpty(occupationBehavior.getLastModifiedBy().getKeycloakId()) : null);
        if (occupationBehavior.getSource() == OccupationQualificationSource.SKILL) {
            dto.skills(occupationBehavior.filterSkillsWithEntity(skills).stream()
                    .map(this::buildSkillSummary)
                    .sorted(Comparator.comparing(SkillSummaryDTO::getTitle))
                    .toList());
        }
        return (OccupationBehaviorDTO) dto;
    }

    private List<OccupationContextDTO> createContextsDTO(Set<OccupationContext> contexts, Set<EscoSkill> skills) {
        Comparator<OccupationContextDTO> comparator = Comparator.comparing(o -> o.getContext().getTitle());
        return contexts.stream().map(c -> createContextDTO(c, skills)).sorted(comparator).toList();
    }

    private OccupationContextDTO createContextDTO(OccupationContext occupationContext, Set<EscoSkill> skills) {
        var dto = new OccupationContextDTO()
                .context(contextDTOBuilder.buildContextDTO(occupationContext.getContext()))
                .state(MandatoryStateDTO.fromValue(occupationContext.getState().name()))
                .source(OccupationQualificationSourceDTO.fromValue(occupationContext.getSource().name()))
                .updatedDate(DTOBuilderUtils.normalizeDate(occupationContext))
                .lastModifiedBy(occupationContext.getLastModifiedBy() != null ? keycloakService.getBackOfficeUserFullnameOrEmpty(occupationContext.getLastModifiedBy().getKeycloakId()) : null);
        if (occupationContext.getSource() == OccupationQualificationSource.SKILL) {
            dto.skills(occupationContext.filterSkillsWithEntity(skills).stream()
                    .map(this::buildSkillSummary)
                    .sorted(Comparator.comparing(SkillSummaryDTO::getTitle))
                    .toList());
        }
        return dto;
    }

    private List<OccupationActivityDTO> createActivitiesDTO(Set<OccupationActivity> activities, Set<EscoSkill> skills) {
        Comparator<OccupationActivityDTO> comparator = Comparator.comparing(o -> o.getActivity().getTitle());
        return activities.stream().map(a -> createActivityDTO(a, skills)).sorted(comparator).toList();
    }

    public OccupationActivityDTO createActivityDTO(OccupationActivity occupationActivity) {
        return createActivityDTO(occupationActivity, Collections.emptySet());
    }

    private OccupationActivityDTO createActivityDTO(OccupationActivity occupationActivity, Set<EscoSkill> skills) {
        var dto = new OccupationActivityDTO()
                .activity(activityDTOBuilder.buildActivityLabelWithCapacitiesDTO(occupationActivity.getActivity()))
                .state(MandatoryStateDTO.fromValue(occupationActivity.getState().name()))
                .source(OccupationQualificationSourceDTO.fromValue(occupationActivity.getSource().name()))
                .updatedDate(DTOBuilderUtils.normalizeDate(occupationActivity))
                .lastModifiedBy(occupationActivity.getLastModifiedBy() != null ? keycloakService.getBackOfficeUserFullnameOrEmpty(occupationActivity.getLastModifiedBy().getKeycloakId()) : null);
        if (occupationActivity.getSource() == OccupationQualificationSource.SKILL) {
            dto.skills(occupationActivity.filterSkillsWithEntity(skills).stream()
                    .map(this::buildSkillSummary)
                    .sorted(Comparator.comparing(SkillSummaryDTO::getTitle))
                    .toList());
        }
        return dto;
    }

    private SkillSummaryDTO buildSkillSummary(EscoSkill escoSkill) {
        return modelMapper.map(escoSkill, SkillSummaryDTO.class);
    }

    public RomeSummaryDTO buildRomeSummaryDTO(RomeOccupation romeOccupation) {
        return romeOccupation == null ? null : modelMapper.map(romeOccupation, RomeSummaryDTO.class);
    }

    public ErhgoOccupationSummaryDTO buildOccupationSummaryDTO(ErhgoOccupationRepository.SearchErhgoOccupationDTO searchOccupationDTO) {
        var searchOccupation = buildOccupationSummaryDTO(searchOccupationDTO.getOccupation());
        searchOccupation.setQualifiedSkillNumber(searchOccupationDTO.getQualifiedSkillNumber());
        searchOccupation.setSkillSize(searchOccupationDTO.getSkillSize());
        searchOccupation.setNotQualifiedSkillNumber(searchOccupationDTO.getNotQualifiedSkillNumber());
        searchOccupation.setRomeCodes(searchOccupationDTO.getOccupation().getRomeOccupations().stream().sorted().map(RomeOccupation::getCode).toList());
        return searchOccupation;
    }

    public ErhgoOccupationOTSummaryDTO buildOccupationOTSummaryDTO(ErhgoOccupationRepository.SearchErhgoOccupationDTO searchOccupationDTO) {
        return buildOccupationOTSummaryDTO(searchOccupationDTO.getOccupation());
    }

    public ErhgoOccupationOTSummaryDTO buildOccupationOTSummaryDTO(ErhgoOccupation occupation) {
        return modelMapper.map(occupation, ErhgoOccupationOTSummaryDTO.class);
    }

    public ErhgoOccupationSummaryDTO buildOccupationSummaryDTO(ErhgoOccupation erhgoOccupation) {
        var searchOccupation = modelMapper.map(erhgoOccupation, ErhgoOccupationSummaryDTO.class);
        searchOccupation.setErhgoOccupationState(ErhgoOccupationStateDTO.fromValue(erhgoOccupation.getQualificationState().name()));
        return searchOccupation;
    }

    public ErhgoOccupationBehaviorsCategoriesDTO buildOccupationBehaviorsCategories(ErhgoOccupation erhgoOccupation) {
        var occupationDTO = modelMapper.map(erhgoOccupation, ErhgoOccupationBehaviorsCategoriesDTO.class);
        occupationDTO.setIsBehaviorCategory1Overloaded(erhgoOccupation.isBehaviorCategory1Overloaded());
        occupationDTO.setIsBehaviorCategory2Overloaded(erhgoOccupation.isBehaviorCategory2Overloaded());
        occupationDTO.setIsBehaviorCategory3Overloaded(erhgoOccupation.isBehaviorCategory3Overloaded());
        return occupationDTO;
    }

    public ErhgoOccupationDetailDTO buildOccupationDTOWithoutState(ErhgoOccupation occupation) {
        var dto = buildOccupationDTO(occupation);
        dto.setErhgoOccupationState(ErhgoOccupationStateDTO.QUALIFIED_V2);
        return dto;
    }
}
