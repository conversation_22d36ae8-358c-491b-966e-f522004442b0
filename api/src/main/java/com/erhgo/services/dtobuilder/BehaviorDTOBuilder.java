package com.erhgo.services.dtobuilder;

import com.erhgo.domain.enums.BehaviorCategory;
import com.erhgo.domain.referential.Behavior;
import com.erhgo.openapi.dto.BehaviorCategoryDTO;
import com.erhgo.openapi.dto.BehaviorDTO;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class BehaviorDTOBuilder {
    public static BehaviorDTO buildDTO(Behavior behavior) {
        return new BehaviorDTO()
                .id(behavior.getId())
                .behaviorCategory(BehaviorCategoryDTO.fromValue(behavior.getBehaviorCategory().toString()))
                .categoryIndex(behavior.getCategoryIndex())
                .description(behavior.getDescription())
                .code(behavior.getCode())
                .title(behavior.getTitle());
    }

    public static BehaviorCategoryDTO buildCategoryDTO(BehaviorCategory behaviorCategory) {
        return behaviorCategory == null ? null : BehaviorCategoryDTO.fromValue(behaviorCategory.name());
    }
}
