package com.erhgo.services.dtobuilder;

import com.erhgo.domain.job.Mission;
import com.erhgo.domain.recruitment.ContextQuestion;
import com.erhgo.domain.recruitment.OptionalActivity;
import com.erhgo.domain.recruitment.OptionalContext;
import com.erhgo.domain.recruitment.RecruitmentProfile;
import com.erhgo.openapi.dto.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;

@Service
@RequiredArgsConstructor
public class RecruitmentProfileDTOBuilder {
    private final ActivityDTOBuilder activityDTOBuilder;
    private final ContextDTOBuilder contextDTOBuilder;

    public RecruitmentProfileDetailDTO build(RecruitmentProfile profile) {
        return new RecruitmentProfileDetailDTO()
                .id(profile.getUuid())
                .modifiable(profile.isModifiable())
                .title(profile.getTitle())
                .optionalActivities(profile.getOptionalActivities() == null ? Collections.emptyList() : profile.getOptionalActivities().stream().map(this::buildOptionalActivityDTO).toList())
                .optionalContexts(profile.getOptionalContexts() == null ? Collections.emptyList() : profile.getOptionalContexts().stream().map(this::buildOptionalContextDTO).toList())
                .qualifiedMissionIds(profile.getQualifiedMissions() == null ? Collections.emptyList() : profile.getQualifiedMissions().stream().map(Mission::getId).toList())
                .contextQuestions(profile.getContextQuestions() == null ? Collections.emptyList() : profile.getContextQuestions().stream().map(RecruitmentProfileDTOBuilder::buildCustomContextLabelDTO).toList())
                .customQuestion(profile.getCustomQuestion());

    }

    private static CustomContextLabelDTO buildCustomContextLabelDTO(ContextQuestion contextQuestion) {
        return new CustomContextLabelDTO()
                .question(QuestionForContextsDTOBuilder.buildSummary(contextQuestion.getQuestion()))
                .contextId(contextQuestion.getContext().getId());
    }

    private OptionalActivityDetailDTO buildOptionalActivityDTO(OptionalActivity optionalActivity) {
        return new OptionalActivityDetailDTO()
                .acquisitionModality(optionalActivity.getAcquisitionModality() == null ? null : AcquisitionModalityDTO.fromValue(optionalActivity.getAcquisitionModality().name()))
                .id(optionalActivity.getId())
                .activity(activityDTOBuilder.buildActivityLabelWithCapacitiesDTO(optionalActivity.getActivityLabel()));
    }

    private OptionalContextDetailDTO buildOptionalContextDTO(OptionalContext optionalContext) {
        return new OptionalContextDetailDTO()
                .acquisitionModality(optionalContext.getAcquisitionModality() == null ? null : AcquisitionModalityDTO.fromValue(optionalContext.getAcquisitionModality().name()))
                .id(optionalContext.getId())
                .context(contextDTOBuilder.buildContextDTO(optionalContext.getContext()));
    }

    public static RecruitmentProfileSummaryDTO buildSummary(RecruitmentProfile profile) {
        return new RecruitmentProfileSummaryDTO()
                .id(profile.getUuid())
                .title(profile.getTitle())
                .qualified(profile.isQualified())
                .modifiable(profile.isModifiable());
    }
}
