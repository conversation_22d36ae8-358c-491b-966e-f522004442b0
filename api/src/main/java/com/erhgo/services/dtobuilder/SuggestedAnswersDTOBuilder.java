package com.erhgo.services.dtobuilder;

import com.erhgo.domain.referential.QuestionForContexts;
import com.erhgo.openapi.dto.SuggestedAnswersDTO;

import java.util.Optional;

import static com.erhgo.domain.enums.Frequency.*;

public class SuggestedAnswersDTOBuilder {
    private SuggestedAnswersDTOBuilder() {
        throw new IllegalStateException("Utility class");
    }

    public static SuggestedAnswersDTO buildForQuestion(Optional<QuestionForContexts> contextQuestion) {
        if (contextQuestion.isEmpty()) {
            return new SuggestedAnswersDTO()
                    .high(HIGH.getDefaultLabel())
                    .medium(MEDIUM.getDefaultLabel())
                    .low(LOW.getDefaultLabel())
                    .none(NONE.getDefaultLabel());
        } else {
            var question = contextQuestion.get();
            return new SuggestedAnswersDTO()
                    .high(question.getTitleForHigh())
                    .medium(question.getTitleForMedium())
                    .low(question.getTitleForLow())
                    .none(question.getTitleForNone());

        }
    }

}
