package com.erhgo.services.dtobuilder;

import com.erhgo.domain.referential.AbstractActivityLabel;
import com.erhgo.domain.referential.Activity;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.openapi.dto.ActivityDTO;
import com.erhgo.openapi.dto.ActivityLabelDTO;
import com.erhgo.openapi.dto.ActivityLabelSummaryDTO;
import com.erhgo.openapi.dto.ActivityLabelWithCapacitiesDTO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;

@Service
@RequiredArgsConstructor
public class ActivityDTOBuilder {
    private final AuditingDTOBuilder auditingDTOBuilder;

    public <A extends AbstractActivityLabel> ActivityLabelWithCapacitiesDTO buildActivityLabelWithCapacitiesDTO(A activityLabel) {
        var activityLabelWithCapacitiesDTO = new ActivityLabelWithCapacitiesDTO()
                .description(activityLabel.getDescription())
                .id(activityLabel.getUuid())
                .title(activityLabel.getTitle())
                .inducedCapacities(
                        activityLabel.getInducedCapacities().stream().sorted(Comparator.comparing(Capacity::getCode).reversed()).map(CapacityDTOBuilder::buildCapacityDetailDTO).toList());
        auditingDTOBuilder.setAuditingAttributeDTO(activityLabel, activityLabelWithCapacitiesDTO);
        return activityLabelWithCapacitiesDTO;
    }

    public static <A extends AbstractActivityLabel> ActivityLabelSummaryDTO buildSummary(A jobActivityLabel) {
        return new ActivityLabelSummaryDTO()
                .description(StringUtils.trimToEmpty(jobActivityLabel.getDescription()))
                .id(jobActivityLabel.getUuid())
                .title(jobActivityLabel.getTitle());
    }

    public static <A extends AbstractActivityLabel> ActivityDTO buildActivityForUpdate(Activity activity, List<A> activities) {
        return new ActivityDTO()
                .description(activity.getDescription())
                .id(activity.getUuid())
                .inducedCapacities(
                        activity.getInducedCapacities().stream().map(CapacityDTOBuilder::buildCapacityDetailDTO).toList())
                .labels(activities.stream().sorted(Comparator.comparing(AbstractActivityLabel::getPosition)).map(ActivityDTOBuilder::buildLabel).toList());

    }

    public static ActivityLabelDTO buildLabel(AbstractActivityLabel jobActivityLabel) {
        return new ActivityLabelDTO()
                .id(jobActivityLabel.getUuid())
                .title(jobActivityLabel.getTitle())
                .position(jobActivityLabel.getPosition());
    }
}
