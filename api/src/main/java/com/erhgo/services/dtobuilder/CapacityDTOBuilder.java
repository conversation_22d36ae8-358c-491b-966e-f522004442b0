package com.erhgo.services.dtobuilder;

import com.erhgo.domain.classifications.ComparableOccupation;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.openapi.dto.CapacityAndLevelDTO;
import com.erhgo.openapi.dto.CapacityDTO;
import com.erhgo.openapi.dto.CapacityDetailDTO;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CapacityDTOBuilder {

    public static List<CapacityAndLevelDTO> buildCapacityAndLevelDTOSForRepositoryDTOS(Collection<Capacity> capacities, Map<Capacity, Integer> levelPerCapacity) {
        return capacities.stream()
                .map(e -> buildCapacityAndLevelDTO(levelPerCapacity.get(e), e))
                .toList();
    }

    public static List<CapacityAndLevelDTO> buildCapacityAndLevelDTOS(Collection<Capacity> capacities, Map<Capacity, Integer> levelPerCapacity) {
        return getCountPerCapacityStream(capacities)
                .map(e -> buildCapacityAndLevelDTO(levelPerCapacity.get(e.getKey()), e.getKey()))
                .toList();
    }

    public static List<CapacityAndLevelDTO> buildCapacityAndLevelDTOS(ComparableOccupation comparableOccupation) {
        return getCountPerCapacityStream(comparableOccupation.getAllCapacitiesWithDuplicates())
                .map(e -> buildCapacityAndLevelDTO(comparableOccupation.getLevelAsInt(), e.getKey()))
                .toList();
    }

    private static Stream<Map.Entry<Capacity, Long>> getCountPerCapacityStream(Collection<Capacity> capacities) {
        return capacities
                .stream()
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                .entrySet()
                .stream();
    }


    private static CapacityAndLevelDTO buildCapacityAndLevelDTO(Integer level, Capacity capacity) {
        return new CapacityAndLevelDTO()
                .masteryLevel(level)
                .capacity(CapacityDTOBuilder.buildCapacityDetailDTO(capacity));
    }

    public static CapacityDetailDTO buildCapacityDetailDTO(Capacity capacity) {
        return new CapacityDetailDTO()
                .id(capacity.getId())
                .code(capacity.getCode())
                .title(capacity.getTitle())
                .description(capacity.getDescription())
                .inducedCapacities(capacity.getInducedCapacities() != null ?
                        capacity.getInducedCapacities().stream().sorted(Comparator.comparing(Capacity::getCode).reversed()).map(CapacityDTOBuilder::buildCapacityDetailDTO).toList() : Collections.emptyList());
    }

    public static CapacityDTO buildCapacity(Capacity capacity) {
        return new CapacityDTO()
                .code(capacity.getCode())
                .description(capacity.getDescription())
                .title(capacity.getTitle())
                .id(capacity.getId());

    }
}
