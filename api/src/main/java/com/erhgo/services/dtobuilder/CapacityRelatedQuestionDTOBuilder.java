package com.erhgo.services.dtobuilder;

import com.erhgo.domain.referential.CapacityRelatedQuestion;
import com.erhgo.domain.referential.CapacityRelatedQuestionResponse;
import com.erhgo.domain.userprofile.capacityrelatedquestion.AnswerForCapacityRelatedQuestion;
import com.erhgo.openapi.dto.*;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CapacityRelatedQuestionDTOBuilder {

    public static CapacityRelatedQuestionDetailsDTO buildDetails(CapacityRelatedQuestion question) {
        return new CapacityRelatedQuestionDetailsDTO()
                .id(question.getId())
                .title(question.getTitle())
                .questionType(QuestionTypeDTO.fromValue(question.getQuestionType().name()))
                .responses(question.getResponses().stream().map(CapacityRelatedQuestionDTOBuilder::buildResponseDTO).toList());
    }

    private static CapacityRelatedQuestionResponseDTO buildResponseDTO(CapacityRelatedQuestionResponse response) {
        return new CapacityRelatedQuestionResponseDTO()
                .id(response.getId())
                .title(response.getTitle())
                .capacities(response.getCapacities().stream().map(CapacityDTOBuilder::buildCapacityDetailDTO).toList())
                ;
    }

    public static CapacityRelatedQuestionSummaryForUserDTO buildSummaryForUser(CapacityRelatedQuestion question, List<AnswerForCapacityRelatedQuestion> responses) {
        return new CapacityRelatedQuestionSummaryForUserDTO()
                .id(question.getId())
                .title(question.getTitle())
                .responses(question
                        .getResponses()
                        .stream()
                        .map(x -> CapacityRelatedQuestionDTOBuilder.buildResponseForUser(x, responses))
                        .toList()
                );
    }

    public static CapacityRelatedQuestionSummaryDTO buildSummaryDTO(CapacityRelatedQuestion question) {
        return new CapacityRelatedQuestionSummaryDTO()
                .id(question.getId())
                .questionIndex(question.getQuestionIndex())
                .title(question.getTitle());
    }


    private static CapacityRelatedQuestionResponseForUserDTO buildResponseForUser(CapacityRelatedQuestionResponse response, List<AnswerForCapacityRelatedQuestion> responses) {
        return new CapacityRelatedQuestionResponseForUserDTO()
                .id(response.getId())
                .title(response.getTitle())
                .selected(responses.stream().anyMatch(x -> x.getResponse().equals(response)));
    }
}
