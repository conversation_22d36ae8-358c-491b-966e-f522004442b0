package com.erhgo.services.dtobuilder;

import com.erhgo.domain.AbstractAuditableEntity;
import com.google.common.collect.Lists;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DTOBuilderUtils {

    public static <A> List<A> prettyList(Collection<A> items) {
        return items == null ? Collections.emptyList() : Lists.newArrayList(items);
    }

    public static <A, B> List<B> prettyMappedList(Collection<A> items, Function<A, B> function) {
        if (items == null) {
            return Collections.emptyList();
        } else {
            return items.stream().map(function).toList();
        }
    }

    public static OffsetDateTime normalizeDate(AbstractAuditableEntity entity) {
        return entity.getUpdatedDate() == null ? null : entity.getUpdatedDate().toInstant().atOffset(ZoneOffset.UTC);
    }


}
