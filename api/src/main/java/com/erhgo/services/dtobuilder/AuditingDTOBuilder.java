package com.erhgo.services.dtobuilder;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.openapi.dto.AuditingDTO;
import com.erhgo.services.keycloak.KeycloakService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.ZoneOffset;

@Service
@RequiredArgsConstructor
public class AuditingDTOBuilder {
    private final KeycloakService keycloakService;

    public <A extends AbstractAuditableEntity, B extends AuditingDTO> void setAuditingAttributeDTO(A entity, B dtoEntity) {
        dtoEntity.setLastModifiedBy(entity.getLastModifiedBy() != null ? keycloakService.getBackOfficeUserFullnameOrEmpty(entity.getLastModifiedBy().getKeycloakId()) : null);
        dtoEntity.setUpdatedDate(entity.getUpdatedDate() != null ? entity.getUpdatedDate().toInstant().atOffset(ZoneOffset.UTC) : null);
    }
}
