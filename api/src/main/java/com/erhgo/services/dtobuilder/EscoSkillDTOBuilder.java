package com.erhgo.services.dtobuilder;

import com.erhgo.domain.classifications.esco.EscoSkill;
import com.erhgo.openapi.dto.SkillDTO;
import com.erhgo.services.keycloak.KeycloakService;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import java.time.ZoneOffset;
import java.util.Collections;

@Service
@RequiredArgsConstructor
public class EscoSkillDTOBuilder {
    private final ModelMapper modelMapper;
    private final KeycloakService keycloakService;
    private final ActivityDTOBuilder activityDTOBuilder;
    private final ContextDTOBuilder contextDTOBuilder;

    public SkillDTO buildSkillDTO(EscoSkill skill) {
        var skillDTO = modelMapper.map(skill, SkillDTO.class);
        skillDTO.setLastModifiedBy(skill.getLastModifiedBy() != null ? keycloakService.getBackOfficeUserFullnameOrEmpty(skill.getLastModifiedBy().getKeycloakId()) : null);
        skillDTO.setUpdatedDate(skill.getUpdatedDate() != null ? skill.getUpdatedDate().toInstant().atOffset(ZoneOffset.UTC) : null);
        skillDTO.setActivities(skill.getActivities() != null ? skill.getActivities().stream().map(activityDTOBuilder::buildActivityLabelWithCapacitiesDTO).toList() : Collections.emptyList());
        skillDTO.setContexts(skill.getContexts() != null ? skill.getContexts().stream().map(contextDTOBuilder::buildContextDTO).toList() : Collections.emptyList());
        return skillDTO;
    }
}
