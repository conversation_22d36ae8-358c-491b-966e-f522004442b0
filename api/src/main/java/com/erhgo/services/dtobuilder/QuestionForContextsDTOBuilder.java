package com.erhgo.services.dtobuilder;

import com.erhgo.domain.referential.QuestionForContexts;
import com.erhgo.openapi.dto.QuestionForContextsDetailsDTO;
import com.erhgo.openapi.dto.QuestionForContextsSummaryDTO;
import com.erhgo.openapi.dto.SuggestedAnswersDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class QuestionForContextsDTOBuilder {
    private final ContextDTOBuilder contextDTOBuilder;

    public static QuestionForContextsSummaryDTO buildSummary(QuestionForContexts questionForContexts) {
        return new QuestionForContextsSummaryDTO()
                .title(questionForContexts.getTitle())
                .suggestedAnswers(new SuggestedAnswersDTO()
                        .none(questionForContexts.getTitleForNone())
                        .low(questionForContexts.getTitleForLow())
                        .medium(questionForContexts.getTitleForMedium())
                        .high(questionForContexts.getTitleForHigh())
                )
                .id(questionForContexts.getUuid());
    }

    public QuestionForContextsDetailsDTO buildDetails(QuestionForContexts questionForContexts) {
        var contextsDTO = questionForContexts.getContexts().stream().map(contextDTOBuilder::buildContextDTO).toList();
        return new QuestionForContextsDetailsDTO()
                .title(questionForContexts.getTitle())
                .contexts(contextsDTO)
                .suggestedAnswers(new SuggestedAnswersDTO()
                        .none(questionForContexts.getTitleForNone())
                        .low(questionForContexts.getTitleForLow())
                        .medium(questionForContexts.getTitleForMedium())
                        .high(questionForContexts.getTitleForHigh())
                )
                .id(questionForContexts.getUuid());
    }
}
