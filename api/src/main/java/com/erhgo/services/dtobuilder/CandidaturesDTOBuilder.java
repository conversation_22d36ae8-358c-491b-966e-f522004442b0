package com.erhgo.services.dtobuilder;

import com.erhgo.domain.candidature.job.*;
import com.erhgo.domain.enums.DriverLicence;
import com.erhgo.domain.userprofile.GeneralInformation;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.dto.SourcingCandidateDTO;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.SourcingKeycloakService;
import com.erhgo.utils.DateTimeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.erhgo.utils.StringUtils.firstNameCase;
import static com.erhgo.utils.StringUtils.lastNameCase;

@Slf4j
@Service
@RequiredArgsConstructor
public class CandidaturesDTOBuilder {

    private final KeycloakService keycloakService;

    private final SourcingKeycloakService sourcingKeycloakService;

    private final UserProfileDTOBuilder userProfileDTOBuilder;

    private final RecruitmentDTOBuilder recruitmentDTOBuilder;

    public SourcingCandidatureItemDTO buildSourcingCandidatureItem(RecruitmentCandidature candidature) {
        var dto = new SourcingCandidatureItemDTO();
        updateCandidatureItemPart(dto, candidature);
        return dto;
    }

    private void updateCandidatureItemPart(SourcingCandidatureItemDTO dto, AbstractCandidature candidature) {
        updateAbstractCandidatureItemPart(dto, candidature);
        if (candidature.isRecruitmentCandidature()) {
            dto.answer(((RecruitmentCandidature) candidature).getCustomAnswer())
                    .generated(((RecruitmentCandidature) candidature).isGeneratedForSourcing());
        }
    }


    public SourcingCandidatureItemDTO buildSourcingCandidatureItem(SourcingCandidateDTO candidate) {
        var dto = new SourcingCandidatureItemDTO();
        updateAbstractCandidatureItemPart(dto, candidate.getCandidature());

        dto
                .numberOfOfferCandidatures(Optional.ofNullable(candidate.getNbCandidatures()).map(Long::intValue).orElse(0));
        return dto;
    }

    private void updateAbstractCandidatureItemPart(SourcingCandidatureItemDTO dto, AbstractCandidature candidatureIn) {
        Optional.ofNullable(candidatureIn).ifPresent(candidature -> {
            var locationDTO = Optional.ofNullable(candidature.getLocation()).map(l -> {
                if (candidatureIn.isAnonymous()) {
                    return l.getDepartmentCode() == null ? null : new LocationDTO().departmentCode(l.getDepartmentCode());
                } else {
                    return l.buildDTO();
                }
            }).orElse(null);
            dto.candidatureId(candidature.getId())
                    .anonymousCode(candidature.getAnonymousCode())
                    .color(candidature.getColor())
                    .lastActionDate(OffsetDateTime.ofInstant(candidature.getUpdatedDate().toInstant(), ZoneId.systemDefault()))
                    .submissionDate(candidature.getSubmissionDate())
                    .situation(Optional.ofNullable(candidature.getUserProfile().generalInformation()).map(GeneralInformation::getSituation).map(Enum::name).map(SituationDTO::fromValue).orElse(null))
                    .state(getSourcingStateFromGlobalState(candidature.getGlobalCandidatureState()))
                    .lastNote(candidature.getLastNote())
                    .userId(candidature.getUserId())
                    .location(locationDTO)
            ;
            if (!candidature.isAnonymous()) {
                keycloakService.getFrontOfficeUserProfile(candidature.getUserProfile().userId()).ifPresent(u -> {
                    dto.firstName(firstNameCase(u.getFirstName()));
                    dto.lastName(lastNameCase(u.getLastName()));
                    dto.email(u.getEmail());
                    dto.phone(Optional.ofNullable(candidature.getUserProfile().generalInformation()).map(GeneralInformation::getPhoneNumber).orElse(null));
                });
            }
            if (!candidature.isRecruitmentCandidature()) {
                var spontaneousCandidature = ((SpontaneousCandidature) candidature);
                var sectors = new ArrayList<SectorDTO>();
                spontaneousCandidature.getReferentialSectors().stream().map(SectorDTOBuilder::buildSectorDTO).forEach(sectors::add);
                var customSectors = spontaneousCandidature.getCustomSectors();
                if (!customSectors.isEmpty()) {
                    sectors.add(SectorDTOBuilder.buildCustomSectorDTO(customSectors));
                }
                dto.sectors(sectors)
                        .hasSoftSkillPdf(StringUtils.isNotBlank(candidature.getUserProfile().trimojiPdfUrl()));
            }
        });
    }

    public static Stream<GlobalCandidatureState> getGlobalStatesFromSourcingState(SourcingCandidatureStateDTO state) {
        Stream<GlobalCandidatureState> result;
        switch (state) {
            case NEW -> result = Stream.of(GlobalCandidatureState.values()).filter(GlobalCandidatureState::isNew);
            case DISMISS -> result = Stream.of(GlobalCandidatureState.REFUSED_MEETING_CLIENT,
                    GlobalCandidatureState.REFUSED_ON_CALL,
                    GlobalCandidatureState.REFUSED_BY_CLIENT_WITH_SHEETS);
            case CONTACTED -> result = Stream.of(GlobalCandidatureState.INTRODUCE_TO_CLIENT,
                    GlobalCandidatureState.SUMMARY_SHEET_SENT);
            case FAVORITE -> result = Stream.of(GlobalCandidatureState.ON_RECRUITMENT_CLIENT,
                    GlobalCandidatureState.RECRUITMENT_VALIDATED);
            case TO_CONTACT -> result = Stream.of(GlobalCandidatureState.INTERNAL_POSITION);
            default -> result = Stream.empty();
        }
        return result;
    }

    public static SourcingCandidatureStateDTO getSourcingStateFromGlobalState(GlobalCandidatureState state) {
        if (state == null) return null;
        SourcingCandidatureStateDTO result = null;
        if (state.isNew()) {
            return SourcingCandidatureStateDTO.NEW;
        }
        switch (state) {
            case INTERNAL_POSITION -> result = SourcingCandidatureStateDTO.TO_CONTACT;
            case INTRODUCE_TO_CLIENT, SUMMARY_SHEET_SENT -> result = SourcingCandidatureStateDTO.CONTACTED;
            case ON_RECRUITMENT_CLIENT, RECRUITMENT_VALIDATED -> result = SourcingCandidatureStateDTO.FAVORITE;
            case REFUSED_MEETING_CLIENT, REFUSED_ON_CALL, REFUSED_BY_CLIENT_WITH_SHEETS ->
                    result = SourcingCandidatureStateDTO.DISMISS;
            default -> log.error("state {} not treated in getSourcingStateFromGlobalState", state);
        }
        return result;
    }

    public SourcingCandidatureDetailDTO buildSourcingCandidatureDetail(AbstractCandidature candidature, Collection<RecruitmentCandidature> previousCandidatures, SpontaneousCandidature spontaneousCandidature) {
        var userRepresentation = keycloakService.getFrontOfficeUserProfile(candidature.getUserProfile().userId());
        var completionScore = userProfileDTOBuilder.buildIncompleteInformations(candidature.getUserProfile(), userRepresentation.orElseThrow()).size();

        var candidatures = previousCandidatures.stream()
                .filter(c -> !c.getId().equals(candidature.getId()))
                .sorted(Comparator.comparing(
                                (RecruitmentCandidature c) -> Optional.ofNullable(c.getSubmissionDate())
                                        .orElse(DateTimeUtils.dateToOffsetDate(c.getUpdatedDate())))
                        .reversed())
                .collect(Collectors.toCollection(ArrayList<AbstractCandidature>::new));
        if (spontaneousCandidature != null && !spontaneousCandidature.getId().equals(candidature.getId())) {
            candidatures.add(spontaneousCandidature);
        }
        var dto = new SourcingCandidatureDetailDTO()
                .userId(candidature.getUserProfile().userId())
                .candidatureNotes(candidature.getCandidatureNotes().stream()
                        .sorted(Comparator.comparing(CandidatureNote::getCreatedDate).reversed())
                        .map(this::buildNoteDTO)
                        .filter(Objects::nonNull)
                        .toList())
                .numberOfExperiences(candidature.getUserProfile().experiences().size())
                .hasDrivingLicense(candidature.getUserProfile().getDriverLicenceFromCriteria() == DriverLicence.LICENCE_B)
                .workingTimes(candidature.getUserProfile().getTypeWorkingTimeFromCriteria().stream().map(Enum::name).sorted().map(WorkingTimeDTO::fromValue).toList())
                .contracts(candidature.getUserProfile().getContractsFromCriteria().stream().map(Enum::name).sorted().map(TypeContractCategoryDTO::fromValue).toList())
                .completionScore(completionScore)
                .lastConnectionDate(DateTimeUtils.localDateTimeToOffsetDateTime(candidature.getUserProfile().lastConnectionDate()))
                .recruitment(candidature.isRecruitmentCandidature() ? recruitmentDTOBuilder.buildSumUp(((RecruitmentCandidature) candidature).getRecruitment()) : null)
                .previousCandidatures(candidatures.stream()
                        .map(this::buildCandidatureSumUp)
                        .toList());
        updateCandidatureItemPart(dto, candidature);
        return dto;
    }

    private SourcingCandidatureSumUpDTO buildCandidatureSumUp(AbstractCandidature candidature) {
        return new SourcingCandidatureSumUpDTO()
                .id(candidature.getId())
                .state(getSourcingStateFromGlobalState(candidature.getGlobalCandidatureState()))
                .submissionDate(candidature.getSubmissionDate())
                .jobTitle(candidature.getTitle())
                ;
    }


    private SourcingCandidatureNoteDTO buildNoteDTO(CandidatureNote candidatureNote) {
        var user = sourcingKeycloakService.getSourcingUserWithGroups(candidatureNote.getCreatedByUserId());
        return user == null ? null : new SourcingCandidatureNoteDTO()
                .content(candidatureNote.getText())
                .noteDateTime(OffsetDateTime.ofInstant(candidatureNote.getUpdatedDate().toInstant(), ZoneId.systemDefault()))
                .userFullname(user.getFullname())
                ;
    }
}
