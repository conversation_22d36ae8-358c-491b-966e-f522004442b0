package com.erhgo.services.dtobuilder;

import com.erhgo.openapi.dto.*;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Page;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PageDTOBuilder {
    private static <A extends AbstractItemPageDTO> A enhancePage(Page<?> page, A itemPageDTO) {
        return (A) itemPageDTO
                .numberOfElementsInPage(page.getNumberOfElements())
                .pageIndex(page.getNumber())
                .pageSize(page.getSize())
                .totalPages(page.getTotalPages())
                .totalNumberOfElements(page.getTotalElements());
    }

    public static ActivityLabelPageDTO buildActivityPage(Page<ActivityLabelWithCapacitiesDTO> page) {
        return enhancePage(page, new ActivityLabelPageDTO().content(page.getContent()));
    }

    public static ContextsPageDTO buildContextPage(Page<ContextDTO> page) {
        return enhancePage(page, new ContextsPageDTO().content(page.getContent()));
    }

    public static CandidatureContactInfoPageDTO buildCandidatureContactInfoPage(Page<ContactForCandidatureDTO> contactInfos) {
        return enhancePage(contactInfos, new CandidatureContactInfoPageDTO().content(contactInfos.getContent()));
    }

    public static QuestionForContextsPageDTO buildQuestionForContextsPage(Page<QuestionForContextsDetailsDTO> questionForContextsPage) {
        return enhancePage(questionForContextsPage, new QuestionForContextsPageDTO().content(questionForContextsPage.getContent()));
    }

    public static EscoOccupationPageDTO buildEscoOccupationPage(Page<EscoOccupationSummaryDTO> occupationsPage) {
        return enhancePage(occupationsPage, new EscoOccupationPageDTO().content(occupationsPage.getContent()));
    }

    public static ErhgoOccupationPageDTO buildErhgoOccupationPage(Page<ErhgoOccupationSummaryDTO> occupationsPage) {
        return enhancePage(occupationsPage, new ErhgoOccupationPageDTO().content(occupationsPage.getContent()));
    }

    public static ErhgoOccupationOTPageDTO buildErhgoOccupationOTPage(Page<ErhgoOccupationOTSummaryDTO> occupationsPage) {
        return enhancePage(occupationsPage, new ErhgoOccupationOTPageDTO().content(occupationsPage.getContent()));
    }

    public static RomePageDTO buildRomePage(Page<RomeSummaryDTO> romesPage) {
        return enhancePage(romesPage, new RomePageDTO().content(romesPage.getContent()));
    }

    public static JobPageDTO buildJobPage(Page<JobSummaryWithRecruitmentProfileDTO> jobPage) {
        return enhancePage(jobPage, new JobPageDTO().content(jobPage.getContent()));
    }

    public static OrganizationPageDTO buildOrganizationPage(Page<OrganizationDTO> organizationPage) {
        return enhancePage(organizationPage, new OrganizationPageDTO().content(organizationPage.getContent()));
    }

    public static SimpleRecruitmentPageDTO buildSimpleRecruitmentPage(Page<SimpleRecruitmentSummaryDTO> recruitmentPage) {
        return enhancePage(recruitmentPage, new SimpleRecruitmentPageDTO().content(recruitmentPage.getContent()));
    }

    public static UserPageDTO buildUserPage(Page<FoUserSummaryDTO> userPage) {
        return enhancePage(userPage, new UserPageDTO().content(userPage.getContent()));
    }

    public static BehaviorPageDTO buildBehaviorPage(Page<BehaviorDTO> behaviorPage) {
        return enhancePage(behaviorPage, new BehaviorPageDTO().content(behaviorPage.getContent()));
    }

    public static LandingPagePageDTO buildLandingPagePage(Page<LandingPageSummaryDTO> page) {
        return enhancePage(page, new LandingPagePageDTO().content(page.getContent()));
    }

    public static RecruitmentPageDTO buildRecruitmentPage(Page<RecruitmentSummaryDTO> page) {
        return enhancePage(page, new RecruitmentPageDTO().content(page.getContent()));
    }

    public static RecruitmentPageForCandidateDTO buildRecruitmentPageForCandidate(Page<RecruitmentSummaryForCandidateDTO> page) {
        return enhancePage(page, new RecruitmentPageForCandidateDTO().content(page.getContent()));
    }

    public static CapacityRelatedQuestionPageDTO buildCapacityRelatedQuestionPage(Page<CapacityRelatedQuestionSummaryDTO> page) {
        return enhancePage(page, new CapacityRelatedQuestionPageDTO().content(page.getContent()));
    }


    public static UserMatchingJobPageDTO buildUserMatchingJobPage(Page<MatchingUserSummaryDTO> page) {
        return enhancePage(page, new UserMatchingJobPageDTO().content(page.getContent()));

    }

    public static UserByGroupPageDTO buildUserByGroupPage(Page<UserContactInfoDTO> page) {
        return enhancePage(page, new UserByGroupPageDTO().content(page.getContent()));
    }

    public static SourcingCandidaturePageDTO buildSourcingCandidaturesPage(Page<SourcingCandidatureItemDTO> page) {
        return enhancePage(page, new SourcingCandidaturePageDTO().content(page.getContent()));
    }

    public static ExternalOffersPageDTO buildExternalOffersPage(Page<ExternalOfferSummaryDTO> page) {
        return enhancePage(page, new ExternalOffersPageDTO().content(page.getContent()));
    }
}
