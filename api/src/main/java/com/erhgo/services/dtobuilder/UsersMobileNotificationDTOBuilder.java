package com.erhgo.services.dtobuilder;

import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.openapi.dto.UsersMobileNotificationDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class UsersMobileNotificationDTOBuilder {
    // this specific id can be retrieved from mobile source code
    private static final String RECRUITMENT_NOTIFICATION_TYPE = "1";
    private static final String RECRUITMENT_NOTIFICATION_PREFIX = "RECRUITMENT_MOBILE_NOTIFICATION";
    private static final Locale CURRENT_LANGUAGE = Locale.FRENCH;
    private final MessageSource messageSource;

    public UsersMobileNotificationDTO buildUsersMobileRecruitmentNotificationDTO(Collection<UserProfile> usersToNotify, Recruitment recruitment) {
        return new UsersMobileNotificationDTO()
                .usersId(usersToNotify.stream().map(UserProfile::userId).sorted().toList())
                .subject(messageSource.getMessage(RECRUITMENT_NOTIFICATION_PREFIX + ".SUBJECT", new Object[]{recruitment.getJobTitle(), Optional.ofNullable(recruitment.getLocation()).map(Location::getCity).orElse("")}, CURRENT_LANGUAGE))
                .content(messageSource.getMessage(RECRUITMENT_NOTIFICATION_PREFIX + ".CONTENT", null, CURRENT_LANGUAGE))
                .data(Map.of("notificationType", RECRUITMENT_NOTIFICATION_TYPE,
                        "recruitmentCode", recruitment.getCode()));
    }
}
