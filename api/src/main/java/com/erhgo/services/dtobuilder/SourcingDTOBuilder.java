package com.erhgo.services.dtobuilder;

import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.ContractType;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.sourcing.SourcingInvitation;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.dto.CandidatureCountDTO;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.SourcingKeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.utils.DateTimeUtils;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class SourcingDTOBuilder {

    private final KeycloakService keycloakService;
    private final SourcingKeycloakService sourcingKeycloakService;
    private final SourcingUserDTOBuilder sourcingUserDTOBuilder;

    public static ErhgoClassificationDTO buildDTO(ErhgoClassification erhgoClassification) {
        return new ErhgoClassificationDTO()
                .code(erhgoClassification.getCode())
                .title(erhgoClassification.getTitle())
                .orderIndex(erhgoClassification.getOrderIndex())
                .icon(erhgoClassification.getIcon())
                .highPriority(erhgoClassification.isHighPriority());
    }

    public SourcingInvitationCodeDTO buildInvitationCodeDTO(SourcingInvitation sourcingInvitation) {
        return new SourcingInvitationCodeDTO()
                .code(sourcingInvitation.getCode())
                .maxNumberOfGuests(sourcingInvitation.getMaxNumberOfGuests())
                .numberOfGuests(sourcingInvitation.getGuests().size())
                .createdBy(keycloakService.getBackOfficeUserFullnameOrEmpty(sourcingInvitation.getCreatedByUserId()))
                .duration(sourcingInvitation.getDuration())
                .organization(sourcingInvitation.getHost().getTitle())
                .creationDate(OffsetDateTime.ofInstant(sourcingInvitation.getCreatedDate().toInstant(), ZoneId.systemDefault()));

    }


    public SourcingJobAndRecruitmentDTO buildSourcingJobAndRecruitmentDTO(Recruitment recruitment) {
        var usersToNotify = recruitment.getSourcingUsersIdToNotify().stream()
                .map(sourcingKeycloakService::getSourcingUser)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .map(SourcingDTOBuilder::buildSourcingUserDTO)
                .toList();
        return new SourcingJobAndRecruitmentDTO()
                .baseSalary(recruitment.getBaseSalary())
                .description(recruitment.getDescription())
                .location(Optional.ofNullable(recruitment.getLocation()).map(Location::buildDTO).orElse(null))
                // Working time & type contract are handle directly
                .criteriaValues(recruitment.getJob().getCriteriaValuesCodes().stream().filter(c -> !CriteriaValue.WORKING_TIME_FOR_CRITERIA_RESPONSE.containsKey(c) && !CriteriaValue.TYPE_CONTRACT_FOR_CRITERIA_RESPONSE.containsKey(c)).toList())
                .customQuestion(recruitment.getRecruitmentProfile().getCustomQuestion())
                .hideSalary(recruitment.getHideSalary())
                .maxSalary(recruitment.getMaxSalary())
                .occupationId(Optional.ofNullable(recruitment.getJob().getErhgoOccupation()).map(ErhgoOccupation::getId).orElse(null))
                .organization(OrganizationDTOBuilder.buildForSourcing(recruitment))
                .step(recruitment.getSourcingStep())
                .title(recruitment.getJob().getTitle())
                .jobId(recruitment.getJob().getId())
                .typeContractCategory(Optional.ofNullable(recruitment.getTypeContract()).map(ContractType::getTypeContractCategory).map(Enum::name).map(TypeContractCategoryDTO::fromValue).orElse(null))
                .workingTimeType(recruitment.getJob().getWorkingTimes().stream().findFirst().map(Enum::name).map(WorkingTimeDTO::valueOf).orElse(null))
                .modularWorkingTime(recruitment.getModularWorkingTime())
                .workingWeeklyTime(recruitment.getWorkingWeeklyTime())
                .organizationDescription(recruitment.getOrganizationOrRecruiterDescription())
                .externalUrl(recruitment.getUrl())
                .recruitmentId(recruitment.getId())
                .recruitmentState(RecruitmentStateDTO.valueOf(recruitment.getState().name()))
                .publicationEndDate(recruitment.getPublicationEndDate())
                .isForcedUrl(recruitment.getRecruiter().isForcedUrl())
                .usersToNotify(usersToNotify)
                .erhgoClassifications(recruitment.getErhgoClassifications().stream()
                        .map(SourcingDTOBuilder::buildDTO)
                        .toList())
                .manager(getSourcingUserDTO(recruitment.getManagerUserId()))
                .publicationDate(DateTimeUtils.dateToOffsetDate(recruitment.getPublicationDate()))
                .lastProcessingDate(recruitment.getLastProcessingDate())
                .lastProcessingType(getLastProcessingType(recruitment.getLastProcessingType()))
                ;
    }

    private static SourcingUserDTO buildSourcingUserDTO(UserRepresentation u) {
        return new SourcingUserDTO().id(u.getId()).email(u.getEmail()).fullname(u.getFullname());
    }

    public SourcingRecruitmentItemDTO buildSourcingRecruitmentDTO(CandidatureCountDTO dto) {
        return new SourcingRecruitmentItemDTO()
                .recruitmentState(RecruitmentStateDTO.valueOf(dto.getRecruitmentState().name()))
                .recruitmentId(dto.getRecruitmentId())
                .title(dto.getJobTitle())
                .location(dto.getLocation() == null ? null : dto.getLocation().buildDTO())
                .baseSalary(dto.getBaseSalary() == null ? null : dto.getBaseSalary())
                .maxSalary(dto.getMaxSalary() == null ? null : dto.getMaxSalary())
                .candidaturesCount(new SourcingCandidatureCountRecruitmentDTO()
                        .dismissCandidatureCount(dto.getDismissCandidatureCount())
                        .favoriteCandidatureCount(dto.getFavoriteCandidatureCount())
                        .newCandidatureCount(new CandidatureCountItemDTO()
                                .fromUser(dto.getNewFromUser())
                                .generated(dto.getNewGenerated())
                        )
                        .totalCandidatureCount(new CandidatureCountItemDTO()
                                .fromUser(dto.getTotalFromUser())
                                .generated(dto.getTotalGenerated())
                        )
                        .contactedCandidatureCount(dto.getContactedCandidatureCount())
                        .toContactCandidatureCount(dto.getToContactCandidatureCount())
                )
                .publicationDate(DateTimeUtils.instantToOffsetDateTime(dto.getPublicationDate()))
                .publicationEndDate(dto.getPublicationEndDate())
                .notifiedUsersCount(dto.getNotifiedUsersCount())
                .lastProcessingType(getLastProcessingType(dto.getLastProcessingType()))
                .lastProcessingDate(dto.getLastProcessingDate())
                .manager(getSourcingUserDTO(dto.getManagerUserId()))
                .jobId(dto.getJobId())
                ;

    }

    @Nullable
    private static ProcessingTypeDTO getLastProcessingType(Recruitment.ProcessingType processingType) {
        return processingType == null ? null : ProcessingTypeDTO.valueOf(processingType.name());
    }

    @Nullable
    private SourcingUserDTO getSourcingUserDTO(String userId) {
        return sourcingKeycloakService.getSourcingUser(userId).map(sourcingUserDTOBuilder::buildSourcingUser).orElse(null);
    }

}
