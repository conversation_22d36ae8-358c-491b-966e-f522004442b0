package com.erhgo.services.dtobuilder;

import com.erhgo.domain.criteria.Criteria;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.userprofile.criteria.UserCriteriaValue;
import com.erhgo.openapi.dto.CriteriaDTO;
import com.erhgo.openapi.dto.CriteriaValueDTO;
import com.erhgo.openapi.dto.UserCriteriaValueDTO;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CriteriaDTOBuilder {

    private final ModelMapper modelMapper;

    public CriteriaDTO buildDTO(Criteria criteria) {
        return modelMapper.map(criteria, CriteriaDTO.class);
    }

    public CriteriaValueDTO buildCriteriaValueDTO(CriteriaValue criteria) {
        return modelMapper.map(criteria, CriteriaValueDTO.class);
    }

    public UserCriteriaValueDTO buildCriteriaSummaryDTO(UserCriteriaValue criteria) {
        var criteriaValue = criteria.getValue();
        return (UserCriteriaValueDTO) new UserCriteriaValueDTO()
                .selected(criteria.isSelected())
                .code(criteriaValue.getCode())
                .titleForQuestion(criteriaValue.getTitleForQuestion())
                .titleStandalone(criteriaValue.getTitleStandalone())
                .icon(criteria.getValue().getIcon())
                .valueIndex(criteriaValue.getValueIndex());
    }
}
