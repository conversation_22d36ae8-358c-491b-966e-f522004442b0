package com.erhgo.services.dtobuilder;

import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Employer;
import com.erhgo.openapi.dto.OrganizationDTO;
import com.erhgo.openapi.dto.OrganizationSummaryDTO;
import com.erhgo.openapi.dto.OrganizationTypeDTO;
import com.erhgo.openapi.dto.SourcingOrganizationDTO;
import com.erhgo.repositories.RecruiterRepository;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;

import static com.erhgo.domain.referential.AbstractOrganization.SiretVerificationStatus.WRONG_SIRET;

@Service
@RequiredArgsConstructor
public class OrganizationDTOBuilder {
    private final KeycloakService keycloakService;
    private final RecruiterRepository recruiterRepository;
    private final ModelMapper modelMapper;

    public OrganizationDTO buildOrganizationDTO(AbstractOrganization organization) {
        var dto = modelMapper.map(organization, OrganizationDTO.class);

        if (organization.getClass().isAssignableFrom(Employer.class)) {
            var employer = (Employer) organization;
            dto.setRefererRecruiter(buildOrganizationSummary(employer.getRefererRecruiter()));
            dto.setConsortiums(employer.getConsortiums()
                    .stream()
                    .map(this::buildOrganizationSummary)
                    .toList());
        }
        if (organization.mayHaveProjects()) {
            var organizationRoles = keycloakService.getRolesForGroup(organization.getCode());
            dto.setProjects(this.fetchAndBuildSummary(organizationRoles).stream().filter(o -> o.getOrganizationType() == OrganizationTypeDTO.PROJECT).toList());
        }
        dto.setForcedUrl(organization.isForcedUrl() ? organization.getUrl() : null);
        return dto;
    }

    public List<OrganizationSummaryDTO> fetchAndBuildSummary(Collection<String> organizationRoles) {
        return recruiterRepository.findByCodeIn(organizationRoles)
                .stream()
                .map(this::buildOrganizationSummary)
                .sorted(Comparator.comparing(OrganizationSummaryDTO::getTitle, StringUtils.DIATRIC_AND_CASE_INSENSITIVE_COMPARATOR.INSTANCE))
                .toList();
    }

    public OrganizationSummaryDTO buildOrganizationSummary(AbstractOrganization organization) {
        var result = modelMapper.map(organization, OrganizationSummaryDTO.class);
        result.setExternalUrl(organization.getUrl());
        return result;
    }

    public static SourcingOrganizationDTO buildForSourcing(Recruitment recruitment) {
        var dto = buildForSourcing(recruitment.getJob().getRecruiter());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(recruitment.getOrganizationOrRecruiterDescription())) {
            dto.description(recruitment.getOrganizationOrRecruiterDescription());
        }
        dto.externalUrl(recruitment.getUrl());
        return dto;
    }

    public static SourcingOrganizationDTO buildForSourcing(AbstractOrganization organization) {

        return new SourcingOrganizationDTO()
                .description(organization.getDescription())
                .name(organization.getTitle())
                .siret(organization.getSiret())
                .hasSiretError(organization.getSiretVerificationStatus() == WRONG_SIRET)
                .isForcedUrl(organization.isForcedUrl())
                .externalUrl(StringUtils.normalizeURL(organization.getOrganizationUrl()))
                .gdprMention(organization.getGdprMention());
    }
}
