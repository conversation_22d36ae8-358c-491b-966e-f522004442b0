package com.erhgo.services.dtobuilder;

import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.openapi.dto.ErhgoClassificationDTO;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ErhgoClassificationDTOBuilder {
    private final ModelMapper modelMapper;

    public ErhgoClassificationDTO buildErhgoClassificationDTO(ErhgoClassification erhgoClassification) {
        return erhgoClassification == null ? null : modelMapper.map(erhgoClassification, ErhgoClassificationDTO.class);
    }
}
