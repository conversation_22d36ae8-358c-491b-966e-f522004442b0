package com.erhgo.services.dtobuilder;

import com.erhgo.domain.sourcing.SourcingPreferences;
import com.erhgo.openapi.dto.SourcingUserDTO;
import com.erhgo.repositories.SourcingPreferencesRepository;
import com.erhgo.services.keycloak.UserRepresentation;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class SourcingUserDTOBuilder {

    private final SourcingPreferencesRepository preferencesRepository;

    public SourcingUserDTO buildSourcingUser(UserRepresentation userRepresentation) {
        var notificationRefused = preferencesRepository.findByUserId(userRepresentation.getId()).map(SourcingPreferences::hasRefuseNotification).orElse(false);
        return new SourcingUserDTO()
                .id(userRepresentation.getId())
                .email(userRepresentation.getEmail())
                .fullname(userRepresentation.getFullname())
                .lastConnectionDate(userRepresentation.getLastConnectionDate())
                .enabled(userRepresentation.getEnabled())
                .notificationRefused(notificationRefused);
    }

}
