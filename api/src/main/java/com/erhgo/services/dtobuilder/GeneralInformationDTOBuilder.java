package com.erhgo.services.dtobuilder;

import com.erhgo.domain.enums.ContactTime;
import com.erhgo.domain.enums.Situation;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.userprofile.GeneralInformation;
import com.erhgo.domain.userprofile.GeneralInformationHolder;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.RecruiterRepository;
import com.erhgo.repositories.UserMobileTokenRepository;
import com.erhgo.repositories.dto.UserMatchingSummary;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.utils.DateTimeUtils;
import com.erhgo.utils.KeycloakUtils;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class GeneralInformationDTOBuilder {
    private final ModelMapper modelMapper;
    private final RecruiterRepository recruiterRepository;
    private final UserMobileTokenRepository userMobileTokenRepository;

    public static ContactForCandidatureDTO buildContactForCandidature(Long candidatureId, OffsetDateTime refusalDate, GeneralInformation information, UserRepresentation userRepresentation, boolean candidatureIsArchived) {
        var userProfile = information.getUserProfile();
        return new ContactForCandidatureDTO()
                .candidatureId(candidatureId)
                .userId(userProfile.userId())
                .firstName(userRepresentation.getFirstName())
                .lastName(userRepresentation.getLastName())
                .email(userRepresentation.getEmail())
                .refusalDate(refusalDate)
                .isBlacklisted(userProfile.getJobOfferOptOut())
                .contactTime(information.getContactTime() == null ? null : ContactTimeDTO.fromValue(information.getContactTime().name()))
                .phoneNumber(information.getPhoneNumber())
                .candidatureIsArchived(candidatureIsArchived)
                ;
    }

    public GeneralInformationDTO buildGeneralInformationDTO(GeneralInformationHolder generalInformation, UserRepresentation keycloakUser) {
        var informationDTO = new GeneralInformationDTO();
        if (keycloakUser != null) {
            var date = DateTimeUtils.millisToOffsetDateTime(keycloakUser.getCreatedTimestamp());
            informationDTO
                    .firstName(keycloakUser.getFirstName())
                    .lastName(keycloakUser.getLastName())
                    .email(keycloakUser.getEmail())
                    .lastMobileConnexionDate(Optional.ofNullable(generalInformation.getLastMobileConnectionDate()).map(DateTimeUtils::localDateTimeToOffsetDateTime).orElse(null))
                    .creationDate(date)
            ;

            if (keycloakUser.getGroups() != null && !keycloakUser.getGroups().isEmpty()) {
                var organizations = getOrganizationsOfChannels(keycloakUser);
                informationDTO.channels(organizations.stream().map(AbstractOrganization::getTitle).toList());
                informationDTO.setIsPrivate(organizations.stream().anyMatch(AbstractOrganization::isPrivateUsers));
            } else {
                informationDTO.channels(Collections.emptyList());
                informationDTO.setIsPrivate(false);
            }
        }


        var optionalGeneralInfo = Optional.ofNullable(generalInformation);
        return informationDTO.phoneNumber(optionalGeneralInfo.map(GeneralInformationHolder::getPhoneNumber).orElse(null))
                .location(optionalGeneralInfo.map(GeneralInformationHolder::getLocation).map(GeneralInformationDTOBuilder::buildLocationDTO).orElse(null))
                .birthDate(optionalGeneralInfo.map(GeneralInformationHolder::getBirthDate).orElse(null))
                .contactTime(optionalGeneralInfo.map(GeneralInformationHolder::getContactTime).map(ContactTime::name).map(ContactTimeDTO::fromValue).orElse(null))
                .salary(optionalGeneralInfo.map(GeneralInformationHolder::getSalary).orElse(null))
                .situation(optionalGeneralInfo.map(GeneralInformationHolder::getSituation).map(Situation::name).map(SituationDTO::fromValue).orElse(null))
                .transactionalBlacklisted(optionalGeneralInfo.map(GeneralInformationHolder::getTransactionalBlacklisted).orElse(null))
                .lastConnectionDate(optionalGeneralInfo.map(GeneralInformationHolder::getLastConnectionDate).map(a -> a.atOffset(DateTimeUtils.zoneOffset())).orElse(null))
                .smsBlacklisted(optionalGeneralInfo.map(GeneralInformationHolder::getSmsBlacklisted).orElse(null))
                .source(optionalGeneralInfo.map(GeneralInformationHolder::getSource).orElse(null))
                .delayInMonth(optionalGeneralInfo.map(GeneralInformationHolder::getDelayInMonth).orElse(null))
                ;
    }

    private List<AbstractOrganization> getOrganizationsOfChannels(UserRepresentation keycloakUser) {
        var groups = keycloakUser.getGroups();
        var organizations = new ArrayList<AbstractOrganization>(recruiterRepository.findByCodeIn(groups));
        if (organizations.size() != groups.size()) {
            log.warn("Some user groups are not known organizations {}", String.join(", ", groups));
        }
        return organizations;
    }

    public static LocationDTO buildLocationDTO(Location location) {
        return new LocationDTO()
                .city(location.getCity())
                .citycode(location.getCitycode())
                .postcode(location.getPostcode())
                .departmentCode(location.getDepartmentCode())
                .regionName(location.getRegionName())
                .longitude(location.getLongitude())
                .latitude(location.getLatitude())
                .radiusInKm(location.getRadiusInKm());
    }

    public void feedUserContactInfo(UserContactInfoDTO dto, GeneralInformation generalInformation, UserRepresentation keycloakUser) {
        dto.id(keycloakUser == null ? null : keycloakUser.getId())
                .contactInformation(buildGeneralInformationDTO(generalInformation, keycloakUser));
    }

    public <A extends UserSummaryDTO> A buildUserSummary(UserRepresentation keycloakUser, Class<A> destinationType) {
        return modelMapper.map(keycloakUser, destinationType);
    }

    public MatchingUserSummaryDTO buildMatchingUserSummary(
            UserMatchingSummary data,
            Optional<UserRepresentation> authOpt,
            int numberOfCapacitiesInJob,
            Collection<String> jobCriteriaCodes
    ) {
        var userId = data.getUserId();

        var auth = authOpt.orElseGet(() -> KeycloakUtils.defaultUserRepresentation(userId));

        var matchingCriteria = Splitter.on(",").omitEmptyStrings().trimResults().splitToList(Strings.nullToEmpty(data.getMatchingCriteriaAsStringFromDB()));
        var missingCriteria = Splitter.on(",").omitEmptyStrings().trimResults().splitToList(Strings.nullToEmpty(data.getMissingCriteriaAsStringFromDB()));
        var unknownCriteria = jobCriteriaCodes.stream().filter(c -> !matchingCriteria.contains(c) && !missingCriteria.contains(c)).toList();

        return new MatchingUserSummaryDTO()
                .id(data.getUserId())
                .contactInformation(buildGeneralInformationDTO(data, auth))
                .matchingRateInPercent(data.getNumberOfCapacities() * 100 / numberOfCapacitiesInJob)
                .candidatureId(data.getCandidatureId())
                .missingCriteria(missingCriteria)
                .unknownCriteria(unknownCriteria);
    }

}
