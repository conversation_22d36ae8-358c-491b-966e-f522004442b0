package com.erhgo.services;

import com.erhgo.domain.enums.QuestionType;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.InvalidCommandException;
import com.erhgo.domain.referential.CapacityRelatedQuestion;
import com.erhgo.domain.userprofile.capacityrelatedquestion.AnswerForCapacityRelatedQuestion;
import com.erhgo.openapi.dto.CapacityRelatedQuestionSummaryForUserDTO;
import com.erhgo.openapi.dto.QuestionTypeDTO;
import com.erhgo.openapi.dto.QuestionsSumupForUserDTO;
import com.erhgo.openapi.dto.SaveAnswerToCapacityRelatedQuestionCommandDTO;
import com.erhgo.repositories.AnswerForCapacityRelatedQuestionRepository;
import com.erhgo.repositories.CapacityRelatedQuestionRepository;
import com.erhgo.security.AuthorizeExpression;
import com.erhgo.security.Role;
import com.erhgo.services.dtobuilder.CapacityRelatedQuestionDTOBuilder;
import com.erhgo.services.userprofile.UserProfileProvider;
import com.google.common.collect.Sets;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class AnswerForCapacityRelatedQuestionService {

    @Autowired
    private CapacityRelatedQuestionRepository capacityRelatedQuestionRepository;

    @Autowired
    private AnswerForCapacityRelatedQuestionRepository answerForCapacityRelatedQuestionRepository;

    @Autowired
    private UserProfileProvider userProfileProvider;

    @Transactional(readOnly = true)
    @RolesAllowed(Role.CANDIDATE)
    public List<CapacityRelatedQuestionSummaryForUserDTO> getQuestionsForUser(QuestionTypeDTO questionTypeDTO, String organizationCode) {
        if ((questionTypeDTO == QuestionTypeDTO.EXTRAPROFESSIONAL) != (organizationCode == null)) {
            throw new InvalidCommandException("Question type and organization code are not compatible");
        }
        var userProfile = userProfileProvider.getAuthenticatedUserProfileOrCreate();
        var questionType = QuestionType.valueOf(questionTypeDTO.name());
        var candidateResponses = answerForCapacityRelatedQuestionRepository
                .findUserAnswers(userProfile, questionType);

        return capacityRelatedQuestionRepository
                .findByQuestionTypeOrderByQuestionIndex(questionType, Pageable.unpaged())
                .stream()
                .map(x -> CapacityRelatedQuestionDTOBuilder.buildSummaryForUser(x, candidateResponses))
                .toList();
    }

    @Transactional
    @RolesAllowed(Role.CANDIDATE)
    public void save(SaveAnswerToCapacityRelatedQuestionCommandDTO command) {
        var userProfile = userProfileProvider.getAuthenticatedUserProfileOrCreate();

        var question = capacityRelatedQuestionRepository
                .findById(command.getQuestionId())
                .orElseThrow(() -> new EntityNotFoundException(command.getQuestionId(), CapacityRelatedQuestion.class));

        var selectedAnswers = question.getResponses()
                .stream()
                .filter(r -> command.getAnswers().contains(r.getId()))
                .map(e -> AnswerForCapacityRelatedQuestion.builder().response(e).userProfile(userProfile).build())
                .collect(Collectors.toSet());

        var previousAnswers = Set.copyOf(answerForCapacityRelatedQuestionRepository
                .findByUserProfileAndResponseIn(userProfile, question.getResponses()));

        var answersToDelete = Sets.difference(previousAnswers, selectedAnswers).immutableCopy();
        var answersToCreate = Sets.difference(selectedAnswers, previousAnswers).immutableCopy();

        answerForCapacityRelatedQuestionRepository.saveAll(answersToCreate);
        answerForCapacityRelatedQuestionRepository.deleteAll(answersToDelete);
        userProfile.addCapacities(answersToCreate.stream().flatMap(a -> a.getRelatedCapacitiesRecursively().stream()).toList());
        userProfile.removeCapacities(answersToDelete.stream().flatMap(a -> a.getRelatedCapacitiesRecursively().stream()).toList());
    }

    @Transactional(readOnly = true)
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_READ)
    public QuestionsSumupForUserDTO sumupForUser(String userId) {
        var user = userProfileProvider.getUserProfileOrCreate(userId);
        return new QuestionsSumupForUserDTO()
                .userAnsweredQuestionsCount(user.countExtraProfessionalQuestionsAnswered())
                .totalQuestionsCount((int) capacityRelatedQuestionRepository.count());
    }
}
