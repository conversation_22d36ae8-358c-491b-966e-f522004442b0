package com.erhgo.services;

import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.InvalidCommandException;
import com.erhgo.domain.referential.AbstractActivityLabel;
import com.erhgo.domain.referential.Activity;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.ActivityRepository;
import com.erhgo.repositories.CapacityRepository;
import com.erhgo.repositories.JobActivityLabelRepository;
import com.erhgo.security.Role;
import com.erhgo.services.candidature.RecruitmentCandidatureService;
import com.erhgo.services.denormalization.ActivityUpdateListener;
import com.erhgo.services.dto.criteria.ActivityCriteria;
import com.erhgo.services.dtobuilder.ActivityDTOBuilder;
import com.erhgo.services.dtobuilder.PageDTOBuilder;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import jakarta.annotation.security.RolesAllowed;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ActivityService {
    private final ActivityRepository activityRepository;
    @Lazy
    private final RecruitmentCandidatureService candidatureService;
    private final CapacityRepository capacityRepository;
    private final JobActivityLabelRepository repository;
    private final EntityManager entityManager;
    private final ActivityDTOBuilder activityDTOBuilder;
    private final Collection<ActivityUpdateListener> activityUpdateListeners;

    @Transactional(readOnly = true)
    public List<ActivityLabelSummaryDTO> searchActivities(String query) {
        return activityRepository.searchActivities(query).stream()
                .map(ActivityDTOBuilder::buildSummary).toList();
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public Activity saveActivity(ActivityTypeDTO activityType, SaveActivityCommandDTO command) {
        var activity = saveActivity(command);

        updateActivityLabels(activityType, command.getLabels(), activity);

        return activity;
    }

    private Activity saveActivity(SaveActivityCommandDTO command) {
        var activityOptional = activityRepository.findById(command.getId());
        var previousCapacities = activityOptional.map(Activity::getInducedCapacities).orElse(Collections.emptySet());

        var activity = activityOptional.orElse(Activity.builder()
                .uuid(command.getId())
                .build());

        activity.setDescription(command.getDescription());
        var inducedCapacitiesCodesFromCommand = Sets.newHashSet(command.getInducedCapacities());
        Set<Capacity> capacities = Sets.newHashSet(capacityRepository.findByCodeIn(inducedCapacitiesCodesFromCommand));
        var notFoundCapacities = Sets.difference(inducedCapacitiesCodesFromCommand, capacities.stream().map(Capacity::getCode).collect(Collectors.toSet()));
        if (!notFoundCapacities.isEmpty()) {
            throw new EntityNotFoundException(notFoundCapacities.iterator().next(), Capacity.class);
        }
        activity.setInducedCapacities(capacities);

        activity = activityRepository.save(activity);

        if (activityOptional.isPresent()) {
            notifyCapacitiesUpdate(activity, previousCapacities);
        }
        return activity;
    }

    private void notifyCapacitiesUpdate(Activity activity, Set<Capacity> previousCapacities) {
        var removedCapacities = Sets.difference(previousCapacities, activity.getInducedCapacities()).immutableCopy();
        var addedCapacities = Sets.difference(activity.getInducedCapacities(), previousCapacities).immutableCopy();
        activityUpdateListeners.forEach(listener -> {
            listener.notifyCapacitiesAdded(activity, addedCapacities);
            listener.notifyCapacitiesRemoved(activity, removedCapacities);
        });
    }

    // Suppress warning: we use a generic call to 'deleteAll' & 'saveAll' to allow reuse of code for all types of activities
    @SuppressWarnings("unchecked")
    private void updateActivityLabels(ActivityTypeDTO activityType, List<ActivityLabelDTO> newLabels, Activity activity) {
        var isJob = activityType == ActivityTypeDTO.JOB;
        var activityLabelsRemaining = newLabels.stream()
                .map(activityLabel -> buildActivityLabel(activity, activityLabel))
                .toList();
        activityLabelsRemaining = Lists.newArrayList(repository.saveAll((Iterable) activityLabelsRemaining));
        var previousActivityLabels = repository.findByActivityUuid(activity.getUuid());
        var activityLabelsToDelete = previousActivityLabels.stream().filter(a -> newLabels.stream().noneMatch(l -> l.getId().equals(a.getUuid()))).collect(Collectors.toSet());
        var jobActivityLabelsToDelete = activityLabelsToDelete.stream()
                .filter(AbstractActivityLabel::isJob)
                .map(JobActivityLabel.class::cast)
                .toList();
        if (activityLabelsRemaining.isEmpty()) {
            throw new InvalidCommandException("ACTIVITY_LABELS_NOT_DELETABLE", "Some activity labels are not deletable", "ActivityLabel deletion");
        }
        if (isJob && !jobActivityLabelsToDelete.isEmpty()) {
            var defaultActivityLabel = activityLabelsRemaining.get(0);
            repository.bulkUpdateActivityLabel((JobActivityLabel) defaultActivityLabel, jobActivityLabelsToDelete);
        }
        repository.deleteAll(activityLabelsToDelete);
    }

    private AbstractActivityLabel buildActivityLabel(Activity activity, ActivityLabelDTO activityLabel) {
        return new JobActivityLabel()
                .setActivity(activity)
                .setPosition(activityLabel.getPosition())
                .setTitle(activityLabel.getTitle())
                .setUuid(activityLabel.getId());
    }


    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public ActivityDTO getActivity(UUID id) {
        var activityLabel = repository.findById(id).orElseThrow(() -> new EntityNotFoundException(id, AbstractActivityLabel.class));
        var activities = repository.findByActivityUuid(activityLabel.getActivity().getUuid());
        // FIXME: why "buildActivityForUpdate" ? Retrieving single activity should not serialize induced capacities
        return ActivityDTOBuilder.buildActivityForUpdate(activityLabel.getActivity(), activities);
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional(readOnly = true)
    public boolean isActivityLabelDeletable(UUID id) {
        // ActivityLabel is deletable if it is not the used by any other entity (Mission, UserExperience) and if it remains at least one ActivityLabel per Activity
        var activity = repository.findById(id).orElseThrow(() -> new EntityNotFoundException(id, JobActivityLabel.class)).getActivity();

        return !repository.isActivityUsedByAnyEntity(id) && repository.findByActivityUuid(activity.getUuid()).size() > 1;
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void mergeActivity(MergeActivityCommandDTO mergeActivityCommandDTO) {
        if (mergeActivityCommandDTO.getSourceActivities().stream().anyMatch(Objects::isNull)) {
            throw new IllegalArgumentException("Source activity uuid cannot be null");
        }
        var activitiesToDelete = activityRepository.findAllById(mergeActivityCommandDTO.getSourceActivities());
        if (activitiesToDelete.size() != mergeActivityCommandDTO.getSourceActivities().size()) {
            throw new EntityNotFoundException(
                    mergeActivityCommandDTO.getSourceActivities()
                            .stream()
                            .map(UUID::toString)
                            .filter(requestActivityUUID -> activitiesToDelete.stream().noneMatch(dbActivity -> dbActivity.getUuid().toString().equals(requestActivityUUID)))
                            .collect(Collectors.joining(",")
                            )
                    , Activity.class);
        }
        var newActivity =
                saveActivity(ActivityTypeDTO.JOB, mergeActivityCommandDTO.getMergedActivity());
        log.debug("Activity created for merge: {}", newActivity);
        log.debug("Deleting activities {}", activitiesToDelete);
        activityRepository.deleteAll(activitiesToDelete);
    }

    private Page<ActivityLabelWithCapacitiesDTO> doListActivities(ActivityCriteria activityCriteria) {
        final var sortDirection = activityCriteria.direction() == SortDirectionDTO.ASC ? Sort.Direction.ASC : Sort.Direction.DESC;
        var safeBy = Strings.isNullOrEmpty(activityCriteria.by()) ? "uuid" : activityCriteria.by();
        final var pageable = PageRequest.of(activityCriteria.page(), activityCriteria.size(), Sort.by(sortDirection, safeBy));
        return repository.findByCriteria(activityCriteria, pageable).map(activityDTOBuilder::buildActivityLabelWithCapacitiesDTO);
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public ActivityLabelPageDTO listActivities(ActivityCriteria criteria) {
        return PageDTOBuilder.buildActivityPage(doListActivities(criteria));
    }

}
