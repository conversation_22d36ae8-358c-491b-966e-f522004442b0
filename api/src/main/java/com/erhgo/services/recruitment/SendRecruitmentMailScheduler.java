package com.erhgo.services.recruitment;

import com.erhgo.services.SecurityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class SendRecruitmentMailScheduler {

    private final SecurityService securityService;
    private final SendRecruitmentProposalService sendRecruitmentProposalService;

    @Scheduled(cron = "${application.sendRecruitmentProposalMails.cron}", zone = "Europe/Paris")
    @SchedulerLock(name = "SendRecruitmentProposalMails")
    public void sendRecruitmentProposalMails() {
        log.debug("Starting sendRecruitmentProposalMails");
        securityService.doAsAdmin(() -> {
            var future = sendRecruitmentProposalService.sendRecruitmentProposal();
            log.debug("SendRecruitmentProposalMails launch successfully");
            future.join();
            log.debug("SendRecruitmentProposalMails ended successfully");
        });
    }

}
