package com.erhgo.services.recruitment;

import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.notification.AbstractNotification;
import com.erhgo.domain.userprofile.notification.RecruitmentNotification;
import com.erhgo.repositories.NotificationRepository;
import com.erhgo.services.mailing.MailingListService;
import com.erhgo.utils.SalaryUtils;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class SendRecruitmentProposalService {

    private final NotificationRepository notificationRepository;
    private final MailingListService mailingListService;
    private final TransactionTemplate transactionTemplate;
    @Value("${sendinblue.templates.sourcing-offer-sum-up}")
    private Long templateId;
    @Value("${keycloak-realms.front_office_base_url}")
    private String foUrl;
    private final ObjectMapper objectMapper;
    @Value("${sourcing.maxOffersPerCandidate}")
    private Integer maxOffersPerCandidate;
    @Value("${sourcing.maxOffersPerRecruiter}")
    private Integer maxOffersPerRecruiter;

    record DailyRecapOffer(
            @JsonProperty("TITRE")
            String title,
            @JsonProperty("CONTRAT")
            String contract,
            @JsonProperty("VILLE")
            String city,
            @JsonProperty("CODE_POSTAL")
            String postcode,
            @JsonProperty("LIEN")
            String link,
            @JsonProperty("SALAIRE")
            String salary,
            @JsonProperty("OCCUPATION_ID")
            String occupationId

    ) {
        public static DailyRecapOffer forRecruitment(Recruitment recruitment, String foUrl) {
            return new DailyRecapOffer(
                    recruitment.getJobTitle(),
                    recruitment.getTypeContract().getDescription(),
                    Optional.ofNullable(recruitment.getLocation()).map(Location::getCity).orElse(""),
                    Optional.ofNullable(recruitment.getLocation()).map(Location::getPostcode).orElse(""),
                    foUrl + "/jobs/" + recruitment.getCode(),
                    SalaryUtils.getSalaryLabel(recruitment),
                    Optional.ofNullable(recruitment.getErhgoOccupation()).map(a -> a.getId().toString()).orElse("")
            );
        }
    }

    record DailyRecapPerRecruiter(
            @JsonProperty("RECRUTEUR")
            String recruiter,
            @JsonProperty("OFFRES")
            @Getter
            List<DailyRecapOffer> offers) {
        public void addRecruitment(Recruitment recruitment, String foUrl) {
            this.offers.add(DailyRecapOffer.forRecruitment(recruitment, foUrl));
        }

        public int size() {
            return offers.size();
        }
    }

    @RequiredArgsConstructor
    @Data
    static class DailyRecap {
        @JsonProperty("SOUHAITS")
        private final String classifications;
        @JsonProperty("OFFRES_PAR_RECRUTEUR")
        @Getter
        private List<DailyRecapPerRecruiter> offers = new ArrayList<>();

        @JsonIgnore
        private final int maxOffersPerRecruiter;
        @JsonIgnore
        private Set<Long> ignoredRecruitments = new HashSet<>();

        @JsonGetter
        @JsonProperty("NB_OFFRES")
        public int numberOfOffers() {
            return offers.stream().mapToInt(o -> o.offers.size()).sum();
        }

        void addRecruitment(Recruitment recruitment, String foUrl) {
            var recruiterTitle = recruitment.getRecruiterTitle();
            var recruiterOffers = offers.stream().filter(r -> r.recruiter.equals(recruiterTitle)).findFirst().orElse(null);
            if (recruiterOffers == null) {
                recruiterOffers = new DailyRecapPerRecruiter(recruiterTitle, new ArrayList<>());
                offers.add(recruiterOffers);
            }
            if (recruiterOffers.size() < maxOffersPerRecruiter)
                recruiterOffers.addRecruitment(recruitment, foUrl);
            else {
                ignoredRecruitments.add(recruitment.getId());
            }
        }
    }

    public CompletableFuture<Void> sendRecruitmentProposal() {
        Map<String, DailyRecap> paramPerUserId = new HashMap<>();
        Set<UserProfile> userProfiles = new HashSet<>();
        List<RecruitmentNotification> notifications = new ArrayList<>();
        Set<String> userIdsHavingTooManyRecruitments = new HashSet<>();
        transactionTemplate.execute(_unused -> {
            notifications.addAll(notificationRepository.findByRequiresMailSendingIsTrue());
            notifications.forEach(notification -> {
                if (notification.requiresMailSendingActually()) {
                    var dto = paramPerUserId.get(notification.getUserId());
                    if (dto == null) {
                        userProfiles.add(notification.getUserProfile());
                        dto = new DailyRecap(notification.getUserProfile().getAcceptedErhgoClassifications().stream().map(ErhgoClassification::getTitle).sorted().collect(Collectors.joining(" ; ")), maxOffersPerRecruiter);
                        paramPerUserId.put(notification.getUserId(), dto);
                    }
                    if (dto.numberOfOffers() < maxOffersPerCandidate) {
                        dto.addRecruitment(notification.getRecruitment(), foUrl);
                    } else {
                        userIdsHavingTooManyRecruitments.add(notification.getUserId());
                    }
                } else {
                    log.trace("Ignoring notification {} on non active recruitment", notification.getId());
                }
            });
            return null;
        });
        if (!userIdsHavingTooManyRecruitments.isEmpty()) {
            log.debug("Notifications were truncating to {} while sending recruitment proposal to users {}", maxOffersPerCandidate, userIdsHavingTooManyRecruitments);
        }
        paramPerUserId.entrySet().stream().filter(a -> !a.getValue().ignoredRecruitments.isEmpty()).forEach(a -> {
            log.debug("Some recruitments where ignored for user {} (too many recruitments for some recruiters) : {}", a.getKey(), a.getValue().ignoredRecruitments);
        });

        if (!userProfiles.isEmpty()) {
            var offerStats = paramPerUserId.values().stream().map(dailyRecap -> dailyRecap.getOffers().stream().flatMap(o -> o.getOffers().stream()).toList()).mapToInt(Collection::size).summaryStatistics();
            return mailingListService.sendMailsToProfilesForTemplate(
                            userProfiles,
                            "Envoi par mail à %d candidats de rappel des offres (min/moyenne/max : %d/%.0f/%d offres par candidat - limité à %d)"
                                    .formatted(userProfiles.size(), offerStats.getMin(), offerStats.getAverage(), offerStats.getMax(), maxOffersPerCandidate),
                            templateId,
                            null,
                            objectMapper.convertValue(paramPerUserId, new TypeReference<>() {
                            }))
                    .thenAccept(userIdHavingMails -> {
                        updateNotifications(userIdHavingMails, notifications.stream().map(AbstractNotification::getId).collect(Collectors.toSet()));
                        log.debug("SendRecruitmentProposal completed: all mail sending and notification updates finished");
                    });
        } else {
            updateNotifications(Collections.emptySet(), notifications.stream().map(AbstractNotification::getId).collect(Collectors.toSet()));
            log.debug("SendRecruitmentProposal completed: no users to notify, notification updates finished");
            return CompletableFuture.completedFuture(null);
        }

    }

    private void updateNotifications(Set<String> userIdHavingMails, Set<UUID> notificationIds) {
        transactionTemplate.execute(_unused -> {
            var notifications = notificationRepository.findAllById(notificationIds);
            notifications.forEach(n -> n.mailHandled(userIdHavingMails.contains(n.getUserId())));
            notificationRepository.saveAll(notifications);
            log.debug("Notification updates completed: {} mails successfully sent to {} users out of {} total users with notifications",
                    userIdHavingMails.size(),
                    userIdHavingMails.size(),
                    notifications.stream().map(AbstractNotification::getUserId).distinct().count());
            return null;
        });
    }
}
