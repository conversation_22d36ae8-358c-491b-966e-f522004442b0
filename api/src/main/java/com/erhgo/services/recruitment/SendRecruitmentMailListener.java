package com.erhgo.services.recruitment;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.userprofile.event.UserNewCandidaturePublishedEvent;
import com.erhgo.repositories.NotificationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class SendRecruitmentMailListener {

    private final NotificationRepository notificationRepository;

    @EventListener(UserNewCandidaturePublishedEvent.class)
    @Transactional
    public void onUserNewCandidaturePublished(UserNewCandidaturePublishedEvent event) {
        if (event.getCandidature().isRecruitmentCandidature()) {
            log.trace("Disabling mail notification on candidature published");
            notificationRepository
                    .findByRecruitmentAndUserProfile(((RecruitmentCandidature) event.getCandidature()).getRecruitment(), event.getUserProfile())
                    .forEach(n -> n.mailHandled(false))
            ;
        }
    }

}
