package com.erhgo.services.denormalization;

import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.referential.JobActivityLabel;

import java.util.Collection;

public interface ErhgoOccupationUpdateListener {

    void notifyLevelUpdated(ErhgoOccupation occupation);

    void notifyActivitiesAdded(ErhgoOccupation occupation, JobActivityLabel... activityLabel);

    void notifyActivitiesRemoved(ErhgoOccupation occupation, JobActivityLabel... activityLabel);

    void notifyActivitiesReplaced(ErhgoOccupation occupation, Collection<JobActivityLabel> previousActivities, Collection<JobActivityLabel> nextActivities);

    void notifyRomeOrEscoUpdated(ErhgoOccupation occupation);

}
