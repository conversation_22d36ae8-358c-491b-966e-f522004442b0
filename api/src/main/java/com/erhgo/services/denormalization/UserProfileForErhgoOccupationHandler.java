package com.erhgo.services.denormalization;

import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.experience.UserExperience;
import com.erhgo.repositories.UserExperienceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserProfileForErhgoOccupationHandler implements ErhgoOccupationUpdateListener {

    private final UserExperienceRepository userExperienceRepository;

    @Override
    public void notifyLevelUpdated(ErhgoOccupation occupation) {
        log.info("Updating mastery level of experiences of users");
        getUserProfileStream(occupation)
                .forEach(UserProfile::refreshMasteryLevelAndMarkAsIndexationRequired);
    }

    @Override
    public void notifyActivitiesAdded(ErhgoOccupation occupation, JobActivityLabel... activityLabels) {
        log.info("Activities added on user profile");
        getUserProfileStream(occupation)
                .forEach(u -> u.addCapacities(Stream.of(activityLabels).flatMap(a -> a.getInducedCapacities().stream()).toList()));

    }

    @Override
    public void notifyActivitiesRemoved(ErhgoOccupation occupation, JobActivityLabel... activitiesLabel) {
        log.info("Activities removed on user profile");
        var capacitiesToRemove = Stream.of(activitiesLabel).flatMap(a -> a.getInducedCapacities().stream()).toList();
        getUserProfileStream(occupation)
                .forEach(u -> u.removeCapacities(capacitiesToRemove));
    }

    @Override
    public void notifyActivitiesReplaced(ErhgoOccupation occupation, Collection<JobActivityLabel> previousActivities, Collection<JobActivityLabel> nextActivities) {
        var removedCapacities = previousActivities.stream().flatMap(a -> a.getInducedCapacities().stream()).toList();
        var addedCapacities = nextActivities.stream().flatMap(a -> a.getInducedCapacities().stream()).toList();
        getUserProfileStream(occupation)
                .forEach(u -> {
                    u.addCapacities(addedCapacities);
                    u.removeCapacities(removedCapacities);
                });

    }

    @Override
    public void notifyRomeOrEscoUpdated(ErhgoOccupation occupation) {
        log.info("Updating users experience where ROME or ESCO occupations changed");
        getUserProfileStream(occupation).forEach(UserProfile::dirtiesUserProfile);
    }

    private Stream<UserProfile> getUserProfileStream(ErhgoOccupation occupation) {
        return userExperienceRepository
                .findByErhgoOccupationId(occupation.getId())
                .stream()
                .map(UserExperience::getUserProfile);
    }
}
