package com.erhgo.services.denormalization;

import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.CapacityRelatedQuestionResponse;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.capacityrelatedquestion.AnswerForCapacityRelatedQuestion;
import com.erhgo.repositories.AnswerForCapacityRelatedQuestionRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Set;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
public class UserCapacitiesFromExtraProfessionalHandler implements CapacityRelatedQuestionUpdateListener {

    @Autowired
    private AnswerForCapacityRelatedQuestionRepository repository;

    private Stream<UserProfile> getUserProfileStream(CapacityRelatedQuestionResponse response) {
        return repository.findByResponseIn(Set.of(response))
                .stream()
                .map(AnswerForCapacityRelatedQuestion::getUserProfile);
    }

    @Override
    public void notifyCapacitiesRemoved(CapacityRelatedQuestionResponse response, Collection<Capacity> removedCapacities) {
        getUserProfileStream(response).forEach(up -> up.removeCapacities(removedCapacities));
    }

    @Override
    public void notifyCapacitiesAdded(CapacityRelatedQuestionResponse response, Collection<Capacity> addedCapacities) {
        getUserProfileStream(response).forEach(up -> up.addCapacities(addedCapacities));
    }
}
