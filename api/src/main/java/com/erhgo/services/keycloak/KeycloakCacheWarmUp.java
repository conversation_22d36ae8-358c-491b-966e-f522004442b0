package com.erhgo.services.keycloak;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Profile("!test")
@Service
@RequiredArgsConstructor
@Slf4j
public class KeycloakCacheWarmUp {

    private final CacheManager cacheManager;
    private final KeycloakService keycloakService;

    @Scheduled(fixedDelayString = "${application.cache-warmup.delay}", initialDelayString = "PT10S")
    public void warmupCache() {
        log.info("Starting FO user caches warmup");
        clearAllUsersCache();
        warmupCaches();
        log.info("FO User caches warmup ends");
    }

    private void warmupCaches() {
        var cacheName = KeycloakService.FO_USER_REPRESENTATION_CACHE;
        var userPerIdCache = cacheManager.getCache(cacheName);
        if (userPerIdCache != null) {
            keycloakService.findAllFrontOfficeUsers().forEach(user -> userPerIdCache.put(user.getId(), user));
        } else {
            log.error("User cache warmup fails for {}", cacheName);
        }
    }

    private void clearAllUsersCache() {
        var cacheName = KeycloakService.FO_ALL_USERS_CACHE;
        var allUsersCache = cacheManager.getCache(cacheName);
        if (allUsersCache != null) {
            allUsersCache.clear();
        } else {
            log.error("User cache cleanup fails for {}", cacheName);
        }
    }
}
