package com.erhgo.services.keycloak;

import com.erhgo.domain.exceptions.EntityAlreadyExistException;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.openapi.dto.ConfirmFOUserFromBOCommandDTO;
import com.erhgo.openapi.dto.SaveUserNameCommandDTO;
import com.erhgo.openapi.dto.SetFrontOfficeUserPasswordCommandDTO;
import com.erhgo.security.Role;
import com.erhgo.services.AbstractService;
import com.erhgo.services.dto.UserKeycloakRepresentation;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import jakarta.annotation.security.RolesAllowed;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.UriBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.GroupResource;
import org.keycloak.admin.client.resource.RealmResource;
import org.keycloak.admin.client.resource.UserResource;
import org.keycloak.admin.client.resource.UsersResource;
import org.keycloak.representations.idm.CredentialRepresentation;
import org.keycloak.representations.idm.GroupRepresentation;
import org.keycloak.representations.idm.RoleRepresentation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.context.annotation.Profile;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.google.common.collect.Lists.newArrayList;

@Service
@RequiredArgsConstructor
@Slf4j
@Profile("!test")
public class KeycloakServiceImpl implements KeycloakService, SourcingKeycloakService, HealthIndicator {

    private static final Collection<String> CACHES = List.of(BO_USER_FULL_NAME_CACHE, BO_USER_REPRESENTATION_CACHE, FO_USER_REPRESENTATION_CACHE, FO_ALL_USERS_CACHE, SOURCING_USER_BY_EMAIL_CACHE);
    public static final String PASSWORD_CONFIRMED_QUERY_PARAM = "passwordconfirmed";

    private static final String EMAIL_KEY = "email";
    private static final int MAX_NUMBER_OF_USERS_PER_ORGANIZATION = 10_000;

    private final Keycloak keycloak;

    @Value("${keycloak-realms.sourcing_realm_id:''}")
    private String sourcingRealm;

    @Value("${keycloak-realms.back_office_realm_id:''}")
    private String backOfficeRealm;

    @Value("${keycloak-realms.front_office_realm_id:''}")
    private String frontOfficeRealm;

    @Value("${keycloak-realms.front_office_base_url}")
    private String frontOfficeBaseUrl;

    private final CacheManager cacheManager;

    @Override
    public AbstractService.PageDTOAdapter<UserRepresentation> getBackOfficeGroupMembersPaginatedResource(String groupName, int pageIndex, int pageSize) {
        final List<org.keycloak.representations.idm.UserRepresentation> response = new ArrayList<>();
        final var group = getGroupResourceByName(groupName, getBackOfficeRealmResource());
        if (group != null) {
            var users = group.members(pageIndex * pageSize, pageSize);
            users.forEach(this::setApplicativeGroupsToUser);
            response.addAll(users);
        }
        var page = new PageImpl<>(response, PageRequest.of(pageIndex, pageSize), countBackOfficeGroupMembers(groupName)).map(UserRepresentation::forUser);
        return new AbstractService.PageDTOAdapter<>(page);
    }

    private void setApplicativeGroupsToUser(org.keycloak.representations.idm.UserRepresentation userRepresentation) {
        var groups = getBackOfficeRealmResource().users()
                .get(userRepresentation.getId())
                .groups()
                .stream()
                .map(GroupRepresentation::getName)
                .filter(Predicate.not(Role::isOrganizationRoleOrGroup))
                .toList();

        userRepresentation.setGroups(groups);
    }

    @Override
    public int countBackOfficeGroupMembers(String groupName) {
        var response = 0;
        final var group = getGroupResourceByName(groupName, getBackOfficeRealmResource());
        if (group != null) {
            response = group.members().size();
        }
        return response;
    }

    @Override
    public String createUserInBackOfficeRealm(UserKeycloakRepresentation userKeycloakRepresentation) {
        var userResource = getBackOfficeRealmResource().users();

        return createUserInRealm(userKeycloakRepresentation, userResource);

    }

    @Override
    @CacheEvict(value = SOURCING_USER_REPRESENTATION_CACHE, key = "#userId")
    public void markNewAuthentication(String userId) {
        var userResource = getSourcingRealmResource().users().get(userId);
        var userRepresentation = userResource.toRepresentation();
        var attributes = userRepresentation.getAttributes();
        attributes.put(UserRepresentation.LAST_LOGIN_TIMESTAMP_ATTRIBUTE, List.of("%d".formatted(Instant.now().toEpochMilli())));
        userResource.update(userRepresentation);
    }

    @Override
    public String createUserInFrontOfficeRealm(UserKeycloakRepresentation userKeycloakRepresentation) {
        var userResource = getFrontOfficeRealmResource().users();
        return createUserInRealm(userKeycloakRepresentation, userResource);
    }

    @Override
    public String createUserInSourcingRealm(UserKeycloakRepresentation userKeycloakRepresentation) {
        var userResource = getSourcingRealmResource().users();
        return createUserInRealm(userKeycloakRepresentation, userResource);
    }

    private String createUserInRealm(UserKeycloakRepresentation userKeycloakRepresentation, UsersResource userResource) {
        final var user = new org.keycloak.representations.idm.UserRepresentation();
        user.setEmail(userKeycloakRepresentation.getEmail());
        user.setFirstName(userKeycloakRepresentation.getFirstName());
        user.setLastName(userKeycloakRepresentation.getLastName());
        user.setUsername(userKeycloakRepresentation.getUsername());
        user.setAttributes(userKeycloakRepresentation.getMiscAttributes());
        user.setEnabled(true);
        if (userKeycloakRepresentation.getPassword() != null) {
            final var passwordCred = new CredentialRepresentation();
            passwordCred.setTemporary(true);
            passwordCred.setType(CredentialRepresentation.PASSWORD);
            passwordCred.setValue(userKeycloakRepresentation.getPassword());
            user.setCredentials(List.of(passwordCred));
        }
        var allGroups = new ArrayList<String>();
        if (userKeycloakRepresentation.getOrganizationCode() != null) {
            allGroups.add(userKeycloakRepresentation.getOrganizationCode());
        }
        if (userKeycloakRepresentation.getGroup() != null) {
            allGroups.add(userKeycloakRepresentation.getGroup());
        }
        user.setGroups(allGroups);
        try (final var result = userResource.create(user)) {
            var statusId = result.getStatus();
            var status = HttpStatus.valueOf(statusId);
            if (status.is2xxSuccessful()) {
                return result.getLocation().getPath().replaceAll(".*/([^/]+)$", "$1");
            } else {
                var reason = extractReasonFromResponse(userKeycloakRepresentation, result);
                if (HttpStatus.CONFLICT.equals(status)) {
                    throw new EntityAlreadyExistException(reason, UserKeycloakRepresentation.class, userKeycloakRepresentation.getEmail());
                } else {
                    log.error("{} could not be created in keycloak - got status {}, reason: {}", userKeycloakRepresentation, status, reason);
                    throw new GenericTechnicalException(userKeycloakRepresentation + " could not be created in keycloak - got response " + statusId);
                }
            }
        } catch (WebApplicationException | ProcessingException e) {
            throw new GenericTechnicalException("Unable to create Keycloak account " + userKeycloakRepresentation, e);
        }
    }


    private String extractReasonFromResponse(UserKeycloakRepresentation userKeycloakRepresentation, Response result) {
        String error;
        try {
            error = result.readEntity(String.class);
        } catch (IllegalStateException | ProcessingException e) {
            error = "unknown";
        }
        if (error != null
                && (error.toLowerCase().contains(EMAIL_KEY) || error.toLowerCase().contains("username"))) {
            log.info("Email " + userKeycloakRepresentation.getEmail() + " already present in keycloak");
            return EMAIL_KEY;
        }
        return error;
    }

    private RealmResource getBackOfficeRealmResource() {
        return keycloak.realm(backOfficeRealm);
    }

    private RealmResource getFrontOfficeRealmResource() {
        return keycloak.realm(frontOfficeRealm);
    }

    private RealmResource getSourcingRealmResource() {
        return keycloak.realm(sourcingRealm);
    }

    private GroupResource getGroupResourceByName(String groupName, RealmResource realmResource) {

        // TODO: simplify with realmResource.groups().groups(groupName, true, 0, 1, true) ?
        final GroupResource group;
        String organizationGroupId = null;

        final var groupsResource = realmResource.groups();
        for (var groupRepresentation : groupsResource.groups()) {
            if (groupRepresentation.getName().equalsIgnoreCase(groupName.toUpperCase())) {
                organizationGroupId = groupRepresentation.getId();
            }
        }
        if (organizationGroupId != null) {
            group = realmResource.groups().group(organizationGroupId);
        } else {
            group = null;
        }
        return group;
    }

    @Override
    @Cacheable(BO_USER_REPRESENTATION_CACHE)
    public Optional<UserRepresentation> getBackOfficeUserProfile(String userId) {
        try {
            return Optional.of(getBackOfficeRealmResource().users().get(userId))
                    .map(UserResource::toRepresentation)
                    .map(UserRepresentation::forUser)
                    ;
        } catch (NotFoundException e) {
            return Optional.empty();
        }
    }

    @Override
    @Cacheable(value = FO_USER_REPRESENTATION_CACHE, unless = "#result == null")
    public Optional<UserRepresentation> getFrontOfficeUserProfile(String userId) {
        return getUserRepresentation(userId).map(UserRepresentation::forUser);
    }

    @Override
    public Optional<UserRepresentation> getFrontOfficeUserProfileWithGroups(String userId) {
        var userRepresentation = getUserRepresentation(userId).map(UserRepresentation::forUser);
        userRepresentation.ifPresent(u -> u.setGroups(getFrontOfficeRealmResource().users().get(userId).groups().stream().map(GroupRepresentation::getName).toList()));
        return userRepresentation;
    }

    private Optional<org.keycloak.representations.idm.UserRepresentation> getUserRepresentation(String userId) {
        try {
            var userResource = getFrontOfficeRealmResource().users().get(userId);
            return Optional.of(userResource).map(UserResource::toRepresentation);
        } catch (NotFoundException e) {
            return Optional.empty();
        }
    }

    @Override
    public UserRepresentation getFOUserRepresentationByEmail(String email) {
        return getUserFromEmail(email, getFrontOfficeRealmResource()).orElse(null);
    }

    @Override
    public Page<UserRepresentation> searchFrontOfficeUsersPaginated(String search, int page, int size) {
        var frontUser = getFrontOfficeRealmResource().users();
        if (isUUID(search)) {
            try {
                var userRepresentation = Collections.singletonList(UserRepresentation.forUser(frontUser.get(search).toRepresentation()));
                return new PageImpl<>(userRepresentation, PageRequest.of(page, size), 1);
            } catch (NotFoundException e) {
                return new PageImpl<>(Collections.emptyList(), PageRequest.of(page, size), 0);
            }
        } else {
            var first = page * size;
            var userCount = frontUser.count(search);
            var result = frontUser.search(search, first, size);
            return new PageImpl<>(result, PageRequest.of(page, size), userCount).map(UserRepresentation::forUser);
        }
    }

    @Override
    public List<UserRepresentation> getBackOfficeUsers() {
        var count = getBackOfficeRealmResource()
                .users()
                .count();
        var backUser = getBackOfficeRealmResource().users();

        return backUser.list(null, count).stream().map(UserRepresentation::forUser)
                .toList();
    }

    private boolean isUUID(String uuid) {
        try {
            UUID.fromString(uuid);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    @Override
    @Cacheable(BO_USER_FULL_NAME_CACHE)
    public String getBackOfficeUserFullnameOrEmpty(String keycloakId) {
        return keycloakId == null ? "" : getBackOfficeUserProfile(keycloakId).map(u -> u.getFirstName() + " " + u.getLastName()).orElse("");
    }


    @Override
    @SuppressWarnings("java:S1181")
    public Health health() {
        try {
            getBackOfficeRealmResource().groups().groups();
            return Health.up().build();
        } catch (Throwable e) {
            return Health.down().withException(e).build();
        }
    }

    @Override
    @RolesAllowed({Role.ODAS_ADMIN, Role.SOURCING})
    public void createBackOfficeGroupAndRoles(String groupAndRole, String... otherRoles) {
        createGroupAndRoles(groupAndRole, getBackOfficeRealmResource(), otherRoles);
    }

    @Override
    @RolesAllowed({Role.ODAS_ADMIN, Role.SOURCING})
    public void createFrontOfficeGroupAndRole(String role) {
        createGroupAndRoles(role, getFrontOfficeRealmResource());
    }


    private String createGroupAndRoles(String roleAndGroupCode, RealmResource realmResource, String... roles) {
        log.info("Creating role and group {}", roleAndGroupCode);
        createGroup(roleAndGroupCode, realmResource);
        var groupId = getGroupId(roleAndGroupCode, realmResource);
        createRole(realmResource, roleAndGroupCode);
        var allRoles = Sets.newHashSet(roles);
        allRoles.add(roleAndGroupCode);
        attachRolesToGroup(realmResource, allRoles, groupId);

        log.info("Role and group {} created", roleAndGroupCode);
        return groupId;
    }

    private void attachRolesToGroup(RealmResource realmResource, Collection<String> roles, String groupId) {
        try {
            var allRoles = roles.stream()
                    .map(r -> realmResource.roles().get(r).toRepresentation())
                    .collect(Collectors.toCollection(Lists::newArrayList));

            var groupResource = realmResource.groups()
                    .group(groupId);

            var realmRolesResource = groupResource.roles()
                    .realmLevel();

            realmRolesResource.add(allRoles);

            var rolesToRemove = groupResource.roles()
                    .realmLevel()
                    .listAll()
                    .stream()
                    .filter(r -> !roles.contains(r.getName()))
                    .toList();

            if (!rolesToRemove.isEmpty()) {
                realmRolesResource.remove(rolesToRemove);
            }

        } catch (ProcessingException | NotFoundException e) {
            var rolesAsString = String.join(", ", roles);
            log.error("Unable to attach roles {} to group {}", rolesAsString, groupId, e);
            throw new GenericTechnicalException("Roles " + rolesAsString + " could not join group " + groupId + " could not be created in keycloak " + e.getMessage(), e);
        }
    }

    private String getGroupId(String groupCode, RealmResource realmResource) {
        return realmResource.groups()
                .groups()
                .stream()
                .filter(gr -> gr.getName().equals(groupCode))
                .findFirst()
                .map(GroupRepresentation::getId)
                .orElseThrow();
    }

    private void createRole(RealmResource realmResource, String roleCode) {
        var role = new RoleRepresentation();
        role.setName(roleCode);
        try {
            realmResource.roles().create(role);
        } catch (ClientErrorException e) {
            if (e.getResponse().getStatus() != Response.Status.CONFLICT.getStatusCode()) {
                throw e;
            }
            log.info("Role {} already exists - try to associate to group", roleCode);
        }
    }

    private void createGroup(String roleCode, RealmResource realmResource) {
        try {
            var groupRepresentation = new GroupRepresentation();
            groupRepresentation.setName(roleCode);
            groupRepresentation.setRealmRoles(newArrayList(roleCode));
            var response = realmResource.groups().add(groupRepresentation);
            var status = HttpStatus.valueOf(response.getStatus());
            if (status.is2xxSuccessful()) {
                log.info("Group {} created", roleCode);
            } else if (status.value() == Response.Status.CONFLICT.getStatusCode()) {
                log.info("Group {} already exists - do nothing", roleCode);
            } else {
                throw new GenericTechnicalException("Group " + roleCode + " could not be created in keycloak : " + status + ", " + response.getStatusInfo().getReasonPhrase());
            }
        } catch (ProcessingException | NotFoundException e) {
            log.error("Unable to create group {}", roleCode, e);
            throw new GenericTechnicalException("Group " + roleCode + " could not be created in keycloak " + e.getMessage(), e);
        }
    }

    @Override
    public void assignToFrontOfficeGroups(String userId, Set<String> groupNames) {
        final var user = getFrontOfficeRealmResource().users().get(userId);

        groupNames.forEach(group -> assignToFrontOfficeGroup(userId, user, group));
    }

    @Override
    public void removeUserFromFrontOfficeGroup(String userId, String groupName) {
        final var user = getFrontOfficeRealmResource().users().get(userId);
        user.leaveGroup(getGroupResourceIdByName(groupName));
    }

    private void assignToFrontOfficeGroup(String userId, UserResource user, String groupName) {
        try {
            user.joinGroup(getGroupResourceIdByName(groupName));
        } catch (ProcessingException | WebApplicationException e) {
            log.error("Unable to assign user {} to fo group {}", userId, groupName, e);
            throw new GenericTechnicalException("Unable to assign user " + userId + " to fo group " + groupName, e);
        }
    }

    private void assignToSourcingGroup(String userId, UserResource user, String groupName) {
        try {
            user.joinGroup(getGroupResourceByNameOrCreate(groupName, getSourcingRealmResource()));
        } catch (ProcessingException | WebApplicationException e) {
            log.error("Unable to assign user {} to sourcing group {}", userId, groupName, e);
            throw new GenericTechnicalException("Unable to assign user " + userId + " to sourcing group " + groupName, e);
        }
    }

    private String getGroupResourceByNameOrCreate(String groupName, RealmResource realmResource) {
        final var group = getGroupResourceByName(groupName, realmResource);
        if (group == null) {
            return createGroupAndRoles(groupName, realmResource);
        }
        return group.toRepresentation().getId();
    }

    private String getGroupResourceIdByName(String groupName) {
        final var group = getGroupResourceByName(groupName, getFrontOfficeRealmResource());
        return group != null ? group.toRepresentation().getId() : null;
    }

    @Override
    public Set<String> getRolesForGroup(String group) {
        var realmResource = getBackOfficeRealmResource();

        return getRolesForGroup(group, realmResource);
    }

    @Override
    public Set<String> getGroupsOfRoles(Collection<String> roles) {
        return roles.stream().distinct()
                .flatMap(this::getGroupsOfApplicationRole)
                .map(GroupRepresentation::getName)
                .collect(Collectors.toSet());
    }

    @Override
    public void clearCaches() {
        CACHES.stream().map(cacheManager::getCache).filter(Objects::nonNull).forEach(Cache::clear);
    }

    @Override
    @Retryable(
            include = ServerErrorException.class,
            maxAttempts = 10,
            // Try during around two hours with increasing delays
            backoff = @Backoff(delay = 5_000, multiplier = 1.8),
            label = "Reset FO password"
    )
    @Async
    public void resetFOPassword(String userId) {
        var redirectUri = UriBuilder.fromUri(frontOfficeBaseUrl).queryParam(PASSWORD_CONFIRMED_QUERY_PARAM, true)
                .toTemplate();

        try {
            getFrontOfficeRealmResource().users().get(userId).executeActionsEmail(
                    "web-front",
                    redirectUri,
                    (int) TimeUnit.DAYS.toSeconds(1),
                    List.of(UserRepresentation.UPDATE_PASSWORD_ACTION_KEYCLOAK_ATTRIBUTE));
            log.debug("Successfully setting reset password for user {}", userId);
        } catch (ServerErrorException e) {
            log.warn("Caught exception when resetting FO password - rethrow to delegate to retry handler", e);
            throw e;
        }
    }

    private Stream<GroupRepresentation> getGroupsOfApplicationRole(String role) {
        Stream<GroupRepresentation> groups = Stream.empty();
        if (Role.isOrganizationRoleOrGroup(role)) {
            try {
                groups = getBackOfficeRealmResource().roles().get(role).getRoleGroupMembers().stream();
            } catch (NotFoundException e) {
                log.warn("Unable to find role {} on backoffice - considering empty group list", role, e);
            }
        }
        return groups;
    }

    private Set<String> getRolesForGroup(String group, RealmResource realmResource) {
        return realmResource.groups()
                .groups()
                .stream()
                .filter(gr -> gr.getName().equals(group))
                .flatMap(g -> realmResource.groups().group(g.getId()).roles().realmLevel().listAll().stream())
                .map(RoleRepresentation::getName)
                .collect(Collectors.toSet());
    }

    @Override
    @Cacheable(FO_ALL_USERS_CACHE)
    public List<UserRepresentation> findAllFrontOfficeUsers() {
        return findAllFrontOfficeUsersForAnonymizationOnly()
                .stream()
                .map(UserRepresentation::forUser)
                .toList();
    }

    @Override
    public ArrayList<org.keycloak.representations.idm.UserRepresentation> findAllFrontOfficeUsersForAnonymizationOnly() {
        try {
            var count = getFrontOfficeRealmResource()
                    .users()
                    .count();
            var pageSize = 100;
            var nbPages = count / pageSize + 1;
            var index = -1;
            var allUsers = new ArrayList<org.keycloak.representations.idm.UserRepresentation>();
            while (index++ < nbPages) {
                allUsers.addAll(getFrontOfficeRealmResource()
                        .users()
                        .list(index * pageSize, pageSize));
            }
            return allUsers;
        } catch (ProcessingException | WebApplicationException e) {
            log.error("Unable to retrieve users list", e);
            throw new GenericTechnicalException("Unable to retrieve users list ", e);
        }
    }

    @Override
    public void setFrontOfficeUserPassword(SetFrontOfficeUserPasswordCommandDTO setFrontOfficeUserPasswordCommandDTO) {
        var userId = setFrontOfficeUserPasswordCommandDTO.getUserId();
        var userResource = getFrontOfficeRealmResource().users().get(userId);
        final var passwordCred = new CredentialRepresentation();
        passwordCred.setTemporary(false);
        passwordCred.setType(CredentialRepresentation.PASSWORD);
        passwordCred.setValue(setFrontOfficeUserPasswordCommandDTO.getPassword());
        try {
            userResource.resetPassword(passwordCred);
        } catch (WebApplicationException e) {
            log.warn("Unable to reset password for {}, got status {}", userId, e.getResponse().getStatus());
            throw new IllegalArgumentException("invalid password not following rules");
        }
    }

    @Override
    public String confirmFOUserFromBO(ConfirmFOUserFromBOCommandDTO confirmFOUserFromBOCommandDTO) {
        var userId = confirmFOUserFromBOCommandDTO.getUserId();
        var userResource = getFrontOfficeRealmResource().users().get(userId);
        final var passwordCred = new CredentialRepresentation();
        var pwd = com.erhgo.utils.StringUtils.generateRandomPassword();
        passwordCred.setTemporary(true);
        passwordCred.setType(CredentialRepresentation.PASSWORD);
        passwordCred.setValue(pwd);
        userResource.resetPassword(passwordCred);
        return pwd;
    }

    @Override
    @CacheEvict(value = FO_USER_REPRESENTATION_CACHE, key = "#userId")
    public void setFrontOfficeUserName(String userId, SaveUserNameCommandDTO setUserNameCommandDTO) {
        var userRepresentation = getUserRepresentation(userId).orElseThrow();
        userRepresentation.setFirstName(setUserNameCommandDTO.getFirstName());
        userRepresentation.setLastName(setUserNameCommandDTO.getLastName());
        var users = getFrontOfficeRealmResource().users();
        var userResource = users.get(userId);
        userResource.update(userRepresentation);
    }

    @Override
    public boolean deleteFrontOfficeUser(String userId) {
        var response = getFrontOfficeRealmResource().users().delete(userId);
        var status = HttpStatus.valueOf(response.getStatus());
        if (status.is2xxSuccessful()) {
            return true;
        } else {
            if (HttpStatus.NOT_FOUND.equals(status)) {
                return false;
            } else {
                throw new GenericTechnicalException("Unable to delete user in keycloak " + userId + "; Status = " + status.value());
            }
        }
    }


    @Override
    @CacheEvict(value = FO_USER_REPRESENTATION_CACHE, key = "#userId")
    public boolean updateFOEmailAndUsername(String userId, String nextEmail, String firstNameParam, String lastNameParam) {
        var firstName = Optional.ofNullable(firstNameParam);
        var lastName = Optional.ofNullable(lastNameParam);
        var email = Optional.ofNullable(nextEmail).filter(StringUtils::isNotBlank);

        return updateUserCommon(userId, firstName, lastName, email);
    }

    @Override
    @CacheEvict(value = FO_USER_REPRESENTATION_CACHE, key = "#userId")
    public boolean updateFOEmail(String userId, String nextEmail) {
        return updateUserCommon(userId, Optional.empty(), Optional.empty(), Optional.of(nextEmail));
    }

    @Override
    @Async
    public void removePhoneForFOUser(String userId) {
        var userResource = getFrontOfficeRealmResource().users().get(userId);
        var user = userResource.toRepresentation();
        var miscAttributes = user.getAttributes();
        if (miscAttributes != null && miscAttributes.containsKey(UserRepresentation.PHONE_ATTRIBUTE)) {
            miscAttributes.remove(UserRepresentation.PHONE_ATTRIBUTE);
            user.setAttributes(miscAttributes);
            userResource.update(user);
        }
    }

    @Override
    public Map<String, Set<String>> searchBOGroupsWithRoles(String query) {
        return getBackOfficeRealmResource().groups().groups(query, 0, 10_000, false).stream().collect(Collectors.toMap(GroupRepresentation::getName, k -> new HashSet<>(k.getRealmRoles())));
    }

    @Override
    @CacheEvict(value = SOURCING_USER_REPRESENTATION_CACHE, key = "#userId")
    public void assignUserToSourcingGroup(String userId, String organizationCode) {
        final var user = getSourcingRealmResource().users().get(userId);
        assignToSourcingGroup(userId, user, organizationCode);

    }

    @Override
    public UserRepresentation getSourcingUserWithGroups(String userId) {
        var userResource = getSourcingRealmResource().users().get(userId);
        var user = Optional.of(userResource)
                .map(UserResource::toRepresentation)
                .map(UserRepresentation::forUser)
                .orElseThrow(() -> new EntityNotFoundException(userId, UserRepresentation.class));
        user.setGroups(Optional.ofNullable(userResource.groups())
                .map(c -> c.stream().map(GroupRepresentation::getName).toList())
                .orElseGet(Collections::emptyList));
        return user;
    }

    @Override
    @Cacheable(SOURCING_USER_REPRESENTATION_CACHE)
    public Optional<UserRepresentation> getSourcingUser(String userId) {
        try {
            return Optional.of(getSourcingRealmResource().users().get(userId))
                    .map(UserResource::toRepresentation)
                    .map(UserRepresentation::forUser)
                    ;
        } catch (NotFoundException e) {
            return Optional.empty();
        }
    }

    @Override
    @Cacheable(SOURCING_USER_BY_EMAIL_CACHE)
    public Optional<UserRepresentation> getSourcingUserFromEmail(String email) {
        return getUserFromEmail(email, getSourcingRealmResource());
    }

    private Optional<UserRepresentation> getUserFromEmail(String email, RealmResource realmResource) {
        return realmResource.users().searchByEmail(email, true).stream().findFirst().map(UserRepresentation::forUser);
    }

    private List<org.keycloak.representations.idm.UserRepresentation> getKeycloakUsersForGroup(String organizationCode) {
        return Optional.ofNullable(getGroupResourceByName(organizationCode, getSourcingRealmResource()))
                .map(g -> g.members(0, MAX_NUMBER_OF_USERS_PER_ORGANIZATION))
                .orElseGet(() -> {
                    log.error("No sourcing group for {}", organizationCode);
                    return Collections.emptyList();
                });
    }

    @Override
    public List<UserRepresentation> getEnabledSourcingUsersForGroup(String organizationCode) {
        return getKeycloakUsersForGroup(organizationCode).stream()
                .filter(org.keycloak.representations.idm.UserRepresentation::isEnabled)
                .map(UserRepresentation::forUser)
                .toList();
    }

    @Override
    public List<UserRepresentation> getSourcingUsersForGroup(String organizationCode) {
        return getKeycloakUsersForGroup(organizationCode).stream()
                .map(UserRepresentation::forUser)
                .toList();
    }

    @Override
    @Caching(evict = {
            @CacheEvict(value = SOURCING_USER_REPRESENTATION_CACHE, key = "#userId"),
            @CacheEvict(value = SOURCING_USER_BY_EMAIL_CACHE, key = "#email")
    })
    public void updateUser(String userId, String email, String phone, String fullname) {
        var userResource = getSourcingRealmResource().users().get(userId);
        var userRepresentation = userResource.toRepresentation();
        var attributes = userRepresentation.getAttributes();
        attributes.put(UserRepresentation.FULLNAME_ATTRIBUTE, List.of(fullname));
        attributes.put(UserRepresentation.PHONE_ATTRIBUTE, List.of(phone));
        userRepresentation.setEmail(email);
        userResource.update(userRepresentation);
    }

    @Override
    @CacheEvict(value = SOURCING_USER_REPRESENTATION_CACHE, key = "#userId")
    public void updateUserSiret(String userId, String siret) {
        var userResource = getSourcingRealmResource().users().get(userId);
        var userRepresentation = userResource.toRepresentation();
        var attributes = userRepresentation.getAttributes();
        attributes.put(UserRepresentation.SIRET_ATTRIBUTE, List.of(siret));
        userResource.update(userRepresentation);
    }

    private boolean updateUserCommon(String userId, Optional<String> firstName, Optional<String> lastName, Optional<String> email) {
        var userResource = getFrontOfficeRealmResource().users().get(userId);
        var user = userResource.toRepresentation();

        if (
                firstName.filter(f -> !f.equals(user.getFirstName())).isEmpty()
                        && lastName.filter(f -> !f.equals(user.getLastName())).isEmpty()
                        && email.filter(f -> !f.equals(user.getEmail())).isEmpty()
        ) {
            log.debug("Ignoring updates for {}: no change detected", userId);
            return false;
        }

        email.ifPresent(user::setEmail);
        email.ifPresent(user::setUsername);
        firstName.ifPresent(user::setFirstName);
        lastName.ifPresent(user::setLastName);

        try {
            userResource.update(user);
            return true;
        } catch (ClientErrorException e) {
            if (e.getResponse().getStatus() != Response.Status.CONFLICT.getStatusCode()) {
                throw e;
            }
            throw new EntityAlreadyExistException(EMAIL_KEY, UserRepresentation.class, email.orElse("<illegal state>"));
        }
    }

    @Override
    @CacheEvict(value = SOURCING_USER_REPRESENTATION_CACHE, key = "#userId")
    public void disableUser(String userId) {
        setUserEnabled(userId, false);
    }


    @Override
    @CacheEvict(value = SOURCING_USER_REPRESENTATION_CACHE, key = "#userId")
    public void enableUser(String userId) {
        setUserEnabled(userId, true);
    }

    @Override
    public void createSourcingGroupAndRole(String groupAndRole) {
        createGroupAndRoles(groupAndRole, getSourcingRealmResource());
    }

    private void setUserEnabled(String userId, boolean enabled) {
        var userResource = getSourcingRealmResource().users().get(userId);
        var userRepresentation = userResource.toRepresentation();
        userRepresentation.setEnabled(enabled);
        userResource.update(userRepresentation);
        log.info("User {} {} in Keycloak sourcing realm", userId, enabled ? "activated" : "deactivated");
    }
}
