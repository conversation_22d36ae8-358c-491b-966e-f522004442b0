package com.erhgo.services.keycloak;

import com.erhgo.security.Role;
import com.erhgo.utils.DateTimeUtils;
import com.erhgo.utils.StringUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.io.Serializable;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.stream.Stream;

@Accessors(chain = true)
@Data
public class UserRepresentation implements Serializable {
    public static final String FULLNAME_ATTRIBUTE = "fullname";
    public static final String PHONE_ATTRIBUTE = "phone";
    public static final String SIRET_ATTRIBUTE = "siret";
    public static final String LAST_LOGIN_TIMESTAMP_ATTRIBUTE = "lastLoginTimestamp";
    public static final String UPDATE_PASSWORD_ACTION_KEYCLOAK_ATTRIBUTE = "UPDATE_PASSWORD";

    private String firstName;
    private String lastName;
    private String email;
    private String username;
    private Long createdTimestamp;
    private String id;
    private List<String> groups;
    private Boolean enabled;
    private Map<String, List<String>> miscAttributes;
    protected List<String> requiredActions;

    public static UserRepresentation forUser(org.keycloak.representations.idm.UserRepresentation userRepresentation) {
        return new UserRepresentation()
                .setFirstName(userRepresentation.getFirstName())
                .setLastName(userRepresentation.getLastName())
                .setEmail(userRepresentation.getEmail())
                .setUsername(userRepresentation.getUsername())
                .setCreatedTimestamp(userRepresentation.getCreatedTimestamp())
                .setId(userRepresentation.getId())
                .setEnabled(userRepresentation.isEnabled())
                .setMiscAttributes(userRepresentation.getAttributes())
                .setGroups(userRepresentation.getGroups())
                .setRequiredActions(userRepresentation.getRequiredActions())
                ;
    }

    public String getSiret() {
        return getAttributeValue(SIRET_ATTRIBUTE);
    }

    @Nullable
    private String getAttributeValue(String value) {
        return Optional.ofNullable(miscAttributes)
                .map(m -> m.get(value))
                .map(Collection::stream)
                .flatMap(Stream::findFirst)
                .orElse(null);
    }

    public String getFullname() {
        return Optional.ofNullable(miscAttributes)
                .map(m -> m.get(FULLNAME_ATTRIBUTE))
                .map(Collection::stream)
                .flatMap(Stream::findFirst)
                .orElse((firstName == null ? "" : firstName) + " " + (lastName == null ? "" : lastName));
    }

    public List<String> getGroupsRelatedToOrganizations() {
        return groups == null ? List.of() : groups.stream().filter(Role::isOrganizationRoleOrGroup).toList();
    }

    public void setSiret(String siret) {
        if (miscAttributes == null) {
            miscAttributes = new HashMap<>();
        }
        miscAttributes.put(SIRET_ATTRIBUTE, List.of(siret));
    }

    @JsonIgnore
    public String getSingleGroupsRelatedToOrganization() {
        return getGroupsRelatedToOrganizations().stream().findFirst().orElse(null);
    }

    public String getSourcingPhoneNumber() {
        return getAttributeValue(PHONE_ATTRIBUTE);
    }

    public String getFOPhoneNumber() {
        return getAttributeValue(PHONE_ATTRIBUTE);
    }

    public String getFirstNameOrFullName() {
        return Optional.ofNullable(firstName).orElse(getFullname());
    }


    public OffsetDateTime getLastConnectionDate() {
        var hasAlreadyAuthenticated = getRequiredActions() == null || !getRequiredActions().contains(UPDATE_PASSWORD_ACTION_KEYCLOAK_ATTRIBUTE);
        var lastConnectionDate = Optional.ofNullable(miscAttributes)
                .map(m -> m.get(LAST_LOGIN_TIMESTAMP_ATTRIBUTE))
                .flatMap(l -> l.stream().findFirst())
                .map(Long::parseLong)
                .orElse(hasAlreadyAuthenticated ? getCreatedTimestamp() : null);

        return DateTimeUtils.timestampToOffsetDateTime(lastConnectionDate);
    }

    public String getLastNameNeverEmpty() {
        return StringUtils.trimToDefault(lastName, " - ");
    }

    public String getFirstNameNeverEmpty() {
        return StringUtils.trimToDefault(firstName, " - ");
    }
}
