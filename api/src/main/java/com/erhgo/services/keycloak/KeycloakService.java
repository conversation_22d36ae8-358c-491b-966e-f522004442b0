package com.erhgo.services.keycloak;

import com.erhgo.openapi.dto.ConfirmFOUserFromBOCommandDTO;
import com.erhgo.openapi.dto.SaveUserNameCommandDTO;
import com.erhgo.openapi.dto.SetFrontOfficeUserPasswordCommandDTO;
import com.erhgo.services.AbstractService;
import com.erhgo.services.dto.UserKeycloakRepresentation;
import org.springframework.data.domain.Page;

import java.util.*;

public interface KeycloakService {

    String BO_USER_REPRESENTATION_CACHE = "keycloakBackUserRepresentation";
    String SOURCING_USER_REPRESENTATION_CACHE = "keycloakSourcingUserRepresentation";
    String SOURCING_USER_BY_EMAIL_CACHE = "keycloakSourcingUserByEmail";
    String FO_USER_REPRESENTATION_CACHE = "keycloakFrontUserRepresentation";
    String BO_USER_FULL_NAME_CACHE = "keycloakUserFullname";
    String FO_ALL_USERS_CACHE = "keycloakAllFOUsers";

    /**
     * @param userKeycloakRepresentation
     * @return created user UUID in string format
     */
    String createUserInBackOfficeRealm(UserKeycloakRepresentation userKeycloakRepresentation);

    String createUserInFrontOfficeRealm(UserKeycloakRepresentation userKeycloakRepresentation);

    AbstractService.PageDTOAdapter<UserRepresentation> getBackOfficeGroupMembersPaginatedResource(String groupName, int offset, int max);

    int countBackOfficeGroupMembers(String groupName);

    void createBackOfficeGroupAndRoles(String groupAndRole, String... otherRoles);

    Optional<UserRepresentation> getBackOfficeUserProfile(String userId);

    String getBackOfficeUserFullnameOrEmpty(String keycloakId);

    Optional<UserRepresentation> getFrontOfficeUserProfile(String userId);

    Optional<UserRepresentation> getFrontOfficeUserProfileWithGroups(String userId);

    Page<UserRepresentation> searchFrontOfficeUsersPaginated(String search, int page, int size);

    List<UserRepresentation> getBackOfficeUsers();

    UserRepresentation getFOUserRepresentationByEmail(String email);

    void createFrontOfficeGroupAndRole(String role);

    List<UserRepresentation> findAllFrontOfficeUsers();

    void assignToFrontOfficeGroups(String userId, Set<String> groupNames);

    void removeUserFromFrontOfficeGroup(String userId, String groupName);

    Set<String> getRolesForGroup(String group);

    Set<String> getGroupsOfRoles(Collection<String> roles);

    void clearCaches();

    void resetFOPassword(String userId);

    ArrayList<org.keycloak.representations.idm.UserRepresentation> findAllFrontOfficeUsersForAnonymizationOnly();

    void setFrontOfficeUserPassword(SetFrontOfficeUserPasswordCommandDTO setFrontOfficeUserPasswordCommandDTO);

    String confirmFOUserFromBO(ConfirmFOUserFromBOCommandDTO confirmFOUserFromBOCommandDTO);

    void setFrontOfficeUserName(String userId, SaveUserNameCommandDTO setUserNameCommandDTO);

    boolean deleteFrontOfficeUser(String userId);

    boolean updateFOEmailAndUsername(String userId, String nextEmail, String firstName, String lastName);

    boolean updateFOEmail(String userId, String nextEmail);

    void removePhoneForFOUser(String userId);

    Map<String, Set<String>> searchBOGroupsWithRoles(String query);
}
