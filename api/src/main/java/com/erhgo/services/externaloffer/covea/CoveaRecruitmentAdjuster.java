package com.erhgo.services.externaloffer.covea;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.exceptions.openai.AbstractRetryableGenerationException;
import com.erhgo.domain.externaloffer.ExternalOffer;
import com.erhgo.openapi.dto.CreateOrUpdateFullRecruitmentCommandDTO;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.externaloffer.RecruitmentAdjuster;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.generation.AbstractGenerationService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CoveaRecruitmentAdjuster extends AbstractGenerationService<String, String> implements RecruitmentAdjuster {


    public CoveaRecruitmentAdjuster(YamlPromptReader yamlPromptReader,
                                    GenerationClient generationClient,
                                    PromptConfig formatTextToHtmlPromptConfig,
                                    ErhgoOccupationRepository erhgoOccupationRepository,
                                    SecurityService securityService) {
        super(yamlPromptReader, generationClient, formatTextToHtmlPromptConfig, erhgoOccupationRepository, securityService);
    }

    @Override
    public void adjustRecruitmentCreationCommand(ExternalOffer externalOffer, CreateOrUpdateFullRecruitmentCommandDTO recruitmentCreationCommand, AtsGetOfferConfig atsConfig) {
        if (!acceptOffer(atsConfig)) return;
        try {
            var newDescription = generate(recruitmentCreationCommand.getDescription()).getResult();
            if (!StringUtils.isBlank(newDescription)) {
                recruitmentCreationCommand.setDescription(newDescription);
            }
        } catch (RuntimeException e) {
            log.error("Unable to adjust recruitment creation command for {} - continue as is", externalOffer, e);
        }
    }

    private boolean acceptOffer(AtsGetOfferConfig config) {
        return config != null && config.getAtsCode() != null && config.getAtsCode().equalsIgnoreCase("covea");
    }

    @Override
    protected String handleResponse(String content) throws AbstractRetryableGenerationException {
        return content;
    }
}
