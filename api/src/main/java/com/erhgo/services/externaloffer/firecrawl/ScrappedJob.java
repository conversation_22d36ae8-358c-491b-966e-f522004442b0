package com.erhgo.services.externaloffer.firecrawl;

import com.erhgo.openapi.dto.TypeContractCategoryDTO;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.codehaus.plexus.util.StringUtils;

import java.net.URI;
import java.net.URISyntaxException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ScrappedJob extends AbstractRemoteOfferContent<ScrappedJob> {

    @JsonProperty("url")
    private String id;

    @JsonProperty("title")
    private String offerTitle;

    @JsonProperty("contract")
    private String contractType;

    @JsonProperty("salary")
    private String salary;

    @JsonProperty("location")
    private String location;

    @JsonProperty("job_description")
    private String jobDescription;

    @JsonProperty("organization_description")
    private String organizationDescription;

    private LocalDateTime publicationDate;


    @JsonIgnore
    @Override
    public LocalDateTime getLastModificationDate() {
        return null;
    }

    @Override
    public List<String> getCriterias() {
        return List.of();
    }


    @Override
    public String getRemoteRecruiterCode() {
        return Optional.ofNullable(StringUtils.trim(id))
                .map(url -> {
                    try {
                        var uri = new URI(url);
                        return Optional.ofNullable(uri.getScheme())
                                .filter(scheme -> uri.getHost() != null)
                                .map(scheme -> scheme + "://" + uri.getHost())
                                .orElse(null);
                    } catch (URISyntaxException e) {
                        return null;
                    }
                })
                .orElse(null);
    }

    @JsonIgnore
    @Override
    public List<Integer> getSalaryValues() {
        return Collections.emptyList();
    }

    @Override
    public String getDescription() {
        return jobDescription;
    }

    @Override
    public TypeContractCategoryDTO getTypeContractCategory() {
        if (contractType == null) return null;
        return contractType.contains("CDI") ?
                TypeContractCategoryDTO.PERMANENT :
                TypeContractCategoryDTO.TEMPORARY;
    }

    @Override
    public String getLocationIndication() {
        return location;
    }

    @Override
    public boolean isInFrance() {
        return true;
    }

    @Override
    public String getOrganizationDescription() {
        return organizationDescription;
    }

    @Override
    public boolean shouldNotCreateNewOffer(AtsGetOfferConfig config) {
        return false;
    }

    @Override
    public boolean shouldNotGenerateRecruitment(AtsGetOfferConfig config) {
        return !isInsideAURA();
    }

}
