package com.erhgo.services.externaloffer.talentsoft;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.externaloffer.GenericAtsClient;
import com.erhgo.services.externaloffer.candidature.AbstractATSWithHttpClientNotificationSender;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.send.SendCandidaturesRecruiterProvider;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import com.erhgo.utils.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import okhttp3.MultipartBody;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

// see https://developers.cegid.com/api-details#api=cegid-talentsoft-recruiting-frontoffice&operation=ApplicationWithoutAccountController_PostApplicantApplication
@Slf4j
@Service
public class TalentsoftApiCandidatureNotificationScheduler extends AbstractATSWithHttpClientNotificationSender {

    private final ObjectMapper objectMapper;
    private final GenericAtsClient genericAtsClient;

    public TalentsoftApiCandidatureNotificationScheduler(
            RecruitmentCandidatureRepository recruitmentCandidatureRepository,
            SpontaneousCandidatureRepository spontaneousCandidatureRepository,
            AbstractCandidatureRepository abstractCandidatureRepository,
            KeycloakService keycloakService,
            ConfigurablePropertyRepository configurablePropertyRepository,
            UserProfileCompetencesExportService userProfileCompetencesExportService,
            SecurityService securityService,
            List<AtsSendCandidaturesConfig> talentsoftSendCandidaturesConfig,
            TransactionTemplate transactionTemplate,
            ObjectMapper objectMapper,
            SendCandidaturesRecruiterProvider sendCandidaturesRecruiterProvider,
            GenericAtsClient genericAtsClient
    ) {
        super(keycloakService, configurablePropertyRepository, userProfileCompetencesExportService, transactionTemplate, recruitmentCandidatureRepository, spontaneousCandidatureRepository, talentsoftSendCandidaturesConfig, securityService, abstractCandidatureRepository, sendCandidaturesRecruiterProvider);
        this.objectMapper = objectMapper;
        this.genericAtsClient = genericAtsClient;
    }

    @Override
    @Scheduled(initialDelay = 45, fixedRate = 60, timeUnit = TimeUnit.MINUTES)
    @SchedulerLock(name = "handleNewCandidaturesForTalentsoft", lockAtLeastFor = "35M")
    public void handleNewCandidatures() {
        super.handleNewCandidatures();
    }

    @Override
    protected Request buildSendCandidatureRequest(UserRepresentation userRepresentation, RecruitmentCandidature candidature, AtsSendCandidaturesConfig config) throws IOException {
        var fullProfile = getProfile(candidature, userRepresentation.getFullname(), ProfileCompetencesViewObject.AnonymousMode.BOTH);

        var applicantApplication = new TalentsoftApplicantApplication()
                .setApplicant(new TalentsoftApplicantApplication.Applicant()
                        .setPersonalInformation(
                                new TalentsoftApplicantApplication.Applicant.PersonalInformation()
                                        .setEmail(userRepresentation.getEmail())
                                        .setFirstName(userRepresentation.getFirstNameNeverEmpty())
                                        .setLastName(userRepresentation.getLastNameNeverEmpty())
                                        .setPhoneNumber(candidature.getUserProfile().getPhoneNumber())
                        ))
                .setApplication(new TalentsoftApplicantApplication.Application()
                        .setOfferReference(candidature.getExternalOfferId())
                        .setOrigin(Map.of("code", Long.parseLong(config.getTrackingId())))
                )
                .setUploadedFiles(List.of(new TalentsoftApplicantApplication.UploadedFileInfo().setFileTypeId(config.getCustom().get("fileTypeId"))));
        var applicantJson = objectMapper.writeValueAsString(applicantApplication);

        var builder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("applicantApplication", applicantJson)
                .addFormDataPart("pdc", "pdc.pdf", RequestBody.create(fullProfile.readAllBytes(), okhttp3.MediaType.parse("application/pdf")));
        return new Request.Builder()
                .url(config.getCandidatureNotificationUrl())
                .header("Authorization", "Bearer " + genericAtsClient.generateToken(config.getTokenAuthentication()))
                .post(builder.build())
                .build();
    }

    @Override
    protected boolean isCandidatureOnClosedRecruitment(int code, String body) {
        return HttpStatus.BAD_REQUEST.value() == code && StringUtils.normalizeLowerCase(body).contains("existe pas");
    }
}
