package com.erhgo.services.externaloffer.covea;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.externaloffer.ExternalOffer;
import com.erhgo.openapi.dto.AtsOfferSimulatedDTO;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.services.externaloffer.AbstractATSSynchronizer;
import com.erhgo.services.externaloffer.OfferDataExtractor;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.parsers.GenericJobJsonParser;
import com.erhgo.services.externaloffer.recruiterdispatcher.fetch.ExternalOfferRecruiterProvider;
import com.erhgo.services.recruitmentimporter.OfferCsvParserService;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class CoveaSynchronizer extends AbstractATSSynchronizer<CoveaJob> {

    private final OfferCsvParserService offerCsvParserService;
    private final SimpleSftpClient sftpClient;

    @Getter
    private final GenericJobJsonParser<CoveaJob> parser;

    private static final String FALLBACK_CSV_PATH = "/tmp/covea.csv";

    public CoveaSynchronizer(ConfigurablePropertyRepository configurablePropertyRepository,
                             ExternalOfferRepository repository,
                             ExternalOfferRecruiterProvider externalOfferRecruiterProvider,
                             AtsGetOfferConfig atsConfig,
                             ApplicationContext applicationContext,
                             OfferCsvParserService offerCsvParserService,
                             GenericJobJsonParser<CoveaJob> parser,
                             SimpleSftpClient sftpClient) {
        super(configurablePropertyRepository, repository, externalOfferRecruiterProvider, atsConfig, applicationContext);
        this.offerCsvParserService = offerCsvParserService;
        this.parser = parser;
        this.sftpClient = sftpClient;
    }

    @Override
    protected List<ExternalOffer> modifyOffers(Map<String, CoveaJob> current, Map<String, ExternalOffer> previous) {
        log.debug("Ignored modification for Covea");
        return new ArrayList<>();
    }

    @Override
    protected Page<CoveaJob> fetchOffers(Map<String, List<String>> queryParams) {
        try (var inputStream = getCsvInputStream()) {
            var jobs = offerCsvParserService.parseOffersFromCsv(inputStream, CoveaJob.class).stream()
                    .map(a -> {
                        try {
                            return a.setRawContent(parser.getObjectMapper().writeValueAsString(a));
                        } catch (JsonProcessingException e) {
                            log.error("Unable to convert job {} in json", a, e);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .toList();
            log.info("Successfully parsed {} Covea job offers", jobs.size());
            if (jobs.isEmpty()) {
                throw new GenericTechnicalException("Covea CSV file is empty, skip");
            }
            return new PageImpl<>(jobs);
        } catch (RuntimeException | IOException e) {
            log.error("Error parsing Covea offers", e);
            throw new GenericTechnicalException("Failed to parse Covea offers", e);
        }
    }

    private @NotNull InputStream getCsvInputStream() {
        try {
            return fetchCsvUsingFtp();
        } catch (RuntimeException e) {
            log.error("Using fallback CSV file {}", FALLBACK_CSV_PATH, e);
            return failoverToFsCsvFile();
        }
    }

    private @NotNull InputStream fetchCsvUsingFtp() {
        return sftpClient.streamLatestDailyOfferFile(atsConfig.getRemoteUrl(), atsConfig.getTokenAuthentication().getClientId(), atsConfig.getTokenAuthentication().getClientSecret());
    }

    private InputStream failoverToFsCsvFile() {
        log.info("Parsing Covea offers from CSV file: {}", FALLBACK_CSV_PATH);
        try {
            var path = Path.of(FALLBACK_CSV_PATH);
            if (!Files.exists(path)) {
                log.warn("CSV file not found at path: {}", FALLBACK_CSV_PATH);
                throw new GenericTechnicalException("CSV file not found at path: %s".formatted(FALLBACK_CSV_PATH));
            }
            return Files.newInputStream(path);
        } catch (IOException e) {
            log.error("Error reading CSV file: {}", FALLBACK_CSV_PATH, e);
            throw new GenericTechnicalException("Failed to read Covea CSV file", e);
        }
    }

    @Override
    protected String fetchRemoteOffersRawContent(Map queryParams) {
        throw new UnsupportedOperationException("Not implemented");
    }

    @Override
    public void simulateAtsOffer(AtsOfferSimulatedDTO offer) {
        throw new UnsupportedOperationException("Not implemented");
    }

    @Override
    protected OfferDataExtractor<CoveaJob> getOfferDataExtractor() {
        throw new UnsupportedOperationException("Not implemented");
    }
}
