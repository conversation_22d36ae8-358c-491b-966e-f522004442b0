package com.erhgo.services.externaloffer.talentsoft;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
@Data
public class TalentsoftApplicantApplication {
    @Accessors(chain = true)
    @Data
    public static class Applicant {
        @Accessors(chain = true)
        @Data
        public static class PersonalInformation {
            private String phoneNumber;
            private String email;
            private String lastName;
            private String firstName;
        }

        private Map<String, String> jobPreferences = new HashMap<>();
        private PersonalInformation personalInformation;
    }

    @Accessors(chain = true)
    @Data
    public static class Application {
        private String type = "offer";
        private String offerReference;
        private Map<String, Object> origin;
    }

    @Accessors(chain = true)
    @Data
    public static class UploadedFileInfo {
        private String key = "pdc";
        private String fileTypeId;
        private String description = "profil de compétence #jenesuisPASunCV";
    }

    @JsonProperty
    private List<UploadedFileInfo> uploadedFiles = new ArrayList<>();
    @JsonProperty
    private Applicant applicant;
    @JsonProperty
    private Application application;
}
