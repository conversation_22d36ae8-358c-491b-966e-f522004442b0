package com.erhgo.services.externaloffer.limiter;

import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.PerOfferATSConfigurationItem;
import com.erhgo.services.externaloffer.recruiterdispatcher.PerOfferATSConfigurationItemRepository;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Predicate;

@Service("perOfferConfigCodeAndAuraInOutLimiter")
@Slf4j
public class PerOfferConfigCodeAndAuraInOutLimiter extends PerAuraInOutLimiter {

    private final PerOfferATSConfigurationItemRepository repository;

    public PerOfferConfigCodeAndAuraInOutLimiter(ConfigurablePropertyRepository configurablePropertyRepository, PerOfferATSConfigurationItemRepository repository) {
        super(configurablePropertyRepository);
        this.repository = repository;
    }

    @Override
    public <A extends AbstractRemoteOfferContent<A>> Map<Predicate<A>, Integer> limits(AtsGetOfferConfig config) {
        Map<Predicate<A>, Integer> limits = new HashMap<>();
        repository.findByAtsCodeAndConfigCode(config.getAtsCode(), config.getConfigCode()).stream().map(PerOfferATSConfigurationItem::getRemoteRecruiterCode)
                .forEach(configCode -> {
                    var localConfig = config.clone().setConfigCode(configCode);
                    Predicate<A> predicateOnConfig = o -> configCode.equalsIgnoreCase(StringUtils.trimToEmpty(o.getRemoteRecruiterCode()));
                    Map<Predicate<A>, Integer> superLimits = super.limits(localConfig);
                    superLimits.forEach((key, value) -> limits.put((key).and(predicateOnConfig), value));
                    log.debug("=> Per offer limit, config {}: limiting, per offer to {} ", localConfig.getAtsAndConfigCode(), Joiner.on(", ").join(superLimits.values()));
                });
        log.debug("Using %d limits for ATS %s".formatted(limits.size(), config.getAtsCode()));
        return limits;
    }
}
