package com.erhgo.services.externaloffer;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.Authentication;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.time.Instant;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import static com.erhgo.utils.StringUtils.buildUri;

@Service
@Slf4j
@RequiredArgsConstructor
public class GenericAtsClient {
    private static final long TIMEOUT_IN_SECONDS = 200;
    private static final long DEFAULT_TOKEN_LIFETIME_SECONDS = 240;
    private static final double SAFETY_MARGIN_FACTOR = 0.7;

    private final ConcurrentHashMap<String, CachedToken> tokenCache = new ConcurrentHashMap<>();
    private final RestTemplateBuilder restTemplateBuilder;
    private RestTemplate restTemplate;

    private record CachedToken(String token, Instant expiryTime) {
        boolean isValid() {
            return Instant.now().isBefore(expiryTime);
        }
    }

    @PostConstruct
    public GenericAtsClient initializeTemplate() {
        log.info("Initializing ATS client");
        this.restTemplate = restTemplateBuilder
                .setReadTimeout(Duration.ofSeconds(TIMEOUT_IN_SECONDS))
                .setConnectTimeout(Duration.ofSeconds(TIMEOUT_IN_SECONDS))
                .build();
        return this;
    }

    @Retryable(
            retryFor = ATSRetryableException.class,
            maxAttempts = 5,
            backoff = @Backoff(delay = 2000L, multiplier = 1.5),
            label = "Fetch ATS Offer"
    )
    public String fetch(AtsGetOfferConfig config, Optional<Map<String, List<String>>> queryParams) {
        var uri = buildUri(config.getRemoteUrl(), queryParams);
        try {
            var headers = new HttpHeaders();
            if (config.requiresTokenGeneration()) {
                headers.add("Authorization", "Bearer " + generateToken(config.getTokenAuthentication()));
            } else if (config.isAuthenticated()) {
                headers.setBasicAuth(config.getBasicAuthentication().split(":")[0], config.getBasicAuthentication().split(":")[1]);
            }
            config.getCustomHeaders().forEach(c -> {
                var indexOfSeparator = c.indexOf(":");
                headers.add(c.substring(0, indexOfSeparator), c.substring(indexOfSeparator + 1));
            });
            log.debug("Using URI {} to fetch offers for {}", uri, config.getAtsAndConfigCode());
            var response = restTemplate.exchange(uri, HttpMethod.GET, new HttpEntity<>(headers), String.class);
            if (!response.getStatusCode().is2xxSuccessful()) {
                if (response.getStatusCode().is5xxServerError()) {
                    log.warn("Got status code {} on request for config {} with params {} - retry", response.getStatusCode(), config, queryParams);
                    throw new ATSRetryableException("Unable to fetch offers, bad remote response %s for ATS %s".formatted(response.getStatusCode(), config.getAtsCode()));
                }
                log.warn("Unable to fetch offers at URL {} (invalid status in response, got: {})", uri, response.getStatusCode());
                throw new GenericTechnicalException("Unable to fetch offers, bad remote response");
            }
            return response.getBody();
        } catch (HttpServerErrorException e) {
            log.warn("Unable to fetch offers at URL {} - server error (message: {})", uri, e.getMessage(), e);
            throw new ATSRetryableException("Unable to fetch offers - retry", e);
        } catch (RestClientException e) {
            log.warn("Unable to fetch offers at URL {} (message: {})", uri, e.getMessage(), e);
            throw new GenericTechnicalException("Unable to fetch offers", e);
        }

    }

    private String createCacheKey(Authentication authentication) {
        return authentication.getUrl() + "#" + authentication.getClientId();
    }

    public String generateToken(Authentication authentication) {
        var cacheKey = createCacheKey(authentication);
        return tokenCache.computeIfAbsent(cacheKey, key -> createNewCachedToken(authentication))
                .isValid() ? tokenCache.get(cacheKey).token() : refreshAndCacheToken(authentication, cacheKey);
    }

    private String refreshAndCacheToken(Authentication authentication, String cacheKey) {
        log.debug("Refreshing token for {}", authentication.getUrl());
        var cachedToken = createNewCachedToken(authentication);
        tokenCache.put(cacheKey, cachedToken);
        return cachedToken.token();
    }

    private CachedToken createNewCachedToken(Authentication authentication) {
        var response = fetchTokenFromRemote(authentication);
        var token = extractAccessToken(response);
        var expiryTime = calculateExpiryTime(response);
        return new CachedToken(token, expiryTime);
    }

    private Map<String, Object> fetchTokenFromRemote(Authentication authentication) {
        var header = createRequestHeader(authentication);
        var body = createRequestBody(authentication);
        var request = new HttpEntity<>(body, header);
        return restTemplate.postForObject(authentication.getUrl(), request, Map.class);
    }

    private HttpHeaders createRequestHeader(Authentication authentication) {
        var header = new HttpHeaders();
        header.setContentType(authentication.isJson() ? MediaType.APPLICATION_JSON : MediaType.APPLICATION_FORM_URLENCODED);
        return header;
    }

    private Map<String, ?> createRequestBody(Authentication authentication) {
        // Tricky: handle Gestmax flavored authentication (camelcase, singleton)
        // AND TS flavored authentication: snakecase, list
        var body = authentication.isJson() ? new LinkedHashMap() : new LinkedMultiValueMap();
        body.put(authentication.isJson() ? "clientId" : "client_id", authentication.isJson() ? authentication.getClientId() : List.of(authentication.getClientId()));
        body.put(authentication.isJson() ? "clientSecret" : "client_secret", authentication.isJson() ? authentication.getClientSecret() : List.of(authentication.getClientSecret()));
        body.put(authentication.isJson() ? "grantType" : "grant_type", authentication.isJson() ? authentication.getGrantType() : List.of(authentication.getGrantType()));
        if (!StringUtils.isBlank(authentication.getScope())) {
            body.put("scope", authentication.isJson() ? authentication.getScope() : List.of(authentication.getScope()));
        }
        return body;
    }

    private String extractAccessToken(Map<String, Object> response) {
        return (String) response.get("access_token");
    }

    private Instant calculateExpiryTime(Map<String, Object> response) {
        var expiresInSeconds = extractExpiresInSeconds(response);
        var adjustedExpiresInSeconds = (long) (expiresInSeconds * SAFETY_MARGIN_FACTOR);
        return Instant.now().plusSeconds(adjustedExpiresInSeconds);
    }

    private long extractExpiresInSeconds(Map<String, Object> response) {
        if (response.containsKey("expires_in") && response.get("expires_in") != null) {
            try {
                return Long.parseLong(response.get("expires_in").toString());
            } catch (NumberFormatException e) {
                log.warn("Could not parse expires_in value: {}", response.get("expires_in"));
            }
        }
        return DEFAULT_TOKEN_LIFETIME_SECONDS;
    }
}
