package com.erhgo.services.externaloffer.talentplug;

import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.externaloffer.candidature.AbstractATSMailNotificationSender;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.send.SendCandidaturesRecruiterProvider;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.mailing.MailNotifier;
import com.erhgo.services.reminder.EmailToNotifyFilterInterface;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import jakarta.annotation.Nullable;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class TalentPlugATSMailNotificationScheduler extends AbstractATSMailNotificationSender {

    public TalentPlugATSMailNotificationScheduler(RecruitmentCandidatureRepository recruitmentCandidatureRepository, SpontaneousCandidatureRepository spontaneousCandidatureRepository,
                                                  AbstractCandidatureRepository abstractCandidatureRepository,
                                                  KeycloakService keycloakService, ConfigurablePropertyRepository configurablePropertyRepository, UserProfileCompetencesExportService userProfileCompetencesExportService, @Nullable MailNotifier mailNotifier, SecurityService securityService, List<AtsSendCandidaturesConfig> talentPlugSendCandidaturesConfig, TransactionTemplate transactionTemplate, EmailToNotifyFilterInterface emailToNotifyFilterInterface, SendCandidaturesRecruiterProvider sendCandidaturesRecruiterProvider) {
        super(recruitmentCandidatureRepository, spontaneousCandidatureRepository, abstractCandidatureRepository, keycloakService, configurablePropertyRepository, userProfileCompetencesExportService, mailNotifier, securityService, talentPlugSendCandidaturesConfig, transactionTemplate, emailToNotifyFilterInterface, sendCandidaturesRecruiterProvider);
    }

    @PostConstruct
    public void init() {
        log.info("TalentPlugATSMailNotificationHandler initialized");
    }

    @Scheduled(initialDelay = 30, fixedRate = 60, timeUnit = TimeUnit.MINUTES)
    @SchedulerLock(name = "handleNewCandidaturesForTalentPlugATS", lockAtLeastFor = "35M")
    @Override
    public void handleNewCandidatures() {
        super.handleNewCandidatures();
    }

    @Override
    protected boolean useReplyToAsFrom() {
        return true;
    }

}
