package com.erhgo.services.externaloffer.candidature;

import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Configuration
@EnableConfigurationProperties
public class DefaultSendMailCandidaturesConfiguration {

    @Bean
    @ConfigurationProperties(prefix = "ats.default.send")
    public List<AtsSendCandidaturesConfig> defaultMailSendCandidaturesConfig() {
        return new ArrayList<>();
    }
}
