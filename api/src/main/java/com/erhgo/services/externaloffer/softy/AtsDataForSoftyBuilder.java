package com.erhgo.services.externaloffer.softy;

import com.erhgo.openapi.dto.ExtractedAtsDataDTO;
import com.erhgo.services.externaloffer.AtsDataBuilder;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Joiner;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@RequiredArgsConstructor
public class AtsDataForSoftyBuilder implements AtsDataBuilder<SoftyJob> {
    private final ObjectMapper objectMapper;

    public ExtractedAtsDataDTO buildAtsData(SoftyJob softyJob) {
        return softyJob == null ? null : new ExtractedAtsDataDTO()
                .typeContractInformations(Map.of("contrat", softyJob.getContractType()))
                .occupationTitles(Map.of("occupation", softyJob.getOfferTitle()))
                .descriptionParts(Map.of("description", softyJob.getDescription()))
                .organizationDescriptionParts(Map.of("description organisation", softyJob.getOrganizationDescription()))
                .localisationInformations(Map.of("ville", softyJob.getLocationIndication()))
                .criteriaRelatedData(Map.of("criterias", Joiner.on(",").join(softyJob.getCriterias())))
                ;
    }
}
