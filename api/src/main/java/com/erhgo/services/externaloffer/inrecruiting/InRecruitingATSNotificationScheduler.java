package com.erhgo.services.externaloffer.inrecruiting;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.openapi.client.inrecruiting.ApiClient;
import com.erhgo.openapi.client.inrecruiting.ApiException;
import com.erhgo.openapi.client.inrecruiting.api.DefaultApi;
import com.erhgo.openapi.client.inrecruiting.api.model.CandidateStoreRequest;
import com.erhgo.openapi.client.inrecruiting.api.model.CandidateStoreRequestCv;
import com.erhgo.openapi.client.inrecruiting.api.model.VacancyApplicationStoreRequest;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.externaloffer.candidature.AbstractATSNotificationSender;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.send.SendCandidaturesRecruiterProvider;
import com.erhgo.services.http.RetryableHttpClient;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import okhttp3.FormBody;
import okhttp3.Request;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.http.HttpHeaders;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
// See https://inrecruiting.intervieweb.it/api-docs/
public class InRecruitingATSNotificationScheduler extends AbstractATSNotificationSender implements ApplicationContextAware {

    private static final String GRANT_TYPE = "client_credentials";
    private static final String TOKEN_PATH = "/oauth2/token";
    private static final String SOURCE = "jenesuispasunCV";

    private RetryableHttpClient httpClient;
    private final ObjectMapper objectMapper;

    public InRecruitingATSNotificationScheduler(ObjectMapper objectMapper, KeycloakService keycloakService, ConfigurablePropertyRepository configurablePropertyRepository, UserProfileCompetencesExportService userProfileCompetencesExportService, TransactionTemplate transactionTemplate, RecruitmentCandidatureRepository recruitmentCandidatureRepository, SpontaneousCandidatureRepository spontaneousCandidatureRepository, List<AtsSendCandidaturesConfig> inRecruitingSendConfigurations, SecurityService securityService, AbstractCandidatureRepository abstractCandidatureRepository, SendCandidaturesRecruiterProvider sendCandidaturesRecruiterProvider) {
        super(keycloakService, configurablePropertyRepository, userProfileCompetencesExportService, transactionTemplate, recruitmentCandidatureRepository, spontaneousCandidatureRepository, inRecruitingSendConfigurations, securityService, abstractCandidatureRepository, sendCandidaturesRecruiterProvider);
        this.objectMapper = objectMapper;
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        log.info("InRecruiting ATS candidature notifier initialized");
        httpClient = applicationContext.getBean(RetryableHttpClient.class, 30, 30, 30);
    }

    @Scheduled(initialDelay = 21, fixedRate = 60, timeUnit = TimeUnit.MINUTES)
    @SchedulerLock(name = "handleNewCandidaturesForInRecruitingATS", lockAtLeastFor = "35M")
    @Override
    public void handleNewCandidatures() {
        log.debug("Sync InRecruiting candidatures start");
        super.handleNewCandidatures();
        log.debug("Sync InRecruiting candidatures end");
    }

    @Override
    protected void sendCandidature(UserRepresentation userRepresentation, RecruitmentCandidature candidature, AtsSendCandidaturesConfig config) {
        try {
            var token = getToken(config);
            var applicantId = getOrCreateCandidate(token, candidature, userRepresentation, config);
            log.info("Got ID {} for candidate {}", applicantId, userRepresentation.getId());
            createApplication(token, userRepresentation, applicantId, candidature.getExternalOfferId(), config);
        } catch (IOException e) {
            log.error("Unable to send candidature for {} to InRecruiting", userRepresentation, e);
            throw new GenericTechnicalException("Unable to send candidature for %s to InRecruiting".formatted(userRepresentation.getId()), e);
        }
    }

    private void createApplication(String token, UserRepresentation userRepresentation, String applicantId, String offerId, AtsSendCandidaturesConfig config) {
        var api = buildClient(token, config);
        try {
            var request = new VacancyApplicationStoreRequest().selfApplication(true).vacancyId(offerId).candidateId(applicantId).candidateNotification(0).source(SOURCE);
            api.vacancyApplicationStore(request);
            log.debug("Candidature for user {} and remote InRecruiting offer {} sent", userRepresentation.getId(), offerId);
        } catch (ApiException e) {
            throw new GenericTechnicalException("Unable to send candidature to InRecruiting for applicantId %s (user %s) and offer %s".formatted(applicantId, userRepresentation.getId(), offerId), e);
        }
    }

    private String getOrCreateCandidate(String token, RecruitmentCandidature candidature, UserRepresentation userRepresentation, AtsSendCandidaturesConfig config) throws IOException {
        var api = buildClient(token, config);
        var candidateStoreRequest = buildCandidateRequest(userRepresentation, candidature);
        try {
            return api
                    .candidateStore(candidateStoreRequest)
                    .getApplicantId();
        } catch (ApiException e) {
            if (e.getCode() == 303) {
                log.debug("Got recoverable error (user already exists) for candidate update in InRecruiting for userId {}", userRepresentation.getId(), e);
                var applicantId = String.valueOf(objectMapper.readValue(e.getResponseBody(), Map.class).get("applicantId"));
                try {
                    var result = api.candidateUpdate(Integer.parseInt(applicantId), candidateStoreRequest);
                    log.debug("Got result {} for candidate update in InRecruiting for applicantId {} and userId {}", result, applicantId, userRepresentation.getId());
                } catch (ApiException ex) {
                    log.error("Unable to update applicant ID {} for candidate {} - it will remain as is", applicantId, userRepresentation.getId(), e);
                }
                return applicantId;
            } else {
                log.error("Got  error for candidate update in InRecruiting for userId {}", userRepresentation.getId(), e);
                throw new GenericTechnicalException("Unable to create or update candidate %s in InRecruiting".formatted(userRepresentation.getId()), e);
            }
        }

    }

    private static @NotNull DefaultApi buildClient(String token, AtsSendCandidaturesConfig config) {
        var apiClient = new ApiClient()
                .setRequestInterceptor(builder -> builder.header(HttpHeaders.AUTHORIZATION, "Bearer %s".formatted(token)));
        apiClient.updateBaseUri(config.getCandidatureNotificationUrl());
        return new DefaultApi(apiClient);
    }

    private CandidateStoreRequest buildCandidateRequest(UserRepresentation userRepresentation, RecruitmentCandidature candidature) throws IOException {
        var profile = getProfile(candidature, userRepresentation.getFullname(), ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE);
        return new CandidateStoreRequest()
                .cv(new CandidateStoreRequestCv().content(profile.readAllBytesAsStringifiedBase64()).ext("pdf").filename(profile.fileName()))
                .email(userRepresentation.getEmail())
                .name(userRepresentation.getFirstNameNeverEmpty())
                .userType(0)
                .surname(userRepresentation.getLastNameNeverEmpty())
                .source(SOURCE)
                ;
    }

    private String getToken(AtsSendCandidaturesConfig config) throws IOException {
        var clientAndSecret = config.getCandidatureNotificationApiKey().split(";");
        var formBody = new FormBody.Builder()
                .add("client_id", clientAndSecret[0])
                .add("client_secret", clientAndSecret[1])
                .add("grant_type", GRANT_TYPE)
                .build();

        var url = config.getCandidatureNotificationUrl() + TOKEN_PATH;
        var request = new Request.Builder()
                .url(url)
                .post(formBody)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();

        try (var response = httpClient.executeRequest(request)) {
            if (response.isSuccessful() && response.body() != null) {
                return (String) objectMapper.readValue(response.body().string(), Map.class).get("access_token");
            } else {
                throw new GenericTechnicalException("Unable to get access token to InRecruiting for url %s - response: %d".formatted(url, response.code()));
            }
        }
    }
}
