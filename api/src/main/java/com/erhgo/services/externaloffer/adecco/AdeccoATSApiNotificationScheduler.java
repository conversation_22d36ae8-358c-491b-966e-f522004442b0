package com.erhgo.services.externaloffer.adecco;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.enums.CandidatureSynchronizationState;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.openapi.client.adecco.ApiClient;
import com.erhgo.openapi.client.adecco.ApiException;
import com.erhgo.openapi.client.adecco.api.CandidateJobUnauthenticatedApi;
import com.erhgo.openapi.client.adecco.api.model.*;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.externaloffer.candidature.AbstractATSNotificationSender;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.send.SendCandidaturesRecruiterProvider;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.userprofile.FilePartProvider;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import com.erhgo.utils.StringUtils;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class AdeccoATSApiNotificationScheduler extends AbstractATSNotificationSender {

    public static final int QUALIFICATION_VALUE__PROVIDED_BY_ADECCO = 1495;
    public static final String SOURCE_INFO__PROVIDED_BY_ADECCO = "184";
    private Map<String, CandidateJobUnauthenticatedApi> clientForAts = new HashMap<>();

    private static final int CDI = 11;
    private static final int CDD = 12;
    private static final int ALT = 15;

    public AdeccoATSApiNotificationScheduler(
            RecruitmentCandidatureRepository recruitmentCandidatureRepository,
            SpontaneousCandidatureRepository spontaneousCandidatureRepository,
            AbstractCandidatureRepository abstractCandidatureRepository,
            KeycloakService keycloakService,
            ConfigurablePropertyRepository configurablePropertyRepository,
            UserProfileCompetencesExportService userProfileCompetencesExportService,
            SecurityService securityService,
            List<AtsSendCandidaturesConfig> adeccoSendCandidaturesConfig,
            TransactionTemplate transactionTemplate,
            SendCandidaturesRecruiterProvider sendCandidaturesRecruiterProvider
    ) {
        super(keycloakService, configurablePropertyRepository, userProfileCompetencesExportService, transactionTemplate, recruitmentCandidatureRepository, spontaneousCandidatureRepository, adeccoSendCandidaturesConfig, securityService, abstractCandidatureRepository, sendCandidaturesRecruiterProvider);
    }

    @PostConstruct
    public void init() {
        sendCandidaturesConfig.forEach(config -> {
            var apiClient = new ApiClient();
            apiClient.updateBaseUri(config.getCandidatureNotificationUrl());
            var client = new CandidateJobUnauthenticatedApi(apiClient);
            clientForAts.put(config.getCandidatureNotificationUrl(), client);
        });
    }

    @Override
    @Scheduled(initialDelay = 3, fixedRate = 60, timeUnit = TimeUnit.MINUTES)
    @SchedulerLock(name = "handleNewCandidaturesForAdeccoATS", lockAtLeastFor = "PT35M")
    public void handleNewCandidatures() {
        super.handleNewCandidatures();
    }

    @Override
    protected void sendCandidature(UserRepresentation user, RecruitmentCandidature candidatureIn, AtsSendCandidaturesConfig config) {
        var client = clientForAts.get(config.getCandidatureNotificationUrl());
        var command = transactionTemplate.execute(__ -> {
            var candidature = recruitmentCandidatureRepository.findById(candidatureIn.getId()).orElseThrow();
            return buildCandidatureDTO(user, candidature);
        });
        var remoteId = candidatureIn.getRecruitment().getExternalOffer().getRemoteId();
        if (!StringUtils.isFrenchPhone(Optional.ofNullable(command.getUser()).map(User::getPhone).orElse(null))) {
            log.warn("Candidature {} (offer's remote id {}) without french phone on Adecco - will result as warning", candidatureIn.getId(), remoteId);
        }
        int status;
        ApiException caught = null;
        try {
            var response = client.postcreateAccountandApplyForJobBoardWithHttpInfo(
                    remoteId,
                    command,
                    config.getCandidatureNotificationApiKey(),
                    "jnspcv_adecco"
            );
            log.debug("Candidature {} sent to {} - status: {}", candidatureIn.getId(), config.getAtsCode(), response.getStatusCode());
            status = response.getStatusCode();
        } catch (ApiException e) {
            status = e.getCode();
            caught = e;
        }
        handleResponse(candidatureIn, config, status, caught, command);
    }

    private void handleResponse(RecruitmentCandidature candidatureIn, AtsSendCandidaturesConfig config, int status, ApiException caught, RequestJobApplyJobBoard command) {
        var success = status >= 200 && status < 300;
        // Responses 400 & 500 handled as warning: candidates will receive an email to finish their candidature on Adecco app.
        if (!success && status != 400 && status != 500) {
            if (caught != null) {
                throw new GenericTechnicalException("Fail to send candidature - response code: %s, response body: %s, request: %s".formatted(status, caught.getResponseBody(), toString(command)), caught);
            }
            throw new GenericTechnicalException("Fail to send candidature - response code: %s".formatted(status));
        }
        if (!success) {
            setATSState(candidatureIn, CandidatureSynchronizationState.WARNING, config);
        }
    }

    private RequestJobApplyJobBoard buildCandidatureDTO(UserRepresentation userRepresentation, RecruitmentCandidature candidature) {
        FilePartProvider profile;
        try {
            profile = getProfile(candidature, userRepresentation.getFullname(), ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE);
            var base64Encoded = Base64.getEncoder().encodeToString(profile.readAllBytes());
            return new RequestJobApplyJobBoard()
                    .user(new User()
                            .email(userRepresentation.getEmail())
                            .firstname(StringUtils.normalizeIdentityForAdecco(userRepresentation.getFirstName()))
                            .lastname(StringUtils.normalizeIdentityForAdecco(userRepresentation.getLastName()))
                            .phone(StringUtils.toFrenchFormat(candidature.getUserProfile().getPhoneNumber()))
                    )
                    .userProfile(new UserProfile()
                            .civility(/* hard coded as we don't (want to) know 1=male, 2=female*/ 1)
                            .address(
                                    new Address()
                                            .city(StringUtils.normalizeAddressForAdecco(candidature.getUserProfile().getCity()))
                                            .country(1)
                                            .zipCode(candidature.getUserProfile().getPostcode())
                            )
                            .qualification(List.of(QUALIFICATION_VALUE__PROVIDED_BY_ADECCO))
                            .contractType(candidature.getUserProfile().getContractsFromCriteria().stream().map(c -> switch (c) {
                                case PRO -> ALT;
                                case TEMPORARY -> CDD;
                                default -> CDI;
                            }).toList())
                            .hasHandicap(false)
                            .hasAcceptedByEmail(false)
                            .hasAcceptedByPhone(false)
                            .consent(true)
                    )
                    .uploadedCV(new UploadedCV().fileName(profile.fileName())._file(base64Encoded))
                    .sourceInfo(new SourceInfo().source(SOURCE_INFO__PROVIDED_BY_ADECCO))
                    .creationFlowStatus("")
                    ;
        } catch (IOException e) {
            log.error("Exception while building profile for candidature {} for Adecco", candidature.getId(), e);
            throw new GenericTechnicalException("Unable to send candidature %d to Adecco".formatted(candidature.getId()), e);
        }
    }

    private String toString(RequestJobApplyJobBoard command) {
        return command == null ? "" : "class RequestJobApplyJobBoard {\n" +
                "    user: " + command.getUser() + "\n" +
                "    userProfile: " + command.getUserProfile() + "\n" +
                "    uploadedCV size: " + Optional.ofNullable(command.getUploadedCV()).map(UploadedCV::getFile).map(String::length).orElse(0) + "\n" +
                "    sourceInfo: " + command.getSourceInfo() + "\n" +
                "}";
    }
}
