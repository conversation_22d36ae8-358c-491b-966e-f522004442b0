package com.erhgo.services.externaloffer.eolia;

import com.erhgo.services.externaloffer.ExternalJobParser;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import com.erhgo.services.externaloffer.config.ParserProvider;
import com.erhgo.services.externaloffer.parsers.GenericJobXmlParser;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeansException;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.event.EventListener;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Configuration
@EnableConfigurationProperties
public class EoliaConfiguration {

    private final AtsDataForEoliaBuilder atsDataBuilder;
    private final ExternalOfferServiceProvider serviceProvider;
    private final ParserProvider parserProvider;

    @Bean
    @ConfigurationProperties(prefix = "ats.eolia.fetch")
    public List<AtsGetOfferConfig> eoliaConfigurations() {
        return new ArrayList<>();
    }

    @Bean
    @ConfigurationProperties(prefix = "ats.eolia.send")
    public List<AtsSendCandidaturesConfig> eoliaSendCandidaturesConfig() {
        return new ArrayList<>();
    }

    @Bean
    @Scope("prototype")
    public ExternalJobParser<EoliaJob> eoliaJobXmlParser(AtsGetOfferConfig config) {
        return new GenericJobXmlParser<>(EoliaJob.class, config.getRootPath(), config.getAtsCode());
    }

    @EventListener(ApplicationReadyEvent.class)
    public void initializeEoliaServices() throws BeansException {
        eoliaConfigurations().forEach(config ->
                {
                    var eoliaJobXmlParser = parserProvider.initializeBean(config.getAtsCode(), "eoliaJobXmlParser", config);
                    serviceProvider.initializeGenericService(
                            atsDataBuilder,
                            config,
                            eoliaJobXmlParser);
                }
        );
    }
}
