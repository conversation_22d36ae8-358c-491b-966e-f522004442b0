package com.erhgo.services.externaloffer.gestmax;

import com.erhgo.openapi.dto.TypeContractCategoryDTO;
import com.erhgo.openapi.dto.WorkingTimeDTO;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Joiner;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Stream;

@Data
@ToString
@EqualsAndHashCode(callSuper = false)
public class GestmaxJob extends AbstractRemoteOfferContent<GestmaxJob> {
    private static final String DATE_PATTERN = "dd/MM/yyyy";
    private static final Logger log = LoggerFactory.getLogger(GestmaxJob.class);

    @JsonProperty("vacancy_id")
    private String id;

    private String vacancyRef;

    @JsonProperty("vacancy_title")
    private String offerTitle;

    @JsonFormat(pattern = DATE_PATTERN)
    private LocalDate vacancyDate;

    @JsonFormat(pattern = DATE_PATTERN)
    private LocalDate vacancyActivationDate;

    @JsonProperty("customer_description")
    private String customerDescription;

    @JsonProperty("cust_logo")
    private String customerLogo;

    private Boolean vacancyAnonymousCustomer;

    private String vacancyAnonymousCustomerName;

    @JsonFormat(pattern = DATE_PATTERN)
    private LocalDate publicationStartDate;

    @JsonFormat(pattern = DATE_PATTERN)
    private LocalDate publicationEndDate;

    private Boolean isUnsolicited;

    @JsonProperty("vac_function")
    private String function;

    @JsonProperty("vac_orga_niveau_1")
    private String level1Orga_alt1;

    @JsonProperty("department_name")
    private String level1Orga_alt2;

    @JsonProperty("vac_orga_niveau_2")
    private String level2Orga;

    @JsonProperty("vac_contract")
    private String contract_alt1;

    @JsonProperty("vac_contrat")
    private String contract_alt2;

    @JsonProperty("vac_town")
    private String city_alt1;
    @JsonProperty("vac_dpt")
    private String department;
    @JsonProperty("vac_ville")
    private String city_alt2;

    private String customerCompany;
    private String customerName;
    private String customerId;

    @JsonProperty("vac_salaire")
    private String salaries_alt1;

    @JsonProperty("vac_remuneration")
    private String salaries_alt2;

    @JsonProperty("vaclang_contexte")
    private String context;

    @JsonProperty("vaclang_tasks")
    private String tasks;

    @JsonProperty("vaclang_profil")
    private String profil;

    @JsonProperty("vaclang_offer")
    private String offer;

    @JsonProperty("vaclang_job_description")
    private String jobDescription;

    @JsonProperty("vaclang_job_details")
    private String jobDetails;

    @JsonProperty("vaclang_profile")
    private String jobProfile;

    @JsonProperty("vac_temps_travail")
    private String workingTimeLabel;

    @JsonProperty("vac_duree")
    private String duration;

    @JsonProperty("vac_pays")
    private String country;

    @Override
    public LocalDateTime getLastModificationDate() {
        return Optional.ofNullable(vacancyActivationDate).map(LocalDate::atStartOfDay).orElse(null);
    }

    @Override
    public String getOrganizationDescription() {
        var description = "";
        if (StringUtils.isNotBlank(customerLogo)) {
            description += "<img style=\"max-width:100%\" src=\"" + customerLogo.trim() + "\"><br>";
        }
        if (StringUtils.isNotBlank(customerDescription)) {
            description += customerDescription.trim();
        }
        return description;
    }

    @Override
    public String getDescription() {
        var content = new ArrayList<>();

        if (StringUtils.isNotBlank(offer)) {
            content.add(offer);
        }
        if (StringUtils.isNotBlank(context)) {
            content.add("<h3>Contexte</h3>");
            content.add(context);
        }
        if (StringUtils.isNotBlank(profil)) {
            content.add("<h3>Profile recherché</h3>");
            content.add(context);
        }
        if (StringUtils.isNotBlank(tasks)) {
            content.add("<h3>Vos missions</h3>");
            content.add(context);
        }
        if (StringUtils.isNotBlank(jobDescription)) {
            content.add("<h3>Vos missions</h3>");
            content.add(jobDescription);
        }
        if (StringUtils.isNotBlank(jobDetails)) {
            content.add("<h3>Détails du poste</h3>");
            content.add(jobDescription);
        }
        if (StringUtils.isNotBlank(jobDetails)) {
            content.add("<h3>Profil, Expérience, Formation</h3>");
            content.add(jobDescription);
        }
        return Joiner.on("<br/>").join(content);
    }


    @Override
    public TypeContractCategoryDTO getTypeContractCategory() {
        var contract = StringUtils.trimToNull(getContract());
        if (contract == null) {
            log.debug("No contract found for gestmax offer {}", id);
            return null;
        }
        if (contract.toUpperCase().contains("CDI")) return TypeContractCategoryDTO.PERMANENT;
        if (contract.toUpperCase().contains("CDD")) return TypeContractCategoryDTO.TEMPORARY;
        if (contract.toUpperCase().contains("ALT")) return TypeContractCategoryDTO.PRO;
        log.debug("Ignoring contract type {} for gestmax offer {}", contract, id);
        return null;
    }

    @Override
    public List<String> getCriterias() {
        return List.of();
    }

    @Override
    public WorkingTimeDTO getWorkingTimeType() {

        if (Stream.of(getContract(), workingTimeLabel)
                .filter(Objects::nonNull)
                .map(String::toLowerCase)
                .anyMatch(a -> a.contains("partiel"))
        ) {
            return WorkingTimeDTO.PART_TIME;
        }
        return super.getWorkingTimeType();
    }

    private String getContract() {
        return Optional.ofNullable(StringUtils.trimToNull(contract_alt1)).orElse(StringUtils.trimToNull(contract_alt2));
    }


    @Override
    public String getRemoteRecruiterCode() {
        return "%s / %s / %s".formatted(
                Optional.ofNullable(StringUtils.trimToNull(customerCompany)).orElse(StringUtils.trimToEmpty(customerName)),
                Optional.ofNullable(StringUtils.trimToNull(level1Orga_alt1)).orElse(StringUtils.trimToEmpty(level1Orga_alt2)),
                level2Orga);
    }

    @Override
    public String getLocationIndication() {
        var city = Stream.of(city_alt1, city_alt2)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse("");

        var dpt = Optional.ofNullable(StringUtils.trimToNull(department))
                .orElse("");
        return "%s %s".formatted(city, dpt);
    }

    @Override
    public boolean isInFrance() {
        return Optional.ofNullable(StringUtils.trimToNull(country))
                .filter(a -> !a.toLowerCase().contains("france"))
                .isEmpty();
    }

    @Override
    public List<Integer> getSalaryValues() {
        var salarie = Stream.of(salaries_alt1, salaries_alt2)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
        if (salarie != null) try {
            return List.of(((Float) Float.parseFloat(salarie)).intValue());
        } catch (IllegalArgumentException e) {
            log.warn("Unable to parse salary value as float {}", salarie, e);
        }
        return Collections.emptyList();
    }
}
