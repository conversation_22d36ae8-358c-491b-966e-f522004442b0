package com.erhgo.services.externaloffer.candidature;

import com.erhgo.domain.candidature.job.AbstractCandidature;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.candidature.job.SpontaneousCandidature;
import com.erhgo.domain.enums.CandidatureSynchronizationState;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.send.SendCandidaturesRecruiterProvider;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.userprofile.FilePartProvider;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import com.google.common.base.Joiner;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
public abstract class AbstractATSNotificationSender {
    private final KeycloakService keycloakService;
    private final ConfigurablePropertyRepository configurablePropertyRepository;
    private final UserProfileCompetencesExportService userProfileCompetencesExportService;
    protected final TransactionTemplate transactionTemplate;
    protected final RecruitmentCandidatureRepository recruitmentCandidatureRepository;
    protected final SpontaneousCandidatureRepository spontaneousCandidatureRepository;
    protected final List<AtsSendCandidaturesConfig> sendCandidaturesConfig;
    protected final SecurityService securityService;
    protected final AbstractCandidatureRepository abstractCandidatureRepository;
    protected final SendCandidaturesRecruiterProvider sendCandidaturesRecruiterProvider;

    public void handleNewCandidatures() {
        handleNewRecruitmentCandidatures();
        handleNewSpontaneousCandidatures();
    }

    public void handleNewSpontaneousCandidatures() {
        securityService.doAsAdmin(() -> sendCandidaturesConfig.stream().filter(AtsSendCandidaturesConfig::shouldSendSpontaneousCandidatures).forEach(this::sendSpontaneousCandidatures));
    }

    public void handleNewRecruitmentCandidatures() {
        securityService.doAsAdmin(() -> sendCandidaturesConfig.forEach(this::sendRecruitmentCandidatures));
    }

    @PostConstruct
    public void logInit() {
        log.info("Scheduler initialized for {}", Joiner.on(" // ").join(sendCandidaturesConfig));
    }

    protected abstract void sendCandidature(UserRepresentation user, RecruitmentCandidature candidature, AtsSendCandidaturesConfig config);

    protected void sendCandidature(UserRepresentation user, SpontaneousCandidature candidature, AtsSendCandidaturesConfig config) {
        throw new IllegalStateException("Not implemented");
    }

    private void sendRecruitmentCandidatures(AtsSendCandidaturesConfig config) {
        log.debug("Let's send recruitment candidatures for config {}", config);
        var atsCode = config.getAtsCode();
        var recruiterCodesToConsider = getRecruiterCodesToConsider(config);
        var candidatures = recruitmentCandidatureRepository.findCandidaturesToHandleForATS(recruiterCodesToConsider, atsCode, OffsetDateTime.now().minusDays(config.getNumberOfDaysToConsider()), OffsetDateTime.now().minusMinutes(config.getDelayInMinutes()));
        sendCandidatures(config, atsCode, candidatures, (CandidatureSender<RecruitmentCandidature>) this::sendCandidature);
        log.debug("Recruitment Candidatures for config {} sending ended", config);
    }

    private void sendSpontaneousCandidatures(AtsSendCandidaturesConfig config) {
        log.debug("Let's send spontaneous candidatures for config {}", config);
        var atsCode = config.getAtsCode();
        var recruiterCodesToConsider = getRecruiterCodesToConsider(config);
        var candidatures = spontaneousCandidatureRepository.findCandidaturesToHandleForATS(recruiterCodesToConsider, OffsetDateTime.now().minusDays(config.getNumberOfDaysToConsider()), OffsetDateTime.now().minusMinutes(config.getDelayInMinutes()));
        sendCandidatures(config, atsCode, candidatures, (CandidatureSender<SpontaneousCandidature>) this::sendCandidature);
        log.debug("Spontaneous Candidatures for config {} sending ended", config);
    }

    private void sendCandidatures(AtsSendCandidaturesConfig config, String atsCode, List<? extends AbstractCandidature> candidatures, CandidatureSender sender) {
        var notificationIsDisabled = "true".equalsIgnoreCase(configurablePropertyRepository.getPropertyValueOrDefaults("ats.disable-candidature-notification", atsCode));
        if (!candidatures.isEmpty() && notificationIsDisabled) {
            log.info("Ignoring {} candidatures for {} notification (including {} archived or refused)", candidatures.size(), atsCode, candidatures.stream().filter(c -> c.isRefused() || c.isArchived()).count());
            return;
        }
        log.debug("Considering {} candidatures for {} notification", candidatures.size(), atsCode);
        candidatures.forEach(c -> {
            try {
                sendCandidatureUnsafe(config, c, atsCode, sender);
            } catch (RuntimeException e) {
                log.error("Unable to send candidature {} (config: {})", c.getId(), config, e);
            } finally {
                if (c.getSynchronizationState() == null || !c.getSynchronizationState().isFinal()) {
                    setATSState(c, CandidatureSynchronizationState.ERROR, config);
                }
            }
        });
    }

    private @NotNull List<String> getRecruiterCodesToConsider(AtsSendCandidaturesConfig config) {
        return sendCandidaturesRecruiterProvider.findRecruitersCodesFor(config);
    }

    private <A extends AbstractCandidature> void sendCandidatureUnsafe(AtsSendCandidaturesConfig config, A c, String atsCode, CandidatureSender sender) {
        log.trace("Handling {} candidature {}", atsCode, c.getId());
        if (c.isArchived() || c.isRefused()) {
            setATSState(c, CandidatureSynchronizationState.IGNORE, config);
            log.debug("{} candidature {} ignored - already refused or archived", atsCode, c.getId());
        } else {
            setATSState(c, CandidatureSynchronizationState.PENDING, config);
            var userId = c.getUserProfile().userId();
            keycloakService.getFrontOfficeUserProfile(userId).ifPresentOrElse(
                    e -> {
                        sender.sendCandidature(e, c, config);
                        if (c.getSynchronizationState() == null || !c.getSynchronizationState().isFinal()) {
                            setATSState(c, CandidatureSynchronizationState.DONE, config);
                        }
                    },
                    () -> log.error("No user for userId {}: no candidature sent to ATS {}", userId, atsCode)
            );
            log.debug("{} candidature {} handled - status: {}", atsCode, c.getId(), c.getSynchronizationState());
        }
    }

    protected void setATSState(AbstractCandidature c, CandidatureSynchronizationState state, AtsSendCandidaturesConfig config) {
        c.setSynchronizationState(state);
        c.setRemoteNotifiedIdentifier(getRemoteNotifiedIdentifier(c, config));
        abstractCandidatureRepository.save(c);
    }

    String getRemoteNotifiedIdentifier(AbstractCandidature c, AtsSendCandidaturesConfig config) {
        return config.getCandidatureNotificationUrl();
    }

    protected FilePartProvider getProfile(AbstractCandidature candidature, String fullname, ProfileCompetencesViewObject.AnonymousMode anonymous) throws IOException {
        return userProfileCompetencesExportService.getProfileCompetenceForBatch(candidature.getId(), fullname, anonymous);
    }

    @FunctionalInterface
    interface CandidatureSender<A extends AbstractCandidature> {

        void sendCandidature(UserRepresentation user, A candidature, AtsSendCandidaturesConfig config);

    }
}
