package com.erhgo.services.externaloffer.digitalrecruiters;

import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import com.erhgo.services.externaloffer.config.ParserProvider;
import com.erhgo.services.externaloffer.parsers.GenericJobJsonParser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.event.EventListener;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Configuration
@EnableConfigurationProperties
public class DRConfiguration {

    private final AtsDataForDRBuilder atsDataForDRBuilder;
    private final ExternalOfferServiceProvider serviceProvider;
    private final ParserProvider parserProvider;

    @Bean
    @ConfigurationProperties(prefix = "ats.digital-recruiters.fetch")
    public List<AtsGetOfferConfig> drConfigurations() {
        return new ArrayList<>();
    }

    @Bean
    @Scope("prototype")
    public GenericJobJsonParser<DRJob> drJobJSONParser() {
        return new GenericJobJsonParser<>(DRJob.class, "ads");
    }

    @Bean
    @ConfigurationProperties(prefix = "ats.digital-recruiters.send")
    public List<AtsSendCandidaturesConfig> drSendCandidaturesConfig() {
        return new ArrayList<>();
    }

    @EventListener(ApplicationReadyEvent.class)
    public void initializeServices() throws BeansException {
        drConfigurations().forEach(config ->
                {
                    var drJobJSONParser = parserProvider.initializeBean(config.getAtsCode(), "drJobJSONParser");
                    serviceProvider.initializeGenericService(
                            atsDataForDRBuilder,
                            config,
                            drJobJSONParser);
                }
        );
    }
}
