package com.erhgo.services.externaloffer.gestmax;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.externaloffer.candidature.AbstractATSWithHttpClientNotificationSender;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.send.SendCandidaturesRecruiterProvider;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import okhttp3.Credentials;
import okhttp3.FormBody;
import okhttp3.Request;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
// See  https://opteven.dev.gestmax.fr/rest/apidoc/
// Login: JNSPUCV / KljFtC57m0JYx[eQWwX9GT54
// Get external offer, dev: curl --location 'https://opteven.dev.gestmax.fr/rest/v2/onlinevacancy?pageItems=100&fields=vacancy_ref' --header 'Authorization: Basic Sk5TUFVDVjpLbGpGdEM1N20wSll4W2VRV3dYOUdUNTQ=' | jq
// Prod: curl -u jenesuispasuncv:REDI7mL98kePaGmHu8FP4mRe https://opteven.simply-jobs.fr/rest/v2/onlinevacancy?fields=vacancy_ref | jq
public class GestmaxATSNotificationScheduler extends AbstractATSWithHttpClientNotificationSender {

    private final ObjectMapper objectMapper;

    public GestmaxATSNotificationScheduler(ObjectMapper objectMapper, KeycloakService keycloakService, ConfigurablePropertyRepository configurablePropertyRepository, UserProfileCompetencesExportService userProfileCompetencesExportService, TransactionTemplate transactionTemplate, RecruitmentCandidatureRepository recruitmentCandidatureRepository, SpontaneousCandidatureRepository spontaneousCandidatureRepository,
                                           AbstractCandidatureRepository abstractCandidatureRepository,
                                           List<AtsSendCandidaturesConfig> gestmaxSendCandidaturesConfig, SecurityService securityService, SendCandidaturesRecruiterProvider sendCandidaturesRecruiterProvider) {
        super(keycloakService, configurablePropertyRepository, userProfileCompetencesExportService, transactionTemplate, recruitmentCandidatureRepository, spontaneousCandidatureRepository, gestmaxSendCandidaturesConfig, securityService, abstractCandidatureRepository, sendCandidaturesRecruiterProvider);
        this.objectMapper = objectMapper;
    }

    @Scheduled(initialDelay = 33, fixedRate = 60, timeUnit = TimeUnit.MINUTES)
    @SchedulerLock(name = "handleNewCandidaturesForGestmaxATS", lockAtLeastFor = "35M")
    @Override
    public void handleNewCandidatures() {
        super.handleNewCandidatures();
    }

    @Override
    protected Request buildSendCandidatureRequest(UserRepresentation userRepresentation, RecruitmentCandidature candidature, AtsSendCandidaturesConfig config) throws IOException {

        var fullProfile = getProfile(candidature, userRepresentation.getFullname(), ProfileCompetencesViewObject.AnonymousMode.BOTH);
        var profileContent = fullProfile.readAllBytesAsStringifiedBase64();
        var dataPart = ImmutableMap.of(
                "candidate_first_name", StringUtils.trimToEmpty(userRepresentation.getFirstNameNeverEmpty()),
                "candidate_last_name", StringUtils.trimToEmpty(userRepresentation.getLastNameNeverEmpty()),
                "candidate_town", StringUtils.trimToEmpty(candidature.getUserProfile().getCity()),
                "candidate_zip", StringUtils.trimToEmpty(candidature.getUserProfile().getPostcode()),
                "cand_tel", StringUtils.trimToEmpty(candidature.getUserProfile().getPhoneNumber()),
                "cand_cv", ImmutableMap.of(
                        "name", fullProfile.fileName(),
                        "data", profileContent
                ));
        var body = new FormBody.Builder()
                .add("vacancy_id", candidature.getRecruitment().getExternalOffer().getRemoteId())
                .add("data", objectMapper.writeValueAsString(dataPart))
                .add("candidate_language", "fr_FR")
                .add("candidate_email", userRepresentation.getEmail())
                .add("application_date", candidature.getSubmissionDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss")))
                .add("media_id", config.getTrackingId())
                .build();


        var ids = config.getCandidatureNotificationApiKey().split(":");
        var credentials = Credentials.basic(ids[0], ids[1]);


        return new Request.Builder()
                .url(config.getCandidatureNotificationUrl())
                .header(HttpHeaders.AUTHORIZATION, credentials)
                .header(HttpHeaders.CONTENT_TYPE, "application/x-www-form-urlencoded")
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .post(body)
                .build();
    }
}
