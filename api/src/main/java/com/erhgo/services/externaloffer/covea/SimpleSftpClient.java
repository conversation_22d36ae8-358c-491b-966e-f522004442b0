package com.erhgo.services.externaloffer.covea;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import org.springframework.integration.sftp.session.DefaultSftpSessionFactory;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Objects;

@Service
public class SimpleSftpClient {

    private static final String FILENAME_PREFIX = "offresPubliees_";
    private static final String FILENAME_SUFFIX = ".csv";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final int DEFAULT_SFTP_PORT = 22;

    public InputStream streamLatestDailyOfferFile(String host, String user, String password) {
        Objects.requireNonNull(host, "host cannot be null");
        Objects.requireNonNull(user, "user cannot be null");
        Objects.requireNonNull(password, "password cannot be null");

        var factory = new DefaultSftpSessionFactory(false);
        factory.setHost(host);
        factory.setPort(DEFAULT_SFTP_PORT);
        factory.setUser(user);
        factory.setPassword(password);
        factory.setAllowUnknownKeys(true); // As requested, less secure

        var currentDateString = LocalDate.now().format(DATE_FORMATTER);
        var searchPrefix = FILENAME_PREFIX + currentDateString + "_";

        try (var session = factory.getSession()) {
            if (session == null) {
                throw new IOException("Failed to obtain SFTP session for host " + host);
            }

            var fileEntries = session.list(".");

            var latestMatchingFile = Arrays.stream(fileEntries)
                    .filter(entry ->
                            entry != null &&
                                    entry.getAttributes() != null &&
                                    !entry.getAttributes().isDirectory() &&
                                    entry.getFilename() != null &&
                                    entry.getFilename().startsWith(searchPrefix) &&
                                    entry.getFilename().endsWith(FILENAME_SUFFIX) &&
                                    !entry.getFilename().equals(".") &&
                                    !entry.getFilename().equals("..")
                    )
                    .max(Comparator.comparingLong(entry -> entry.getAttributes().getModifyTime().toMillis()));

            if (latestMatchingFile.isEmpty()) {
                throw new GenericTechnicalException("No file found matching prefix '" + searchPrefix + "' for date " + currentDateString);
            }

            var fileToDownload = latestMatchingFile.get().getFilename();

            try (var outputStream = new ByteArrayOutputStream()) {
                session.read(fileToDownload, outputStream);
                return new ByteArrayInputStream(outputStream.toByteArray());
            }

        } catch (RuntimeException | IOException e) {
            throw new GenericTechnicalException("SFTP operation failed for host " + host + ": " + e.getMessage(), e);
        }
    }

}
