package com.erhgo.services.externaloffer.taleez;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.externaloffer.candidature.AbstractATSWithHttpClientNotificationSender;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.send.SendCandidaturesRecruiterProvider;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
// See https://taleez.notion.site/API-Candidature-documentation-publique-a5e6672789bf417c9d9bd4ce2bdfae71
public class TaleezATSApiNotificationScheduler extends AbstractATSWithHttpClientNotificationSender {

    public static final String PARTNER_KEY_HEADER = "X-Partner-Key";
    private final ObjectMapper objectMapper;

    public TaleezATSApiNotificationScheduler(
            RecruitmentCandidatureRepository recruitmentCandidatureRepository,
            SpontaneousCandidatureRepository spontaneousCandidatureRepository,
            AbstractCandidatureRepository abstractCandidatureRepository,
            KeycloakService keycloakService,
            ConfigurablePropertyRepository configurablePropertyRepository,
            UserProfileCompetencesExportService userProfileCompetencesExportService,
            SecurityService securityService,
            List<AtsSendCandidaturesConfig> taleezSendCandidaturesConfig,
            TransactionTemplate transactionTemplate,
            ObjectMapper objectMapper,
            SendCandidaturesRecruiterProvider sendCandidaturesRecruiterProvider
    ) {
        super(keycloakService, configurablePropertyRepository, userProfileCompetencesExportService, transactionTemplate, recruitmentCandidatureRepository, spontaneousCandidatureRepository, taleezSendCandidaturesConfig, securityService, abstractCandidatureRepository, sendCandidaturesRecruiterProvider);
        this.objectMapper = objectMapper;
    }

    @Override
    @Scheduled(initialDelay = 27, fixedRate = 60, timeUnit = TimeUnit.MINUTES)
    @SchedulerLock(name = "handleNewCandidaturesForTaleezATS", lockAtLeastFor = "35M")
    public void handleNewCandidatures() {
        super.handleNewCandidatures();
    }

    protected Request buildSendCandidatureRequest(UserRepresentation userRepresentation, RecruitmentCandidature candidature, AtsSendCandidaturesConfig config) throws IOException {
        var fullProfile = getProfile(candidature, userRepresentation.getFullname(), ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE);
        var fullProfileContent = fullProfile.readAllBytesAsStringifiedBase64();
        var body = transactionTemplate.execute(__ -> TaleezSendCandidatureCommandDTO.builder()
                .firstName(userRepresentation.getFirstNameNeverEmpty())
                .lastName(userRepresentation.getLastNameNeverEmpty())
                .jobToken(candidature.getRecruitment().getExternalOffer().getRemoteId())
                .email(userRepresentation.getEmail())
                .cvData(fullProfileContent)
                .cvName(fullProfile.fileName())
                .phone(candidature.getUserProfile().getPhoneNumber())
                .build());

        return new Request.Builder()
                .url(config.getCandidatureNotificationUrl())
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .header(PARTNER_KEY_HEADER, config.getCandidatureNotificationApiKey())
                .post(RequestBody.create(objectMapper.writeValueAsBytes(body)))
                .build();

    }

    @Override
    protected boolean shouldConsiderAsAlreadySent(int code, String body) {
        return code == HttpStatus.CONFLICT.value();
    }

    @Override
    protected boolean isCandidatureOnClosedRecruitment(int code, String body) {
        return HttpStatus.GONE.value() == code;
    }

}
