package com.erhgo.services.externaloffer.config;

import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.services.dtobuilder.SourcingDTOBuilder;
import com.erhgo.services.externaloffer.*;
import com.erhgo.services.externaloffer.covea.CoveaJob;
import com.erhgo.services.externaloffer.covea.CoveaSynchronizer;
import com.erhgo.services.externaloffer.covea.SimpleSftpClient;
import com.erhgo.services.externaloffer.firecrawl.ExternalOfferScraper;
import com.erhgo.services.externaloffer.firecrawl.FirecrawlSynchronizer;
import com.erhgo.services.externaloffer.firecrawl.ScrappedJob;
import com.erhgo.services.externaloffer.parsers.GenericJobJsonParser;
import com.erhgo.services.externaloffer.recruiterdispatcher.fetch.ExternalOfferRecruiterProvider;
import com.erhgo.services.recruitmentimporter.OfferCsvParserService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;

@RequiredArgsConstructor
@Configuration
public class AtsConfig {

    private final ExternalOfferRepository externalOfferRepository;
    private final ConfigurablePropertyRepository configurablePropertyRepository;
    private final GenericAtsClient genericAtsClient;
    private final SourcingDTOBuilder sourcingDTOBuilder;
    private final ExternalOfferRecruiterProvider recruiterProvider;
    private final ApplicationContext applicationContext;
    private final OfferCsvParserService offerCsvParserService;
    private final ExternalOfferScraper externalOfferScraper;

    @Bean
    @Scope("prototype")
    public <A extends AbstractRemoteOfferContent<A>, B extends AtsDataBuilder<A>> GenericATSSynchronizer<A> genericExternalOfferService(
            B atsDataBuilder,
            AtsGetOfferConfig config,
            ExternalJobParser<A> parser) {
        return new GenericATSSynchronizer<>(
                externalOfferRepository,
                configurablePropertyRepository,
                genericAtsClient,
                atsDataBuilder,
                config,
                parser,
                sourcingDTOBuilder,
                recruiterProvider,
                applicationContext
        );
    }

    @Bean
    @Scope("prototype")
    public CoveaSynchronizer coveaSynchronizer(AtsGetOfferConfig config, GenericJobJsonParser<CoveaJob> parser) {
        return new CoveaSynchronizer(
                configurablePropertyRepository,
                externalOfferRepository,
                recruiterProvider,
                config,
                applicationContext,
                offerCsvParserService,
                parser,
                simpleSftpClient());
    }

    @Bean
    @Scope("prototype")
    public FirecrawlSynchronizer firecrawlSynchronizer(AtsGetOfferConfig config, GenericJobJsonParser<ScrappedJob> parser) {
        return new FirecrawlSynchronizer(
                configurablePropertyRepository,
                externalOfferRepository,
                recruiterProvider,
                config,
                applicationContext,
                parser,
                externalOfferScraper);
    }

    @Bean
    public SimpleSftpClient simpleSftpClient() {
        return new SimpleSftpClient();
    }

}
