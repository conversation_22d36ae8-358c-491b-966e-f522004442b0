package com.erhgo.services.externaloffer.softy;

import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import com.erhgo.services.externaloffer.config.ParserProvider;
import com.erhgo.services.externaloffer.parsers.GenericJobXmlParser;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeansException;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.event.EventListener;

import java.util.ArrayList;
import java.util.List;

@Configuration
@EnableConfigurationProperties
@RequiredArgsConstructor
public class SoftyConfiguration {
    private final AtsDataForSoftyBuilder atsDataBuilder;
    private final ExternalOfferServiceProvider serviceProvider;
    private final ParserProvider parserProvider;

    @Bean
    @ConfigurationProperties(prefix = "ats.softy.fetch")
    public List<AtsGetOfferConfig> softyConfigurations() {
        return new ArrayList<>();
    }

    @Bean
    @Scope("prototype")
    public GenericJobXmlParser<SoftyJob> softyXmlParser(AtsGetOfferConfig config) {
        return new GenericJobXmlParser<>(SoftyJob.class, config.getRootPath(), config.getAtsCode());
    }

    @Bean
    @ConfigurationProperties(prefix = "ats.softy.send")
    public List<AtsSendCandidaturesConfig> softySendCandidaturesConfig() {
        return new ArrayList<>();
    }

    @EventListener(ApplicationReadyEvent.class)
    public void initializeSoftyServices() throws BeansException {
        softyConfigurations().forEach(config ->
                {
                    var softyXmlParser = parserProvider.initializeBean(config.getAtsCode(), "softyXmlParser", config);
                    serviceProvider.initializeGenericService(
                            atsDataBuilder,
                            config,
                            softyXmlParser);
                }
        );
    }
}
