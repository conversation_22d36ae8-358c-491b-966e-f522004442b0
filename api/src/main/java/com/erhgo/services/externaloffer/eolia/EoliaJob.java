package com.erhgo.services.externaloffer.eolia;


import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.openapi.dto.TypeContractCategoryDTO;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.utils.XMLUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.MatchResult;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Optional.ofNullable;


@Slf4j
@JacksonXmlRootElement(localName = "job")
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class EoliaJob extends AbstractRemoteOfferContent<EoliaJob> {
    private static final Set<String> FIELDS_TO_IGNORE = Set.of("lastModificationDate");
    private static final String DATEMODIFICATION_TAG = "datemodification";
    private static final String DATE_PATTERN = "dd/MM/yyyy HH:mm:ss";
    public static final String CITY_KEY = "saisie2";

    @JacksonXmlProperty(localName = "ref")
    private String id;

    @JacksonXmlProperty(localName = "datecreation")
    @JsonFormat(pattern = DATE_PATTERN)
    private LocalDateTime creationDate;

    @JacksonXmlProperty(localName = DATEMODIFICATION_TAG)
    @JsonFormat(pattern = DATE_PATTERN)
    @Getter
    private LocalDateTime lastModificationDate;

    @JacksonXmlProperty(localName = "datedernierepublication")
    @JsonFormat(pattern = DATE_PATTERN)
    private LocalDateTime lastRepublishDate;

    @JacksonXmlProperty(localName = "nomposte")
    private String offerTitle;

    @JacksonXmlProperty(localName = "consultant")
    private String referer1;

    @JacksonXmlProperty(localName = "consultant2")
    private String referer2;

    @JacksonXmlProperty(localName = CITY_KEY)
    private String location;

    @JacksonXmlProperty(localName = "nomclient")
    private String remoteRecruiterCode;
    @JsonIgnore
    @Getter(AccessLevel.PACKAGE)
    private final Map<String, String> occupationTitles = new HashMap<>();
    @JsonIgnore
    @Getter(AccessLevel.PACKAGE)
    private final Map<String, String> typeContractInformations = new HashMap<>();
    @JsonIgnore
    @Getter(AccessLevel.PACKAGE)
    private final Map<String, String> descriptionParts = new HashMap<>();
    @JsonIgnore
    @Getter(AccessLevel.PACKAGE)
    private final Map<String, String> organizationDescriptionParts = new HashMap<>();
    @JsonIgnore
    @Getter(AccessLevel.PACKAGE)
    private final Map<String, String> localisationInformations = new HashMap<>();
    @JsonIgnore
    private final Map<String, String> otherInformations = new HashMap<>();
    @JsonIgnore
    @Getter(AccessLevel.PACKAGE)
    private final Map<String, String> criteriaRelatedData = new HashMap<>();
    @JsonIgnore
    @Getter(AccessLevel.PACKAGE)
    private final Map<String, String> salaryRelatedData = new HashMap<>();
    @JsonIgnore
    private final Map<String, Map<String, String>> actionMap = new HashMap<>();

    public EoliaJob() {
        actionMap.put("saisie1", occupationTitles);
        actionMap.put("listerecrut1", occupationTitles);
        actionMap.put("listerecrut2", occupationTitles);
        actionMap.put("activite", typeContractInformations);
        actionMap.put("liste1", typeContractInformations);
        actionMap.put("memo2", descriptionParts);
        actionMap.put("memo3[@label!='Société']", descriptionParts);
        actionMap.put("memo3[@label='Société']", organizationDescriptionParts);
        actionMap.put("memo1[@label=\"Présentation de l'entreprise\"]", organizationDescriptionParts);
        actionMap.put("memo4", descriptionParts);
        actionMap.put("memo5", organizationDescriptionParts);
        actionMap.put("pays", localisationInformations);
        actionMap.put("region", localisationInformations);
        actionMap.put("saisierecrut4", localisationInformations);
        actionMap.put(CITY_KEY, localisationInformations);
        actionMap.put("listerecrut7", localisationInformations);
        actionMap.put("listerecrut6[@label!='Région']", criteriaRelatedData);
        actionMap.put("listerecrut6[@label='Région']", localisationInformations);
        actionMap.put("listerecrut3", salaryRelatedData);
    }

    private void putKeyAndLabelInMap(Map<String, String> map, String xpath) {
        var node = XMLUtils.getNodeByXPath(rawContent, "/job/%s".formatted(xpath));
        if (node != null) {
            var label = XMLUtils.getNodeLabel(node);
            var formattedKey = label != null ? "%s (%s)".formatted(xpath, label) : xpath;
            var key = (label != null && (map == descriptionParts || map == organizationDescriptionParts)) ? label : formattedKey;
            map.put(key, XMLUtils.getNodeContent(node));
        }
    }

    public void extractDataFromRawXml() {
        actionMap.forEach((relativeXpath, mapToUpdate) -> putKeyAndLabelInMap(mapToUpdate, relativeXpath));
        XMLUtils.findAllTags(rawContent, "/job").stream().filter(a -> !DATEMODIFICATION_TAG.equals(a) && !actionMap.containsKey(a)).forEach(k -> putKeyAndLabelInMap(otherInformations, k));
    }

    @Override
    public EoliaJob setRawContent(String xml) {
        super.setRawContent(xml);
        extractDataFromRawXml();
        return this;
    }

    public List<String> getCriterias() {
        if (criteriaRelatedData.containsValue("Oui")) {
            return List.of(CriteriaValue.WEEKEND_WORK_CRITERIA_VALUE_CODE);
        }

        return List.of();
    }

    public String getDescription() {
        return descriptionParts.values().stream()
                .collect(Collectors.joining(System.lineSeparator()));
    }

    public String getOrganizationDescription() {
        return organizationDescriptionParts.values().stream().findFirst().orElse(null);
    }

    public List<Integer> getSalaryValues() {
        var salaryText = salaryRelatedData.values().stream().findFirst().orElse(null);
        var pattern = Pattern.compile("\\d+");

        if (salaryText != null) {
            return pattern.matcher(salaryText).results().map(MatchResult::group).mapToInt(num -> Integer.parseInt(num) * 1000).boxed().toList();
        }

        return Collections.emptyList();
    }

    public TypeContractCategoryDTO getTypeContractCategory() {
        if (mapHasValueContainingIgnoringCase(typeContractInformations, "CDI")) {
            return TypeContractCategoryDTO.PERMANENT;
        } else if (mapHasValueContainingIgnoringCase(typeContractInformations, "CDD")) {
            return TypeContractCategoryDTO.TEMPORARY;
        } else if (mapHasValueContainingIgnoringCase(typeContractInformations, "ALTERNANCE")) {
            return TypeContractCategoryDTO.PRO;
        }

        return null;
    }

    private boolean mapHasValueContainingIgnoringCase(Map<String, String> typeContractInformations, String query) {
        return typeContractInformations.values().stream().anyMatch(v -> v.toLowerCase().contains(query.toLowerCase()));
    }

    /**
     * This custom code to include modificationdate in otherInfo map BUT not consider jobs modified if it is the only
     * modification
     */
    @JsonIgnore
    public Map<String, String> getOtherInformations() {
        var all = new HashMap<>(otherInformations);
        all.put(DATEMODIFICATION_TAG, DateTimeFormatter.ofPattern(DATE_PATTERN).format(lastModificationDate));
        return all;
    }

    @Override
    public Collection<String> getFieldsToIgnore() {
        return new HashSet<>(FIELDS_TO_IGNORE);
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public List<String> getRelatedUsernames() {
        return Stream.of(ofNullable(referer1), ofNullable(referer2)).filter(Optional::isPresent).map(Optional::get).toList();
    }

    @Override
    public String getLocationIndication() { return getLocation(); }

    @Override
    public boolean isInFrance() {
        return true;
    }

    @Override
    public boolean isInsideAURA() {
        return super.isInsideAURA() || mapHasValueContainingIgnoringCase(localisationInformations, "AUVERGNE");
    }
}
