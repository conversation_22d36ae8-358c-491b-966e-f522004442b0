package com.erhgo.services.externaloffer.covea;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.externaloffer.GenericAtsClient;
import com.erhgo.services.externaloffer.candidature.AbstractATSWithHttpClientNotificationSender;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.send.SendCandidaturesRecruiterProvider;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import com.erhgo.utils.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

// see https://csod.dev/reference/recruiting/#tag/Candidates/operation/post-x-candidate-v1-application
@Slf4j
@Service
@ConditionalOnProperty(name = "ats.covea.enabled", havingValue = "true")
public class CoveaApiCandidatureNotificationScheduler extends AbstractATSWithHttpClientNotificationSender {

    public static final String ERROR_CODE_FOR_CLOSED_RECRUITMENT = "3002101";
    private final ObjectMapper objectMapper;
    private final GenericAtsClient genericAtsClient;

    public CoveaApiCandidatureNotificationScheduler(
            RecruitmentCandidatureRepository recruitmentCandidatureRepository,
            SpontaneousCandidatureRepository spontaneousCandidatureRepository,
            AbstractCandidatureRepository abstractCandidatureRepository,
            KeycloakService keycloakService,
            ConfigurablePropertyRepository configurablePropertyRepository,
            UserProfileCompetencesExportService userProfileCompetencesExportService,
            SecurityService securityService,
            List<AtsSendCandidaturesConfig> coveaSendCandidaturesConfig,
            TransactionTemplate transactionTemplate,
            ObjectMapper objectMapper,
            SendCandidaturesRecruiterProvider sendCandidaturesRecruiterProvider,
            GenericAtsClient genericAtsClient
    ) {
        super(keycloakService, configurablePropertyRepository, userProfileCompetencesExportService, transactionTemplate, recruitmentCandidatureRepository, spontaneousCandidatureRepository, coveaSendCandidaturesConfig, securityService, abstractCandidatureRepository, sendCandidaturesRecruiterProvider);
        this.objectMapper = objectMapper;
        this.genericAtsClient = genericAtsClient;
    }

    @Override
    @Scheduled(initialDelay = 48, fixedRate = 60, timeUnit = TimeUnit.MINUTES)
    @SchedulerLock(name = "handleNewCandidaturesForCovea", lockAtLeastFor = "35M")
    public void handleNewCandidatures() {
        super.handleNewCandidatures();
    }

    @Override
    protected Request buildSendCandidatureRequest(UserRepresentation userRepresentation, RecruitmentCandidature candidature, AtsSendCandidaturesConfig config) throws IOException {
        var fullProfile = getProfile(candidature, userRepresentation.getFullname(), ProfileCompetencesViewObject.AnonymousMode.BOTH);
        var data = new TreeMap<>(Map.of(
                "jobRequisitionId", candidature.getExternalOfferId(),
                "candidate", Map.of(
                        "email", userRepresentation.getEmail(),
                        "firstName", userRepresentation.getFirstName(),
                        "lastName", userRepresentation.getLastName(),
                        "contactDetails", Map.of("PhoneNumber", candidature.getUserProfile().getPhoneNumber())
                ),
                "source", Map.of("submissionSource", "jenesuisPASunCV"),
                "resume", Map.of(
                        "fileName", "profilDeCompetences.pdf",
                        "file", StringUtils.toStringifiedBase64(fullProfile.readAllBytes())
                ))
        );

        return new Request.Builder()
                .url(config.getCandidatureNotificationUrl())
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + genericAtsClient.generateToken(config.getTokenAuthentication()))
                .post(RequestBody.create(objectMapper.writeValueAsBytes(data)))
                .build();
    }

    @Override
    protected boolean isCandidatureOnClosedRecruitment(int code, String body) {
        return code == 400 && StringUtils.normalizeLowerCase(body).contains(ERROR_CODE_FOR_CLOSED_RECRUITMENT);
    }

}
