package com.erhgo.services.externaloffer.digitalrecruiters;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.externaloffer.candidature.AbstractATSWithHttpClientNotificationSender;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.send.SendCandidaturesRecruiterProvider;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class DRApiCandidatureNotificationScheduler extends AbstractATSWithHttpClientNotificationSender {

    private static final String CONFIG_SOURCE = "jenesuispasuncv";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final ObjectMapper objectMapper;

    public DRApiCandidatureNotificationScheduler(
            RecruitmentCandidatureRepository recruitmentCandidatureRepository,
            SpontaneousCandidatureRepository spontaneousCandidatureRepository,
            AbstractCandidatureRepository abstractCandidatureRepository,
            KeycloakService keycloakService,
            ConfigurablePropertyRepository configurablePropertyRepository,
            UserProfileCompetencesExportService userProfileCompetencesExportService,
            SecurityService securityService,
            List<AtsSendCandidaturesConfig> drSendCandidaturesConfig,
            TransactionTemplate transactionTemplate,
            ObjectMapper objectMapper,
            SendCandidaturesRecruiterProvider sendCandidaturesRecruiterProvider
    ) {
        super(keycloakService, configurablePropertyRepository, userProfileCompetencesExportService, transactionTemplate, recruitmentCandidatureRepository, spontaneousCandidatureRepository, drSendCandidaturesConfig, securityService, abstractCandidatureRepository, sendCandidaturesRecruiterProvider);
        this.objectMapper = objectMapper;
    }

    @Override
    @Scheduled(initialDelay = 9, fixedRate = 60, timeUnit = TimeUnit.MINUTES)
    @SchedulerLock(name = "handleNewCandidaturesForDR", lockAtLeastFor = "35M")
    public void handleNewCandidatures() {
        super.handleNewCandidatures();
    }

    @Override
    protected Request buildSendCandidatureRequest(UserRepresentation userRepresentation, RecruitmentCandidature candidature, AtsSendCandidaturesConfig config) throws IOException {
        var fullProfile = getProfile(candidature, userRepresentation.getFullname(), ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE);
        var profileContent = fullProfile.readAllBytesAsStringifiedBase64();

        var body = Map.of(
                "reference", candidature.getRecruitment().getExternalOffer().getRemoteId(),
                "consent_date", candidature.getSubmissionDate().format(DATE_FORMATTER),
                "s_o", CONFIG_SOURCE,
                "locale", "fr_FR",
                "ApplicationProfile", Map.of(
                        "firstName", Optional.ofNullable(StringUtils.trimToNull(userRepresentation.getFirstName())).orElse("inconnu"),
                        "lastName", Optional.ofNullable(StringUtils.trimToNull(userRepresentation.getLastName())).orElse("inconnu"),
                        "email", userRepresentation.getEmail(),
                        "phoneNumber", StringUtils.trimToEmpty(candidature.getUserProfile().getPhoneNumber()),
                        "addressCity", StringUtils.trimToEmpty(candidature.getUserProfile().getCity()),
                        "addressZip", StringUtils.trimToEmpty(candidature.getUserProfile().getPostcode())
                ),
                "file", Map.of(
                        "content", profileContent,
                        "name", fullProfile.fileName()
                )
        );

        var jsonBody = RequestBody.create(objectMapper.writeValueAsBytes(body));

        return new Request.Builder()
                .url(config.getCandidatureNotificationUrl())
                .post(jsonBody)
                .addHeader(HttpHeaders.AUTHORIZATION, BEARER_PREFIX + config.getCandidatureNotificationApiKey())
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .build();
    }
}
