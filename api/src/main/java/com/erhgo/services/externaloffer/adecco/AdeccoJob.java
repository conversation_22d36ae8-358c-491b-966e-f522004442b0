package com.erhgo.services.externaloffer.adecco;

import com.erhgo.openapi.dto.TypeContractCategoryDTO;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.utils.StringUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.google.common.annotations.VisibleForTesting;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Slf4j
@JacksonXmlRootElement(localName = "item")
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class AdeccoJob extends AbstractRemoteOfferContent<AdeccoJob> {
    private static final String DATE_PATTERN = "yyyy-MM-dd'T'HH:mm:ssX";
    private static final Set<String> FIELDS_TO_IGNORE = Set.of("creationDate");

    @JacksonXmlProperty(localName = "jobOfferRef")
    private String id;

    @JacksonXmlProperty(localName = "PublicationDate")
    @JsonFormat(pattern = DATE_PATTERN)
    private LocalDateTime creationDate;

    @JacksonXmlProperty(localName = "title")
    private String offerTitle;

    @JacksonXmlProperty(localName = "description")
    private String description;

    @JacksonXmlProperty(localName = "PostalCode")
    private String postcode;

    @JacksonXmlProperty(localName = "Town")
    private String city;

    @JacksonXmlProperty(localName = "Origin")
    private String recruiterTitle;

    @JacksonXmlProperty(localName = "ContractType")
    @Getter
    private String contractType;

    @JacksonXmlProperty(localName = "SalaryMin")
    private String salaryMin;

    @JacksonXmlProperty(localName = "SalaryMax")
    private String salaryMax;

    public int getSalaryMax() {
        return getSalaryMax(salaryMax, salaryMin);
    }

    @VisibleForTesting
    static int getSalaryMax(String salaryMax, String salaryMin) {
        if (salaryMax == null || salaryMax.isEmpty()) return 0;
        var parts = Stream.of(salaryMax.split(",")).filter(org.apache.commons.lang3.StringUtils::isNumeric).toArray(String[]::new);
        if (parts.length > 0) {
            var valueMax = parseInteger(parts[0]);
            return parseYearlySalary(valueMax);
        }

        if (salaryMin == null || salaryMin.isEmpty()) return 0;
        var minParts = Stream.of(salaryMin.split(",")).filter(org.apache.commons.lang3.StringUtils::isNumeric).toArray(String[]::new);
        if (minParts.length > 1) {
            var valueMin = parseInteger(minParts[1]);
            return parseYearlySalary(valueMin);
        }

        return 0;
    }

    public int getSalaryMin() {
        return getSalaryMin(salaryMin);
    }

    @VisibleForTesting
    static int getSalaryMin(String salaryMin) {
        if (salaryMin == null || salaryMin.isEmpty()) return 0;
        var parts = Stream.of(salaryMin.split(",")).filter(org.apache.commons.lang3.StringUtils::isNumeric).toArray(String[]::new);
        if (parts.length > 0) {
            var value = parseInteger(parts[0]);
            return parseYearlySalary(value);
        }

        return 0;
    }

    private static int parseYearlySalary(int value) {
        if (value > 1000) {
            if (value < 3000) {
                return value * 12;
            } else {
                return value;
            }
        } else {
            return 0;
        }
    }

    private static int parseInteger(String value) {
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            log.warn("invalid salary number: {}", value);
            return 0;
        }
    }

    @Override
    public String getLocationIndication() {
        var trimCity = org.apache.commons.lang3.StringUtils.trimToEmpty(city);
        var trimPostcode = org.apache.commons.lang3.StringUtils.trimToEmpty(postcode);
        return trimCity.isEmpty() && trimPostcode.isEmpty() ? "" : "%s (%s)".formatted(trimCity, trimPostcode);
    }

    @Override
    public String getDescription() { return description; }

    @Override
    public String getOrganizationDescription() { return ""; }

    @Override
    public String getOfferTitle() { return offerTitle.trim(); }

    @Override
    public TypeContractCategoryDTO getTypeContractCategory() {
        var comparator = StringUtils.DIATRIC_AND_CASE_INSENSITIVE_COMPARATOR.INSTANCE;
        return switch (contractType) {
            case String type when comparator.compare(type, "Intérim") == 0 -> TypeContractCategoryDTO.TEMPORARY;
            case String type when comparator.compare(type, "CDD") == 0 -> TypeContractCategoryDTO.TEMPORARY;
            case String type when comparator.compare(type, "Alternance") == 0 -> TypeContractCategoryDTO.PRO;
            default -> TypeContractCategoryDTO.PERMANENT;
        };
    }

    @Override
    public List<String> getCriterias() { return List.of(); }

    @Override
    public String getRemoteRecruiterCode() {
        return recruiterTitle;
    }

    @Override
    public Collection<String> getFieldsToIgnore() {
        return FIELDS_TO_IGNORE;
    }

    @Override
    public List<Integer> getSalaryValues() { return IntStream.of(getSalaryMin(), getSalaryMax()).filter(s -> s > 0).sorted().boxed().toList(); }

    @Override
    public String getId() { return id; }

    @Override
    public LocalDateTime getLastModificationDate() { return creationDate; }

    @Override
    public boolean isInFrance() {
        return true;
    }

}
