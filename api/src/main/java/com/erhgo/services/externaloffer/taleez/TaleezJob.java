package com.erhgo.services.externaloffer.taleez;


import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.openapi.dto.LocationDTO;
import com.erhgo.openapi.dto.TypeContractCategoryDTO;
import com.erhgo.openapi.dto.WorkingTimeDTO;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Stream;


@Slf4j
@JacksonXmlRootElement(localName = "job")
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class TaleezJob extends AbstractRemoteOfferContent<TaleezJob> {
    private static final String DATE_PATTERN = "yyyy-MM-dd";
    protected static final String DEFAULT_LOCATION_INDICATION_WHEN_REMOTE_WORK = "Lyon (69003)";
    private static final String REMOTE_WORK_MARKER = "télétravail";
    private static final String PUBLISHED = "PUBLISHED";

    @JacksonXmlProperty(localName = "title")
    private String offerTitle;
    @JacksonXmlProperty
    private String url;
    @JacksonXmlProperty(localName = "referencenumber")
    private String id;
    @JacksonXmlProperty(localName = "companydescription")
    private String organizationDescription;
    @JacksonXmlProperty(localName = "description")
    private String jobDescription;
    @JacksonXmlProperty
    private String profile;
    @JacksonXmlProperty
    @JsonFormat(pattern = DATE_PATTERN)
    private LocalDate date;
    @JacksonXmlProperty
    private String jobtype;
    @JacksonXmlProperty
    private String contractDuration;
    @JacksonXmlProperty
    private String fulltime;
    @JacksonXmlProperty
    private String weeklyHours;
    @JacksonXmlProperty
    private String city;
    @JacksonXmlProperty
    private String postalCode;
    @JacksonXmlProperty
    private String coordinate;

    @JacksonXmlProperty
    private String country;
    @JacksonXmlProperty
    private String status;
    @JacksonXmlProperty(localName = "companyId")
    @Getter
    private String companyId;

    @JsonIgnore
    @Override
    public String getDescription() {
        return "%s%n%s".formatted(StringUtils.trimToEmpty(jobDescription), StringUtils.trimToEmpty(profile));
    }

    @Override
    public List<Integer> getSalaryValues() {
        return Collections.emptyList();
    }

    @Override
    public TypeContractCategoryDTO getTypeContractCategory() {
        return switch (StringUtils.trimToEmpty(jobtype)) {
            case "APPRENTICESHIP" -> TypeContractCategoryDTO.PRO;
            case "FIXEDTERM", "CDD" -> TypeContractCategoryDTO.TEMPORARY;
            case "SEASON", "STUDENT" -> TypeContractCategoryDTO.SEASONAL;
            case "FREELANCE" -> TypeContractCategoryDTO.FREELANCE;
            case "CDI", "PERMANENT" -> TypeContractCategoryDTO.PERMANENT;
            default -> null;
        };
    }

    @Override
    public String getLocationIndication() {
        if (isRemoteWork()) {
            return DEFAULT_LOCATION_INDICATION_WHEN_REMOTE_WORK;
        }
        return "%s (%s)".formatted(StringUtils.trimToEmpty(city), StringUtils.trimToEmpty(postalCode));
    }

    private boolean isRemoteWork() {
        return com.erhgo.utils.StringUtils.DIATRIC_AND_CASE_INSENSITIVE_COMPARATOR.INSTANCE.compare(StringUtils.trimToEmpty(city), REMOTE_WORK_MARKER) == 0;
    }

    @Override
    public List<String> getCriterias() {
        return isRemoteWork() ? List.of(CriteriaValue.getValueCodeForFullRemoteWork()) : Collections.emptyList();
    }

    @Override
    public boolean isInFrance() {
        return StringUtils.trimToEmpty(country).toUpperCase().startsWith("FR");
    }

    @Override
    public boolean shouldNotCreateNewOffer(AtsGetOfferConfig config) {
        return super.shouldNotCreateNewOffer(config) || !PUBLISHED.equalsIgnoreCase(StringUtils.trimToEmpty(status));
    }

    @Override
    public LocationDTO getBuiltinLocation() {
        try {
            var latLong = Stream.of(StringUtils.trimToEmpty(coordinate).split(";"))
                    .filter(StringUtils::isNotBlank)
                    .map(String::trim)
                    .map(Float::parseFloat)
                    .toList();
            var trimmedPostcode = StringUtils.trimToNull(postalCode);
            var trimmedCity = StringUtils.trimToNull(city);

            if (latLong.size() == 2 && trimmedCity != null && trimmedPostcode != null) {
                return new LocationDTO()
                        .latitude(latLong.get(0))
                        .longitude(latLong.get(1))
                        .postcode(trimmedPostcode)
                        .city(trimmedCity)
                        ;
            }
        } catch (IllegalArgumentException e) {
            log.warn("Unable to parse coordinates for {}", coordinate, e);
        }
        return null;
    }

    @Override
    public Integer getWorkingTimeWeeklyDuration() {
        if (StringUtils.isBlank(weeklyHours)) return null;
        try {
            return ((Float) Float.parseFloat(weeklyHours)).intValue();
        } catch (IllegalArgumentException e) {
            log.warn("unable to parse weekly hours for {}", weeklyHours, e);
        }
        return null;
    }

    @Override
    public LocalDateTime getLastModificationDate() {
        return Optional.ofNullable(date).map(d -> d.atTime(0, 0)).orElse(null);
    }

    @Override
    public String getId() {
        return StringUtils.trimToEmpty(id);
    }

    @Override
    public String getRemoteRecruiterCode() {
        return StringUtils.trimToEmpty(companyId);
    }

    @Override
    public String getOrganizationDescription() {
        return StringUtils.trimToEmpty(organizationDescription);
    }

    @Override
    public String getOfferTitle() {
        return StringUtils.trimToEmpty(offerTitle);
    }

    @Override
    public WorkingTimeDTO getWorkingTimeType() {
        return Optional.ofNullable(fulltime)
                .map(f -> StringUtils.trimToEmpty(f).equalsIgnoreCase("false") ? WorkingTimeDTO.PART_TIME : WorkingTimeDTO.FULL_TIME)
                .orElse(super.getWorkingTimeType());
    }

    public String getJobType() {
        return StringUtils.trimToNull(jobtype);
    }

    @Override
    protected Collection<String> getFieldsToIgnore() {
        return Set.of("date");
    }

}

