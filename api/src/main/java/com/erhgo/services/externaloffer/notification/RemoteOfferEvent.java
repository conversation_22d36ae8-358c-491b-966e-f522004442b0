package com.erhgo.services.externaloffer.notification;

import com.erhgo.domain.externaloffer.ExternalOffer;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@AllArgsConstructor
@Getter
public class RemoteOfferEvent {
    private List<ExternalOffer> createdOffers;
    private List<ExternalOffer> suspendedOffers;
    private List<ExternalOffer> reactivatedOffers;
    private List<ExternalOffer> modifiedOffers;
    private AtsGetOfferConfig atsConfig;

    public boolean hasAnyChange() {
        return getChangesCount() > 0;
    }

    public int getChangesCount() {
        return createdOffers.size() + suspendedOffers.size() + reactivatedOffers.size() + modifiedOffers.size();
    }

    public String getAtsCode() {
        return atsConfig.getAtsCode();
    }

    public String getForcedSlackChannel() {
        return atsConfig.getForcedSlackChannel();
    }
}
