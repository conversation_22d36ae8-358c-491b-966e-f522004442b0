package com.erhgo.services.externaloffer.recruiterdispatcher.fetch;

import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.PerOfferATSConfigurationItem;
import com.erhgo.services.externaloffer.recruiterdispatcher.PerOfferATSConfigurationItemRepository;
import com.erhgo.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * Per location recruiter provider search in {@link PerOfferATSConfigurationItem} items, based firstly on postcode,
 * then on departmentCode
 */
@Service
@RequiredArgsConstructor
class PerLocationFetcher implements ExternalOfferRecruiterFinder {

    private final PerOfferATSConfigurationItemRepository repository;

    @Override
    public <A extends AbstractRemoteOfferContent<A>> String findRecruiterCodeFor(A offer, AtsGetOfferConfig atsConfig) {
        var postcode = StringUtils.extractPostcode(offer.getLocationIndication());
        if (postcode != null && postcode.length() >= 2) {
            var departmentCode = postcode.substring(0, 2);
            return repository.findOneByAtsCodeAndConfigCodeAndLocationCode(atsConfig.getAtsCode(), atsConfig.getConfigCode(), postcode)
                    .or(() -> repository.findOneByAtsCodeAndConfigCodeAndLocationCode(atsConfig.getAtsCode(), atsConfig.getConfigCode(), departmentCode))
                    .map(PerOfferATSConfigurationItem::getRecruiterCode)
                    .orElse(null);
        }
        return null;
    }
}
