package com.erhgo.services.externaloffer;

import com.erhgo.domain.externaloffer.ExternalOffer;
import com.erhgo.openapi.dto.CreateOrUpdateFullRecruitmentCommandDTO;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;

public interface RecruitmentAdjuster {

    void adjustRecruitmentCreationCommand(ExternalOffer externalOffer, CreateOrUpdateFullRecruitmentCommandDTO recruitmentCreationCommand, AtsGetOfferConfig atsConfig);
    
}
