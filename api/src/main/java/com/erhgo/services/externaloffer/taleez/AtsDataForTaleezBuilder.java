package com.erhgo.services.externaloffer.taleez;

import com.erhgo.openapi.dto.ExtractedAtsDataDTO;
import com.erhgo.services.externaloffer.AtsDataBuilder;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class AtsDataForTaleezBuilder implements AtsDataBuilder<TaleezJob> {

    public ExtractedAtsDataDTO buildAtsData(TaleezJob job) {
        return job == null ? null : new ExtractedAtsDataDTO()
                .typeContractInformations(Map.of("contrat", String.valueOf(job.getJobType())))
                .occupationTitles(Map.of("occupation", job.getOfferTitle()))
                .descriptionParts(Map.of("description", String.valueOf(job.getDescription())))
                .organizationDescriptionParts(Map.of("organisation", job.getOrganizationDescription()))
                .localisationInformations(Map.of("ville", String.valueOf(job.getLocationIndication())))
                .criteriaRelatedData(Map.of("critère", String.join(", ", job.getCriterias())))
                .salaryRelatedData(Map.of("salary", job.getSalaryValues().stream().map(String::valueOf).collect(Collectors.joining(", "))))
                ;
    }
}
