package com.erhgo.services.externaloffer;

import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.exceptions.AbstractFunctionalException;
import com.erhgo.domain.exceptions.AbstractTechnicalException;
import com.erhgo.domain.externaloffer.ExternalOffer;
import com.erhgo.domain.externaloffer.RecruitmentCreationState;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.services.externaloffer.config.ParserProvider;
import com.erhgo.services.externaloffer.notification.RemoteOfferErrorDTO;
import com.erhgo.services.externaloffer.notification.RemoteOfferEvent;
import com.erhgo.services.generation.OccupationForLabelGenerationService;
import com.erhgo.services.generation.dto.OccupationForLabelGenerationResult;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.notifier.OccupationCreationSourceType;
import com.erhgo.services.search.GeoService;
import com.erhgo.services.sourcing.SourcingJobRecruitmentService;
import com.erhgo.services.sourcing.SourcingUserSearchService;
import jakarta.validation.ConstraintViolationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Consumer;

@RequiredArgsConstructor
@Service
@Slf4j
public class ExternalOfferRecruitmentService {
    private final Notifier notifier;
    private final SourcingJobRecruitmentService sourcingJobService;
    private final GeoService geoService;
    private final OccupationForLabelGenerationService erhgoOccupationImporterService;
    private final SourcingUserSearchService sourcingUserSearchService;
    private final ParserProvider parserProvider;
    private final List<RecruitmentAdjuster> recruitmentAdjusters;
    private final ExternalOfferRepository externalOfferRepository;

    @EventListener(RemoteOfferEvent.class)
    public void handleExternalOffersUpdates(RemoteOfferEvent event) {
        event.getSuspendedOffers().forEach(safely(this::closeOfferRecruitment));
        event.getReactivatedOffers().forEach(safely(r -> republishOfferRecruitment(r, event)));
        event.getCreatedOffers().stream()
                .filter(o -> o.getRecruitmentCreationState() != RecruitmentCreationState.MANUAL)
                .filter(o -> o.getRecruitmentCreationState() != RecruitmentCreationState.IGNORE)
                .forEach(safely(r -> createOrUpdateRecruitmentForOffer(r, event)));
        event.getModifiedOffers().forEach(safely(r -> modifyRecruitment(r, event)));
    }

    private void modifyRecruitment(ExternalOffer externalOffer, RemoteOfferEvent event) {
        var existingRecruitment = externalOffer.getRecruitment();
        if (existingRecruitment == null) {
            log.info("Creating recruitment on modification detection without existing recruitment - offer {} ", externalOffer.getUuid());
        }
        createOrUpdateRecruitmentForOffer(externalOffer, event);
    }

    private Consumer<? super ExternalOffer> safely(Consumer<ExternalOffer> unsafeConsumer) {
        return externalOffer -> {
            try {
                unsafeConsumer.accept(externalOffer);
            } catch (AbstractTechnicalException | AbstractFunctionalException | IllegalArgumentException |
                     DataIntegrityViolationException | ConstraintViolationException e) {
                log.error("failed to handle recruitment creation or update from {} and external offer id {} - ignore and continue", externalOffer.getAtsCode(), externalOffer.getUuid(), e);
            } catch (RuntimeException e) {
                log.error(
                        "Unexpected error during recruitment creation for offer {} - no recruitment created",
                        Optional.ofNullable(externalOffer).map(ExternalOffer::getUuid).map(UUID::toString).orElse(" NULL! "),
                        e
                );
            }
        };

    }

    private void createOrUpdateRecruitmentForOffer(ExternalOffer externalOffer, RemoteOfferEvent event) {
        log.debug("Creating or updating recruitment for external offer {} with remoteId {}", externalOffer.getUuid(), externalOffer.getRemoteId());
        var remoteJob = parserProvider.getParser(externalOffer.getAtsCode()).parseJob(externalOffer.getLastRawContent());
        LocationDTO coordinates;
        if (remoteJob.hasBuiltinLocation()) {
            coordinates = remoteJob.getBuiltinLocation();
        } else {
            coordinates = geoService.fetchGeoCoordinates(externalOffer.getOfferLocation(), "ATS - offer %s".formatted(externalOffer.getUuid()));
        }
        if (coordinates == null || coordinates.getCity() == null) {
            log.error("No city found for external offer {} location {} and config {} - No recruitment created for this offer", externalOffer.getUuid(), externalOffer.getOfferLocation(), event.getAtsConfig());
            return;
        }
        var offerTitle = externalOffer.getOfferTitle();
        var occupationId = Optional.ofNullable(externalOffer.getRecruitment())
                .map(Recruitment::getErhgoOccupation)
                .map(ErhgoOccupation::getId)
                .or(() -> Optional.of(erhgoOccupationImporterService.createOrUpdateOccupation(offerTitle, OccupationCreationSourceType.FROM_ATS)).filter(r -> !r.inError()).map(OccupationForLabelGenerationResult::uuid))
                .orElse(null);
        if (occupationId == null) {
            log.warn("Unable to generate occupation - probably malformed title  {}, for external offer {}, location {} and config {} - No recruitment created for this offer", offerTitle, externalOffer.getUuid(), externalOffer.getOfferLocation(), event.getAtsConfig());
            externalOffer.setRecruitmentCreationState(RecruitmentCreationState.IGNORE);
            externalOfferRepository.save(externalOffer);
            return;
        }
        var salaryValues = remoteJob.getSalaryValues();
        var recruiterCode = externalOffer.getComputedRecruiterCode();
        if (recruiterCode == null) {
            log.error("No recruiter found for external offer {}, location {} and config {} - No recruitment created for this offer", externalOffer.getUuid(), externalOffer.getOfferLocation(), event.getAtsConfig());
            return;
        }
        var sourcingUserReferer = sourcingUserSearchService.searchUserInGroup(recruiterCode, externalOffer.relatedUsernames()).stream().map(UserRepresentation::getEmail).toList();

        var optionalRecruitmentId = Optional.ofNullable(externalOffer.getRecruitment()).map(Recruitment::getId);

        var recruitmentCreationCommand = new CreateOrUpdateFullRecruitmentCommandDTO()
                .recruitmentId(optionalRecruitmentId.orElse(null))
                .description(Optional.ofNullable(remoteJob.getDescription()).orElse(""))
                .organizationDescription(Optional.ofNullable(remoteJob.getOrganizationDescription()).orElse(""))
                .jobOccupationId(occupationId)
                .title(remoteJob.getOfferTitle())
                .jobLocation(coordinates)
                .typeContractCategory(Optional.ofNullable(remoteJob.getTypeContractCategory()).orElse(TypeContractCategoryDTO.PERMANENT))
                .workingTimeType(Optional.ofNullable(remoteJob.getWorkingTimeType()).orElse(WorkingTimeDTO.FULL_TIME))
                .workingWeeklyTime(remoteJob.getWorkingTimeWeeklyDuration())
                .baseSalary(salaryValues.stream().findFirst().orElse(null))
                .maxSalary(!salaryValues.isEmpty() ? salaryValues.getLast() : null)
                .modularWorkingTime(false)
                .criteriaValues(remoteJob.getCriterias())
                .hideSalary(remoteJob.getHideSalary())
                .workContractDuration(remoteJob.getWorkContractDuration())
                .workContractDurationUnit(remoteJob.getWorkContractDurationUnit())
                .externalOfferId(externalOffer.getUuid());

        recruitmentAdjusters.forEach(r -> r.adjustRecruitmentCreationCommand(externalOffer, recruitmentCreationCommand, event.getAtsConfig()));

        var recruitmentId = sourcingJobService.createOrUpdateRecruitmentForBatch(
                recruiterCode,
                recruitmentCreationCommand,
                OccupationCreationSourceType.FROM_ATS.getLabel() + " " + externalOffer.getAtsCode(),
                sourcingUserReferer
        );

        sourcingJobService.changeRecruitmentStateForBatch(
                recruitmentId,
                new ChangeSourcingRecruitmentStateCommandDTO()
                        .nextState(RecruitmentStateDTO.PUBLISHED)
                        .sendNotifications(UsersToNotifySelectionTypeDTO.NEW)
        );
        log.debug("Recruitment {} created for external offer {} with remoteId {}", recruitmentId, externalOffer.getUuid(), externalOffer.getRemoteId());
    }

    private void republishOfferRecruitment(ExternalOffer externalOffer, RemoteOfferEvent event) {
        log.debug("Republication of external offer {} with remoteId {}", externalOffer.getUuid(), externalOffer.getRemoteId());
        var recruitmentId = externalOffer.getRecruitmentId();
        if (recruitmentId != null) {
            sourcingJobService.changeRecruitmentStateForBatch(recruitmentId, new ChangeSourcingRecruitmentStateCommandDTO()
                    .nextState(RecruitmentStateDTO.PUBLISHED)
                    .sendNotifications(UsersToNotifySelectionTypeDTO.NEW)
            );
            log.debug("Recruitment {} republished", recruitmentId);
        } else {
            log.warn("No recruitment found for offer {} - unable to republish: let's create it", externalOffer.getUuid());
            createOrUpdateRecruitmentForOffer(externalOffer, event);
        }
        log.debug("Republication of external offer {} with remoteId {} and recruitmentId {} finished", externalOffer.getUuid(), externalOffer.getRemoteId(), recruitmentId);
    }

    private void closeOfferRecruitment(ExternalOffer externalOffer) {
        log.debug("Closing external offer {} with remoteId {} ", externalOffer.getUuid(), externalOffer.getRemoteId());
        var recruitmentId = externalOffer.getRecruitmentId();
        boolean success = false;
        if (recruitmentId != null) {
            sourcingJobService.changeRecruitmentStateForBatch(recruitmentId, new ChangeSourcingRecruitmentStateCommandDTO().nextState(RecruitmentStateDTO.SELECTION));
            var updatedRecruitment = sourcingJobService.getSourcingJobAndRecruitment(recruitmentId);
            success = updatedRecruitment.getRecruitmentState() == RecruitmentStateDTO.SELECTION;
        } else {
            log.warn("No recruitment found for offer {}", externalOffer.getUuid());
        }
        if (!success) {
            notifier.sendMessage(new RemoteOfferErrorDTO(externalOffer.getUuid(), externalOffer.getOfferTitle(), externalOffer.getAtsCode(), externalOffer.getOfferRecruiterCode()));
        }
        log.debug("External offer {} with remoteId {} and recruitmentId {} closed", externalOffer.getUuid(), externalOffer.getRemoteId(), recruitmentId);
    }
}
