package com.erhgo.services.externaloffer.teamtailor;

import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.openapi.dto.TypeContractCategoryDTO;
import com.erhgo.openapi.dto.WorkingTimeDTO;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.utils.DateTimeUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.stream.Stream;

@Data
@ToString
@EqualsAndHashCode(callSuper = true)
@Slf4j
public class TeamtailorJob extends AbstractRemoteOfferContent<TeamtailorJob> {

    private static final String DATE_PATTERN = "yyyy-MM-dd'T'HH:mm:ssXXX";

    @JacksonXmlProperty(localName = "referencenumber")
    private String id;
    private String type;
    private Recruiter recruiter;
    @JacksonXmlProperty(localName = "company")
    private String remoteRecruiterTitle;
    @JacksonXmlProperty(localName = "companyuuid")
    private String remoteRecruiterCode;
    @JacksonXmlProperty(localName = "companydescription")
    private String organizationDescription;

    private String logotype;
    private String headquarters;
    @JacksonXmlProperty(localName = "title")
    private String offerTitle;
    private String description;
    @JsonFormat(pattern = DATE_PATTERN)
    private OffsetDateTime startdate;
    @JsonFormat(pattern = DATE_PATTERN)
    private OffsetDateTime enddate;
    @JsonFormat(pattern = DATE_PATTERN)
    private OffsetDateTime createddate;
    private String remotestatus;
    private String employmenttype;
    private String coverimageurl;
    private String pitch;
    private List<Location> locations;
    @JacksonXmlProperty(localName = "department.name")
    private String departmentName;
    @JacksonXmlProperty(localName = "role.name")
    private String roleName;
    private Salary salary;

    @Data
    public static class Location {
        private String city;
        private String country;
        private Float longitude;
        private String postalcode;
        private String countrycode;
    }

    @Data
    public static class Recruiter {
        private String name;
        private String email;
    }

    @Data
    public static class Salary {
        private Float min;
        private Float max;
        private String timeunit;
    }

    @Override
    public LocalDateTime getLastModificationDate() {
        return DateTimeUtils.offsetDateTimeToLocalDateTime(startdate);
    }

    @Override
    public List<String> getRelatedUsernames() {
        return Optional.ofNullable(recruiter).map(Recruiter::getEmail).filter(StringUtils::isNotBlank).stream().toList();
    }

    @Override
    public String getRecruitersInfos() {
        return Optional.ofNullable(recruiter).map(s -> "%s (%s)".formatted(s.getName(), s.getEmail())).orElse(null);
    }

    @Override
    public String getDescription() {
        var jobDescription = new StringJoiner("<br>");

        if (StringUtils.isNotBlank(getPitch())) {
            jobDescription.add("<h4>%s</h4>".formatted(getPitch()));
            }
        if (StringUtils.isNotBlank(description)) {
            jobDescription.add(description);
        }
        if (StringUtils.isNotBlank(coverimageurl)) {
            jobDescription.add("<br/><img src=\"%s\"/><br/>".formatted(coverimageurl));
        }

        if (StringUtils.isNotBlank(departmentName)) {
            jobDescription.add("<br/><b>Département&nbsp;:</h3>");
            jobDescription.add(departmentName);
        }
        if (StringUtils.isNotBlank(roleName)) {
            jobDescription.add("<br/><b>Votre rôle&nbsp;:</h3>");
            jobDescription.add(roleName);
        }

        return jobDescription.toString();
    }

    @Override
    public TypeContractCategoryDTO getTypeContractCategory() {
        return switch (StringUtils.trimToEmpty(employmenttype).toLowerCase()) {
            case "temporary" -> TypeContractCategoryDTO.TEMPORARY;
            case "apprenticeship" -> TypeContractCategoryDTO.PRO;
            case "internship", "volunteer" -> null;
            default -> TypeContractCategoryDTO.PERMANENT;
        };
    }

    @Override
    public List<String> getCriterias() {
        if (StringUtils.trimToEmpty(remotestatus).contains("full"))
            return List.of(CriteriaValue.getValueCodeForFullRemoteWork());
        if (StringUtils.trimToEmpty(remotestatus).contains("hybrid") || StringUtils.trimToEmpty(remotestatus).contains("temp"))
            return List.of(CriteriaValue.getValueCodeForPartialRemoteWork());
        return Collections.emptyList();
    }


    @Override
    public WorkingTimeDTO getWorkingTimeType() {
        var type = StringUtils.trimToEmpty(employmenttype).toLowerCase();
        if (type.contains("full")) return WorkingTimeDTO.FULL_TIME;
        if (type.contains("part")) return WorkingTimeDTO.PART_TIME;
        return null;
    }

    @Override
    public String getLocationIndication() {
        return getLocationOptional().map(l -> "%s (%s)".formatted(l.getCity(), l.getPostalcode())).orElse(null);
    }

    private @NotNull Optional<Location> getLocationOptional() {
        return Optional.ofNullable(locations).orElse(new ArrayList<>()).stream().filter(Objects::nonNull).findFirst();
    }

    @Override
    public boolean isInFrance() {
        return getLocationOptional().filter(a -> StringUtils.trimToEmpty(a.getCountry()).toLowerCase().startsWith("fr")).isPresent();
    }

    @Override
    public List<Integer> getSalaryValues() {
        if (salary == null) {
            return Collections.emptyList();
        }
        var coef = switch (StringUtils.trimToEmpty(salary.timeunit).toLowerCase()) {
            case "hourly" -> hourlyToYearlySalaryCoefficient();
            case "daily" -> dailyToYearlySalaryCoefficient();
            case "weekly" -> weeklyToYearlySalaryCoefficient();
            case "monthly" -> monthlyToYearlySalaryCoefficient();
            default -> 1;
        };
        return Stream.of(salary.min, salary.max).filter(Objects::nonNull).map(a -> coef * a)
                .map(Float::intValue).toList();
    }

}
