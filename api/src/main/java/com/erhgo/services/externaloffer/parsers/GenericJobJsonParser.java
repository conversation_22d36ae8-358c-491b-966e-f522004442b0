package com.erhgo.services.externaloffer.parsers;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.services.externaloffer.ExternalJobParser;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.utils.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.stream.StreamSupport;

@Slf4j
public class GenericJobJsonParser<A extends AbstractRemoteOfferContent<A>> implements ExternalJobParser<A> {

    @Getter
    private final ObjectMapper objectMapper;
    private final String rootElement;
    protected final Class<A> targetClass;

    public GenericJobJsonParser(Class<A> targetClass, String rootElement) {
        this.rootElement = rootElement;
        this.targetClass = targetClass;
        objectMapper = buildObjectMapperForATS();
    }

    public static @NotNull ObjectMapper buildObjectMapperForATS() {
        final ObjectMapper objectMapper;
        objectMapper = new ObjectMapper();
        var javaTimeModule = new JavaTimeModule();
        var dateFormatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(dateFormatter));
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(dateFormatter));
        objectMapper.registerModule(javaTimeModule);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
        return objectMapper;
    }

    @Override
    public Page<A> parseJobs(String data, AtsGetOfferConfig atsConfig) {
        try {
            var rootNode = objectMapper.readTree(data);
            var adsNode = rootNode;
            if (StringUtils.isNotBlank(rootElement)) {
                adsNode = JsonUtils.atSimpleXpath(adsNode, rootElement);
            }
            var list = StreamSupport.stream(adsNode.spliterator(), false)
                    .map(JsonNode::toString)
                    .map(this::parseJob)
                    .filter(Objects::nonNull)
                    .toList();

            if (atsConfig.isPaginated()) {
                return new PageImpl<>(list, Pageable.ofSize(atsConfig.getPageSize()), JsonUtils.atSimpleXpath(rootNode, atsConfig.getTotalCountParam()).asLong());
            }
            return new PageImpl<>(list);
        } catch (JsonProcessingException e) {
            log.error("unable to parse json globally {}", data, e);
            throw new GenericTechnicalException("Unable to parse json data", e);
        }
    }

    @Override
    public A parseJob(String json) {
        try {
            return objectMapper.readValue(json, targetClass).setRawContent(json);
        } catch (JsonProcessingException e) {
            log.error("unable to parse job for json {}", json, e);
            return null;
        }
    }
}
