package com.erhgo.services.externaloffer.teamtailor;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.externaloffer.candidature.AbstractATSWithHttpClientNotificationSender;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.send.SendCandidaturesRecruiterProvider;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import com.erhgo.utils.StringUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.util.Base64;
import java.util.HexFormat;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
// See https://partner.teamtailor.com/job_boards/direct_apply/#direct-apply
public class TeamtailorATSNotificationScheduler extends AbstractATSWithHttpClientNotificationSender {

    public static final String JOBBOARD_ID = "jenesuispasuncv_-_le_jobboard";
    private final ObjectMapper objectMapper;

    protected TeamtailorATSNotificationScheduler(KeycloakService keycloakService, ConfigurablePropertyRepository configurablePropertyRepository, UserProfileCompetencesExportService userProfileCompetencesExportService, TransactionTemplate transactionTemplate, RecruitmentCandidatureRepository recruitmentCandidatureRepository, SpontaneousCandidatureRepository spontaneousCandidatureRepository, List<AtsSendCandidaturesConfig> teamtailorSendCandidaturesConfig, SecurityService securityService, AbstractCandidatureRepository abstractCandidatureRepository, SendCandidaturesRecruiterProvider sendCandidaturesRecruiterProvider, ObjectMapper objectMapper) {
        super(keycloakService, configurablePropertyRepository, userProfileCompetencesExportService, transactionTemplate, recruitmentCandidatureRepository, spontaneousCandidatureRepository, teamtailorSendCandidaturesConfig, securityService, abstractCandidatureRepository, sendCandidaturesRecruiterProvider);
        this.objectMapper = objectMapper;
    }

    @Scheduled(initialDelay = 42, fixedRate = 60, timeUnit = TimeUnit.MINUTES)
    @SchedulerLock(name = "handleNewCandidaturesForTeamtailorATS", lockAtLeastFor = "35M")
    @Override
    public void handleNewCandidatures() {
        super.handleNewCandidatures();
    }


    @Override
    protected Request buildSendCandidatureRequest(UserRepresentation userRepresentation, RecruitmentCandidature candidature, AtsSendCandidaturesConfig config) throws IOException {
        var candidatureDTO = buildCandidature(userRepresentation, candidature);
        var body = objectMapper.writeValueAsString(candidatureDTO);
        var signature = generateSignature(body, config);
        return new Request.Builder()
                .url(config.getCandidatureNotificationUrl())
                .header("X-Api-Signature", signature)
                .header("ReferenceNumber", candidature.getExternalOfferId())
                .header("JobBoardId", JOBBOARD_ID)
                .post(RequestBody.create(body, MediaType.get("application/json")))
                .build();
    }

    private Candidature buildCandidature(UserRepresentation userRepresentation, RecruitmentCandidature candidature) throws IOException {
        var fullProfile = getProfile(candidature, userRepresentation.getFullname(), ProfileCompetencesViewObject.AnonymousMode.BOTH);
        return new Candidature().setCandidate(
                new Candidature.Candidate()
                        .setEmail(userRepresentation.getEmail())
                        .setFirstName(userRepresentation.getFirstName())
                        .setLastName(userRepresentation.getLastName())
                        .setPhone(candidature.getUserProfile().getPhoneNumber())
                        .setResume(new Candidature.Candidate.Resume()
                                .setFileName("profileJenesuisPASunCV.pdf")
                                .setFileType("application/pdf")
                                .setBinaryData(StringUtils.toStringifiedBase64(fullProfile.readAllBytes()))
                        )
        );
    }

    private static String generateSignature(String body, AtsSendCandidaturesConfig config) {
        try {
            var sha256Hmac = Mac.getInstance("HmacSHA256");
            var secretKey = new SecretKeySpec(config.getCandidatureNotificationApiKey().getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256Hmac.init(secretKey);
            var hmacData = sha256Hmac.doFinal(body.getBytes(StandardCharsets.UTF_8));
            String hexEncoded = HexFormat.of().formatHex(hmacData);
            return Base64.getEncoder().encodeToString(hexEncoded.getBytes(StandardCharsets.UTF_8));
        } catch (GeneralSecurityException e) {
            log.error("Unable to encode signature header for Teamtailor candidature sending - body {}", body, e);
            throw new GenericTechnicalException("Unable to encode signature header for Teamtailor candidature sending", e);
        }
    }


    @Data
    @Accessors(chain = true)
    private static class Candidature {
        @Data
        @Accessors(chain = true)
        private static class Candidate {
            private String phone;
            private String email;
            @JsonProperty("last-name")
            private String lastName;
            @JsonProperty("first-name")
            private String firstName;
            private Resume resume;

            @Data
            @Accessors(chain = true)
            private static class Resume {
                @JsonProperty("binary-data")
                private String binaryData;
                @JsonProperty("file-name")
                private String fileName;
                @JsonProperty("file-type")
                private String fileType;
            }
        }

        private Candidate candidate;

    }
}
