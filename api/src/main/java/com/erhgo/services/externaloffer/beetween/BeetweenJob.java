package com.erhgo.services.externaloffer.beetween;

import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.DiplomaLevel;
import com.erhgo.openapi.dto.TypeContractCategoryDTO;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.*;

@Slf4j
@JacksonXmlRootElement(localName = "job")
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
// see https://support.beetween.com/fr/knowledge/integrer-les-candidatures-dans-beetween
public class BeetweenJob extends AbstractRemoteOfferContent<BeetweenJob> {
    private static final String DATE_PATTERN = "yyyy-MM-dd HH:mm:ss Z";
    private static final Set<String> FIELDS_TO_IGNORE = Set.of("creationDate");

    @JacksonXmlProperty(localName = "id")
    private String id;

    @JacksonXmlProperty(localName = "title")
    private String offerTitle;

    @JacksonXmlProperty(localName = "recruiter")
    private String remoteRecruiterCode;

    @JacksonXmlProperty(localName = "creation_date")
    @JsonFormat(pattern = DATE_PATTERN)
    private ZonedDateTime creationDate;

    @JacksonXmlProperty(localName = "last_modification_date")
    @JsonFormat(pattern = DATE_PATTERN)
    private ZonedDateTime lastModificationDateInput;

    @JacksonXmlProperty(localName = "application_email")
    private String applicationEmail;

    @JacksonXmlProperty(localName = "job_description")
    private BeetweenJob.Description descriptionInput;

    @JacksonXmlProperty(localName = "location")
    private BeetweenJob.Location location;

    @JacksonXmlProperty(localName = "contract")
    private BeetweenJob.Contract contract;

    @JacksonXmlProperty(localName = "salary")
    private BeetweenJob.Salary salary;

    @JacksonXmlProperty(localName = "study_levels")
    private List<BeetweenJob.StudyLevel> studyLevels;

    @Override
    public List<Integer> getSalaryValues() {
        return Optional.ofNullable(salary).flatMap(s -> Optional.ofNullable(salary.value).map(v -> "MONTH".equalsIgnoreCase(StringUtils.trimToEmpty(salary.duration)) ? 12 * v : v))
                .map(Double::intValue)
                .stream()
                .toList();
    }

    @Override
    public String getOrganizationDescription() {
        return Optional.ofNullable(descriptionInput).map(Description::getCompany).orElse(null);
    }

    @Override
    @JsonIgnore
    public String getDescription() {
        return Optional.ofNullable(descriptionInput).map(Description::getJoinedDescription).orElse(null);
    }

    @Override
    public TypeContractCategoryDTO getTypeContractCategory() {
        return Optional.ofNullable(contract).map(Contract::getTypeCategory).orElse(null);
    }

    @Override
    public List<String> getCriterias() {
        return (studyLevels == null ? Collections.emptyList() : studyLevels.stream()
                .map(StudyLevel::getCriteriaCode)
                .filter(Objects::nonNull)
                .min(String::compareTo)
                .map(List::of)
                .orElse(Collections.emptyList()));
    }

    @Override
    public String getLocationIndication() {
        return Optional.ofNullable(location).map(l -> "%s (%s)".formatted(StringUtils.trimToEmpty(l.city), StringUtils.trimToEmpty(l.postCode))).orElse(null);
    }

    @Override
    public boolean isInFrance() {
        return Optional.ofNullable(location).map(Location::getCountryCode).filter(c -> StringUtils.trimToEmpty(c).toUpperCase().contains("FR")).isPresent();
    }

    @Override
    public LocalDateTime getLastModificationDate() {
        return Optional.ofNullable(lastModificationDateInput).or(() -> Optional.ofNullable(creationDate)).map(ZonedDateTime::toLocalDateTime).orElse(null);
    }

    @Override
    public Integer getWorkingTimeWeeklyDuration() {
        return Optional.ofNullable(contract).map(Contract::getHoursPerWeek).orElse(null);
    }

    @Data
    public static class Description {
        @JacksonXmlProperty(localName = "profile")
        private String profile;
        @JacksonXmlProperty(localName = "mission")
        private String mission;
        @JacksonXmlProperty(localName = "company")
        private String company;

        public String getJoinedDescription() {
            return "%s%n%s".formatted(StringUtils.trimToEmpty(mission), StringUtils.trimToEmpty(profile));
        }
    }

    @Data
    public static class Location {
        @JacksonXmlProperty(localName = "country_code")
        private String countryCode;
        @JacksonXmlProperty(localName = "post_code")
        private String postCode;
        @JacksonXmlProperty(localName = "city")
        private String city;
    }

    @Data
    public static class Contract {
        @JacksonXmlProperty(localName = "rythm")
        private Rythm rythm;
        @JacksonXmlProperty(localName = "duration")
        private String duration;
        @JacksonXmlProperty(localName = "type")
        private CodeWrapper type;

        public TypeContractCategoryDTO getTypeCategory() {
            return switch (StringUtils.trimToEmpty(type == null ? "" : type.code)) {
                case "TEMPORARY", "PERMANENT_PROJECT", "COLLABORATION_AGREEMENT" -> TypeContractCategoryDTO.TEMPORARY;
                case "SUMMER_JOB" -> TypeContractCategoryDTO.SEASONAL;
                case "CONTRAT_DE_PROFESIONNALISATION", "APPRENTICESHIP" -> TypeContractCategoryDTO.PRO;
                case "PERMANENT" -> TypeContractCategoryDTO.PERMANENT;
                case "INTERIM" -> TypeContractCategoryDTO.INTERIM;
                default -> null;
            };
        }

        public Integer getHoursPerWeek() {
            return Optional.ofNullable(rythm).map(Rythm::getNbHoursPerWeek).map(Double::intValue).orElse(null);
        }
    }

    @Data
    public static class Salary {
        @JacksonXmlProperty(localName = "value", isAttribute = true)
        private Double value;
        @JacksonXmlProperty(localName = "unit", isAttribute = true)
        private String duration;
    }

    @Data
    public static class StudyLevel {

        @JacksonXmlProperty(localName = "code", isAttribute = true)
        private String code;

        public String getCriteriaCode() {
            return Optional.ofNullable(switch (code) {
                case "ALL" -> DiplomaLevel.WITHOUT_DIPLOMA;
                case "BEP_CAP" -> DiplomaLevel.CAP_BEP;
                case "BAC" -> DiplomaLevel.BAC_BAC_PRO;
                case "BAC_1_2" -> DiplomaLevel.BAC_2;
                case "BAC_3", "BAC_4" -> DiplomaLevel.BAC_3;
                case "BAC_5" -> DiplomaLevel.BAC_5;
                case "BAC_6_PLUS" -> DiplomaLevel.DOCTORATE;
                default -> null;
            }).map(CriteriaValue::getValueCodeForDiplomaLevel).orElse(null);
        }
    }

    @Data
    public static class CodeWrapper {
        @JacksonXmlProperty(localName = "code", isAttribute = true)
        private String code;
    }

    @Data
    public static class Rythm {
        @JacksonXmlProperty(localName = "hours_per_week", isAttribute = true)
        private String hoursPerWeek;

        public Double getNbHoursPerWeek() {
            try {
                return hoursPerWeek == null ? null : Double.parseDouble(hoursPerWeek);
            } catch (IllegalArgumentException e) {
                return null;
            }
        }
    }


}
