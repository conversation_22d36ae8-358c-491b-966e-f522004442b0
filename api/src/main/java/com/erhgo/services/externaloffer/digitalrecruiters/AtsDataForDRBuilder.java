package com.erhgo.services.externaloffer.digitalrecruiters;

import com.erhgo.openapi.dto.ExtractedAtsDataDTO;
import com.erhgo.services.externaloffer.AtsDataBuilder;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class AtsDataForDRBuilder implements AtsDataBuilder<DRJob> {

    private final ObjectMapper objectMapper;

    public ExtractedAtsDataDTO buildAtsData(DRJob job) {
        return job == null ? null : new ExtractedAtsDataDTO()
                .typeContractInformations(Map.of("contrat", job.getContractType()))
                .occupationTitles(Map.of("occupation", job.getOfferTitle()))
                .descriptionParts(Map.of("description", job.getDescription()))
                .organizationDescriptionParts(Map.of("description organisation", job.getOrganizationDescription()))
                .localisationInformations(AtsDataBuilder.writeSafely(objectMapper, job.getBuiltInLocation()))
                .criteriaRelatedData(new HashMap<>())
                .salaryRelatedData(AtsDataBuilder.writeSafely(objectMapper, salaryMap(job)))
                ;
    }

    private static @NotNull Map<String, ? extends Serializable> salaryMap(DRJob job) {
        var result = new HashMap<String, Serializable>();
        result.put("kind", job.getSalaryKind());
        result.put("min", job.getSalaryMin());
        result.put("max", job.getSalaryMax());
        return result;
    }

}
