package com.erhgo.services.externaloffer.beetween;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.candidature.job.SpontaneousCandidature;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.externaloffer.candidature.AbstractATSMailNotificationSender;
import com.erhgo.services.externaloffer.candidature.AbstractATSWithHttpClientNotificationSender;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.send.SendCandidaturesRecruiterProvider;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailNotifier;
import com.erhgo.services.reminder.EmailToNotifyFilterInterface;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
// See https://support.beetween.com/fr/knowledge/candidature-par-api
public class BeetweenATSNotificationScheduler {

    private final BeetweenRecruitmentCandidatureSender recruitmentCandidatureSender;
    private final BeetweenSpontaneousCandidatureSender spontaneousCandidatureSender;

    @Scheduled(initialDelay = 6, fixedRate = 60, timeUnit = TimeUnit.MINUTES)
    @SchedulerLock(name = "handleNewCandidaturesForBeetweenATS", lockAtLeastFor = "35M")
    public void handleNewCandidatures() {
        recruitmentCandidatureSender.handleNewRecruitmentCandidatures();
        spontaneousCandidatureSender.handleNewSpontaneousCandidatures();
    }

    @Service
    public static class BeetweenSpontaneousCandidatureSender extends AbstractATSMailNotificationSender {

        private static final String MAIL_BODY = """
                - Prénom = %s
                - Nom = %s
                - Email = %s
                - Téléphone = %s
                """;

        public BeetweenSpontaneousCandidatureSender(RecruitmentCandidatureRepository recruitmentCandidatureRepository, SpontaneousCandidatureRepository spontaneousCandidatureRepository,
                                                    AbstractCandidatureRepository abstractCandidatureRepository,
                                                    KeycloakService keycloakService, ConfigurablePropertyRepository configurablePropertyRepository, UserProfileCompetencesExportService userProfileCompetencesExportService, @Nullable MailNotifier mailNotifier, SecurityService securityService, List<AtsSendCandidaturesConfig> beetweenSendCandidaturesConfig, TransactionTemplate transactionTemplate, EmailToNotifyFilterInterface emailToNotifyFilterInterface, SendCandidaturesRecruiterProvider sendCandidaturesRecruiterProvider) {
            super(recruitmentCandidatureRepository, spontaneousCandidatureRepository, abstractCandidatureRepository, keycloakService, configurablePropertyRepository, userProfileCompetencesExportService, mailNotifier, securityService, beetweenSendCandidaturesConfig, transactionTemplate, emailToNotifyFilterInterface, sendCandidaturesRecruiterProvider);
        }

        @Override
        protected boolean useReplyToAsFrom() {
            return true;
        }

        @Override
        protected String buildSubject(SpontaneousCandidature candidature) {
            return "jenesuisPASunCV : nouvelle candidature spontanée";
        }

        @Override
        protected String buildBody(SpontaneousCandidature candidature, UserRepresentation userRepresentation) {
            return MAIL_BODY.formatted(
                    Optional.ofNullable(userRepresentation.getFirstName()).orElse("(non précisé)"),
                    Optional.ofNullable(userRepresentation.getLastName()).orElse("(non précisé)"),
                    StringUtils.trimToEmpty(userRepresentation.getEmail()),
                    StringUtils.trimToEmpty(candidature.getUserProfile().getPhoneNumber())
            );
        }
    }


    @Service
    public static class BeetweenRecruitmentCandidatureSender extends AbstractATSWithHttpClientNotificationSender {
        public BeetweenRecruitmentCandidatureSender(
                RecruitmentCandidatureRepository recruitmentCandidatureRepository,
                SpontaneousCandidatureRepository spontaneousCandidatureRepository,
                AbstractCandidatureRepository abstractCandidatureRepository,
                KeycloakService keycloakService,
                ConfigurablePropertyRepository configurablePropertyRepository,
                UserProfileCompetencesExportService userProfileCompetencesExportService,
                SecurityService securityService,
                List<AtsSendCandidaturesConfig> beetweenSendCandidaturesConfig,
                TransactionTemplate transactionTemplate,
                SendCandidaturesRecruiterProvider sendCandidaturesRecruiterProvider
        ) {
            super(keycloakService, configurablePropertyRepository, userProfileCompetencesExportService, transactionTemplate, recruitmentCandidatureRepository, spontaneousCandidatureRepository, beetweenSendCandidaturesConfig, securityService, abstractCandidatureRepository, sendCandidaturesRecruiterProvider);
        }


        @Override
        protected Request buildSendCandidatureRequest(UserRepresentation userRepresentation, RecruitmentCandidature candidature, AtsSendCandidaturesConfig config) throws IOException {
            var fullProfile = getProfile(candidature, userRepresentation.getFullname(), ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE);
            var anonymousProfile = getProfile(candidature, userRepresentation.getFullname(), ProfileCompetencesViewObject.AnonymousMode.ANONYMOUS);
            var fullProfileContent = fullProfile.readAllBytes();
            var anonymousProfileContent = anonymousProfile.readAllBytes();

            var body = transactionTemplate.execute(__ -> new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("firstname", com.erhgo.utils.StringUtils.trimToDefault(userRepresentation.getFirstName(), "Non précisé"))
                    .addFormDataPart("lastname", com.erhgo.utils.StringUtils.trimToDefault((userRepresentation.getLastName()), "Non précisé"))
                    .addFormDataPart("wid", "%s%s".formatted(candidature.getRecruitment().getExternalOffer().getRemoteId(), config.getCandidatureNotificationApiKey()))
                    .addFormDataPart("email", userRepresentation.getEmail())
                    .addFormDataPart("data", fullProfile.fileName(), RequestBody.create(fullProfileContent, MediaType.parse(fullProfile.contentType())))
                    .addFormDataPart("data", anonymousProfile.fileName(), RequestBody.create(anonymousProfileContent, MediaType.parse(anonymousProfile.contentType())))
                    .addFormDataPart("filenames", fullProfile.fileName())
                    .addFormDataPart("filenames", anonymousProfile.fileName())
                    .build());
            return new Request.Builder()
                    .url(config.getCandidatureNotificationUrl())
                    .post(body)
                    .build();
        }
    }
}
