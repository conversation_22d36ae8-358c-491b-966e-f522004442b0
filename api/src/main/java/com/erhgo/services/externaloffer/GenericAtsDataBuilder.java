package com.erhgo.services.externaloffer;

import com.erhgo.openapi.dto.ExtractedAtsDataDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;

@Component
@RequiredArgsConstructor
public class GenericAtsDataBuilder implements AtsDataBuilder<AbstractRemoteOfferContent> {

    private final ObjectMapper objectMapper;

    @Override
    public ExtractedAtsDataDTO buildAtsData(AbstractRemoteOfferContent job) {
        return job == null ? null : new ExtractedAtsDataDTO()
                .typeContractInformations(Map.of("contrat", Optional.ofNullable(job.getTypeContractCategory()).map(Enum::name).orElse("")))
                .occupationTitles(Map.of("occupation", job.getOfferTitle()))
                .descriptionParts(Map.of("description", job.getDescription()))
                .organizationDescriptionParts(Map.of("description organisation", job.getOrganizationDescription()))
                .localisationInformations(AtsDataBuilder.writeSafely(objectMapper, job.getLocationIndication()))
                .criteriaRelatedData(Map.of("critères", String.join(",", Optional.ofNullable(job.getCriterias()).orElse(Collections.<String>emptyList()))))
                .salaryRelatedData(AtsDataBuilder.writeSafely(objectMapper, job.getSalaryValues()))
                ;
    }

}
