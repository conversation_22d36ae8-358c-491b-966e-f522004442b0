package com.erhgo.services.externaloffer.hellowork;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.candidature.job.SpontaneousCandidature;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.externaloffer.candidature.AbstractATSMailNotificationSender;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.send.SendCandidaturesRecruiterProvider;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailNotifier;
import com.erhgo.services.reminder.EmailToNotifyFilterInterface;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import jakarta.annotation.Nullable;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class HelloWorkATSMailNotificationScheduler extends AbstractATSMailNotificationSender {

    private static final String MAIL_BODY = """
            Prénom = %s <br />
            <br />
            Nom = %s <br />
            <br />
            Civilité = M. ou Mme<br />
            <br />
            Email = %s<br />
            <br />
            Téléphone = %s<br />
            <br />
            Source = jenesuispasuncv
            <br />
            Ville = %s
            """;


    public HelloWorkATSMailNotificationScheduler(RecruitmentCandidatureRepository recruitmentCandidatureRepository, SpontaneousCandidatureRepository spontaneousCandidatureRepository,
                                                 AbstractCandidatureRepository abstractCandidatureRepository,
                                                 KeycloakService keycloakService, ConfigurablePropertyRepository configurablePropertyRepository, UserProfileCompetencesExportService userProfileCompetencesExportService, @Nullable MailNotifier mailNotifier, SecurityService securityService, List<AtsSendCandidaturesConfig> helloWorkSendCandidaturesConfig, TransactionTemplate transactionTemplate, EmailToNotifyFilterInterface emailToNotifyFilterInterface, SendCandidaturesRecruiterProvider sendCandidaturesRecruiterProvider) {
        super(recruitmentCandidatureRepository, spontaneousCandidatureRepository, abstractCandidatureRepository, keycloakService, configurablePropertyRepository, userProfileCompetencesExportService, mailNotifier, securityService, helloWorkSendCandidaturesConfig, transactionTemplate, emailToNotifyFilterInterface, sendCandidaturesRecruiterProvider);
    }

    @PostConstruct
    public void init() {
        log.info("HelloWorkATSMailNotificationHandler initialized");
    }

    @Scheduled(initialDelay = 18, fixedRate = 60, timeUnit = TimeUnit.MINUTES)
    @SchedulerLock(name = "handleNewCandidaturesForHelloWorkATS", lockAtLeastFor = "35M")
    @Override
    public void handleNewCandidatures() {
        super.handleNewCandidatures();
    }

    @Override
    protected String buildBody(RecruitmentCandidature candidature, UserRepresentation userRepresentation) {
        var userProfile = candidature.getUserProfile();
        return buildBody(userRepresentation, userProfile);
    }

    private static String buildBody(UserRepresentation userRepresentation, UserProfile userProfile) {
        return MAIL_BODY.formatted(
                Optional.ofNullable(userRepresentation.getFirstName()).orElse(""),
                Optional.ofNullable(userRepresentation.getLastName()).orElse(""),
                StringUtils.trimToEmpty(userRepresentation.getEmail()),
                StringUtils.trimToEmpty(userProfile.getPhoneNumber()),
                StringUtils.trimToEmpty(Optional.ofNullable(userProfile.getCity()).orElse(""))
        );
    }

    @Override
    protected String buildSubject(SpontaneousCandidature candidature) {
        return "jenesuisPASunCV : candidature spontanée";
    }

    @Override
    protected String buildBody(SpontaneousCandidature candidature, UserRepresentation userRepresentation) {
        return buildBody(userRepresentation, candidature.getUserProfile());
    }

}
