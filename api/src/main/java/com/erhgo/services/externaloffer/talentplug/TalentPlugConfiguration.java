package com.erhgo.services.externaloffer.talentplug;

import com.erhgo.services.externaloffer.ExternalJobParser;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import com.erhgo.services.externaloffer.config.ParserProvider;
import com.erhgo.services.externaloffer.parsers.GenericJobXmlParser;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeansException;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.event.EventListener;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Configuration
@EnableConfigurationProperties
public class TalentPlugConfiguration {

    private final AtsDataForTalentPlugBuilder atsDataBuilder;
    private final ExternalOfferServiceProvider serviceProvider;
    private final ParserProvider parserProvider;

    @Bean
    @ConfigurationProperties(prefix = "ats.talent-plug.fetch")
    public List<AtsGetOfferConfig> talentPlugConfigurations() {
        return new ArrayList<>();
    }

    @Bean
    @ConfigurationProperties(prefix = "ats.talent-plug.send")
    public List<AtsSendCandidaturesConfig> talentPlugSendCandidaturesConfig() {
        return new ArrayList<>();
    }

    @Bean
    @Scope("prototype")
    public ExternalJobParser<TalentPlugJob> talentPlugJobXmlParser(AtsGetOfferConfig config) {
        return new GenericJobXmlParser<>(TalentPlugJob.class, config.getRootPath(), config.getAtsCode());
    }

    @EventListener(ApplicationReadyEvent.class)
    public void initializeTalentPlugServices() throws BeansException {
        talentPlugConfigurations().forEach(config ->
                {
                    var talentPlugJobXmlParser = parserProvider.initializeBean(config.getAtsCode(), "talentPlugJobXmlParser", config);
                    serviceProvider.initializeGenericService(
                            atsDataBuilder,
                            config,
                            talentPlugJobXmlParser);
                }
        );
    }
}
