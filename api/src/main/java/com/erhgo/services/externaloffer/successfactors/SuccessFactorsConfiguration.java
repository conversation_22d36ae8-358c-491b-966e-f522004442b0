package com.erhgo.services.externaloffer.successfactors;

import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import com.erhgo.services.externaloffer.config.ParserProvider;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeansException;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.event.EventListener;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Configuration
@EnableConfigurationProperties
public class SuccessFactorsConfiguration {

    private final AtsDataForSuccessFactorsBuilder atsDataBuilder;
    private final ExternalOfferServiceProvider serviceProvider;
    private final ParserProvider parserProvider;

    @Bean
    @ConfigurationProperties(prefix = "ats.success-factors.fetch")
    public List<AtsGetOfferConfig> successFactorsConfigurations() {
        return new ArrayList<>();
    }

    @Bean
    @Scope("prototype")
    public SuccessFactorsJobParser successFactorsJobParser(AtsGetOfferConfig config) {
        return new SuccessFactorsJobParser(config);
    }

    @EventListener(ApplicationReadyEvent.class)
    public void initializeServices() throws BeansException {
        successFactorsConfigurations().forEach(config ->
                {
                    var successFactorsJobParser = parserProvider.initializeBean(config.getAtsCode(), "successFactorsJobParser", config);
                    serviceProvider.initializeGenericService(
                            atsDataBuilder,
                            config,
                            successFactorsJobParser);
                }
        );
    }
}
