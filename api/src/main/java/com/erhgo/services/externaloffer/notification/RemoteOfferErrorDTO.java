package com.erhgo.services.externaloffer.notification;

import com.erhgo.services.notifier.messages.AbstractNotifierMessageDTO;

import java.util.UUID;

public class RemoteOfferErrorDTO extends AbstractNotifierMessageDTO {


    public RemoteOfferErrorDTO(UUID externalOfferId, String recruitmentTitle, String sourceType, boolean isModification, String forcedSlackChannel) {
        var url = baseSourcingExternalOfferPath.formatted(externalOfferId);
        initializeText(recruitmentTitle, sourceType, url, isModification);
        setForcedSlackChannel(forcedSlackChannel);
    }

    public RemoteOfferErrorDTO(Long recruitmentId, String recruitmentTitle, String sourceType, boolean isModification, String forcedSlackChannel) {
        var url = baseSourcingRecruitmentPath.formatted(recruitmentId);
        initializeText(recruitmentTitle, sourceType, url, isModification);
        setForcedSlackChannel(forcedSlackChannel);
    }

    private void initializeText(String recruitmentTitle, String sourceType, String url, boolean isModification) {
        var actionType = isModification ? "Modification automatique" : "Publication";
        this.text = ":warning: Échec de %s d'%s depuis %s pour l'offre %s".formatted(actionType, link(url, "un recrutement"), sourceType, recruitmentTitle);
    }

    public RemoteOfferErrorDTO(UUID externalOfferId, String recruitmentTitle, String atsCode, String offerRecruiter) {
        var sb = new StringBuilder();
        var url = baseSourcingExternalOfferPath.formatted(externalOfferId);

        sb.append(":warning: Échec de suspension d'%s - ATS %s pour l'offre %s (recruteur : %s)".formatted(link(url, "un recrutement"), atsCode, recruitmentTitle, offerRecruiter));

        this.text = sb.toString();
    }
}
