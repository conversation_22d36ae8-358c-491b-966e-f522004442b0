package com.erhgo.services.externaloffer.limiter;

import com.erhgo.config.ConfigurableProperty;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.google.common.base.Joiner;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class PerRecruiterLimiter implements ExternalOfferLimiter {

    private final ConfigurablePropertyRepository configurablePropertyRepository;

    @Override
    public <A extends AbstractRemoteOfferContent<A>> Map<Predicate<A>, Integer> limits(AtsGetOfferConfig config) {

        var configCode = config.getAtsAndConfigCode();
        var limits = configurablePropertyRepository.findByPropertyKeyStartsWith("ats.limit.per-recruiter.%s".formatted(configCode))
                .stream()
                .collect(Collectors.toMap(PerRecruiterLimiter::extractOrgaCodeFromPropertyKey, p -> Integer.parseInt(p.getPropertyValue())));
        log.debug("For ATS {}, using limits: {}", configCode, Joiner.on(", ").join(limits.values()));

        return limits
                .entrySet()
                .stream()
                .collect(Collectors.toMap(
                        limit -> (offer -> match(offer, limit.getKey(), limits.keySet())),
                        Map.Entry::getValue
                ));
    }

    private <A extends AbstractRemoteOfferContent<A>> boolean match(A offer, String currentLimitRecruiterCode, Set<String> availableKeys) {
        var recruiterCode = offer.getRecruiterCode();
        if (currentLimitRecruiterCode.equals(recruiterCode)) {
            return true;
        }
        return !availableKeys.contains(recruiterCode) && DEFAULT_KEY.equals(currentLimitRecruiterCode);
    }

    private static String extractOrgaCodeFromPropertyKey(ConfigurableProperty p) {
        return p.getPropertyKey().split("\\.")[4];
    }

}
