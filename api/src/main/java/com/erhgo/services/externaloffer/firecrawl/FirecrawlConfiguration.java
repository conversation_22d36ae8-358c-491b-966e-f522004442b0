package com.erhgo.services.externaloffer.firecrawl;

import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import com.erhgo.services.externaloffer.config.ParserProvider;
import com.erhgo.services.externaloffer.parsers.GenericJobJsonParser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.event.EventListener;

import java.util.ArrayList;
import java.util.List;

@Configuration
@RequiredArgsConstructor
@ConditionalOnProperty(name = "ats.firecrawl.enabled", havingValue = "true")
@Slf4j
public class FirecrawlConfiguration {

    private final ExternalOfferServiceProvider serviceProvider;
    private final ParserProvider parserProvider;

    @Bean
    @ConfigurationProperties(prefix = "ats.firecrawl.fetch")
    public List<AtsGetOfferConfig> firecrawlConfigurations() {
        return new ArrayList<>();
    }

    @Bean
    @ConfigurationProperties(prefix = "ats.firecrawl.send")
    public List<AtsSendCandidaturesConfig> firecrawlSendCandidaturesConfig() {
        return new ArrayList<>();
    }


    @EventListener(ApplicationReadyEvent.class)
    public void initializeServices() throws BeansException {
        firecrawlConfigurations().forEach(config -> {
            var firecrawlParser = parserProvider.initializeBean(config.getAtsCode(), "firecrawlJobParser");
            serviceProvider.initializeCustomService(FirecrawlSynchronizer.class, config, firecrawlParser);
        });
    }

    @Bean
    @Scope("prototype")
    public GenericJobJsonParser<ScrappedJob> firecrawlJobParser() {
        return new GenericJobJsonParser<>(ScrappedJob.class, null);
    }
}
