package com.erhgo.services.externaloffer.candidature;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.send.SendCandidaturesRecruiterProvider;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.SourcingKeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailNotifier;
import com.erhgo.services.reminder.EmailToNotifyFilterInterface;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimaps;
import com.google.common.collect.SetMultimap;
import jakarta.annotation.Nullable;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class DefaultMailNotificationScheduler extends AbstractATSMailNotificationSender {

    private final SourcingKeycloakService sourcingKeycloakService;

    private final SetMultimap<String, String> emailsPerRecruiterCache = Multimaps.synchronizedSetMultimap(HashMultimap.create());


    public DefaultMailNotificationScheduler(RecruitmentCandidatureRepository recruitmentCandidatureRepository, SpontaneousCandidatureRepository spontaneousCandidatureRepository,
                                            AbstractCandidatureRepository abstractCandidatureRepository,
                                            KeycloakService keycloakService,
                                            ConfigurablePropertyRepository configurablePropertyRepository,
                                            UserProfileCompetencesExportService userProfileCompetencesExportService,
                                            @Nullable MailNotifier mailNotifier,
                                            SecurityService securityService,
                                            List<AtsSendCandidaturesConfig> defaultMailSendCandidaturesConfig,
                                            TransactionTemplate transactionTemplate,
                                            SourcingKeycloakService sourcingKeycloakService,
                                            EmailToNotifyFilterInterface emailToNotifyFilterInterface,
                                            SendCandidaturesRecruiterProvider sendCandidaturesRecruiterProvider
    ) {
        super(recruitmentCandidatureRepository, spontaneousCandidatureRepository, abstractCandidatureRepository, keycloakService, configurablePropertyRepository, userProfileCompetencesExportService, mailNotifier, securityService, defaultMailSendCandidaturesConfig, transactionTemplate, emailToNotifyFilterInterface, sendCandidaturesRecruiterProvider);
        this.sourcingKeycloakService = sourcingKeycloakService;
    }

    @PostConstruct
    public void init() {
        log.info("DefaultMailATSMailNotificationScheduler initialized");
    }

    @Scheduled(initialDelay = 36, fixedRate = 60, timeUnit = TimeUnit.MINUTES)
    @SchedulerLock(name = "handleNewCandidaturesForDefaultMailATS", lockAtLeastFor = "35M")
    @Override
    public void handleNewCandidatures() {
        emailsPerRecruiterCache.clear();
        super.handleNewCandidatures();
    }

    @Override
    protected Collection<String> getRecipientsForRecruitmentCandidature(RecruitmentCandidature candidature, AtsSendCandidaturesConfig config) {
        var recruiterCode = candidature.getCodeOfRecruiter();
        Collection<String> emails;
        if (emailsPerRecruiterCache.containsKey(recruiterCode)) {
            emails = emailsPerRecruiterCache.get(recruiterCode);
        } else {
            emails = sourcingKeycloakService.getEnabledSourcingUsersForGroup(recruiterCode).stream().map(UserRepresentation::getEmail).toList();
            emailsPerRecruiterCache.putAll(recruiterCode, emails);
        }
        return emails;
    }
}
