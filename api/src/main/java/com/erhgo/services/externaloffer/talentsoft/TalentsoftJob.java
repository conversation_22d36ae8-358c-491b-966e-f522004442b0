package com.erhgo.services.externaloffer.talentsoft;


import com.erhgo.openapi.dto.TypeContractCategoryDTO;
import com.erhgo.openapi.dto.WorkContractDurationUnitDTO;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;


@Slf4j
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class TalentsoftJob extends AbstractRemoteOfferContent<TalentsoftJob> {

    private static final Set<String> FIELDS_TO_IGNORE = Set.of("lastModificationDate", "locationIndication", "rawContent");

    @JsonProperty("reference")
    private String id;
    @JsonProperty("title")
    private String offerTitle;
    @JsonProperty("location")
    private String locationIndication;
    @JsonProperty("modificationDate")
    private LocalDateTime lastModificationDate;
    @JsonProperty("contractType")
    private Map<String, String> contractType;
    @JsonProperty("organisationName")
    private String recruiter;
    @JsonProperty("organisationDescription")
    private String rawOrganizationDescription;
    @JsonProperty("organisationLogoUrl")
    private String organisationLogoUrl;
    @JsonProperty("contractDuration")
    private String contractDuration;
    @JsonProperty("country")
    private List<Map<String, String>> country;
    @JsonProperty("description1Formatted")
    private String description1Formatted;
    @JsonProperty("description2Formatted")
    private String description2Formatted;

    @Override
    public String getOrganizationDescription() {
        return com.erhgo.utils.StringUtils
                .trimToDefault(rawOrganizationDescription, "")
                .replaceAll("\r\n", "<br/>");
    }

    @Override
    @JsonProperty
    public boolean isInFrance() {
        return country == null || country.isEmpty() || country.stream().anyMatch(a -> a.getOrDefault("label", "").toLowerCase().startsWith("fr"));
    }

    @Override
    @JsonProperty
    public Integer getWorkContractDuration() {
        return Optional.ofNullable(StringUtils.trimToNull(contractDuration)).map(a -> a.split(" ")).map(a -> {
            try {
                return Float.parseFloat(a[0]);
            } catch (NumberFormatException e) {
                return null;
            }
        }).map(Float::intValue).orElse(null);
    }

    @Override
    @JsonProperty
    public WorkContractDurationUnitDTO getWorkContractDurationUnit() {
        return Optional.ofNullable(StringUtils.trimToNull(contractDuration))
                .map(a -> a.split(" "))
                .filter(a -> a.length>1)
                .map(a -> {
                    var duration = a[1].toLowerCase();
                    if (duration.contains("jour")) {
                        return WorkContractDurationUnitDTO.DAY;
                    }
                    if (duration.contains("semaine")) {
                        return WorkContractDurationUnitDTO.WEEK;
                    }
                    if (duration.contains("an")) {
                        return WorkContractDurationUnitDTO.YEAR;
                    }
                    return WorkContractDurationUnitDTO.MONTH;
                }).orElse(null);
    }

    @Override
    @JsonProperty
    public TypeContractCategoryDTO getTypeContractCategory() {
        if (contractType == null) return null;
        var rawContract = contractType.get("label");
        if (rawContract == null) return null;
        var contract = com.erhgo.utils.StringUtils.normalizeLowerCase(rawContract);
        if (contract.contains("cdi")) return TypeContractCategoryDTO.PERMANENT;
        if (contract.contains("stage")) return null;
        if (contract.contains("alternan") || contract.contains("apprentissage")) return TypeContractCategoryDTO.PRO;
        return TypeContractCategoryDTO.TEMPORARY;
    }

    @Override
    public List<String> getCriterias() {
        return List.of();
    }

    @Override
    @JsonProperty
    public String getRemoteRecruiterCode() {
        return recruiter;
    }

    @Override
    public List<Integer> getSalaryValues() {
        return List.of();
    }

    @Override
    @JsonProperty
    public String getDescription() {
        var jobDescription = "";
        if (StringUtils.isNotBlank(organisationLogoUrl)) {
            jobDescription += "<img src=\"%s\"/>".formatted(organisationLogoUrl);

        }
        if (StringUtils.isNotBlank(description1Formatted)) {
            jobDescription += "<br/>";
            jobDescription += description1Formatted;
        }
        if (StringUtils.isNotBlank(description2Formatted)) {
            jobDescription += "<br/>";
            jobDescription += description2Formatted;
        }
        return jobDescription;

    }

    @Override
    public boolean shouldForceSuspension(AtsGetOfferConfig config) {
        // Default behavior is based on shouldBeIgnored.
        // It uses locationIndication, which is not set at this point.
        // We do not want to initialize deeply all offers (requires one more request, through lazy init service)
        return false;
    }
}
