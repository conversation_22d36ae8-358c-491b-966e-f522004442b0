package com.erhgo.services.externaloffer.gestmax;

import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import com.erhgo.services.externaloffer.config.ParserProvider;
import com.erhgo.services.externaloffer.eolia.AtsDataForEoliaBuilder;
import com.erhgo.services.externaloffer.parsers.GenericJobJsonParser;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeansException;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.event.EventListener;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Configuration
@EnableConfigurationProperties
public class GestmaxConfiguration {

    private final AtsDataForEoliaBuilder atsDataBuilder;
    private final ExternalOfferServiceProvider serviceProvider;
    private final ParserProvider parserProvider;

    @Bean
    @ConfigurationProperties(prefix = "ats.gestmax.fetch")
    public List<AtsGetOfferConfig> gestmaxConfigurations() {
        return new ArrayList<>();
    }

    @Bean
    @ConfigurationProperties(prefix = "ats.gestmax.send")
    public List<AtsSendCandidaturesConfig> gestmaxSendCandidaturesConfig() {
        return new ArrayList<>();
    }

    @Bean
    @Scope("prototype")
    public GenericJobJsonParser<GestmaxJob> gestmaxJobJSONParser() {
        return new GenericJobJsonParser<>(GestmaxJob.class, "data");
    }

    @EventListener(ApplicationReadyEvent.class)
    public void initializeServices() throws BeansException {
        gestmaxConfigurations().forEach(config ->
                {
                    var gestmaxJobJSONParser = parserProvider.initializeBean(config.getAtsCode(), "gestmaxJobJSONParser");
                    serviceProvider.initializeGenericService(
                            atsDataBuilder,
                            config,
                            gestmaxJobJSONParser);
                }
        );
    }
}
