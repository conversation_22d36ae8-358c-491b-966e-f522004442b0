package com.erhgo.services.externaloffer.successfactors;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.parsers.GenericJobXmlParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.jsoup.Jsoup;
import org.jsoup.helper.ValidationException;
import org.jsoup.nodes.Element;
import org.jsoup.parser.Parser;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

import java.util.List;
import java.util.Optional;

@Slf4j
public class SuccessFactorsJobParser extends GenericJobXmlParser<SuccessFactorsJob> {

    private final boolean isManual;

    public SuccessFactorsJobParser(AtsGetOfferConfig successFactorsConfig) {
        super(SuccessFactorsJob.class, successFactorsConfig.getRootPath(), successFactorsConfig.getAtsCode());
        this.isManual = BooleanUtils.isTrue(successFactorsConfig.getRequiresConfirmation());
    }

    @Override
    public Page<SuccessFactorsJob> parseJobs(String xmlInput, AtsGetOfferConfig config) {
        try {
            return new PageImpl<>(extractJobElements(xmlInput).stream()
                    .map(this::cleanJobElement)
                    .filter(this::isValidJob)
                    .map(this::convertToJobObject)
                    .toList());
        } catch (ValidationException e) {
            log.warn("Error parsing XML: {}", xmlInput, e);
            return Page.empty();
        }

    }

    private List<Element> extractJobElements(String xmlInput) {
        var doc = Jsoup.parse(xmlInput, "", Parser.xmlParser());
        return doc.selectXpath(rootElementName);
    }

    private Element cleanJobElement(Element jobElement) {
        jobElement.select("apply_url").remove();
        jobElement.select("description").forEach(desc -> {
            if (!desc.html().startsWith("<![CDATA[")) {
                desc.html("<![CDATA[" + desc.text() + "]]>");
            }
        });
        return jobElement;
    }

    private boolean isValidJob(Element jobElement) {
        if (isManual) return true;
        var country = Optional.ofNullable(jobElement.selectFirst("country")).map(Element::text);
        var contractType = Optional.ofNullable(jobElement.selectFirst("contract_type")).map(Element::text);
        return country.filter("FR"::equalsIgnoreCase).isPresent() &&
                contractType.filter(this::isValidContractType).isPresent();
    }

    private boolean isValidContractType(String s) {
        return !"VIE".equalsIgnoreCase(s) && !"Stage".equalsIgnoreCase(s);
    }

    private SuccessFactorsJob convertToJobObject(Element jobElement) {
        var xml = jobElement.toString();
        try {
            return this.xmlMapper.readValue(xml, SuccessFactorsJob.class).setRawContent(xml);
        } catch (JsonProcessingException e) {
            log.error("Unable to convert xml in job for {}", xml, e);
            throw new GenericTechnicalException("Unable to convert xml in job ", e);
        }
    }
}
