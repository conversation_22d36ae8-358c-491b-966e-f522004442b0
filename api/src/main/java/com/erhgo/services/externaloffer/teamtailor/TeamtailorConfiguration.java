package com.erhgo.services.externaloffer.teamtailor;

import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import com.erhgo.services.externaloffer.config.ParserProvider;
import com.erhgo.services.externaloffer.eolia.AtsDataForEoliaBuilder;
import com.erhgo.services.externaloffer.parsers.GenericJobXmlParser;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeansException;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.event.EventListener;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Configuration
@EnableConfigurationProperties
public class TeamtailorConfiguration {

    private final AtsDataForEoliaBuilder atsDataBuilder;
    private final ExternalOfferServiceProvider serviceProvider;
    private final ParserProvider parserProvider;

    @Bean
    @ConfigurationProperties(prefix = "ats.teamtailor.fetch")
    public List<AtsGetOfferConfig> teamtailorConfigurations() {
        return new ArrayList<>();
    }

    @Bean
    @ConfigurationProperties(prefix = "ats.teamtailor.send")
    public List<AtsSendCandidaturesConfig> teamtailorSendCandidaturesConfig() {
        return new ArrayList<>();
    }

    @Bean
    @Scope("prototype")
    public GenericJobXmlParser<TeamtailorJob> teamtailorJobParser(AtsGetOfferConfig config) {
        return new GenericJobXmlParser<>(TeamtailorJob.class, config.getRootPath(), config.getAtsCode());
    }

    @EventListener(ApplicationReadyEvent.class)
    public void initializeServices() throws BeansException {
        teamtailorConfigurations().forEach(config ->
                {
                    var teamtailorJobParser = parserProvider.initializeBean(config.getAtsCode(), "teamtailorJobParser", config);
                    serviceProvider.initializeGenericService(
                            atsDataBuilder,
                            config,
                            teamtailorJobParser);
                }
        );
    }
}
