package com.erhgo.services.externaloffer;

import com.erhgo.domain.externaloffer.ExternalOffer;
import com.erhgo.openapi.dto.OfferDataDTO;
import com.erhgo.services.dtobuilder.SourcingDTOBuilder;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

@RequiredArgsConstructor
public class OfferDataExtractor<T extends AbstractRemoteOfferContent<T>> {

    private final SourcingDTOBuilder sourcingDTOBuilder;
    private final AtsDataBuilder<T> atsDataBuilder;
    private final ExternalJobParser<T> parser;

    public OfferDataDTO extractFromExternalOffer(ExternalOffer externalOffer) {
        var sourcingRecruitment = Optional.ofNullable(externalOffer.getRecruitment())
                .map(sourcingDTOBuilder::buildSourcingJobAndRecruitmentDTO)
                .orElse(null);
        var latest = atsDataBuilder.buildAtsData(parser.parseJob(externalOffer.getLastRawContent()));
        var previous = Optional.ofNullable(externalOffer.getPreviousRawContent())
                .filter(StringUtils::isNotBlank)
                .map(parser::parseJob)
                .map(atsDataBuilder::buildAtsData)
                .orElse(null);
        return new OfferDataDTO()
                .latestVersion(latest)
                .previousVersion(previous)
                .existingRecruitment(sourcingRecruitment);
    }
}
