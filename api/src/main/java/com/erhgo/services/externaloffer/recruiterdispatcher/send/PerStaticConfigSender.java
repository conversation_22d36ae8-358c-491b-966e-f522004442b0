package com.erhgo.services.externaloffer.recruiterdispatcher.send;

import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Stream;

@Service
class PerStaticConfigSender implements SendCandidaturesRecruitersFinder {

    @Override
    public List<String> findRecruitersCodesFor(AtsSendCandidaturesConfig atsConfig) {
        return Stream.of(atsConfig.getRecruiterCode()).filter(StringUtils::isNotBlank).toList();
    }
}
