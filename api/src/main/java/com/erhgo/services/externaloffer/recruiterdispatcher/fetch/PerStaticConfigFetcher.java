package com.erhgo.services.externaloffer.recruiterdispatcher.fetch;

import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import org.springframework.stereotype.Service;

@Service
class PerStaticConfigFetcher implements ExternalOfferRecruiterFinder {

    @Override
    public <A extends AbstractRemoteOfferContent<A>> String findRecruiterCodeFor(A offer, AtsGetOfferConfig atsConfig) {
        return atsConfig.getRecruiterCode();
    }
}
