package com.erhgo.services.externaloffer.successfactors;

import com.erhgo.openapi.dto.TypeContractCategoryDTO;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.utils.StringUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.*;

@Slf4j
@JacksonXmlRootElement(localName = "item")
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class SuccessFactorsJob extends AbstractRemoteOfferContent<SuccessFactorsJob> {
    public static final String ATS_CODE = "SUCCESS_FACTORS";
    private static final String DATE_PATTERN = "EEE, dd MM yyyy HH:mm:ss z";
    private static final Set<String> FIELDS_TO_IGNORE = Set.of("lastModificationDate");

    @JacksonXmlProperty(localName = "id")
    @Getter
    private String id;

    @JacksonXmlProperty(localName = "date")
    @JsonFormat(pattern = DATE_PATTERN, locale = "en")
    @Getter
    private ZonedDateTime date;

    @JacksonXmlProperty(localName = "title")
    private String offerTitle;

    @JacksonXmlProperty(localName = "description")
    @Getter
    private String description;

    @JacksonXmlProperty(localName = "location")
    @Getter
    private String location;

    @JacksonXmlProperty(localName = "country")
    private String countryCode;

    @JacksonXmlProperty(localName = "company")
    private String recruiterTitle;

    @JacksonXmlProperty(localName = "contract_type")
    @Getter
    private String contractType;

    @Override
    public String getOrganizationDescription() {
        return "";
    }

    @Override
    public String getOfferTitle() {
        return org.apache.commons.lang3.StringUtils.trimToEmpty(offerTitle);
    }

    @Override
    public TypeContractCategoryDTO getTypeContractCategory() {
        var comparator = StringUtils.DIATRIC_AND_CASE_INSENSITIVE_COMPARATOR.INSTANCE;
        return contractType == null ? null : switch (contractType) {
            case String type when comparator.compare(type, "Alternance") == 0 -> TypeContractCategoryDTO.PRO;
            case String type when comparator.compare(type, "CDD/MISSION") == 0 -> TypeContractCategoryDTO.TEMPORARY;
            default -> TypeContractCategoryDTO.PERMANENT;
        };
    }

    @Override
    public List<String> getCriterias() {
        return List.of();
    }

    @Override
    public String getRemoteRecruiterCode() {
        return recruiterTitle;
    }

    @Override
    public String getLocationIndication() { return getLocation(); }

    @JsonIgnore
    @Override
    public LocalDateTime getLastModificationDate() {
        return Optional.ofNullable(date).map(ZonedDateTime::toLocalDateTime).orElse(null);
    }

    @Override
    public Collection<String> getFieldsToIgnore() {
        return FIELDS_TO_IGNORE;
    }

    @Override
    public List<Integer> getSalaryValues() {
        return new ArrayList<>();
    }

    @Override
    public boolean isInFrance() {
        return "FR".equalsIgnoreCase(countryCode);
    }

}
