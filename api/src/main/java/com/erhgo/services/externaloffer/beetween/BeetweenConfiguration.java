package com.erhgo.services.externaloffer.beetween;

import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import com.erhgo.services.externaloffer.config.ParserProvider;
import com.erhgo.services.externaloffer.parsers.GenericJobXmlParser;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeansException;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.event.EventListener;

import java.util.ArrayList;
import java.util.List;

@Configuration
@EnableConfigurationProperties
@RequiredArgsConstructor
public class BeetweenConfiguration {

    private final AtsDataForBeetweenBuilder atsDataBuilder;
    private final ExternalOfferServiceProvider serviceProvider;
    private final ParserProvider parserProvider;

    @Bean
    @ConfigurationProperties(prefix = "ats.beetween.fetch")
    public List<AtsGetOfferConfig> beetweenConfigurations() {
        return new ArrayList<>();
    }

    @Bean
    @Scope("prototype")
    public GenericJobXmlParser<BeetweenJob> beetweenXmlParser(AtsGetOfferConfig config) {
        return new GenericJobXmlParser<>(BeetweenJob.class, config.getRootPath(), config.getAtsCode());
    }

    @Bean
    @ConfigurationProperties(prefix = "ats.beetween.send")
    public List<AtsSendCandidaturesConfig> beetweenSendCandidaturesConfig() {
        return new ArrayList<>();
    }

    @EventListener(ApplicationReadyEvent.class)
    public void initializeBeetweenServices() throws BeansException {
        beetweenConfigurations().forEach(config ->
                {
                    var beetweenXmlParser = parserProvider.initializeBean(config.getAtsCode(), "beetweenXmlParser", config);
                    serviceProvider.initializeGenericService(
                            atsDataBuilder,
                            config,
                            beetweenXmlParser);
                }
        );
    }
}
