package com.erhgo.services.externaloffer.recruiterdispatcher.send;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class SendCandidaturesRecruiterProvider {

    private final List<SendCandidaturesRecruitersFinder> providers;
    private final PerStaticConfigSender defaultProvider;

    public List<String> findRecruitersCodesFor(AtsSendCandidaturesConfig atsConfig) {
        return Optional.ofNullable(StringUtils.trimToNull(atsConfig.getRecruitersProvider()))
                .flatMap(this::getProviderForName)
                .orElse(defaultProvider)
                .findRecruitersCodesFor(atsConfig);
    }

    private Optional<SendCandidaturesRecruitersFinder> getProviderForName(String recruiterProviderName) {
        var providerOpt = providers.stream().filter(p -> AopProxyUtils.ultimateTargetClass(p).getSimpleName()
                        .equalsIgnoreCase(recruiterProviderName))
                .findFirst();
        if (providerOpt.isEmpty()) {
            throw new GenericTechnicalException("Misconfiguration: no provider found for name %s".formatted(recruiterProviderName));
        }
        return providerOpt;
    }
}
