package com.erhgo.services.externaloffer.successfactors;

import com.erhgo.openapi.dto.ExtractedAtsDataDTO;
import com.erhgo.services.externaloffer.AtsDataBuilder;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class AtsDataForSuccessFactorsBuilder implements AtsDataBuilder<SuccessFactorsJob> {

    public ExtractedAtsDataDTO buildAtsData(SuccessFactorsJob job) {
        return job == null ? null : new ExtractedAtsDataDTO()
                .typeContractInformations(Map.of("contrat", job.getContractType()))
                .occupationTitles(Map.of("occupation", job.getOfferTitle()))
                .descriptionParts(Map.of("description", job.getDescription()))
                .organizationDescriptionParts(new HashMap<>())
                .localisationInformations(Map.of("ville", job.getLocationIndication()))
                .criteriaRelatedData(new HashMap<>())
                .salaryRelatedData(new HashMap<>())
                ;
    }

}
