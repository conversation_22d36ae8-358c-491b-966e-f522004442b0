package com.erhgo.services.externaloffer;

import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.externaloffer.ExternalOffer;
import com.erhgo.domain.externaloffer.RecruitmentCreationState;
import com.erhgo.domain.utils.EventPublisherUtils;
import com.erhgo.openapi.dto.ExternalOfferSummaryDTO;
import com.erhgo.openapi.dto.ExternalOffersPageDTO;
import com.erhgo.openapi.dto.RecruitmentCreationStateDTO;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.security.Role;
import com.erhgo.services.dtobuilder.PageDTOBuilder;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.ParserProvider;
import com.erhgo.services.externaloffer.notification.RemoteOfferEvent;
import com.erhgo.utils.DateTimeUtils;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.UUID;


@RequiredArgsConstructor
@Service
@Slf4j
public class ExternalOfferService {
    private final ExternalOfferRepository externalOfferRepository;
    private final ParserProvider parserProvider;

    @Transactional(readOnly = true)
    @RolesAllowed({Role.ODAS_ADMIN, Role.SOURCING})
    public ExternalOffersPageDTO findExternalOffers(
            Integer size,
            Integer page,
            String organizationCode) {
        var pageRequest = PageRequest.of(page, size);
        var externalOffersPage = externalOfferRepository.findExternalOffersForRecruiter(organizationCode, pageRequest);
        return PageDTOBuilder.buildExternalOffersPage(externalOffersPage.map(this::convertToSummaryDTO));
    }

    private ExternalOfferSummaryDTO convertToSummaryDTO(ExternalOffer externalOffer) {
        var summaryDTO = new ExternalOfferSummaryDTO()
                .title(externalOffer.getOfferTitle())
                .location(externalOffer.getOfferLocation())
                .remoteId(externalOffer.getRemoteId())
                .recruitmentId(externalOffer.getRecruitmentId())
                .lastModificationDate(DateTimeUtils.localDateTimeToOffsetDateTime(externalOffer.getRemoteLastModificationDate()))
                .recruitmentCreationState(RecruitmentCreationStateDTO.valueOf(externalOffer.getRecruitmentCreationState().name()))
                .id(externalOffer.getUuid());

        if (externalOffer.getLastRawContent() != null) {
            var remoteJob = parserProvider.getParser(externalOffer.getAtsCode()).parseJob(externalOffer.getLastRawContent());
            if (remoteJob != null) {
                summaryDTO
                        .typeContractCategory(remoteJob.getTypeContractCategory())
                        .salaries(remoteJob.getSalaryValues())
                        .description(remoteJob.getDescription())
                        .organizationDescription(remoteJob.getOrganizationDescription());
            }
        }

        return summaryDTO;
    }


    @Transactional
    @RolesAllowed({Role.ODAS_ADMIN, Role.SOURCING})
    public void ignoreExternalOffer(UUID offerId) {
        var offer = externalOfferRepository.findById(offerId)
                .orElseThrow(() -> new EntityNotFoundException(offerId, ExternalOffer.class));

        if (offer.getRecruitment() != null) {
            throw new GenericTechnicalException("Cannot ignore offer with existing recruitment");
        }

        offer.setRecruitmentCreationState(RecruitmentCreationState.IGNORE);
        log.debug("Offer {} ignored", offerId);
    }

    @Transactional
    @RolesAllowed({Role.ODAS_ADMIN, Role.SOURCING})
    public void integrateExternalOffer(UUID offerId) {
        log.debug("Start integration for external offer {}", offerId);
        var offer = externalOfferRepository.findById(offerId)
                .orElseThrow(() -> new EntityNotFoundException(offerId, ExternalOffer.class));

        if (offer.getRecruitment() != null) {
            log.warn("External offer {} already has a recruitment {}, skipping integration.", offerId, offer.getRecruitment().getId());
            return;
        }

        if (!offer.isRecruitmentCreationManual() && !offer.isRecruitmentCreationIgnored()) {
            throw new GenericTechnicalException("Offer is not in MANUAL or IGNORE state and cannot be integrated");
        }

        offer.setRecruitmentCreationState(RecruitmentCreationState.PROCESSING);
        EventPublisherUtils.publish(new RemoteOfferEvent(List.of(offer), Collections.emptyList(), Collections.emptyList(), Collections.emptyList(), AtsGetOfferConfig.buildForManualOffer(offer)));
    }
}
