package com.erhgo.services.externaloffer.inrecruiting;

import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.openapi.dto.TypeContractCategoryDTO;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Joiner;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class InRecruitingJob extends AbstractRemoteOfferContent<InRecruitingJob> {

    public static final String DATE_PATTERN = "yyyy-MM-dd HH:mm:ss";

    @JsonProperty("id")
    private String id;

    @JsonProperty("title")
    private String offerTitle;

    @JsonProperty("company")
    private String remoteRecruiterCode;

    @JsonProperty("last_update")
    @JsonFormat(pattern = DATE_PATTERN)
    private LocalDateTime lastModificationDate;

    @JsonProperty("contract_type")
    private String jobContract;

    @JsonProperty("lat")
    private Double latitude;

    @JsonProperty("lng")
    private Double longitude;

    @JsonProperty("position_description")
    private String positionDescription;
    @JsonProperty("requirements_description")
    private String requirementsDescription;

    @JsonProperty("other_information_description")
    private String moreDescription;

    @JsonProperty("company_description")
    private String organizationDescription;

    @JsonProperty("years_of_experience")
    private String yearsOfExperience;

    @JsonProperty("remote_working")
    private String remoteWork;

    @JsonProperty("location")
    private String city;

    @JsonProperty("county_code")
    private String department;

    @JsonProperty("nation")
    private String country;

    @JsonProperty("url_video")
    private String urlVideo;

    @JsonProperty("logo")
    private String logo;

    @JsonProperty("team")
    private String team;

    @JsonProperty("vacancy_owner")
    private String vacancyOwner;

    @Override
    public List<Integer> getSalaryValues() {
        return List.of();
    }

    @Override
    public TypeContractCategoryDTO getTypeContractCategory() {
        return switch (StringUtils.trimToEmpty(jobContract)) {
            case "CDI" -> TypeContractCategoryDTO.PERMANENT;
            case "CDD" -> TypeContractCategoryDTO.TEMPORARY;
            default -> null;
        };
    }

    @Override
    public List<String> getCriterias() {
        var trimmedRemoteWork = StringUtils.trimToEmpty(remoteWork).toLowerCase();
        if (!trimmedRemoteWork.isEmpty() && !trimmedRemoteWork.contains("présentiel") && !trimmedRemoteWork.contains("non") && !trimmedRemoteWork.contains("false")) {
            return List.of(CriteriaValue.getValueCodeForPartialRemoteWork());
        }
        return List.of();
    }

    @Override
    public String getLocationIndication() {
        var trimmedCity = StringUtils.trimToEmpty(city);
        var trimmedZipCode = StringUtils.trimToEmpty(department);
        if (trimmedCity.isEmpty() && trimmedZipCode.isEmpty()) {
            return "";
        }
        return "%s (%s)".formatted(trimmedCity, trimmedZipCode);
    }

    @Override
    public boolean isInFrance() {
        return country != null && country.equalsIgnoreCase("France");
    }

    @Override
    public boolean shouldNotCreateNewOffer(AtsGetOfferConfig config) {
        return StringUtils.isBlank(getLocationIndication()) || !isInFrance() || getTypeContractCategory() == TypeContractCategoryDTO.PRO;
    }

    @Override
    public String getDescription() {
        var descriptionItems = new ArrayList<>();
        if (StringUtils.isNotBlank(logo)) {
            descriptionItems.add("<img src=\"%s\" />".formatted(StringUtils.trimToEmpty(logo)));
        }
        if (StringUtils.isNotBlank(positionDescription)) {
            descriptionItems.add(StringUtils.trimToEmpty(positionDescription));
        }
        if (StringUtils.isNotBlank(requirementsDescription)) {
            descriptionItems.add(StringUtils.trimToEmpty(requirementsDescription));
        }
        if (StringUtils.isNotBlank(yearsOfExperience)) {
            descriptionItems.add("Expérience souhaitée&nbsp;: %s".formatted(yearsOfExperience));
        }
        if (StringUtils.isNotBlank(urlVideo)) {
            descriptionItems.add("Vidéo de présentation&nbsp;: <a target=\"_blank\" href=\"%s\">%s</a>\"".formatted(urlVideo, urlVideo));
        }
        if (StringUtils.isNotBlank(moreDescription)) {
            descriptionItems.add(StringUtils.trimToEmpty(moreDescription));
        }
        return Joiner.on("<br/>").join(descriptionItems);
    }

    @Override
    public List<String> getRelatedUsernames() {
        return Stream.of(team, vacancyOwner).filter(StringUtils::isNotBlank).toList();
    }
}
