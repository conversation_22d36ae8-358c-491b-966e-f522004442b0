package com.erhgo.services.externaloffer.recruiterdispatcher;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Entity
@Table(uniqueConstraints = {
        @UniqueConstraint(columnNames = {"atsCode", "configCode", "locationCode"}, name = "UC_PERLOCATIONCONFIGITEM"),
})
@Data
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class PerOfferATSConfigurationItem {

    @Id
    private UUID id = UUID.randomUUID();
    /**
     * Code of ATS - must be equal to one of {@link com.erhgo.services.externaloffer.config.AtsGetOfferConfig#atsCode}
     * Not empty
     */
    private String atsCode;
    /**
     * Code of config - must be equal to one of {@link com.erhgo.services.externaloffer.config.AtsGetOfferConfig#configCode}
     * May be empty, in case of single feed for multiple customers (fi {@link com.erhgo.services.externaloffer.taleez.TaleezConfiguration})
     */
    private String configCode;
    /**
     * The code of recruiter in the external offer (ie. in remote system) ; one of this or locationCode must not be
     * empty.
     */
    private String remoteRecruiterCode;
    /**
     * THe code of location defining the recruiter code to use ; one of this or remoteRecruiterCode must not be empty
     */
    private String locationCode;
    /**
     * The recruiterCode to use, according the tuple {atsCode!, configCode?, remoteRecruiterCode || locationCode}
     */
    private String recruiterCode;
}
