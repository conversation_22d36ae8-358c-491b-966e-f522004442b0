package com.erhgo.services.externaloffer.candidatus;

import com.erhgo.services.externaloffer.ExternalJobParser;
import com.erhgo.services.externaloffer.GenericAtsDataBuilder;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import com.erhgo.services.externaloffer.config.ParserProvider;
import com.erhgo.services.externaloffer.parsers.GenericJobXmlParser;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeansException;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.event.EventListener;

import java.util.ArrayList;
import java.util.List;

@Configuration
@EnableConfigurationProperties
@RequiredArgsConstructor
public class CandidatusConfiguration {

    private final GenericAtsDataBuilder atsDataBuilder;
    private final ExternalOfferServiceProvider serviceProvider;
    private final ParserProvider parserProvider;

    @Bean
    @ConfigurationProperties(prefix = "ats.candidatus.fetch")
    public List<AtsGetOfferConfig> candidatusConfigurations() {
        return new ArrayList<>();
    }

    @Bean
    @ConfigurationProperties(prefix = "ats.candidatus.send")
    public List<AtsSendCandidaturesConfig> candidatusSendCandidaturesConfig() {
        return new ArrayList<>();
    }

    @Bean
    @Scope("prototype")
    public ExternalJobParser<CandidatusJob> candidatusJobXmlParser(AtsGetOfferConfig config) {
        return new GenericJobXmlParser<>(CandidatusJob.class, config.getRootPath(), config.getAtsCode());
    }

    @EventListener(ApplicationReadyEvent.class)
    public void initializeCandidatusServices() throws BeansException {
        candidatusConfigurations().forEach(config ->
                {
                    var candidatusJobXmlParser = parserProvider.initializeBean(config.getAtsCode(), "candidatusJobXmlParser", config);
                    serviceProvider.initializeGenericService(
                            atsDataBuilder,
                            config,
                            candidatusJobXmlParser);
                }
        );
    }
}
