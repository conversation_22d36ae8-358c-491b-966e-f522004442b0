package com.erhgo.services.externaloffer.recruiterdispatcher.send;

import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.PerOfferATSConfigurationItem;
import com.erhgo.services.externaloffer.recruiterdispatcher.PerOfferATSConfigurationItemRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Per offer recruiter provider search in {@link PerOfferATSConfigurationItem} items, based on remote recruiter code
 */
@Service
@RequiredArgsConstructor
@Slf4j
class PerDBConfigSender implements SendCandidaturesRecruitersFinder {

    private final PerOfferATSConfigurationItemRepository repository;

    @Override
    public List<String> findRecruitersCodesFor(AtsSendCandidaturesConfig atsConfig) {
        return repository.findByAtsCodeAndConfigCode(atsConfig.getAtsCode(), atsConfig.getRecruitersProviderConfigCode()).stream().map(PerOfferATSConfigurationItem::getRecruiterCode).toList();
    }
}
