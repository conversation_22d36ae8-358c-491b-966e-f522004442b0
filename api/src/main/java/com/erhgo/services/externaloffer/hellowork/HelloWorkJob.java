package com.erhgo.services.externaloffer.hellowork;


import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.openapi.dto.TypeContractCategoryDTO;
import com.erhgo.openapi.dto.WorkContractDurationUnitDTO;
import com.erhgo.openapi.dto.WorkingTimeDTO;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.fasterxml.jackson.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Slf4j
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class HelloWorkJob extends AbstractRemoteOfferContent<HelloWorkJob> {

    private static final Set<String> FIELDS_TO_IGNORE = Set.of("lastModificationDate", "creationDate", "rawContent", "otherFields");

    public static final String DATE_PATTERN = "yyyy-MM-dd'T'HH:mm:ss.SSS";
    @JsonProperty("EncryptedId")
    private String id;

    @JsonProperty("NameAdvertise")
    private String offerTitle;

    @JsonProperty("CompanyName")
    private String remoteRecruiterCode;

    @JsonProperty("LastDiffusion")
    @JsonFormat(pattern = DATE_PATTERN)
    private LocalDateTime lastModificationDate;

    @JsonProperty("JobContract")
    private String jobContract;

    @JsonProperty("TemporaryContractDuration")
    private String temporaryContractDuration;

    @JsonProperty("TemporaryContractDurationInterval")
    private String temporaryContractDurationInterval;

    @JsonProperty("MinRemuneration")
    private Double minRemuneration;

    @JsonProperty("MaxRemuneration")
    private Double maxRemuneration;

    @JsonProperty("RemunerationCurrency")
    private String remunerationCurrency;

    @JsonProperty("RemunerationInterval")
    private String remunerationInterval;

    @JsonProperty("RemunerationIsNotVisible")
    private Boolean hideSalary;

    @JsonProperty("City")
    private String city;

    @JsonProperty("ZipCode")
    private String zipCode;

    @JsonProperty("Country")
    private String country;

    @JsonProperty("EmailPage")
    private String candidatureEmail;

    @JsonProperty("LocationTravelArea")
    private String locationTravelArea;

    @JsonProperty("IsFullTime")
    private Boolean isFullTime;

    @JsonProperty("WorkingPeriod")
    private String workingPeriod;

    @JsonProperty("IsTelework")
    private Boolean isTelework;

    @JsonProperty("TeleworkRate")
    private String teleworkRate;

    @JsonProperty("Creator")
    private String creator;

    @JsonProperty("Intervenors")
    private List<String> intervenors;

    @JsonProperty("DescriptionFormatted")
    private String descriptionFormatted;

    @JsonProperty("ProfileFormatted")
    private String profileFormatted;

    @JsonProperty("UsefullInformationFormatted")
    private String usefullInformationFormatted;

    @JsonProperty("Tags")
    private List<String> tags;

    @JsonProperty("Images")
    private List<String> images;

    @JsonProperty("VideoUrl")
    private String videoUrl;

    @JsonProperty("CompanyInformationFormatted")
    private String organizationDescription;

    @JsonProperty("ExternalUrl")
    private String externalUrl;

    @JsonIgnore
    private Map<String, Object> otherFields = new HashMap<>();

    @JsonAnySetter
    public void otherField(String k, Object v) {
        otherFields.put(k, v);
    }

    @Override
    public Collection<String> getFieldsToIgnore() {
        return FIELDS_TO_IGNORE;
    }

    @Override
    public List<Integer> getSalaryValues() {
        return Stream.of(minRemuneration, maxRemuneration).filter(Objects::nonNull).map(this::toAnnualSalary).toList();
    }

    private Integer toAnnualSalary(Double salary) {
        var coef = switch (remunerationInterval) {
            case "heure" -> hourlyToYearlySalaryCoefficient();
            case "mois" -> monthlyToYearlySalaryCoefficient();
            case "semaine" -> weeklyToYearlySalaryCoefficient();
            case "an" -> 1;
            default -> null;
        };
        if (coef == null) {
            log.error("Unknown remuneration interval {}, consider 1", remunerationInterval);
            coef = 1;
        }
        if (!"EUR".equalsIgnoreCase(remunerationCurrency) && remunerationCurrency != null) {
            log.warn("Caution: unknown or wrong currency {} ?", remunerationCurrency);
        }
        return ((Double) (salary * coef)).intValue();
    }

    @Override
    public String getDescription() {
        var jobDescription = new StringJoiner("<br>");

        if (!StringUtils.isBlank(this.descriptionFormatted)) {
            jobDescription.add(this.descriptionFormatted);
        }

        if (!StringUtils.isBlank(this.profileFormatted)) {
            jobDescription.add("<h3>Profil recherché</h3>");
            jobDescription.add(this.profileFormatted);
        }

        if (!StringUtils.isBlank(this.usefullInformationFormatted)) {
            jobDescription.add("<h3>Informations complémentaires</h3>");
            jobDescription.add(this.usefullInformationFormatted);
        }

        if (videoUrl != null && !videoUrl.isEmpty()) {
            jobDescription.add("""
                    <strong>Plus d'info&nbsp;: <a href="%s" target="_blank">voir la vidéo</a></strong>
                    """.formatted(videoUrl));
        }

        if (images != null && !images.isEmpty()) {
            jobDescription.add(
                    images.stream()
                            .filter(StringUtils::isNotBlank)
                            .map("<img src=\"%s\"/>"::formatted)
                            .collect(Collectors.joining(""))
            );
        }

        return jobDescription.toString();
    }

    /**
     * CDI/ CDD/ Intérim / mission/ Saisonnier / Contrat de professionnalisation / Apprentissage / Alternance / Stage / Freelance / indépendant / Franchisé / Portage salarial / Intermittent / Associé(e)
     */
    @Override
    public TypeContractCategoryDTO getTypeContractCategory() {
        if (jobContract != null) {
            if (jobContract.toUpperCase().contains("CDI")) {
                return TypeContractCategoryDTO.PERMANENT;
            }
            if (jobContract.toUpperCase().contains("CDD") || jobContract.toUpperCase().contains("MISSION")) {
                return TypeContractCategoryDTO.TEMPORARY;
            }
            if (jobContract.toUpperCase().contains("ALT") || jobContract.toUpperCase().contains("PRO") || jobContract.toUpperCase().contains("CP") || jobContract.toUpperCase().contains("APPRENTI")) {
                return TypeContractCategoryDTO.PRO;
            }
            if (jobContract.toUpperCase().contains("INTÉRIM")) {
                return TypeContractCategoryDTO.INTERIM;
            }
            if (jobContract.toUpperCase().contains("SAISON")) {
                return TypeContractCategoryDTO.SEASONAL;
            }
        }
        return null;
    }

    @Override
    public List<String> getCriterias() {

        var remoteWorkCriteria = Optional.ofNullable(isTelework)
                .filter(a -> a)
                .map(remoteWork -> StringUtils.trimToEmpty(teleworkRate).contains("100") ? CriteriaValue.getValueCodeForFullRemoteWork() : CriteriaValue.getValueCodeForPartialRemoteWork())
                .orElse(null);
        return Stream.of(
                        remoteWorkCriteria,
                        Optional.ofNullable(StringUtils.trimToNull(workingPeriod)).filter(c -> !c.toLowerCase().contains("jour")).map(c -> CriteriaValue.NIGHT_WORK_CRITERIA_VALUE_CODE).orElse(null),
                        Optional.ofNullable(locationTravelArea).filter(c -> !c.toLowerCase().startsWith("pas de")).map(c-> CriteriaValue.REGULAR_MOVE).orElse(null)
                ).filter(Objects::nonNull)
                .toList();

    }

    @Override
    public boolean isInFrance() {
        return StringUtils.trimToEmpty(country).toLowerCase().contains("france");
    }

    @Override
    public String getLocationIndication() {
        var trimPostcode = StringUtils.trimToEmpty(zipCode);
        var trimCity = StringUtils.trimToEmpty(city);
        if (trimPostcode.isEmpty() && trimCity.isEmpty()) {
            return "";
        }
        return "%s (%s)".formatted(trimCity, trimPostcode);
    }

    @Override
    public Integer getWorkContractDuration() {
        return Optional.ofNullable(StringUtils.trimToNull(temporaryContractDuration)).map(s -> {
            try {
                return ((Float) Float.parseFloat(s)).intValue();
            } catch (NumberFormatException e) {
                log.debug("{} is not a float", s, e);
                return null;
            }
        }).orElse(null);
    }

    @Override
    public WorkContractDurationUnitDTO getWorkContractDurationUnit() {
        if ( getWorkContractDuration() == null){
            return null;
        }
        var trimUnit = StringUtils.trimToNull(temporaryContractDurationInterval);
        WorkContractDurationUnitDTO duration = null;
        if (trimUnit != null) {
            duration = switch (trimUnit) {
                case "an" -> WorkContractDurationUnitDTO.YEAR;
                case "jour" -> WorkContractDurationUnitDTO.DAY;
                case "semaine" -> WorkContractDurationUnitDTO.WEEK;
                case "mois" -> WorkContractDurationUnitDTO.MONTH;
                default -> null;
            };
            if (duration == null) {
                log.error("{} contract duration unit is unknown, using month", trimUnit);
                duration = WorkContractDurationUnitDTO.MONTH;
            }
        }
        return duration;
    }

    @Override
    public List<String> getRelatedUsernames() {
        var result = new ArrayList<String>();
        if (StringUtils.isNotBlank(creator)) {
            result.add(creator.trim());
        }
        if (intervenors != null) {
            intervenors.stream().map(StringUtils::trimToEmpty).filter(StringUtils::isNotBlank).forEach(result::add);
        }
        return result;
    }

    @Override
    public WorkingTimeDTO getWorkingTimeType() {
        return BooleanUtils.isFalse(isFullTime) ? WorkingTimeDTO.PART_TIME : WorkingTimeDTO.FULL_TIME;
    }

}
