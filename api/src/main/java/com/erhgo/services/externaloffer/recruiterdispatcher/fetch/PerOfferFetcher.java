package com.erhgo.services.externaloffer.recruiterdispatcher.fetch;

import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.PerOfferATSConfigurationItem;
import com.erhgo.services.externaloffer.recruiterdispatcher.PerOfferATSConfigurationItemRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * Per offer recruiter provider search in {@link PerOfferATSConfigurationItem} items, based on remote recruiter code
 */
@Service
@RequiredArgsConstructor
@Slf4j
class PerOfferFetcher implements ExternalOfferRecruiterFinder {

    private final PerOfferATSConfigurationItemRepository repository;

    @Override
    @Cacheable(value = "recruiterCodesPerOfferCache", key = "#offer.getRemoteRecruiterCode()", unless = "#result == null")
    public <A extends AbstractRemoteOfferContent<A>> String findRecruiterCodeFor(A offer, AtsGetOfferConfig atsConfig) {
        var remoteRecruiterCode = offer.getRemoteRecruiterCode();
        if (StringUtils.isBlank(remoteRecruiterCode)) {
            log.error("Empty offer remote recruiter code for offer {} and ats {}", offer.getId(), atsConfig);
            return null;
        }
        var atsConfiguration = repository.findOneByAtsCodeAndRemoteRecruiterCode(atsConfig.getAtsCode(), remoteRecruiterCode);
        return getRecruiterCode(offer, atsConfig, atsConfiguration, remoteRecruiterCode);
    }

    protected <A extends AbstractRemoteOfferContent<A>> @Nullable String getRecruiterCode(A offer, AtsGetOfferConfig atsConfig, Optional<PerOfferATSConfigurationItem> atsConfiguration, String remoteRecruiterCode) {
        if (atsConfiguration.isEmpty()) {
            log.error("Unknown offer remote recruiter code {} for offer {} and ats {} - missing configuration in PerLocationATSConfigurationItem", remoteRecruiterCode, offer.getId(), atsConfig);
            return null;
        }
        return atsConfiguration.get().getRecruiterCode();
    }
}
