package com.erhgo.services.externaloffer.limiter;

import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;
import java.util.function.Predicate;

@Service("globalLimiter")
@RequiredArgsConstructor
@Slf4j
public class GlobalLimiter implements ExternalOfferLimiter {

    public static final String ATS_LIMIT_GLOBAL_KEY = "ats.limit.global";
    private final ConfigurablePropertyRepository configurablePropertyRepository;

    protected Integer getPropertyOrDefaults(AtsGetOfferConfig config) {
        return Optional.ofNullable(configurablePropertyRepository.getPropertyValueOrDefaults(ATS_LIMIT_GLOBAL_KEY, config.getAtsAndConfigCode(), config.getAtsCode()))
                .map(Integer::parseInt)
                .or(() -> Optional.ofNullable(config.getGlobalMaxNumberOfOffersAllowed()))
                .orElse(DEFAULT_LIMIT)
                ;
    }

    @Override
    public <A extends AbstractRemoteOfferContent<A>> Map<Predicate<A>, Integer> limits(AtsGetOfferConfig config) {
        var globalLimit = getPropertyOrDefaults(config);
        log.debug("Global limit for ATS {}: {}", config.getAtsCode(), globalLimit);
        return Map.of(a -> true, globalLimit);
    }
}
