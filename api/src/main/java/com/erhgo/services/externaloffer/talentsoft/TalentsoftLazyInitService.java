package com.erhgo.services.externaloffer.talentsoft;

import com.erhgo.services.externaloffer.GenericAtsClient;
import com.erhgo.services.externaloffer.OfferLazyInitializer;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.http.RetryableHttpClient;
import com.erhgo.utils.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import org.jetbrains.annotations.NotNull;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service("talentsoftLazyInitService")
@RequiredArgsConstructor
public class TalentsoftLazyInitService implements OfferLazyInitializer<TalentsoftJob> {

    private final GenericAtsClient genericAtsClient;
    private final ObjectMapper objectMapper;
    private final ApplicationContext applicationContext;

    private RetryableHttpClient httpClient;

    @PostConstruct
    public void init() {
        log.info("AbstractATSWithHttpClientNotificationSender initialized for {}", getClass().getSimpleName());
        httpClient = applicationContext.getBean(RetryableHttpClient.class, 30, 30, 30);
    }

    @Override
    public TalentsoftJob init(TalentsoftJob job, AtsGetOfferConfig config) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(job.getLocationIndication())) {
            return job;
        }
        var thisSpringifiedForCacheReason = applicationContext.getBean(TalentsoftLazyInitService.class);
        var location = thisSpringifiedForCacheReason.fetchLocation(job, config);
        location.ifPresentOrElse(l -> {
            log.debug("Talentsoft job {} initialized with value {}", job.getId(), l);
            job.setLocationIndication(l);
        }, () -> {
            log.warn("Talentsoft job {} : no location found", job.getId());
        });
        return job;
    }

    @Cacheable(value = "locationPerTalentsoftUrl", key = "#job.getId()", unless = "#result.isEmpty()")
    public @NotNull Optional<String> fetchLocation(TalentsoftJob job, AtsGetOfferConfig config) {
        var result = Optional.<String>empty();
        try {
            log.debug("Initializing Talentsoft job {}", job.getId());
            var url = StringUtils.buildUri(config.getLazyInitUrl(), Optional.of(Map.of("reference", List.of(job.getId())))).toURL();
            var request = new Request.Builder()
                    .url(url)
                    .header("Authorization", "Bearer " + genericAtsClient.generateToken(config.getTokenAuthentication()))
                    .get()
                    .build();
            try (var response = httpClient.executeRequest(request)) {
                var body = response.body() == null ? "{}" : response.body().string();
                if (!response.isSuccessful()) {
                    log.error("Unable to get offer detail for Talentsoft lazy init service - bad status {}, body {}, job {} - config: {}", response.code(), body, job, config);
                } else {
                    result = Optional.ofNullable(objectMapper.readTree(body))
                            .map(a -> {
                                var city = a.path("customFields").path("location").path("shortText1");
                                var department = a.path("department").path(0).path("label");
                                return (city.isMissingNode() ? "" : (city.asText() + " ")) + (department.isMissingNode() ? "" : department.asText());
                            })
                            .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                    ;
                }
            }
        } catch (IOException e) {
            log.error("Unable to get offer detail for Talentsoft lazy init service {} - config: {}", job, config, e);
        }
        return result;
    }
}
