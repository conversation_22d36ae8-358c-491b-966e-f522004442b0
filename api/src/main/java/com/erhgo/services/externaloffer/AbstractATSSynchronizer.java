package com.erhgo.services.externaloffer;

import com.erhgo.domain.externaloffer.ExternalOffer;
import com.erhgo.domain.externaloffer.ExternalOfferEventType;
import com.erhgo.domain.externaloffer.RecruitmentCreationState;
import com.erhgo.domain.utils.EventPublisherUtils;
import com.erhgo.openapi.dto.AtsOfferSimulatedDTO;
import com.erhgo.openapi.dto.OfferDataDTO;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.security.Role;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.limiter.ExternalOfferLimiter;
import com.erhgo.services.externaloffer.notification.RemoteOfferEvent;
import com.erhgo.services.externaloffer.recruiterdispatcher.fetch.ExternalOfferRecruiterProvider;
import com.google.common.base.Joiner;
import jakarta.annotation.security.RolesAllowed;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationContext;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.LinkedCaseInsensitiveMap;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RequiredArgsConstructor
@Slf4j
public abstract class AbstractATSSynchronizer<A extends AbstractRemoteOfferContent<A>> {

    public static final String DEFAULT_LIMITER_SERVICE_NAME = "perAuraInOutLimiter";
    private static final long GLOBAL_MAX_PAGES_PER_ATS = 100L;
    protected final ConfigurablePropertyRepository configurablePropertyRepository;
    protected final ExternalOfferRepository repository;
    private final ExternalOfferRecruiterProvider externalOfferRecruiterProvider;
    @Getter
    protected final AtsGetOfferConfig atsConfig;
    protected final ApplicationContext applicationContext;

    public String getAtsCode() {
        return getAtsConfig().getAtsCode();
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    public void simulateAtsOffer(AtsOfferSimulatedDTO offer) {
        var offers = getParser().parseJobs(offer.getRawFlow(), atsConfig).stream().map(this::updateRecruiterCode).filter(l -> l.getRecruiterCode() != null).toList();
        mergeRemoteAndLocalOffers(offers);
    }

    protected List<ExternalOffer> findExternalOffers() {
        return repository.findExternalOffersForConfig(getAtsCode(), getAtsConfig().getRecruiterCode(), getAtsConfig().getConfigCode());
    }

    @RolesAllowed({Role.ODAS_ADMIN, Role.SOURCING})
    public void fetchAndUpdateOffers() {
        try {
            var hasNext = true;
            var offers = new ArrayList<A>();
            var page = new AtomicLong(1L);
            var pageSize = atsConfig.getPageSize();
            var totalSize = new AtomicLong(0L);
            while (hasNext && page.get() <= GLOBAL_MAX_PAGES_PER_ATS) {
                Map<String, List<String>> queryParams = null;
                if (atsConfig.isPaginated()) {
                    queryParams = Map.of(
                            atsConfig.getPageIndexParam(), List.of(String.valueOf(page)),
                            atsConfig.getPageSizeParam(), List.of(String.valueOf(pageSize))
                    );
                }
                var pageContent = fetchOffers(queryParams);
                pageContent.stream().map(this::updateRecruiterCode).filter(l -> l.getRecruiterCode() != null).forEach(offers::add);
                totalSize.set(pageContent.getTotalElements());
                hasNext = atsConfig.isPaginated() && computeHasNext(page.get(), pageSize, totalSize);
                page.incrementAndGet();
            }
            if (page.get() >= GLOBAL_MAX_PAGES_PER_ATS) {
                log.error("Suspicious pagination handling for ATS {}: more than {} pages", getAtsCode(), GLOBAL_MAX_PAGES_PER_ATS);
            }
            Optional.ofNullable(atsConfig.getGlobalMaxNumberOfOffersAllowed()).ifPresent(a -> {
                if (pageSize != null && page.get() * pageSize >= a) {
                    log.warn("ATS {} has {} offers, which is more than configured max {} - ignore the last offers", getAtsCode(), totalSize, a);
                }
            });
            mergeRemoteAndLocalOffers(offers);
        } catch (RuntimeException e) {
            log.error("Unable to handle offer for {}", getAtsConfig(), e);
        }
    }

    private boolean computeHasNext(Long page, Integer pageSize, AtomicLong totalSize) {
        if (page == null || pageSize == null) {
            return false;
        }
        boolean hasNext;
        var numberOfAlreadyHandledOffers = page * pageSize;
        var globalMaxReached = Optional.ofNullable(atsConfig.getGlobalMaxNumberOfOffersAllowed()).map(a -> numberOfAlreadyHandledOffers >= a).orElse(false);
        hasNext = atsConfig.isPaginated() &&
                numberOfAlreadyHandledOffers < totalSize.get() &&
                !globalMaxReached;
        return hasNext;
    }

    protected Page<A> fetchOffers(Map<String, List<String>> queryParams) {
        return getParser().parseJobs(fetchRemoteOffersRawContent(queryParams), getAtsConfig());
    }

    protected void mergeRemoteAndLocalOffers(List<A> offers) {

        var forceSuspension = new ArrayList<String>();
        var current = offers
                .stream()
                .filter(l -> {
                    var ignore = l.shouldForceSuspension(getAtsConfig());
                    if (ignore) {
                        forceSuspension.add(l.getId());
                    }
                    return !ignore;
                })
                .collect(Collectors.toMap(A::getId, Function.identity(), (existing, replacement) -> existing, LinkedCaseInsensitiveMap::new));

        log.debug("For ATS {}: {} offers in a 'require suspension' state over {}", getAtsConfig().getAtsAndConfigCode(), forceSuspension.size(), current.size());

        var previous = findExternalOffers().stream()
                .collect(Collectors.toMap(ExternalOffer::getRemoteId, Function.identity(), (a, b) -> a, LinkedCaseInsensitiveMap::new));

        var createdOffers = createNewOffersAndUpdatePreviousIfRequired(current, previous);
        var suspendedOffers = suspendRemovedOffers(current, previous);
        var reactivatedOffers = reactivatePreviousOffers(current, previous);
        var modifiedOffers = modifyOffers(current, previous);

        emitEvent(createdOffers, suspendedOffers, reactivatedOffers, modifiedOffers);
    }

    private A updateRecruiterCode(A offer) {
        var recruiterCode = externalOfferRecruiterProvider.findRecruiterCodeFor(offer, getAtsConfig());
        if (recruiterCode == null) {
            log.debug("No recruiter configured for offer {} - ignore", offer.getId());
        }
        offer.setRecruiterCode(recruiterCode);
        return offer;
    }

    private void emitEvent(
            List<ExternalOffer> createdOffers,
            List<ExternalOffer> suspendedOffers,
            List<ExternalOffer> reactivatedOffers,
            List<ExternalOffer> modifiedOffers
    ) {
        log.debug("{} created offers, {} suspended offers, {} republished offers, {} modified offers on ATS {}", createdOffers, suspendedOffers, reactivatedOffers, modifiedOffers, getAtsCode());
        EventPublisherUtils.publish(new RemoteOfferEvent(createdOffers, suspendedOffers, reactivatedOffers, modifiedOffers, getAtsConfig()));
    }

    protected List<ExternalOffer> modifyOffers(Map<String, A> current, Map<String, ExternalOffer> previous) {
        return previous.entrySet().stream()
                .filter(prev -> !prev.getValue().isRecruitmentCreationIgnored())
                .filter(prev -> {
                    var previousRemoteOfferContent = getParser().parseJob(prev.getValue().getLastRawContent());
                    return current.containsKey(prev.getKey()) && current.get(prev.getKey()).doesModify(previousRemoteOfferContent);
                })
                .map(r -> updateLocalWithRemote(r.getValue(), current.get(r.getKey())))
                .toList();
    }

    private ExternalOffer updateLocalWithRemote(ExternalOffer local, A remote) {
        local
                .setRemoteLastModificationDate(remote.getLastModificationDate())
                .setLastEventType(ExternalOfferEventType.MODIFIED)
                .setLastRawContent(remote.getRawContent(), remote.getOfferTitle(), remote.getRemoteRecruiterCode(), remote.getLocationIndication(), remote.getRelatedUsernames(), remote.getCandidatureEmail());
        repository.save(local);
        return local;
    }

    private ExternalOffer updateLocalWithRemoteForOfferChangingConfigCode(ExternalOffer local, ExternalOffer remote) {
        local
                .setRemoteLastModificationDate(remote.getRemoteLastModificationDate())
                .setLastEventType(remote.getLastEventType())
                .setConfigCode(remote.getConfigCode())
                .setLastRawContent(remote.getLastRawContent(), remote.getOfferTitle(), remote.getOfferRecruiterCode(), remote.getOfferLocation(), remote.relatedUsernames(), remote.getCandidatureEmail());
        repository.save(local);
        return local;
    }

    private List<ExternalOffer> reactivatePreviousOffers(Map<String, A> current, Map<String, ExternalOffer> previous) {
        return previous.entrySet().stream()
                .filter(e -> current.containsKey(e.getKey()) && e.getValue().isSuspended())
                .map(e -> updateState(e, ExternalOfferEventType.REPUBLISHED))
                .toList();
    }

    private ExternalOffer updateState(Map.Entry<String, ExternalOffer> e, ExternalOfferEventType externalOfferEventType) {
        var r = e.getValue();
        r.setLastEventType(externalOfferEventType);
        repository.save(r);
        return r;
    }

    private List<ExternalOffer> suspendRemovedOffers(Map<String, A> current, Map<String, ExternalOffer> previous) {
        return previous.entrySet().stream()
                .filter(e -> !current.containsKey(e.getKey()) && !e.getValue().isSuspended() && !e.getValue().isRecruitmentCreationIgnored())
                .map(e -> updateState(e, ExternalOfferEventType.SUSPENDED))
                .toList();
    }

    private List<ExternalOffer> createNewOffersAndUpdatePreviousIfRequired(Map<String, A> current, Map<String, ExternalOffer> previous) {
        var allOffersToCreate = current.entrySet().stream()
                .filter(e -> !previous.containsKey(e.getKey()))
                .map(Map.Entry::getValue)
                .toList();

        var allOffersSize = allOffersToCreate.size();
        log.info("For ATS {}: {} new offers to consider for creation", getAtsConfig().getAtsAndConfigCode(), allOffersSize);
        var newOffers = limitedStream(allOffersToCreate.stream())
                .map(this::buildExternalOfferDO)
                .collect(Collectors.groupingBy(ExternalOffer::getRemoteId));

        log.debug("For ATS {}: {} new offers effectively created over {} to consider", getAtsCode(), newOffers.size(), allOffersSize);
        var duplicatedEntries = newOffers.entrySet().stream().filter(kv -> kv.getValue().size() > 1).collect(Collectors.toSet());
        if (!duplicatedEntries.isEmpty()) {
            log.warn("Some duplicated remote id in ATS {}: {}", getAtsConfig().getAtsAndConfigCode(), duplicatedEntries.stream().map(Map.Entry::getKey).collect(Collectors.joining(", ")));
        }
        return saveAndHandleConflict(previous, newOffers);
    }

    protected @NotNull List<ExternalOffer> saveAndHandleConflict(Map<String, ExternalOffer> previous, Map<String, @NotNull List<ExternalOffer>> newOffers) {
        var createdOffers = newOffers.values().stream().map(List::getFirst).map(this::saveSafely).filter(Objects::nonNull).toList();
        previous.putAll(createdOffers.stream().filter(o -> o.getLastEventType() != ExternalOfferEventType.CREATED).collect(Collectors.toMap(ExternalOffer::getRemoteId, Function.identity())));
        return createdOffers.stream().filter(o -> o.getLastEventType() == ExternalOfferEventType.CREATED).toList();
    }

    private ExternalOffer saveSafely(ExternalOffer externalOffer) {
        try {
            return repository.save(externalOffer);
        } catch (DataAccessException e) {
            log.warn("Unable to save external offer {} - let's try to update it", externalOffer, e);
            return fetchAndUpdateOfferOnOtherConfigCode(externalOffer);
        }
    }

    private ExternalOffer fetchAndUpdateOfferOnOtherConfigCode(ExternalOffer externalOffer) {
        return applicationContext.getBean(TransactionTemplate.class).execute((_unused) ->

        {
            try {
                return repository.findByRemoteIdAndComputedRecruiterCodeAndAtsCode(externalOffer.getRemoteId(), externalOffer.getComputedRecruiterCode(), externalOffer.getAtsCode())
                        .map(a -> updateLocalWithRemoteForOfferChangingConfigCode(a, externalOffer))
                        .orElseGet(() -> {
                            log.error("Unable to create or update external offer {}", externalOffer);
                            return null;
                        });
            } catch (RuntimeException e) {
                log.error("Unable to create or update external offer {}", externalOffer, e);
            }
            return null;
        });
    }

    private Stream<A> limitedStream(Stream<A> allOffersStream) {
        var limiterBeanName = Optional.ofNullable(atsConfig.getLimiterServiceName()).orElse(DEFAULT_LIMITER_SERVICE_NAME);
        Map<Predicate<A>, Integer> predicateLimits = applicationContext.getBean(limiterBeanName, ExternalOfferLimiter.class).limits(getAtsConfig());
        var counters = new HashMap<Predicate<A>, AtomicInteger>();
        predicateLimits.forEach((predicate, limit) -> counters.put(predicate, new AtomicInteger(limit)));
        var ignoredOffersIds = new ArrayList<String>();
        OfferLazyInitializer<A> lazyInitService = Optional.ofNullable(atsConfig.getLazyInitServiceName())
                .map(a -> applicationContext.getBean(a, OfferLazyInitializer.class))
                .orElse(OfferLazyInitializer.identity());
        var result = allOffersStream
                .takeWhile(__ -> counters.values().stream().anyMatch(v -> v.get() > 0))
                .map(a -> lazyInitService.init(a, atsConfig))
                .filter(a -> {
                    var ignored = a.shouldNotCreateNewOffer(atsConfig);
                    if (ignored) {
                        ignoredOffersIds.add(a.getId());
                    }
                    return !ignored;
                })
                .sequential()
                .reduce(Stream.<A>builder(),
                        (stream, offer) -> {
                            var counter = counters.entrySet().stream()
                                    .filter(v -> v.getKey().test(offer) && v.getValue().get() > 0)
                                    .map(Map.Entry::getValue)
                                    .findFirst();
                            counter.ifPresent(c -> {
                                c.decrementAndGet();
                                stream.add(offer);
                            });
                            return stream;
                        },
                        (stream1, stream2) -> stream1
                ).build();
        if (ignoredOffersIds.isEmpty()) {
            log.debug("No offer ignored for {}", atsConfig);
        } else {
            log.info("Offers {} ignored for {}", Joiner.on(", ").join(ignoredOffersIds), atsConfig);
        }
        return result;
    }

    private ExternalOffer buildExternalOfferDO(A remoteJob) {
        var offer = new ExternalOffer()
                .setAtsCode(getAtsCode())
                .setLastEventType(ExternalOfferEventType.CREATED)
                .setLastRawContent(remoteJob.getRawContent(), remoteJob.getOfferTitle(), remoteJob.getRemoteRecruiterCode(), remoteJob.getLocationIndication(), remoteJob.getRelatedUsernames(), remoteJob.getCandidatureEmail())
                .setRemoteLastModificationDate(remoteJob.getLastModificationDate())
                .setRemoteId(remoteJob.getId())
                .setComputedRecruiterCode(remoteJob.getRecruiterCode())
                .setConfigCode(getAtsConfig().getConfigCode())
                .setRecruitmentCreationState(getAtsConfig().getRequiresConfirmation() ? RecruitmentCreationState.MANUAL : RecruitmentCreationState.PROCESSING);

        if (remoteJob.shouldNotGenerateRecruitment(getAtsConfig())) {
            offer.setRecruitmentCreationState(RecruitmentCreationState.IGNORE);
        }

        log.debug("New offer created {}", offer.toString());
        return offer;
    }

    @Transactional
    public OfferDataDTO extractDataFromExternalOffer(UUID externalOfferId) {
        var externalOffer = externalOfferId != null ? repository.findById(externalOfferId).orElse(null) : null;
        return externalOffer != null ? getOfferDataExtractor().extractFromExternalOffer(externalOffer) : null;
    }

    protected abstract String fetchRemoteOffersRawContent(Map<String, List<String>> queryParams);

    protected abstract ExternalJobParser<A> getParser();

    protected abstract OfferDataExtractor<A> getOfferDataExtractor();

}
