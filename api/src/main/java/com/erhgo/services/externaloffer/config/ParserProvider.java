package com.erhgo.services.externaloffer.config;

import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.services.externaloffer.ExternalJobParser;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
@RequiredArgsConstructor
public class ParserProvider {

    private final ApplicationContext applicationContext;
    private final Map<String, ExternalJobParser<?>> parsers = new ConcurrentHashMap<>();

    public <A extends AbstractRemoteOfferContent<A>, T extends ExternalJobParser<A>> T initializeBean(String key,
                                                                                                      String name,
                                                                                                      Object... params) {
        var service = (T) applicationContext.getBean(name, params);
        parsers.put(key, service);
        return service;
    }

    public <A extends AbstractRemoteOfferContent<A>, T extends ExternalJobParser<A>> T getParser(String key) {
        return (T) parsers.get(key);
    }

}
