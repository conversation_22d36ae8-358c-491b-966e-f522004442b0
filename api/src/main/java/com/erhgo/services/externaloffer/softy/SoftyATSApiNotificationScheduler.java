package com.erhgo.services.externaloffer.softy;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.externaloffer.candidature.AbstractATSWithHttpClientNotificationSender;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.send.SendCandidaturesRecruiterProvider;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import com.erhgo.utils.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
// see https://recrutement.softy.pro/api_softy_doc.html
public class SoftyATSApiNotificationScheduler extends AbstractATSWithHttpClientNotificationSender {
    private static final String INVALID_JOB_MARKER = StringUtils.normalizeLowerCase("Job Offer id is not valid");
    private final ObjectMapper objectMapper;

    public SoftyATSApiNotificationScheduler(
            RecruitmentCandidatureRepository recruitmentCandidatureRepository,
            SpontaneousCandidatureRepository spontaneousCandidatureRepository,
            AbstractCandidatureRepository abstractCandidatureRepository,
            KeycloakService keycloakService,
            ConfigurablePropertyRepository configurablePropertyRepository,
            UserProfileCompetencesExportService userProfileCompetencesExportService,
            SecurityService securityService,
            List<AtsSendCandidaturesConfig> softySendCandidaturesConfig,
            TransactionTemplate transactionTemplate,
            ObjectMapper objectMapper,
            SendCandidaturesRecruiterProvider sendCandidaturesRecruiterProvider
    ) {
        super(keycloakService, configurablePropertyRepository, userProfileCompetencesExportService, transactionTemplate, recruitmentCandidatureRepository, spontaneousCandidatureRepository, softySendCandidaturesConfig, securityService, abstractCandidatureRepository, sendCandidaturesRecruiterProvider);
        this.objectMapper = objectMapper;
    }

    @Override
    @Scheduled(initialDelay = 24, fixedRate = 60, timeUnit = TimeUnit.MINUTES)
    @SchedulerLock(name = "handleNewCandidaturesForSoftyATS", lockAtLeastFor = "35M")
    public void handleNewCandidatures() {
        super.handleNewCandidatures();
    }

    protected Request buildSendCandidatureRequest(UserRepresentation userRepresentation, RecruitmentCandidature candidature, AtsSendCandidaturesConfig config) throws IOException {
        var body = transactionTemplate.execute(__ -> buildCandidatureDTO(userRepresentation, candidature));

        return new Request.Builder()
                .url(config.getCandidatureNotificationUrl())
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .header(HttpHeaders.AUTHORIZATION, BEARER_PREFIX + config.getCandidatureNotificationApiKey())
                .post(RequestBody.create(objectMapper.writeValueAsBytes(body)))
                .build();
    }

    private SoftySendCandidatureCommandDTO buildCandidatureDTO(UserRepresentation userRepresentation, RecruitmentCandidature candidature) {
        try {
            var fullProfile = getProfile(candidature, userRepresentation.getFullname(), ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE);

            var base64CV = fullProfile.readAllBytesAsStringifiedBase64();
            var serializedCV = CVCandidatureDTO.builder()
                    .data(base64CV)
                    .contentType("application/pdf")
                    .build();

            return SoftySendCandidatureCommandDTO.builder()
                    .job(candidature.getRecruitment().getExternalOffer().getRemoteId())
                    .prenom(userRepresentation.getFirstNameNeverEmpty())
                    .nom(userRepresentation.getLastNameNeverEmpty())
                    .email(userRepresentation.getEmail())
                    .phone(candidature.getUserProfile().getPhoneNumber())
                    .city(candidature.getUserProfile().getCity())
                    .postcode(candidature.getUserProfile().getPostcode())
                    .cv(serializedCV)
                    .build();
        } catch (IOException e) {
            log.error("Exception while building profile for candidature {} for Softy", candidature.getId(), e);
            throw new GenericTechnicalException("Unable to send candidature %d to Softy".formatted(candidature.getId()), e);
        }
    }

    @Override
    protected boolean isCandidatureOnClosedRecruitment(int code, String body) {
        return HttpStatus.BAD_REQUEST.value() == code && StringUtils.normalizeLowerCase(body).contains(INVALID_JOB_MARKER);
    }
}
