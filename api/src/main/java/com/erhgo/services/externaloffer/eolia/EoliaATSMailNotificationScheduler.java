package com.erhgo.services.externaloffer.eolia;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.externaloffer.candidature.AbstractATSMailNotificationSender;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.send.SendCandidaturesRecruiterProvider;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailNotifier;
import com.erhgo.services.reminder.EmailToNotifyFilterInterface;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import com.erhgo.utils.DateTimeUtils;
import jakarta.annotation.Nullable;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class EoliaATSMailNotificationScheduler extends AbstractATSMailNotificationSender {

    private static final String MAIL_SUBJECT = "%s - %s";

    private static final String MAIL_BODY = """
            anonymouscode : %s
            email : %s
            nom : %s
            prenom : %s
            ville : %s
            cp : %s
            portable : %s
            saisielib : %s
            origine : jenesuisPASunCV
            datesoumission : %s
            """.replace("\n", "\r\n");

    public EoliaATSMailNotificationScheduler(RecruitmentCandidatureRepository recruitmentCandidatureRepository, SpontaneousCandidatureRepository spontaneousCandidatureRepository,
                                             AbstractCandidatureRepository abstractCandidatureRepository,
                                             KeycloakService keycloakService, ConfigurablePropertyRepository configurablePropertyRepository, UserProfileCompetencesExportService userProfileCompetencesExportService, @Nullable MailNotifier mailNotifier, SecurityService securityService, List<AtsSendCandidaturesConfig> eoliaSendCandidaturesConfig, TransactionTemplate transactionTemplate, EmailToNotifyFilterInterface emailToNotifyFilterInterface, SendCandidaturesRecruiterProvider sendCandidaturesRecruiterProvider) {
        super(recruitmentCandidatureRepository, spontaneousCandidatureRepository, abstractCandidatureRepository, keycloakService, configurablePropertyRepository, userProfileCompetencesExportService, mailNotifier, securityService, eoliaSendCandidaturesConfig, transactionTemplate, emailToNotifyFilterInterface, sendCandidaturesRecruiterProvider);
    }

    @PostConstruct
    public void init() {
        log.info("EoliaATSMailNotificationHandler initialized");
    }

    @Scheduled(initialDelay = 12, fixedRate = 60, timeUnit = TimeUnit.MINUTES)
    @SchedulerLock(name = "handleNewCandidaturesForEoliaATS", lockAtLeastFor = "35M")
    @Override
    public void handleNewCandidatures() {
        super.handleNewCandidatures();
    }

    protected String buildBody(RecruitmentCandidature candidature, UserRepresentation userRepresentation) {
        return MAIL_BODY.formatted(
                candidature.getAnonymousCode(),
                userRepresentation.getEmail(),
                StringUtils.trimToEmpty(userRepresentation.getLastName()),
                StringUtils.trimToEmpty(userRepresentation.getFirstName()),
                StringUtils.trimToEmpty(candidature.getUserProfile().getCity()),
                StringUtils.trimToEmpty(candidature.getUserProfile().getPostcode()),
                StringUtils.trimToEmpty(candidature.getUserProfile().getPhoneNumber()),
                StringUtils.trimToEmpty(candidature.getCustomAnswer()),
                DateTimeUtils.formatDate(candidature.getSubmissionDate())
        );
    }

    protected String buildSubject(RecruitmentCandidature candidature) {
        return MAIL_SUBJECT.formatted(candidature.getJobTitle(), candidature.getRecruitment().getExternalOffer().getRemoteId());
    }

}
