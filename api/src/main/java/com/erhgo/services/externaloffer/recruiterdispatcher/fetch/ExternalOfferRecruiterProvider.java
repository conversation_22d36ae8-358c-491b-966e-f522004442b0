package com.erhgo.services.externaloffer.recruiterdispatcher.fetch;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class ExternalOfferRecruiterProvider {

    private final List<ExternalOfferRecruiterFinder> providers;
    private final PerStaticConfigFetcher defaultProvider;

    public <A extends AbstractRemoteOfferContent<A>> String findRecruiterCodeFor(A offer, AtsGetOfferConfig atsConfig) {
        return Optional.ofNullable(StringUtils.trimToNull(atsConfig.getRecruiterProvider()))
                .flatMap(this::getProviderForName)
                .orElse(defaultProvider)
                .findRecruiterCodeFor(offer, atsConfig);
    }

    private Optional<ExternalOfferRecruiterFinder> getProviderForName(String recruiterProviderName) {
        var providerOpt = providers.stream().filter(p -> AopProxyUtils.ultimateTargetClass(p).getSimpleName()
                        .equalsIgnoreCase(recruiterProviderName))
                .findFirst();
        if (providerOpt.isEmpty()) {
            throw new GenericTechnicalException("Misconfiguration: no provider found for name %s".formatted(recruiterProviderName));
        }
        return providerOpt;
    }
}
