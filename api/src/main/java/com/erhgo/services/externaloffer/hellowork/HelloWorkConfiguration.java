package com.erhgo.services.externaloffer.hellowork;

import com.erhgo.services.externaloffer.GenericAtsDataBuilder;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import com.erhgo.services.externaloffer.config.ParserProvider;
import com.erhgo.services.externaloffer.parsers.GenericJobJsonParser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.event.EventListener;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Configuration
@EnableConfigurationProperties
public class HelloWorkConfiguration {

    private final GenericAtsDataBuilder atsDataBuilder;
    private final ExternalOfferServiceProvider serviceProvider;
    private final ParserProvider parserProvider;

    @Bean
    @ConfigurationProperties(prefix = "ats.hello-work.fetch")
    public List<AtsGetOfferConfig> helloWorkConfigurations() {
        return new ArrayList<>();
    }

    @Bean
    @ConfigurationProperties(prefix = "ats.hello-work.send")
    public List<AtsSendCandidaturesConfig> helloWorkSendCandidaturesConfig() {
        return new ArrayList<>();
    }

    @Bean
    @Scope("prototype")
    public GenericJobJsonParser<HelloWorkJob> helloWorkJobJSONParser() {
        return new GenericJobJsonParser<>(HelloWorkJob.class, "Results");
    }

    @EventListener(ApplicationReadyEvent.class)
    public void initializeServices() throws BeansException {
        helloWorkConfigurations().forEach(config ->
                {
                    var helloWorkJobJSONParser = parserProvider.initializeBean(config.getAtsCode(), "helloWorkJobJSONParser");
                    serviceProvider.initializeGenericService(
                            atsDataBuilder,
                            config,
                            helloWorkJobJSONParser);
                }
        );
    }
}
