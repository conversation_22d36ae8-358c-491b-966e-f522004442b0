package com.erhgo.services.externaloffer.taleez;

import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import com.erhgo.services.externaloffer.config.ParserProvider;
import com.erhgo.services.externaloffer.parsers.GenericJobXmlParser;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeansException;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.event.EventListener;

import java.util.ArrayList;
import java.util.List;

@Configuration
@EnableConfigurationProperties
@RequiredArgsConstructor
public class TaleezConfiguration {

    private final AtsDataForTaleezBuilder atsDataBuilder;
    private final ExternalOfferServiceProvider serviceProvider;
    private final ParserProvider parserProvider;

    @Bean
    @ConfigurationProperties(prefix = "ats.taleez.fetch")
    public List<AtsGetOfferConfig> taleezConfigurations() {
        return new ArrayList<>();
    }

    @Bean
    @Scope("prototype")
    public GenericJobXmlParser<TaleezJob> taleezXmlParser(AtsGetOfferConfig config) {
        return new GenericJobXmlParser<>(TaleezJob.class, config.getRootPath(), config.getAtsCode());
    }

    @Bean
    @ConfigurationProperties(prefix = "ats.taleez.send")
    public List<AtsSendCandidaturesConfig> taleezSendCandidaturesConfig() {
        return new ArrayList<>();
    }

    @EventListener(ApplicationReadyEvent.class)
    public void initializeTaleezServices() throws BeansException {
        taleezConfigurations().forEach(config ->
                {
                    var taleezXmlParser = parserProvider.initializeBean(config.getAtsCode(), "taleezXmlParser", config);
                    serviceProvider.initializeGenericService(
                            atsDataBuilder,
                            config,
                            taleezXmlParser);
                }
        );
    }
}
