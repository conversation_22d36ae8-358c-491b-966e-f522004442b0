package com.erhgo.services.externaloffer.softy;

import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.DiplomaLevel;
import com.erhgo.openapi.dto.LocationDTO;
import com.erhgo.openapi.dto.TypeContractCategoryDTO;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.utils.StringUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static org.apache.commons.lang3.StringUtils.*;

@Slf4j
@JacksonXmlRootElement(localName = "job")
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class SoftyJob extends AbstractRemoteOfferContent<SoftyJob> {
    private static final String DATE_PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final Set<String> FIELDS_TO_IGNORE = Set.of("creationDate");

    @JacksonXmlProperty(localName = "company")
    private String remoteRecruiterCode;

    @JacksonXmlProperty(localName = "id")
    private String id;

    @JacksonXmlProperty(localName = "date")
    @JsonFormat(pattern = DATE_PATTERN)
    private LocalDateTime creationDate;

    @JacksonXmlProperty(localName = "title")
    private String offerTitle;

    @JacksonXmlProperty(localName = "description")
    private String organizationDescription;

    @JacksonXmlProperty(localName = "position")
    private String description;

    @JacksonXmlProperty(localName = "location")
    private String city;

    @JacksonXmlProperty(localName = "postcode")
    private String postcode;

    @JacksonXmlProperty(localName = "long")
    private String longitude;

    @JacksonXmlProperty(localName = "lat")
    private String latitude;

    @JacksonXmlProperty(localName = "country")
    private String country;

    @JacksonXmlProperty(localName = "salary")
    private String salary;

    @JacksonXmlProperty(localName = "contract_type")
    @Getter
    private String contractType;

    @JacksonXmlProperty(localName = "education")
    @Getter
    private String education;

    @JacksonXmlProperty(localName = "work_time")
    @Getter
    private String workTime;

    @Override
    public LocalDateTime getLastModificationDate() {
        return creationDate;
    }

    @Override
    public List<Integer> getSalaryValues() { return List.of(); }

    @Override
    public TypeContractCategoryDTO getTypeContractCategory() {
        var comparator = StringUtils.DIATRIC_AND_CASE_INSENSITIVE_COMPARATOR.INSTANCE;
        return switch (contractType) {
            case String type when comparator.compare(type, "CDD") == 0 -> TypeContractCategoryDTO.TEMPORARY;
            case String type when comparator.compare(type, "Intérim") == 0 -> TypeContractCategoryDTO.TEMPORARY;
            case String type when comparator.compare(type, "Alternance") == 0 -> TypeContractCategoryDTO.PRO;
            case String type when comparator.compare(type, "Apprentissage") == 0 -> TypeContractCategoryDTO.PRO;
            case String type when comparator.compare(type, "Professionnalisation") == 0 -> TypeContractCategoryDTO.PRO;
            case String type when comparator.compare(type, "Stage") == 0 -> TypeContractCategoryDTO.PRO;
            default -> TypeContractCategoryDTO.PERMANENT;
        };
    }

    @Override
    public List<String> getCriterias() {
        var criterias = new ArrayList<String>();
        var trimmedWorkTime = trimToEmpty(workTime);
        var trimmedEducation = trimToEmpty(education);

        if (containsIgnoreCase(trimmedWorkTime, "Nuit")) {
            criterias.add(CriteriaValue.NIGHT_WORK_CRITERIA_VALUE_CODE);
        }

        if (containsIgnoreCase(trimmedWorkTime, "Samedi") || containsIgnoreCase(trimmedWorkTime, "Dimanche")) {
            criterias.add(CriteriaValue.WEEKEND_WORK_CRITERIA_VALUE_CODE);
        }

        if (containsIgnoreCase(trimmedEducation, "Bac +2 à Bac +4")) {
            criterias.add(CriteriaValue.getValueCodeForDiplomaLevel(DiplomaLevel.BAC_2));
        } else if (containsIgnoreCase(trimmedEducation, "Bac")) {
            criterias.add(CriteriaValue.getValueCodeForDiplomaLevel(DiplomaLevel.BAC_BAC_PRO));
        } else if (containsIgnoreCase(trimmedEducation, "Sans diplôme")) {
            criterias.add(CriteriaValue.getValueCodeForDiplomaLevel(DiplomaLevel.WITHOUT_DIPLOMA));
        }

        return criterias;
    }

    @Override
    @JsonIgnore
    public String getLocationIndication() {
        var trimCity = trimToEmpty(city);
        var trimPostcode = trimToEmpty(postcode);
        return trimCity.isEmpty() && trimPostcode.isEmpty() ? "" : "%s (%s)".formatted(trimCity, trimPostcode);
    }

    @Override
    public boolean isInFrance() {
        return "France".equalsIgnoreCase(trimToEmpty(country));
    }

    @Override
    public LocationDTO getBuiltinLocation() {
        if (isNotBlank(longitude) && isNotBlank(latitude) && isNotBlank(city) && isNotBlank(postcode)) {
            try {
                return new LocationDTO().longitude(Float.parseFloat(longitude)).latitude(Float.parseFloat(latitude)).city(city).postcode(postcode);
            } catch (IllegalArgumentException e) {
                log.warn("Invalid longitude ({}) or latitude ({})", longitude, latitude, e);
            }
        }
        return null;
    }
}
