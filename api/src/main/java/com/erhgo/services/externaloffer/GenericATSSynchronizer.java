package com.erhgo.services.externaloffer;

import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.services.dtobuilder.SourcingDTOBuilder;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.fetch.ExternalOfferRecruiterProvider;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
public class GenericATSSynchronizer<A extends AbstractRemoteOfferContent<A>> extends AbstractATSSynchronizer<A> {

    @Getter
    private final ExternalJobParser<A> parser;
    private final GenericAtsClient atsClient;
    private final OfferDataExtractor<A> offerDataExtractor;

    public <B extends AtsDataBuilder<A>> GenericATSSynchronizer(
            ExternalOfferRepository externalOfferRepository,
            ConfigurablePropertyRepository configurablePropertyRepository,
            GenericAtsClient genericAtsClient,
            B atsDataBuilder,
            AtsGetOfferConfig config,
            ExternalJobParser<A> parser,
            SourcingDTOBuilder sourcingDTOBuilder,
            ExternalOfferRecruiterProvider recruiterProvider,
            ApplicationContext applicationContext
    ) {
        super(configurablePropertyRepository, externalOfferRepository, recruiterProvider, config, applicationContext);
        this.atsClient = genericAtsClient;
        this.parser = parser;
        this.offerDataExtractor = new OfferDataExtractor<>(sourcingDTOBuilder, atsDataBuilder, this.parser);
    }

    @Override
    protected String fetchRemoteOffersRawContent(Map<String, List<String>> queryParams) {
        return atsClient.fetch(atsConfig, Optional.ofNullable(queryParams));
    }

    @Override
    protected OfferDataExtractor<A> getOfferDataExtractor() {
        return offerDataExtractor;
    }

}
