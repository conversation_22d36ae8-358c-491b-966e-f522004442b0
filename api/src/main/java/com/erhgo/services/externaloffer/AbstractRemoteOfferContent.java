package com.erhgo.services.externaloffer;

import com.erhgo.openapi.dto.LocationDTO;
import com.erhgo.openapi.dto.TypeContractCategoryDTO;
import com.erhgo.openapi.dto.WorkContractDurationUnitDTO;
import com.erhgo.openapi.dto.WorkingTimeDTO;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Modifier;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Stream;


@Getter
@EqualsAndHashCode
@ToString
@Slf4j
public abstract class AbstractRemoteOfferContent<A extends AbstractRemoteOfferContent<A>> {

    public static final int NB_WEEKLY_HOURS_TO_CONSIDER_FULL_TIME = 30;
    @JsonIgnore
    protected String rawContent;

    @JsonIgnore
    @Setter
    protected String recruiterCode;

    public A setRawContent(String rawContent) {
        this.rawContent = rawContent;
        return (A) this;
    }

    public boolean doesModify(A value) {
        if (value == null) {
            return true;
        }
        var isModified = getLastModificationDate() != value.getLastModificationDate() && (getLastModificationDate() == null || value.getLastModificationDate() == null || getLastModificationDate().isAfter(value.getLastModificationDate()));
        return isModified && hasAnyModification(value);
    }

    @SuppressWarnings("java:S3011")
    private boolean hasAnyModification(A other) {
        return other == null || Stream.of(getClass().getDeclaredFields())
                .filter(f -> !getFieldsToIgnoreInternal().contains(f.getName()))
                .filter(f -> !Modifier.isStatic(f.getModifiers()))
                .anyMatch(f -> {
                    f.setAccessible(true);
                    try {
                        var isDifferent = !Objects.deepEquals(f.get(this), f.get(other));
                        if (isDifferent) {
                            log.debug("Modification on offer {} for field {}", other.getId(), f.getName());
                        }
                        return isDifferent;
                    } catch (IllegalAccessException e) {
                        return true;
                    } finally {
                        f.setAccessible(false);
                    }
                });
    }

    protected Collection<String> getFieldsToIgnore() {
        return new HashSet<>();
    }

    private Collection<String> getFieldsToIgnoreInternal() {
        var result = new HashSet<>(getFieldsToIgnore());
        result.add("rawContent");
        result.add("recruiterCode");
        return result;

    }

    public boolean shouldForceSuspension(AtsGetOfferConfig config) {
        return shouldNotCreateNewOffer(config);
    }

    public boolean shouldNotCreateNewOffer(AtsGetOfferConfig config) {
        return StringUtils.isBlank(getLocationIndication()) || !isInFrance() || getTypeContractCategory() == TypeContractCategoryDTO.PRO || getTypeContractCategory() == null;
    }

    public boolean shouldNotGenerateRecruitment(AtsGetOfferConfig config) {
        return false;
    }

    public List<String> getRelatedUsernames() {
        return Collections.emptyList();
    }

    public boolean isInsideAURA() {
        return com.erhgo.utils.StringUtils.isInsideAURA(getLocationIndication());
    }

    public LocationDTO getBuiltinLocation() {
        return null;
    }

    public abstract String getId();

    public abstract LocalDateTime getLastModificationDate();

    public abstract List<Integer> getSalaryValues();

    public String getRecruitersInfos() {
        return getRelatedUsernames() == null ? null : String.join(",", getRelatedUsernames());
    }

    public abstract String getDescription();

    public abstract String getOrganizationDescription();

    public abstract String getOfferTitle();

    public abstract TypeContractCategoryDTO getTypeContractCategory();

    public abstract List<String> getCriterias();

    public abstract String getRemoteRecruiterCode();

    public abstract String getLocationIndication();

    public abstract boolean isInFrance();

    public WorkingTimeDTO getWorkingTimeType() {
        return Optional.ofNullable(getWorkingTimeWeeklyDuration()).map(v -> v >= NB_WEEKLY_HOURS_TO_CONSIDER_FULL_TIME ? WorkingTimeDTO.FULL_TIME : WorkingTimeDTO.PART_TIME).orElse(null);
    }

    public Integer getWorkingTimeWeeklyDuration() {
        return null;
    }

    public boolean hasBuiltinLocation() {
        var location = getBuiltinLocation();
        return location != null &&
                StringUtils.isNotBlank(location.getCity()) &&
                StringUtils.isNotBlank(location.getPostcode()) &&
                location.getLongitude() != null &&
                location.getLatitude() != null;
    }

    public Boolean getHideSalary() {
        return null;
    }

    public WorkContractDurationUnitDTO getWorkContractDurationUnit() {
        return null;
    }

    public Integer getWorkContractDuration() {
        return null;
    }

    public String getCandidatureEmail() {
        return null;
    }

    protected Integer hourlyToYearlySalaryCoefficient() {
        return 35 * 52;
    }

    protected Integer weeklyToYearlySalaryCoefficient() {
        return 52;
    }

    protected Integer monthlyToYearlySalaryCoefficient() {
        return 12;
    }

    protected Integer dailyToYearlySalaryCoefficient() {
        return 365;
    }

    public String getRemoteRecruiterTitle() {
        return null;
    }
}
