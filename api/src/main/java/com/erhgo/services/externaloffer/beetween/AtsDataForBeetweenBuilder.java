package com.erhgo.services.externaloffer.beetween;

import com.erhgo.openapi.dto.ExtractedAtsDataDTO;
import com.erhgo.services.externaloffer.AtsDataBuilder;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class AtsDataForBeetweenBuilder implements AtsDataBuilder<BeetweenJob> {

    public ExtractedAtsDataDTO buildAtsData(BeetweenJob job) {
        return job == null ? null : new ExtractedAtsDataDTO()
                .typeContractInformations(Map.of("contrat", String.valueOf(job.getContract())))
                .occupationTitles(Map.of("occupation", job.getOfferTitle()))
                .descriptionParts(Map.of("description", String.valueOf(job.getDescription())))
                .organizationDescriptionParts(new HashMap<>())
                .localisationInformations(Map.of("ville", String.valueOf(job.getLocation())))
                .criteriaRelatedData(new HashMap<>())
                .salaryRelatedData(Map.of("salary", job.getSalaryValues().stream().map(String::valueOf).collect(Collectors.joining(", "))))
                ;
    }
}
