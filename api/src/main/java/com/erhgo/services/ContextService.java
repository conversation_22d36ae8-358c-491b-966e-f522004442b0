package com.erhgo.services;

import com.erhgo.domain.KeycloakUserSummary;
import com.erhgo.domain.exceptions.CategoryLevelDoesNotExist;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.Origin;
import com.erhgo.openapi.dto.ContextDTO;
import com.erhgo.openapi.dto.ContextsPageDTO;
import com.erhgo.openapi.dto.SaveContextCommandDTO;
import com.erhgo.openapi.dto.SortDirectionDTO;
import com.erhgo.repositories.CategoryLevelRepository;
import com.erhgo.repositories.ContextRepository;
import com.erhgo.security.Role;
import com.erhgo.services.dtobuilder.ContextDTOBuilder;
import com.erhgo.services.dtobuilder.PageDTOBuilder;
import jakarta.annotation.security.RolesAllowed;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;


@Service
@RequiredArgsConstructor
@AllArgsConstructor
@Slf4j
public class ContextService {

    @Autowired
    private ContextRepository contextRepository;

    @Autowired
    private CategoryLevelRepository categoryLevelRepository;

    @Autowired
    private ContextDTOBuilder contextDTOBuilder;

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public ContextDTO createNewContext(SaveContextCommandDTO createContextCommand) {
        var context = new Context();
        return createOrUpdateContext(createContextCommand, context);
    }

    private ContextDTO createOrUpdateContext(SaveContextCommandDTO saveContextCommand, Context context) {
        long categoryLevelId = saveContextCommand.getCategoryLevelId();
        final var categoryLevel = categoryLevelRepository.findById(categoryLevelId)
                .orElseThrow(() -> new CategoryLevelDoesNotExist(categoryLevelId));

        context.setTitle(saveContextCommand.getTitle());
        context.setDescription(saveContextCommand.getDescription());
        context.setCategoryLevel(categoryLevel);
        if (saveContextCommand.getOrigin() != null) {
            context.setOrigin(Origin.valueOf(saveContextCommand.getOrigin().name()));
        }

        context = contextRepository.save(context);

        return contextDTOBuilder.buildContextDTO(context);
    }

    @Transactional(readOnly = true)
    public ContextDTO getContext(UUID contextId) {
        final var context = findById(contextId);

        return contextDTOBuilder.buildContextDTO(context);
    }

    @Transactional(readOnly = true)
    public Context findById(UUID contextId) {
        return contextRepository.findById(contextId)
                .orElseThrow(() -> new EntityNotFoundException(contextId, Context.class));
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public ContextDTO updateContext(UUID contextId, SaveContextCommandDTO updateContextCommand) {
        var context = findById(contextId);

        return createOrUpdateContext(updateContextCommand, context);
    }

    @Transactional(readOnly = true)
    public ContextsPageDTO listContexts(Integer page, Integer size, String by, SortDirectionDTO direction, String filter, String userId) {

        final var sortDirection = direction == SortDirectionDTO.ASC ? Sort.Direction.ASC : Sort.Direction.DESC;
        var safeBy = Strings.isBlank(by) ? "title" : by;
        final Pageable pageable = PageRequest.of(page, size, sortDirection, safeBy);

        Page<ContextDTO> pageContext;

        if (Strings.isNotBlank(filter)) {
            pageContext = contextRepository.findByIndexOrTitleContainingIgnoreCase(filter, userId, pageable).map(contextDTOBuilder::buildContextDTO);
        } else if (Strings.isNotBlank(userId)){
            pageContext = contextRepository.findByLastModifiedBy(new KeycloakUserSummary(userId), pageable).map(contextDTOBuilder::buildContextDTO);
        } else {
            pageContext = contextRepository.findAll(pageable).map(contextDTOBuilder::buildContextDTO);
        }
        return PageDTOBuilder.buildContextPage(pageContext);
    }

    @Transactional(readOnly = true)
    public List<ContextDTO> searchContexts(String query, String categoryCode) {
        return contextRepository.findByTitleContainingIgnoreCaseAndCategoryLevelCategoryCodeIgnoreCase(query, categoryCode)
                .stream()
                .map(contextDTOBuilder::buildContextDTO)
                .toList();
    }
}
