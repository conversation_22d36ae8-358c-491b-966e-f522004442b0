package com.erhgo.services;

import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.userprofile.GeneralInformation;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserChannel;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.openapi.dto.UserChannelSourceDTO;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.notifier.messages.FrontofficeNotifierMessageDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.stream.Collectors;

import static java.util.Optional.ofNullable;

@Service
@RequiredArgsConstructor
public class ChannelAssignationHelper {

    private final Notifier notifier;
    private final KeycloakService keycloakService;

    public void assignOnCandidature(String organizationCode, String organizationTitle, UserProfile userProfile) {
        var channelKey = Set.of(organizationCode);
        var needsUpdates = userProfile.updatedChannels(channelKey, UserChannel.ChannelSourceType.CANDIDATURE);
        if (needsUpdates) {
            assignInKeycloakAndNotifyUserAssignation(
                    UserChannelSourceDTO.CANDIDATURE,
                    channelKey,
                    Set.of(organizationTitle),
                    userProfile
            );
        }
    }

    public void assignOnProfileInitialization(Set<Recruiter> organizations, UserChannel.ChannelSourceType source, UserProfile userProfile) {
        var channels = organizations.stream().map(Recruiter::getCode).collect(Collectors.toSet());
        var needsUpdates = userProfile.updatedChannels(channels, source);
        if (needsUpdates) {
            assignInKeycloakAndNotifyUserAssignation(
                    UserChannelSourceDTO.fromValue(source.name()),
                    channels,
                    organizations.stream().map(AbstractOrganization::getTitle).collect(Collectors.toSet()),
                    userProfile
            );
        }
    }

    private void assignInKeycloakAndNotifyUserAssignation(UserChannelSourceDTO source, Set<String> channelKeys, Set<String> channelTitles, UserProfile userProfile) {
        if (channelKeys != null && !channelKeys.isEmpty()) {
            keycloakService.assignToFrontOfficeGroups(userProfile.userId(), channelKeys);

            var generalInformation = ofNullable(userProfile.generalInformation());
            var location = generalInformation.map(GeneralInformation::getLocation);

            var message = FrontofficeNotifierMessageDTO.builderForAssignToChannel()
                    .text("Assignation d'un utilisateur à un canal :")
                    .userId(userProfile.userId())
                    .canalKey(String.join(", ", channelKeys))
                    .city(location.map(Location::getCity).orElse(""))
                    .postCode(location.map(Location::getPostcode).orElse(""))
                    .organizationNames(channelTitles)
                    .channel(source)
                    .iconEmoji(":heavy_plus_sign:")
                    .build();

            notifier.sendMessage(message);

        }
    }

}
