package com.erhgo.services;

import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.InvalidCommandException;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.QuestionForContexts;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.ContextRepository;
import com.erhgo.repositories.QuestionForContextsRepository;
import com.erhgo.repositories.RecruitmentProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.services.dtobuilder.PageDTOBuilder;
import com.erhgo.services.dtobuilder.QuestionForContextsDTOBuilder;
import com.erhgo.services.dtobuilder.RecruitmentProfileDTOBuilder;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class QuestionForContextsService {
    private final QuestionForContextsRepository questionForContextsRepository;
    private final ContextRepository contextRepository;
    private final RecruitmentProfileRepository recruitmentProfileRepository;
    private final QuestionForContextsDTOBuilder questionForContextsDTOBuilder;

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public QuestionForContexts saveContextQuestion(SaveQuestionForContextsCommandDTO command) {
        var questionForContexts = QuestionForContexts.builder()
                .uuid(command.getId())
                .contexts(getContexts(command))
                .title(command.getTitle())
                .build();
        setContextQuestionAnswers(command, questionForContexts);
        return questionForContextsRepository.save(questionForContexts);
    }

    private void setContextQuestionAnswers(SaveQuestionForContextsCommandDTO command, QuestionForContexts questionForContexts) {
        var answers = command.getSuggestedAnswers();
        if (Strings.isNullOrEmpty(answers.getNone()) && Strings.isNullOrEmpty(answers.getLow()) || Strings.isNullOrEmpty(answers.getMedium()) && Strings.isNullOrEmpty(answers.getHigh())) {
            throw new InvalidCommandException(null, "missing answers", "setContextQuestionAnswers");
        }
        questionForContexts.setTitleForNone(Strings.emptyToNull(answers.getNone()));
        questionForContexts.setTitleForLow(Strings.emptyToNull(answers.getLow()));
        questionForContexts.setTitleForMedium(Strings.emptyToNull(answers.getMedium()));
        questionForContexts.setTitleForHigh(Strings.emptyToNull(answers.getHigh()));
    }

    private Set<Context> getContexts(SaveQuestionForContextsCommandDTO command) {
        if (command.getContexts().isEmpty()) {
            return Collections.emptySet();
        }
        return Sets.newHashSet(contextRepository.findAllById(command.getContexts()));
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public List<QuestionForContextsSummaryDTO> getListContextsQuestionsByContextId(UUID contextId) {
        var context = getContext(contextId);
        return questionForContextsRepository
                .findByContextsIn(Collections.singletonList(context))
                .stream().map(QuestionForContextsDTOBuilder::buildSummary)
                .toList();
    }

    private Context getContext(UUID contextId) {
        return contextRepository.findById(contextId).orElseThrow(() -> new EntityNotFoundException(contextId, Context.class));
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public QuestionForContextsPageDTO getQuestionForContextsPage(Integer page,
                                                                 Integer size,
                                                                 String by,
                                                                 SortDirectionDTO direction,
                                                                 UUID contextId,
                                                                 Long categoryLevelId,
                                                                 String filter) {
        final var sortDirection = direction == SortDirectionDTO.ASC ? Sort.Direction.ASC : Sort.Direction.DESC;
        var safeBy = Strings.isNullOrEmpty(by) ? "id" : by;
        final Pageable pageable = PageRequest.of(page, size, sortDirection, safeBy);

        var pageQuestionForContexts = questionForContextsRepository.searchQuestionForContext(filter, contextId, categoryLevelId, pageable).map(questionForContextsDTOBuilder::buildDetails);

        return PageDTOBuilder.buildQuestionForContextsPage(pageQuestionForContexts);
    }

    @Transactional(readOnly = true)
    @RolesAllowed({Role.ODAS_ADMIN})
    public QuestionForContextsDetailsDTO getQuestionForContextsById(UUID uuid) {
        var questionForContexts = questionForContextsRepository
                .findById(uuid)
                .orElseThrow(() -> new EntityNotFoundException(uuid, QuestionForContexts.class));
        return questionForContextsDTOBuilder.buildDetails(questionForContexts);
    }

    @Transactional(readOnly = true)
    @RolesAllowed({Role.ODAS_ADMIN})
    public List<RecruitmentProfileSummaryDTO> getRecruitmentProfilesByContextQuestionAndContextId(UUID questionId, UUID contextId) {
        return recruitmentProfileRepository
                .findByContextQuestionsQuestionUuidAndContextQuestionsContextId(questionId, contextId)
                .stream().map(RecruitmentProfileDTOBuilder::buildSummary)
                .toList();
    }
}
