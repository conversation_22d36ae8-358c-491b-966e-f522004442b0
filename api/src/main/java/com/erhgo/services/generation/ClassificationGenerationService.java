package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.domain.exceptions.openai.AbstractRetryableGenerationException;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.domain.exceptions.openai.InvalidClassificationCountException;
import com.erhgo.domain.exceptions.openai.InvalidClassificationException;
import com.erhgo.openapi.dto.ErhgoClassificationDTO;
import com.erhgo.repositories.classifications.ErhgoClassificationRepository;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import com.erhgo.services.dtobuilder.ErhgoClassificationDTOBuilder;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import com.google.common.annotations.VisibleForTesting;
import jakarta.annotation.security.RolesAllowed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class ClassificationGenerationService extends AbstractGenerationService<List<ErhgoClassification>, String> {
    @VisibleForTesting
    public static Integer MIN_CLASSIFICATION = 1;
    @VisibleForTesting
    public static Integer MAX_CLASSIFICATION = 6;
    private final ErhgoClassificationRepository erhgoClassificationRepository;
    private final ErhgoClassificationDTOBuilder erhgoClassificationDTOBuilder;

    public ClassificationGenerationService(
            YamlPromptReader yamlPromptReader,
            GenerationClient generationClient,
            PromptConfig classificationPromptConfig,
            ErhgoOccupationRepository erhgoOccupationRepository,
            ErhgoClassificationRepository erhgoClassificationRepository,
            ErhgoClassificationDTOBuilder erhgoClassificationDTOBuilder,
            SecurityService securityService
    ) {
        super(yamlPromptReader, generationClient, classificationPromptConfig, erhgoOccupationRepository, securityService);
        this.erhgoClassificationDTOBuilder = erhgoClassificationDTOBuilder;
        this.erhgoClassificationRepository = erhgoClassificationRepository;
    }


    @Override
    public List<ErhgoClassification> handleResponse(String responseContent) throws AbstractRetryableGenerationException {
        var behaviorsCodes = extractCodesFromResponse(responseContent);
        return validateClassification(behaviorsCodes, responseContent);
    }

    @Transactional(noRollbackFor = {FatalGenerationException.class})
    @RolesAllowed({Role.ODAS_ADMIN, Role.CANDIDATE, Role.SOURCING})
    public OpenAIResponse<List<ErhgoClassificationDTO>> generateAndAssociateClassificationsAssociations(UUID occupationId) {
        var occupation = getOccupationOrThrow(occupationId);
        var response = generate(occupation.getTitle());
        var newClassifications = response.getResult();
        occupation.resetErhgoClassifications(newClassifications);
        erhgoOccupationRepository.save(occupation);
        return response.transform(res -> newClassifications
                .stream()
                .map(erhgoClassificationDTOBuilder::buildErhgoClassificationDTO)
                .toList());
    }

    private List<ErhgoClassification> validateClassification(List<String> classifications, String jsonResult) throws AbstractRetryableGenerationException {

        var validClassifications = erhgoClassificationRepository.findErhgoClassificationByCodeIn(classifications);
        var foundClassificationsCodes = validClassifications.stream()
                .map(ErhgoClassification::getCode)
                .toList();

        var invalidClassifications = classifications.stream()
                .filter(classificationCode -> !foundClassificationsCodes.contains(classificationCode))
                .toList();

        if (classifications.size() < MIN_CLASSIFICATION || classifications.size() > MAX_CLASSIFICATION) {
            throw new InvalidClassificationCountException(MIN_CLASSIFICATION, MAX_CLASSIFICATION, classifications.size(), jsonResult);
        }

        if (!invalidClassifications.isEmpty()) {
            throw new InvalidClassificationException(String.join(", ", invalidClassifications), jsonResult);
        }
        return validClassifications;
    }


}
