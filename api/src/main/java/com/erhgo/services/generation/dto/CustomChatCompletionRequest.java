package com.erhgo.services.generation.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.openai.OpenAiChatOptions;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomChatCompletionRequest extends Prompt {
    public static class JsonObjectMarker {
        @JsonProperty("type")
        public String type = "json_object";
    }

    // Used to force JSON completion
    @SuppressWarnings("unused")
    @JsonProperty("response_format")
    public JsonObjectMarker responseFormat;

    @SuppressWarnings("unused")
    @JsonProperty("seed")
    public Long seed;

    @lombok.Builder(builderMethodName = "customBuilder")
    public CustomChatCompletionRequest(OpenAiChatOptions options, List<Message> messages, boolean gpt4JsonMode) {
        super(messages, options);
        if (gpt4JsonMode) {
            responseFormat = new JsonObjectMarker();
            seed = 242242548457115L;
        }
    }
}
