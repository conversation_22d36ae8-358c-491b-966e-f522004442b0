package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.enums.HardSkillType;
import com.erhgo.domain.exceptions.openai.AbstractRetryableGenerationException;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.domain.exceptions.openai.InvalidUserExperienceException;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class HardSkillsFromCVGenerationService extends AbstractGenerationService<Map<HardSkillType, String>, String> {
    public HardSkillsFromCVGenerationService(YamlPromptReader yamlPromptReader, GenerationClient generationClient, PromptConfig hardSkillsPromptConfig, ErhgoOccupationRepository erhgoOccupationRepository, SecurityService securityService) {
        super(yamlPromptReader, generationClient, hardSkillsPromptConfig, erhgoOccupationRepository, securityService);
    }

    public OpenAIResponse<Map<HardSkillType, String>> generateHardSkills(String content) throws FatalGenerationException {
        return generate(content);
    }

    @Override
    protected Map<HardSkillType, String> handleResponse(String jsonResult) throws AbstractRetryableGenerationException {
        try {
            return objectMapper.readValue(jsonResult, new TypeReference<>() {
            });
        } catch (JsonProcessingException e) {
            log.warn("Invalid json produced {}", jsonResult, e);
            throw new InvalidUserExperienceException(jsonResult, e);
        }
    }
}
