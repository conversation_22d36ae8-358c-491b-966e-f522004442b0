package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.exceptions.openai.*;
import com.erhgo.domain.referential.Activity;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.openapi.dto.OccupationActivityDTO;
import com.erhgo.repositories.ActivityRepository;
import com.erhgo.repositories.CapacityRepository;
import com.erhgo.repositories.JobActivityLabelRepository;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import com.erhgo.services.denormalization.ErhgoOccupationUpdateListener;
import com.erhgo.services.dtobuilder.ErhgoOccupationDataDTOBuilder;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ActivityForCapacityModel;
import com.erhgo.services.generation.dto.ActivityGenerationResponse;
import com.erhgo.services.generation.dto.GenerationRetryState;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.generation.prompt.YamlActivityGenerationPromptModel;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.annotations.VisibleForTesting;
import jakarta.annotation.security.RolesAllowed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ActivityGenerationService extends AbstractGenerationService<List<ActivityForCapacityModel>, UUID> {
    public static final int SAMPLE_NUMBER = 3;

    private final List<ErhgoOccupationUpdateListener> erhgoOccupationUpdateListeners;
    private final CapacityRepository capacityRepository;
    private final ActivityRepository activityRepository;
    private final JobActivityLabelRepository jobActivityLabelRepository;
    private final ErhgoOccupationDataDTOBuilder erhgoOccupationDataDTOBuilder;

    @VisibleForTesting
    private static Integer MINIMUM_TOLERABLE_NUMBER_OF_CAPACITIES = 16;
    @VisibleForTesting
    private static Integer MAXIMUM_TOLERABLE_NUMBER_OF_CAPACITIES = 33;
    @VisibleForTesting
    private static Integer MINIMUM_TOLERABLE_NUMBER_OF_ACTIVITIES = 5;
    @VisibleForTesting
    private static Integer MAXIMUM_TOLERABLE_NUMBER_OF_ACTIVITIES = 16;

    public ActivityGenerationService(
            YamlPromptReader yamlPromptReader,
            GenerationClient generationClient,
            PromptConfig activityPromptConfig,
            ErhgoOccupationRepository erhgoOccupationRepository,
            List<ErhgoOccupationUpdateListener> erhgoOccupationUpdateListeners,
            CapacityRepository capacityRepository,
            ActivityRepository activityRepository,
            JobActivityLabelRepository jobActivityLabelRepository,
            ErhgoOccupationDataDTOBuilder erhgoOccupationDataDTOBuilder,
            SecurityService securityService
    ) {
        super(yamlPromptReader, generationClient, activityPromptConfig, erhgoOccupationRepository, securityService);
        this.erhgoOccupationUpdateListeners = erhgoOccupationUpdateListeners;
        this.capacityRepository = capacityRepository;
        this.activityRepository = activityRepository;
        this.jobActivityLabelRepository = jobActivityLabelRepository;
        this.erhgoOccupationDataDTOBuilder = erhgoOccupationDataDTOBuilder;
    }


    @Override
    protected List<Message> getChatMessages(UUID uuid) {
        var data = yamlPromptReader.readYamlDataForActivity(promptConfig.getMessageFilename());
        var prompt = new ArrayList<>(data.getMessages());

        var formattedLastPrompt = buildLastPrompt(uuid, data);

        prompt.add(new UserMessage(formattedLastPrompt));
        return prompt;
    }

    private String buildLastPrompt(UUID uuid, YamlActivityGenerationPromptModel data) {
        var occupation = getOccupationOrThrow(uuid);
        var codeRomes = occupation.getRomeOccupations().stream().map(rome -> " %s (%s)".formatted(rome.getCode(), rome.getTitle())).collect(Collectors.joining(", "));
        var samples = erhgoOccupationRepository.findByRomeOfErhgoOccupationsRomeOccupationIn(occupation.getRomeOccupations(), PageRequest.of(0, SAMPLE_NUMBER)).stream().map(ErhgoOccupation::getTitle).collect(Collectors.joining(", "));
        var formattedLastPrompt = "";
        var title = occupation.getTitle();

        if (codeRomes.isEmpty()) {
            formattedLastPrompt = data.getParameterizedLastPromptMessageWithoutRome().formatted(title);
        } else if (!samples.isEmpty()) {
            formattedLastPrompt = data.getParameterizedLastPromptMessageWithRomeAndOccupationAssociated().formatted(title, codeRomes, samples);
        } else {
            formattedLastPrompt = data.getParameterizedLastPromptMessageWithRomeAndNoOccupationAssociated().formatted(title, codeRomes);
        }
        return formattedLastPrompt;
    }

    @Override
    protected List<ActivityForCapacityModel> handleResponse(String jsonResult) throws AbstractRetryableGenerationException {
        try {
            var response = objectMapper.readValue(jsonResult, ActivityGenerationResponse.class);
            var activities = response.getActivitiesDetail();
            validateActivities(jsonResult, activities == null ? new ArrayList<>() : activities);
            return activities;
        } catch (JsonProcessingException e) {
            log.warn("Failed to parse OpenAI response {} - this is NOT json ; check rome codes and occupation title", jsonResult, e);
            throw new InvalidGeneratedJsonException(jsonResult, e);
        }
    }

    @VisibleForTesting
    public ErhgoOccupation associateActivitiesToOccupation(UUID occupationId, List<ActivityForCapacityModel> activities) {
        var occupation = getOccupationOrThrow(occupationId);
        var oldActivities = occupation.getOccupationActivities();
        var newActivities = activities.stream().map(activityDetail -> {
            var activity = new Activity();
            activity.setDescription(activityDetail.getTitle());
            var capacities = new HashSet<>(capacityRepository.findByCodeIn(activityDetail.getCapacities()));
            activity.setInducedCapacities(capacities);
            activity = activityRepository.save(activity);
            return jobActivityLabelRepository.save(JobActivityLabel.builder().activity(activity).title(activityDetail.getTitle()).build());
        }).toList();

        if (oldActivities != null && !oldActivities.isEmpty()) {
            occupation.clearAllActivities();
        }
        occupation.addActivities(newActivities);
        return erhgoOccupationRepository.save(occupation);
    }

    @Transactional(noRollbackFor = {FatalGenerationException.class})
    @RolesAllowed({Role.ODAS_ADMIN, Role.CANDIDATE, Role.SOURCING})
    public OpenAIResponse<List<OccupationActivityDTO>> qualifyAndPersistOccupationActivities(UUID occupationId) {
        var initialActivities = new HashSet<>(getOccupationOrThrow(occupationId).getActivities());
        var report = generate(occupationId);
        var occupation = associateActivitiesToOccupation(occupationId, report.getResult());
        erhgoOccupationUpdateListeners.forEach(l -> l.notifyActivitiesReplaced(
                occupation,
                initialActivities,
                new HashSet<>(occupation.getActivities()))
        );
        return report.transform(res -> occupation.getOccupationActivities().stream().map(erhgoOccupationDataDTOBuilder::createActivityDTO).toList());
    }


    private void validateActivities(String jsonResult, List<ActivityForCapacityModel> activities) throws AbstractRetryableGenerationException {
        List<String> allCapacityCodes = activities.stream()
                .flatMap(activity -> activity.getCapacities().stream())
                .distinct()
                .toList();

        List<String> existingCapacityCodes = capacityRepository.findByCodeIn(allCapacityCodes)
                .stream()
                .map(Capacity::getCode)
                .toList();

        List<String> invalidCodes = allCapacityCodes.stream()
                .filter(code -> !existingCapacityCodes.contains(code))
                .toList();

        if (!invalidCodes.isEmpty()) {
            throw new InvalidCapacityCodesException(String.join(", ", invalidCodes), jsonResult);
        }

        long distinctCapacitiesCount = allCapacityCodes.size();
        if (distinctCapacitiesCount < MINIMUM_TOLERABLE_NUMBER_OF_CAPACITIES || distinctCapacitiesCount > MAXIMUM_TOLERABLE_NUMBER_OF_CAPACITIES) {
            throw new InvalidCapacityCountException(distinctCapacitiesCount, MINIMUM_TOLERABLE_NUMBER_OF_CAPACITIES, MAXIMUM_TOLERABLE_NUMBER_OF_CAPACITIES, jsonResult);
        }

        int activitiesCount = activities.size();
        if (activitiesCount < MINIMUM_TOLERABLE_NUMBER_OF_ACTIVITIES || activitiesCount > MAXIMUM_TOLERABLE_NUMBER_OF_ACTIVITIES) {
            throw new InvalidActivityCountException(activitiesCount, MINIMUM_TOLERABLE_NUMBER_OF_ACTIVITIES, MAXIMUM_TOLERABLE_NUMBER_OF_ACTIVITIES, jsonResult);
        }
    }

    @Override
    protected String computePromptAdjust(GenerationRetryState retryState, int nbTry, UUID uuid) {
        var result = new StringBuilder(retryState.getErrorMessage()).append(", ");
        var data = yamlPromptReader.readYamlDataForActivity(promptConfig.getMessageFilename());
        if (nbTry == 1) {
            result.append("recommence en respectant toutes les consignes et sans faire de commentaire : ");
        } else {
            result.append(data.getComputePromptAdjust());
        }
        result.append(buildLastPrompt(uuid, data));
        return result.toString();
    }

}
