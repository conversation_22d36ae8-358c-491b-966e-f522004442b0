package com.erhgo.services.generation.dto;

import com.erhgo.domain.enums.ExperienceType;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;

import java.time.LocalDate;

@Data
public class UserExperienceExtractionResponse {
    private String title;
    private String organizationTitle;
    @JsonDeserialize(using = SafeEnumDeserializer.class)
    private ExperienceType type;
    @JsonDeserialize(using = SafeIntegerDeserializer.class)
    private Integer durationInMonths;
    @JsonDeserialize(using = SafeLocalDateDeserializer.class)
    private LocalDate startDate;
    @JsonDeserialize(using = SafeLocalDateDeserializer.class)
    private LocalDate endDate;
}
