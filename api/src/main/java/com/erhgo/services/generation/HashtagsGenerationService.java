package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.enums.HardSkillType;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.openai.AbstractRetryableGenerationException;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.domain.exceptions.openai.GenericRetryableGenerationException;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.security.AuthorizeExpression;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import com.google.common.annotations.VisibleForTesting;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class HashtagsGenerationService extends AbstractGenerationService<Set<String>, String> {
    private static final int HASHTAGS_COUNT = 12;
    private static final int MAX_ACTIVITIES = 60;
    private static final int MAX_HASHTAG_LENGTH = 20;
    private final UserProfileRepository userProfileRepository;

    public HashtagsGenerationService(YamlPromptReader yamlPromptReader, GenerationClient generationClient, PromptConfig hashtagsPromptConfig, ErhgoOccupationRepository erhgoOccupationRepository, UserProfileRepository userProfileRepository, SecurityService securityService) {
        super(yamlPromptReader, generationClient, hashtagsPromptConfig, erhgoOccupationRepository, securityService);
        this.userProfileRepository = userProfileRepository;
    }

    @Override
    protected Set<String> handleResponse(String content) throws AbstractRetryableGenerationException {
        if (StringUtils.isNotBlank(content)) {
            var result = Stream.of(content.split("(?=[#\\s\\n])"))
                    .map(String::trim)
                    .filter(this::isValidHashtag)
                    .collect(Collectors.toSet());
            if (!result.isEmpty()) {
                return result;
            }
        }
        throw new GenericRetryableGenerationException("Contenu vide ou sans mot clé", content);
    }

    private boolean isValidHashtag(String s) {
        return s != null && s.startsWith("#");
    }

    @VisibleForTesting
    protected List<String> generateHashtags(List<String> activitiesLabels) {
        if (activitiesLabels == null || activitiesLabels.isEmpty() || activitiesLabels.size() > MAX_ACTIVITIES) {
            throw new FatalGenerationException("Activities should not be empty or higher than %d".formatted(MAX_ACTIVITIES));
        }
        OpenAIResponse<Set<String>> result = null;
        try {
            result = generate(String.join("\n", activitiesLabels));
            return limitGlobalLengthOfHashtags(result.getResult());
        } catch (FatalGenerationException e) {
            result = e.getResponse();
            throw e;
        } finally {
            if (result != null) {
                log.info("Hashtag generation cost: model=%s".formatted(result.getModel()));
            }
        }
    }

    private static @NotNull List<String> limitGlobalLengthOfHashtags(Set<String> generated) {
        var tooFatCounter = new AtomicInteger(0);
        var filtered = generated.stream().distinct().sorted(Comparator.comparing(String::length)).filter(a -> {
            if (a.length() > MAX_HASHTAG_LENGTH) {
                return tooFatCounter.incrementAndGet() <= 3;
            }
            return true;
        }).limit(HASHTAGS_COUNT).toList();
        log.debug("Hashtags generated: {}, using for length reason: {}", generated, filtered);
        return filtered;
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_READ)
    public List<String> getHashtagsOrGenerate(String userId) {
        var user = userProfileRepository.findByUserId(userId).orElseThrow(() -> new EntityNotFoundException(userId, UserProfile.class));
        if (!user.hashtags().isEmpty()) {
            return limitGlobalLengthOfHashtags(user.hashtags());
        }
        var activities = user.getAllActivities().stream().map(JobActivityLabel::getTitle).distinct().limit(MAX_ACTIVITIES).collect(Collectors.joining(" ; "));
        log.debug("Let's generate hashtags for {}, with {} activities", userId, activities.split(";").length);
        if (activities.isEmpty()) {
            log.warn("No experience for user {} - unable to generate hashtags", userId);
            return Collections.emptyList();
        }

        var param = buildActivitiesLabels(activities, user);

        var generatedHashtags = generateHashtags(param);
        user.updateHashtags(generatedHashtags);
        return generatedHashtags;
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_WRITE)
    public List<String> regenerateHashtags(String userId, List<String> selectedHashtags, List<String> deselectedHashtags) {
        log.debug("Regenerating hashtags for user {} with {} selected hashtags and {} deselected hashtags (target: {} total)",
                userId, selectedHashtags.size(), deselectedHashtags.size(), HASHTAGS_COUNT);

        var user = userProfileRepository.findByUserId(userId).orElseThrow(() -> new EntityNotFoundException(userId, UserProfile.class));

        int hashtagsToGenerate = HASHTAGS_COUNT - selectedHashtags.size();
        log.debug("Need to generate {} new hashtags to reach target of {} total", hashtagsToGenerate, HASHTAGS_COUNT);

        if (hashtagsToGenerate <= 0) {
            log.debug("No new hashtags needed for user {} (already has {} hashtags), returning selected hashtags", userId, selectedHashtags.size());
            user.updateHashtags(selectedHashtags);
            return new ArrayList<>(selectedHashtags);
        }

        validateHashtags(selectedHashtags, deselectedHashtags);

        var activities = user.getAllActivities().stream().map(JobActivityLabel::getTitle).distinct().limit(MAX_ACTIVITIES).collect(Collectors.joining(" ; "));
        if (activities.isEmpty()) {
            log.warn("No experience for user {} - unable to generate new hashtags", userId);
            user.updateHashtags(selectedHashtags);
            return new ArrayList<>(selectedHashtags);
        }

        var param = buildActivitiesLabels(activities, user);

        if (!selectedHashtags.isEmpty()) {
            param.add("Hashtags à éviter (déjà sélectionnés) : ");
            param.add(String.join(" ", selectedHashtags));
        }

        param.add("Hashtags à éviter (explicitement rejetés) : ");
        param.add(String.join(" ", deselectedHashtags));

        var allGeneratedHashtags = generateHashtags(param);

        var selectedHashtagsLower = selectedHashtags.stream()
                .map(String::toLowerCase)
                .collect(Collectors.toSet());

        var deselectedHashtagsLower = deselectedHashtags.stream()
                .map(String::toLowerCase)
                .collect(Collectors.toSet());

        var newHashtags = allGeneratedHashtags.stream()
                .filter(hashtag -> !selectedHashtagsLower.contains(hashtag.toLowerCase()))
                .filter(hashtag -> !deselectedHashtagsLower.contains(hashtag.toLowerCase()))
                .limit(hashtagsToGenerate)
                .toList();

        var finalHashtags = new ArrayList<>(selectedHashtags);
        finalHashtags.addAll(newHashtags);

        log.info("Successfully regenerated hashtags for user {}: {} selected + {} new = {} total (target: {}, avoided {} deselected)",
                userId, selectedHashtags.size(), newHashtags.size(), finalHashtags.size(), HASHTAGS_COUNT, deselectedHashtags.size());

        user.updateHashtags(finalHashtags);
        return finalHashtags;
    }

    private static ArrayList<String> buildActivitiesLabels(String activities, UserProfile user) {
        var param = new ArrayList<String>();
        param.add("Activités : ");
        param.add(activities);
        var knowledge = Optional.ofNullable(StringUtils.trimToNull(user.getHardSkills().get(HardSkillType.KNOWLEDGE)));
        if (knowledge.isPresent()) {
            param.add("Connaissances : ");
            param.add(knowledge.get());
            log.debug("Using knowledge for hashtag regeneration: {}", knowledge.get());
        }
        return param;
    }

    private void validateHashtags(List<String> selectedHashtags, List<String> deselectedHashtags) {
        var totalSelectedAndDeselected = selectedHashtags.size() + deselectedHashtags.size();
        if (totalSelectedAndDeselected < 8 || totalSelectedAndDeselected > HASHTAGS_COUNT) {
            throw new FatalGenerationException("Total selected and deselected hashtags must be between 8 and 12 items, got: " + totalSelectedAndDeselected);
        }

        if (!selectedHashtags.isEmpty()) {
            validateHashtagList(selectedHashtags, "Selected");
        }

        if (!deselectedHashtags.isEmpty()) {
            validateHashtagList(deselectedHashtags, "Deselected");
        }
    }

    private void validateHashtagList(List<String> hashtags, String listType) {
        hashtags.stream()
                .filter(hashtag -> !isValidHashtag(hashtag))
                .findFirst()
                .ifPresent(hashtag -> {
                    throw new FatalGenerationException("Invalid " + listType.toLowerCase() + " hashtag format: " + hashtag + ". Hashtags must start with # and be 2-20 characters total");
                });

        hashtags.stream()
                .filter(hashtag -> hashtag.length() > MAX_HASHTAG_LENGTH)
                .findFirst()
                .ifPresent(hashtag -> {
                    throw new FatalGenerationException(listType + " hashtag too long: " + hashtag + ". Maximum length is " + MAX_HASHTAG_LENGTH + " characters");
                });

        var uniqueHashtags = hashtags.stream()
                .map(String::toLowerCase)
                .collect(Collectors.toSet());

        if (uniqueHashtags.size() != hashtags.size()) {
            throw new FatalGenerationException(listType + " hashtags contain duplicates");
        }
    }
}

