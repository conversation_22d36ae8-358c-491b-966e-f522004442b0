package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.exceptions.openai.AbstractRetryableGenerationException;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.domain.exceptions.openai.InvalidUserInfosException;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.generation.dto.UserInfosExtractionResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UserInfosFromCVGenerationService extends AbstractGenerationService<UserInfosExtractionResponse, String> {
    public UserInfosFromCVGenerationService(YamlPromptReader yamlPromptReader, GenerationClient generationClient, PromptConfig userInfosPromptConfig, ErhgoOccupationRepository erhgoOccupationRepository, SecurityService securityService) {
        super(yamlPromptReader, generationClient, userInfosPromptConfig, erhgoOccupationRepository, securityService);
    }

    public OpenAIResponse<UserInfosExtractionResponse> generateUserInfos(String content) throws FatalGenerationException {
        return generate(content);
    }

    @Override
    protected UserInfosExtractionResponse handleResponse(String jsonResult) throws AbstractRetryableGenerationException {
        try {
            return objectMapper.readValue(jsonResult, UserInfosExtractionResponse.class);
        } catch (JsonProcessingException e) {
            log.warn("Invalid json produced {}", jsonResult, e);
            throw new InvalidUserInfosException(jsonResult, e);
        }
    }
}
