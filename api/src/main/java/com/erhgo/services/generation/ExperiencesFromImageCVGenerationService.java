package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.enums.ExperienceType;
import com.erhgo.domain.exceptions.openai.AbstractRetryableGenerationException;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.domain.exceptions.openai.InvalidUserExperienceException;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ArrayWrapper;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.generation.dto.UserExperienceExtractionResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import com.erhgo.utils.dto.ImageFileData;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.content.Media;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ExperiencesFromImageCVGenerationService extends AbstractGenerationService<List<UserExperienceExtractionResponse>, Media> {
    public ExperiencesFromImageCVGenerationService(YamlPromptReader yamlPromptReader, GenerationClient generationClient, PromptConfig userExperiencesVisionPromptConfig, ErhgoOccupationRepository erhgoOccupationRepository, SecurityService securityService) {
        super(yamlPromptReader, generationClient, userExperiencesVisionPromptConfig, erhgoOccupationRepository, securityService);
    }

    public OpenAIResponse<List<UserExperienceExtractionResponse>> generateUserExperiences(ImageFileData mediaFile) throws FatalGenerationException {
        var media = Media.builder()
                .data(mediaFile.getBytes())
                .mimeType(mediaFile.getContentType())
                .build();

        return generate(media);
    }

    @Override
    protected List<UserExperienceExtractionResponse> handleResponse(String jsonResult) throws AbstractRetryableGenerationException {
        try {
            var dtoRoot = objectMapper.readValue(jsonResult, new TypeReference<ArrayWrapper<UserExperienceExtractionResponse>>() {
            });
            var dto = dtoRoot.getResult();
            if (dto == null || dto.stream().peek(x -> {
                if (x.getType() == null) {
                    x.setType(ExperienceType.JOB);
                }
            }).anyMatch(x -> StringUtils.isBlank(x.getTitle()))) {
                throw new InvalidUserExperienceException(jsonResult);
            }
            return dto;
        } catch (JsonProcessingException e) {
            log.warn("Invalid json produced {}", jsonResult, e);
            throw new InvalidUserExperienceException(jsonResult, e);
        }
    }
}
