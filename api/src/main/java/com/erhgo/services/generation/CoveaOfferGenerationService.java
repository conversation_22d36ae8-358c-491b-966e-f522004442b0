package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.exceptions.openai.AbstractRetryableGenerationException;
import com.erhgo.domain.exceptions.openai.GenericRetryableGenerationException;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.security.AuthorizeExpression;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

@Service
@Slf4j
public class CoveaOfferGenerationService extends AbstractGenerationService<Map<String, String>, String> {

    public CoveaOfferGenerationService(YamlPromptReader yamlPromptReader, GenerationClient generationClient, PromptConfig hashtagsPromptConfig, ErhgoOccupationRepository erhgoOccupationRepository, UserProfileRepository userProfileRepository, SecurityService securityService) {
        super(yamlPromptReader, generationClient, hashtagsPromptConfig, erhgoOccupationRepository, securityService);
    }

    @Override
    protected Map<String, String> handleResponse(String jsonResult) throws AbstractRetryableGenerationException {
        try {
            return objectMapper.readValue(jsonResult, new TypeReference<>() {
            });
        } catch (JsonProcessingException e) {
            log.warn("Invalid json produced {}", jsonResult, e);
            throw new GenericRetryableGenerationException(jsonResult, "Bad json", e);
        }
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_READ)
    public Map<String, String> cleanupOffer(String offer) {
        return generate(offer).getResult();
    }

}

