package com.erhgo.services.generation.client;

import com.erhgo.config.PromptConfig;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Service;

@Slf4j
@ConditionalOnExpression("'${spring.ai.openai.api-key}'.isEmpty()")
@Service
public class MockedGenerationClient implements GenerationClient {
    @Override
    public ChatCompletionResponse createChatCompletion(Prompt prompt, PromptConfig promptConfig) {
        log.error("No generation client configured");
        return new ChatCompletionResponse("No generation client configured", null);
    }
}
