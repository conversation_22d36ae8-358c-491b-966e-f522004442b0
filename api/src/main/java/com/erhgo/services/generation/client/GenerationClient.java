package com.erhgo.services.generation.client;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import org.springframework.ai.chat.prompt.Prompt;

public interface GenerationClient {
    String STOP_REASON = "stop";

    ChatCompletionResponse createChatCompletion(Prompt prompt, PromptConfig promptConfig) throws FatalGenerationException;
}
