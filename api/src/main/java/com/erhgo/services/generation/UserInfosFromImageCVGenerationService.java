package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.exceptions.openai.AbstractRetryableGenerationException;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.domain.exceptions.openai.InvalidUserInfosException;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.generation.dto.UserInfosExtractionResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import com.erhgo.utils.dto.ImageFileData;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.content.Media;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UserInfosFromImageCVGenerationService extends AbstractGenerationService<UserInfosExtractionResponse, Media> {
    public UserInfosFromImageCVGenerationService(YamlPromptReader yamlPromptReader, GenerationClient generationClient, PromptConfig userInfosVisionPromptConfig, ErhgoOccupationRepository erhgoOccupationRepository, SecurityService securityService) {
        super(yamlPromptReader, generationClient, userInfosVisionPromptConfig, erhgoOccupationRepository, securityService);
    }

    public OpenAIResponse<UserInfosExtractionResponse> generateUserInfos(ImageFileData mediaFile) throws FatalGenerationException {
        var media = Media.builder()
                .data(mediaFile.getBytes())
                .mimeType(mediaFile.getContentType())
                .build();

        return generate(media);
    }

    @Override
    protected UserInfosExtractionResponse handleResponse(String jsonResult) throws AbstractRetryableGenerationException {
        try {
            return objectMapper.readValue(jsonResult, UserInfosExtractionResponse.class);
        } catch (JsonProcessingException e) {
            log.warn("Invalid json produced {}", jsonResult, e);
            throw new InvalidUserInfosException(jsonResult, e);
        }
    }
}
