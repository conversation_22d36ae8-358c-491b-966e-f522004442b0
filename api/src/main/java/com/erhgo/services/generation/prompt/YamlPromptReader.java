package com.erhgo.services.generation.prompt;

import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Slf4j
@Service
public class YamlPromptReader {
    private final ResourceLoader resourceLoader;
    private final ObjectMapper yamlObjectMapper;

    public static final String GENERATION_ROOT_PATH = "classpath:generation/";

    public YamlPromptReader(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
        this.yamlObjectMapper = new ObjectMapper(new YAMLFactory()).configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }


    public YamlActivityGenerationPromptModel readYamlDataForActivity(String yamlFileName) {
        return getYamlActivityGenerationPromptModel(yamlFileName, YamlActivityGenerationPromptModel.class);
    }

    private <A> A getYamlActivityGenerationPromptModel(String yamlFileName, Class<A> valueType) {
        try {
            var resource = resourceLoader.getResource(GENERATION_ROOT_PATH + yamlFileName);
            return yamlObjectMapper.readValue(resource.getInputStream(), valueType);
        } catch (IOException e) {
            var logMessage = "Erreur lors de la lecture du fichier YAML: %s".formatted(yamlFileName);
            log.error(logMessage, e);
            throw new FatalGenerationException(logMessage, e);
        }
    }

    public YamlGenericGenerationPromptModel readYamlDataGeneric(String yamlFileName) {
        return getYamlActivityGenerationPromptModel(yamlFileName, YamlGenericGenerationPromptModel.class);
    }

    public YamlGenericGenerationPromptModel readYamlDataGenericFromContent(String promptMessages) {
        try {
            return yamlObjectMapper.readValue(promptMessages, YamlGenericGenerationPromptModel.class);
        } catch (JsonProcessingException e) {
            throw new FatalGenerationException("invalid yaml", e);
        }
    }
}
