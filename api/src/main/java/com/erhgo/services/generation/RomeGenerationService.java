package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.classifications.rome.RomeOccupation;
import com.erhgo.domain.exceptions.openai.AbstractRetryableGenerationException;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.domain.exceptions.openai.InvalidRomeCountException;
import com.erhgo.domain.exceptions.openai.InvalidRomeException;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.repositories.classifications.RomeOccupationRepository;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import com.google.common.annotations.VisibleForTesting;
import jakarta.annotation.security.RolesAllowed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class RomeGenerationService extends AbstractGenerationService<List<RomeOccupation>, String> {

    @VisibleForTesting
    public static int MAX_ROME = 4;

    private final RomeOccupationRepository romeOccupationRepository;

    public RomeGenerationService(
            YamlPromptReader yamlPromptReader,
            GenerationClient generationClient,
            PromptConfig romePromptConfig,
            RomeOccupationRepository romeOccupationRepository,
            ErhgoOccupationRepository erhgoOccupationRepository,
            SecurityService securityService
    ) {
        super(yamlPromptReader, generationClient, romePromptConfig, erhgoOccupationRepository, securityService);
        this.romeOccupationRepository = romeOccupationRepository;
    }


    @Override
    public List<RomeOccupation> handleResponse(String responseContent) throws AbstractRetryableGenerationException {
        return validateRome(extractCodesFromResponse(responseContent), responseContent);

    }

    @Transactional(noRollbackFor = {FatalGenerationException.class})
    @RolesAllowed({Role.ODAS_ADMIN, Role.CANDIDATE, Role.SOURCING})
    public OpenAIResponse<List<RomeOccupation>> generateRomeAssociations(UUID occupationId) {
        var occupation = getOccupationOrThrow(occupationId);
        var response = generate(occupation.getTitle());
        if (response.getResult().isEmpty()) {
            log.error("No ROME code found for {} - living empty, probably wrong occupation title:", occupation.getTitle());
        }
        occupation.resetRomeOccupations(response.getResult());
        erhgoOccupationRepository.save(occupation);
        return response;
    }

    private List<RomeOccupation> validateRome(List<String> romeCodes, String jsonResult) throws AbstractRetryableGenerationException {
        var validRomes = romeOccupationRepository.findAllByCodeIn(romeCodes);
        var foundRomeCodes = validRomes.stream()
                .map(RomeOccupation::getCode)
                .toList();

        var invalidRomeCodes = romeCodes.stream()
                .filter(romeCode -> !foundRomeCodes.contains(romeCode))
                .toList();

        if (romeCodes.size() > MAX_ROME) {
            throw new InvalidRomeCountException(romeCodes.size(), MAX_ROME, jsonResult);
        }

        if (!invalidRomeCodes.isEmpty()) {
            throw new InvalidRomeException(String.join(", ", invalidRomeCodes), jsonResult);
        }

        return validRomes;
    }
}
