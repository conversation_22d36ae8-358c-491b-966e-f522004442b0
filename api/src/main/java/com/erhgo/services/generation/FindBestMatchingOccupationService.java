package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.openai.AbstractRetryableGenerationException;
import com.erhgo.openapi.dto.ErhgoOccupationMinimumInfoDTO;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import com.erhgo.services.dtobuilder.ErhgoOccupationDataDTOBuilder;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.FindBestMatchingOccupationArguments;
import com.erhgo.services.generation.dto.FindBestMatchingOccupationResponse;
import com.erhgo.services.generation.dto.GenerationRetryState;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import com.erhgo.services.search.ErhgoOccupationFinder;
import com.fasterxml.jackson.core.JsonProcessingException;
import jakarta.annotation.security.RolesAllowed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FindBestMatchingOccupationService extends AbstractGenerationService<ErhgoOccupationMinimumInfoDTO, FindBestMatchingOccupationArguments> {
    private final ErhgoOccupationDataDTOBuilder erhgoDataDTOBuilder;
    private final ErhgoOccupationFinder erhgoOccupationFinder;


    public FindBestMatchingOccupationService(
            YamlPromptReader yamlPromptReader,
            GenerationClient generationClient,
            PromptConfig bestMatchingOccupationPromptConfig,
            ErhgoOccupationRepository erhgoOccupationRepository,
            ErhgoOccupationDataDTOBuilder erhgoDataDTOBuilder,
            ErhgoOccupationFinder erhgoOccupationFinder,
            SecurityService securityService
    ) {
        super(yamlPromptReader, generationClient, bestMatchingOccupationPromptConfig, erhgoOccupationRepository, securityService);
        this.erhgoDataDTOBuilder = erhgoDataDTOBuilder;
        this.erhgoOccupationFinder = erhgoOccupationFinder;
    }

    @Override
    protected List<Message> getChatMessages(FindBestMatchingOccupationArguments args) {
        var data = yamlPromptReader.readYamlDataGeneric(promptConfig.getMessageFilename());
        var prompt = new ArrayList<>(data.getMessages());

        var similarOccupations = args.getSimilarOccupationsList().stream()
                .map(occupation -> "%s(%s)".formatted(occupation.getTitle(), occupation.getCode()))
                .collect(Collectors.joining(", "));

        prompt.add(new UserMessage(data.getParameterizedLastPromptMessage().formatted(args.getNewLabel(), similarOccupations)));
        return prompt;
    }


    @Override
    public ErhgoOccupationMinimumInfoDTO handleResponse(String responseContent) throws AbstractRetryableGenerationException {
        return validateLabel(responseContent);
    }

    private ErhgoOccupationMinimumInfoDTO validateLabel(String jsonResult) throws AbstractRetryableGenerationException {
        try {
            var dto = objectMapper.readValue(jsonResult, FindBestMatchingOccupationResponse.class);
            return Optional
                    .ofNullable(dto.getUuid())
                    .map(UUID::fromString)
                    .map(this::getOrThrow)
                    .map(erhgoDataDTOBuilder::buildOccupationMininumInfo)
                    .orElse(new ErhgoOccupationMinimumInfoDTO());
        } catch (JsonProcessingException | EntityNotFoundException e) {
            log.warn("Invalid json produced {}", jsonResult, e);
            return new ErhgoOccupationMinimumInfoDTO();
        }
    }

    private ErhgoOccupation getOrThrow(UUID uuid) {
        return erhgoOccupationRepository.findById(uuid).orElseThrow(() -> new EntityNotFoundException(uuid, ErhgoOccupation.class));
    }

    @RolesAllowed({Role.ODAS_ADMIN, Role.CANDIDATE, Role.SOURCING})
    @Transactional(readOnly = true)
    public ErhgoOccupationMinimumInfoDTO findSimilarLabel(FindBestMatchingOccupationArguments args) {
        var bestMatchingOccupations = erhgoOccupationFinder.searchOccupations(args.getNewLabel(), false);
        if (!bestMatchingOccupations.isEmpty()) {
            args.setSimilarOccupationsList(bestMatchingOccupations);
            return generate(args).getResult();
        }
        return new ErhgoOccupationMinimumInfoDTO();

    }

    @Override
    protected String computePromptAdjust(GenerationRetryState retryState, int nbTry, FindBestMatchingOccupationArguments args) {
        var result = new StringBuilder(retryState.getErrorMessage()).append(", ");
        var data = yamlPromptReader.readYamlDataForActivity(promptConfig.getMessageFilename());
        if (nbTry == 1) {
            result.append("recommence en respectant toutes les consignes et sans faire de commentaire : ");
        } else {
            result.append(data.getComputePromptAdjust());
        }
        result.append(data.getComputePromptAdjust().formatted(args.getNewLabel(), args.getSimilarOccupationsList()));
        return result.toString();
    }

}
