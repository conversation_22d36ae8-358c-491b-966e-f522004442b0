package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.enums.HardSkillType;
import com.erhgo.domain.exceptions.openai.AbstractRetryableGenerationException;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.domain.exceptions.openai.InvalidUserExperienceException;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import com.erhgo.utils.dto.ImageFileData;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.content.Media;
import org.springframework.stereotype.Service;
import java.util.Map;

@Slf4j
@Service
public class HardSkillsFromImageCVGenerationService extends AbstractGenerationService<Map<HardSkillType, String>, Media> {
    public HardSkillsFromImageCVGenerationService(YamlPromptReader yamlPromptReader, GenerationClient generationClient, PromptConfig hardSkillsVisionPromptConfig, ErhgoOccupationRepository erhgoOccupationRepository, SecurityService securityService) {
        super(yamlPromptReader, generationClient, hardSkillsVisionPromptConfig, erhgoOccupationRepository, securityService);
    }

    public OpenAIResponse<Map<HardSkillType, String>> generateHardSkills(ImageFileData mediaFile) throws FatalGenerationException {
        var media = Media.builder()
                .data(mediaFile.getBytes())
                .mimeType(mediaFile.getContentType())
                .build();

        return generate(media);
    }

    @Override
    protected Map<HardSkillType, String> handleResponse(String jsonResult) throws AbstractRetryableGenerationException {
        try {
            return objectMapper.readValue(jsonResult, new TypeReference<>() {
            });
        } catch (JsonProcessingException e) {
            log.warn("Invalid json produced {}", jsonResult, e);
            throw new InvalidUserExperienceException(jsonResult, e);
        }
    }
}
