package com.erhgo.services.generation;

import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationState;
import com.erhgo.domain.classifications.erhgooccupation.OccupationCreationReason;
import com.erhgo.domain.exceptions.EntityAlreadyExistException;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.openapi.dto.GenerationReportItemDTO;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.security.Role;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.search.ErhgoOccupationIndexer;
import com.erhgo.utils.ReportUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.security.RolesAllowed;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CancellationException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Service
@RequiredArgsConstructor
@Slf4j
public class OccupationGenerator {
    public static final String DESCRIPTION = "2 - description";
    public static final String BEHAVIORS_DESCRIPTION = "3 - description des comportements";
    public static final String CODES_ROME = "4 - codes rome";
    public static final String CLASSIFICATIONS = "5 - classifications";
    public static final String MASTERY_LEVEL = "6 - niveau de maitrise";
    public static final String BEHAVIORS = "7 - comportements";
    public static final String ACTIVITIES = "8 - activités";

    private final DescriptionGenerationService.BehaviorDescriptionService behaviorDescriptionGenerationService;
    private final DescriptionGenerationService.OccupationDescriptionService occupationDescriptionGenerationService;
    private final ClassificationGenerationService classificationGenerationService;
    private final RomeGenerationService romeGenerationService;
    private final BehaviorGenerationService behaviorGenerationService;
    private final ActivityGenerationService activityGenerationService;

    private final ErhgoOccupationRepository erhgoOccupationRepository;
    private final MasteryLevelGenerationService masteryLevelGenerationService;
    private final ErhgoOccupationIndexer erhgoOccupationIndexer;

    private Map<String, Function<ErhgoOccupation, OpenAIResponse<?>>> generationMap;

    @PostConstruct
    public void init() {
        generationMap = Map.of(
                DESCRIPTION, occupation -> {
                    var response = occupationDescriptionGenerationService.generateOccupationDescription(occupation.getId());
                    occupation.setDescription(response.getResult().getDescription());
                    return response;
                },
                BEHAVIORS_DESCRIPTION, occupation -> {
                    var response = behaviorDescriptionGenerationService.generateOccupationBehaviorsDescription(occupation.getId());
                    occupation.setBehaviorsDescription(response.getResult().getDescription());
                    return response;
                },
                CODES_ROME, occupation -> romeGenerationService.generateRomeAssociations(occupation.getId()),
                CLASSIFICATIONS, occupation -> classificationGenerationService.generateAndAssociateClassificationsAssociations(occupation.getId()),
                MASTERY_LEVEL, occupation -> masteryLevelGenerationService.generateMasteryLevel(occupation.getId()),
                BEHAVIORS, occupation -> behaviorGenerationService.associateBehaviorsToOccupation(occupation.getId()),
                ACTIVITIES, occupation -> activityGenerationService.qualifyAndPersistOccupationActivities(occupation.getId())
        );
    }

    @Transactional(dontRollbackOn = {FatalGenerationException.class})
    @RolesAllowed({Role.ODAS_ADMIN, Role.CANDIDATE, Role.SOURCING})
    public List<GenerationReportItemDTO> qualifyOccupation(UUID occupationUuid, String title, boolean occupationRequiresConfirmation, OccupationCreationReason occupationCreationReason, String... alternativeLabels) {
        log.info("Starting generation for occupation {} - {}", title, occupationUuid);
        erhgoOccupationRepository.findById(occupationUuid).ifPresent(erhgoOccupation -> {
            throw new EntityAlreadyExistException(ErhgoOccupation.class, occupationUuid);
        });

        var occupation = new AtomicReference<>(ErhgoOccupation
                .builder()
                .id(occupationUuid)
                .alternativeLabels(Stream.of(alternativeLabels).filter(StringUtils::isNotBlank).collect(Collectors.toSet()))
                .title(title)
                .occupationCreationReason(occupationCreationReason)
                .qualificationState(occupationRequiresConfirmation ? ErhgoOccupationState.TO_CONFIRM : ErhgoOccupationState.NONE)
                .build());

        var allReports = new ArrayList<GenerationReportItemDTO>();

        occupation.set(erhgoOccupationRepository.save(occupation.get().computeQualificationState()));

        generationMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(e -> generate(e.getKey(), e.getValue(), occupation.get()))
                .forEach(allReports::add);

        erhgoOccupationRepository.save(occupation.get());

        try {
            erhgoOccupationIndexer.updateOccupationIndexation(occupation.get()).get();
        } catch (ExecutionException | InterruptedException | CancellationException e) {
            log.error("failed to index erhgo occupation {}", occupation.get().getId(), e);
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
        }

        log.info("End of qualification - reports: {}", allReports);
        return allReports;
    }

    private GenerationReportItemDTO generate(String generationItemTitle, Function<ErhgoOccupation, OpenAIResponse<?>> generator, ErhgoOccupation occupation) {
        OpenAIResponse<?> response;
        var report = new GenerationReportItemDTO();
        try {
            response = generator.apply(occupation);
        } catch (FatalGenerationException ex) {
            response = ex.getResponse();
            report.errorMessage(ex.getMessage());
        }
        return ReportUtils.updateReportWithResponse(generationItemTitle, report, response);
    }

}
