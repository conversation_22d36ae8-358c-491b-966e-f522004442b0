package com.erhgo.services.generation.prompt;

import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;

import java.util.List;

@Data
public class YamlGenericGenerationPromptModel {
    @Getter(AccessLevel.PRIVATE)
    private List<InnerMessage> promptMessages;
    private String parameterizedLastPromptMessage;
    private String computePromptAdjust;

    public List<Message> getMessages() {
        return promptMessages.stream().map(InnerMessage::getMessage).toList();
    }

    @Data
    public static class InnerMessage {
        String role;
        String content;

        Message getMessage() {
            return switch (role) {
                case "assistant" -> new AssistantMessage(content);
                case "system" -> new SystemMessage(content);
                default -> new UserMessage(content);
            };
        }

    }
}
