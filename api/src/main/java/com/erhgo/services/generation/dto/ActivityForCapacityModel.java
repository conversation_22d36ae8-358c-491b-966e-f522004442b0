package com.erhgo.services.generation.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class ActivityForCapacityModel {
    private String title;
    private List<String> capacities;

    @JsonCreator
    public ActivityForCapacityModel(@JsonProperty("title") String title,
                                    @JsonProperty("capacities") List<String> capacities) {
        this.title = title;
        this.capacities = capacities;
    }
}
