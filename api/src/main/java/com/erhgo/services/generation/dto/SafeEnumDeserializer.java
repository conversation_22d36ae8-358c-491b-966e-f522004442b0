package com.erhgo.services.generation.dto;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;

@Slf4j
public class SafeEnumDeserializer<T extends Enum<T>> extends JsonDeserializer<T> implements ContextualDeserializer {

    private Class<T> enumClass;

    public SafeEnumDeserializer() {
    }

    public SafeEnumDeserializer(Class<T> enumClass) {
        this.enumClass = enumClass;
    }

    @Override
    public JsonDeserializer<?> createContextual(DeserializationContext ctxt, BeanProperty property) throws JsonMappingException {
        var enumClass = (Class<T>) ctxt.getContextualType().getRawClass();
        return new SafeEnumDeserializer<>(enumClass);
    }

    @Override
    public T deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        var value = p.getText();
        if (StringUtils.isBlank(value)) return null;

        var trimmedValue = value.trim();

        try {
            return Enum.valueOf(enumClass, trimmedValue);
        } catch (IllegalArgumentException e) {
            try {
                return Enum.valueOf(enumClass, trimmedValue.toUpperCase());
            } catch (IllegalArgumentException e2) {
                log.debug("Failed to parse {} enum value: '{}'", enumClass.getSimpleName(), trimmedValue);
                return null;
            }
        }
    }
}
