package com.erhgo.services.generation.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.function.Function;

import static java.lang.System.currentTimeMillis;

@Data
@Accessors(chain = true)
public class OpenAIResponse<T> {
    private long totalDurationInMs;
    private int nbTry = 0;
    private String model;
    private T result;

    public void increment() {
        nbTry++;
    }

    public void updateWith(long startTime) {
        var durationInMs = currentTimeMillis() - startTime;
        totalDurationInMs += durationInMs;
    }

    public <U> OpenAIResponse<U> transform(Function<T, U> transformer) {
        return new OpenAIResponse<U>()
                .setTotalDurationInMs(totalDurationInMs)
                .setNbTry(nbTry)
                .setResult(transformer.apply(result))
                .setModel(model)
                ;
    }

    public boolean isSuccess() {
        return result != null;
    }

}
