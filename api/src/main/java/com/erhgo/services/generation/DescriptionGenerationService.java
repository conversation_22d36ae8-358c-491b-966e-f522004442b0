package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.exceptions.openai.AbstractRetryableGenerationException;
import com.erhgo.domain.exceptions.openai.InvalidDescriptionCountException;
import com.erhgo.openapi.dto.GeneratedOccupationDescriptionDTO;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import jakarta.annotation.security.RolesAllowed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Slf4j
public abstract class DescriptionGenerationService extends AbstractGenerationService<String, String> {
    public static Integer MIN_DESCRIPTION_WORD_COUNT = 120;
    public static Integer MAX_DESCRIPTION_WORD_COUNT = 300;
    public static Integer MIN_BEHAVIOR_DESCRIPTION_WORD_COUNT = 40;
    public static Integer MAX_BEHAVIOR_DESCRIPTION_WORD_COUNT = 96;

    private DescriptionGenerationService(YamlPromptReader yamlPromptReader, GenerationClient generationClient, PromptConfig promptConfig, ErhgoOccupationRepository erhgoOccupationRepository, SecurityService securityService) {
        super(yamlPromptReader, generationClient, promptConfig, erhgoOccupationRepository, securityService);
    }

    protected void validateDescription(String description) throws AbstractRetryableGenerationException {
        var wordCount = description.split("\\s+").length;
        if (wordCount < MIN_DESCRIPTION_WORD_COUNT || wordCount > MAX_DESCRIPTION_WORD_COUNT) {
            throw new InvalidDescriptionCountException(wordCount, MIN_DESCRIPTION_WORD_COUNT, MAX_DESCRIPTION_WORD_COUNT, description);
        }
    }

    protected void validateBehaviorDescription(String description) throws AbstractRetryableGenerationException {
        var wordCount = description.split("\\s+").length;
        if (wordCount < MIN_BEHAVIOR_DESCRIPTION_WORD_COUNT || wordCount > MAX_BEHAVIOR_DESCRIPTION_WORD_COUNT) {
            throw new InvalidDescriptionCountException(wordCount, MIN_BEHAVIOR_DESCRIPTION_WORD_COUNT, MAX_BEHAVIOR_DESCRIPTION_WORD_COUNT, description);
        }
    }

    @Service
    public static class OccupationDescriptionService extends DescriptionGenerationService {
        public OccupationDescriptionService(YamlPromptReader yamlPromptReader, ErhgoOccupationRepository erhgoOccupationRepository, GenerationClient generationClient, PromptConfig occupationDescriptionPromptConfig, SecurityService securityService) {
            super(yamlPromptReader, generationClient, occupationDescriptionPromptConfig, erhgoOccupationRepository, securityService);
        }

        @RolesAllowed({Role.ODAS_ADMIN, Role.CANDIDATE, Role.SOURCING})
        public OpenAIResponse<GeneratedOccupationDescriptionDTO> generateOccupationDescription(UUID occupationUuid) {
            return generate(getOccupationOrThrow(occupationUuid).getTitle()).transform(GeneratedOccupationDescriptionDTO::new);
        }


        @Override
        public String handleResponse(String responseContent) throws AbstractRetryableGenerationException {
            validateDescription(responseContent);
            return responseContent;
        }
    }


    @Service
    public static class BehaviorDescriptionService extends DescriptionGenerationService {
        public BehaviorDescriptionService(YamlPromptReader yamlPromptReader, ErhgoOccupationRepository erhgoOccupationRepository, GenerationClient generationClient, PromptConfig behaviorDescriptionPromptConfig, SecurityService securityService) {
            super(yamlPromptReader, generationClient, behaviorDescriptionPromptConfig, erhgoOccupationRepository, securityService);
        }

        @RolesAllowed({Role.ODAS_ADMIN, Role.CANDIDATE, Role.SOURCING})
        public OpenAIResponse<GeneratedOccupationDescriptionDTO> generateOccupationBehaviorsDescription(UUID occupationUuid) {

            return generate(getOccupationOrThrow(occupationUuid).getTitle()).transform(GeneratedOccupationDescriptionDTO::new);
        }

        @Override
        public String handleResponse(String responseContent) throws AbstractRetryableGenerationException {
            validateBehaviorDescription(responseContent);
            return responseContent;
        }

    }
}

