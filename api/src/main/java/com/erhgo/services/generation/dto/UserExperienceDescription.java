package com.erhgo.services.generation.dto;

import com.erhgo.domain.enums.ExperienceType;
import com.erhgo.domain.userprofile.experience.UserExperience;

import java.util.Date;
import java.util.Optional;

public record UserExperienceDescription(
        String title,
        String type,
        String status,
        String duration,
        String employer,
        String date
) {

    public static UserExperienceDescription forExperience(UserExperience userExperience) {
        return new UserExperienceDescription(
                userExperience.getJobTitle(),
                userExperience.getType() == ExperienceType.INTERNSHIP ? "stage" : "emploi",
                userExperience.isOngoing() ? "expérience en cours" : "expérience terminée",
                formatDuration(userExperience.getDurationInMonths()),
                userExperience.getOrganizationName(),
                Optional.ofNullable(userExperience.getCreatedDate()).map(Date::toString).orElse(null)
        );
    }

    private static String formatDuration(Integer durationInMonths) {
        if (durationInMonths == null) {
            return null;
        }
        if (durationInMonths < 6) {
            return "Moins de 6 mois";
        } else if (durationInMonths <= 12) {
            return "6 mois à 1 an";
        } else if (durationInMonths <= 60) {
            return "1 an à 5 ans";
        } else {
            return "Plus de 5 ans";
        }
    }
}
