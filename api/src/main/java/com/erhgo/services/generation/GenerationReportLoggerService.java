package com.erhgo.services.generation;

import com.erhgo.openapi.dto.GenerationReportItemDTO;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class GenerationReportLoggerService {
    private static long computeDuration(List<GenerationReportItemDTO> allReports) {
        return allReports.stream().mapToLong(GenerationReportItemDTO::getDurationInMs).sum();
    }

    private static @NotNull Stream<GenerationReportItemDTO> allReportOnModelStream(List<GenerationReportItemDTO> allReports, String model) {
        return allReports.stream().filter(r -> model != null && model.equals(r.getModel()));
    }

    public void logForReports(String message, List<GenerationReportItemDTO> allReports) {
        var totalDurationReport = computeDuration(allReports);
        log.info("{} - total duration: {}", message, totalDurationReport);
        allReports.stream().map(GenerationReportItemDTO::getModel).distinct().forEach(model -> {
            var sourcesReport = computePromptSources(allReports, model);
            log.debug("- Model {}: (source(s): {})", model, sourcesReport);
        });
    }

    private String computePromptSources(List<GenerationReportItemDTO> allReports, String model) {
        return allReportOnModelStream(allReports, model).map(GenerationReportItemDTO::getTitle).collect(Collectors.joining(", "));
    }
}
