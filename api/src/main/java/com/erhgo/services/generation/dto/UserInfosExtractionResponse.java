package com.erhgo.services.generation.dto;

import com.erhgo.domain.enums.DiplomaLevel;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class UserInfosExtractionResponse {
    private String lastName;
    private String firstName;
    private String email;
    private String phoneNumber;
    private Boolean driverLicense;
    @JsonDeserialize(using = SafeEnumDeserializer.class)
    private DiplomaLevel diplomaLevel;
    private String location;


}
