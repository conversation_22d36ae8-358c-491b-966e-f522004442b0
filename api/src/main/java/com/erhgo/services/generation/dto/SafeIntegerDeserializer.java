package com.erhgo.services.generation.dto;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;

@Slf4j
public class SafeIntegerDeserializer extends JsonDeserializer<Integer> {

    @Override
    public Integer deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        var value = p.getText();
        if (StringUtils.isBlank(value)) return null;
        
        try {
            var doubleValue = Double.parseDouble(value.trim());
            return (int) Math.ceil(doubleValue);
        } catch (NumberFormatException e) {
            log.debug("Failed to parse integer value: '{}'", value);
            return null;
        }
    }
}
