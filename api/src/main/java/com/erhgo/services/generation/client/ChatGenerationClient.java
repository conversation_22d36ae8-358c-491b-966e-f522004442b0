package com.erhgo.services.generation.client;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import com.erhgo.services.generation.dto.OpenAIResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.AbstractMessage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@ConditionalOnExpression("!'${spring.ai.openai.api-key}'.isEmpty()")
public class ChatGenerationClient implements GenerationClient {
    Map<String, ChatClient> chatClients;

    ChatGenerationClient(Map<String, ChatModel> chatModels) {
        this.chatClients = chatModels
                .entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> ChatClient.builder(e.getValue()).build()));
    }

    @Override
    public ChatCompletionResponse createChatCompletion(Prompt request, PromptConfig promptConfig) throws FatalGenerationException {
        ChatResponse response;
        try {
            if (log.isTraceEnabled()) log.trace("About to send request {} to AI Client", request);
            log.debug(
                    "About to send request to AI Client - last prompt is {}, model={}, temperature={}, type={}, maxlength={}",
                    request.getInstructions().getLast(),
                    request.getOptions().getModel(),
                    request.getOptions().getTemperature(),
                    request.getClass().getSimpleName(),
                    request.getOptions().getMaxTokens()
            );
            var chatClient = chatClients.entrySet()
                    .stream()
                    .filter(x -> x.getKey()
                            .toLowerCase()
                            .contains(promptConfig.getProvider().toLowerCase()))
                            .findFirst()
                            .orElseThrow(() -> new FatalGenerationException("No chat client found for provider %s".formatted(promptConfig.getProvider())))
                    .getValue();
            response = chatClient.prompt(request).call().chatResponse();
            log.debug("Got AI client result messages {}", response);
        } catch (RuntimeException e) {
            log.error("Error while creating chat completion: {}", e.getMessage(), e);
            throw new FatalGenerationException("Error while creating chat completion: " + e.getMessage(), e);
        }

        if (response == null) {
            log.error("No response from AI Client");
            throw new FatalGenerationException("No response from AI Client");
        }

        var generations = response.getResults();
        if (generations == null || generations.isEmpty()) {
            log.error("No generations in response {}", response);
            throw new FatalGenerationException("No generations in AI response");
        }

        var content = generations.stream()
                .findFirst()
                .map(Generation::getOutput)
                .map(AbstractMessage::getText)
                .orElseThrow(() -> new FatalGenerationException("No content in response"));

        var chatResponse = new OpenAIResponse<ChatCompletionResponse>()
                .setModel(request.getOptions().getModel());

        if (!STOP_REASON.equalsIgnoreCase(generations.getFirst().getMetadata().getFinishReason())) {
            log.error("Unexpected response or bad finish reason {}", generations);
            throw new FatalGenerationException("Unexpected response or bad finish reason", chatResponse);
        }

        return new ChatCompletionResponse(content, chatResponse);
    }
}
