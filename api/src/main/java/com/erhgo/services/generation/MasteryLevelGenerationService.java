package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.openai.AbstractRetryableGenerationException;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.domain.exceptions.openai.InvalidMasteryLevelException;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.MasteryLevelGenerationResponse;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import com.fasterxml.jackson.core.JsonProcessingException;
import jakarta.annotation.security.RolesAllowed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Slf4j
@Service
public class MasteryLevelGenerationService extends AbstractGenerationService<MasteryLevelGenerationResponse, String> {
    public MasteryLevelGenerationService(
            YamlPromptReader yamlPromptReader,
            GenerationClient generationClient,
            PromptConfig levelPromptConfig,
            ErhgoOccupationRepository erhgoOccupationRepository,
            SecurityService securityService
    ) {
        super(yamlPromptReader, generationClient, levelPromptConfig, erhgoOccupationRepository, securityService);
    }

    @Override
    public MasteryLevelGenerationResponse handleResponse(String responseContent) throws AbstractRetryableGenerationException {
        return validateSpecificationAndMasteryLevel(responseContent);
    }

    private MasteryLevelGenerationResponse validateSpecificationAndMasteryLevel(String jsonResult) throws AbstractRetryableGenerationException {
        try {
            var dto = objectMapper.readValue(jsonResult, MasteryLevelGenerationResponse.class);
            if (dto.getMasteryLevel() == null || !MasteryLevel.isValid(dto.getMasteryLevel())) {
                throw new InvalidMasteryLevelException(jsonResult);
            }
            return dto;
        } catch (JsonProcessingException e) {
            log.warn("Invalid json produced {}", jsonResult, e);
            throw new InvalidMasteryLevelException(jsonResult, e);
        }
    }

    private ErhgoOccupation getOrThrow(UUID uuid) {
        return erhgoOccupationRepository.findById(uuid).orElseThrow(() -> new EntityNotFoundException(uuid, ErhgoOccupation.class));
    }


    @Transactional(noRollbackFor = {FatalGenerationException.class})
    @RolesAllowed({Role.ODAS_ADMIN, Role.CANDIDATE, Role.SOURCING})
    public OpenAIResponse<MasteryLevelGenerationResponse> generateMasteryLevel(UUID occupationId) {
        var occupation = getOrThrow(occupationId);
        var response = generate(occupation.getTitle());
        occupation.updateLevel(MasteryLevel.forLevel(response.getResult().getMasteryLevel()));
        erhgoOccupationRepository.save(occupation);
        return response;
    }

}
