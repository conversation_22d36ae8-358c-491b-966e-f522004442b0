package com.erhgo.services.generation;

import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.openapi.dto.ChatCompletionResponseDTO;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import com.erhgo.services.generation.dto.CustomChatCompletionRequest;
import com.erhgo.services.generation.dto.GenericOpenAiPromptTesterCommand;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import com.erhgo.services.userprofile.FilePartProvider;
import com.erhgo.utils.PDFUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class GenericPromptTesterService {

    private final GenerationClient generationClient;
    private final SecurityService securityService;
    private final YamlPromptReader yamlPromptReader;

    public ChatCompletionResponseDTO testPrompt(GenericOpenAiPromptTesterCommand command) {
        try {
            String fileContent = null;
            if (command.getFileContent() != null) {
                fileContent = PDFUtils.extractTextFromPDF(new FilePartProvider(command.getFileContent()));
            }
            var data = yamlPromptReader.readYamlDataGenericFromContent(command.getPromptMessages());
            var messages = new ArrayList<>(data.getMessages());
            if (StringUtils.isNotBlank(fileContent)) {
                messages.add(new UserMessage(fileContent));
            }
            return createChatCompletionForMessages(messages, command);
        } catch (RuntimeException e) {
            log.error("Unable to send completion request for command {}", command, e);
            return new ChatCompletionResponseDTO().content("Erreur à la génération - réponse : %s, erreur: %s".formatted(e.getMessage(), ExceptionUtils.getStackTrace(e)));
        }
    }

    ChatCompletionResponseDTO createChatCompletionForMessages(List<Message> prompt, GenericOpenAiPromptTesterCommand command) throws FatalGenerationException {
        var options = OpenAiChatOptions
                .builder()
                .model(command.getModel())
                .temperature(command.getTemperature())
                .maxTokens(command.getMaxTokens())
                .user(securityService.isAdmin() ? "" : securityService.getAuthenticatedUserId())
                .build();
        var chatCompletionRequest = CustomChatCompletionRequest.customBuilder()
                .messages(prompt)
                .options(options)
                .gpt4JsonMode(command.isForceJson())
                .build();
        log.debug("Sending chat completion request {}", chatCompletionRequest);
        var result = sanitize(generationClient.createChatCompletion(chatCompletionRequest, null));
        log.debug("Got chat completion response {}", result);
        return result;
    }

    protected ChatCompletionResponseDTO sanitize(ChatCompletionResponse response) {
        var usage = response.getUsage();
        return new ChatCompletionResponseDTO()
                .content(response.getContent())
                .model(usage.getModel());
    }
}
