package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.enums.BehaviorCategory;
import com.erhgo.domain.exceptions.openai.AbstractRetryableGenerationException;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.domain.exceptions.openai.InvalidBehaviorsCountException;
import com.erhgo.domain.exceptions.openai.InvalidBehaviorsException;
import com.erhgo.openapi.dto.ErhgoOccupationBehaviorsCategoriesDTO;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import com.erhgo.services.dtobuilder.ErhgoOccupationDataDTOBuilder;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import com.google.common.annotations.VisibleForTesting;
import jakarta.annotation.security.RolesAllowed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static java.util.stream.IntStream.range;

@Slf4j
@Service
public class BehaviorGenerationService extends AbstractGenerationService<List<BehaviorCategory>, String> {

    private final ErhgoOccupationDataDTOBuilder erhgoOccupationDataDTOBuilder;
    @VisibleForTesting
    public static int MIN_BEHAVIORS = 1;

    @VisibleForTesting
    public static int MAX_BEHAVIORS = 3;

    public BehaviorGenerationService(
            YamlPromptReader yamlPromptReader,
            GenerationClient generationClient,
            PromptConfig behaviorPromptConfig,
            ErhgoOccupationDataDTOBuilder erhgoOccupationDataDTOBuilder,
            ErhgoOccupationRepository erhgoOccupationRepository,
            SecurityService securityService
    ) {
        super(yamlPromptReader, generationClient, behaviorPromptConfig, erhgoOccupationRepository, securityService);
        this.erhgoOccupationDataDTOBuilder = erhgoOccupationDataDTOBuilder;
    }

    @Transactional(noRollbackFor = {FatalGenerationException.class})
    @RolesAllowed({Role.ODAS_ADMIN, Role.CANDIDATE, Role.SOURCING})
    public OpenAIResponse<ErhgoOccupationBehaviorsCategoriesDTO> associateBehaviorsToOccupation(UUID occupationId) {
        var occupation = getOccupationOrThrow(occupationId);
        var response = generate(occupation.getTitle());
        var behaviors = response.getResult();
        range(0, behaviors.size()).forEach(i -> occupation.updateBehaviorCategory(behaviors.get(i), i));
        if (behaviors.size() < 3) {
            range(behaviors.size(), 3).forEach(i -> occupation.updateBehaviorCategory(null, i));
        }
        erhgoOccupationRepository.save(occupation);
        return response.transform(res -> erhgoOccupationDataDTOBuilder.buildOccupationBehaviorsCategories(occupation));
    }

    @Override
    public List<BehaviorCategory> handleResponse(String responseContent) throws AbstractRetryableGenerationException {
        var behaviorsCodes = extractCodesFromResponse(responseContent);
        return validateBehaviors(behaviorsCodes, responseContent);
    }

    private List<BehaviorCategory> validateBehaviors(List<String> behaviors, String responseContent) throws AbstractRetryableGenerationException {
        var behaviorCategories = Arrays.stream(BehaviorCategory.values())
                .map(Enum::name)
                .toList();

        var existingBehaviors = behaviors.stream()
                .filter(behaviorCategories::contains)
                .map(BehaviorCategory::valueOf)
                .toList();

        var behaviorsNotFound = behaviors.stream()
                .filter(behavior -> Arrays.stream(BehaviorCategory.values()).noneMatch(behaviorCategory -> behaviorCategory.name().equals(behavior)))
                .toList();

        if (behaviors.size() < MIN_BEHAVIORS || behaviors.size() > MAX_BEHAVIORS) {
            throw new InvalidBehaviorsCountException(behaviors.size(), MIN_BEHAVIORS, MAX_BEHAVIORS, responseContent);
        }
        if (!behaviorsNotFound.isEmpty()) {
            throw new InvalidBehaviorsException(String.join(", ", behaviorsNotFound), responseContent);
        }

        return existingBehaviors;

    }
}
