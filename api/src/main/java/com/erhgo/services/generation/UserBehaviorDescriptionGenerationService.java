package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.exceptions.openai.AbstractRetryableGenerationException;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.domain.exceptions.openai.InvalidDescriptionCountException;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import jakarta.annotation.security.RolesAllowed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UserBehaviorDescriptionGenerationService extends AbstractGenerationService<String, String> {
    public static final Integer MIN_USER_BEHAVIOR_DESCRIPTION_WORD_COUNT = 25;
    public static final Integer MAX_USER_BEHAVIOR_DESCRIPTION_WORD_COUNT = 80;

    public UserBehaviorDescriptionGenerationService(
            YamlPromptReader yamlPromptReader,
            GenerationClient generationClient,
            PromptConfig userBehaviorDescriptionPromptConfig,
            ErhgoOccupationRepository erhgoOccupationRepository,
            SecurityService securityService
    ) {
        super(yamlPromptReader, generationClient, userBehaviorDescriptionPromptConfig, erhgoOccupationRepository, securityService);
    }

    @RolesAllowed({Role.CANDIDATE, Role.SOURCING, Role.ODAS_ADMIN})
    public OpenAIResponse<String> generateUserBehaviorDescription(String behaviorsDescriptionAndTitle) throws FatalGenerationException {
        return generate(behaviorsDescriptionAndTitle);
    }


    @Override
    public String handleResponse(String responseContent) throws AbstractRetryableGenerationException {
        validateBehaviorDescription(responseContent);
        return responseContent;
    }

    protected void validateBehaviorDescription(String description) throws AbstractRetryableGenerationException {
        var wordCount = description.split("\\s+").length;
        if (wordCount < MIN_USER_BEHAVIOR_DESCRIPTION_WORD_COUNT || wordCount > MAX_USER_BEHAVIOR_DESCRIPTION_WORD_COUNT) {
            throw new InvalidDescriptionCountException(wordCount, MIN_USER_BEHAVIOR_DESCRIPTION_WORD_COUNT, MAX_USER_BEHAVIOR_DESCRIPTION_WORD_COUNT, description);
        }
    }

}
