package com.erhgo.services;

import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.userprofile.UserExperienceGeneratedData;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.experience.UserExperience;
import com.erhgo.openapi.dto.UserBehaviorDescriptionDTO;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.AuthorizeExpression;
import com.erhgo.security.Role;
import com.erhgo.services.generation.UserBehaviorDescriptionGenerationService;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.utils.KeycloakUtils;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserBehaviorDescriptionService {
    private final UserBehaviorDescriptionGenerationService generationService;
    private final SecurityService securityService;
    private final UserProfileRepository userProfileRepository;
    private final KeycloakService keycloakService;

    @Transactional
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_WRITE)
    public UserBehaviorDescriptionDTO getUserBehaviorDescriptionOrGenerate(String userId) {
        var userProfile = getUserProfileOrThrow(userId);
        var userBehavior = Optional.ofNullable(userProfile.userExperienceGeneratedData());
        var fisrtName = keycloakService.getFrontOfficeUserProfile(userId).orElseGet(() -> KeycloakUtils.defaultUserRepresentation(userId)).getFirstName();
        if (userBehavior.map(UserExperienceGeneratedData::requiresAttitudeGeneration).orElse(true)) {
            var behaviors = getUserOccupationsBehaviorsDescription(userProfile);
            var result = "";
            if (Strings.isBlank(behaviors)) {
                log.error("Unable to get behaviors description for user {} - no experiences or no behaviors on occupation - using empty generated behaviors", userId);
            } else {
                behaviors = "%s pour le prénom %s".formatted(behaviors, fisrtName);
                result = generationService.generateUserBehaviorDescription(behaviors).getResult();
                userProfile.setGeneratedUserBehaviorDescription(result);
            }
        }
        var description = userProfile.userExperienceGeneratedData().attitude();
        return new UserBehaviorDescriptionDTO().description(description);
    }

    @Transactional
    @RolesAllowed(Role.CANDIDATE)
    public void forceUserBehaviorDescription(String description) {
        var user = securityService.getAuthenticatedUserProfile();
        user.setForcedUserBehaviorDescription(description);
    }

    @Transactional
    public void dirtyUserBehaviorIfNeeded(String userId) {
        userProfileRepository.findByUserId(userId)
                .ifPresentOrElse(
                        UserProfile::dirtyUserExperiencesGeneratedData,
                        () -> log.error("User with uuid {} not found, unable to dirty user behavior", userId));
    }

    private String getUserOccupationsBehaviorsDescription(UserProfile userProfile) {
        var distinctAndLimitedUserOccupationsStream = userProfile.experiences()
                .stream()
                .filter(x -> Optional.ofNullable(x.getErhgoOccupation()).map(ErhgoOccupation::getBehaviorsDescription).filter(Strings::isNotBlank).isPresent())
                .collect(Collectors.groupingBy(UserExperience::getOccupationId))
                .values()
                .stream()
                .distinct()
                .map(ux -> ux.stream().findFirst())
                .filter(Optional::isPresent)
                .map(Optional::get)
                .limit(10);

        return distinctAndLimitedUserOccupationsStream
                .map(x -> "%s : \"%s\"".formatted(x.getJobTitle(), x.getErhgoOccupation().getBehaviorsDescription()))
                .collect(Collectors.joining("\n"));
    }

    private UserProfile getUserProfileOrThrow(String userId) {
        return userProfileRepository.findByUserId(userId).orElseThrow(() -> new EntityNotFoundException(userId, UserProfile.class));
    }
}
