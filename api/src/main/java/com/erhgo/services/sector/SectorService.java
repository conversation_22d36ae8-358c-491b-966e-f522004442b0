package com.erhgo.services.sector;

import com.erhgo.openapi.dto.SectorDTO;
import com.erhgo.repositories.SectorRepository;
import com.erhgo.services.dtobuilder.SectorDTOBuilder;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class SectorService {

    private final SectorRepository repository;

    public List<SectorDTO> getReferentialSectors() {
        return repository.findAllByOrderByCode().stream().map(SectorDTOBuilder::buildSectorDTO).toList();
    }

}
