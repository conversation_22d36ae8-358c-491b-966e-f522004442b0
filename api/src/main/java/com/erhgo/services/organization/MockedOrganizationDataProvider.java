package com.erhgo.services.organization;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@ConditionalOnExpression("T(org.springframework.util.StringUtils).isEmpty('${insee.apiKey:}')")
@Slf4j
public class MockedOrganizationDataProvider implements OrganizationDataProvider {

    @Override
    public String getNameForSiret(String siret) {
        return "Entreprise " + siret;
    }
}
