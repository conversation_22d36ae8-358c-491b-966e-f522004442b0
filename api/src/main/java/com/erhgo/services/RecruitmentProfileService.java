package com.erhgo.services;

import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.InvalidProfileIdException;
import com.erhgo.domain.exceptions.NoSuchMissionInJob;
import com.erhgo.domain.exceptions.ProfileNotModifiableException;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.recruitment.RecruitmentProfile;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.QuestionForContexts;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.*;
import com.erhgo.security.Role;
import com.erhgo.services.dtobuilder.CapacityDTOBuilder;
import com.erhgo.services.dtobuilder.RecruitmentProfileDTOBuilder;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class RecruitmentProfileService {

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private RecruitmentProfileRepository recruitmentProfileRepository;

    @Autowired
    private RecruitmentProfileDTOBuilder recruitmentProfileDTOBuilder;

    @Autowired
    private OptionalActivityRepository optionalActivityRepository;

    @Autowired
    private OptionalContextRepository optionalContextRepository;

    @Autowired
    private ContextRepository contextRepository;

    @Autowired
    private SecurityService securityService;

    @Autowired
    private QuestionForContextsRepository questionRepository;

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public List<RecruitmentProfile> findByJobId(UUID jobId, Boolean qualifiedOnly) {
        var recruitmentProfiles = recruitmentProfileRepository.findByJobIdOrderByTitleAsc(jobId);

        if (qualifiedOnly != null && recruitmentProfiles != null) {
            recruitmentProfiles = recruitmentProfiles.stream().filter(r -> r.isQualified() == qualifiedOnly).toList();
        }
        return recruitmentProfiles;
    }

    private Job getJobOrThrow(UUID jobId) {
        return jobRepository.findById(jobId)
                .orElseThrow(() -> new EntityNotFoundException(jobId, Job.class));
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public RecruitmentProfile createOrUpdate(UUID jobId, SaveRecruitmentProfileCommandDTO saveRecruitmentProfileDTO) {

        var profile = getProfileOrCreate(saveRecruitmentProfileDTO.getId());

        profile.setTitle(saveRecruitmentProfileDTO.getTitle());
        profile.setJob(getJobOrThrow(jobId));
        profile.setCustomQuestion(saveRecruitmentProfileDTO.getCustomQuestion());

        return recruitmentProfileRepository.save(profile);
    }

    private RecruitmentProfile getProfileOrCreate(UUID id) {
        return recruitmentProfileRepository.findById(id).orElse(RecruitmentProfile.builder().uuid(id).build());
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public RecruitmentProfileDetailDTO getProfileDetail(UUID jobId, UUID profileId) {
        return recruitmentProfileDTOBuilder.build(getRecruitmentProfileOrThrow(jobId, profileId));
    }

    private RecruitmentProfile getRecruitmentProfileOrThrow(UUID jobId, UUID profileId) {
        var profile = recruitmentProfileRepository.findById(profileId).orElseThrow(() -> new EntityNotFoundException(profileId, RecruitmentProfile.class));

        if (!jobId.equals(profile.getJob().getId())) {
            throw new InvalidProfileIdException("Job id does not match profile id");
        }
        return profile;
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    // Do not consider this as transactional: use & flush transaction per tiontional deletion to avoid random lock problem on relationship deletion
    public void deleteOptionals(UUID jobId, UUID profileId, List<DeleteOptionalsCommandItemDTO> deleteOptionalsCommandItems) {
        var profile = getRecruitmentProfileOrThrow(jobId, profileId);
        ensureProfileIsModifiable(profile);
        deleteOptionalsCommandItems.forEach(o -> deleteOptional(profile, o));

        recruitmentProfileRepository.save(profile);
    }

    private void deleteOptional(RecruitmentProfile profile, DeleteOptionalsCommandItemDTO deleteOptionalsCommand) {
        switch (deleteOptionalsCommand.getOptionalType()) {
            case CONTEXT:
                var optionalContext = profile.deleteOptionalContext(deleteOptionalsCommand.getOptionalId());
                optionalContextRepository.delete(optionalContext);
                break;
            case ACTIVITY:
                var optionalActivity = profile.deleteOptionalActivity(deleteOptionalsCommand.getOptionalId());
                optionalActivityRepository.delete(optionalActivity);
                break;
            default:
                throw new IllegalArgumentException("Unknown optional type " + deleteOptionalsCommand.getOptionalType());
        }

    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public void updateOptionals(UUID jobId, UUID profileId, List<AddOptionalsCommandItemDTO> optionalCommands) {
        var profile = getRecruitmentProfileOrThrow(jobId, profileId);
        ensureProfileIsModifiable(profile);
        optionalCommands.forEach(o -> updateOptional(profile, o));
        recruitmentProfileRepository.save(profile);
    }

    private void ensureProfileIsModifiable(RecruitmentProfile profile) {
        if (!securityService.isAdmin() && !profile.isModifiable()) {
            throw new ProfileNotModifiableException(profile.getUuid());
        }
    }

    private void updateOptional(RecruitmentProfile profile, AddOptionalsCommandItemDTO optional) {

        switch (optional.getOptionalType()) {
            case CONTEXT:
                profile.updateOptionalContext(optional.getOptionalId(), optional.getAcquisitionModality());
                break;
            case ACTIVITY:
                profile.updateOptionalActivity(optional.getOptionalId(), optional.getAcquisitionModality());
                break;
            default:
                throw new IllegalArgumentException("Unknown optional type " + optional.getOptionalType());
        }
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public void endQualification(UUID jobId, UUID profileId, Long missionId) {
        var profile = getRecruitmentProfileOrThrow(jobId, profileId);
        ensureProfileIsModifiable(profile);
        var mission = profile.getJob().getMissions().stream().filter(m -> m.getId().equals(missionId)).findFirst().orElseThrow(() -> new NoSuchMissionInJob(missionId, jobId));
        profile.addQualifiedMission(mission);
        recruitmentProfileRepository.save(profile);
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public void setCustomContextLabelForProfile(UUID jobId, UUID profileId, SetQuestionForContextCommandDTO command) {
        var profile = getRecruitmentProfileOrThrow(jobId, profileId);
        var context = contextRepository.findById(command.getContextId())
                .orElseThrow(() -> new EntityNotFoundException(command.getContextId(), Context.class));
        var question = questionRepository.findById(command.getQuestionId())
                .orElseThrow(() -> new EntityNotFoundException(command.getQuestionId(), QuestionForContexts.class));
        profile.updateContextQuestion(context, question);
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public List<CapacityAndLevelDTO> getProfileCapacities(UUID uuid) {
        var profile = recruitmentProfileRepository.findById(uuid).orElseThrow(() -> new EntityNotFoundException(uuid, RecruitmentProfile.class));
        return CapacityDTOBuilder.buildCapacityAndLevelDTOS(profile.getMandatoryCapacities(), profile.getJob().getLevelForCapacities());
    }

}
