package com.erhgo.services.notifier;

import com.erhgo.services.notifier.messages.AbstractNotifierMessageDTO;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class MessagesInitializer {

    @Value("${keycloak-realms.back_office_base_url}${application.occupation-relative-url}")
    private String baseOccupationPath;
    @Value("${keycloak-realms.back_office_base_url}${application.profile-relative-url}")
    private String baseProfilePath;
    @Value("${keycloak-realms.back_office_base_url}${application.recruitment-relative-url}")
    private String baseRecruitmentPath;
    @Value("${keycloak-realms.back_office_base_url}${application.organization-relative-url}")
    private String baseOrganizationPath;
    @Value("${keycloak-realms.sourcing_base_url}${application.external-offer-relative-url}")
    private String baseSourcingExternalOfferPath;
    @Value("${keycloak-realms.sourcing_base_url}${application.sourcing-recruitment-relative-url}")
    private String baseSourcingRecruitmentPath;


    @PostConstruct
    // SuppressWarnings => Hack to use Spring inside domain
    @SuppressWarnings("java:S2696")
    void initStaticFields() {
        AbstractNotifierMessageDTO.baseProfilePath = baseProfilePath;
        AbstractNotifierMessageDTO.baseOccupationPath = baseOccupationPath;
        AbstractNotifierMessageDTO.baseRecruitmentPath = baseRecruitmentPath;
        AbstractNotifierMessageDTO.baseOrganizationPath = baseOrganizationPath;
        AbstractNotifierMessageDTO.baseSourcingExternalOfferPath = baseSourcingExternalOfferPath;
        AbstractNotifierMessageDTO.baseSourcingRecruitmentPath = baseSourcingRecruitmentPath;
    }
}
