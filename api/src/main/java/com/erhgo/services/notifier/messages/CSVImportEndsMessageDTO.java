package com.erhgo.services.notifier.messages;

import com.erhgo.services.dto.CsvCreationResult;
import com.google.common.base.Joiner;
import lombok.Getter;

import java.util.List;
import java.util.function.Predicate;

@Getter
public class CSVImportEndsMessageDTO extends AbstractNotifierMessageDTO {
    public CSVImportEndsMessageDTO(String email, List<CsvCreationResult> result) {
        if (result == null) {
            this.text = ":no_entry: L'import CSV lancé par %s a globalement échoué, aucun recrutement créé (fichier invalide ?)".formatted(email);
            return;
        }
        var errors = result.stream().filter(CsvCreationResult::isErroneous).map(o -> "Ligne N° %d - Erreur : %ss".formatted(o.rowNum(), o.errorMessage())).toList();
        var warning = result.stream().filter(Predicate.not(CsvCreationResult::isErroneous)).filter(CsvCreationResult::isWarning).map(o -> "Ligne N° %d - Attention : %s".formatted(o.rowNum(), o.warningMessage())).toList();
        var ok = result.stream().filter(CsvCreationResult::isOk).map(CsvCreationResult::rowNum).toList();
        var icon = !errors.isEmpty() ? "no_entry" : !warning.isEmpty() ? "warning" : "white_check_mark";
        this.text = ":%s: L'import CSV démarré par %s est terminé - %d recrutement(s) créé(s), %d recrutement(s) en erreur)".formatted(icon, email, warning.size() + ok.size(), errors.size());
        this.text += errors.isEmpty() ? "" : "\n\t\t :no_entry: %d Ligne(s) en erreur (=> recrutement non créé) : \n\t\t\t\t\t\t- %s".formatted(errors.size(), Joiner.on("\n\t\t\t\t\t\t- ").join(errors));
        this.text += warning.isEmpty() ? "" : "\n\t\t :warning: %d Ligne(s) nécessitant une attention : \n\t\t\t\t\t\t- %s".formatted(warning.size(), Joiner.on("\n\t\t\t\t\t\t- ").join(warning));
        this.text += ok.isEmpty() ? "" : "\n\t\t :white_check_mark: %d  Ligne(s) correcte(s) : Ligne(s) n° %s".formatted(ok.size(), Joiner.on(", ").join(ok));
    }

    @Override
    public boolean isImmediate() {
        return true;
    }
}
