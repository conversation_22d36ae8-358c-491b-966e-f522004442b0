package com.erhgo.services.notifier.messages;

import com.erhgo.domain.referential.AbstractOrganization;
import lombok.Getter;

@Getter
public class SourcingNewUserMessageDTO extends AbstractNotifierMessageDTO {

    public SourcingNewUserMessageDTO(AbstractOrganization onOrganisation, boolean isFirst, boolean isSubscriptionActivated) {
        var recruiterNameWithPeriodTrialText = getRecruiterNameWithTrialPeriodText(onOrganisation.getTitle(), isSubscriptionActivated);
        if (isFirst) {
            text = ":tada: Un nouvel utilisateur a été créé sur l'application sourcing " + getOrganizationLink(onOrganisation.getId(), "pour l'entreprise *" + recruiterNameWithPeriodTrialText + "*");
            if (onOrganisation.getSiretVerificationStatus() == AbstractOrganization.SiretVerificationStatus.WRONG_SIRET) {
                text += "\n\t\t :no_entry: *Le siret utilisé ne semble pas valide :* " + onOrganisation.getSiret() + " :no_entry:";
            }
            if (onOrganisation.getSiretVerificationStatus() == AbstractOrganization.SiretVerificationStatus.TECHNICAL_ERROR) {
                text += "\n\t\t :ladybug: *Une erreur technique est survenue à la vérification du siret :* " + onOrganisation.getSiret() + " :ladybug:";
            }
        } else {
            text = ":medal: Un nouvel utilisateur invité sur l'organisation %s".formatted(recruiterNameWithPeriodTrialText);
        }
    }


}
