package com.erhgo.services.notifier.messages;

import lombok.Getter;

import java.util.UUID;

@Getter
public class ExperienceOnUnqualifiedOccupationMessageDTO extends AbstractNotifierMessageDTO {

    public ExperienceOnUnqualifiedOccupationMessageDTO(UUID occupationId, String occupationTitle, UUID userId) {
        this.text = ":warning: %s a ajouté une expérience sur un métier (*qualification non confirmée*) %s".formatted(getFOUserLink(userId), getOccupationLink(occupationTitle, occupationId));
    }

}
