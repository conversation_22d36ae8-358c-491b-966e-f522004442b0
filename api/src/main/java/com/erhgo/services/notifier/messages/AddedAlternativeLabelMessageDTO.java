package com.erhgo.services.notifier.messages;

import com.erhgo.services.notifier.OccupationCreationSourceType;
import lombok.Getter;

import java.util.UUID;

@Getter
public class AddedAlternativeLabelMessageDTO extends AbstractNotifierMessageDTO {
    public AddedAlternativeLabelMessageDTO(UUID occupationId, String occupationLabel, String label, OccupationCreationSourceType sourceType) {
        this.text = ":information_source: Le label %s a été utilisé par %s en lien avec le métier %s (aucun label alternatif ajouté)".formatted(label, sourceType == OccupationCreationSourceType.FROM_CV ? "CV" : "ATS", getOccupationLink(occupationLabel, occupationId));
    }
}
