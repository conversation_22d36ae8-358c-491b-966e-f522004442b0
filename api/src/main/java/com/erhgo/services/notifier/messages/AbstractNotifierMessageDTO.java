package com.erhgo.services.notifier.messages;

import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

@SuppressWarnings({"java:S1444", "java:S1104"})
public abstract class AbstractNotifierMessageDTO {
    public static String baseOccupationPath = "";
    public static String baseProfilePath = "";
    public static String baseOrganizationPath = "";
    public static String baseRecruitmentPath = "";

    public static final String TRIAL_PERIOD_TEXT = "_(version d'essai)_";
    public static String baseSourcingExternalOfferPath = "";
    public static String baseSourcingRecruitmentPath = "";

    public boolean isImmediate() {
        return false;
    }

    @Getter
    @Setter
    protected String text;
    @Getter
    @Setter
    protected String forcedSlackChannel;

    static String formatUserId(String userId) {
        return userId.split("-")[0] + "...";
    }

    public static String link(String url, String label) {
        return "<%s|%s>".formatted(url, label);
    }

    String getOccupationLink(String message, UUID occupationId) {
        return link(baseOccupationPath.formatted(occupationId), message);
    }

    String getFOUserLink(UUID userId) {
        return link(baseProfilePath.formatted(userId), "Un utilisateur");
    }

    String getOrganizationLink(Long id, String message) {
        return link(baseOrganizationPath.formatted(id.toString()), message);
    }

    String getRecruitmentLink(String recruitmentCode, String organizationCode) {
        return getRecruitmentLink("un recrutement", recruitmentCode, organizationCode);
    }

    String getRecruitmentLink(String label, String recruitmentCode, String organizationCode) {
        return link(baseRecruitmentPath.formatted(organizationCode, recruitmentCode), label);
    }

    String getRecruiterNameWithTrialPeriodText(String recruiterName, boolean isSubscriptionActivated) {
        return !isSubscriptionActivated ? "%s %s".formatted(recruiterName, TRIAL_PERIOD_TEXT) : recruiterName;
    }
}
