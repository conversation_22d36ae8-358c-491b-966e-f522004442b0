package com.erhgo.services.notifier.messages;

import com.erhgo.domain.userprofile.Location;
import com.erhgo.openapi.dto.UsersToNotifySelectionTypeDTO;
import com.erhgo.utils.StringUtils;
import lombok.Builder;
import lombok.Getter;

import java.time.Instant;
import java.util.Map;
import java.util.Optional;

@Getter
public class SourcingPublishRecruitmentMessageDTO extends AbstractNotifierMessageDTO {

    private static final Map<UsersToNotifySelectionTypeDTO, String> labelForSendNotificationRequired = Map.of(
            UsersToNotifySelectionTypeDTO.ALL, "- Notifications renvoyées également aux personnes déjà notifiées - ",
            UsersToNotifySelectionTypeDTO.NEW, "- Notifications envoyées seulement aux nouveaux utilisateurs - ",
            UsersToNotifySelectionTypeDTO.NONE, "- L'utilisateur n'a pas souhaité renvoyer de notifications."

    );

    @Builder
    public SourcingPublishRecruitmentMessageDTO(String recruiter,
                                                boolean isSubscriptionActivated,
                                                String jobTitle,
                                                Location location,
                                                Instant sendMailDate,
                                                int numberOfTotalNotification,
                                                int numberOfMobilesNotification,
                                                boolean republication,
                                                UsersToNotifySelectionTypeDTO sendMailRequired
    ) {
        var recruiterWithTrialPeriodText = getRecruiterNameWithTrialPeriodText(recruiter, isSubscriptionActivated);
        var optionalLocation = Optional.ofNullable(location);
        text = ":%s: l'organisation %s a %spublié le recrutement %s à %s (%s) %s".formatted(
                republication ? "recycle" : "email",
                recruiterWithTrialPeriodText,
                republication ? "re" : "",
                jobTitle,
                optionalLocation.map(Location::getCity).orElse(" ????ERREUR??? "),
                optionalLocation.map(Location::getPostcode).orElse(" ???Inconnu??? "),
                republication ? labelForSendNotificationRequired.get(sendMailRequired) : ""
        );
        if (sendMailRequired != UsersToNotifySelectionTypeDTO.NONE) {
            text += "%d notifications (dont %d sur mobiles)".formatted(numberOfTotalNotification, numberOfMobilesNotification);
            if (republication) {
                text += " diffusées immédiatement.";
            } else if (sendMailDate != null) {
                text += " diffusées après : %s".formatted(StringUtils.formatTimestamp(sendMailDate));
            }
        }
    }
}
