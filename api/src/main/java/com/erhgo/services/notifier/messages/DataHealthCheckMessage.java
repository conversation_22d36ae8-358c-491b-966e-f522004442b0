package com.erhgo.services.notifier.messages;

import com.erhgo.domain.configuration.DataHealthChecker;
import com.erhgo.services.datahealthchecker.DataHealthCheckerService.HealthCheckResultDTO;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public class DataHealthCheckMessage extends AbstractNotifierMessageDTO {

    private static final String LARGE_ORANGE_CIRCLE = ":large_orange_circle: ";
    private static final String RED_CIRCLE = ":red_circle: ";
    private final List<String> testMessages = new ArrayList<>();
    private int nbOfExecutionErrors = 0;
    private int nbOfFailedTests = 0;

    public DataHealthCheckMessage(Map<DataHealthChecker, HealthCheckResultDTO> testResults) {
        testResults.forEach((dhc, result) -> {
            if (!result.isSuccessful()) {
                if (result.hasExecutionProblem()) {
                    var text = "%sLe test %s n'a pas pu être exécuté correctement (raison: %s)".formatted(RED_CIRCLE, dhc.title(), result.getException().getMessage());
                    testMessages.add(text);
                    nbOfExecutionErrors += 1;
                } else {
                    var text = "%sLe test %s a échoué. (raison: %s, valeur obtenue : %s)"
                            .formatted(LARGE_ORANGE_CIRCLE, dhc.title(), dhc.errorMessage(), result.getFailedTestData());
                    testMessages.add(text);
                    nbOfFailedTests += 1;
                }
            }
        });

        this.text =
                testMessages.isEmpty() ?
                        "" :
                        "Il y a %d test(s) en échec ".formatted(nbOfFailedTests) +
                                "et %d erreur(s) d'exécution ".formatted(nbOfExecutionErrors) +
                                "sur %d tests :%n".formatted(testResults.size()) +
                                testMessages.stream().sorted().collect(Collectors.joining("\n"));
    }

}
