package com.erhgo.services.notifier.messages;

import lombok.Builder;
import lombok.Getter;

@Getter
public class EmailSendingNotifierMessageDTO extends AbstractNotifierMessageDTO {

    @Builder(builderMethodName = "builderForFailure", builderClassName = "builderForFailure")
    public EmailSendingNotifierMessageDTO(String notificationLabel) {
        this.text = ":warning: Echec d'envoi d'emails, aucun envoyé pour ";
        this.text += notificationLabel;

    }

    @Builder(builderMethodName = "builderForNoUser", builderClassName = "builderForNoUser")
    public EmailSendingNotifierMessageDTO(String recruitmentCode, String organizationCode, String recruitmentTitle, String recruiter) {
        this.text = ":information_source: Aucun utilisateur à notifier sur le recrutement de %s : ".formatted(recruiter);
        this.text += getRecruitmentLink(recruitmentTitle, recruitmentCode, organizationCode);
    }

    @Builder(builderMethodName = "builderForSuccess", builderClassName = "builderForSuccess")
    public EmailSendingNotifierMessageDTO(int successfulSentEmailsLength, String notificationLabel) {
        this.text = ":ok: Envoi de " + successfulSentEmailsLength + " emails réussis pour " + notificationLabel;
    }
}
