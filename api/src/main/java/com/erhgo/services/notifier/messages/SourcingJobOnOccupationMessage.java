package com.erhgo.services.notifier.messages;

import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.referential.Recruiter;
import lombok.Getter;

@Getter
public class SourcingJobOnOccupationMessage extends AbstractNotifierMessageDTO {

    private static final String ROTATING_LIGHT_EMOTE = ":rotating_light:";
    private static final String WHITE_CHECK_MARK_EMOTE = ":white_check_mark:";
    private static final String LARGE_ORANGE_CIRCLE = ":large_orange_circle:";
    private static final String RED_CIRCLE = ":red_circle:";

    public SourcingJobOnOccupationMessage(ErhgoOccupation erhgoOccupation, Recruiter recruiter, String recruitmentCode, String titleUsed, boolean isSubscriptionActivated) {
        var recruiterWithTrialPeriodText = getRecruiterNameWithTrialPeriodText(recruiter.getTitle(), isSubscriptionActivated);
        var organizationTextWithWhiteSpace = "de l'organisation %s ".formatted(recruiterWithTrialPeriodText);
        if (erhgoOccupation.isQualificationInFinalState()) {
            var text = new StringBuilder()
                    .append(WHITE_CHECK_MARK_EMOTE)
                    .append(" Un utilisateur ")
                    .append(getOrganizationLink(recruiter.getId(), organizationTextWithWhiteSpace))
                    .append(" a créé ")
                    .append(getRecruitmentLink(recruitmentCode, recruiter.getCode()))
                    .append(" sur le métier (qualification confirmée) ")
                    .append(getOccupationLink(erhgoOccupation.getTitle(), erhgoOccupation.getId()))
                    .append(titleUsed)
                    .toString();
            updateMessageText(text);
        } else {
            var text = new StringBuilder()
                    .append(ROTATING_LIGHT_EMOTE)
                    .append(" Un utilisateur ")
                    .append(getOrganizationLink(recruiter.getId(), organizationTextWithWhiteSpace))
                    .append(" a créé ")
                    .append(getRecruitmentLink(recruitmentCode, recruiter.getCode()))
                    .append(" sur le métier (*qualification NON CONFIRMÉE* - état : %s) ".formatted(erhgoOccupation.getQualificationState().name()))
                    .append(getOccupationLink(erhgoOccupation.getTitle(), erhgoOccupation.getId()))
                    .append(titleUsed)
                    .toString();

            updateMessageText(text);
        }


        if (erhgoOccupation.isTechnical()) {
            if (erhgoOccupation.hasNoCode()) {
                var text = new StringBuilder()
                        .append(RED_CIRCLE)
                        .append(" Le métier est technique et ne dispose pas de code ROME ni de code ISCO")
                        .toString();
                updateMessageText(text);
            } else {
                var text = new StringBuilder()
                        .append(LARGE_ORANGE_CIRCLE)
                        .append(" Le métier est technique (")
                        .append(erhgoOccupation.getRomeOccupations().size())
                        .append(" code(s) ROME, ")
                        .append(erhgoOccupation.getEscoOccupations().size())
                        .append(" code(s) ISCO)")
                        .toString();
                updateMessageText(text);
            }
        }

    }

    private void updateMessageText(String text) {
        if (this.text == null) {
            this.text = text;
        } else {
            this.text += "%n%s".formatted(text);
        }
    }


}
