package com.erhgo.services.notifier;

import com.erhgo.services.notifier.messages.AbstractNotifierMessageDTO;
import com.slack.api.Slack;
import com.slack.api.methods.SlackApiException;
import com.slack.api.methods.request.chat.ChatPostMessageRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Slf4j
@Service
class SlackNotifier {

    @Value("${slack.token}")
    private String slackToken;

    public void sendMessage(String channelId, AbstractNotifierMessageDTO slackMessage) {
        try {
            var message = ChatPostMessageRequest.builder().channel(channelId).text(slackMessage.getText()).build();
            Slack.getInstance().methods(slackToken).chatPostMessage(message);
            log.debug("Sending message {} to Slack", message);
        } catch (RuntimeException | IOException | SlackApiException e) {
            log.error("Error when sending message to slack channel {}: {}", channelId, e.getMessage(), e);
        }
    }


}
