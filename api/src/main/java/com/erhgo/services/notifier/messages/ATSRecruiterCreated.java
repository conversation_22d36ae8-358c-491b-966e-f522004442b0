package com.erhgo.services.notifier.messages;

import lombok.Getter;

@Getter
public class ATSRecruiterCreated extends AbstractNotifierMessageDTO {

    public ATSRecruiterCreated(String recruiter, String code, String ats, String who) {
        this.text = ":bulb: Le recruteur %s (code %s) a été créé depuis l'ATS %s \n\t\tContact : %s".formatted(recruiter, code, ats, who);
    }

    @Override
    public boolean isImmediate() {
        return true;
    }
}
