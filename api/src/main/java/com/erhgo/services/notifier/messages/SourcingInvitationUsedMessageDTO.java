package com.erhgo.services.notifier.messages;

import com.erhgo.domain.referential.Recruiter;
import lombok.Getter;

@Getter
public class SourcingInvitationUsedMessageDTO extends AbstractNotifierMessageDTO {

    public SourcingInvitationUsedMessageDTO(Recruiter recruiter, String code, boolean isSubscriptionActivated) {
        var recruiterNameWithTrialPeriodText = getRecruiterNameWithTrialPeriodText(recruiter.getTitle(), isSubscriptionActivated);
        text = ":heavy_dollar_sign: l'organisation %s a utilisé le code d'activation %s".formatted(recruiterNameWithTrialPeriodText, code);

    }


}
