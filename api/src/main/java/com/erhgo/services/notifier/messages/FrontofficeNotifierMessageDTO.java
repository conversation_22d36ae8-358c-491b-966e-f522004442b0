package com.erhgo.services.notifier.messages;

import com.erhgo.openapi.dto.UserChannelSourceDTO;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import lombok.Builder;
import lombok.Getter;

import java.util.Set;

@Getter
public class FrontofficeNotifierMessageDTO extends AbstractNotifierMessageDTO {
    @Builder(builderMethodName = "builderForAccountCreation")
    public FrontofficeNotifierMessageDTO(String text, String userId, String iconEmoji) {
        this.text = iconEmoji + " *" + text + " _" + formatUserId(userId) + "_* ";
    }

    @Builder(builderClassName = "builderForAssignToChannel", builderMethodName = "builderForAssignToChannel")
    public FrontofficeNotifierMessageDTO(String text,
                                         String userId,
                                         String postCode,
                                         String city,
                                         String canalKey,
                                         Set<String> organizationNames,
                                         UserChannelSourceDTO channel,
                                         String iconEmoji) {
        var location = getLocation(postCode, city);
        switch (channel) {
            case OTHER:
            case NOTHING:
            case CANDIDATURE:
            case NOT_UNDERSTOOD:
                this.text = iconEmoji + " *" + text + " _" + formatUserId(userId) + "_* " + location + "affecté(e) à aucun canal - choix \"" + channel.name() + "\"";
                break;
            default:
                if (canalKey == null) {
                    break;
                }
                var fromChannel = "depuis la page d'accueil *_" + canalKey + "_* ";
                if (!organizationNames.isEmpty()) {
                    fromChannel += (organizationNames.size() > 1 ? "des canaux" : "du canal") + " *_" + Joiner.on(", ").join(organizationNames) + "_*";
                }
                this.text = iconEmoji + " *" + text + " _" + userId + "_* " + location + fromChannel;
                break;
        }

    }

    private String getLocation(String postCode, String city) {
        return Strings.isNullOrEmpty(city) && Strings.isNullOrEmpty(postCode) ? "" : "( _" + postCode + " - " + city + "_ ) ";
    }

}
