package com.erhgo.services.sourcing;

import com.erhgo.domain.candidature.job.AbstractCandidature;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.candidature.job.SpontaneousCandidature;
import com.erhgo.domain.enums.CandidatureSynchronizationState;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.sourcing.SourcingPreferences;
import com.erhgo.domain.sourcing.SourcingSubscription;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.openapi.dto.SendContactFormCommandDTO;
import com.erhgo.repositories.*;
import com.erhgo.services.SecurityService;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.SourcingKeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailingListService;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import com.erhgo.utils.DateTimeUtils;
import com.google.common.base.Joiner;
import com.google.common.collect.HashMultimap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SourcingMailingService {

    private final MailingListService mailingListService;
    private final SecurityService securityService;
    private final SourcingKeycloakService sourcingKeycloakService;
    private final KeycloakService keycloakService;
    private final RecruitmentRepository recruitmentRepository;
    private final SourcingPreferencesRepository sourcingPreferencesRepository;
    private final SourcingSubscriptionRepository sourcingSubscriptionRepository;
    private final SpontaneousCandidatureRepository spontaneousCandidatureRepository;
    private final UserProfileCompetencesExportService userProfileCompetencesExportService;
    private final TransactionTemplate transactionTemplate;
    private final AbstractCandidatureRepository abstractCandidatureRepository;

    @Value("${sendinblue.templates.invite-user-sourcing}")
    private Long sendEmailToInviteUserTemplate;

    @Value("${sendinblue.templates.contact-sourcing}")
    private long contactTemplateId;

    @Value("${sendinblue.templates.remind-recruitment-to-recruiters}")
    private long remindOpenRecruitmentToRecruiters;

    @Value("${sendinblue.templates.technical-warning-sourcing}")
    private long technicalWarningSourcing;

    @Value("${sendinblue.templates.sourcing-welcome-trial-users}")
    private Long welcomeTrialUsersTemplateId;

    @Value("${sendinblue.templates.sourcing-warning-end-trial-users}")
    private Long warningEndTrialUsers;

    @Value("${sendinblue.supportEmail}")
    private String supportEmail;

    @Value("${keycloak-realms.sourcing_base_url}")
    private String sourcingUrl;

    @Value("${application.sourcing.nbDaysBeforeSendingWelcome}")
    private int nbDaysBeforeSendingWelcome;

    @Value("${application.sourcing.nbDaysBeforeSendingWarningEndOfTrial}")
    private int nbDaysBeforeSendingWarningEndOfTrial;

    @Value("${sendinblue.templates.sourcing-candidature-notification}")
    private Long sourcingRecruitmentCandidatureNotificationTemplate;

    @Value("${sendinblue.templates.sourcing-spontaneous-candidature-notification}")
    private Long sourcingSpontaneousCandidatureNotificationTemplate;

    @Value("${sendinblue.templates.suspend-sourcing-recruitment-after-end-date}")
    private Long suspendSourcingRecruitmentAfterEndDateTemplate;

    public void sendContactForm(SendContactFormCommandDTO command) {
        var authenticatedUser = Optional.ofNullable(securityService.getAuthenticatedUserId()).map(sourcingKeycloakService::getSourcingUserWithGroups);
        mailingListService.sendMailsForTemplate(Set.of(supportEmail), contactTemplateId, Map.of(
                "context", command.getContext(),
                "hasAccount", authenticatedUser.isEmpty() ? "non" : "oui",
                "userEmail", authenticatedUser.map(UserRepresentation::getEmail).orElse(command.getUserEmail()),
                "fullname", authenticatedUser.map(UserRepresentation::getFullname).orElse(command.getFullname()),
                "content", command.getContent()
        ), null);
    }

    @Transactional
    public void remindSourcingRecruitmentsInfo() {
        var candidaturesForUserId = buildCandidaturesForUserIdMultimap();
        log.info("Prepare sending mail to {} users to remind current recruitments", candidaturesForUserId.keySet().size());
        candidaturesForUserId.asMap().forEach((userId, notifiableCandidatures) ->
                sourcingKeycloakService.getSourcingUser(userId).ifPresentOrElse(
                        user -> doSendEmailRemindToUser(notifiableCandidatures, user),
                        () -> log.warn("user {} not found in sourcing realm", userId)
                ));
    }

    private void doSendEmailRemindToUser(Collection<AbstractCandidature> notifiableCandidatures, UserRepresentation user) {
        var recruiterTitle = notifiableCandidatures.stream().map(AbstractCandidature::getRecruiter)
                .map(Recruiter::getTitle).distinct().collect(Collectors.joining(","));
        sendMailsToRecipientsOfRecruiter(
                recruiterTitle,
                notifiableCandidatures,
                user
        );
    }

    private HashMultimap<String, AbstractCandidature> buildCandidaturesForUserIdMultimap() {
        var recruitmentCandidaturesForUserId = buildRecruitmentCandidaturesForUserId();
        var spontaneousCandidaturesForUserId = buildSpontaneousCandidaturesForUserId();

        var allPreferences = sourcingPreferencesRepository.findAll();

        var usersNOTToNotifyOnSpontaneousCandidature = allPreferences.stream()
                .filter(Predicate.not(SourcingPreferences::notifyOnSpontaneousCandidature))
                .map(SourcingPreferences::userId)
                .toList();
        spontaneousCandidaturesForUserId.keySet().stream().filter(usersNOTToNotifyOnSpontaneousCandidature::contains).toList().forEach(spontaneousCandidaturesForUserId::removeAll);
        var allCandidaturesForUserId = HashMultimap.<String, AbstractCandidature>create();

        allCandidaturesForUserId.putAll(recruitmentCandidaturesForUserId);
        allCandidaturesForUserId.putAll(spontaneousCandidaturesForUserId);
        userIdsNotRequiringNotificationNow(recruitmentCandidaturesForUserId.keySet(), allPreferences).forEach(allCandidaturesForUserId::removeAll);

        return allCandidaturesForUserId;
    }

    @NotNull
    private HashMultimap<String, AbstractCandidature> buildSpontaneousCandidaturesForUserId() {
        var spontaneousCandidaturesForUserId = HashMultimap.<String, AbstractCandidature>create();
        spontaneousCandidatureRepository.findCandidaturesWithAtLeastOneUntreatedCandidatureOnRecruiter()
                .forEach(c -> sourcingKeycloakService.getEnabledSourcingUsersForGroup(c.getCodeOfRecruiter()).forEach(user -> spontaneousCandidaturesForUserId.put(user.getId(), c)));
        return spontaneousCandidaturesForUserId;
    }

    @NotNull
    private HashMultimap<String, AbstractCandidature> buildRecruitmentCandidaturesForUserId() {
        var recruitmentCandidaturesForUserId = HashMultimap.<String, AbstractCandidature>create();
        recruitmentRepository.findCandidaturesOnOpenRecruitmentsWithAtLeastOneUntreatedCandidatureOnRecruiter()
                .forEach(c -> c.getRecruitment().getSourcingUsersIdToNotify().forEach(userId -> recruitmentCandidaturesForUserId.put(userId, c)));
        return recruitmentCandidaturesForUserId;
    }

    private Collection<String> userIdsNotRequiringNotificationNow(Collection<String> userIds, List<SourcingPreferences> allPreferences) {
        return userIds.stream()
                .filter(userId -> !allPreferences
                        .stream()
                        .filter(p -> p.userId().equals(userId))
                        .findFirst()
                        .map(SourcingPreferences::isActiveToday)
                        .orElse(false)
                )
                .toList();
    }

    public void sendMailsToRecipientsOfRecruiter(String recruiterName, Collection<AbstractCandidature> candidaturesForUser, UserRepresentation user) {
        var paramsTemplateRecruitmentCandidature = buildMailParamFromRecruitmentCandidatures(recruiterName, candidaturesForUser);
        var params = new HashMap<>(paramsTemplateRecruitmentCandidature);
        var paramsTemplateSpontaneousCandidature = buildMailParamFromSpontaneousCandidatures(candidaturesForUser);

        params.put("FULLNAME", user.getFullname());
        params.putAll(paramsTemplateSpontaneousCandidature);
        mailingListService.sendMailsForTemplate(Set.of(user.getEmail()), remindOpenRecruitmentToRecruiters, params, null);
    }

    private Map<String, String> buildMailParamFromSpontaneousCandidatures(Collection<AbstractCandidature> candidaturesForUser) {
        var spontaneousCandidatures = candidaturesForUser.stream()
                .filter(Predicate.not(AbstractCandidature::isRecruitmentCandidature))
                .map(c -> (SpontaneousCandidature) c)
                .collect(Collectors.toSet());
        return spontaneousCandidatures.isEmpty() ?
                Map.of() :
                Map.of(
                        "TOTAL_SPONTANEOUS_CANDIDATURES", String.valueOf(spontaneousCandidatures.size()),
                        "SPONTANEOUS_LINK", sourcingUrl + "/#/sourcing",
                        "NEW_SPONTANEOUS_CANDIDATURES", String.valueOf(spontaneousCandidatures.stream().filter(c -> c.getGlobalCandidatureState().isNew()).count())
                );
    }

    private Map<String, String> buildRecruitmentParam(Map.Entry<Recruitment, List<RecruitmentCandidature>> entry) {
        return Map.of(
                "LINK", getRecruitmentLink(entry.getKey().getId()),
                "TITLE", entry.getKey().getJob().getTitle(),
                "LOCATION", Optional.ofNullable(entry.getKey().getLocation()).map(Location::getCity).orElse(""),
                "TOTAL_CANDIDATURES", String.valueOf(entry.getValue().size()),
                "NEW_CANDIDATURES", String.valueOf(entry.getValue().stream().filter(c -> c.getGlobalCandidatureState().isNew()).count()));
    }

    @NotNull
    private String getRecruitmentLink(long id) {
        return sourcingUrl + "/#/recruitment-detail/%d".formatted(id);
    }

    @NotNull
    private String getSpontaneousCandidaturesLink() {
        return sourcingUrl + "/#/sourcing";
    }

    public Map<String, Object> buildMailParamFromRecruitmentCandidatures(String recruiterName, Collection<AbstractCandidature> candidaturesForUser) {
        var candidaturesForRecruiter = candidaturesForUser.stream()
                .filter(AbstractCandidature::isRecruitmentCandidature)
                .map(c -> (RecruitmentCandidature) c)
                .collect(Collectors.groupingBy(RecruitmentCandidature::getRecruitment));

        var params = new HashMap<String, Object>();
        params.put("ORGANIZATION_NAME", recruiterName);
        var candidaturesParam = candidaturesForRecruiter.entrySet().stream().map(this::buildRecruitmentParam)
                .sorted(Comparator.comparing(a -> a.get("LINK")))
                .toList();
        params.put("CANDIDATURES", candidaturesParam);
        return params;
    }


    public void sendEmailToInviteUserSourcing(String email, String password, String organizationTitle, String sender) {

        var parameters = Map.of(
                "SENDER_FULLNAME", sender,
                "LOGIN", email,
                "PASSWORD", password,
                "ORGANIZATION_TITLE", organizationTitle,
                "SOURCING_URL", sourcingUrl);
        mailingListService.sendMailsForTemplate(Set.of(email), sendEmailToInviteUserTemplate, parameters, null);
    }

    @Transactional
    public void sendTrialWelcomeMails() {

        var date = DateTimeUtils.startOfTodayMinusDaysToDate(nbDaysBeforeSendingWelcome - 1);

        var subscriptions = sourcingSubscriptionRepository
                .findByCreatedDateBeforeAndMailStateAndInvitationIsNull(date, SourcingSubscription.SourcingMailState.SEND_WELCOME);
        log.debug("Sending trial mail welcome for {} subscriptions", subscriptions.size());
        subscriptions
                .forEach(s -> s.welcomeMailSent(this.sendEmailToTemplateForOrganization(s.getRecruiter(), welcomeTrialUsersTemplateId, Map.of(
                        "WRONG_SIRET", String.valueOf(s.getRecruiter().isInvalidSiret()),
                        "SIRET", StringUtils.trimToEmpty(s.getRecruiter().getSiret()),
                        "SOURCING_URL", sourcingUrl
                ))));
    }

    @Transactional
    public void sendTrialEndMails() {
        var date = LocalDate.now().atStartOfDay().atOffset(DateTimeUtils.zoneOffset());

        var subscriptions = sourcingSubscriptionRepository
                .findByExpirationDateBeforeAndMailStateAndInvitationIsNull(date, SourcingSubscription.SourcingMailState.SEND_TRIAL_END);
        log.debug("Sending trial mail end for {} subscriptions", subscriptions.size());
        subscriptions
                .forEach(s -> s.welcomeMailSent(this.sendEmailToTemplateForOrganization(s.getRecruiter(), warningEndTrialUsers, Map.of(
                        "SOURCING_URL", sourcingUrl
                ), supportEmail)));
    }

    private boolean sendEmailToTemplateForOrganization(Recruiter recruiter, long templateId, Map<String, String> parameters, String... forcedEmails) {
        var emails = sourcingKeycloakService.getEnabledSourcingUsersForGroup(recruiter.getCode()).stream().map(UserRepresentation::getEmail).collect(Collectors.toSet());
        try {
            return mailingListService.sendMailsForTemplate(emails, templateId, parameters, null, forcedEmails).get().size() == emails.size();
        } catch (InterruptedException e) {
            log.error("Interrupted when sending mails for id {}", templateId, e);
            Thread.currentThread().interrupt();
            return false;
        } catch (ExecutionException e) {
            log.error("Error when sending mails for id {}; message= {}", templateId, e.getMessage(), e);
            return false;
        }
    }

    private void sendImmediateNotificationToSourcingUsers(RecruitmentCandidature candidature, List<SourcingPreferences> allPreferences) {
        if (candidature.isHandledByAts()) {
            log.info("Skipping recruiter email notification for candidature for an external offer.");
            return;
        }
        var usersNOTToNotify = allPreferences.stream().filter(SourcingPreferences::isNotImmediate).map(SourcingPreferences::userId).collect(Collectors.toSet());
        log.debug("Ignoring usersIds {} on recruitmentCandidature notif {}", usersNOTToNotify, candidature.getId());
        var location = Optional.ofNullable(candidature.getRecruitment().getLocation())
                .map(Location::getCity).map(" (%s) "::formatted)
                .orElse("");
        var mailParameters = Map.of(
                    "TITLE", candidature.getJobTitle() + location,
                "LINK", getRecruitmentLink(candidature.getRecruitment().getId())
            );

        var templateId = sourcingRecruitmentCandidatureNotificationTemplate;

        sendImmediateNotifications(
                candidature,
                templateId,
                id -> !usersNOTToNotify.contains(id) && candidature.getRecruitment().getSourcingUsersIdToNotify().contains(id),
                mailParameters);
    }


    private void sendImmediateNotifications(
            AbstractCandidature candidature,
            Long templateId,
            Predicate<String> userToIncludeFilter,
            Map<String, String> mailParameters
    ) {
        if (!candidature.requiresSourcingSync()) {
            log.debug("Not trying to send notification for candidature {}.", candidature.getId());
            setSyncState(candidature, CandidatureSynchronizationState.IGNORE, null);
            return;
        }
        var sourcingUsersForRecruiter = sourcingKeycloakService.getEnabledSourcingUsersForGroup(candidature.getCodeOfRecruiter());
        var emailsToNotify = sourcingUsersForRecruiter
                .stream()
                .filter(user -> userToIncludeFilter.test(user.getId()))
                .map(UserRepresentation::getEmail)
                .collect(Collectors.toSet());

        if (!emailsToNotify.isEmpty()) {
            setSyncState(candidature, CandidatureSynchronizationState.PENDING, null);
            try {
                var filePartProvider = userProfileCompetencesExportService.getProfileCompetenceForBatch(candidature.getId(), keycloakService.getFrontOfficeUserProfile(candidature.getUserId()).map(UserRepresentation::getFullname).orElse(""), ProfileCompetencesViewObject.AnonymousMode.BOTH);
                mailingListService
                        .sendMailsForTemplate(emailsToNotify, templateId, mailParameters, filePartProvider)
                        .thenAccept(mailSent -> {
                            if (!mailSent.isEmpty()) {
                                setSyncState(candidature, CandidatureSynchronizationState.DONE, Joiner.on(",").join(emailsToNotify));
                            } else {
                                log.warn("No emails to send for candidature {} - {} were all rejected", candidature.getId(), emailsToNotify);
                                setSyncState(candidature, CandidatureSynchronizationState.ERROR, null);
                            }
                        })
                        .exceptionally(e -> {
                            log.error("Error when sending emails for candidature {} - {}", candidature.getId(), emailsToNotify, e);
                            setSyncState(candidature, CandidatureSynchronizationState.ERROR, null);
                            return null;
                        })
                ;
            } catch (IOException e) {
                throw new GenericTechnicalException("Failed to generate competence profile for candidature %d".formatted(candidature.getId()), e);
            }
        } else {
            log.info("Ignore candidature {} - nobody to notify immediately", candidature.getId());
            setSyncState(candidature, CandidatureSynchronizationState.IGNORE, null);
        }
    }

    private void sendImmediateNotificationToSourcingUsers(SpontaneousCandidature candidature, List<SourcingPreferences> allPreferences) {
        var templateId = sourcingSpontaneousCandidatureNotificationTemplate;
        if (templateId != null) {
            var usersNOTToNotify = allPreferences.stream()
                    .filter(a -> a.isNotImmediate() || !a.notifyOnSpontaneousCandidature())
                    .map(SourcingPreferences::userId)
                    .collect(Collectors.toSet());
            log.debug("Ignoring usersIds {} on spontaneousCandidature notif {}", usersNOTToNotify, candidature.getId());
            var mailParameters = Map.of(
                    "LINK", getSpontaneousCandidaturesLink()
            );
            sendImmediateNotifications(candidature, templateId, id -> !usersNOTToNotify.contains(id), mailParameters);
        } else {
            log.error("No notification sent on spontaneous candidature - no template configured");
        }
    }

    public void sendEmailAboutRecruitmentSuspensionAfterEndDate(Recruitment recruitment) {
        if (recruitment.relatesToExternalOffer()) {
            log.info("Skipping email notification for recruitment suspension as it is an external offer.");
            return;
        }
        var emails = recruitment.getSourcingUsersIdToNotify().stream()
                .map(sourcingKeycloakService::getSourcingUser)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .map(UserRepresentation::getEmail)
                .collect(Collectors.toSet());
        var jobTitle = recruitment.getJobTitle();

        var parameters = Map.of("poste", jobTitle);

        mailingListService.sendMailsForTemplate(emails, suspendSourcingRecruitmentAfterEndDateTemplate, parameters, null);
    }

    protected void setSyncState(AbstractCandidature c, CandidatureSynchronizationState state, String email) {
        c.setSynchronizationState(state);
        if (StringUtils.isNotBlank(email)) c.addRemoteNotifiedIdentifier(email);
        abstractCandidatureRepository.save(c);
    }


    public void sendImmediateNotificationToSourcingUsers(AbstractCandidature abstractCandidature, List<SourcingPreferences> allPreferences) {
        try {
            if (abstractCandidature.isRecruitmentCandidature()) {
                sendImmediateNotificationToSourcingUsers((RecruitmentCandidature) abstractCandidature, allPreferences);
            } else {
                sendImmediateNotificationToSourcingUsers((SpontaneousCandidature) abstractCandidature, allPreferences);
            }
        } catch (RuntimeException e) {
            log.error("Unable to send candidature {} notification ", abstractCandidature.getId(), e);
            setSyncState(abstractCandidature, CandidatureSynchronizationState.ERROR, null);
        }
    }
}
