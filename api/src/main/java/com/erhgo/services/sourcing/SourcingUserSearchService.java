package com.erhgo.services.sourcing;

import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.sourcing.SourcingPreferences;
import com.erhgo.repositories.SourcingPreferencesRepository;
import com.erhgo.services.keycloak.SourcingKeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.utils.StringSimilarityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class SourcingUserSearchService {

    private final SourcingKeycloakService sourcingKeycloakService;
    private final SourcingPreferencesRepository preferencesRepository;

    public List<UserRepresentation> searchUserInGroup(String groupName, List<String> query) {
        if (query == null || StringUtils.isAllBlank(query.toArray(new String[0]))) {
            return Collections.emptyList();
        }

        var usersByFullname = getEnabledUserStream(groupName)
                .collect(Collectors.groupingBy(a -> "%s %s".formatted(StringUtils.trimToEmpty(a.getFullname()), a.getEmail())));

        return query.stream().filter(StringUtils::isNotBlank).map(q -> searchUserInGroup(usersByFullname, q, groupName))
                .filter(Objects::nonNull).distinct().toList();

    }

    private UserRepresentation searchUserInGroup(Map<String, List<UserRepresentation>> usersByFullname, String query, String groupName) {
        var mostSimilarFullname = StringSimilarityUtils.getTheMostSimilarStringWithAtLeastOneCommonWord(query, usersByFullname.keySet());
        if (StringUtils.isBlank(mostSimilarFullname)) {
            log.info("No user found for group {} and query {}", groupName, query);
            return null;
        }
        var mostSimilarUsers = usersByFullname.getOrDefault(mostSimilarFullname, Collections.emptyList());
        if (mostSimilarUsers.isEmpty()) {
            log.info("No such user found in group {} for {}", groupName, mostSimilarFullname);
            return null;
        }
        return mostSimilarUsers.getFirst();
    }

    public String findDefaultManagerFor(Recruitment recruitment) {
        var allIds = getRecruiterUsersSortedByCreationDateDesc(recruitment);
        String result = null;
        if (!allIds.isEmpty()) {
            var defaultManager = allIds.getFirst();
            var allPrefsPerUserId = preferencesRepository.findByUserIdIn(allIds).stream().collect(Collectors.toMap(SourcingPreferences::userId, Function.identity()));

            result = allIds
                    .stream()
                    .filter(id -> isUserNotifiable(id, allPrefsPerUserId))
                    .findFirst()
                    .orElseGet(() -> {
                        log.info("Enabled users have disabled candidature notification - using {} as manager, but nobody will be notified on recruitment {} ", defaultManager, recruitment.getId());
                        return defaultManager;
                    });
        }
        log.debug("Considering user {} as default manager for recruitment {}", result, recruitment.getId());
        return result;
    }

    private @NotNull List<String> getRecruiterUsersSortedByCreationDateDesc(Recruitment recruitment) {
        return getEnabledUserStream(recruitment.getRecruiterCode()).sorted(Comparator.comparing(UserRepresentation::getCreatedTimestamp, Comparator.nullsLast(Comparator.naturalOrder()))).map(UserRepresentation::getId).toList();
    }

    private static boolean isUserNotifiable(String id, Map<String, SourcingPreferences> allPrefsPerUserId) {
        return !allPrefsPerUserId.containsKey(id) || !allPrefsPerUserId.get(id).hasRefuseNotification();
    }

    private @NotNull Stream<UserRepresentation> getEnabledUserStream(String groupName) {
        return sourcingKeycloakService.getEnabledSourcingUsersForGroup(groupName)
                .stream().filter(UserRepresentation::getEnabled);
    }
}
