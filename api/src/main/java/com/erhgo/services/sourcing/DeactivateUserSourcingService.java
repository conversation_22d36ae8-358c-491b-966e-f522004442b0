package com.erhgo.services.sourcing;

import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.repositories.RecruitmentRepository;
import com.erhgo.repositories.SourcingPreferencesRepository;
import com.erhgo.security.Role;
import com.erhgo.services.keycloak.SourcingKeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;

@Service
@RequiredArgsConstructor
@Slf4j
public class DeactivateUserSourcingService {

    private final SourcingKeycloakService sourcingKeycloakService;
    private final SourcingPreferencesRepository sourcingPreferencesRepository;
    private final RecruitmentRepository recruitmentRepository;

    @Transactional
    @RolesAllowed(value = Role.ODAS_ADMIN)
    public void deactivateUser(String userIdToDeactivate, String replacementUserId) {
        sourcingKeycloakService.getSourcingUser(userIdToDeactivate)
                .ifPresentOrElse(
                        u -> sourcingKeycloakService.disableUser(userIdToDeactivate),
                        () -> {
                            throw new EntityNotFoundException(userIdToDeactivate, UserRepresentation.class);
                        }
                );
        cleanupSourcingPreferences(userIdToDeactivate);
        transferRecruitmentManagement(userIdToDeactivate, replacementUserId);
        updateRecruitmentNotifiedUsers(userIdToDeactivate, replacementUserId);
    }

    @Transactional
    @RolesAllowed(value = Role.ODAS_ADMIN)
    public void activateUser(String userId) {

        sourcingKeycloakService.getSourcingUser(userId)
                .ifPresentOrElse(
                        u -> sourcingKeycloakService.enableUser(userId),
                        () -> {
                            throw new EntityNotFoundException(userId, UserRepresentation.class);
                        }
                );
    }

    private void cleanupSourcingPreferences(String userId) {
        sourcingPreferencesRepository.findByUserId(userId)
                .ifPresent(prefs -> {
                    sourcingPreferencesRepository.delete(prefs);
                    log.debug("Deleted sourcing preferences for user {}", userId);
                });
    }

    private void transferRecruitmentManagement(String userIdToDeactivate, String replacementUserId) {
        var activeRecruitments = recruitmentRepository.findActiveRecruitmentsByManagerUserId(userIdToDeactivate);
        if (activeRecruitments.isEmpty()) {
            log.debug("No recruitment associated to deactivated sourcing user {}",
                    userIdToDeactivate);
            return;
        }

        activeRecruitments.forEach(recruitment -> {
            recruitment.setManagerUserId(replacementUserId);
            log.debug("Updated recruitment {}: user {} replaced by {}",
                    recruitment.getId(), userIdToDeactivate, replacementUserId);
        });
    }

    private void updateRecruitmentNotifiedUsers(String userIdToDeactivate, String replacementUserId) {
        var recruitments = recruitmentRepository.findRecruitmentsByNotifiedUser(userIdToDeactivate);

        if (recruitments.isEmpty()) {
            log.debug("Skip updating notifications, no recruitments found for user {}", userIdToDeactivate);
            return;
        }

        recruitments.forEach(recruitment -> {
            var notifiedUsers = new HashSet<>(recruitment.getSourcingUsersIdToNotify());
            if (!StringUtils.isBlank(replacementUserId)) {
                notifiedUsers.add(replacementUserId);
            }
            notifiedUsers.remove(userIdToDeactivate);
            recruitment.setSourcingUsersIdToNotify(notifiedUsers);
            log.debug("Updated notifications for recruitment {}: removed {}, added {}",
                    recruitment.getId(), userIdToDeactivate, replacementUserId);
        });
    }

}
