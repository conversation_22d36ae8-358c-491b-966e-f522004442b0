package com.erhgo.services.notes;

import com.erhgo.domain.userprofile.UserNote;
import com.erhgo.repositories.CandidatureNoteRepository;
import com.erhgo.repositories.UserNoteRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Slf4j
@Service
public class ObsoleteNoteHandlerScheduler {


    private final UserNoteRepository userNoteRepository;

    private final CandidatureNoteRepository candidatureNoteRepository;

    @Value("${application.notesLifespanInMonth}")
    private int notesLifespanInMonth;

    @Scheduled(cron = "${application.updateObsoleteNotes.cron}", zone = "Europe/Paris")
    @SchedulerLock(name = "checkNotesAreUpToDateScheduler")
    @Transactional
    public void checkNotesAreUpToDate() {
        log.info("Start obsolete notes update with notesLifespanInMonth = {}", notesLifespanInMonth);
        var userNotes = userNoteRepository.findByLastUpdatedWithinLastMonths(notesLifespanInMonth);
        var candidatureNotes = candidatureNoteRepository.findByLastUpdatedWithinLastMonths(notesLifespanInMonth);

        userNotes.forEach(n -> n.setContent(UserNote.REMOVED_NOTE_TEXT));
        candidatureNotes.forEach(n -> n.setText(UserNote.REMOVED_NOTE_TEXT));

        userNoteRepository.saveAll(userNotes);
        candidatureNoteRepository.saveAll(candidatureNotes);
    }
}
