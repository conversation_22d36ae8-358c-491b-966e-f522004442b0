package com.erhgo.services.dto.criteria;

import lombok.Builder;
import lombok.Data;

import java.util.Collection;

@Data
@Builder
public class MatchingJobsCriteria {
    private final Float capacityThreshold;
    private final Collection<String> organizationCodes;
    private final Collection<String> criteriaCodes;
    private final Float masteryLevelRange;
    private final int pageSize;
    private final int pageNumber;
    private final String postcode;
    private final Boolean strictOrganizationFilter;
    private final Boolean isAffectedToNoChannel;
    private final Boolean hasToFilterOnSourcingRecruitment;
}
