package com.erhgo.services.dto.criteria;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum Effort {

    VERY_STRONG(0f, false, false),

    STRONG(0.5f, false, false),

    MEDIUM(0.7f, false, false),

    WEAK(0.8f, true, false),

    VERY_WEAK(0.85f, true, false),

    IGNORE(0.9f, true, true);

    private final float capacityThreshold;

    private boolean withoutHighLevelOccupations;

    private boolean withoutMediumLevelOccupations;

}
