package com.erhgo.services.dto.criteria;

import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.TypeContractCategory;
import com.erhgo.domain.enums.TypeWorkingTime;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.repositories.AbstractOrganizationRepository;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Data
@Accessors(fluent = true)
@Slf4j
@Component
@RequiredArgsConstructor
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class SourcingCandidatesCriteria {

    // Used to adjust the range of mastery level search to account for potential floating-point precision issues in the database
    public static final double FLOATING_POINT_PRECISION_ADJUSTMENT = 0.0001;
    public static final int NUMBER_OF_USER_FOR_TOP_TEN = 10;
    public static final Long NO_LIMIT_MAGIC_INT = Long.MAX_VALUE;
    @Value("${sourcing.capacityTolerance}")
    private float defaultCapacityTolerance;
    @Value("${sourcing.salaryMinToleranceRatio}")
    private float salaryMinToleranceRatio;
    @Value("${sourcing.salaryMaxToleranceRatio}")
    private float salaryMaxToleranceRatio;
    @Value("${sourcing.topTenUpperDelta}")
    private float topTenUpperDelta;
    @Value("${sourcing.topTenLowerDelta}")
    private float topTenLowerDelta;
    @Autowired
    private AbstractOrganizationRepository abstractOrganizationRepository;


    private UUID occupationId;
    private Collection<Long> capacitiesIds = new ArrayList<>();
    private Float longitude;
    private Float latitude;
    private TypeContractCategory typeContractCategory;
    private List<String> criteria = new ArrayList<>();
    private List<String> classifications = new ArrayList<>();
    private Integer salaryMin;
    private Integer salaryMax;
    private Set<String> refusedChannels = new HashSet<>();
    private Collection<String> mandatoryRomeCodesPrefix = new HashSet<>();
    private Collection<String> optionalRomeCodes = new HashSet<>();
    private Long lastConnectionTimestampInSeconds;
    private LocalDateTime lastConnectionBefore;
    private boolean activeSearch = true;
    private boolean excludesNotified = true;
    private boolean withDetails;
    private Integer masteryLevelAround;
    private boolean topTen;
    private Recruitment excludesRecruitment;
    private Double customCapacityTolerance;

    public SourcingCandidatesCriteria addRomeAndIscoCodes(ErhgoOccupation occupation) {
        if (occupation != null) {
            var romeCodes = occupation.getRomeCodes();
            if (occupation.isTechnical()) {
                this.mandatoryRomeCodesPrefix(romeCodes.stream().map(a -> a.substring(0, 3)).collect(Collectors.toSet()));
                if (romeCodes.isEmpty()) {
                    log.warn("Occupation {} is technical but has no rome codes associated", occupation.getId());
                }
            }
            if (this.topTen()) {
                this.optionalRomeCodes(romeCodes);
            }
        }
        return this;
    }

    public SourcingCandidatesCriteria excludingRecruitment(Recruitment recruitment) {
        this.excludesRecruitment = recruitment;
        return this;
    }

    public SourcingCandidatesCriteria workingTimeType(TypeWorkingTime workingTimeType) {
        if (workingTimeType == TypeWorkingTime.PART_TIME) {
            criteria.add(CriteriaValue.getValueCodeForTypeWorkingTime(workingTimeType));
        }
        return this;
    }

    public List<String> criteriaForContracts() {
        if (typeContractCategory != null) {
            return List.of(CriteriaValue.getValueCodeForTypeContractCategory(typeContractCategory));
        }
        return Collections.emptyList();
    }

    public SourcingCandidatesCriteria lastConnectionDateByTopTen() {
        lastConnectionBefore = topTen ? LocalDateTime.now().minusDays(60) : LocalDateTime.now().minusYears(2);
        return this;
    }

    public boolean excludesNotified() {
        return recruitmentId() != null && this.excludesNotified && !topTen;
    }

    public Float salaryMinCalculated() {
        return salaryMin != null ? (salaryMin() * salaryMinToleranceRatio) : null;
    }

    public Float salaryMaxCalculated() {
        return salaryMax != null ? (salaryMax() * salaryMaxToleranceRatio) : null;
    }

    public Double masteryLevelMax() {
        var upperDelta = topTen ? topTenUpperDelta : 1d;
        return masteryLevelAround != null ? masteryLevelAround + upperDelta + FLOATING_POINT_PRECISION_ADJUSTMENT : null;
    }

    public Double masteryLevelMin() {
        var lowerDelta = topTen ? topTenLowerDelta : 1d;
        return masteryLevelAround != null ? masteryLevelAround - lowerDelta - FLOATING_POINT_PRECISION_ADJUSTMENT : null;
    }

    public long rowLimit() {
        return topTen ? NUMBER_OF_USER_FOR_TOP_TEN : NO_LIMIT_MAGIC_INT;
    }

    public Double capacityTolerance() {
        return customCapacityTolerance != null ? customCapacityTolerance : defaultCapacityTolerance;
    }

    public boolean hasZeroTolerance() {
        return capacityTolerance() == null || capacityTolerance() < 0.01d;
    }

    public Long recruitmentId() {
        return excludesRecruitment != null ? excludesRecruitment.getId() : null;
    }

    public SourcingCandidatesCriteria updateCriteriaWithOccupationRelatedDatasAndGlobalParameters(
            ErhgoOccupation occupation
    ) {
        var privateOrganizations = abstractOrganizationRepository.findByPrivateUsers(true);
        return capacitiesIds(new ArrayList<>(occupation == null ? Collections.emptyList() : occupation.getAllCapacitiesId()))
                .masteryLevelAround(occupation == null ? 0 : occupation.getLevelAsInt())
                .lastConnectionDateByTopTen()
                .refusedChannels(privateOrganizations.stream().map(AbstractOrganization::getCode).collect(Collectors.toCollection(HashSet::new)))
                .addRomeAndIscoCodes(occupation);
    }
}
