package com.erhgo.services.dto.criteria;

import com.erhgo.domain.KeycloakUserSummary;
import com.erhgo.openapi.dto.ActivityTypeDTO;
import com.erhgo.openapi.dto.SortDirectionDTO;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.logging.log4j.util.Strings;

import java.util.Set;

@Data
@Accessors(fluent = true)
public class ActivityCriteria {
    private ActivityTypeDTO activityType;
    private Integer page;
    private Integer size;
    private Set<Long> capacityIds;
    private Boolean isCapacityRecursive;
    private String filter;
    private String by;
    private SortDirectionDTO direction;
    private KeycloakUserSummary userFilter;

    public boolean hasCapacity() {
        return this.capacityIds != null && !this.capacityIds.isEmpty();
    }

    public boolean hasUserId() {
        return userFilter != null && Strings.isNotBlank(userFilter.getKeycloakId());
    }
}
