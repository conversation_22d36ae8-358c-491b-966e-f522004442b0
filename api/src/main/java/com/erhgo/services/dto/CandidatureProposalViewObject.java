package com.erhgo.services.dto;

import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.domain.enums.TypeContractCategory;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.userprofile.Location;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Collection;
import java.util.Locale;
import java.util.UUID;

public class CandidatureProposalViewObject {
    public static final String UNKNOWN = "Non précisé";

    // Key used in mail template
    private static final String EMPTY_CLASSIFICATIONS_CODE = "empty";
    @JsonProperty("recruitment_code")
    String recruitmentCode;
    @JsonProperty("occupation_id")
    String occupationId;
    @JsonProperty("organization")
    String organization;
    @JsonProperty("job_title")
    String jobTitle;
    @JsonProperty("contract")
    String contract;
    @JsonProperty("time")
    String time;
    @JsonProperty("salary")
    String salary;
    @JsonProperty("city")
    String city;
    @JsonProperty("post_code")
    String postCode;
    @JsonProperty("erhgoClassifications")
    String erhgoClassifications;

    public CandidatureProposalViewObject(Recruitment recruitment) {
        withRecruitmentCode(recruitment.getCode())
                .withOrganization(recruitment.getRecruiterTitle())
                .withOccupationId(recruitment.getErhgoOccupationId())
                .withJobTitle(recruitment.getJob().getTitle())
                .withContract(recruitment.getTypeContract().getTypeContractCategory())
                .withTime(recruitment.getWorkingWeeklyTime())
                .withSalary(recruitment.getBaseSalary(), recruitment.getMaxSalary(), recruitment.getHideSalary())
                .withLocation(recruitment.getLocation())
                .withClassifications(recruitment.getErhgoClassifications().stream().map(ErhgoClassification::getTitle).toList());
    }

    CandidatureProposalViewObject withClassifications(Collection<String> withErhgoClassifications) {
        this.erhgoClassifications = withErhgoClassifications.isEmpty() ? EMPTY_CLASSIFICATIONS_CODE : String.join(", ", withErhgoClassifications);
        return this;
    }

    CandidatureProposalViewObject withRecruitmentCode(String withRecruitmentCode) {
        this.recruitmentCode = withRecruitmentCode;
        return this;
    }

    CandidatureProposalViewObject withOrganization(String withOrganization) {
        this.organization = withOrganization;
        return this;
    }

    CandidatureProposalViewObject withJobTitle(String withJobTitle) {
        this.jobTitle = withJobTitle;
        return this;
    }

    CandidatureProposalViewObject withContract(TypeContractCategory typeContractCategory) {
        switch (typeContractCategory) {
            case PRO -> this.contract = "Alternance";
            case PERMANENT -> this.contract = "CDI";
            case TEMPORARY -> this.contract = UNKNOWN;
        }
        return this;
    }

    CandidatureProposalViewObject withTime(Integer time) {
        this.time = time == null ? UNKNOWN : "%dh par semaine".formatted(time);
        return this;
    }

    CandidatureProposalViewObject withSalary(Integer min, Integer max, Boolean hide) {
        if (BooleanUtils.isTrue(hide) || min == null || max == null) {
            this.salary = UNKNOWN;
        } else {
            this.salary = String.format(Locale.FRANCE, "%,d - %,d € bruts annuels", min, max);
        }
        return this;
    }

    CandidatureProposalViewObject withLocation(Location location) {
        if (location == null) {
            this.city = UNKNOWN;
            this.postCode = "";
        } else {
            this.city = location.getCity();
            this.postCode = location.getPostcode();
        }
        return this;
    }

    CandidatureProposalViewObject withOccupationId(UUID withOccupationId) {
        this.occupationId = withOccupationId.toString();
        return this;
    }

}
