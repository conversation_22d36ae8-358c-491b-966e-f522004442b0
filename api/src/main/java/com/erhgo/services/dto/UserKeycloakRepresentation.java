package com.erhgo.services.dto;

import com.erhgo.security.Role;
import com.erhgo.services.keycloak.UserRepresentation;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Accessors(chain = true)
@Data
@EqualsAndHashCode
@NoArgsConstructor
@ToString(exclude = "password")
public class UserKeycloakRepresentation implements Serializable {
    private static final long serialVersionUID = 1L;

    private String firstName;
    private String lastName;
    private String email;
    private String password;
    private String group;
    private String organizationCode;
    private Map<String, List<String>> miscAttributes;

    public String getUsername() {
        return this.email;
    }

    public static UserKeycloakRepresentation createAdmin(String firstName, String lastName, String email, String password) {
        var representation = new UserKeycloakRepresentation();
        representation.firstName = firstName;
        representation.lastName = lastName;
        representation.email = email;
        representation.password = password;
        representation.group = "ODAS";
        return representation;
    }

    public static UserKeycloakRepresentation createForSourcing(String fullname, String email, String password, String organizationCode) {
        var representation = new UserKeycloakRepresentation();
        representation.email = email;
        representation.password = password;
        representation.organizationCode = organizationCode;
        representation.miscAttributes = Map.of(UserRepresentation.FULLNAME_ATTRIBUTE, List.of(fullname));
        representation.group = Role.SOURCING;
        return representation;
    }


}
