package com.erhgo.services.dto;

import com.erhgo.openapi.dto.BehaviorDTO;
import com.erhgo.openapi.dto.UserProfileDetailWithCapacitiesDTO;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
@AllArgsConstructor
public class UserProfileFOExport {
    private String userId;
    private UserProfileDetailWithCapacitiesDTO profileDetails; // note: always sort them according to their experience year
    private List<BehaviorDTO> profileBehaviorsCategories; // note: always sort them
}
