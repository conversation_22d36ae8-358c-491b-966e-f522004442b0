package com.erhgo.services.mailing;

import com.erhgo.domain.candidature.job.AbstractCandidature;
import com.erhgo.domain.candidature.job.CandidatureEmailRefusalState;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.openapi.dto.CustomEmailTemplateDTO;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.security.Role;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.OffsetDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
@RequiredArgsConstructor
public class SendCandidatureRefusalEmailService {
    private final MailingListService mailingListService;
    private final KeycloakService keycloakService;
    private final TransactionTemplate transactionTemplate;
    private final RecruitmentCandidatureRepository recruitmentCandidatureRepository;

    @Value("${sendinblue.templates.refuse-candidature-sourcing}")
    private Long refuseCandidatureSourcingTemplate;


    @RolesAllowed(Role.ODAS_ADMIN)
    public void sendCustomEmail(AbstractCandidature candidature, CustomEmailTemplateDTO customTemplate) {

        sendEmailInternal(candidature.getId(), () -> {
            UserRepresentation user = getUserRepresentation(candidature);
            var email = user.getEmail();
            var recipientName = Stream.of(user.getFirstName(), user.getLastName())
                    .filter(StringUtils::isNotBlank)
                    .map(StringUtils::capitalize)
                    .collect(Collectors.joining(" "));
            return mailingListService.sendMail(
                    email,
                    recipientName,
                    customTemplate.getSubject(),
                    customTemplate.getContent(),
                    customTemplate.getEmailFrom(),
                    customTemplate.getAuthorAlias()).thenApply(a -> a ? Set.of(email) : new HashSet<>());
        });
    }

    @NotNull
    private UserRepresentation getUserRepresentation(AbstractCandidature candidature) {
        return keycloakService.getFrontOfficeUserProfile(candidature.getUserProfile().userId()).orElseThrow();
    }

    private void sendEmailInternal(Long candidatureId, Supplier<CompletableFuture<Collection<String>>> sendMail) {
        sendMail
                .get()
                .handle((emailsSent, exception) -> {
                    transactionTemplate.execute(useless -> {
                        var localCandidature = recruitmentCandidatureRepository.findById(candidatureId).orElseThrow();
                        if (exception == null) {
                            localCandidature.markEmailRefusedState(emailsSent != null && emailsSent.size() == 1 ? CandidatureEmailRefusalState.DONE : CandidatureEmailRefusalState.NONE);
                        } else {
                            log.error("Unable to send refusal email", exception);
                            localCandidature.markEmailRefusedState(CandidatureEmailRefusalState.ERROR);
                        }
                        return null;
                    });
                    return null;
                });
    }

    @RolesAllowed({Role.ODAS_ADMIN})
    public void sendEmailForSourcingCandidaturesRefused() {
        if (refuseCandidatureSourcingTemplate == null) {
            log.error("Ignoring candidature refusal send, no mail template id provided for refusal candidature");
            return;
        }
        var datasForCandidature = new HashMap<RecruitmentCandidature, Map<String, ?>>();

        transactionTemplate.execute(useless -> {
            var candidaturesPerBlacklistedState = recruitmentCandidatureRepository
                    .findByCandidatureRefusalStateEmailSentAndSubmissionDateBefore(CandidatureEmailRefusalState.WAITING, OffsetDateTime.now().minusHours(48))
                    .stream()
                    .collect(Collectors.groupingBy(c -> BooleanUtils.isTrue(c.getUserProfile().getJobOfferOptOut())));

            candidaturesPerBlacklistedState.getOrDefault(false, new ArrayList<>())
                    .forEach(candidature -> datasForCandidature.put(candidature, buildDatas(candidature)));
            candidaturesPerBlacklistedState.getOrDefault(true, new ArrayList<>())
                    .forEach(c -> c.markEmailRefusedState(CandidatureEmailRefusalState.NONE));
            return null;
        });

        datasForCandidature.forEach((candidature, parameters) ->
                sendEmailInternal(candidature.getId(), () ->
                        mailingListService.sendMailsForTemplate(Set.of(getUserRepresentation(candidature).getEmail()), refuseCandidatureSourcingTemplate, parameters, null)
                )
        );
    }

    private static @NotNull Map<String, String> buildDatas(RecruitmentCandidature candidature) {
        return Map.of("JOB", candidature.getJobTitle(),
                "LOCATION", Optional.ofNullable(candidature.getRecruitment().getLocation()).map(l -> "%s (%s)".formatted(l.getCity(), l.getPostcode())).orElse(""),
                "RECRUITER", candidature.getOrganizationName());
    }
}
