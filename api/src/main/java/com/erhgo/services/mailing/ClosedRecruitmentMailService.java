package com.erhgo.services.mailing;

import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.services.keycloak.KeycloakService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Slf4j
@Service
public class ClosedRecruitmentMailService {

    private final RecruitmentCandidatureRepository recruitmentCandidatureRepository;
    private final KeycloakService keycloakService;
    private final MailingListService mailingListService;
    @Value("${sendinblue.templates.close-recruitment-fo-notification}")
    private Long closeRecruitmentTemplate;

    public void notifyRecruitmentClosed(Recruitment recruitment) {
        var usersToNotify = recruitmentCandidatureRepository
                .findByRecruitmentIdAndGlobalCandidatureStateIn(recruitment.getId(), Pageable.unpaged(), GlobalCandidatureState.statesToNotifyOnRecruitmentClose())
                .stream()
                .filter(RecruitmentCandidature::isVisibleForUser)
                .map(RecruitmentCandidature::getUserProfile)
                .collect(Collectors.toSet());
        var params = Map.of(
                "RECRUITER", recruitment.getRecruiterTitle(),
                "TITLE", recruitment.getJobTitle()
        );
        mailingListService.sendMailsToProfilesForTemplate(
                usersToNotify,
                "la cloture du recruitment %s de %s (id=%d)".formatted(recruitment.getJobTitle(), recruitment.getRecruiter().getTitle(), recruitment.getId()),
                closeRecruitmentTemplate,
                params,
                null);
    }

}
