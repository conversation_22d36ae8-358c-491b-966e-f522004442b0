package com.erhgo.services.mailing;

import com.erhgo.services.SecurityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
@Profile("!test")
public class NewUserWelcomeMailScheduler {

    private final NewUserWelcomeMailService newUserSynchronizedService;
    private final SecurityService securityService;

    @Scheduled(cron = "${application.mailing.newUserSendWelcomeMailCron}", zone = "Europe/Paris")
    @SchedulerLock(name = "launchNewUserSendWelcomeMailCron")
    public void sendNewUserSendWelcomeMail() {
        log.info("Launch new users welcome mail send");
        securityService.doAsAdmin(newUserSynchronizedService::sendWelcomeMails);
        log.info("New users welcome mail sent");
    }

}
