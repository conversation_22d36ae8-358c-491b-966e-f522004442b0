package com.erhgo.services.mailing;

import com.erhgo.services.SecurityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
@Slf4j
public class SendCandidatureSuspendedEmailScheduler {
    private final SendCandidatureSuspendedNotificationService sendCandidatureSuspendedNotificationService;
    private final SecurityService securityService;

    @Scheduled(fixedRate = 4, timeUnit = TimeUnit.HOURS)
    @SchedulerLock(name = "launchCandidatureSuspendedEmailSending")
    public void sendCandidatureSuspendedEmails() {
        log.info("Launch sending of suspended candidatures emails");
        securityService.doAsAdmin(sendCandidatureSuspendedNotificationService::sendSuspendedRecruitmentNotifications);
        log.info("Sending of suspended candidatures emails ended");
    }

}
