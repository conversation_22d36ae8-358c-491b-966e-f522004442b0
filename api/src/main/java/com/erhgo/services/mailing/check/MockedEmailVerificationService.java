package com.erhgo.services.mailing.check;

import com.erhgo.domain.dto.EmailVerificationResultDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@ConditionalOnExpression("T(org.springframework.util.StringUtils).isEmpty('${quickemailverification.apiKey:}')")
@Slf4j
public class MockedEmailVerificationService implements EmailVerificationService {

    @Override
    public EmailVerificationResultDTO verify(String email) {
        log.warn("Email verification is disabled - no QEV api key is defined");
        return new EmailVerificationResultDTO().setEmailStatus(email.contains("ko") ? EmailVerificationResultDTO.EmailStatus.INVALID_MAIL : EmailVerificationResultDTO.EmailStatus.VALID);
    }
}
