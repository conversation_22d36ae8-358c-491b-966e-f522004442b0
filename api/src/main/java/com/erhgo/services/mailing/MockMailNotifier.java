package com.erhgo.services.mailing;

import com.erhgo.services.userprofile.FilePartProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Service;

import java.util.Collection;

@Service
@Slf4j
@ConditionalOnExpression("'${gmail.apiKey}'.isEmpty()")
public class MockMailNotifier implements MailNotifier {

    @Override
    public void sendMail(Collection<String> to, String replyTo, String replyToAlias,
                         boolean useReplyToAsFrom, String subject, String body,
                         FilePartProvider... parts) {
        log.error("Using mock mail notifier - Email not sent to {}, subject: {}", to, subject);
    }
}
