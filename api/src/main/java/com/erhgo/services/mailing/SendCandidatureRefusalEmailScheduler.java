package com.erhgo.services.mailing;

import com.erhgo.services.SecurityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
@Slf4j
public class SendCandidatureRefusalEmailScheduler {
    private final SendCandidatureRefusalEmailService sendCandidatureRefusalEmailService;
    private final SecurityService securityService;

    @Scheduled(fixedRate = 1, timeUnit = TimeUnit.HOURS)
    @SchedulerLock(name = "launchCandidatureRefusalEmailSending")
    public void sendCandidatureRefusalEmails() {
        log.info("Launch sending of refusal candidatures emails");
        securityService.doAsAdmin(sendCandidatureRefusalEmailService::sendEmailForSourcingCandidaturesRefused);
        log.info("Sending of refusal candidatures emails launched");
    }

}
