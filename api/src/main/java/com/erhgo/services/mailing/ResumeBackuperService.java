package com.erhgo.services.mailing;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.services.userprofile.FilePartProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Arrays;

@Service
@Slf4j
@RequiredArgsConstructor
public class ResumeBackuperService {
    private final MailNotifier mailNotifier;

    @Value("${gmail.cvRecipients}")
    private String cvRecipients;

    @Value("${keycloak-realms.back_office_base_url}")
    private String boBaseUrl;

    private static final String MAIL_SUBJECT = "Nouvel import de CV";

    private static final String MAIL_BODY = """
            Bonjour,
            Un candidat a importé son CV sur jenesuispasuncv.fr.
            Vous trouverez en pièce jointe son CV.
            Lien vers son profil de compétences : %s
            """.replace("\n", "\r\n");

    @Async
    public void backupResume(String userId, FilePartProvider file) {
        try {
            var fileBytes = file.content();
            var contentType = file.contentType();
            var linkProfile = "%s/repository/user-edit/%s".formatted(boBaseUrl, userId);
            var cvAttachment = new FilePartProvider(
                    fileBytes,
                    "cv_import",
                    contentType != null ? contentType : MediaType.APPLICATION_OCTET_STREAM_VALUE
            );

                mailNotifier.sendMail(
                        Arrays.asList(cvRecipients.split(",")),
                        "<EMAIL>",
                        "Jenesuispasuncv",
                        false,
                        MAIL_SUBJECT,
                        MAIL_BODY.formatted(linkProfile),
                        cvAttachment
                );
                log.debug("CV email sent for user {}", userId);
        } catch (RuntimeException e) {
            log.error("Failed to send CV notification email for user {}", userId, e);
            throw new GenericTechnicalException("Failed to send CV notification email", e);
        }
    }
}
