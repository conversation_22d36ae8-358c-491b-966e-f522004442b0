package com.erhgo.services.mailing;

import com.erhgo.domain.enums.NotificationState;
import com.erhgo.domain.enums.NotificationType;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.notification.RecruitmentNotification;
import com.erhgo.repositories.NotificationRepository;
import com.erhgo.repositories.RecruitmentRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.services.dtobuilder.UsersMobileNotificationDTOBuilder;
import com.erhgo.services.notifications.NotificationService;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.notifier.messages.EmailSendingNotifierMessageDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
@RequiredArgsConstructor
public class RecruitmentNotificationGenerator {
    private final NotificationRepository notificationRepository;
    private final NotificationService notificationService;
    private final UserProfileRepository userProfileRepository;
    private final RecruitmentRepository recruitmentRepository;
    private final UsersMobileNotificationDTOBuilder usersMobileNotificationDTOBuilder;
    private final Notifier notifier;

    @Async
    @Transactional
    public CompletableFuture<Void> generateNotifications(Set<String> usersId, Long recruitmentId) {
        var users = userProfileRepository.findByUserIdIn(usersId);
        return sendNotifications(users, recruitmentId);
    }

    private CompletableFuture<Void> sendNotifications(Collection<UserProfile> usersToNotify, Long recruitmentId) {
        var recruitment = recruitmentRepository.findById(recruitmentId).orElseThrow(() -> new EntityNotFoundException(recruitmentId, Recruitment.class));
        log.trace("Sending notifications to {} users for recruitment {}", usersToNotify.size(), recruitmentId);
        if (!usersToNotify.isEmpty()) {
            var dto = usersMobileNotificationDTOBuilder
                    .buildUsersMobileRecruitmentNotificationDTO(usersToNotify, recruitment);
            var usersMobile = notificationService.sendNotificationToUsers(dto);
            usersToNotify.forEach(u -> this.notifyUser(recruitment, usersMobile.contains(u) ? NotificationType.MOBILE : NotificationType.NONE, u));
            log.debug("{} users notified", usersToNotify.size());
        } else {
            var message = EmailSendingNotifierMessageDTO.builderForNoUser()
                    .organizationCode(recruitment.getRecruiterCode())
                    .recruitmentCode(recruitment.getCode())
                    .recruitmentTitle(recruitment.getJobTitle())
                    .recruiter(recruitment.getRecruiterTitle())
                    .build();
            notifier.sendMessage(message);
        }
        return CompletableFuture.completedFuture(null);
    }

    private RecruitmentNotification notifyUser(Recruitment recruitment, NotificationType type, UserProfile userProfile) {
        var notifiedRecruitment = RecruitmentNotification.builder()
                .userProfile(userProfile)
                .recruitment(recruitment)
                .type(type)
                .state(NotificationState.NEW)
                .build();
        return notificationRepository.save(notifiedRecruitment);
    }
}

