package com.erhgo.services.mailing;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;
import java.util.Set;

@Service
@Slf4j
@RequiredArgsConstructor
public class SendEmailValidationCandidatureService {
    private final MailingListService mailingListService;

    @Value("${sendinblue.templates.validation-candidature}")
    private Long validationEmailCandidatureTemplate;

    private final KeycloakService keycloakService;

    public void sendValidationEmailCandidatureTemplate(RecruitmentCandidature candidature) {
        if (validationEmailCandidatureTemplate == null) {
            log.warn("Ignoring candidature validation email send, no mail template id provided for candidature {}", candidature.getId());
            return;
        }
        var user = getUserRepresentation(candidature);
        var email = user.getEmail();
        var location = Optional.ofNullable(candidature.getRecruitment().getLocation()).map(l -> "%s %s".formatted(l.getPostcode(), l.getCity())).orElse("Non précisé");
        var salary = candidature.getRecruitment().getBaseSalary() != null && candidature.getRecruitment().getMaxSalary() != null ? "%s - %s".formatted(StringUtils.normalizeSalary(candidature.getRecruitment().getBaseSalary()), StringUtils.normalizeSalary(candidature.getRecruitment().getMaxSalary())) : "";
        var time = candidature.getRecruitment().getWorkingWeeklyTime();
        var contract = candidature.getRecruitment().getTypeContract();

        var parameters = Map.of("JOB", candidature.getRecruitment().getJob().getTitle(),
                "RECRUITER", candidature.getRecruitment().getJob().getRecruiter().getTitle(),
                "CITY", location,
                "NAME", org.apache.commons.lang3.StringUtils.trimToEmpty(user.getFirstName()),
                "CONTRACT", trimToEmpty(contract),
                "TIME", trimToEmpty(time),
                "SALARY", salary);

        if (email.isEmpty()) {
            log.info("Ignoring candidature email send, email is null for candidature {}", candidature.getId());
        } else {
            mailingListService.sendMailsForTemplate(Set.of(email), validationEmailCandidatureTemplate, parameters, null);
        }
    }

    private static String trimToEmpty(Object object) {
        return Optional.ofNullable(object).map(String::valueOf).map(org.apache.commons.lang3.StringUtils::trimToEmpty).orElse("");
    }

    private UserRepresentation getUserRepresentation(RecruitmentCandidature candidature) {
        return keycloakService.getFrontOfficeUserProfile(candidature.getUserProfile().userId()).orElseThrow();
    }

}

