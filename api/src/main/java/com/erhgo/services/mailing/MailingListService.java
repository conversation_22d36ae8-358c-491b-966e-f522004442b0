package com.erhgo.services.mailing;

import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.services.dto.TransactionalBlackListResult;
import com.erhgo.services.dtobuilder.MailingUserDTO;
import com.erhgo.services.userprofile.FilePartProvider;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

public interface MailingListService {

    void updateLastConnectionDate(UserProfile userProfile);

    void updateLastCandidatureDate(UserProfile userProfile);

    void updateNumberOfExperiences(UserProfile userProfile);

    boolean getJobDatingOptIn(UserProfile userProfile);

    void updateLocation(UserProfile userProfile);

    void updatePhoneNumber(UserProfile userProfile);

    void updateName(UserProfile userProfile);

    void updateSalary(UserProfile userProfile);

    void updateSituation(UserProfile userProfile);

    void updateContactInfo(UserProfile userProfile);

    void updateUserMobileUsage(UserProfile userProfile);

    void updateJobOffersOptIn(UserProfile userProfile);

    void updateNewsOptIn(UserProfile userProfile, Boolean optIn);

    CompletableFuture<Set<String>> sendMailsToProfilesForTemplate(Collection<UserProfile> userProfiles, String notificationLabel, long templateId, Object globalParams, Map<String, Map<String, Object>> perUserIdParam, String... forcedEmail);

    CompletableFuture<Collection<String>> sendMailsForTemplate(Set<String> emails, long templateId, Object params, FilePartProvider filePartProvider, String... forcedEmails);

    CompletableFuture<Boolean> sendMail(String email, String recipientName, String subject, String content, String emailFrom, String authorAlias);

    List<TransactionalBlackListResult> getTransactionalBlacklistedEmails(Long limit, Long offset, LocalDateTime modifiedSince);

    List<MailingUserDTO> getContacts(Long limit, Long offset, LocalDateTime modifiedSince);

    void processFOEmailUpdate(String previousEmail, String newEmail);

    List<MailingUserDTO> getListUsersJobDating(Long limit, Long offset);

    void blacklistFromAllSenders(UserProfile userProfile);

    void addContactToAgefiphCandidatesList(String email, String scrapedUserId, String candidateId);
}
