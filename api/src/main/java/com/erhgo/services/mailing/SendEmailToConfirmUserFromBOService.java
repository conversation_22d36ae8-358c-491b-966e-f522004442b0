package com.erhgo.services.mailing;

import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;

@Service
@Slf4j
@RequiredArgsConstructor
public class SendEmailToConfirmUserFromBOService {

    private final MailingListService mailingListService;
    private final KeycloakService keycloakService;

    @Value("${sendinblue.templates.confirm-account}")
    private Long confirmAccountTemplate;

    public void sendEmailForNewUserBO(String userId, String password) {
        if (confirmAccountTemplate == null) {
            log.error("Ignoring temporary password send, no mail template id provided for temporary password {}", confirmAccountTemplate);
            return;
        }
        var parameters = Map.of("password", password);
        var email = keycloakService.getFrontOfficeUserProfile(userId)
                .map(UserRepresentation::getEmail)
                .orElseThrow(() -> new EntityNotFoundException(userId, UserRepresentation.class));
        mailingListService.sendMailsForTemplate(Set.of(email), confirmAccountTemplate, parameters, null);
    }
}
