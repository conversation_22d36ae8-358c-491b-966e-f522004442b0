package com.erhgo.services.mailing;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import sendinblue.ApiClient;
import sendinblue.auth.ApiKeyAuth;
import sibApi.ContactsApi;
import sibApi.SendersApi;
import sibApi.TransactionalEmailsApi;

@Service
@Slf4j
@RequiredArgsConstructor
public class SendInBlueClientConfiguration {

    // Timeout set to 2 min
    private static final int TIMEOUT_IN_MS = 2 * 60 * 1_000;

    @Value("${sendinblue.apiKey:}")
    private String apiKey;
    @Getter
    @Value("${sendinblue.list.job-dating-optin-front-users}")
    private Long jobDatingOptinFrontUsersListId;
    @Getter
    @Value("${sendinblue.list.agefiph-candidates}")
    private Long agefiphCandidatesListId;

    private ApiClient getClient() {
        if (apiKey == null) {
            log.error("No sendinblue configured");
            return null;
        } else {
            var apiClient = new ApiClient();
            apiClient.setReadTimeout(TIMEOUT_IN_MS);
            apiClient.setWriteTimeout(TIMEOUT_IN_MS);
            apiClient.setConnectTimeout(TIMEOUT_IN_MS);
            var apiKeyAuth = (ApiKeyAuth) apiClient.getAuthentication("api-key");
            apiKeyAuth.setApiKey(apiKey);
            return apiClient;
        }
    }


    public ContactsApi getContactApi() {
        return new ContactsApi(getClient());
    }

    public TransactionalEmailsApi getTransactionalEmailApi() {
        return new TransactionalEmailsApi(getClient());
    }

    public SendersApi getSendersApi() {
        return new SendersApi(getClient());
    }
}
