package com.erhgo.services.mailing;

import com.erhgo.security.Role;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class SendCandidatureNotificationEmailService {
    private final MailingListService mailingListService;
    private final KeycloakService keycloakService;

    @Value("${sendinblue.templates.candidature-notification}")
    private Long candidatureNotificationTemplate;

    @PostConstruct
    void init() {
        log.info("Init SendCandidatureNotificationEmailService with candidature notification template: {}", candidatureNotificationTemplate);
    }


    @Transactional
    @RolesAllowed(Role.CANDIDATE)
    public void sendCandidatureNotificationEmails(Set<String> usersId, Long candidatureId, String jobTitle, String organizationName, String organizationCode, Long recruitmentId) {
        if (candidatureNotificationTemplate == null) {
            log.warn("Ignoring candidature notification send, no mail template id provided for candidature {}", candidatureId);
            return;
        }

        var emails = getBackOfficeUsersEmails(usersId);
        var parameters = Map.of("jobTitle", jobTitle,
                "organizationName", organizationName,
                "organizationCode", organizationCode,
                "candidatureId", candidatureId,
                "recruitmentId", recruitmentId);

        if (emails.isEmpty()) {
            log.info("Ignoring candidature notification send, emails list empty for candidature {}", candidatureId);
        } else {
            mailingListService.sendMailsForTemplate(emails, candidatureNotificationTemplate, parameters, null);
        }
    }

    private Set<String> getBackOfficeUsersEmails(Set<String> usersId) {
        var keycloakUsers = usersId.stream()
                .map(keycloakService::getBackOfficeUserProfile)
                .filter(Optional::isPresent)
                .map(Optional::get).toList();

        return keycloakUsers.stream()
                .map(UserRepresentation::getEmail)
                .collect(Collectors.toSet());
    }
}
