package com.erhgo.services;

import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.openapi.dto.ErhgoClassificationDTO;
import com.erhgo.repositories.classifications.ErhgoClassificationRepository;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.services.dtobuilder.ErhgoClassificationDTOBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.UUID;

@Service
@Slf4j
@RequiredArgsConstructor
public class ErhgoClassificationService {
    private final ErhgoClassificationRepository erhgoClassificationRepository;
    private final ErhgoClassificationDTOBuilder erhgoClassificationDTOBuilder;

    private final ErhgoOccupationRepository erhgoOccupationRepository;

    @Transactional(readOnly = true)
    public List<ErhgoClassificationDTO> listErhgoClassifications(UUID optionalOccupationIdFilter) {
        Collection<ErhgoClassification> erhgoClassifications;
        if (optionalOccupationIdFilter != null) {
            var occupation = erhgoOccupationRepository
                    .findById(optionalOccupationIdFilter)
                    .orElseThrow(() -> new EntityNotFoundException(optionalOccupationIdFilter, ErhgoOccupation.class));

            erhgoClassifications = occupation.getErhgoClassifications();

        } else {
            erhgoClassifications = erhgoClassificationRepository.findAllByOrderByHighPriorityDescOrderIndexAsc();
        }
        return erhgoClassifications
                .stream()
                .map(erhgoClassificationDTOBuilder::buildErhgoClassificationDTO)
                .toList();
    }
}
