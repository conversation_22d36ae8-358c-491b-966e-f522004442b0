package com.erhgo.services;

import com.erhgo.domain.AbstractEntity;
import com.erhgo.repositories.AbstractRepository;
import com.erhgo.security.Role;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.transaction.annotation.Transactional;

public class AbstractReferentialService<E extends AbstractEntity, R extends AbstractRepository<E>> extends AbstractService<E, R> {
    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    @Override
    public E create(E origin) {
        return super.create(origin);
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    @Override
    public E updateIgnoringFields(E origin, String... ignoringFields) {
        return super.updateIgnoringFields(origin, ignoringFields);
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    @Override
    public E update(E origin) {
        return super.update(origin);
    }

}
