package com.erhgo.services.reminder;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Slf4j
@Profile("!master")
@Service
public class EmailToNotifyRestrictedFilter implements EmailToNotifyFilterInterface {

    @Override
    public boolean emailAccepted(String email) {
        return Optional.ofNullable(email)
                .filter(e -> {
                    var result = e.toLowerCase().endsWith("@erhgo.fr")
                            || e.toLowerCase().endsWith("@jenesuispasuncv.fr")
                            || e.toLowerCase().endsWith("@localhost");
                    if (!result) {
                        log.info("Ignoring email {} due to Spring Profile", e);
                    }
                    return result;
                })
                .isPresent();
    }
}
