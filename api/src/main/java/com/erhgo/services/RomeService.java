package com.erhgo.services;

import com.erhgo.openapi.dto.RomePageDTO;
import com.erhgo.repositories.classifications.RomeOccupationRepository;
import com.erhgo.security.Role;
import com.erhgo.services.dtobuilder.ErhgoOccupationDataDTOBuilder;
import com.erhgo.services.dtobuilder.PageDTOBuilder;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class RomeService {

    @Autowired
    private RomeOccupationRepository romeOccupationRepository;
    @Autowired
    private ErhgoOccupationDataDTOBuilder erhgoOccupationDataDTOBuilder;

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional(readOnly = true)
    public RomePageDTO getRomePage(Integer size, Integer page, String query) {
        var pageable = PageRequest.of(page, size);
        return PageDTOBuilder.buildRomePage(romeOccupationRepository
                .searchRomes(query, pageable)
                .map(erhgoOccupationDataDTOBuilder::buildRomeSummaryDTO));
    }
}
