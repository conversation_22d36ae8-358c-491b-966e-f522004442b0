package com.erhgo;

import com.erhgo.config.SecurityConfig;
import com.erhgo.migrations.AbstractMigrationService;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.transaction.annotation.EnableTransactionManagement;


@SpringBootApplication
@ComponentScan(excludeFilters = {
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = {".*openapi.esco.*"}),
        @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = AbstractMigrationService.class),
        @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = AbstractMigrationService.class),
})
@EnableTransactionManagement(order = SecurityConfig.ORDER - 1)
public class ErhgoApplication {

    public static void main(String[] args) {
        SpringApplication.run(ErhgoApplication.class, args);
    }
}
