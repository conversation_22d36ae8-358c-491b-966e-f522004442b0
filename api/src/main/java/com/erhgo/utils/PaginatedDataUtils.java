package com.erhgo.utils;

import com.google.common.util.concurrent.Uninterruptibles;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.BiFunction;

import static org.apache.commons.collections.CollectionUtils.isNotEmpty;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PaginatedDataUtils {

    public static <A> List<A> getPaginatedData(Long limit, BiFunction<Long, Long, List<A>> listRetriever, Long optionalDelayInMs) {
        var offset = 0L;
        var all = new ArrayList<A>();
        List<A> current;
        if (listRetriever != null) {
            do {
                current = listRetriever.apply(limit, offset);
                if (isNotEmpty(current)) {
                    all.addAll(current);
                    offset += current.size();
                    if (optionalDelayInMs != null && current.size() == limit) {
                        Uninterruptibles.sleepUninterruptibly(optionalDelayInMs, TimeUnit.MILLISECONDS);
                    }
                }
            } while (isNotEmpty(current) && (offset % limit == 0) && !current.isEmpty());
        }
        return all;
    }
}
