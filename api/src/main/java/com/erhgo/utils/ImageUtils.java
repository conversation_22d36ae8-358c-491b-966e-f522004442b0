package com.erhgo.utils;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.services.userprofile.FilePartProvider;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamSource;
import org.springframework.http.MediaType;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ImageUtils {
    private static final float COMPRESSION_QUALITY = 0.5f;
    private static final int MIN_SIZE_REQUIRED_FOR_COMPRESSION = 500 * 1024;

    public static byte[] compressImage(InputStreamSource inputStreamSource) throws IOException {
        try (var originalInputStream = inputStreamSource.getInputStream()) {
            var data = originalInputStream.readAllBytes();
            if (data.length <= MIN_SIZE_REQUIRED_FOR_COMPRESSION) {
                return data;
            }
            var inputImage = ImageIO.read(new ByteArrayInputStream(data));
            if (inputImage == null) {
                throw new GenericTechnicalException("Failed to read image data");
            }
            var grayImage = convertToGrayscale(inputImage);
            return compressToJpeg(grayImage);
        }
    }

    static BufferedImage convertToGrayscale(BufferedImage image) {
        int width = image.getWidth() / 2;
        int height = image.getHeight() / 2;

        var result = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_GRAY);
        result.getGraphics().drawImage(image, 0, 0, width, height, null);

        return result;
    }

    static byte[] compressToJpeg(BufferedImage image) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        var writer = ImageIO.getImageWritersByFormatName("jpg").next();
        var ios = ImageIO.createImageOutputStream(outputStream);
        writer.setOutput(ios);

        var param = writer.getDefaultWriteParam();
        param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
        param.setCompressionQuality(COMPRESSION_QUALITY);

        writer.write(null, new IIOImage(image, null, null), param);

        writer.dispose();
        ios.close();

        return outputStream.toByteArray();
    }

    public static MediaType getMediaType(FilePartProvider file) {
        var contentType = file.contentType();
        if (contentType == null || contentType.isBlank()) {
            return MediaType.APPLICATION_OCTET_STREAM;
        }

        try {
            return MediaType.parseMediaType(contentType);
        } catch (Exception e) {
            return MediaType.APPLICATION_OCTET_STREAM;
        }
    }


    public static boolean isPdf(FilePartProvider file) {
        var mediaType = getMediaType(file);
        if (mediaType.toString().equalsIgnoreCase("application/pdf")) {
            return true;
        }
        if (mediaType.toString().equalsIgnoreCase("application/octet-stream")) {
            return isPdfBySignature(file);
        }
        return false;
    }

    public static boolean isImage(FilePartProvider file) {
        var mediaType = getMediaType(file);
        if (mediaType.getType().equalsIgnoreCase("image")) {
            return true;
        }
        if (mediaType.toString().equalsIgnoreCase("application/octet-stream")) {
            return isImageBySignature(file);
        }
        return false;
    }

    private static boolean isPdfBySignature(FilePartProvider file) {
        try (var inputStream = file.getInputStream()) {
            var header = new byte[4];
            if (inputStream.read(header) >= 4) {
                return header[0] == '%' && header[1] == 'P' && header[2] == 'D' && header[3] == 'F';
            }
        } catch (IOException e) {
            log.error("Error reading PDF file signature: {}", e.getMessage());
            throw new GenericTechnicalException("unable to read multipart file", e);
        }
        return false;
    }

    private static boolean isImageBySignature(FilePartProvider file) {
        try (var inputStream = file.getInputStream()) {
            var header = new byte[8];
            int bytesRead = inputStream.read(header);
            if (bytesRead >= 2) {
                if (header[0] == (byte) 0xFF && header[1] == (byte) 0xD8) {
                    return true;
                }
                if (bytesRead >= 8 && header[0] == (byte) 0x89 && header[1] == 'P' &&
                        header[2] == 'N' && header[3] == 'G') {
                    return true;
                }
                if (bytesRead >= 6 && header[0] == 'G' && header[1] == 'I' && header[2] == 'F') {
                    return true;
                }
            }
        } catch (IOException e) {
            log.error("Error reading image file signature: {}", e.getMessage());
            throw new GenericTechnicalException("unable to read multipart file", e);
        }
        return false;
    }
}
