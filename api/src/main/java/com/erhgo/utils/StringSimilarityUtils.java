package com.erhgo.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.similarity.CosineSimilarity;
import org.apache.commons.text.similarity.JaroWinklerSimilarity;

import java.util.Comparator;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class StringSimilarityUtils {
    private static final Double SIMILARITY_MAX_SCORE = 1.0;
    private static final JaroWinklerSimilarity jaroWinklerSimilarity = new JaroWinklerSimilarity();

    private StringSimilarityUtils() {
        throw new IllegalStateException("static utility class");
    }

    private static double computeCosineSimilarity(String str1, String str2) {
        return new CosineSimilarity().cosineSimilarity(
                new TermFrequency().buildWithText(StringUtils.stripAccents(str1)).getWordFrequencies(),
                new TermFrequency().buildWithText(StringUtils.stripAccents(str2)).getWordFrequencies()
        );
    }

    /**
     * <h2>Finds the most similar string to the input among the given set of strings.</h2>
     * <p>
     * A score (ranging from 0 to 2) is assigned to each string, indicating its similarity to the input.
     * </p>
     * <p>
     * A first score is computed using cosine similarity, which determines the term frequencies in the strings.
     * If the cosine similarity score is greater than 0, meaning that the input term is found at least once in the string,
     * the score is increased by 1 to give it more weight.
     * If the cosine similarity score is equal to 0, it indicates that the term is not found in the string and need an
     * additional score to determine similarity with the input.
     * </p>
     * <p>
     * The second score is based on the Jaro-Winkler similarity algorithm, which computes an approximate similarity
     * between the input and the string.
     * </p>
     * <p>
     * For more information on the used algorithms, refer to the Wikipedia articles on
     * <a href="https://en.wikipedia.org/wiki/Cosine_similarity">cosine similarity</a>
     * and
     * <a href="https://en.wikipedia.org/wiki/Jaro%E2%80%93Winkler_distance">Jaro-Winkler similarity</a>.
     * </p>
     *
     * @param input   The input string to compare against.
     * @param strings The set of strings to compare with the input.
     * @return The most similar string from the set, based on cosine similarity or Jaro-Winkler similarity.
     */
    public static String getTheMostSimilarString(String input, Set<String> strings) {
        var similarityScoreOfStrings = new HashMap<String, Double>();
        strings.forEach(string -> {
            var preprocessedString = cleanOccupationLabel(input, com.erhgo.utils.StringUtils.removeHtmlTag(string));
            var cosineSimilarityScore = computeCosineSimilarity(input, preprocessedString);
            similarityScoreOfStrings.put(string, cosineSimilarityScore > 0 ? (SIMILARITY_MAX_SCORE + cosineSimilarityScore) : jaroWinklerSimilarity.apply(input, preprocessedString));
        });
        return similarityScoreOfStrings
                .entrySet()
                .stream()
                .min(Map.Entry
                        .<String, Double>comparingByValue(Comparator.reverseOrder())
                        .thenComparing(Map.Entry.comparingByKey(Comparator.naturalOrder())))
                .map(Map.Entry::getKey)
                .orElse("");
    }

    private static String cleanOccupationLabel(String input, String occupationLabel) {
        var stringWithoutParenthesis = occupationLabel.replaceAll("\\([^)]*\\)", "").trim();
        var labels = Set.of(stringWithoutParenthesis.split("/"));
        if (labels.size() > 1) {
            return getTheMostSimilarString(input, labels);
        }
        return stringWithoutParenthesis;
    }

    private static class TermFrequency {
        private final Map<CharSequence, Integer> wordFrequencies;

        public TermFrequency() {
            wordFrequencies = new HashMap<>();
        }

        public TermFrequency buildWithText(String text) {
            wordFrequencies.clear();

            var words = text.toLowerCase().split("\\s+");

            for (var word : words) {
                wordFrequencies.put(word, wordFrequencies.getOrDefault(word, 0) + 1);
            }
            return this;
        }

        public Map<CharSequence, Integer> getWordFrequencies() {
            return wordFrequencies;
        }
    }

    public static String getTheMostSimilarStringWithAtLeastOneCommonWord(String input, Set<String> strings) {
        var subsetMatchingOneWord = strings.stream().filter(s -> com.erhgo.utils.StringUtils.oneWordMatch(input, s)).collect(Collectors.toSet());
        if (subsetMatchingOneWord.isEmpty()) return null;
        if (subsetMatchingOneWord.size() == 1) return subsetMatchingOneWord.iterator().next();
        return getTheMostSimilarString(input, subsetMatchingOneWord);
    }

}
