package com.erhgo.utils;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;

import javax.xml.XMLConstants;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathException;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;
import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.IntStream;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class XMLUtils {

    public static final String NOT_FOUND_IN_XML = "<?>";

    public static List<String> extractRawXmlNodeSetAtXPath(String xmlDataIn, String xpath) {
        var xmlData = cleanup(xmlDataIn);
        try {
            var xPathExpression = XPathFactory.newInstance().newXPath().compile(xpath);
            var xmlNodes = (NodeList) xPathExpression.evaluate(new InputSource(new StringReader(xmlData)), XPathConstants.NODESET);
            return convertXmlNodesToStrings(xmlNodes);
        } catch (XPathExpressionException e) {
            throw new GenericTechnicalException("unable to parse xml %s".formatted(xmlDataIn), e);
        }
    }

    private static String cleanup(String xmlDataIn) {
        var firstOpenTag = xmlDataIn.indexOf('<');
        var lastCLosedTag = xmlDataIn.lastIndexOf('>');
        if (firstOpenTag > 0 || lastCLosedTag < xmlDataIn.length() - 1) {
            return xmlDataIn.substring(Math.max(0, firstOpenTag), lastCLosedTag + 1);
        }
        return xmlDataIn;
    }

    private static List<String> convertXmlNodesToStrings(NodeList xmlNodes) {
        return IntStream.range(0, xmlNodes.getLength()).mapToObj(i -> {
            try {
                var writer = new StringWriter();
                TransformerFactory.newInstance().newTransformer().transform(new DOMSource(xmlNodes.item(i)), new StreamResult(writer));
                return writer.toString();
            } catch (TransformerException e) {
                throw new GenericTechnicalException("Unable to convert xml node to string at index %d".formatted(i), e);
            }
        }).toList();
    }

    private static Document createDocumentFromString(String xmlString) throws ParserConfigurationException, IOException, SAXException {
        var factory = getDocumentBuilderFactory();
        var builder = factory.newDocumentBuilder();
        var inputSource = new InputSource(new StringReader(xmlString));
        return builder.parse(inputSource);
    }


    private static DocumentBuilderFactory getDocumentBuilderFactory() {
        var factory = DocumentBuilderFactory.newInstance();
        factory.setAttribute(XMLConstants.ACCESS_EXTERNAL_DTD, "");
        factory.setAttribute(XMLConstants.ACCESS_EXTERNAL_SCHEMA, "");
        return factory;
    }

    public static String getNodeContentByXPath(String documentAsString, String xpathExpression) {
        var node = getNodeByXPath(documentAsString, xpathExpression);
        return getNodeContent(node);
    }

    public static String getNodeContent(Node node) {
        return node == null ? NOT_FOUND_IN_XML : node.getTextContent();
    }

    public static String getNodeLabel(Node node) {
        return node == null ?
                null :
                Optional.ofNullable(node.getAttributes().getNamedItem("label"))
                        .map(Node::getTextContent)
                        .orElse(null);
    }

    public static Node getNodeByXPath(String documentAsString, String xpathExpression) {
        try {
            var document = createDocumentFromString(documentAsString);
            var xpathFactory = XPathFactory.newInstance();
            var xpath = xpathFactory.newXPath();
            var expr = xpath.compile(xpathExpression);
            return (Node) expr.evaluate(document, XPathConstants.NODE);
        } catch (ParserConfigurationException | IOException | SAXException | XPathException e) {
            log.error("Unable to evaluate xpath {}", xpathExpression, e);
            throw new GenericTechnicalException("invalid xpath or xml", e);
        }
    }

    public static Collection<String> findAllTags(String rawXml, String fromPath) {

        try {
            var doc = createDocumentFromString(rawXml);
            var xPath = XPathFactory.newInstance().newXPath();
            var expression = "%s//*".formatted(fromPath);
            var nodes = xPath.compile(expression).evaluate(doc, XPathConstants.NODESET);
            if (nodes instanceof NodeList nodeList) {
                return IntStream.range(0, nodeList.getLength()).mapToObj(i -> nodeList.item(i).getNodeName()).distinct().toList();
            }
            log.warn("Unable to get 'other info' node list for xml {}", rawXml);
            return new ArrayList<>();
        } catch (ParserConfigurationException | IOException | SAXException | XPathException e) {
            log.error("Unable to get node keys {}", rawXml, e);
            throw new GenericTechnicalException("invalid path or xml", e);
        }
    }
}
