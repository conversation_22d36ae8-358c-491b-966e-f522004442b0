package com.erhgo.utils;

import lombok.extern.slf4j.Slf4j;

import java.nio.ByteBuffer;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Set;
import java.util.UUID;

@Slf4j
public class ChecksumUtils {
    private static final int UUID_BYTES_SIZE = 16;

    public static String calculateChecksum(Set<UUID> uuidSet) {
        var bb = ByteBuffer.allocate(uuidSet.size() * UUID_BYTES_SIZE);
        uuidSet.forEach(x -> {
            bb.putLong(x.getMostSignificantBits());
            bb.putLong(x.getLeastSignificantBits());
        });

        try {
            var digest = MessageDigest.getInstance("SHA-1");
            var bytes = digest.digest(bb.array());
            return bytesToHex(bytes);
        } catch (NoSuchAlgorithmException e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    private static String bytesToHex(byte[] bytes) {
        var sb = new StringBuilder();
        for (var b : bytes) {
            var hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) sb.append('0');
            sb.append(hex);
        }
        return sb.toString();
    }
}
