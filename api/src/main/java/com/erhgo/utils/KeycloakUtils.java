package com.erhgo.utils;

import com.erhgo.services.keycloak.UserRepresentation;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class KeycloakUtils {

    public static UserRepresentation defaultUserRepresentation(String userId) {
        log.warn("Unknown Keycloak user {}", userId);
        return new UserRepresentation().setId(userId);
    }

}
