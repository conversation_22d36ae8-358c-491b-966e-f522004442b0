package com.erhgo.repositories;

import com.erhgo.domain.recruitment.RecruitmentProfile;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.UUID;

public interface RecruitmentProfileRepository extends JpaRepository<RecruitmentProfile, UUID> {
    long countByJobId(UUID jobId);

    void deleteByJobId(UUID id);

    List<RecruitmentProfile> findByJobIdOrderByTitleAsc(UUID jobId);

    List<RecruitmentProfile> findByContextQuestionsQuestionUuidAndContextQuestionsContextId(UUID questionId, UUID contextId);

}
