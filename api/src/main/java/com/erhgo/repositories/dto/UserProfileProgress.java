package com.erhgo.repositories.dto;

import com.erhgo.domain.userprofile.GeneralInformation;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.UserRegistrationState;

public interface UserProfileProgress {

    float getMasteryLevel();

    int getCapacitiesCount();

    int getExperiencesCount();

    boolean isHasBehaviors();

    int getCandidaturesCount();

    UserProfile getUserProfile();

    default String getUserId() {
        return getUserProfile().userId();
    }

    UserRegistrationState getUserRegistrationState();

    GeneralInformation getGeneralInformation();
}
