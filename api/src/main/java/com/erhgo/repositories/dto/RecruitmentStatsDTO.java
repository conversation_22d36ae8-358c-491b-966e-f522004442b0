package com.erhgo.repositories.dto;

import java.time.Instant;

public interface RecruitmentStatsDTO {

    String getOrganizationTitle();

    String getOrganizationCode();

    Long getOrganizationId();

    Instant getLastRecruitmentPublicationDate();

    int getTotalCandidatures();

    int getTotalUsersInChannel();

    int getTotalCandidaturesArchived();

    int getSpontaneousCandidaturesArchived();

    int getOpenRecruitmentsCount();

    int getClosedRecruitmentsCount();

    int getTotalTransmittedCandidatures();

    int getSelectedCandidaturesCount();

    int getContactedCandidaturesCount();

    int getRefusedCandidaturesCount();

    int getSpontaneousCandidaturesCount();

    String getConnectedAts();

}


