package com.erhgo.repositories.dto;

import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.userprofile.Location;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.util.UUID;

public interface CandidatureCountDTO {

    Integer getBaseSalary();

    Integer getMaxSalary();

    Location getLocation();

    int getToContactCandidatureCount();

    int getContactedCandidatureCount();

    int getNewFromUser();

    int getNewGenerated();

    int getTotalFromUser();

    int getTotalGenerated();

    int getFavoriteCandidatureCount();

    int getDismissCandidatureCount();

    String getJobTitle();

    RecruitmentState getRecruitmentState();

    Long getRecruitmentId();

    Instant getPublicationDate();

    OffsetDateTime getPublicationEndDate();

    int getNotifiedUsersCount();

    String getManagerUserId();

    OffsetDateTime getLastProcessingDate();

    Recruitment.ProcessingType getLastProcessingType();

    UUID getJobId();
}
