package com.erhgo.repositories;

import com.erhgo.domain.externaloffer.ExternalOffer;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface ExternalOfferRepository extends JpaRepository<ExternalOffer, UUID> {

    @EntityGraph(value = ExternalOffer.ENTITY_GRAPH_WITH_HISTORY, type = EntityGraph.EntityGraphType.LOAD)
    @Query("""
                FROM ExternalOffer o
                WHERE o.atsCode=:atsCode
                AND (:recruiterCode IS NULL OR o.computedRecruiterCode=:recruiterCode)
                AND (:configCode IS NULL OR o.configCode IS NULL OR o.configCode=:configCode)
            """)
    List<ExternalOffer> findExternalOffersForConfig(String atsCode, String recruiterCode, String configCode);

    Optional<ExternalOffer> findByRemoteIdAndComputedRecruiterCodeAndAtsCode(String remoteId, String recruiterCode, String atsCode);

    @EntityGraph(value = ExternalOffer.ENTITY_GRAPH_WITH_HISTORY, type = EntityGraph.EntityGraphType.LOAD)
    @Query("""
                FROM ExternalOffer o
                WHERE o.computedRecruiterCode=:recruiterCode
                ORDER BY o.remoteLastModificationDate DESC
            """)
    Page<ExternalOffer> findExternalOffersForRecruiter(String recruiterCode, Pageable pageable);

}
