package com.erhgo.repositories;

import com.erhgo.domain.candidature.job.CandidatureNote;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.UUID;

public interface CandidatureNoteRepository extends PagingAndSortingRepository<CandidatureNote, UUID>, NoteRepository<CandidatureNote>, CrudRepository<CandidatureNote, UUID> {

}
