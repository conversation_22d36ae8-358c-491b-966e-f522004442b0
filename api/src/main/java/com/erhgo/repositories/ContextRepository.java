package com.erhgo.repositories;

import com.erhgo.domain.KeycloakUserSummary;
import com.erhgo.domain.referential.Context;
import org.apache.logging.log4j.util.Strings;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.UUID;
import java.util.regex.Pattern;

public interface ContextRepository extends JpaRepository<Context, UUID> {

    List<Context> findByTitleContainingIgnoreCaseAndCategoryLevelCategoryCodeIgnoreCase(String query, String categoryCode);

    @Query("""
            SELECT c 
            FROM Context c 
            WHERE (:index IS NULL OR c.index = :index) 
            AND (:keycloakUser IS NULL OR c.lastModifiedBy.keycloakId = :keycloakUser)
            """
    )
    Page<Context> findByIndexAndLastModifiedBy(Long index, KeycloakUserSummary keycloakUser, Pageable pageable);

    @Query("""
            SELECT c
            FROM Context c 
            WHERE LOWER(c.title) LIKE CONCAT('%', LOWER(:title), '%')
            AND (:keycloakUser IS NULL OR c.lastModifiedBy.keycloakId = :keycloakUser)
            """
    )
    Page<Context> findByTitleContainingIgnoreCaseAndLastModifiedBy(String title, KeycloakUserSummary keycloakUser, Pageable pageable);

    Page<Context> findByLastModifiedBy(KeycloakUserSummary keycloakUser, Pageable pageable);

    default Page<Context> findByIndexOrTitleContainingIgnoreCase(String query, String userId, Pageable pageable) {
        KeycloakUserSummary keycloakUser = null;
        if (Strings.isNotBlank(userId)) {
            keycloakUser = new KeycloakUserSummary(userId);
        }
        var matcher = Pattern.compile("^CT-([0-9]+)$").matcher(query.trim());
        if (matcher.matches() && matcher.groupCount() == 1) {
            return this.findByIndexAndLastModifiedBy(Long.parseLong(matcher.group(1)), keycloakUser, pageable);
        } else {
            return this.findByTitleContainingIgnoreCaseAndLastModifiedBy(query, keycloakUser, pageable);
        }
    }

    @Override
    default Context save(Context entity) {
        return this.saveAndFlush(entity);
    }
}
