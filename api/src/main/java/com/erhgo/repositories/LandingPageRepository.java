package com.erhgo.repositories;

import com.erhgo.domain.landingpage.LandingPage;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface LandingPageRepository extends JpaRepository<LandingPage, UUID> {

    Optional<LandingPage> getByUrlKey(String urlKey);

    List<LandingPage> getByUrlKeyIn(Collection<String> urlKeys);
}
