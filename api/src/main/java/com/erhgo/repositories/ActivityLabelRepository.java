package com.erhgo.repositories;

import com.erhgo.domain.referential.AbstractActivityLabel;
import com.erhgo.services.dto.criteria.ActivityCriteria;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.UUID;

@NoRepositoryBean
public interface ActivityLabelRepository<A extends AbstractActivityLabel> extends JpaRepository<A, UUID> {

    List<A> findByActivityUuid(UUID uuid);

    // Requests activities for which there is no capacity both in filter AND not in activity's recursively induced capacities
    @Query(
            """
                    SELECT DISTINCT al
                    FROM #{#entityName} al
                    INNER JOIN al.activity AS a
                    WHERE (?#{#criteria.filter()} IS NULL OR al.title LIKE %?#{#criteria.filter()}%)
                    AND (?#{#criteria.hasUserId()} = false OR al.lastModifiedBy = ?#{#criteria.userFilter()}) 
                    AND (?#{#criteria.hasCapacity()} = false OR NOT EXISTS (SELECT v FROM Capacity v WHERE v.id in (?#{#criteria.capacityIds()}) 
                    AND v NOT IN (SELECT c FROM Activity a2 INNER JOIN a2.inducedCapacities c WHERE a2 = a)
                    AND (?#{#criteria.isCapacityRecursive()} = false OR v NOT IN (SELECT c2 FROM Activity a2 INNER JOIN a2.inducedCapacities c INNER JOIN c.inducedCapacities c2 WHERE a2 = a))
                    AND (?#{#criteria.isCapacityRecursive()} = false OR v NOT IN (SELECT c3 FROM Activity a2 INNER JOIN a2.inducedCapacities c INNER JOIN c.inducedCapacities c2 INNER JOIN c2.inducedCapacities c3 WHERE a2 = a))))
                    """
    )
    Page<A> findByCriteria(@Param("criteria") ActivityCriteria criteria, Pageable pageable);
}
