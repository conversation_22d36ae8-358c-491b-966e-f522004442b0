package com.erhgo.repositories;

import com.erhgo.domain.enums.BehaviorCategory;
import com.erhgo.domain.referential.Behavior;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.UUID;

public interface BehaviorRepository extends JpaRepository<Behavior, UUID> {

    @Query("SELECT b " +
            "FROM Behavior b " +
            "WHERE :query IS NULL OR b.code = :query OR LOWER(b.title) LIKE CONCAT('%', LOWER(:query), '%')")
    Page<Behavior> findBehavior(
            String query,
            Pageable pageable);

    List<Behavior> findByBehaviorCategory(BehaviorCategory behaviorCategory);


}
