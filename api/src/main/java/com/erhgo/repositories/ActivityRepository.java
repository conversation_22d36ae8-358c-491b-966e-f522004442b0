package com.erhgo.repositories;

import com.erhgo.domain.referential.Activity;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.JobActivityLabel;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Set;
import java.util.UUID;

public interface ActivityRepository extends JpaRepository<Activity, UUID> {
    @Query("""
            SELECT a AS activity,
               COUNT(DISTINCT ic) AS commonCapacitiesCount
               FROM Activity a
               INNER JOIN a.inducedCapacities ic
               INNER JOIN JobActivityLabel jal ON jal.activity = a
               INNER JOIN OccupationActivity oa ON oa.activity = jal
               WHERE ic IN (:selectedCapacities)
               GROUP BY a.uuid
               ORDER BY COUNT(DISTINCT ic) DESC
            """
    )
    List<RelativeActivityDTO> topActivitiesForRelativeCapacities(Set<Capacity> selectedCapacities, Pageable pageable);

    @Query("""
                SELECT DISTINCT jal
                FROM JobActivityLabel jal
                WHERE LOWER(jal.title) LIKE LOWER(CONCAT('%', :query, '%'))
                ORDER BY jal.title
            """)
    List<JobActivityLabel> searchActivities(@Param("query") String query);

    interface RelativeActivityDTO {
        Activity getActivity();

        int getCommonCapacitiesCount();

        default UUID getActivityUuid() {
            return getActivity().getUuid();
        }
    }
}
