package com.erhgo.repositories;

import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.QuestionForContexts;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.UUID;

public interface QuestionForContextsRepository extends JpaRepository<QuestionForContexts, UUID> {
    List<QuestionForContexts> findByContextsIn(List<Context> contexts);

    @Query("SELECT DISTINCT cq FROM QuestionForContexts AS cq " +
            "JOIN cq.contexts AS c " +
            "JOIN c.categoryLevel AS cl " +
            "WHERE (:title IS null OR (lower(cq.title)) LIKE CONCAT('%',lower(:title), '%')) " +
            "AND (:contextId IS null OR (c.id = :contextId)) " +
            "AND (:categoryId IS null OR (cl.category.id = :categoryId))")
    Page<QuestionForContexts> searchQuestionForContext(String title, UUID contextId, Long categoryId, Pageable pageable);

}
