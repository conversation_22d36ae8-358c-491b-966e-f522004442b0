package com.erhgo.repositories;

import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.esco.EscoSkill;
import com.erhgo.domain.job.Mission;
import com.erhgo.domain.referential.JobActivityLabel;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.UUID;
import java.util.stream.Stream;

public interface JobActivityLabelRepository extends ActivityLabelRepository<JobActivityLabel> {

    @Query("SELECT count(al)>0 FROM JobActivityLabel AS al " +
            "WHERE al.uuid = :uuid " +
            "AND (al IN (SELECT a from EscoSkill e JOIN e.activities a) " +
            "OR al IN (SELECT activityLabel from OptionalActivity) " +
            "OR al IN (SELECT a.activity from ErhgoOccupation eo JOIN eo.entities a) " +
            "OR al IN (SELECT a from Mission mi join mi.activities a) )")
    boolean isActivityUsedByAnyEntity(UUID uuid);

    @Modifying
    @Query("UPDATE OptionalActivity SET activityLabel = :newLabel WHERE activityLabel in :oldLabels")
    void updateOptionalActivity(JobActivityLabel newLabel, Collection<JobActivityLabel> oldLabels);

    @Query("SELECT DISTINCT mi FROM Mission mi join mi.activities a where a in :oldLabels")
    Stream<Mission> findMissions(Collection<JobActivityLabel> oldLabels);

    @Query("SELECT DISTINCT es FROM EscoSkill es JOIN es.activities a where a in :oldLabels")
    Stream<EscoSkill> findSkills(Collection<JobActivityLabel> oldLabels);

    @Query("SELECT DISTINCT eo FROM ErhgoOccupation eo JOIN OccupationActivity oa ON eo = oa.occupation where oa.activity in :oldLabels")
    Stream<ErhgoOccupation> findOccupation(Collection<JobActivityLabel> oldLabels);

    default void bulkUpdateActivityLabel(JobActivityLabel newLabel, Collection<JobActivityLabel> oldLabels) {
        this.updateOptionalActivity(newLabel, oldLabels);
        this.findMissions(oldLabels).forEach(l -> l.replaceActivities(oldLabels, newLabel));
        this.findSkills(oldLabels).forEach(l -> l.replaceActivities(oldLabels, newLabel));
        this.findOccupation(oldLabels).forEach(l -> l.replaceActivities(oldLabels, newLabel));
        this.flush();
    }

}
