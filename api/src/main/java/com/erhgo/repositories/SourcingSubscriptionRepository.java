package com.erhgo.repositories;

import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.sourcing.SourcingSubscription;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.OffsetDateTime;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface SourcingSubscriptionRepository extends JpaRepository<SourcingSubscription, UUID> {
    Optional<SourcingSubscription> findOneByRecruiter(Recruiter recruiter);

    List<SourcingSubscription> findByCreatedDateBeforeAndMailStateAndInvitationIsNull(Date createdAfter, SourcingSubscription.SourcingMailState sendWelcome);

    List<SourcingSubscription> findByExpirationDateBeforeAndMailStateAndInvitationIsNull(OffsetDateTime createdAfter, SourcingSubscription.SourcingMailState sendWelcome);

    List<SourcingSubscription> findByExpirationDateBeforeAndInvitationIsNull(OffsetDateTime date);
}
