package com.erhgo.repositories.classifications;

import com.erhgo.domain.classifications.rome.RomeOccupation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface RomeOccupationRepository extends JpaRepository<RomeOccupation, String> {
    @Query("SELECT r FROM RomeOccupation AS r " +
            "WHERE :query IS NULL OR r.code = :query OR LOWER(r.title) LIKE CONCAT('%', LOWER(:query), '%')")
    Page<RomeOccupation> searchRomes(String query, Pageable pageable);

    List<RomeOccupation> findAllByCodeIn(List<String> codes);
}
