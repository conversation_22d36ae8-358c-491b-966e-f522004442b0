package com.erhgo.repositories.classifications;

import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.Collection;
import java.util.List;

public interface ErhgoClassificationRepository extends PagingAndSortingRepository<ErhgoClassification, String>, CrudRepository<ErhgoClassification, String> {
    List<ErhgoClassification> findAllByOrderByHighPriorityDescOrderIndexAsc();

    @Query("SELECT DISTINCT ec " +
            "FROM ErhgoClassification ec " +
            "WHERE ec.code IN (:codes)")
    List<ErhgoClassification> findErhgoClassificationByCodeIn(Collection<String> codes);

}
