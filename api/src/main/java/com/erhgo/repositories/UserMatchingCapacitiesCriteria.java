package com.erhgo.repositories;

import lombok.Builder;
import lombok.Data;

import java.util.Collection;

@Data
@Builder
public class UserMatchingCapacitiesCriteria {
    private final Collection<Long> capacitiesIds;
    private final int capacitiesLength;
    private final float capacityThreshold;
    private final int minLevel;
    private final Collection<Long> recruitmentIds;
    private final Collection<String> channels;
    private final Collection<String> excludedChannels;
    private final boolean noChannel;
    private final boolean affectedToNoChannel;
    private final float masteryLevelRange;
    private final long limit;
    private final long offset;
    private final String postcode;
    private final Collection<String> criteriaValueCodes;
}
