package com.erhgo.repositories;

import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Employer;
import com.erhgo.domain.referential.Recruiter;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;

public interface EmployerRepository extends AbstractRepository<Employer> {

    @Query("FROM Employer e " +
            "WHERE e.refererRecruiter in :referers " +
            "AND (:filter IS NULL or UPPER(e.title) like UPPER(CONCAT('%', :filter, '%')))")
    Page<Employer> findEmployers(String filter, Collection<Recruiter> referers, Pageable pageable);

    @Query("FROM AbstractOrganization ao " +
            "JOIN Employer e " +
            "WHERE e.refererRecruiter in :referers " +
            "AND (:filter IS NULL or UPPER(e.title) like UPPER(CONCAT('%', :filter, '%')))")
    Page<AbstractOrganization> findEmployersPage(String filter, Collection<Recruiter> referers, Pageable pageable);

}
