package com.erhgo.repositories;

import com.erhgo.domain.referential.AbstractOrganization;

import java.util.Collection;
import java.util.List;

public interface AbstractOrganizationRepository extends AbstractRepository<AbstractOrganization> {

    List<AbstractOrganization> findByCodeIn(Collection<String> channels);


    Collection<AbstractOrganization> findByPrivateUsers(boolean privateUsers);

    List<AbstractOrganization> findByCodeIn(List<String> codes);
}
