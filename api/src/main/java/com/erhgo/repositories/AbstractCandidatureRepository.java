package com.erhgo.repositories;

import com.erhgo.domain.candidature.job.AbstractCandidature;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.enums.CandidatureSynchronizationState;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.Repository;

import java.time.OffsetDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Optional;

public interface AbstractCandidatureRepository extends Repository<AbstractCandidature, Long> {

    Optional<AbstractCandidature> findById(Long id);

    List<AbstractCandidature> findByGlobalCandidatureStateInAndUpdatedDateBeforeAndArchivedIsFalse(Collection<GlobalCandidatureState> globalCandidatureState, Date updatedDate);

    List<AbstractCandidature> findByUserProfileUserIdAndModifiedByUserIsTrueAndArchivedIsFalse(String userId);

    List<AbstractCandidature> findByUserProfileUserId(String userId);

    List<AbstractCandidature> findByArchivedIsFalse();

    AbstractCandidature save(AbstractCandidature entity);

    @Query("""
            SELECT DISTINCT c
            FROM AbstractCandidature c
            LEFT JOIN FETCH c.recruitment r
            LEFT JOIN FETCH r.recruitmentProfile rp
            LEFT JOIN FETCH rp.job j
            LEFT JOIN FETCH j.recruiter rec
            LEFT JOIN FETCH r.sourcingUsersIdToNotify notify
            INNER JOIN UserExperience ux on ux.userProfile=c.userProfile
            WHERE c.synchronizationState=:candidatureSynchronizationState
            AND c.submissionDate <= :submissionDateBefore
            AND c.submissionDate >= :submissionDateAfter
            """)
    List<AbstractCandidature> findCandidaturesToNotifyImmediately(CandidatureSynchronizationState candidatureSynchronizationState, OffsetDateTime submissionDateBefore, OffsetDateTime submissionDateAfter);
}
