package com.erhgo.repositories;

import com.erhgo.domain.enums.QuestionType;
import com.erhgo.domain.referential.CapacityRelatedQuestion;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.UUID;

public interface CapacityRelatedQuestionRepository extends JpaRepository<CapacityRelatedQuestion, UUID> {

    Page<CapacityRelatedQuestion> findByQuestionTypeOrderByQuestionIndex(
            QuestionType questionType,
            Pageable pageable
    );

}
