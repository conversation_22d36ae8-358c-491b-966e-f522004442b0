package com.erhgo.repositories;

import com.erhgo.domain.userprofile.ScrapedUser;
import com.erhgo.domain.userprofile.UserProfileCreationState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface ScrapedUserRepository extends JpaRepository<ScrapedUser, UUID> {
    boolean existsByCandidateId(String candidateId);

    Optional<ScrapedUser> findByCandidateId(String candidateId);

    List<ScrapedUser> findByCreatedDateBeforeAndCreationState(Date createdDate, UserProfileCreationState creationState);
}
