package com.erhgo.repositories;

import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.experience.UserExperience;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Set;
import java.util.UUID;

public interface UserExperienceRepository extends JpaRepository<UserExperience, UUID> {

    List<UserExperience> findByUserProfileUuid(UUID userProfileUuid);

    List<UserExperience> findByUserProfileUserId(String userId);

    List<UserExperience> findByErhgoOccupationId(UUID erhgoOccupationId);

    List<UserExperience> findByErhgoOccupationIn(List<ErhgoOccupation> impactedOccupations);

    @Modifying
    @Query("UPDATE UserProfile u SET u.requiresCapacitiesRefresh = true WHERE u in (:impactedUsers)")
    void markUsersAsStaleForCapacityDenormalization(Set<UserProfile> impactedUsers);
}
