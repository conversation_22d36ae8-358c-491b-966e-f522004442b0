package com.erhgo.repositories;

import com.erhgo.domain.userprofile.UserMobileToken;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.OffsetDateTime;
import java.util.*;

public interface UserMobileTokenRepository extends JpaRepository<UserMobileToken, UUID> {
    Collection<UserMobileToken> findByUserProfileUserId(String userId);

    // https://firebase.google.com/docs/cloud-messaging/manage-tokens?hl=en&authuser=0#ensuring-registration-token-freshness
    @Query("SELECT umt FROM UserMobileToken umt WHERE umt.timestamp <= :fromDate")
    List<UserMobileToken> findObsoleteUserMobileTokens(OffsetDateTime fromDate);

    Set<UserMobileToken> findByUserProfileUserIdIn(Collection<String> userIds);

    Optional<UserMobileToken> findFirstByUserProfileUserIdOrderByTimestampDesc(String userId);
}
