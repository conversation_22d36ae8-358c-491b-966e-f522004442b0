package com.erhgo.repositories;

import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.enums.JobEvaluationState;
import com.erhgo.domain.job.Job;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.UUID;

public interface JobRepository extends JpaRepository<Job, UUID> {

    String SELECT_JOB_BY_ORGANIZATION_CODE = "SELECT j " +
            "FROM Job j " +
            "INNER JOIN j.recruiter r " +
            "LEFT JOIN j.employer e " +
            "WHERE ( " +
            "   r.code in (:organizationCodes) " +
            "   OR e.code in (:organizationCodes) " +
            ") ";

    @Query("SELECT j " +
            "FROM Job j " +
            "INNER JOIN j.recruiter r " +
            "LEFT JOIN j.employer e " +
            "WHERE ( " +
            "   r.code in (:organizationCodes) " +
            "   OR e.code in (:organizationCodes) " +
            ")" +
            "AND LOWER(j.title) LIKE %:title%" +
            " OR LOWER(j.service) LIKE %:title%" +
            " OR LOWER(e.title) LIKE %:title%")
    Page<Job> findJobs(
            Set<String> organizationCodes,
            String title,
            Pageable pageable);

    @Query(SELECT_JOB_BY_ORGANIZATION_CODE)
    Page<Job> findByRecruiterCodeInOrEmployerCodeIn(Collection<String> organizationCodes,
                                                    Pageable pageable);

    default List<Job> findAllPublishedJob(Set<String> organizationCodes) {

        return findByOrganizationCodeInAndStateIn(organizationCodes, Set.of(JobEvaluationState.PUBLISHED, JobEvaluationState.FINISHED));
    }

    @Query(SELECT_JOB_BY_ORGANIZATION_CODE +
            "AND j.state IN  (:states)")
    List<Job> findByOrganizationCodeInAndStateIn(Collection<String> organizationCodes, Set<JobEvaluationState> states);

    List<Job> findByErhgoOccupation(ErhgoOccupation occupation);

}
