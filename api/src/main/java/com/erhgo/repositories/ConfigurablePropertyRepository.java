package com.erhgo.repositories;

import com.erhgo.config.ConfigurableProperty;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

public interface ConfigurablePropertyRepository extends JpaRepository<ConfigurableProperty, String> {
    ConfigurableProperty findOneByPropertyKey(String propertyKey);

    default String getPropertyValueOrDefaults(String propertyValue, String... propertyDiscriminators) {
        return Stream.concat(Stream.of(propertyDiscriminators), Stream.of("default"))
                .map(c -> findOneByPropertyKey("%s.%s".formatted(propertyValue, c)))
                .filter(Objects::nonNull)
                .findFirst()
                .map(ConfigurableProperty::getPropertyValue)
                .orElse(null);
    }

    List<ConfigurableProperty> findByPropertyKeyStartsWith(String prefix);
}

