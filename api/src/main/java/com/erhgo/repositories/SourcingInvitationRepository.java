package com.erhgo.repositories;

import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.sourcing.SourcingInvitation;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface SourcingInvitationRepository extends PagingAndSortingRepository<SourcingInvitation, UUID>, CrudRepository<SourcingInvitation, UUID> {
    Optional<SourcingInvitation> findOneByCode(String code);

    List<SourcingInvitation> findBySubscriptionsRecruiter(AbstractOrganization recruiter);

    List<SourcingInvitation> findBySubscriptionsRecruiterIn(Collection<? extends AbstractOrganization> toList);
}
