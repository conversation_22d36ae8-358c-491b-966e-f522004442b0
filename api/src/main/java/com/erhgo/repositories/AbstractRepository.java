package com.erhgo.repositories;

import com.erhgo.domain.AbstractEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

@NoRepositoryBean
public interface AbstractRepository<E extends AbstractEntity> extends CrudRepository<E, Long>, PagingAndSortingRepository<E, Long> {

    E findOneByCode(@Param("code") String code);

    Page<E> findByCodeContainingIgnoreCaseOrTitleContainingIgnoreCase(String code, String title, Pageable pageable);
}
