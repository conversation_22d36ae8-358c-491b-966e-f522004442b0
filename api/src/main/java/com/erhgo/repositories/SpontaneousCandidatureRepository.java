package com.erhgo.repositories;

import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.candidature.job.SpontaneousCandidature;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.repositories.dto.SourcingCandidateDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.time.OffsetDateTime;
import java.util.Collection;
import java.util.List;

public interface SpontaneousCandidatureRepository extends PagingAndSortingRepository<SpontaneousCandidature, Long>, CrudRepository<SpontaneousCandidature, Long> {

    List<SpontaneousCandidature> findByRecruiterInAndUserProfile(List<Recruiter> recruiters, UserProfile user);

    @Query("""
            SELECT up as userProfile,
            count(distinct recruitmentCandidature.id) as nbCandidatures,
            spontaneousCandidature as candidature
            FROM SpontaneousCandidature spontaneousCandidature
            INNER JOIN spontaneousCandidature.userProfile up
            LEFT JOIN RecruitmentCandidature recruitmentCandidature ON
                recruitmentCandidature.userProfile = up
                AND recruitmentCandidature.globalCandidatureState IN (:finalizedStates)
                AND recruitmentCandidature.recruitment in (SELECT r FROM Recruitment  r JOIN r.recruitmentProfile rp JOIN rp.job j WHERE j.recruiter in (:recruiters))
                AND recruitmentCandidature.archived = FALSE
            WHERE spontaneousCandidature.recruiter IN (:recruiters)
                AND spontaneousCandidature.globalCandidatureState IN (:relatedStates)
                AND spontaneousCandidature.archived = FALSE
            GROUP BY up.uuid, spontaneousCandidature.id
            HAVING spontaneousCandidature.id IS NOT NULL OR COUNT(DISTINCT recruitmentCandidature.id) > 0
            """)
    Page<SourcingCandidateDTO> _internalFindAllCandidates(
            Collection<Recruiter> recruiters,
            List<GlobalCandidatureState> relatedStates,
            List<GlobalCandidatureState> finalizedStates,
            Pageable pageRequest
    );

    default Page<SourcingCandidateDTO> findAllCandidates(Collection<Recruiter> recruiters, List<GlobalCandidatureState> relatedStates, Pageable pageRequest) {
        return _internalFindAllCandidates(recruiters, relatedStates, GlobalCandidatureState.finalizedValues(), pageRequest);
    }

    @Query("""
            SELECT c
            FROM SpontaneousCandidature c
            WHERE EXISTS (SELECT 1 FROM SpontaneousCandidature c2 WHERE c2.recruiter=c.recruiter AND c2.globalCandidatureState IN (
                        com.erhgo.domain.candidature.job.GlobalCandidatureState.NEW,
                        com.erhgo.domain.candidature.job.GlobalCandidatureState.NOT_TREATED_BY_ERHGO,
                        com.erhgo.domain.candidature.job.GlobalCandidatureState.STAND_BY,
                        com.erhgo.domain.candidature.job.GlobalCandidatureState.INTERNAL_POSITION,
                        com.erhgo.domain.candidature.job.GlobalCandidatureState.INTRODUCE_TO_CLIENT,
                        com.erhgo.domain.candidature.job.GlobalCandidatureState.SUMMARY_SHEET_SENT
            ))
            AND c.globalCandidatureState in (:#{T(com.erhgo.domain.candidature.job.GlobalCandidatureState).finalizedValues()})
            AND c.archived = FALSE
            """)
    List<SpontaneousCandidature> findCandidaturesWithAtLeastOneUntreatedCandidatureOnRecruiter();

    SpontaneousCandidature findOneByRecruiterAndUserProfile(Recruiter recruiter, UserProfile userProfile);

    List<SpontaneousCandidature> findByRecruiterCodeAndArchivedIsFalse(String organizationCode);

    @Query(nativeQuery = true, value = """
                SELECT
                    DATE_FORMAT(submissionDate, '%Y-%m') AS month,
                    COUNT(*) AS totalCandidatureCount
                FROM SpontaneousCandidature
                WHERE SpontaneousCandidature.submissionDate IS NOT NULL
                GROUP BY DATE_FORMAT(submissionDate, '%Y-%m')
                ORDER BY month DESC
                LIMIT 12
            """)
    List<SpontaneousCandidaturesMonthlyDTO> getMonthlySpontaneousCandidaturesStats();

    interface SpontaneousCandidaturesMonthlyDTO {
        String getMonth();

        Integer getTotalCandidatureCount();
    }


    @Query("""
            SELECT DISTINCT c
            FROM SpontaneousCandidature c
            INNER JOIN FETCH c.recruiter rec
            INNER JOIN FETCH c.userProfile up
            INNER JOIN up.experiences
            WHERE (rec.code in (:recruiterCodesToConsider))
            AND c.submissionDate > :begin
            AND c.submissionDate < :end
            AND c.synchronizationState = com.erhgo.domain.enums.CandidatureSynchronizationState.WAITING
            """)
    List<SpontaneousCandidature> findCandidaturesToHandleForATS(List<String> recruiterCodesToConsider, OffsetDateTime begin, OffsetDateTime end);

}
