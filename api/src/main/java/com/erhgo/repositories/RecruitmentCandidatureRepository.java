package com.erhgo.repositories;

import com.erhgo.domain.candidature.job.CandidatureEmailRefusalState;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.enums.CandidatureState;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.repositories.dto.CandidatureCountDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.time.OffsetDateTime;
import java.util.*;

public interface RecruitmentCandidatureRepository extends PagingAndSortingRepository<RecruitmentCandidature, Long>, CrudRepository<RecruitmentCandidature, Long> {
    String COUNT_CANDIDATES_BY_STATE_QUERY = """
            SELECT
            r.publicationDate as publicationDate,
            r.publicationEndDate as publicationEndDate,
            r.id as recruitmentId,
            r.state as recruitmentState,
            r.location as location,
            r.baseSalary as baseSalary,
            r.maxSalary as maxSalary,
            r.lastProcessingType as lastProcessingType,
            r.lastProcessingDate as lastProcessingDate,
            j.title AS jobTitle,
            j.id AS jobId,
            (SELECT COUNT(distinct rn.userProfile.id) FROM RecruitmentNotification rn where rn.recruitment.id = r.id) as notifiedUsersCount,
            r.managerUserId as managerUserId,
            COALESCE(
               SUM(CASE WHEN c.globalCandidatureState IN (
                  com.erhgo.domain.candidature.job.GlobalCandidatureState.NOT_TREATED_BY_ERHGO,
                  com.erhgo.domain.candidature.job.GlobalCandidatureState.NEW,
                  com.erhgo.domain.candidature.job.GlobalCandidatureState.STAND_BY
               ) AND c.generatedForSourcing = FALSE  then 1 ELSE 0 END), 0) AS newFromUser,
            COALESCE(
               SUM(CASE WHEN c.globalCandidatureState IN (
                  com.erhgo.domain.candidature.job.GlobalCandidatureState.NOT_TREATED_BY_ERHGO,
                  com.erhgo.domain.candidature.job.GlobalCandidatureState.NEW,
                  com.erhgo.domain.candidature.job.GlobalCandidatureState.STAND_BY
               ) AND c.generatedForSourcing = TRUE  then 1 ELSE 0 END), 0) AS newGenerated,
            COALESCE(SUM(CASE WHEN c.generatedForSourcing = TRUE AND c.globalCandidatureState NOT IN (
                  com.erhgo.domain.candidature.job.GlobalCandidatureState.MISSING_PREREQUISITE,
                  com.erhgo.domain.candidature.job.GlobalCandidatureState.NOT_FINALIZED
               ) AND c.globalCandidatureState IS NOT NULL THEN 1 ELSE 0 END), 0) AS totalGenerated,
            COALESCE(SUM(CASE WHEN c.generatedForSourcing = FALSE AND c.globalCandidatureState NOT IN (
                  com.erhgo.domain.candidature.job.GlobalCandidatureState.MISSING_PREREQUISITE,
                  com.erhgo.domain.candidature.job.GlobalCandidatureState.NOT_FINALIZED
               ) AND c.globalCandidatureState IS NOT NULL THEN 1 ELSE 0 END), 0) AS totalFromUser,
            COALESCE(
               SUM(CASE WHEN c.globalCandidatureState = com.erhgo.domain.candidature.job.GlobalCandidatureState.INTERNAL_POSITION then 1 ELSE 0 END), 0) AS toContactCandidatureCount,
            COALESCE(
               SUM(CASE WHEN c.globalCandidatureState IN (
                  com.erhgo.domain.candidature.job.GlobalCandidatureState.INTRODUCE_TO_CLIENT,
                  com.erhgo.domain.candidature.job.GlobalCandidatureState.SUMMARY_SHEET_SENT
               ) then 1 ELSE 0 END), 0) AS contactedCandidatureCount,
            COALESCE(
               SUM(CASE WHEN c.globalCandidatureState IN (
                  com.erhgo.domain.candidature.job.GlobalCandidatureState.ON_RECRUITMENT_CLIENT,
                  com.erhgo.domain.candidature.job.GlobalCandidatureState.RECRUITMENT_VALIDATED
               ) then 1 ELSE 0 END), 0) AS favoriteCandidatureCount,
            COALESCE(
               SUM(CASE WHEN c.globalCandidatureState IN (
                  com.erhgo.domain.candidature.job.GlobalCandidatureState.REFUSED_MEETING_CLIENT,
                  com.erhgo.domain.candidature.job.GlobalCandidatureState.REFUSED_ON_CALL,
                  com.erhgo.domain.candidature.job.GlobalCandidatureState.REFUSED_BY_CLIENT_WITH_SHEETS
               ) then 1 ELSE 0 END), 0) AS dismissCandidatureCount
            FROM Recruitment r
            LEFT JOIN RecruitmentCandidature c ON c.recruitment = r AND c.archived = FALSE
            INNER JOIN r.recruitmentProfile rp
            INNER JOIN rp.job j
            """;

    String RHONE_ALPES_DEPARTMENTS = "69,38,73,74,01,26,42,03,15,07,43,63";


    Optional<RecruitmentCandidature> findByRecruitmentCodeAndUserProfileUserId(String recruitmentCode, String userId);

    Collection<RecruitmentCandidature> findByUserProfileUserId(String userId);

    Optional<RecruitmentCandidature> findTopByUserProfileUserIdAndSubmissionDateIsNotNullOrderBySubmissionDateDesc(String userId);

    @Query("SELECT c " +
            "FROM RecruitmentCandidature c " +
            "INNER JOIN c.userProfile up " +
            "INNER JOIN c.recruitment recruitment " +
            "INNER JOIN recruitment.recruitmentProfile rp " +
            "INNER JOIN rp.job j " +
            "INNER JOIN j.recruiter rec ON (:isAdmin = TRUE OR rec.code in (:organizationsCodes)) " +
            "LEFT JOIN j.employer " +
            "WHERE up.userId = :userId " +
            "AND c.state = com.erhgo.domain.enums.CandidatureState.VALIDATED")
    List<RecruitmentCandidature> findUserCandidatures(String userId,
                                                      Collection<String> organizationsCodes,
                                                      Boolean isAdmin);

    List<RecruitmentCandidature> findByRecruitmentCodeAndState(String code, CandidatureState candidatureState);

    List<RecruitmentCandidature> findByRecruitmentCodeAndArchivedIsFalse(String recruitmentCode);

    Page<RecruitmentCandidature> findByRecruitmentCodeAndValid(String code, Boolean valid, Pageable pageable);

    List<RecruitmentCandidature> findTop5ByUserProfileAndArchivedIsFalseOrderByCreatedDateDesc(UserProfile userProfile);

    List<RecruitmentCandidature> findDistinctByRecruitmentRecruitmentProfileJobId(UUID jobId);

    Page<RecruitmentCandidature> findByRecruitmentIdAndGlobalCandidatureStateIn(Long recruitmentId, Pageable pageable, Collection<GlobalCandidatureState> filters);

    Page<RecruitmentCandidature> findByRecruitmentIdAndGlobalCandidatureStateInAndArchivedIsFalse(Long recruitmentId, Pageable pageable, Collection<GlobalCandidatureState> filters);


    @Query(value = COUNT_CANDIDATES_BY_STATE_QUERY +
            """
                           WHERE r.id IN (
                           SELECT r2.id
                           FROM Recruitment r2
                           JOIN r2.recruitmentProfile rp2
                           JOIN rp2.job j2
                           JOIN j2.recruiter rec2
                    WHERE rec2.code in :recruiterCodes)
                    GROUP BY r.id
                    """
    )
    List<CandidatureCountDTO> findSourcingRecruitments(List<String> recruiterCodes);

    List<RecruitmentCandidature> findDistinctByRecruitmentRecruitmentProfileJobRecruiterAndGlobalCandidatureStateInAndUserProfileAndArchivedIsFalseOrderBySubmissionDateDesc(Recruiter recruiter, List<GlobalCandidatureState> globalCandidatureStates, UserProfile userProfile);

    @Query("""
            SELECT DISTINCT c
            FROM RecruitmentCandidature c
            INNER JOIN FETCH c.recruitment recruitment
            INNER JOIN FETCH recruitment.recruitmentProfile rp
            INNER JOIN FETCH rp.job j
            INNER JOIN FETCH j.recruiter rec
            INNER JOIN FETCH recruitment.externalOffer e
            INNER JOIN FETCH c.userProfile up
            INNER JOIN up.experiences
            WHERE rec.code in (:recruiterCodes)
            AND c.submissionDate > :begin
            AND c.submissionDate < :end
            AND e.atsCode = :atsCode
            AND c.synchronizationState = com.erhgo.domain.enums.CandidatureSynchronizationState.WAITING
            """)
    List<RecruitmentCandidature> findCandidaturesToHandleForATS(Collection<String> recruiterCodes, String atsCode, OffsetDateTime begin, OffsetDateTime end);

    List<RecruitmentCandidature> findByCandidatureRefusalStateEmailSentAndSubmissionDateBefore(CandidatureEmailRefusalState emailSent, OffsetDateTime before);

    @Query(nativeQuery = true, value = """
                WITH
                months AS (
                    SELECT 1 AS seq UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION
                    SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION
                    SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION SELECT 12
                ),
                recruitments_with_month AS (
                    SELECT DISTINCT DATE_FORMAT(r.publicationDate, '%Y-%m') AS month, r.publicationDate,  r.publicationEndDate, r.id as recruitmentId
                    FROM Recruitment r
                    WHERE (
                        (:isRhoneAlpes = TRUE AND SUBSTRING(r.postcode, 1, 2) IN (:rhoneAlpesDepts))
                        OR
                        (:isRhoneAlpes = FALSE AND SUBSTRING(r.postcode, 1, 2) NOT IN (:rhoneAlpesDepts))
                    )
                    AND r.publicationDate IS NOT NULL
                    AND r.state != 0
                ),
                expanded_months AS (
                    SELECT
                        month,
                        recruitmentId
                    FROM (
                        SELECT
                            DATE_FORMAT(
                                DATE_ADD(publicationDate, INTERVAL (t.seq - 1) MONTH),
                                '%Y-%m'
                            ) AS month,
                            recruitmentId
                        FROM recruitments_with_month m
                        JOIN months t ON DATE_ADD(m.publicationDate, INTERVAL (t.seq - 1) MONTH) <=
                            CASE
                                WHEN m.publicationEndDate IS NULL
                                THEN DATE_ADD(sysdate(), INTERVAL 1 DAY)
                                ELSE m.publicationEndDate
                            END
                    ) AS expanded
                ),
                total_recruitments AS (
                    SELECT
                        m.month,
                        COUNT(DISTINCT m.recruitmentId) AS totalRecruitmentCount
                    FROM expanded_months m
                    GROUP BY m.month
                ),
                candidature_counts AS (
                          SELECT
                              m.month,
                              m.recruitmentId,
                              COUNT(DISTINCT c.id) AS candidature_count_per_month_per_recruitment,
                              SUM(IF(n.id IS NOT NULL, 1, 0)) as candidature_with_notification_count
                          FROM expanded_months m
                              LEFT JOIN RecruitmentCandidature c ON c.recruitment_id = m.recruitmentId
                              AND c.submissionDate IS NOT NULL
                              AND c.modifiedByUser = TRUE
                              AND DATE_FORMAT(c.submissionDate, '%Y-%m') = m.month
                          LEFT JOIN Notification n on n.DTYPE = 'RECRUITMENT' AND c.userProfile_uuid=n.userProfile_uuid AND n.recruitment_id = c.recruitment_id
                          GROUP BY m.month, m.recruitmentId
                      ),
                total_candidatures AS (
                    SELECT
                        month,
                        SUM(candidature_count_per_month_per_recruitment) AS total_candidature_count_per_month,
                        SUM(candidature_with_notification_count) AS total_candidature_with_notification_count
                    FROM candidature_counts
                    GROUP BY month
                ),
                average_counts AS (
                    SELECT
                        rc.month,
                        AVG(rc.candidature_count_per_month_per_recruitment) AS averageCandidatureCount,
                        AVG(NULLIF(rc.candidature_count_per_month_per_recruitment, 0)) AS averageCandidatureCountExcludingZero
                    FROM candidature_counts rc
                    GROUP BY rc.month
                )
                SELECT
                    tr.month,
                    amc.averageCandidatureCount,
                    amc.averageCandidatureCountExcludingZero,
                    tr.totalRecruitmentCount,
                    COALESCE(tc.total_candidature_count_per_month, 0) AS totalCandidatureCount,
                    COALESCE(tc.total_candidature_with_notification_count, 0) AS totalCandidatureWithNotificationCount
                FROM total_recruitments tr
                LEFT JOIN average_counts amc ON tr.month = amc.month
                LEFT JOIN total_candidatures tc ON tr.month = tc.month
                ORDER BY tr.month DESC
                LIMIT 12
            """)
    List<MonthlyCandidaturesDTO> getMonthlyApplicationsStats(@Param("isRhoneAlpes") boolean isRhoneAlpes,
                                                             @Param("rhoneAlpesDepts") List<String> rhoneAlpesDepts);

    default List<MonthlyCandidaturesDTO> getMonthlyApplicationsStats(boolean isRhoneAlpes) {
        var rhoneAlpesDepts = Arrays.asList(RHONE_ALPES_DEPARTMENTS.split(","));
        return getMonthlyApplicationsStats(isRhoneAlpes, rhoneAlpesDepts);
    }

    interface MonthlyCandidaturesDTO {
        String getMonth();

        Float getAverageCandidatureCount();

        Float getAverageCandidatureCountExcludingZero();

        Integer getTotalRecruitmentCount();

        Integer getTotalCandidatureCount();

        Integer getTotalCandidatureWithNotificationCount();

    }

    @Query(value = """
                        SELECT DISTINCT rc
                        FROM RecruitmentCandidature rc
                        JOIN FETCH rc.recruitment r
                        JOIN FETCH rc.userProfile up
                        JOIN FETCH r.recruitmentProfile rp
                        JOIN FETCH rp.job j
                        JOIN FETCH j.recruiter r2
                        WHERE r.state IN (com.erhgo.domain.enums.RecruitmentState.UNPUBLISHED, com.erhgo.domain.enums.RecruitmentState.SELECTION)
                        AND rc.globalCandidatureState NOT IN (
                            com.erhgo.domain.candidature.job.GlobalCandidatureState.REFUSED_MEETING_CLIENT,
                            com.erhgo.domain.candidature.job.GlobalCandidatureState.REFUSED_BY_CLIENT_WITH_SHEETS,
                            com.erhgo.domain.candidature.job.GlobalCandidatureState.REFUSED_ON_CALL,
                            com.erhgo.domain.candidature.job.GlobalCandidatureState.NOT_FINALIZED,
                            com.erhgo.domain.candidature.job.GlobalCandidatureState.MISSING_PREREQUISITE
                        )
                        AND rc.notifiedOnClosedRecruitment is false
            """)
    List<RecruitmentCandidature> getRecruitmentCandidaturesFromSuspendedRecruitmentToNotify();

    @Query("""
            SELECT count(DISTINCT c) FROM RecruitmentCandidature c
            INNER JOIN c.userProfile up
            WHERE up.handicapModeEnabled = true
            AND c.submissionDate IS NOT NULL
            """)
    Integer countCandidaturesByHandicapEnabledUsers();
}
