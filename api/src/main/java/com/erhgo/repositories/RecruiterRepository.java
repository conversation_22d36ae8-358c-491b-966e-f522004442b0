package com.erhgo.repositories;

import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Recruiter;

import java.util.Collection;
import java.util.List;

public interface RecruiterRepository extends AbstractRepository<Recruiter> {

    List<Recruiter> findByCodeIn(Collection<String> codes);

    List<Recruiter> findBySiretAndOrganizationType(String siret, AbstractOrganization.OrganizationType organizationType);

}
