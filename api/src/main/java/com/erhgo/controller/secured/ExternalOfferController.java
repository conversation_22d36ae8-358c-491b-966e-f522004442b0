package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.externaloffer.ExternalOffer;
import com.erhgo.openapi.controller.ExternalOfferApi;
import com.erhgo.openapi.dto.AtsOfferSimulatedDTO;
import com.erhgo.openapi.dto.ExternalOffersPageDTO;
import com.erhgo.openapi.dto.OfferDataDTO;
import com.erhgo.openapi.dto.ScrapeOffersCommandDTO;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.services.externaloffer.AbstractATSSynchronizer;
import com.erhgo.services.externaloffer.ExternalOfferService;
import com.erhgo.services.externaloffer.GenericATSSynchronizer;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import com.erhgo.services.externaloffer.firecrawl.ExternalOfferScraper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
@Slf4j
public class ExternalOfferController implements ExternalOfferApi {
    private final ExternalOfferServiceProvider externalOfferServiceProvider;
    private final ExternalOfferRepository externalOfferRepository;
    private final ExternalOfferService externalOfferService;
    private final ExternalOfferScraper externalOfferScraper;

    @Override
    public ResponseEntity<Void> simulateAtsOffer(AtsOfferSimulatedDTO offers) {
        var trimmedFlow = offers.getRawFlow().trim();

        if (trimmedFlow.startsWith("{"))
            drBean().simulateAtsOffer(offers);
        else if (trimmedFlow.contains("<link>https://www.adecco.fr/</link>"))
            adeccoBean().simulateAtsOffer(offers);
        else if (trimmedFlow.contains("<apply_url>https://careers.keolis.com/"))
            successFactorsBean().simulateAtsOffer(offers);
        else eoliaBean().simulateAtsOffer(offers);
        return ResponseEntity.noContent().build();
    }

    private @NotNull GenericATSSynchronizer<?> drBean() {
        return (GenericATSSynchronizer) externalOfferServiceProvider.getService(new AtsGetOfferConfig().setAtsCode("DIGITAL_RECRUITERS").setConfigCode("APRIL"));
    }

    private @NotNull GenericATSSynchronizer<?> adeccoBean() {
        return (GenericATSSynchronizer) externalOfferServiceProvider.getService(new AtsGetOfferConfig().setAtsCode("adecco").setConfigCode("rhône"));
    }

    private @NotNull GenericATSSynchronizer<?> successFactorsBean() {
        return (GenericATSSynchronizer) externalOfferServiceProvider.getService(new AtsGetOfferConfig().setAtsCode("SUCCESS_FACTORS").setConfigCode("KEOLIS"));
    }

    private @NotNull GenericATSSynchronizer<?> eoliaBean() {
        return (GenericATSSynchronizer) externalOfferServiceProvider.getService(new AtsGetOfferConfig().setAtsCode("eolia").setConfigCode("MB"));
    }

    private List<AbstractATSSynchronizer<?>> allServices() {
        return List.of(drBean(), adeccoBean(), successFactorsBean(), eoliaBean());
    }


    @Override
    public ResponseEntity<OfferDataDTO> initializeAndExtractDataFromExternalOffer(UUID externalOfferId) {
        var offer = externalOfferRepository.findById(externalOfferId).orElseThrow(() -> new EntityNotFoundException(externalOfferId, ExternalOffer.class));
        var dto = allServices().stream().filter(s -> s.getAtsCode().equals(offer.getAtsCode())).findFirst().orElseThrow().extractDataFromExternalOffer(externalOfferId);
        return ResponseEntity.ok(dto);
    }

    @Override
    public ResponseEntity<Void> ignoreExternalOffer(UUID externalOfferId) {
        externalOfferService.ignoreExternalOffer(externalOfferId);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> integrateExternalOffer(UUID externalOfferId) {
        externalOfferService.integrateExternalOffer(externalOfferId);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<ExternalOffersPageDTO> getExternalOffers(Integer size, Integer page, String organizationCode) {
        return ResponseEntity.ok(externalOfferService.findExternalOffers(size, page, organizationCode));
    }

    @Override
    public ResponseEntity<Void> scrapeOffers(ScrapeOffersCommandDTO command) {
        log.debug("Starting offer scraping with command: {}", command);
        externalOfferScraper.scrapeOffers(command);
        log.info("Offer scraping launched with command: {}", command);
        return ResponseEntity.noContent().build();
    }
}
