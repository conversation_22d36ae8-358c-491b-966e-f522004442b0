package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.openapi.controller.ScrapedUsersApi;
import com.erhgo.openapi.dto.ScrapedUserDTO;
import com.erhgo.services.scrapeduser.ScrapedUserService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Slf4j
@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
public class ScrapedUserController implements ScrapedUsersApi {

    private final ScrapedUserService scrapedUserService;
    private final HttpServletResponse response;
    private final SimpleDateFormat filenameDateFormatter = new SimpleDateFormat("MM-dd-yyyy_HH-mm-ss");

    @Override
    public ResponseEntity<List<ScrapedUserDTO>> getScrapedUsers() {
        return ResponseEntity.ok(scrapedUserService.getAllScrapedUsers());
    }

    @Override
    public ResponseEntity<ScrapedUserDTO> getScrapedUserDetail(UUID uuid) {
        return ResponseEntity.ok(scrapedUserService.getScrapedUserDetail(uuid));
    }

    @Override
    public ResponseEntity<String> exportScrapedUsers() {
        try {
            response.setContentType("text/csv");
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            var fileName = "candidats-scrapes_" + filenameDateFormatter.format(new Date()) + ".csv";
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName);
            scrapedUserService.writeScrapedUsersCsv(new OutputStreamWriter(response.getOutputStream(), StandardCharsets.UTF_8));
        } catch (IOException e) {
            log.error("failed to export scraped users csv", e);
            throw new GenericTechnicalException("Unable to generate CSV");
        }
        return ResponseEntity.ok().build();
    }
}
