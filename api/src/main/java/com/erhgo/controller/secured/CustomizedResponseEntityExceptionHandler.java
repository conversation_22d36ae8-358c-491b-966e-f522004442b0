package com.erhgo.controller.secured;

import com.erhgo.domain.dto.ValidationError;
import com.erhgo.domain.dto.ValidationErrors;
import com.erhgo.domain.exceptions.*;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.util.Map;

@ControllerAdvice
@RestController
@Slf4j
public class CustomizedResponseEntityExceptionHandler extends ResponseEntityExceptionHandler {

    public static final String REQUEST_CAUSE = "cause";

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex,
                                                                  HttpHeaders headers,
                                                                  HttpStatusCode status,
                                                                  WebRequest request) {
        return new ResponseEntity<>(new ValidationErrors(ex.getBindingResult().getAllErrors().stream().map(
                error -> new ValidationError(((FieldError) error).getField(), error.getDefaultMessage(), ((FieldError) error).getRejectedValue())
        ).toList()), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler({ConstraintViolationException.class})
    public ResponseEntity<Object> handleConstraintViolationException(ConstraintViolationException ex, WebRequest request) {
        return ResponseEntity
                .status(HttpStatus.BAD_REQUEST)
                .body(Map.of(REQUEST_CAUSE, ex.getMessage()));
    }

    @ExceptionHandler({AbstractFunctionalException.class})
    public ResponseEntity<Object> handleFunctionalException(AbstractFunctionalException ex, WebRequest request) {
        return ResponseEntity
                .status(HttpStatus.BAD_REQUEST)
                .body(ex.describe().isEmpty() ? Map.of(REQUEST_CAUSE, ex.getMessage()) : ex.describe());
    }

    @ExceptionHandler({EntityNotFoundException.class})
    public ResponseEntity<Object> handleEntityNotFoundException(EntityNotFoundException ex, WebRequest request) {
        return ResponseEntity
                .status(HttpStatus.NOT_FOUND)
                .body(Map.of(REQUEST_CAUSE, ex.getMessage()));
    }

    @ExceptionHandler({EntityAlreadyExistException.class})
    public ResponseEntity<Object> handleConflict(EntityAlreadyExistException ex, WebRequest request) {
        return ResponseEntity
                .status(HttpStatus.CONFLICT)
                .body(ex.getDetails());
    }

    @ExceptionHandler({UserNotAllowedForEntity.class})
    public ResponseEntity<Object> handleNotAllowed(UserNotAllowedForEntity ex, WebRequest rdequest) {
        return ResponseEntity
                .status(HttpStatus.FORBIDDEN)
                .body(ex.getMessage());
    }

    @ExceptionHandler({NotPublishedRecruitmentException.class})
    public ResponseEntity<Object> handleNotAllowed(NotPublishedRecruitmentException ex, WebRequest request) {
        return ResponseEntity
                .status(HttpStatus.FORBIDDEN)
                .body(ex.getMessage());
    }

    @ExceptionHandler({IllegalArgumentException.class})
    public ResponseEntity<Object> handleIllegalArgument(IllegalArgumentException ex, WebRequest request) {
        return ResponseEntity
                .status(HttpStatus.BAD_REQUEST)
                .body(ex.getMessage());
    }

    @ExceptionHandler({UnknownInvitationCodeException.class})
    public ResponseEntity<Object> handleUnknownCode(UnknownInvitationCodeException ex, WebRequest request) {
        return ResponseEntity
                .status(HttpStatus.FORBIDDEN)
                .body(ex.getMessage());
    }

    @ExceptionHandler({SiretAlreadyUsedException.class})
    public ResponseEntity<Object> handleExistingSiret(SiretAlreadyUsedException ex, WebRequest request) {
        return ResponseEntity
                .status(HttpStatus.CONFLICT)
                .body(ex.getMessage());
    }

    @ExceptionHandler({TrialQuotaExceeded.class, SourcingInvitationQuotaExceeded.class})
    public ResponseEntity<Object> handlePaymentRequired(AbstractFunctionalException ex, WebRequest request) {
        return ResponseEntity
                .status(HttpStatus.PAYMENT_REQUIRED)
                .body(ex.getMessage());
    }

    @ExceptionHandler({UnsupportedMediaTypeException.class})
    public ResponseEntity<Object> handleUnsupportedMediaViolation(UnsupportedMediaTypeException ex, WebRequest request) {
        return ResponseEntity
                .status(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
                .body(ex.getMessage());
    }

    @ExceptionHandler({FileImportRateLimitReachedException.class})
    public ResponseEntity<Object> handleFileImportRateLimitReached(AbstractFunctionalException ex, WebRequest request) {
        return ResponseEntity
                .status(HttpStatus.TOO_MANY_REQUESTS)
                .body(ex.getMessage());
    }

    @ExceptionHandler({InvalidJobTitleException.class})
    public ResponseEntity<Object> handleInvalidJobTitle(AbstractTechnicalException ex, WebRequest request) {
        return ResponseEntity
                .status(HttpStatus.BAD_REQUEST)
                .body(ex.getMessage());
    }

    @Override
    protected ResponseEntity<Object> handleExceptionInternal(
            Exception ex,
            @Nullable Object body,
            HttpHeaders headers,
            HttpStatusCode status,
            WebRequest request) {

        if (HttpStatus.INTERNAL_SERVER_ERROR.equals(status)) {
            log.error("Got error for request {}", request.getContextPath(), ex);
        } else {
            log.warn("Got error for request {}", request.getContextPath(), ex);
        }
        return new ResponseEntity<>(body, headers, status);
    }
}
