package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.openapi.controller.CriteriaApi;
import com.erhgo.openapi.dto.CriteriaDTO;
import com.erhgo.openapi.dto.EditCriteriaCommandDTO;
import com.erhgo.services.criteria.CriteriaService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
public class CriteriaController implements CriteriaApi {

    private final CriteriaService criteriaService;

    @Override
    public ResponseEntity<List<CriteriaDTO>> getCriteria() {
        return ResponseEntity.ok(criteriaService.getCriteria());
    }

    @Override
    public ResponseEntity<Void> editCriteria(EditCriteriaCommandDTO command, String code) {
        criteriaService.updateCriterion(code, command);
        return ResponseEntity.noContent().build();
    }
}
