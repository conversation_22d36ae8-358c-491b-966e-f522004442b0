package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.domain.exceptions.InvalidCommandException;
import com.erhgo.openapi.controller.EscoApi;
import com.erhgo.openapi.dto.*;
import com.erhgo.services.EscoOccupationService;
import com.erhgo.services.EscoSkillService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
public class EscoOccupationDataController implements EscoApi {

    @Autowired
    private EscoOccupationService escoOccupationService;

    @Autowired
    private EscoSkillService escoSkillService;

    @Override
    public ResponseEntity<EscoOccupationPageDTO> escoOccupationPage(Integer size, Integer page, String query) {
        return ResponseEntity.ok(escoOccupationService.getOccupationPage(size, page, query));
    }

    @Override
    public ResponseEntity<List<EscoOccupationSummaryDTO>> searchEscoOccupation(String query) {
        if (query != null && !query.isEmpty()) {
            return ResponseEntity.ok(escoOccupationService.findByQuery(query));
        } else {
            return ResponseEntity.badRequest().body(null);
        }
    }

    @Override
    public ResponseEntity<EscoOccupationDetailDTO> getEscoOccupation(String uri) {
        return ResponseEntity.ok(escoOccupationService.getByUri(uri));
    }

    @Override
    public ResponseEntity<SkillDTO> getSkill(String uri) {
        return ResponseEntity.ok(escoSkillService.getByUri(uri));
    }

    @Override
    public ResponseEntity<Void> updateSkillDescription(UpdateSkillDescriptionCommandDTO updateSkillDescriptionCommandDTO) {
        escoSkillService.updateDescriptionFRForSkill(updateSkillDescriptionCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> linkToActivity(SkillLinkToActivityCommandDTO skillLinkToActivityCommandDTO) {
        escoSkillService.linkWithActivity(skillLinkToActivityCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> deleteLinkToActivity(SkillLinkToActivityCommandDTO skillLinkToActivityCommandDTO) {
        try {
            escoSkillService.deleteLinkToActivity(skillLinkToActivityCommandDTO);
            return ResponseEntity.noContent().build();
        } catch (InvalidCommandException e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @Override
    public ResponseEntity<Void> linkToContext(SkillLinkToContextCommandDTO skillLinkToContextCommandDTO) {
        escoSkillService.linkWithContext(skillLinkToContextCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> deleteLinkToContext(SkillLinkToContextCommandDTO skillLinkToContextCommandDTO) {
        escoSkillService.deleteLinkToContext(skillLinkToContextCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> linkToBehavior(SkillLinkToBehaviorCommandDTO skillLinkToBehaviorCommandDTO) {
        escoSkillService.linkWithBehavior(skillLinkToBehaviorCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> deleteLinkToBehavior(SkillLinkToBehaviorCommandDTO skillLinkToBehaviorCommandDTO) {
        escoSkillService.deleteLinkToBehavior(skillLinkToBehaviorCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> setSkillNoActivity(SkillSetBooleanValueCommandDTO skillSetBooleanValueCommandDTO) {
        escoSkillService.setNoActivity(skillSetBooleanValueCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> setSkillNoBehavior(SkillSetBooleanValueCommandDTO skillSetBooleanValueCommandDTO) {
        escoSkillService.setNoBehavior(skillSetBooleanValueCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> setSkillNoContext(SkillSetBooleanValueCommandDTO skillSetBooleanValueCommandDTO) {
        escoSkillService.setNoContext(skillSetBooleanValueCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<CapacitiesResultDTO> getEscoOccupationCapacities(String uri) {
        return ResponseEntity.ok(escoOccupationService.findCapacities(uri));
    }
}
