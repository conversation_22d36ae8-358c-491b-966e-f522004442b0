package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.openapi.controller.LandingPageApi;
import com.erhgo.openapi.dto.LandingPageDetailDTO;
import com.erhgo.openapi.dto.LandingPagePageDTO;
import com.erhgo.openapi.dto.SaveLandingPageCommandDTO;
import com.erhgo.services.LandingPageService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
public class LandingPageController implements LandingPageApi {
    private final LandingPageService landingPageService;

    @Override
    public ResponseEntity<Void> saveLandingPage(SaveLandingPageCommandDTO saveLandingPageCommandDTO) {
        landingPageService.saveLandingPage(saveLandingPageCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<LandingPageDetailDTO> getLandingPageById(UUID uuid) {
        return ResponseEntity.ok(landingPageService.getLandingPage(uuid));
    }

    @Override
    public ResponseEntity<LandingPagePageDTO> getLandingPages(Integer page, Integer size) {
        return ResponseEntity.ok(landingPageService.getLandingPages(page, size));
    }
}
