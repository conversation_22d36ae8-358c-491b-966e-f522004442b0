package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.openapi.controller.QuestionContextApi;
import com.erhgo.openapi.dto.*;
import com.erhgo.services.QuestionForContextsService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
public class QuestionForContextsController implements QuestionContextApi {

    @Autowired
    private QuestionForContextsService questionForContextsService;

    @Override
    public ResponseEntity<Void> saveContextQuestion(SaveQuestionForContextsCommandDTO saveQuestionForContextsCommandDTO) {
        questionForContextsService.saveContextQuestion(saveQuestionForContextsCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<QuestionForContextsDetailsDTO> getQuestionForContexts(UUID uuid) {
        return ResponseEntity.ok(questionForContextsService.getQuestionForContextsById(uuid));
    }

    @Override
    public ResponseEntity<List<QuestionForContextsSummaryDTO>> listContextsQuestionsByContextId(UUID contextId) {
        return ResponseEntity.ok(questionForContextsService.getListContextsQuestionsByContextId(contextId));
    }

    @Override
    public ResponseEntity<QuestionForContextsPageDTO> listQuestionForContexts(Integer size,
                                                                              Integer page,
                                                                              String by,
                                                                              SortDirectionDTO direction,
                                                                              String filter,
                                                                              UUID contextId,
                                                                              Long categoryId) {
        return ResponseEntity.ok(questionForContextsService.getQuestionForContextsPage(page, size, by, direction, contextId, categoryId, filter));
    }

    @Override
    public ResponseEntity<List<RecruitmentProfileSummaryDTO>> getRecruitmentProfilesByContextQuestionAndContextId(UUID questionId, UUID contextId) {
        return ResponseEntity.ok(questionForContextsService.getRecruitmentProfilesByContextQuestionAndContextId(questionId, contextId));
    }
}
