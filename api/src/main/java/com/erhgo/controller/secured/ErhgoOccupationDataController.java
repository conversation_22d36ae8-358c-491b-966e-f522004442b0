package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.openapi.controller.ErhgoOccupationApi;
import com.erhgo.openapi.dto.*;
import com.erhgo.services.ErhgoOccupationService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
@Slf4j
public class ErhgoOccupationDataController implements ErhgoOccupationApi {

    @Autowired
    private ErhgoOccupationService erhgoOccupationService;


    @Autowired
    private HttpServletResponse response;

    @Override
    public ResponseEntity<ErhgoOccupationPageDTO> erhgoOccupationPage(Integer size, Integer page, String query, UUID activityLabelId, List<ErhgoSearchOrderDTO> by, List<SortDirectionDTO> direction) {
        return ResponseEntity.ok(erhgoOccupationService.getErhgoOccupationPage(size, page, by, direction, query, activityLabelId));
    }

    @Override
    public ResponseEntity<ErhgoOccupationOTPageDTO> erhgoOccupationOTPage(Integer size, Integer page, String query, SortDirectionDTO direction) {
        return ResponseEntity.ok(erhgoOccupationService.getErhgoOccupationOTPage(size, page, direction, query));
    }

    @Override
    public ResponseEntity<ErhgoOccupationDetailDTO> getErhgoOccupation(UUID id) {
        return ResponseEntity.ok(erhgoOccupationService.getByUuid(id));
    }

    @Override
    public ResponseEntity<ErhgoOccupationSumUpDTO> getErhgoOccupationSumUp(UUID id) {
        return ResponseEntity.ok(erhgoOccupationService.getSumUp(id));
    }

    @Override
    public ResponseEntity<ErhgoOccupationDetailDTO> linkEscoOccupationToErhgoOccupation(EditEscoOccupationCommandDTO editEscoOccupationCommandDTO) {
        return ResponseEntity.ok(erhgoOccupationService.linkEscoOccupationToErhgoOccupation(editEscoOccupationCommandDTO));
    }

    @Override
    public ResponseEntity<ErhgoOccupationDetailDTO> unlinkEscoOccupationFromErhgoOccupation(EditEscoOccupationCommandDTO editEscoOccupationCommandDTO) {
        return ResponseEntity.ok(erhgoOccupationService.unlinkEscoOccupationFromErhgoOccupation(editEscoOccupationCommandDTO));
    }

    @Override
    public ResponseEntity<Void> linkRomeToErhgoOccupation(LinkToErhgoOccupationCommandDTO linkToErhgoOccupationCommandDTO) {
        erhgoOccupationService.linkRomeToErhgoOccupation(linkToErhgoOccupationCommandDTO.getId(), linkToErhgoOccupationCommandDTO.getRomeCode());
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> unlinkRomeFromErhgoOccupation(UnlinkRomeFromErhgoOccupationCommandDTO unlinkRomeFromErhgoOccupationCommandDTO) {
        erhgoOccupationService.unlinkRomeFromErhgoOccupation(unlinkRomeFromErhgoOccupationCommandDTO.getId(), unlinkRomeFromErhgoOccupationCommandDTO.getRomeCode());
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> updateDescription(UpdateDescriptionCommandDTO updateDescriptionCommandDTO) {
        erhgoOccupationService.updateDescription(updateDescriptionCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> updateBehaviorsDescription(UpdateBehaviorsDescriptionCommandDTO updateBehaviorsDescriptionCommandDTO) {
        erhgoOccupationService.updateBehaviorsDescription(updateBehaviorsDescriptionCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> updateMasteryLevel(UpdateMasteryLevelCommandDTO updateMasteryLevelCommandDTO) {
        erhgoOccupationService.updateMasteryLevel(updateMasteryLevelCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<String> qualifyOccupationState(UUID id) {
        return ResponseEntity.ok(erhgoOccupationService.qualifyOccupation(id).name());
    }

    @Override
    public ResponseEntity<String> unqualifyOccupationState(UUID id) {
        return ResponseEntity.ok(erhgoOccupationService.unqualifyOccupation(id).name());
    }

    @Override
    public ResponseEntity<Void> addActivitiesToOccupation(OccupationReferentialEntitiesEditCommandDTO command) {
        erhgoOccupationService.addActivitiesToOccupation(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> removeActivitiesFromOccupation(OccupationReferentialEntitiesEditCommandDTO command) {
        erhgoOccupationService.removeActivitiesFromOccupation(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> setOccupationActivityMandatoryState(OccupationReferentialEntityEditWithStateCommandDTO command) {
        erhgoOccupationService.setOccupationActivityMandatoryState(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> addContextToOccupation(OccupationReferentialEntityEditCommandDTO command) {
        erhgoOccupationService.addContextToOccupation(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> removeContextFromOccupation(OccupationReferentialEntityEditCommandDTO command) {
        erhgoOccupationService.removeContextFromOccupation(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> setOccupationContextMandatoryState(OccupationReferentialEntityEditWithStateCommandDTO command) {
        erhgoOccupationService.setOccupationContextMandatoryState(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<ErhgoOccupationBehaviorsCategoriesDTO> addBehaviorToOccupation(OccupationReferentialEntityEditCommandDTO command) {
        return ResponseEntity.ok(erhgoOccupationService.addBehaviorToOccupation(command));
    }

    @Override
    public ResponseEntity<ErhgoOccupationBehaviorsCategoriesDTO> removeBehaviorFromOccupation(OccupationReferentialEntityEditCommandDTO command) {
        return ResponseEntity.ok(erhgoOccupationService.removeBehaviorFromOccupation(command));
    }

    @Override
    public ResponseEntity<Void> editAlternativeLabels(EditAlternativeLabelsCommandDTO editAlternativeLabelsCommandDTO) {
        erhgoOccupationService.editAlternativeLabels(editAlternativeLabelsCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<List<ErhgoOccupationsForLabelDTO>> getErhgoOccupationsWithLabels(List<String> labels) {
        return ResponseEntity.ok(erhgoOccupationService.erhgoOccupationsForLabels(labels));
    }

    @Override
    public ResponseEntity<Void> unlinkSkillFromErhgoOccupation(UnlinkSkillFromErhgoOccupationCommandDTO unlinkSkillFromErhgoOccupationCommandDTO) {
        erhgoOccupationService.unlinkSkillFromErhgoOccupation(unlinkSkillFromErhgoOccupationCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<List<ErhgoOccupationSearchDTO>> searchOccupations(String query, Boolean highlights) {
        return ResponseEntity.ok(erhgoOccupationService.searchOccupations(query, BooleanUtils.isTrue(highlights)));
    }

    @Override
    public ResponseEntity<ErhgoOccupationBehaviorsCategoriesDTO> setOccupationBehaviorCategory(EditOccupationBehaviorCategoryCommandDTO command) {
        return ResponseEntity.ok(erhgoOccupationService.setOccupationBehaviorFamily(command));

    }

    @Override
    public ResponseEntity<CapacitiesResultDTO> getErhgoOccupationCapacities(UUID id) {
        return ResponseEntity.ok(erhgoOccupationService.findCapacities(id));
    }

    @Override
    public ResponseEntity<Void> createErhgoOccupation(CreateErhgoOccupationCommandDTO createErhgoOccupationCommandDTO) {
        erhgoOccupationService.createOccupation(createErhgoOccupationCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> mergeOccupations(MergeOccupationsCommandDTO mergeOccupationsCommandDTO) {
        erhgoOccupationService.mergeOccupations(mergeOccupationsCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> reindexErhgoOccupations() {
        erhgoOccupationService.indexAll();
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Resource> getErhgoOccupationDetailsPdf(UUID id) {
        try (var outputStream = new ByteArrayOutputStream()) {
            response.setContentType(MediaType.APPLICATION_PDF_VALUE);
            response.setHeader("Content-Disposition", "attachment; filename=occupation.pdf");
            erhgoOccupationService.writeErhgoOccupationPDF(id, outputStream);
            response.getOutputStream().write(outputStream.toByteArray());
        } catch (IOException e) {
            log.error("failed to export pdf", e);
        }
        return ResponseEntity.ok().build();
    }

    @Override
    public ResponseEntity<Void> updateWorkEnvironments(UpdateWorkEnvironmentsCommandDTO updateWorkEnvironmentsCommandDTO) {
        erhgoOccupationService.updateWorkEnvironments(updateWorkEnvironmentsCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> updateSpecifications(UpdateSpecificationsCommandDTO updateSpecificationsCommandDTO) {
        erhgoOccupationService.updateSpecifications(updateSpecificationsCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> updateErhgoOccupationCriteriaValues(UpdateCriteriaValuesCommandDTO command) {
        erhgoOccupationService.updateCriteriaValues(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> updateErhgoOccupationErhgoClassifications(UpdateErhgoClassificationsCommandDTO command) {
        erhgoOccupationService.updateErhgoOccupationErhgoClassifications(command);
        return ResponseEntity.noContent().build();
    }
}
