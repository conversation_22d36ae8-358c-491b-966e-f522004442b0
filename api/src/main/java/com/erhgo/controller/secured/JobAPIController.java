package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.openapi.controller.JobApi;
import com.erhgo.openapi.dto.*;
import com.erhgo.services.JobService;
import com.erhgo.services.RecruitmentProfileService;
import com.erhgo.services.dto.criteria.MatchingJobsCriteria;
import com.erhgo.services.dtobuilder.RecruitmentProfileDTOBuilder;
import com.erhgo.services.userprofile.UserProfileService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static java.util.stream.Collectors.toList;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
@Slf4j
public class JobAPIController implements JobApi {
    public static final int MAXIMUM_USERS_EXPORT_SIZE = 1_000_000;

    @Autowired
    private UserProfileService userProfileService;

    @Autowired
    private JobService jobService;

    @Autowired
    private RecruitmentProfileService recruitmentProfileService;

    @Autowired
    private final HttpServletResponse response;

    @Override
    public ResponseEntity<JobPageDTO> getJobPage(List<String> organizationCodes, Integer page, Integer size, SortDirectionDTO direction, String by, String filter, Boolean strictOrganizationFilter) {
        return ResponseEntity.ok(jobService.findPaginatedAndFilteredByOrganizationAndProperty(page, size, by, direction.toString(), organizationCodes, filter, strictOrganizationFilter));
    }

    @Override
    public ResponseEntity<List<JobSummaryDTO>> getPublishedJobs(String organizationCode) {
        return ResponseEntity.ok(jobService.findAllPublishedJob(organizationCode));
    }

    @Override
    public ResponseEntity<Void> saveBehavior(List<UUID> behaviorDTOs, UUID jobId) {
        jobService.saveBehavior(jobId, behaviorDTOs);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> publish(PublishCommandDTO publishCommandDTO, UUID jobId, Boolean isSimpleJobCreated) {
        jobService.publish(jobId, isSimpleJobCreated, publishCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> saveJob(SaveJobCommandDTO saveJobCommandDTO) {
        jobService.saveJob(saveJobCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> setRecommendationForJob(String recommendation, UUID jobId) {
        jobService.setRecommendation(jobId, recommendation);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<List<RecruitmentProfileSummaryDTO>> listRecruitmentProfiles(UUID jobId, Boolean qualifiedOnly) {
        return ResponseEntity.ok(recruitmentProfileService.findByJobId(jobId, qualifiedOnly)
                .parallelStream()
                .map(RecruitmentProfileDTOBuilder::buildSummary)
                .collect(toList()));
    }

    @Override
    public ResponseEntity<JobDetailDTO> getJob(UUID jobId) {
        return ResponseEntity.ok(jobService.getJobDetail(jobId));
    }


    @Override
    public ResponseEntity<Void> deleteJob(UUID jobId) {
        jobService.deleteJob(jobId);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> saveRecruitmentProfile(SaveRecruitmentProfileCommandDTO saveRecruitmentProfileDTO, UUID jobId) {
        recruitmentProfileService.createOrUpdate(jobId, saveRecruitmentProfileDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<RecruitmentProfileDetailDTO> getRecruitmentProfile(UUID jobId, UUID profileId) {
        return ResponseEntity.ok(recruitmentProfileService.getProfileDetail(jobId, profileId));
    }

    @Override
    public ResponseEntity<Void> addOptionals(List<AddOptionalsCommandItemDTO> optionalsCommandItems, UUID jobId, UUID profileId) {
        recruitmentProfileService.updateOptionals(jobId, profileId, optionalsCommandItems);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> deleteOptionals(List<DeleteOptionalsCommandItemDTO> optionalsCommandItems, UUID jobId, UUID profileId) {
        recruitmentProfileService.deleteOptionals(jobId, profileId, optionalsCommandItems);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> endQualification(UUID jobId, UUID profileId, Long missionId) {
        recruitmentProfileService.endQualification(jobId, profileId, missionId);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> reorderMissions(List<Long> missionsIds, UUID jobId) {
        jobService.reorderMissions(jobId, missionsIds);

        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<JobDetailDTO> createJobForTemplate(CreateJobForTemplateCommandDTO command) {
        return ResponseEntity.ok(jobService.createFromErhgoId(command.getTemplateId(), command.getRecruiterCode(), command.getJobType()));
    }

    @Override
    public ResponseEntity<Void> setContextQuestionForProfile(SetQuestionForContextCommandDTO command, UUID jobId, UUID profileId) {
        recruitmentProfileService.setCustomContextLabelForProfile(jobId, profileId, command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<CapacitiesResultDTO> getJobCapacities(UUID jobId) {
        return ResponseEntity.ok(new CapacitiesResultDTO().capacities(jobService.getJobCapacities(jobId)));
    }

    @Override
    public ResponseEntity<CapacitiesResultDTO> getProfileCapacities(UUID profileId, UUID jobId) {
        return ResponseEntity.ok(new CapacitiesResultDTO().capacities(recruitmentProfileService.getProfileCapacities(profileId)));
    }

    @Override
    public ResponseEntity<UserMatchingJobPageDTO> getUsersMatchingJob(UUID jobId, Integer pageNumber, Integer size, Float capacityThreshold, List<String> organizationCodes, Float masteryLevelRange, Boolean strictOrganizationFilter, Boolean isAffectedToNoChannel, String postcode, List<String> criteriaCodes) {
        var page = MatchingJobsCriteria.builder()
                .capacityThreshold(capacityThreshold)
                .organizationCodes(organizationCodes)
                .masteryLevelRange(masteryLevelRange)
                .pageSize(size)
                .pageNumber(pageNumber)
                .postcode(postcode)
                .strictOrganizationFilter(strictOrganizationFilter)
                .isAffectedToNoChannel(isAffectedToNoChannel)
                .criteriaCodes(criteriaCodes != null ? criteriaCodes : Collections.emptyList())
                .build();
        return ResponseEntity.ok(userProfileService.getUsersMatchingJob(jobId, page));
    }

    @Override
    public ResponseEntity<String> getUsersMatchingJobExport(UUID jobId, Float capacityThreshold, List<String> organizationCodes, Float masteryLevelRange, Boolean strictOrganizationFilter, Boolean isAffectedToNoChannel, String postcode, List<String> criteriaCodes) {
        var page = MatchingJobsCriteria.builder()
                .capacityThreshold(capacityThreshold)
                .organizationCodes(organizationCodes)
                .masteryLevelRange(masteryLevelRange)
                .pageSize(MAXIMUM_USERS_EXPORT_SIZE)
                .pageNumber(0)
                .postcode(postcode)
                .strictOrganizationFilter(strictOrganizationFilter)
                .isAffectedToNoChannel(isAffectedToNoChannel)
                .criteriaCodes(criteriaCodes != null ? criteriaCodes : Collections.emptyList())
                .build();

        try {
            response.setContentType("text/csv");
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            var fileName = "utilisateurs-correspondants-" + jobId.toString() + ".csv";
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            userProfileService.writeUsersMatchingJobCsv(
                    new OutputStreamWriter(response.getOutputStream(), StandardCharsets.UTF_8),
                    jobId,
                    page
            );
            return ResponseEntity.ok().build();
        } catch (IOException e) {
            log.error("failed to export csv", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @Override
    public ResponseEntity<List<AlreadyAppliedUserDTO>> getJobCandidates(UUID jobId) {
        return ResponseEntity.ok(jobService.getJobCandidates(jobId));
    }

    @Override
    public ResponseEntity<Void> updateCriteriaForJob(List<String> criteriaValueCodes, UUID jobId) {
        jobService.updateCriteriaForJob(jobId, criteriaValueCodes, null);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> addWorkingTimeTypeForJob(List<WorkingTimeDTO> workingTimeDTOs, UUID jobId) {
        jobService.addCriteriaValuesForJob(jobId, workingTimeDTOs);
        return ResponseEntity.noContent().build();
    }
}
