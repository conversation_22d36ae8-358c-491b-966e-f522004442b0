package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.services.AbstractService;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping(ApiConstants.API_ODAS_USERS)
@RequiredArgsConstructor
public class UserKeycloakController {

    @Autowired
    private KeycloakService keycloakService;

    @GetMapping(value = "/list/{group}", produces = {"application/hal+json"})
    public ResponseEntity<AbstractService.PageDTOAdapter<UserRepresentation>> findPaginatedAndFilteredByProperty(
            @RequestParam(value = "page") int page,
            @RequestParam(value = "size") int size,
            @PathVariable("group") String group) {
        return ResponseEntity.ok(keycloakService.getBackOfficeGroupMembersPaginatedResource(group, page, size));
    }
    
    @GetMapping(value = "/count/{group}", produces = {"application/hal+json"})
    public Integer count(@PathVariable("group") String group) {
        return keycloakService.countBackOfficeGroupMembers(group);
    }
}

