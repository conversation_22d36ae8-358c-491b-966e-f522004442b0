package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.openapi.controller.ErhgoClassificationApi;
import com.erhgo.openapi.dto.ErhgoClassificationDTO;
import com.erhgo.services.ErhgoClassificationService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
public class ErhgoClassificationController implements ErhgoClassificationApi {
    private final ErhgoClassificationService erhgoClassificationService;

    @Override
    public ResponseEntity<List<ErhgoClassificationDTO>> listErhgoClassifications(UUID occupationId) {
        return ResponseEntity.ok(erhgoClassificationService.listErhgoClassifications(occupationId));
    }
}
