package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.openapi.controller.UserExperienceApi;
import com.erhgo.openapi.dto.ExperienceDetailsDTO;
import com.erhgo.openapi.dto.SaveExperienceCommandDTO;
import com.erhgo.services.UserExperienceService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
public class UserExperienceController implements UserExperienceApi {

    private final UserExperienceService service;

    @Override
    public ResponseEntity<ExperienceDetailsDTO> saveExperience(SaveExperienceCommandDTO command) {
        return ResponseEntity.ok(service.saveExperience(command, true));
    }

    @Override
    public ResponseEntity<ExperienceDetailsDTO> getExperience(UUID experienceId) {
        return ResponseEntity.ok(service.getExperience(experienceId));
    }

    @Override
    public ResponseEntity<Void> deleteExperience(UUID experienceId) {
        service.deleteExperience(experienceId);
        return ResponseEntity.noContent().build();
    }

}
