package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.openapi.controller.CapacityRelatedQuestionApi;
import com.erhgo.openapi.dto.*;
import com.erhgo.services.AnswerForCapacityRelatedQuestionService;
import com.erhgo.services.CapacityRelatedQuestionService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
public class CapacityRelatedQuestionController implements CapacityRelatedQuestionApi {

    @Autowired
    private CapacityRelatedQuestionService capacityRelatedQuestionService;

    @Autowired
    private AnswerForCapacityRelatedQuestionService answerForCapacityRelatedQuestionService;

    @Override
    public ResponseEntity<Void> saveCapacityRelatedQuestion(SaveCapacityRelatedQuestionCommandDTO command) {
        capacityRelatedQuestionService.save(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<CapacityRelatedQuestionDetailsDTO> getCapacityRelatedQuestionDetails(UUID questionId) {
        return ResponseEntity.ok(capacityRelatedQuestionService.getQuestion(questionId));
    }

    @Override
    public ResponseEntity<CapacityRelatedQuestionPageDTO> getCapacityRelatedQuestionPage(QuestionTypeDTO questionTypeDTO, Integer page, Integer size, String organizationCode) {
        return ResponseEntity.ok(capacityRelatedQuestionService.getCapacityRelatedQuestions(questionTypeDTO, page, size, organizationCode));
    }

    @Override
    public ResponseEntity<List<CapacityRelatedQuestionSummaryForUserDTO>> getQuestionsForUser(QuestionTypeDTO questionType, String organizationCode) {
        return ResponseEntity.ok(answerForCapacityRelatedQuestionService.getQuestionsForUser(questionType, organizationCode));
    }

    @Override
    public ResponseEntity<Void> reorderCapacityRelatedQuestions(Map<String, Integer> questionIndexById) {
        capacityRelatedQuestionService.reorderQuestions(questionIndexById);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> saveAnswerToCapacityRelatedQuestion(SaveAnswerToCapacityRelatedQuestionCommandDTO command) {
        answerForCapacityRelatedQuestionService.save(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<QuestionsSumupForUserDTO> getQuestionsSumupForUser(String userId) {
        return ResponseEntity.ok(answerForCapacityRelatedQuestionService.sumupForUser(userId));
    }
}
