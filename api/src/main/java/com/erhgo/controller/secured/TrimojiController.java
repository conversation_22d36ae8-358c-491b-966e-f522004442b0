package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.openapi.controller.TrimojiApi;
import com.erhgo.openapi.dto.TrimojiUrlDTO;
import com.erhgo.services.trimoji.TrimojiService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
public class TrimojiController implements TrimojiApi {

    private final TrimojiService trimojiService;

    @Override
    public ResponseEntity<TrimojiUrlDTO> getTrimojiUrl() {
        return ResponseEntity.ok(new TrimojiUrlDTO(trimojiService.getNewUrl()));
    }

    @Override
    public ResponseEntity<Void> markEndOfTest() {
        trimojiService.markEndOfTest();
        return ResponseEntity.noContent().build();
    }
}
