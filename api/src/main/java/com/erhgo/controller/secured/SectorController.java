package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.openapi.controller.SectorApi;
import com.erhgo.openapi.dto.SectorDTO;
import com.erhgo.services.sector.SectorService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
public class SectorController implements SectorApi {

    private final SectorService sectorService;

    @Override
    public ResponseEntity<List<SectorDTO>> getSectors() {
        return ResponseEntity.ok(sectorService.getReferentialSectors());
    }
}
