package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.domain.classifications.erhgooccupation.OccupationCreationReason;
import com.erhgo.openapi.controller.ErhgoOccupationGenerationApi;
import com.erhgo.openapi.dto.*;
import com.erhgo.services.generation.*;
import com.erhgo.services.generation.dto.FindBestMatchingOccupationArguments;
import com.erhgo.services.generation.dto.GenericOpenAiPromptTesterCommand;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
@Slf4j
public class ErhgoOccupationGenerationController implements ErhgoOccupationGenerationApi {

    private final DescriptionGenerationService.OccupationDescriptionService occupationDescriptionGenerationService;
    private final DescriptionGenerationService.BehaviorDescriptionService behaviorDescriptionGenerationService;
    private final ActivityGenerationService activityGenerationService;
    private final BehaviorGenerationService behaviorGenerationService;
    private final ClassificationGenerationService classificationGenerationService;
    private final RomeGenerationService romeGenerationService;
    private final MasteryLevelGenerationService masteryLevelGenerationService;
    private final OccupationGenerator occupationGenerator;
    private final FindBestMatchingOccupationService findBestMatchingOccupationService;
    private final GenericPromptTesterService genericPromptTesterService;

    @Override
    public ResponseEntity<GeneratedOccupationDescriptionDTO> generateErghoOccupationDescription(UUID id) {
        return ResponseEntity.ok(occupationDescriptionGenerationService.generateOccupationDescription(id).getResult());
    }

    @Override
    public ResponseEntity<GeneratedOccupationDescriptionDTO> generateOccupationBehaviorsDescription(UUID id) {
        return ResponseEntity.ok(behaviorDescriptionGenerationService.generateOccupationBehaviorsDescription(id).getResult());
    }

    @Override
    public ResponseEntity<List<OccupationActivityDTO>> generateErghoOccupationActivities(UUID id) {
        return ResponseEntity.ok(activityGenerationService.qualifyAndPersistOccupationActivities(id).getResult());
    }

    @Override
    public ResponseEntity<ErhgoOccupationBehaviorsCategoriesDTO> generateOccupationBehaviors(UUID id) {
        return ResponseEntity.ok(behaviorGenerationService.associateBehaviorsToOccupation(id).getResult());
    }

    @Override
    public ResponseEntity<List<ErhgoClassificationDTO>> generateOccupationClassification(UUID id) {
        return ResponseEntity.ok(classificationGenerationService.generateAndAssociateClassificationsAssociations(id).getResult());
    }

    @Override
    public ResponseEntity<Void> generateOccupationClassificationRome(UUID id) {
        romeGenerationService.generateRomeAssociations(id);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> generateOccupationSpecificationAndMasteryLevel(UUID id) {
        masteryLevelGenerationService.generateMasteryLevel(id);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<List<GenerationReportItemDTO>> generateErhgoOccupationGlobalQualification(CreateErhgoOccupationCommandDTO createErhgoOccupationCommandDTO) {
        var response = occupationGenerator.qualifyOccupation(createErhgoOccupationCommandDTO.getId(), createErhgoOccupationCommandDTO.getTitle(), false, OccupationCreationReason.valueOf(createErhgoOccupationCommandDTO.getOccupationCreationReason().name()));
        return ResponseEntity.ok(response);
    }

    @Override
    public ResponseEntity<ErhgoOccupationMinimumInfoDTO> findBestMatchingOccupationForQuery(String query) {
        return ResponseEntity.ok(findBestMatchingOccupationService.findSimilarLabel(FindBestMatchingOccupationArguments.builder()
                .newLabel(query)
                .build()));

    }

    @Override
    public ResponseEntity<ChatCompletionResponseDTO> genericOpenAiPromptTester(Double temperature, String model, Integer maxTokens, Boolean forceJson, String promptMessages, MultipartFile fileContent) {
        return ResponseEntity.ok(genericPromptTesterService.testPrompt(GenericOpenAiPromptTesterCommand.builder()
                .temperature(temperature)
                .model(model)
                .maxTokens(maxTokens)
                .forceJson(forceJson)
                .promptMessages(promptMessages)
                .fileContent(fileContent)
                .build()));
    }
}
