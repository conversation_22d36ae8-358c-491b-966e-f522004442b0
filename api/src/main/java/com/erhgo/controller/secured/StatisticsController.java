package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.openapi.controller.StatisticsApi;
import com.erhgo.openapi.dto.MonthlyCandidaturesStatsDTO;
import com.erhgo.openapi.dto.MonthlySpontaneousCandidaturesStatsDTO;
import com.erhgo.openapi.dto.RecruitmentStatsDTO;
import com.erhgo.openapi.dto.SeveralMetricsStatsDTO;
import com.erhgo.services.StatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
public class StatisticsController implements StatisticsApi {

    @Autowired
    private StatisticsService statisticsService;

    @Override
    public ResponseEntity<MonthlyCandidaturesStatsDTO> getMonthlyCandidaturesStats() {
        return ResponseEntity.ok(statisticsService.getMonthlyCandidaturesStats());
    }

    @Override
    public ResponseEntity<RecruitmentStatsDTO> getRecruitmentStats() {
        return ResponseEntity.ok(statisticsService.getRecruitmentStats());
    }

    @Override
    public ResponseEntity<MonthlySpontaneousCandidaturesStatsDTO> getMonthlySpontaneousCandidaturesStats() {
        return ResponseEntity.ok(statisticsService.getMonthlySpontaneousCandidaturesStats());
    }


    @Override
    public ResponseEntity<SeveralMetricsStatsDTO> getSeveralMetricsStats() {
        return ResponseEntity.ok(statisticsService.getSeveralMetricsStats());
    }
}
