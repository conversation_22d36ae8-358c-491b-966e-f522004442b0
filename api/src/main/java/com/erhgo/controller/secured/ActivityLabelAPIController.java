package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.domain.KeycloakUserSummary;
import com.erhgo.openapi.controller.ActivityLabelApi;
import com.erhgo.openapi.dto.*;
import com.erhgo.services.ActivityService;
import com.erhgo.services.dto.criteria.ActivityCriteria;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
public class ActivityLabelAPIController implements ActivityLabelApi {

    @Autowired
    private ActivityService activityService;

    @Override
    public ResponseEntity<List<ActivityLabelSummaryDTO>> searchActivities(String query) {
        return ResponseEntity.ok(activityService.searchActivities(query));
    }

    @Override
    public ResponseEntity<ActivityDTO> getActivity(UUID id) {
        return ResponseEntity.ok(activityService.getActivity(id));
    }

    @Override
    public ResponseEntity<ActivityLabelPageDTO> listActivityLabels(ActivityTypeDTO activityType,
                                                                   Integer page,
                                                                   Integer size,
                                                                   List<Long> capacityIds,
                                                                   Boolean isCapacityRecursive,
                                                                   String filter,
                                                                   String userFilter,
                                                                   String by,
                                                                   SortDirectionDTO direction) {
        var criteria = new ActivityCriteria()
                .activityType(activityType)
                .page(page)
                .size(size)
                .capacityIds(capacityIds == null ? null : Sets.newHashSet(capacityIds))
                .isCapacityRecursive(isCapacityRecursive)
                .filter(filter)
                .userFilter(new KeycloakUserSummary(userFilter))
                .by(by)
                .direction(direction);
        return ResponseEntity.ok(activityService.listActivities(criteria));
    }

    @Override
    public ResponseEntity<Boolean> isActivityLabelDeletable(UUID id) {
        return ResponseEntity.ok(activityService.isActivityLabelDeletable(id));
    }
}
