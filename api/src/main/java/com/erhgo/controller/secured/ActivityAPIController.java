package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.openapi.controller.ActivityApi;
import com.erhgo.openapi.dto.ActivityTypeDTO;
import com.erhgo.openapi.dto.MergeActivityCommandDTO;
import com.erhgo.openapi.dto.SaveActivityCommandDTO;
import com.erhgo.services.ActivityService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
public class ActivityAPIController implements ActivityApi {

    @Autowired
    private ActivityService activityService;

    @Override
    public ResponseEntity<Void> saveActivity(SaveActivityCommandDTO saveActivityCommandDTO, ActivityTypeDTO activityType) {
        activityService.saveActivity(activityType, saveActivityCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> mergeActivities(MergeActivityCommandDTO mergeActivityCommandDTO) {
        activityService.mergeActivity(mergeActivityCommandDTO);
        return ResponseEntity.noContent().build();
    }
}
