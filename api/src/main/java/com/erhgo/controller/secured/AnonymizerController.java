package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.services.AnonymizerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Profile("!master")
@RestController
@RequestMapping(ApiConstants.API_ODAS)
@Slf4j
@RequiredArgsConstructor
public class AnonymizerController {

    private final AnonymizerService anonymizerService;

    @GetMapping("/anonymize")
    public ResponseEntity<Void> anonymize() {
        anonymizerService.anonymizeAll();
        return ResponseEntity.noContent().build();
    }
}
