package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.domain.enums.CandidatureState;
import com.erhgo.openapi.controller.CandidatureApi;
import com.erhgo.openapi.dto.*;
import com.erhgo.services.candidature.CommonCandidatureService;
import com.erhgo.services.candidature.RecruitmentCandidatureService;
import com.erhgo.services.candidature.SpontaneousCandidatureService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
@Slf4j
public class CandidatureController implements CandidatureApi {


    private final RecruitmentCandidatureService recruitmentCandidatureService;
    private final SpontaneousCandidatureService spontaneousCandidatureService;
    private final HttpServletResponse response;
    private final CommonCandidatureService commonCandidatureService;


    @Override
    public ResponseEntity<Void> publishCandidature(PublishCandidatureCommandDTO command) {
        if (command.getCandidatureId() != null) {
            recruitmentCandidatureService.changeStateTo(command.getCandidatureId(), CandidatureState.VALIDATED);
        } else if (command.getRecruitmentId() != null) {
            recruitmentCandidatureService.publishForAuthenticatedUser(command.getRecruitmentId());
        } else {
            throw new IllegalArgumentException("expected recruitment or candidature id");
        }
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<CandidatureNoteDTO> saveCandidatureNote(SaveCandidatureNoteCommandDTO saveCandidatureNoteCommandDTO, Long candidatureId) {
        return ResponseEntity.ok(recruitmentCandidatureService.saveNote(candidatureId, saveCandidatureNoteCommandDTO));
    }

    @Override
    public ResponseEntity<Void> setContextsMet(List<ContextMetDTO> contextsMet, Long candidatureId) {
        recruitmentCandidatureService.updateContextsMet(candidatureId, contextsMet);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<List<ContextToEvaluateReferencingExperiencesDTO>> getContextsToEvaluateReferencingExperiences(Long candidatureId) {
        return ResponseEntity.ok(recruitmentCandidatureService.getMandatoryContexts(candidatureId));
    }

    @Override
    public ResponseEntity<Void> saveCustomAnswer(String answer, Long recruitmentId) {
        recruitmentCandidatureService.saveCustomAnswerCandidature(recruitmentId, answer);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> updateAvailabilityForCandidature(UpdateAvailabilityForCandidatureCommandDTO command, Long recruitmentId) {
        recruitmentCandidatureService.updateAvailabilityForCandidature(recruitmentId, command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<List<GeneratedCandidaturesDTO>> generateCandidaturesOnRecruitments(GenerateCandidaturesOnRecruitmentsCommandDTO generateCandidaturesOnRecruitmentsCommandDTO) {
        return ResponseEntity.ok(recruitmentCandidatureService.generateCandidaturesOnRecruitments(
                generateCandidaturesOnRecruitmentsCommandDTO.getUserId(),
                generateCandidaturesOnRecruitmentsCommandDTO.getRecruitmentsId()
        ));
    }

    @Override
    public ResponseEntity<Void> markCandidatureAsRefused(RefuseCandidatureCommandDTO command) {
        recruitmentCandidatureService.markCandidatureAsRefusedFromBO(command.getCandidatureId(), command.getEmailTemplate());
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<CandidaturePreviewDTO> getCandidaturePreview(UUID jobUuid, String userId) {
        return ResponseEntity.ok(recruitmentCandidatureService.previewCandidature(jobUuid, userId));
    }


    @Override
    public ResponseEntity<CandidatureInitializationDataDTO> initializeCandidatureData(Long recruitmentId) {
        return ResponseEntity.ok(recruitmentCandidatureService.initializeCandidatureData(recruitmentId));
    }

    @Override
    public ResponseEntity<Void> updateSectorForSpontaneousCandidature(UpdateSectorForSpontaneousCandidatureCommandDTO command, String organizationCode) {
        spontaneousCandidatureService.updateSectors(organizationCode, command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<String> exportCandidatures(String recruitmentCode, String organizationCode) {
        try {
            response.setContentType("text/csv");
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            var fileName = "candidatures-%s.csv".formatted(Optional.ofNullable(recruitmentCode).or(() -> Optional.ofNullable(organizationCode)).orElse("Tous"));
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            commonCandidatureService.writeCandidaturesCsv(
                    new OutputStreamWriter(response.getOutputStream(), StandardCharsets.UTF_8),
                    organizationCode,
                    recruitmentCode
            );
            return ResponseEntity.ok().build();
        } catch (IOException e) {
            log.error("failed to export csv", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
