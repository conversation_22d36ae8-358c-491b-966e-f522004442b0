package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.openapi.controller.MissionApi;
import com.erhgo.openapi.dto.CreateMissionCommandDTO;
import com.erhgo.openapi.dto.MissionDTO;
import com.erhgo.openapi.dto.UpdateMissionCommandDTO;
import com.erhgo.services.MissionService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.URI;
import java.net.URISyntaxException;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
public class MissionController implements MissionApi {

    @Autowired
    private MissionService missionService;

    @Override
    public ResponseEntity<MissionDTO> createNewMission(CreateMissionCommandDTO createMissionCommand) {
        var mission = missionService.create(createMissionCommand);
        return buildResponse(mission);
    }

    private ResponseEntity<MissionDTO> buildResponse(MissionDTO mission) {
        try {
            return ResponseEntity.created(new URI(ApiConstants.API_ODAS_MISSION + ApiConstants.SEPARATOR + mission.getId()))
                    .body(mission);
        } catch (URISyntaxException e) {
            throw new IllegalStateException(e);
        }
    }

    @Override
    public ResponseEntity<Void> deleteMission(Long missionId) {
        missionService.delete(missionId);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<MissionDTO> updateMission(UpdateMissionCommandDTO updateMissionCommand, Long missionId) {
        return buildResponse(missionService.update(missionId, updateMissionCommand));
    }

}
