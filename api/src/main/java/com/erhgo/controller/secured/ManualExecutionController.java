package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.openapi.controller.ManualExecutionApi;
import com.erhgo.openapi.dto.AtsSyncCommandDTO;
import com.erhgo.openapi.dto.TaskInformationDTO;
import com.erhgo.security.Role;
import com.erhgo.services.AdminService;
import com.erhgo.services.externaloffer.ExternalOfferScheduler;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
@Slf4j
public class ManualExecutionController implements ManualExecutionApi {


    private final AdminService adminService;
    private final ExternalOfferScheduler externalOfferScheduler;

    @Override
    @RolesAllowed(Role.ODAS_ADMIN)
    public ResponseEntity<Void> executeManuallyTask(TaskInformationDTO command) {
        adminService.executeManuallyTask(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> syncAts(AtsSyncCommandDTO atsSyncCommandDTO) {
        externalOfferScheduler.analyzeDataForATS(atsSyncCommandDTO.getAtsCode(), atsSyncCommandDTO.getCustomCode());
        return ResponseEntity.noContent().build();
    }
}
