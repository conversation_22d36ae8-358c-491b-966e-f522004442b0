package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.openapi.controller.RomeApi;
import com.erhgo.openapi.dto.RomePageDTO;
import com.erhgo.services.RomeService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
public class RomeController implements RomeApi {

    @Autowired
    private RomeService romeService;

    @Override
    public ResponseEntity<RomePageDTO> romePage(Integer size, Integer page, String query) {
        return ResponseEntity.ok(romeService.getRomePage(size, page, query));
    }
}
