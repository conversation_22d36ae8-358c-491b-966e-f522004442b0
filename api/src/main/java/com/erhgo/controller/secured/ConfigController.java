package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.openapi.controller.ConfigApi;
import com.erhgo.openapi.dto.AlgoliaConfigDTO;
import com.erhgo.openapi.dto.ConfigurablePropertyDTO;
import com.erhgo.services.ConfigurablePropertyService;
import com.erhgo.services.search.AlgoliaSearchConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
public class ConfigController implements ConfigApi {
    private final Optional<AlgoliaSearchConfigService> configService;
    private final ConfigurablePropertyService configurablePropertyService;

    @Override
    public ResponseEntity<AlgoliaConfigDTO> getAlgoliaSearchConfiguration(String organizationCode) {
        return configService.map(algoliaSearchConfigService -> ResponseEntity.ok().body(algoliaSearchConfigService.getSecuredAlgoliaConfiguration(organizationCode)))
                .orElseGet(() -> ResponseEntity.internalServerError().build());
    }

    @Override
    public ResponseEntity<List<ConfigurablePropertyDTO>> getAllConfigurableProperties() {
        return ResponseEntity.ok(configurablePropertyService.getAllConfigurableProperties());
    }

    @Override
    public ResponseEntity<Void> editConfigurableProperty(ConfigurablePropertyDTO configurablePropertyDTO) {
        configurablePropertyService.updateConfigurablePropertyValue(configurablePropertyDTO);
        return ResponseEntity.noContent().build();
    }
}
