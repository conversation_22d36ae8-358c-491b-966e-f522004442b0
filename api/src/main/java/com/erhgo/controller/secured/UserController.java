package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.controller.MobileHackForContractTypeUtils;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.openapi.controller.UserApi;
import com.erhgo.openapi.dto.*;
import com.erhgo.services.*;
import com.erhgo.services.candidature.CommonCandidatureService;
import com.erhgo.services.criteria.CriteriaService;
import com.erhgo.services.generation.HashtagsGenerationService;
import com.erhgo.services.search.UserIndexer;
import com.erhgo.services.sourcing.DeactivateUserSourcingService;
import com.erhgo.services.trimoji.TrimojiService;
import com.erhgo.services.userprofile.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.OutputStreamWriter;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@Slf4j
@RequiredArgsConstructor
public class UserController implements UserApi {

    private final HttpServletRequest request;

    private final CommonCandidatureService commonCandidatureService;
    private final GeneralInformationService generalInformationService;
    private final JobService jobService;
    private final RecruitmentService recruitmentService;
    private final UserDeletionService userDeletionService;
    private final DeactivateUserSourcingService deactivateUserSourcingService;
    private final UserProfileService userProfileService;
    private final CVDataExtractorService userExperiencesExtractorService;
    private final UserProfileCompetencesExportService userProfileCompetenceExportService;
    private final BulkCVProcessingService bulkCVProcessingService;

    private final UserBehaviorDescriptionService userBehaviorDescriptionService;
    private final HashtagsGenerationService hashtagsGenerationService;

    private final UserErhgoClassificationService userErhgoClassificationService;
    private final CriteriaService criteriaService;
    private final UserIndexer userIndexer;

    private final DateFormat filenameDateFormatter = new SimpleDateFormat("yyyy-MM-dd_hh_mm");
    private final HttpServletResponse response;
    private final TrimojiService trimojiService;

    @Override
    public ResponseEntity<Void> createUser(CreateUserCommandDTO createUserCommandDTO) {
        var createdUserId = userProfileService.createUser(createUserCommandDTO);
        try {
            return ResponseEntity.created(new URI(createdUserId)).build();
        } catch (URISyntaxException e) {
            throw new IllegalStateException();
        }
    }

    @Override
    public ResponseEntity<CreateUserFOResultDTO> createUserFO(CreateUserFOCommandDTO createUserFOCommandDTO) {
        return ResponseEntity.ok(userProfileService.createUserFO(createUserFOCommandDTO));
    }

    @Override
    public ResponseEntity<Void> deleteUser(String userId) {
        userDeletionService.deleteUser(userId);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> setFrontOfficeUserPassword(SetFrontOfficeUserPasswordCommandDTO setFrontOfficeUserPasswordCommandDTO) {
        userProfileService.setFrontOfficeUserPassword(setFrontOfficeUserPasswordCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> confirmFOUserFromBO(ConfirmFOUserFromBOCommandDTO confirmFOUserFromBOCommandDTO) {
        userProfileService.confirmFOUserFromBO(confirmFOUserFromBOCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> resendInitialMail(ResendInitialMailCommandDTO command) {
        userProfileService.resendInitialMail(command.getUserId());
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<UserProfileSummaryDTO> getUserProfile(String userId) {
        return ResponseEntity.ok(userProfileService.findCandidateUserProfileByUserId(userId));
    }

    @Override
    public ResponseEntity<UserProfileDetailWithCapacitiesDTO> getUserProfileDetailWithCapacities(String userId) {
        return ResponseEntity.ok(userProfileService.getUserProfileDetailsWithCapacities(userId));
    }

    @Override
    public ResponseEntity<List<UserCriteriaValueDTO>> getUserCriterias(String userId) {
        return ResponseEntity.ok(userProfileService.getUserCriterias(userId));
    }

    @Override
    public ResponseEntity<List<SimpleCriteriaDTO>> getSimpleUserCriterias() {
        var criteria = criteriaService.getSimpleUserCriteria();
        MobileHackForContractTypeUtils.hackForMobile(criteria, request);

        return ResponseEntity.ok(criteria);
    }

    @Override
    public ResponseEntity<Void> setUserCriterias(SaveUserCriteriasCommandDTO command) {
        criteriaService.setUserCriteria(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> resetUserCriteria() {
        criteriaService.resetAuthenticatedUserSimpleCriteria();
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> setUserName(SaveUserNameCommandDTO saveUserNameCommandDTO) {
        userProfileService.setUserName(saveUserNameCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> setUserCity(SetUserCityCommandDTO setUserCityCommandDTO) {
        generalInformationService.updateCity(setUserCityCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> setUserSalary(SetUserSalaryCommandDTO dto) {
        generalInformationService.updateSalary(dto);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> setUserOccupation(SetUserOccupationCommandDTO dto) {
        userProfileService.setUserOccupation(dto);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> tagNoExperienceForUser() {
        userProfileService.tagNoExperienceForUser();
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> setUserSituation(SetUserSituationCommandDTO dto) {
        generalInformationService.updateSituation(dto);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> setUserPhoneNumber(SetUserPhoneNumberCommandDTO dto) {
        generalInformationService.updatePhoneNumber(dto);
        return ResponseEntity.noContent().build();
    }


    @Override
    public ResponseEntity<Void> setUserContactInfo(SaveUserContactInfoCommandDTO saveUserContactInfoCommandDTO, String userId) {
        generalInformationService.updateContactInfo(userId, saveUserContactInfoCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<UserContactInfoDTO> getUserContactInfo(String userId) {
        return ResponseEntity.ok(generalInformationService.getContactInfo(userId));
    }

    @Override
    public ResponseEntity<List<CandidatureDTO>> getUserCandidatures(String userId, Boolean includesSpontaneous) {
        return ResponseEntity.ok(commonCandidatureService.getUserCandidatures(userId, BooleanUtils.isTrue(includesSpontaneous)));
    }

    @Override
    public ResponseEntity<List<SimpleCandidatureDTO>> getSimpleUserCandidatures(String userId) {
        return ResponseEntity.ok(commonCandidatureService.getSimpleUserCandidatures(userId));
    }

    @Override
    public ResponseEntity<List<ExperienceDetailsDTO>> getUserExperiences(String userId) {
        return ResponseEntity.ok(userProfileService.getUserExperiences(userId));
    }

    @Override
    public ResponseEntity<List<UserSummaryDTO>> getBackOfficeUsers() {
        return ResponseEntity.ok(generalInformationService.getBackOfficeUsers());
    }

    @Override
    public ResponseEntity<UserPageDTO> searchFrontOfficeUser(Integer size,
                                                             Integer page,
                                                             String query) {
        return ResponseEntity.ok(generalInformationService.searchFrontOfficeUsers(query, page, size));
    }

    @Override
    public ResponseEntity<UserByGroupPageDTO> searchFrontOfficeUserByGroups(List<String> organizationCodes, Integer page, Integer size, String postcode, String search, Boolean strictOrganizationFilter) {
        return ResponseEntity.ok(generalInformationService.searchFrontOfficeUsersByGroup(organizationCodes, size, page, strictOrganizationFilter, postcode, search));
    }

    @Override
    public ResponseEntity<String> getFrontOfficeUserByGroupExport(String organizationCode) {
        try {
            response.setContentType("text/csv");
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            var fileName = "utilisateurs_" + filenameDateFormatter.format(new Date()) + ".csv";
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName);
            generalInformationService.writeUsersByGroupCsv(new OutputStreamWriter(response.getOutputStream(), StandardCharsets.UTF_8), organizationCode, false);
        } catch (IOException e) {
            log.error("<failed> to export users by group csv", e);
        }
        return ResponseEntity.ok().build();
    }

    @Override
    public ResponseEntity<CapacitiesResultDTO> getUserCapacities(String userId) {
        return ResponseEntity.ok(userProfileService.getUserCapacities(userId));
    }

    @Override
    public ResponseEntity<Float> getUserLevel(String userId) {
        return ResponseEntity.ok(userProfileService.getUserLevel(userId));
    }

    @Override
    public ResponseEntity<InitializedProfileDTO> initializeProfile(InitializeProfileCommandDTO command) {
        return ResponseEntity.ok(userProfileService.initializeProfile(command, getReferer()));
    }

    private String getReferer() {
        return request.getHeader("Referer");
    }

    @Override
    public ResponseEntity<Void> updateUserRegistrationStateStep(UpdateRegistrationStepCommandDTO userRegistrationStateStep) {
        userProfileService.updateRegistrationState(userRegistrationStateStep);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<MailingListOptInDTO> getUserJobOffersOptIn(String userId) {
        var result = userProfileService.getJobOffersOptIn(userId);
        return ResponseEntity.ok(new MailingListOptInDTO().value(result));
    }

    @Override
    public ResponseEntity<Void> updateUserJobOffersOptIn(UpdateUserMailingListOptInCommandDTO command) {
        userProfileService.updateJobOffersOptIn(command.getUserId(), command.getValue());
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<MailingListOptInDTO> getUserJobDatingNotifyOptIn(String userId) {
        var result = userProfileService.getJobDatingOptIn(userId);
        return ResponseEntity.ok(new MailingListOptInDTO().value(result));
    }

    @Override
    public ResponseEntity<Void> updateUserJobDatingNotifyOptIn(UpdateUserMailingListOptInCommandDTO command) {
        userProfileService.updateNewsOptIn(command.getUserId(), command.getValue());
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Boolean> getUserHandicapModeEnabled(String userId) {
        var result = userProfileService.getHandicapModeEnabled(userId);
        return ResponseEntity.ok(result);
    }

    @Override
    public ResponseEntity<Void> updateUserHandicapModeEnabled(UpdateUserHandicapModeEnabledRequestDTO command) {
        userProfileService.updateHandicapModeEnabled(command.getUserId(), command.getValue());
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> updateUsersChannels(UpdateUsersChannelsCommandDTO command) {
        userProfileService.updateUsersChannels(command);
        return ResponseEntity.noContent().build();
    }


    @Override
    public ResponseEntity<String> exportUsers(UsersExportRequestDTO usersExportRequest) {
        try {
            log.info("***Export FO users - bufferSize set to 32k ***");
            response.setBufferSize(32 * 1024);
            response.setContentType("text/csv");
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            var fileName = "utilisateursFO_" + filenameDateFormatter.format(new Date()) + ".csv";
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName);
            generalInformationService.writeUsersCsv(new OutputStreamWriter(response.getOutputStream(), StandardCharsets.UTF_8), usersExportRequest.getUsersId(), usersExportRequest.getDeanonymizedUser());
        } catch (IOException e) {
            log.error("failed to export users csv", e);
            throw new GenericTechnicalException("Unable to generate CSV");
        }

        return ResponseEntity.ok().build();
    }

    @Override
    public ResponseEntity<SimpleRecruitmentPageDTO> getRecruitments(Integer page, Integer size, String query) {
        return ResponseEntity.ok(MobileHackForContractTypeUtils.hackForMobile(recruitmentService.getRecruitments(page, size, query), request));
    }

    @Override
    public ResponseEntity<SimpleRecruitmentCountDTO> getRecruitmentsCount(String query) {
        return ResponseEntity.ok(recruitmentService.getRecruitmentsCount(query));
    }

    @Override
    public ResponseEntity<List<UserSummaryDTO>> getMembersOfGroups(String organizationCode) {
        return ResponseEntity.ok(userProfileService.getMembersOfGroups(organizationCode));
    }

    @Override
    public ResponseEntity<List<UserCandidatureDTO>> getUserJobsCandidatures(String userId, String organizationCode) {
        return ResponseEntity.ok(jobService.getUserJobsCandidatures(userId, organizationCode));
    }

    @Override
    public ResponseEntity<Void> clearCaches() {
        userProfileService.clearCaches();
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<UserProfileProgressDTO> getUserProfileProgress(String userId, String organization) {
        return ResponseEntity.ok(userProfileService.getUserProfileProgress(userId, organization));
    }

    @Override
    public ResponseEntity<Resource> getUserProfileFOPdf(String userId, Boolean forceAnonymous, Boolean handicap) {
        try (var outputStream = response.getOutputStream()) {
            response.setContentType(MediaType.APPLICATION_PDF_VALUE);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=competences.pdf");
            userProfileCompetenceExportService.getUserProfileForUser(userId, outputStream, Optional.ofNullable(forceAnonymous).orElse(false), BooleanUtils.isTrue(handicap));
        } catch (IOException e) {
            log.error("failed to export pdf", e);
            throw new GenericTechnicalException("Unable to export profile", e);
        }
        return ResponseEntity.ok().build();

    }

    @Override
    public ResponseEntity<UserRegistrationStateDTO> getUserRegistrationState(String userId) {
        return ResponseEntity.ok(userProfileService.getUserRegistrationState(userId));
    }

    @Override
    public ResponseEntity<List<UserNoteDTO>> getUserNotes(String userId) {
        return ResponseEntity.ok(userProfileService.getUserNotes(userId));
    }

    @Override
    public ResponseEntity<Void> saveUserNote(SaveUserNoteCommandDTO saveUserNoteCommandDTO) {
        userProfileService.setUserNote(saveUserNoteCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> deleteUserNote(DeleteUserNoteCommandDTO command) {
        userProfileService.deleteUserNote(command);
        return ResponseEntity.noContent().build();
    }


    @Override
    public ResponseEntity<UserErhgoClassificationsDTO> getUserErhgoClassifications(String userId) {
        return ResponseEntity.ok(userErhgoClassificationService.getUserErhgoClassifications(userId));
    }

    @Override
    public ResponseEntity<Void> setUserErhgoClassification(SetUserErhgoClassificationCommandDTO command) {
        userErhgoClassificationService.setUserErhgoClassification(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> reindexAllUsers() {
        userIndexer.indexAllFOUsers();
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> indexUsersNow() {
        userIndexer.indexModifiedUsers();
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<SetUserEmailResultDTO> setUserEmail(SetUserEmailCommandDTO setUserEmailCommandDTO, String userId) {
        var result = userProfileService.setUserEmail(userId, setUserEmailCommandDTO.getEmail());
        return ResponseEntity.ok(result);
    }

    @Override
    public ResponseEntity<List<BehaviorDTO>> getUserBehaviorDetails(String userId) {
        return ResponseEntity.ok(userProfileService.getUserBehaviors(userId));
    }

    @Override
    public ResponseEntity<Void> updateUserBehaviors(List<UUID> behaviors, String userId) {
        userProfileService.updateUserBehaviors(userId, behaviors);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> setUserBlacklistOccupations(SetUserBlacklistOccupationsCommandDTO command) {
        userProfileService.setUserBlacklistOccupations(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> removeUserBlacklistOccupations(RemoveUserBlacklistOccupationsCommandDTO command) {
        userProfileService.removeUserBlacklistOccupations(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<UserBehaviorDescriptionDTO> getUserBehaviorDescription(String userId) {
        return ResponseEntity.ok(userBehaviorDescriptionService.getUserBehaviorDescriptionOrGenerate(userId));
    }

    @Override
    public ResponseEntity<Void> generateExperiencesFromCV(MultipartFile file, String userId) {
        userExperiencesExtractorService.extractUserExperiencesFromCV(userId, new FilePartProvider(file));
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<UserFileImportStateDTO> getUserFileImportState(String userId) {
        return ResponseEntity.ok(userExperiencesExtractorService.getFileImportState(userId));
    }

    @Override
    public ResponseEntity<Void> forceUserBehaviorDescription(ForceUserBehaviorDescriptionCommandDTO command) {
        userBehaviorDescriptionService.forceUserBehaviorDescription(command.getDescription());
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<ChannelAffectationInformationsDTO> getUserChannelAffectations(String userId) {
        return ResponseEntity.ok(userProfileService.getUserChannelAffectations(userId));
    }

    @Override
    public ResponseEntity<Void> legacyUpdateShouldBeContactedDoNotUse(DeprecatedCommandForLegacyMobileVersionDTO deprecatedCommandForLegacyMobileVersionDTO) {
        log.warn("Legacy service 'ShouldBeContacted' update called - nothing done");
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Resource> getUserSoftSkillsPdfResult(String userId) {
        try (var outputStream = response.getOutputStream()) {
            response.setContentType(MediaType.APPLICATION_PDF_VALUE);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=softSkills.pdf");
            outputStream.write(trimojiService.getPdfForUser(userId));
        } catch (IOException e) {
            log.error("failed to export soft skills pdf", e);
            throw new GenericTechnicalException("Unable to export profile", e);
        }
        return ResponseEntity.ok().build();
    }

    @SneakyThrows
    @Override
    public ResponseEntity<List<String>> getUserHashtags(String userId) {
        return ResponseEntity.ok(new ArrayList<>(hashtagsGenerationService.getHashtagsOrGenerate(userId)));
    }

    @Override
    public ResponseEntity<HashtagsDTO> regenerateUserHashtags(HashtagsDTO command, String userId) {
        var regeneratedHashtags = hashtagsGenerationService.regenerateHashtags(userId, command.getHashtags(), command.getDeselectedHashtags());
        return ResponseEntity.ok(new HashtagsDTO().hashtags(regeneratedHashtags));
    }

    @Override
    public ResponseEntity<Void> deactivateUserSourcing(DeactivateUserSourcingCommandDTO command) {
        deactivateUserSourcingService.deactivateUser(command.getUserId(), command.getReplacementUserId());
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> activateUserSourcing(String userId) {
        deactivateUserSourcingService.activateUser(userId);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<UserProfileCompletionDTO> getUserProfileCompletion() {
        return ResponseEntity.ok(new UserProfileCompletionDTO().value(userProfileService.getUserProfileCompletion()));
    }

    @Override
    public ResponseEntity<BulkCVProcessingResultDTO> createOrUpdateProfilesForCVs(MultipartFile csvFile, Boolean excludeExistingUsers) {
        var result = bulkCVProcessingService.createOrUpdateProfilesForCVs(csvFile, Boolean.TRUE.equals(excludeExistingUsers));
        return ResponseEntity.ok(result);
    }

    @Override
    public ResponseEntity<List<UserHandicapInfoDTO>> getUsersHandicapInfo() {
        return ResponseEntity.ok(generalInformationService.getUsersHandicapInfo());
    }

    @Override
    public ResponseEntity<String> getSimpleUsersInfoExport() {
        try {
            response.setContentType("text/csv");
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            var fileName = "utilisateurs_simple_" + filenameDateFormatter.format(new Date()) + ".csv";
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName);
            generalInformationService.writeSimpleUsersCsv(new OutputStreamWriter(response.getOutputStream(), StandardCharsets.UTF_8));
        } catch (IOException e) {
            log.error("<failed> to export handicap users csv", e);
        }
        return ResponseEntity.ok().build();
    }

}
