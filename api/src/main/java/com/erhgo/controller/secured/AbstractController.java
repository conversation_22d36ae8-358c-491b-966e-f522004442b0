package com.erhgo.controller.secured;

import com.erhgo.domain.AbstractEntity;
import com.erhgo.services.AbstractService;
import jakarta.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

public class AbstractController<E extends AbstractEntity, S extends AbstractService<E, ?>> {

    public static final String DEFAULT_PROPERTY_NAME = "code";
    public static final String DEFAULT_SORT_DIRECTION = "ASC";

    @Autowired
    private S service;

    @GetMapping(value = "/{code}", produces = {"application/hal+json"})
    public ResponseEntity<E> findByCode(@PathVariable("code") String code) {
        final var entity = service.findOneByCode(code);
        if (entity == null) {
            return ResponseEntity.notFound().build();
        }

        return ResponseEntity.ok(entity);
    }

    @GetMapping(produces = {"application/hal+json"})
    public List<E> findByIds(@RequestParam(value = "ids") List<Long> ids) {
        return service.findByIds(ids);
    }

    @GetMapping(value = "/count", produces = {"application/hal+json"})
    public ResponseEntity<Long> count() {
        return ResponseEntity.ok(service.count());
    }

    @GetMapping(value = "/list", produces = {"application/hal+json"})
    public ResponseEntity<AbstractService.PageDTOAdapter<E>> findPaginatedAndFilteredByProperty(
            @RequestParam(value = "page") int page,
            @RequestParam(value = "size") int size,
            @RequestParam(value = "by", required = false, defaultValue = DEFAULT_PROPERTY_NAME) String sortProperty,
            @RequestParam(value = "direction", required = false, defaultValue = DEFAULT_SORT_DIRECTION) String sortDirection,
            @RequestParam(value = "filter", required = false, defaultValue = StringUtils.EMPTY) String filter) {
        return ResponseEntity.ok(service.findPaginatedAndFilteredByProperty(page, size, sortProperty, sortDirection, filter));
    }

    @GetMapping(value = "/list/all", produces = {"application/hal+json"})
    public ResponseEntity<Iterable> findAll() {
        return ResponseEntity.ok(service.findAll());
    }

    @PostMapping(value = "/create", produces = {"application/hal+json"})
    public ResponseEntity<E> create(@RequestBody @Valid E origin) {
        return ResponseEntity.ok(service.create(origin));
    }

    @PatchMapping(value = "/update", produces = {"application/hal+json"})
    public ResponseEntity<E> update(@RequestBody @Valid E origin) {
        return ResponseEntity.ok(service.update(origin));
    }
}
