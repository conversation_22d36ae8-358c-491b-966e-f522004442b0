package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.openapi.controller.DataHealthCheckApi;
import com.erhgo.services.datahealthchecker.DataHealthCheckerService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
public class DataHealthCheckController implements DataHealthCheckApi {

    private final DataHealthCheckerService service;

    @Override
    public ResponseEntity<Void> executeAllHealthCheckQueries() {
        service.executeAllDataHealthChecker();
        return ResponseEntity.noContent().build();
    }
}
