package com.erhgo.controller;

import com.erhgo.openapi.dto.RecruitmentDetailDTO;
import com.erhgo.openapi.dto.SimpleCriteriaDTO;
import com.erhgo.openapi.dto.SimpleRecruitmentPageDTO;
import com.erhgo.openapi.dto.TypeContractDTO;
import jakarta.servlet.http.HttpServletRequest;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.stream.Collectors;

/**
 * FIXME: to remove when mobile app is updated
 */

public class MobileHackForContractTypeUtils {
    public static final List<@NotNull String> CODES_TO_FILTER = List.of("REP-1-4", "REP-1-5", "REP-1-6");

    public static SimpleRecruitmentPageDTO hackForMobile(SimpleRecruitmentPageDTO recruitments, HttpServletRequest request) {
        if (isMobileApp(request)) {
            recruitments.getContent().forEach(a -> a.setTypeContract(adjustTypeContract(a.getTypeContract())));
        }
        return recruitments;
    }

    private static TypeContractDTO adjustTypeContract(TypeContractDTO a) {
        return a == TypeContractDTO.SEASONAL || a == TypeContractDTO.FREELANCE ? TypeContractDTO.CDD : a;
    }

    public static void hackForMobile(List<SimpleCriteriaDTO> criteria, HttpServletRequest request) {
        var isMobileApp = isMobileApp(request);
        if (isMobileApp) {
            criteria.forEach(criterion -> {
                var filteredValues = criterion.getCriteriaValues().stream()
                        .filter(value -> !CODES_TO_FILTER.contains(value.getCode()))
                        .collect(Collectors.toList());
                criterion.setCriteriaValues(filteredValues);
            });
        }
    }

    public static boolean isMobileApp(HttpServletRequest request) {
        var userAgent = request.getHeader("User-Agent");
        return userAgent != null && userAgent.toLowerCase().contains("dart");
    }


    public static RecruitmentDetailDTO hackForMobile(RecruitmentDetailDTO detail, HttpServletRequest request) {
        if (detail != null && detail.getTypeContract() != null && isMobileApp(request)) {
            detail.setTypeContract(adjustTypeContract(detail.getTypeContract()));
        }
        return detail;
    }
}
