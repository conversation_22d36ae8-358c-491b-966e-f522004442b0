package com.erhgo.config;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ApiConstants {

    public static final String SEPARATOR = "/";
    public static final String API = SEPARATOR + "api";
    public static final String API_ODAS = API + SEPARATOR + "odas";

    public static final String API_ODAS_CAPACITY = API_ODAS + SEPARATOR + "capacity";
    public static final String API_ODAS_CATEGORY = API_ODAS + SEPARATOR + "category";
    public static final String API_ODAS_ORGANIZATION = API_ODAS + SEPARATOR + "organization";
    public static final String API_ODAS_JOB = API_ODAS + SEPARATOR + "job";
    public static final String API_ODAS_CANDIDATURE = API_ODAS + SEPARATOR + "candidature";
    public static final String API_ODAS_RECRUITMENT = API_ODAS + SEPARATOR + "recruitment";
    public static final String API_ODAS_MISSION = API_ODAS + SEPARATOR + "mission";


    public static final String API_ODAS_USERS = API_ODAS + SEPARATOR + "users";

    public static final String API_ODAS_ESCO = API_ODAS + SEPARATOR + "esco";
    public static final String API_ODAS_ESCO_SKILL = API_ODAS_ESCO + SEPARATOR + "skill";

}
