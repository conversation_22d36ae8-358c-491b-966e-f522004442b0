package com.erhgo.config;

import com.google.common.base.Strings;
import jakarta.annotation.PostConstruct;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;

@ConfigurationProperties(prefix = "keycloak-realms", ignoreUnknownFields = false)
@Profile("!test")
@Configuration
@Data
@Validated
@Slf4j
public class KeycloakRealmsConfig {
    public static final String URL_PATTERN = "^https?://.*";

    @NotBlank
    private String frontOfficeRealmId;

    @NotBlank
    private String backOfficeRealmId;

    @NotBlank
    private String sourcingRealmId;

    @Pattern(regexp = URL_PATTERN)
    @NotBlank
    private String frontOfficeBaseURL;

    @Pattern(regexp = URL_PATTERN)
    @NotBlank
    private String backOfficeBaseURL;

    @Pattern(regexp = URL_PATTERN)
    @NotBlank
    private String sourcingBaseURL;

    @NotBlank
    private String frontApiClientSecret;

    @NotBlank
    private String backApiClientSecret;

    @NotBlank
    private String sourcingApiClientSecret;

    @NotBlank
    private String passwordPolicy;

    @Pattern(regexp = URL_PATTERN)
    @NotBlank
    private String apiBaseURL;

    @NotBlank
    private String frontOfficeClientId;

    @NotBlank
    private String backOfficeClientId;

    @NotBlank
    private String sourcingClientId;

    @NotEmpty
    private List<String> frontOfficeRedirects;

    @NotEmpty
    private List<String> backOfficeRedirects;

    @NotEmpty
    private List<String> sourcingRedirects;

    private Map<String, String> keyForRealm;

    @NotBlank
    private String authServerUrl;

    @NotBlank
    private String googleClientId;

    @NotBlank
    private String googleClientSecret;

    @NotBlank
    private String appleClientId;

    @NotBlank
    private String appleTeamId;

    @NotBlank
    private String appleKeyId;

    @NotBlank
    private String appleP8Key;

    @PostConstruct
    public void init() {
        this.keyForRealm = Map.of(frontOfficeRealmId, frontApiClientSecret, sourcingRealmId, sourcingApiClientSecret, backOfficeRealmId, backApiClientSecret);
    }

    public String getSecret(String realm) {
        if (Strings.isNullOrEmpty(realm)) {
            return backApiClientSecret;
        }
        return keyForRealm.get(realm);
    }

    public String getAuthServerUrlForRealm(String realm) {
        return "%s/realms/%s/protocol/openid-connect/".formatted(authServerUrl, realm);
    }
}
