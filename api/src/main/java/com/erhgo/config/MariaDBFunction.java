package com.erhgo.config;

import org.hibernate.boot.model.FunctionContributions;
import org.hibernate.boot.model.FunctionContributor;
import org.hibernate.type.StandardBasicTypes;

public class MariaDBFunction implements FunctionContributor {

    // FIXME: unable to have variable number of cols
    @Override
    public void contributeFunctions(FunctionContributions functionContributions) {
        var functionRegistry = functionContributions.getFunctionRegistry();
        functionRegistry.registerNamed(
                "ST_Distance_Sphere", functionContributions.getTypeConfiguration().getBasicTypeRegistry().resolve(StandardBasicTypes.INTEGER)
        );
        functionRegistry.registerPattern("match_against_1_col_with_expansion", "MATCH(?1) AGAINST (?2 WITH QUERY EXPANSION)",
                functionContributions.getTypeConfiguration().getBasicTypeForJavaType(Double.class));

        functionRegistry.registerPattern("match_against_2_col_boolean", "MATCH(?1, ?2) AGAINST (?3 IN BOOLEAN MODE)",
                functionContributions.getTypeConfiguration().getBasicTypeForJavaType(Double.class));

        functionRegistry.registerPattern("match_against_1_col_boolean", "MATCH(?1) AGAINST (?2 IN BOOLEAN MODE)",
                functionContributions.getTypeConfiguration().getBasicTypeForJavaType(Double.class));


    }


}
