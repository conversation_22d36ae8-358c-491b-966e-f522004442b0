package com.erhgo.config;

import com.erhgo.services.notifier.messages.AbstractNotifierMessageDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@ConfigurationProperties(prefix = "slack")
@EnableConfigurationProperties()
@Component
@Slf4j
public class SlackProperties {
    @Getter
    @Setter
    private Map<String, String> urlForMessageType = new HashMap<>();

    public String getUrl(AbstractNotifierMessageDTO message) {
        return Optional.ofNullable(message.getForcedSlackChannel()).or(() -> Optional.ofNullable(urlForMessageType).map(a -> a.get(message.getClass().getSimpleName()))).orElse(null);
    }

}
