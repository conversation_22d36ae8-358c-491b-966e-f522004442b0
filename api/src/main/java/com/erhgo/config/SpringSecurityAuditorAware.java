package com.erhgo.config;


import com.erhgo.domain.KeycloakUserSummary;
import com.erhgo.services.SecurityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.AuditorAware;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
class SpringSecurityAuditorAware implements AuditorAware<KeycloakUserSummary> {

    @Autowired
    private SecurityService securityService;

    @Override
    public Optional<KeycloakUserSummary> getCurrentAuditor() {

        return securityService.getKeycloakUserSummary();
    }


}


