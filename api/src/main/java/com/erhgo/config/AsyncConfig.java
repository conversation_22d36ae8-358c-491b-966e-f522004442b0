package com.erhgo.config;

import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.core.LockProvider;
import net.javacrumbs.shedlock.provider.jdbctemplate.JdbcTemplateLockProvider;
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.task.TaskDecorator;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.concurrent.ConcurrentTaskScheduler;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.security.concurrent.DelegatingSecurityContextExecutor;

import javax.sql.DataSource;
import java.util.concurrent.*;

@Configuration
@EnableAsync
@EnableScheduling
@EnableSchedulerLock(defaultLockAtMostFor = "PT2H", defaultLockAtLeastFor = "PT5S")
@Profile("!test")
@Slf4j
public class AsyncConfig implements AsyncConfigurer, SchedulingConfigurer {

    @Value("${spring.tasks.scheduled.pool.core-size}")
    private int scheduledCorePoolSize;

    @Value("${spring.tasks.scheduled.pool.max-size}")
    private int scheduledMaxPoolSize;

    @Value("${spring.tasks.async.pool.core-size}")
    private int asyncCorePoolSize;

    @Value("${spring.tasks.async.pool.max-size}")
    private int asyncMaxPoolSize;

    @Bean
    public LockProvider lockProvider(DataSource dataSource) {
        return new JdbcTemplateLockProvider(
                JdbcTemplateLockProvider.Configuration.builder()
                        .withJdbcTemplate(new JdbcTemplate(dataSource))
                        .withTableName("SCHEDULER_LOCK")
                        .usingDbTime()
                        .build()
        );
    }

    @Bean
    public TaskDecorator taskDecorator() {
        log.debug("Using task decorator");
        return runnable -> () -> {
            try {
                log.trace("Starting runnable...isVirtual = {}", Thread.currentThread().isVirtual());
                runnable.run();
                if (runnable instanceof FutureTask<?> task) {
                    log.trace("Handling future result...");
                    task.get();
                }
            } catch (InterruptedException e) {
                log.debug("### task interrupted during execution {}", runnable, e);
                Thread.currentThread().interrupt();
            } catch (RuntimeException e) {
                log.error("### Got error on task execution {}", runnable, e);
                throw e;
            } catch (ExecutionException e) {
                log.error("### Got execution exception on task execution {}", runnable, e);
            }
        };
    }

    @SuppressWarnings("resource")
    @Override
    public Executor getAsyncExecutor() {
        var virtualExecutor = new ThreadPoolExecutor(
                asyncCorePoolSize,
                asyncMaxPoolSize,
                0L,
                TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(),
                Thread.ofVirtual().name("Async-erhgo-").factory()
        );

        log.info("⚡ Configured @Async executor with virtual threads (core: {}, max: {})", asyncCorePoolSize, asyncMaxPoolSize);
        return new DelegatingSecurityContextExecutor(runnable -> virtualExecutor.submit(taskDecorator().decorate(runnable)));
    }

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        ScheduledExecutorService virtualExecutor = Executors.newScheduledThreadPool(
                scheduledCorePoolSize,
                Thread.ofVirtual().name("Scheduled-erhgo-").factory()
        );
        log.info("📅 Configured scheduled tasks executor with virtual threads (core: {})", scheduledCorePoolSize);

        taskRegistrar.setTaskScheduler(new ConcurrentTaskScheduler(virtualExecutor));
    }
}
