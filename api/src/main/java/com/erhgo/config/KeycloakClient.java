package com.erhgo.config;

import jakarta.ws.rs.client.ClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.jboss.resteasy.client.jaxrs.ResteasyClientBuilder;
import org.jboss.resteasy.client.jaxrs.engines.ApacheHttpClient43Engine;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.KeycloakBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class KeycloakClient {
    private static final String REALM_MASTER = "master";
    private static final String REALM_CLIENT_ID_ADMIN_CLI = "admin-cli";

    @Bean
    public static Keycloak keycloak(
            @Value("${keycloak.auth-server-url:''}") String authURL,
            @Value("${keycloak-admin.log:''}") String adminLogin,
            @Value("${keycloak-admin.pwd:''}") String adminPassword
    ) {
        var cm = new PoolingHttpClientConnectionManager();
        var httpClient = HttpClients.custom().setConnectionManager(cm).build();
        cm.setMaxTotal(10);
        cm.setDefaultMaxPerRoute(10);
        var engine = new ApacheHttpClient43Engine(httpClient);

        var client = ((ResteasyClientBuilder) ClientBuilder.newBuilder()).httpEngine(engine).build();

        return KeycloakBuilder.builder()
                .serverUrl(authURL)
                .realm(REALM_MASTER)
                .username(adminLogin)
                .password(adminPassword)
                .clientId(REALM_CLIENT_ID_ADMIN_CLI)
                .resteasyClient(client)
                .build();
    }
}
