package com.erhgo.config;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.MDC;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.UUID;

@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class LogFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        try {
            var req = (HttpServletRequest) request;

            MDC.put("referer", req.getHeader("Referer"));
            MDC.put("url", req.getRequestURI());
            MDC.put("requestID", UUID.randomUUID().toString()); // allows to easily find all logs which happened during a specific request's processing
            MDC.put("App-Version", req.getHeader("X-App-Version"));

            chain.doFilter(request, response);
        } finally {
            MDC.remove("referer");
            MDC.remove("url");
            MDC.remove("requestID");
            MDC.remove("App-Version");
        }
    }
}
