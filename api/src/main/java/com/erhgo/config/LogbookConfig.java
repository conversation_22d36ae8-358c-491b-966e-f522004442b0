package com.erhgo.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.zalando.logbook.BodyFilter;
import org.zalando.logbook.HeaderFilter;
import org.zalando.logbook.core.BodyFilters;
import org.zalando.logbook.core.HeaderFilters;

import static java.util.Collections.singleton;
import static org.zalando.logbook.json.JsonBodyFilters.replaceJsonStringProperty;

@Configuration
public class LogbookConfig {
    @Bean
    public BodyFilter bodyFilter() {
        return BodyFilter.merge(BodyFilters.defaultValue(), replaceJsonStringProperty(singleton("password"), "***"));
    }

    @Bean
    public HeaderFilter headerFilter() {
        return HeaderFilter.merge(HeaderFilters.defaultValue(), HeaderFilters.authorization());
    }
}
