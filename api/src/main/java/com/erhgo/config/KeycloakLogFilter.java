package com.erhgo.config;

import com.erhgo.services.SecurityService;
import jakarta.servlet.*;
import org.slf4j.MDC;

import java.io.IOException;

public class KeycloakLogFilter implements Filter {
    private final SecurityService securityService;

    public KeycloakLogFilter(SecurityService securityService) {
        this.securityService = securityService;
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        var keycloakUserId = "keycloakUserId";
        try {
            MDC.put(keycloakUserId, securityService.getKeycloakUserId().orElse("anonymous"));
            chain.doFilter(request, response);
        } finally {
            MDC.remove(keycloakUserId);
        }
    }

}
