package com.erhgo.config;

import lombok.extern.slf4j.Slf4j;
import org.keycloak.representations.adapters.config.AdapterConfig;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;

@ConfigurationProperties(prefix = "keycloak")
@Configuration
// One CustomAdapterConfig instance per realm => scope prototype
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class CustomAdapterConfig extends AdapterConfig {


}
