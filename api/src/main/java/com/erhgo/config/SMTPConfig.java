package com.erhgo.config;

import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.validation.annotation.Validated;

import java.util.HashMap;
import java.util.Map;

@ConfigurationProperties(prefix = "smtp", ignoreUnknownFields = false)
@Profile("!test")
@Configuration
@NoArgsConstructor
@Data
@Validated
public class SMTPConfig {
    @NotBlank
    @Pattern(regexp = "([a-z][a-z\\-]*[a-z]\\.)*[a-z]+")
    private String host;

    @Positive
    private Integer port;

    @NotBlank
    private String user;

    @NotBlank
    private String password;

    private Boolean auth;

    @NotBlank
    @Email
    private String from;

    @NotBlank
    private String fromDisplayName;

    @NotNull
    private Boolean ssl;

    @NotNull
    private Boolean starttls;

    public Map<String, String> toStringMap() {
        var map = new HashMap<String, String>();
        map.put("host", host);
        map.put("port", port.toString());
        map.put("user", user);
        map.put("password", password);
        map.put("auth", auth.toString());
        map.put("from", from);
        map.put("fromDisplayName", fromDisplayName);
        map.put("ssl", ssl.toString());
        map.put("starttls", starttls.toString());
        return map;
    }
}
