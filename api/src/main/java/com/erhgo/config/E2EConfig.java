package com.erhgo.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.data.auditing.DateTimeProvider;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

import java.time.LocalDateTime;
import java.time.temporal.TemporalAccessor;
import java.util.Optional;

@Configuration
@Profile("e2e")
@EnableJpaAuditing(dateTimeProviderRef = "auditingDateTimeProvider")
public class E2EConfig {

    public static class SimonCompliantDateTimeProvider implements DateTimeProvider {
        @Override
        public Optional<TemporalAccessor> getNow() {
            return Optional.of(LocalDateTime.of(2020, 2, 2, 0, 0, 0));
        }
    }

    @Bean
    public DateTimeProvider auditingDateTimeProvider() {
        return new SimonCompliantDateTimeProvider();
    }

}
