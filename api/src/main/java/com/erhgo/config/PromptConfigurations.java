package com.erhgo.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.client.ClientHttpRequestFactories;
import org.springframework.boot.web.client.ClientHttpRequestFactorySettings;
import org.springframework.boot.web.client.RestClientCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.BufferingClientHttpRequestFactory;

import java.time.Duration;

@Configuration
@EnableConfigurationProperties
public class PromptConfigurations {

    @Bean
    @ConfigurationProperties(prefix = "ai-generation.behavior")
    public PromptConfig behaviorPromptConfig() {
        return new PromptConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "ai-generation.rome")
    public PromptConfig romePromptConfig() {
        return new PromptConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "ai-generation.classification")
    public PromptConfig classificationPromptConfig() {
        return new PromptConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "ai-generation.level")
    public PromptConfig levelPromptConfig() {
        return new PromptConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "ai-generation.occupation-description")
    public PromptConfig occupationDescriptionPromptConfig() {
        return new PromptConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "ai-generation.behavior-description")
    public PromptConfig behaviorDescriptionPromptConfig() {
        return new PromptConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "ai-generation.user-behavior-description")
    public PromptConfig userBehaviorDescriptionPromptConfig() {
        return new PromptConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "ai-generation.activity")
    public PromptConfig activityPromptConfig() {
        return new PromptConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "ai-generation.title")
    public PromptConfig titlePromptConfig() {
        return new PromptConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "ai-generation.best-matching-occupation-label")
    public PromptConfig bestMatchingOccupationPromptConfig() {
        return new PromptConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "ai-generation.user-experiences")
    public PromptConfig userExperiencesPromptConfig() {
        return new PromptConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "ai-generation.user-experiences-vision")
    public PromptConfig userExperiencesVisionPromptConfig() {
        return new PromptConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "ai-generation.user-infos")
    public PromptConfig userInfosPromptConfig() { return new PromptConfig(); }

    @Bean
    @ConfigurationProperties(prefix = "ai-generation.user-infos-vision")
    public PromptConfig userInfosVisionPromptConfig() { return new PromptConfig(); }

    @Bean
    @ConfigurationProperties(prefix = "ai-generation.hashtags")
    public PromptConfig hashtagsPromptConfig() {
        return new PromptConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "ai-generation.soft-skills-descriptions")
    public PromptConfig softSkillsDescriptionsPromptConfig() {
        return new PromptConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "ai-generation.hard-skills")
    public PromptConfig hardSkillsPromptConfig() {
        return new PromptConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "ai-generation.hard-skills-vision")
    public PromptConfig hardSkillsVisionPromptConfig() {
        return new PromptConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "ai-generation.format-covea")
    public PromptConfig formatTextToHtmlPromptConfig() {
        return new PromptConfig();
    }

    @Bean
    RestClientCustomizer restClientCustomizer() {
        return restClientBuilder -> {
            restClientBuilder
                    .requestFactory(new BufferingClientHttpRequestFactory(
                            ClientHttpRequestFactories.get(ClientHttpRequestFactorySettings.DEFAULTS
                                    .withConnectTimeout(Duration.ofSeconds(60))
                                    .withReadTimeout(Duration.ofSeconds(120))
                            )));
        };
    }
}
