package com.erhgo.config;

import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

@Data
@Accessors(chain = true)
public class PromptConfig {
    private Double temperature;
    private String model;
    private Integer maxTokens;
    private int maxRetry;
    private String messageFilename;
    private String provider;
    private boolean forceJson = false;

    public String getProvider() {
        return StringUtils.isNotBlank(provider) ? provider : "openai";
    }
}
