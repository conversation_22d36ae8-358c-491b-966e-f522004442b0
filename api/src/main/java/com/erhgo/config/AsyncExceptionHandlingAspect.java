package com.erhgo.config;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

@Slf4j
@Aspect
@Component
public class AsyncExceptionHandlingAspect {

    @Around("execution(java.util.concurrent.CompletableFuture+ *(..))")
    public Object handleAsyncMethod(ProceedingJoinPoint pjp) throws Throwable {
        var result = pjp.proceed();
        if (result instanceof CompletableFuture<?> future) {
            return future.exceptionally(ex -> {
                log.error("Exception in async task", ex);
                return null;
            });
        }
        return result;
    }
}
