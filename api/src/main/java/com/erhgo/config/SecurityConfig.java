package com.erhgo.config;

import com.erhgo.security.ErhgoCompositePermissionEvaluator;
import com.erhgo.security.authentication.RestAuthenticationEntryPoint;
import com.erhgo.services.SecurityService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.convert.converter.Converter;
import org.springframework.http.HttpMethod;
import org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler;
import org.springframework.security.access.expression.method.MethodSecurityExpressionHandler;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationManagerResolver;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.CsrfConfigurer;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoders;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationConverter;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationProvider;
import org.springframework.security.oauth2.server.resource.authentication.JwtIssuerAuthenticationManagerResolver;
import org.springframework.security.oauth2.server.resource.web.authentication.BearerTokenAuthenticationFilter;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.header.writers.ReferrerPolicyHeaderWriter;
import org.springframework.security.web.util.matcher.AnyRequestMatcher;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Configuration
@EnableWebSecurity
@Profile("!test")
@EnableMethodSecurity(securedEnabled = true, jsr250Enabled = true)
@RequiredArgsConstructor
public class SecurityConfig {

    /**
     * constant for AuthorizationInterceptorsOrder.PRE_AUTHORIZE.getOrder();
     */
    public static final int ORDER = 200;

    private final ErhgoCompositePermissionEvaluator customPermissionEvaluator;

    @Value("${spring.profiles.active:default}")
    private String activeProfile;

    @Value("${keycloak.auth-server-url}")
    private String keycloakUrl;

    @Value("${application.cors.allowedOriginPatterns}")
    private List<String> allowedOriginPatterns;

    static {
        SecurityContextHolder.setStrategyName(SecurityContextHolder.MODE_INHERITABLETHREADLOCAL);
    }

    public AuthenticationManager createManager(String issuer) {
        var authenticationProvider = new JwtAuthenticationProvider(JwtDecoders.fromOidcIssuerLocation(issuer));
        authenticationProvider.setJwtAuthenticationConverter(jwtAuthenticationConverterForKeycloak());
        return authenticationProvider::authenticate;
    }

    private JwtAuthenticationConverter jwtAuthenticationConverterForKeycloak() {
        Converter<Jwt, Collection<GrantedAuthority>> jwtGrantedAuthoritiesConverter = jwt -> {
            Map<String, Collection<String>> realmAccess = jwt.getClaim("realm_access");
            Collection<String> roles = realmAccess.get("roles");
            return roles.stream()
                    .map(role -> (GrantedAuthority) new SimpleGrantedAuthority("ROLE_" + role))
                    .toList();
        };

        var jwtAuthenticationConverter = new JwtAuthenticationConverter();
        jwtAuthenticationConverter.setJwtGrantedAuthoritiesConverter(jwtGrantedAuthoritiesConverter);

        return jwtAuthenticationConverter;
    }


    @Bean
    CorsConfigurationSource corsConfigurationSource() {
        var configuration = new CorsConfiguration().setAllowedOriginPatterns(allowedOriginPatterns);
        configuration.setAllowedMethods(Collections.singletonList("*"));
        configuration.setAllowedHeaders(Collections.singletonList("*"));
        configuration.setAllowCredentials(true);
        var source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http, KeycloakLogFilter keycloakLogFilter) throws Exception {
        http
                .cors(c -> c.configurationSource(corsConfigurationSource()))
                .headers(c -> c.referrerPolicy(o -> o.policy(ReferrerPolicyHeaderWriter.ReferrerPolicy.NO_REFERRER)));
        if (shouldHandleHttpsRedirect()) {
            http.headers(h -> h.httpStrictTransportSecurity(hsts ->
                    hsts
                            .maxAgeInSeconds(31536000)
                            .includeSubDomains(true)
                            .preload(false)
                            .requestMatcher(AnyRequestMatcher.INSTANCE))
            );
        }
        http.exceptionHandling(c -> c.authenticationEntryPoint(new RestAuthenticationEntryPoint()))
                .oauth2ResourceServer(oauth2 -> oauth2
                        .authenticationManagerResolver(buildJwtIssuerAuthenticationManagerResolver())
                )
                .csrf(CsrfConfigurer::disable)
                .authorizeHttpRequests(authz -> authz
                        .requestMatchers(
                                HttpMethod.OPTIONS
                        ).permitAll()
                        .requestMatchers("/ssr/public/**").permitAll()
                        .requestMatchers("/actuator/health").permitAll()
                        .requestMatchers("/api/public/**").permitAll() // legacy
                        .requestMatchers("/api/odas/public/**").permitAll()
                        .requestMatchers(HttpMethod.GET,"/api/odas/erhgo-occupation/occupation/search-occupations").permitAll()
                        .requestMatchers(HttpMethod.GET,"/api/odas/sourcing/occupation/**").permitAll()
                        .requestMatchers(HttpMethod.GET,"/api/odas/user/recruitments").permitAll()
                        .requestMatchers("/api/odas/sourcing/contact/**").permitAll()
                        .requestMatchers("/api/odas/**").authenticated()
                        .anyRequest().authenticated()
                ).addFilterAfter(keycloakLogFilter, BearerTokenAuthenticationFilter.class);


        return http.build();
    }

    @Bean
    public KeycloakLogFilter keycloakLogFilter(SecurityService securityService) {
        return new KeycloakLogFilter(securityService);
    }
    /**
     * build lazily AuthenticationManager: in e2e test, it is not possible to initialize AuthenticationManager
     * BEFORE kc init script has run => wait for the first request, on each realm, to initialize according
     * AuthenticationManager
     */
    private JwtIssuerAuthenticationManagerResolver buildJwtIssuerAuthenticationManagerResolver() {
        Map<String, Supplier<AuthenticationManager>> authenticationManagersSupplier = new HashMap<>(Stream.of(
                "%s/realms/bo.erhgo.app".formatted(keycloakUrl),
                "%s/realms/erhgo.app".formatted(keycloakUrl),
                "%s/realms/sourcing.erhgo.app".formatted(keycloakUrl)
        ).collect(Collectors.toMap(Function.identity(), url -> () -> this.createManager(url))));

        var authenticationManagerLazyMap = new ConcurrentHashMap<String, AuthenticationManager>();
        AuthenticationManagerResolver<String> amr = key -> authenticationManagerLazyMap.computeIfAbsent(key, k -> {
            var managerSupplier = authenticationManagersSupplier.get(k);
            if (managerSupplier == null) {
                return null;
            }
            return managerSupplier.get();
        });

        return new JwtIssuerAuthenticationManagerResolver(amr);
    }

    boolean shouldHandleHttpsRedirect() {
        return activeProfile.equals("master") || activeProfile.equals("staging") || activeProfile.equals("testing");
    }


    @Bean
    public MethodSecurityExpressionHandler expressionHandler() {
        var expressionHandler =
                new DefaultMethodSecurityExpressionHandler();
        expressionHandler.setPermissionEvaluator(customPermissionEvaluator);
        return expressionHandler;
    }
}
