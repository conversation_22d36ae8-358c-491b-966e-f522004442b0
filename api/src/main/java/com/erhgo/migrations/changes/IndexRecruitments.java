package com.erhgo.migrations.changes;

import com.erhgo.migrations.AbstractMigrationService;
import com.erhgo.services.SecurityService;
import com.erhgo.services.search.recruitment.RecruitmentIndexer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class IndexRecruitments extends AbstractMigrationService {

    @Autowired
    private RecruitmentIndexer recruitmentIndexer;

    @Autowired
    private SecurityService securityService;

    @Override
    protected void executeUpdate() {
        log.info("All recruitments indexation starts...");
        securityService.doAsAdmin(recruitmentIndexer::indexAll);
        log.info("All recruitments indexation launched");
    }
}
