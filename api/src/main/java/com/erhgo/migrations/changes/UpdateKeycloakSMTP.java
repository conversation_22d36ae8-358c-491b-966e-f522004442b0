package com.erhgo.migrations.changes;

import com.erhgo.config.KeycloakRealmsConfig;
import com.erhgo.config.SMTPConfig;
import com.erhgo.migrations.AbstractMigrationService;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.admin.client.Keycloak;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UpdateKeycloakSMTP extends AbstractMigrationService {

    @Autowired
    private Keycloak keycloak;

    @Autowired
    private KeycloakRealmsConfig config;

    @Autowired
    private SMTPConfig smtpConfig;

    @Override
    public void executeUpdate() {
        log.info("About to migrate smtp...");
        updateSMTP(config.getFrontOfficeRealmId());
        updateSMTP(config.getBackOfficeRealmId());
        log.info("Keycloak SMTP Updated");
    }

    private void updateSMTP(String realmId) {
        var realmResource = keycloak.realm(realmId);
        var realmRepresentation = realmResource.toRepresentation();
        realmRepresentation.setSmtpServer(smtpConfig.toStringMap());
        realmResource.update(realmRepresentation);
    }

}
