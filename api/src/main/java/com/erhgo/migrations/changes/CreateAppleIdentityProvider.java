package com.erhgo.migrations.changes;

import com.erhgo.config.KeycloakRealmsConfig;
import com.erhgo.migrations.AbstractMigrationService;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.representations.idm.IdentityProviderRepresentation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CreateAppleIdentityProvider extends AbstractMigrationService {
    @Autowired
    private Keycloak keycloak;
    @Autowired
    private KeycloakRealmsConfig config;

    @Override
    protected void executeUpdate() {
        log.info("Update keycloak configuration to add apple identity provider...");
        var realmId = config.getFrontOfficeRealmId();
        var realm = keycloak.realm(realmId);

        var identityProvider = buildFrontOfficeSocialProvider();

        try (var response = realm.identityProviders().create(identityProvider)) {
            log.info("Apple identity provider added");
        }
    }

    private IdentityProviderRepresentation buildFrontOfficeSocialProvider() {
        var appleProvider = new IdentityProviderRepresentation();
        appleProvider.setProviderId("apple");
        appleProvider.setAlias("apple");
        appleProvider.setEnabled(true);

        appleProvider.getConfig().put("clientId", config.getAppleClientId());
        // mandatory dummy: https://github.com/klausbetz/apple-identity-provider-keycloak?tab=readme-ov-file#configuration
        appleProvider.getConfig().put("clientSecret", "dummy");
        appleProvider.getConfig().put("teamId", config.getAppleTeamId());
        appleProvider.getConfig().put("keyId", config.getAppleKeyId());
        appleProvider.getConfig().put("p8Content", config.getAppleP8Key());

        return appleProvider;
    }
}
