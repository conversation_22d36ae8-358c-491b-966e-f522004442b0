package com.erhgo.migrations.changes;

import com.erhgo.domain.userprofile.MailConfiguration;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.migrations.AbstractMigrationService;
import com.erhgo.repositories.UserProfileRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.HashSet;
import java.util.Optional;

@Component
@Slf4j
public class CleanupSenderOptOut extends AbstractMigrationService {

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Transactional
    @Override
    public void executeUpdate() {
        log.info("Update transactional blacklisted list");

        transactionTemplate.execute(useless -> {
            userProfileRepository.findAll().forEach(CleanupSenderOptOut::cleanSendersOptOut);
            return null;
        });
        log.info("Success transactional blacklisted list update");
    }

    private static void cleanSendersOptOut(UserProfile up) {
        up.sendersOptOut(up.getSendersOptOut());
        if (!Optional.ofNullable(up.mailConfiguration()).map(MailConfiguration::getJobOfferOptOut).orElse(false)) {
            var optedOut = new HashSet<>(up.getSendersOptOut());
            optedOut.remove("<EMAIL>");
            up.sendersOptOut(optedOut);
        }
    }
}
