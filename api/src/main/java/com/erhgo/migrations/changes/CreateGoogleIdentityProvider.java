package com.erhgo.migrations.changes;

import com.erhgo.config.KeycloakRealmsConfig;
import com.erhgo.migrations.AbstractMigrationService;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.representations.idm.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CreateGoogleIdentityProvider extends AbstractMigrationService {
    @Autowired
    private Keycloak keycloak;
    @Autowired
    private KeycloakRealmsConfig config;

    @Override
    protected void executeUpdate() {
        log.info("Update keycloak configuration to add google identity provider...");
        var realmId = config.getFrontOfficeRealmId();
        var realm = keycloak.realm(realmId);

        var identityProvider = buildFrontOfficeSocialProvider();

        try (var response = realm.identityProviders().create(identityProvider)) {
            log.info("Google identity provider added");
        }
    }

    private IdentityProviderRepresentation buildFrontOfficeSocialProvider() {
        var googleProvider = new IdentityProviderRepresentation();
        googleProvider.setProviderId("google");
        googleProvider.setAlias("google");
        googleProvider.setEnabled(true);

        googleProvider.getConfig().put("clientId", config.getGoogleClientId());
        googleProvider.getConfig().put("clientSecret", config.getGoogleClientSecret());

        return googleProvider;
    }
}
