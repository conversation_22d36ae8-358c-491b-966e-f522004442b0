package com.erhgo.migrations.changes;

import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.services.search.ErhgoOccupationIndexer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Service
@Slf4j
public class AdjustErhgoOccupationTitleService {

    private final ErhgoOccupationRepository erhgoOccupationRepository;
    private final ErhgoOccupationIndexer erhgoOccupationIndexer;

    @Async
    @Transactional
    public void executeUpdate() {
        log.info("Update ERHGO Occupation title starts...");
        erhgoOccupationRepository
                .findAll()
                .stream()
                .filter(ErhgoOccupation::fixTitles)
                .forEach(erhgoOccupationIndexer::updateOccupationIndexation);
        log.info("Success ERHGO Occupation title update");
    }
}
