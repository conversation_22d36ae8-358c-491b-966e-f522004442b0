package com.erhgo.migrations.changes;

import com.erhgo.config.KeycloakRealmsConfig;
import com.erhgo.migrations.AbstractMigrationService;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.admin.client.Keycloak;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class UpdateKeycloakDomainsAndTypeService extends AbstractMigrationService {

    @Autowired
    private Keycloak keycloak;
    @Autowired
    private KeycloakRealmsConfig config;

    @Override
    public void executeUpdate() {
        log.info("About to migrate domains...");
        updateClient(config.getFrontOfficeRealmId(), config.getFrontOfficeClientId(), config.getFrontOfficeRedirects(), config.getFrontOfficeBaseURL());
        updateClient(config.getBackOfficeRealmId(), config.getBackOfficeClientId(), config.getBackOfficeRedirects(), config.getBackOfficeBaseURL());
    }

    private void updateClient(String realmId, String client, List<String> redirects, String baseUrl) {
        var realmResource = keycloak.realm(realmId);
        var id = realmResource.clients().findByClientId(client).get(0).getId();
        var clientResource = realmResource.clients().get(id);
        var updatedClient = clientResource.toRepresentation();
        updatedClient.setPublicClient(true);
        updatedClient.setRedirectUris(redirects.stream().map(o -> o.endsWith("*") ? o : (o + "/*")).toList());
        updatedClient.setBaseUrl(baseUrl);
        updatedClient.setWebOrigins(redirects);
        clientResource.update(updatedClient);
        log.info("Client {} ({}) Updated", client, id);
    }

}
