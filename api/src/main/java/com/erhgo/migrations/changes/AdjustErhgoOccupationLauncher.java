package com.erhgo.migrations.changes;

import com.erhgo.migrations.AbstractMigrationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class AdjustErhgoOccupationLauncher extends AbstractMigrationService {

    @Autowired
    private AdjustErhgoOccupationTitleService adjustErhgoOccupationTitleService;

    @Override
    protected void executeUpdate() {
        log.info("Launch ERHGO Occupation title sanitizing...");
        adjustErhgoOccupationTitleService.executeUpdate();
        log.info("Success ERHGO Occupation title sanitizing launched");
    }
}
