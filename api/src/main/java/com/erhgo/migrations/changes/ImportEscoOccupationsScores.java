package com.erhgo.migrations.changes;

import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.migrations.AbstractMigrationService;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.repositories.classifications.EscoOccupationRepository;
import com.google.common.annotations.VisibleForTesting;
import com.opencsv.CSVReader;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@Service
@Slf4j
@NoArgsConstructor
public class ImportEscoOccupationsScores extends AbstractMigrationService {

    @Autowired
    private EscoOccupationRepository escoOccupationRepository;
    @Autowired
    private ErhgoOccupationRepository erhgoOccupationRepository;
    @Value("classpath:migration/esco_scores.csv")
    private Resource csvFile;
    @Autowired
    private TransactionTemplate transactionTemplate;

    @VisibleForTesting
    public ImportEscoOccupationsScores(
            EscoOccupationRepository escoOccupationRepository,
            ErhgoOccupationRepository erhgoOccupationRepository,
            TransactionTemplate transactionTemplate,
            Resource csvFile
    ) {
        this.erhgoOccupationRepository = erhgoOccupationRepository;
        this.transactionTemplate = transactionTemplate;
        this.escoOccupationRepository = escoOccupationRepository;
        this.csvFile = csvFile;
    }

    @SuppressWarnings("java:S1874")
    public int doImport(InputStream csvFile) {
        log.info("ESCO Score update starts...");
        var input = new InputStreamReader(csvFile);
        var csvReader = new CSVReader(input);

        var dataMap = StreamSupport.stream(csvReader.spliterator(), false)
                .map(line -> Map.entry(line[1].trim(), line[4].trim())) // we extract the two columns we need to create a map uri => score
                .filter(entry -> Strings.isNotBlank(entry.getKey()) && Strings.isNotBlank(entry.getValue())) // we filter out blank values
                .map(entry -> {
                    try {
                        var score = Integer.parseInt(entry.getValue());
                        return Map.entry(entry.getKey(), score);
                    } catch (NumberFormatException e) {
                        log.error("failed to parse ESCO score for URI '" + entry.getKey() + "' (score: '" + entry.getValue() + "')", e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        var uris = dataMap.keySet();
        var occupations = escoOccupationRepository.findAllById(uris).stream()
                .filter(occupation -> {
                    var newScore = dataMap.get(occupation.getUri());
                    return occupation.getLevel() == null || !newScore.equals(occupation.getLevel().getMasteryLevel()); // we keep only the occupations with an updated level
                })
                .toList();

        occupations.forEach(occupation -> occupation.setLevel(MasteryLevel.values()[dataMap.get(occupation.getUri()) - 1]));

        var updatedCount = escoOccupationRepository.saveAll(occupations).size();
        transactionTemplate.execute(doNow -> {
            var erhgo = erhgoOccupationRepository.findAll();
            erhgo.forEach(o -> o.updateLevel(o.getEscoOccupations().stream().findFirst().map(a -> dataMap.get(a.getUri())).map(MasteryLevel::forLevel).orElse(MasteryLevel.MIN_LEVEL)));
            return null;
        });

        log.info("Updated " + updatedCount + " ESCO Scores");

        return updatedCount;
    }

    @Override
    public void executeUpdate() {
        try {
            log.info("Performing import ESCO mastery level from CSV...");
            doImport(this.csvFile.getInputStream());
        } catch (IOException e) {
            throw new IllegalArgumentException("CSV file not found");
        }
    }
}
