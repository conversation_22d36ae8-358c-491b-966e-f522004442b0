package com.erhgo.migrations.changes;

import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.migrations.AbstractMigrationService;
import com.erhgo.repositories.UserProfileRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

@Service
@Slf4j
public class MasteryLevelUpdater extends AbstractMigrationService {

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Override
    protected void executeUpdate() {
        log.info("Updating mastery level of all users...");
        transactionTemplate.execute(unused -> {
            userProfileRepository.findAll().forEach(UserProfile::refreshMasteryLevelSilently);
            return null;
        });
        log.info("All user mastery levels have been updated...");
    }

}
