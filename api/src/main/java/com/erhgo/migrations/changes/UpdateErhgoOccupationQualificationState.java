package com.erhgo.migrations.changes;

import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.migrations.AbstractMigrationService;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

@Component
@Slf4j
public class UpdateErhgoOccupationQualificationState extends AbstractMigrationService {

    @Autowired
    private ErhgoOccupationRepository erhgoOccupationRepository;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Override
    protected void executeUpdate() {
        log.info("ERHGO occupation qualification state update start...");
        transactionTemplate.execute(useless -> {
            erhgoOccupationRepository.findAll().forEach(ErhgoOccupation::computeQualificationState);
            return null;
        });
        log.info("Success update ERHGO occupation qualification state.");
    }
}
