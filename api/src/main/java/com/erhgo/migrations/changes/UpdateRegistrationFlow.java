package com.erhgo.migrations.changes;

import com.erhgo.config.KeycloakRealmsConfig;
import com.erhgo.migrations.AbstractMigrationService;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.RealmResource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class UpdateRegistrationFlow extends AbstractMigrationService {

    @Autowired
    private Keycloak keycloak;

    @Value("${keycloak-realms.front_office_realm_id:''}")
    private String frontOfficeRealm;

    @Value("${keycloak-realms.back_office_realm_id:''}")
    private String backOfficeRealm;

    private static final String REGISTRATION_FLOW_ALIAS = "registration";

    private static final String SET_PASSWORD_ACTION_PROVIDER_ID = "registration-password-action";
    private static final String UPDATE_PROFILE_ACTION_PROVIDER_ID = "registration-profile-action";

    @Autowired
    private KeycloakRealmsConfig config;

    @Override
    protected void executeUpdate() {
        log.info("Update authentication flow starts...");
        var frontOfficeRealmResource = keycloak.realm(frontOfficeRealm);
        enablePasswordAction(frontOfficeRealmResource);
        enableProfileAction(frontOfficeRealmResource);
        updateRealm(frontOfficeRealmResource);
        var backOfficeRealmResource = keycloak.realm(backOfficeRealm);
        disableRequiredSpecialCharForBOPasswordPolicy(backOfficeRealmResource);
        log.info("Success update authentication flow");
    }

    private void disableRequiredSpecialCharForBOPasswordPolicy(RealmResource backOfficeRealmResource) {
        var updatedRealm = backOfficeRealmResource.toRepresentation();
        updatedRealm.setPasswordPolicy(config.getPasswordPolicy());
        backOfficeRealmResource.update(updatedRealm);
    }

    private void updateRealm(RealmResource frontOfficeRealmResource) {
        var updatedRealm = frontOfficeRealmResource.toRepresentation();
        updatedRealm.setPasswordPolicy(config.getPasswordPolicy());
        updatedRealm.setRegistrationFlow(REGISTRATION_FLOW_ALIAS);
        frontOfficeRealmResource.update(updatedRealm);
    }

    private void enablePasswordAction(RealmResource frontOfficeRealmResource) {
        frontOfficeRealmResource.flows().getExecutions(REGISTRATION_FLOW_ALIAS).stream()
                .filter(f -> SET_PASSWORD_ACTION_PROVIDER_ID.equals(f.getProviderId()))
                .forEach(e -> {
                    e.setRequirement("REQUIRED");
                    frontOfficeRealmResource.flows().updateExecutions(REGISTRATION_FLOW_ALIAS, e);
                });
    }

    private void enableProfileAction(RealmResource frontOfficeRealmResource) {
        frontOfficeRealmResource.flows().getExecutions(REGISTRATION_FLOW_ALIAS).stream()
                .filter(f -> UPDATE_PROFILE_ACTION_PROVIDER_ID.equals(f.getProviderId()))
                .forEach(e -> {
                    e.setRequirement("REQUIRED");
                    frontOfficeRealmResource.flows().updateExecutions(REGISTRATION_FLOW_ALIAS, e);
                });
    }
}
