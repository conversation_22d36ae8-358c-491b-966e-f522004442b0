package com.erhgo.migrations.changes;

import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.enums.BehaviorCategory;
import com.erhgo.migrations.AbstractMigrationService;
import com.google.common.annotations.VisibleForTesting;
import com.opencsv.CSVReaderBuilder;
import jakarta.persistence.EntityManager;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Service
@Slf4j
@NoArgsConstructor
public class ImportErhgoOccupationsCategories extends AbstractMigrationService {

    @Autowired
    private EntityManager entityManager;

    @Value("classpath:migration/erhgo_categories.csv")
    private Resource csvFile;

    @Autowired
    private TransactionTemplate transactionTemplate;

    protected Set<String> notModifiedJobs = new HashSet<>();
    protected Set<String> category1UpdatedJobs = new HashSet<>();
    protected Set<String> category2UpdatedJobs = new HashSet<>();
    protected Map<String, String> erroneousJobs = new HashMap<>();

    @VisibleForTesting
    public ImportErhgoOccupationsCategories(
            EntityManager entityManager,
            TransactionTemplate transactionTemplate,
            Resource csvFile
    ) {
        this.entityManager = entityManager;
        this.transactionTemplate = transactionTemplate;
        this.csvFile = csvFile;
    }

    public void doImport(InputStream csvFile) {
        log.info("ERHGO categories update starts...");
        var input = new InputStreamReader(csvFile);
        var csvReader = new CSVReaderBuilder(input).withSkipLines(1).build();
        csvReader.forEach(this::updateCategories);

        log.info("ERHGO categories updated");
        if (!erroneousJobs.isEmpty()) {
            log.error("{} jobs in error:", erroneousJobs.size());
            erroneousJobs.forEach((k, v) -> log.warn("- Job: {} - reason: {}", k, v));
        }
        log.info("{} not modified jobs ({})", notModifiedJobs.size(), String.join(", ", notModifiedJobs));
        log.info("{} jobs with first category updated ({})", category1UpdatedJobs.size(), String.join(", ", category1UpdatedJobs));
        log.info("{} jobs with second category updated ({})", category2UpdatedJobs.size(), String.join(", ", category2UpdatedJobs));
    }

    private void updateCategories(String[] strings) {
        var uri = strings[0];
        transactionTemplate.execute(cb -> {
            var erhgoOccupations = entityManager.createQuery(
                    "SELECT DISTINCT e " +
                            "FROM ErhgoOccupation e " +
                            "INNER JOIN e.escoOccupations es " +
                            "WHERE es.uri = :uri", ErhgoOccupation.class)
                    .setParameter("uri", uri)
                    .getResultList();
            if (erhgoOccupations.isEmpty()) {
                erroneousJobs.put(uri, "No job found for uri " + uri);
            }
            erhgoOccupations.forEach(e -> this.updateCategories(e, strings[1], strings[2]));
            return null;
        });

    }

    private void updateCategories(ErhgoOccupation erhgoOccupation, String labelForCat1, String labelForCat2) {
        var behaviorCategory1 = decodeCategory(labelForCat1);
        var behaviorCategory2 = decodeCategory(labelForCat2);
        if (behaviorCategory1 == null || behaviorCategory2 == null) {
            erroneousJobs.put(erhgoOccupation.getId().toString(), "Category not found for labels " +
                    (behaviorCategory1 == null ? "'" + labelForCat1 + "'" : "") + "--" +
                    (behaviorCategory2 == null ? "'" + labelForCat2 + "'" : "")
            );
            return;
        }
        if (behaviorCategory1 == behaviorCategory2) {
            erroneousJobs.put(erhgoOccupation.getId().toString(), "Same category used for category 1 and category 2: " + behaviorCategory1);
            return;
        }
        if (erhgoOccupation.getBehaviorCategory1() == behaviorCategory1 && erhgoOccupation.getBehaviorCategory2() == behaviorCategory2) {
            notModifiedJobs.add(erhgoOccupation.getId().toString());
        }
        if (erhgoOccupation.getBehaviorCategory1() != behaviorCategory1) {
            erhgoOccupation.setBehaviorCategory1(behaviorCategory1);
            erhgoOccupation.setBehaviorCategory1Overloaded(true);
            category1UpdatedJobs.add(erhgoOccupation.getId().toString());
        }
        if (erhgoOccupation.getBehaviorCategory2() != behaviorCategory2) {
            erhgoOccupation.setBehaviorCategory2(behaviorCategory2);
            erhgoOccupation.setBehaviorCategory2Overloaded(true);
            category2UpdatedJobs.add(erhgoOccupation.getId().toString());
        }
    }

    private BehaviorCategory decodeCategory(String label) {
        switch (label) {
            case "Constance":
                return BehaviorCategory.CONSTANCY;
            case "Honnêteté":
                return BehaviorCategory.HONESTY;
            case "Ténacité":
                return BehaviorCategory.TENACITY;
            case "Sociabilité":
                return BehaviorCategory.SOCIABILITY;
            case "Pragmatisme":
                return BehaviorCategory.PRAGMATISM;
            case "Rigeur":
                return BehaviorCategory.RIGOR;
            default:
                return null;
        }
    }

    @Override
    public void executeUpdate() {
        try {
            doImport(this.csvFile.getInputStream());
        } catch (IOException e) {
            throw new IllegalArgumentException("CSV file not found");
        }
    }
}
