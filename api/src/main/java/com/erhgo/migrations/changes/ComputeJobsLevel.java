package com.erhgo.migrations.changes;

import com.erhgo.domain.job.Job;
import com.erhgo.migrations.AbstractMigrationService;
import com.erhgo.repositories.JobRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

@Component
@Slf4j
public class ComputeJobsLevel extends AbstractMigrationService {

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Override
    protected void executeUpdate() {
        log.info("Jobs level denormalization refresh start...");
        transactionTemplate.execute(useless -> {
            jobRepository.findAll().forEach(Job::refreshLevel);
           return null;
        });
        log.info("Jobs level update finished.");
    }
}
