package com.erhgo.migrations.changes;

import com.algolia.search.DefaultSearchClient;
import com.algolia.search.exceptions.AlgoliaRuntimeException;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.migrations.AbstractMigrationService;
import com.erhgo.services.search.Algolia;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;


@Component
@Slf4j
public class RemoveActivitiesFromAlgolia extends AbstractMigrationService {
    // Wait for Algolia initialization to ensure replicas indexes are not linked to master activity index anymore
    // see https://www.algolia.com/doc/api-reference/api-parameters/replicas/ > removing replicas
    @Autowired(required = false)
    private Algolia algolia;

    private final List<String> activityIndexes = Lists.newArrayList("_activity_index");

    @Value("${algolia.applicationId:''}")
    private String applicationId;
    @Value("${algolia.adminApiKey:''}")
    private String adminApiKey;
    @Value("${algolia.indexPrefix:''}")
    private String indexPrefix;

    @Override
    protected void executeUpdate() {
        if (algolia != null) {
            log.info("About to remove activity indexes from Algolia...");
            try (var client = DefaultSearchClient.create(applicationId, adminApiKey)) {
                activityIndexes.stream()
                        .map(indexName -> indexPrefix + indexName)
                        .forEach(i -> {
                            try {
                                client.initIndex(i).delete();
                                log.info("Index {} removed", i);
                            } catch (AlgoliaRuntimeException e) {
                                log.error("Unable to remove index {}", i, e);
                            }
                        });
            } catch (IOException e) {
                log.error("Failed to initialize Algolia client", e);
                throw new GenericTechnicalException("remove activities from Algolia", e);
            }
            log.info("Activity indexes removal from Algolia launched");
        } else {
            log.info("No algolia conf - Ignore Activity indexes cleanup");
        }
    }
}
