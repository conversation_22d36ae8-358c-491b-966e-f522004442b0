package com.erhgo.migrations.changes;

import com.erhgo.migrations.AbstractMigrationService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.RealmResource;
import org.keycloak.representations.userprofile.config.UPAttribute;
import org.keycloak.representations.userprofile.config.UPAttributePermissions;
import org.keycloak.representations.userprofile.config.UPConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.stream.Stream;

@Component
@Slf4j
public class UpgradeKeycloak26 extends AbstractMigrationService {

    public static final UPAttributePermissions PERMISSIONS_ALLOW_ALL = new UPAttributePermissions(Set.of("user", "admin"), Set.of("user", "admin"));
    @Autowired
    private Keycloak keycloak;

    @Value("${keycloak-realms.front_office_realm_id:''}")
    private String frontOfficeRealm;

    @Value("${keycloak-realms.sourcing_realm_id:''}")
    private String sourcingRealm;

    @Override
    protected void executeUpdate() {
        log.info("Upgrade Keycloak 26 starts...");
        var foRealmResource = keycloak.realm(frontOfficeRealm);
        var foConfig = getUserProfileConfigAndMarkNotRequired(foRealmResource);
        foRealmResource.users().userProfile().update(foConfig);
        log.debug("FO realm - configured");
        var sourcingRealmResource = keycloak.realm(sourcingRealm);
        var sourcingConfig = getUserProfileConfigAndMarkNotRequired(sourcingRealmResource);
        Stream.of("fullname", "siret", "lastLoginTimestamp", "phone").forEach(a -> sourcingConfig.addOrReplaceAttribute(getAttribute(a)));
        sourcingRealmResource.users().userProfile().update(sourcingConfig);
        log.debug("Sourcing realm - configured");
        var foRealmConf = foRealmResource.toRepresentation();
        foRealmConf.setLoginTheme("jenesuispasuncv_keycloakify");
        foRealmResource.update(foRealmConf);
        log.debug("FO realm - theme updated");
        log.info("Success upgrade Keycloak 26");
    }

    private static @NotNull UPAttribute getAttribute(String a) {
        var attribute = new UPAttribute(a, PERMISSIONS_ALLOW_ALL);
        attribute.setDisplayName("${%s}".formatted(a));
        return attribute;
    }

    private static @NotNull UPConfig getUserProfileConfigAndMarkNotRequired(RealmResource realmResource) {
        var config = realmResource.users().userProfile().getConfiguration();
        Stream.of("firstName", "lastName").forEach(a -> {
            config.getAttribute(a).setPermissions(PERMISSIONS_ALLOW_ALL);
            config.getAttribute(a).setRequired(null);
        });
        return config;
    }

}
