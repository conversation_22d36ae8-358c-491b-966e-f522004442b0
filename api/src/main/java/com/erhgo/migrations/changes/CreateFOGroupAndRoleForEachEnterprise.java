package com.erhgo.migrations.changes;

import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.migrations.AbstractMigrationService;
import com.erhgo.repositories.RecruiterRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.keycloak.KeycloakService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.stream.StreamSupport;

@Component
@Slf4j
public class CreateFOGroupAndRoleForEachEnterprise extends AbstractMigrationService {

    @Autowired
    private KeycloakService keycloakService;

    @Autowired
    private SecurityService securityService;

    @Autowired
    private RecruiterRepository recruiterRepository;

    @Override
    public void executeUpdate() {
        log.info("About to create FO Role and Group for organizations that doesn't have one already");
        securityService.doAsAdmin(() -> StreamSupport.stream(recruiterRepository.findAll().spliterator(), false)
                .filter(o -> o.getOrganizationType() == AbstractOrganization.OrganizationType.ENTERPRISE)
                .map(Recruiter::getCode)
                .forEach(keycloakService::createFrontOfficeGroupAndRole));
        log.info("FO Role and Group creation finished");
    }
}
