package com.erhgo.migrations.changes;

import com.erhgo.migrations.AbstractMigrationService;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.admin.client.Keycloak;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class EnableEditUsername extends AbstractMigrationService {

    @Autowired
    private Keycloak keycloak;

    @Value("${keycloak-realms.front_office_realm_id:''}")
    private String frontOfficeRealm;

    @Override
    protected void executeUpdate() {
        log.info("Enabling username edition in keycloak...");
        var frontOfficeRealmResource = keycloak.realm(frontOfficeRealm);
        var updatedRealm = frontOfficeRealmResource.toRepresentation();
        updatedRealm.setEditUsernameAllowed(true);
        frontOfficeRealmResource.update(updatedRealm);
        log.info("Success username edition enabling");
    }
}
