package com.erhgo.migrations;

import liquibase.Liquibase;
import liquibase.database.DatabaseFactory;
import liquibase.database.jvm.JdbcConnection;
import liquibase.resource.ClassLoaderResourceAccessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Profile;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;

@Slf4j
@Service
@Order(Ordered.HIGHEST_PRECEDENCE)
@Profile("!test")
public class MigrationServiceExecutor implements CommandLineRunner {
    @Autowired
    private Environment environment;

    @Autowired
    private DataSource dataSource;

    public MigrationServiceExecutor(@Autowired ApplicationContext applicationContext, @Autowired DataSource dataSource) {
        AbstractMigrationService.applicationContext = applicationContext;
        this.dataSource = dataSource;
    }

    @Override
    public void run(String... args) throws Exception {
        var database = DatabaseFactory.getInstance().findCorrectDatabaseImplementation(new JdbcConnection(dataSource.getConnection()));
        try (var liquibase = new Liquibase("db/changelog/changelog-services.xml", new ClassLoaderResourceAccessor(getClass().getClassLoader()), database)) {
            liquibase.update(String.join(",", environment.getActiveProfiles().length == 0 ? environment.getDefaultProfiles() : environment.getActiveProfiles()));
        }
    }
}
