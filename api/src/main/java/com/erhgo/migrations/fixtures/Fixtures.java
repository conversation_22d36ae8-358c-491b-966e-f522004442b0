package com.erhgo.migrations.fixtures;

import com.erhgo.migrations.AbstractMigrationService;
import com.erhgo.services.SecurityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class Fixtures extends AbstractMigrationService {
    @Autowired
    private DomainFixtures domainFixtures;
    @Autowired
    private KeycloakFixtures keycloakFixtures;
    @Autowired
    private SecurityService securityService;

    @Override
    protected void executeUpdate() {
        securityService.doAsAdmin(() -> {
            keycloakFixtures.createKeycloakFixtures();
            domainFixtures.createDomainFixtures();
        });
    }
}
