package com.erhgo.migrations.fixtures;

import com.erhgo.config.KeycloakRealmsConfig;
import com.erhgo.config.SMTPConfig;
import com.erhgo.security.Role;
import com.google.common.collect.Lists;
import jakarta.ws.rs.ClientErrorException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHeaders;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.UsersResource;
import org.keycloak.representations.idm.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.nio.file.Paths;
import java.util.*;
import java.util.stream.IntStream;

import static com.google.common.collect.Lists.newArrayList;

@Profile({"default", "e2e"})
@Service
@Slf4j
public class KeycloakFixtures {

    private static final String FO_THEME_ID = "erhgo";
    private static final String BO_THEME_ID = "odas";
    public static final String ODAS_GROUP = "ODAS";
    public static final String CANDIDATES_GROUP = Role.CANDIDATES_GROUP;
    private static final String CANDIDATE_USERNAME = "candidate";
    private static final String READY_CANDIDATE_USERNAME = "ready";

    private final Keycloak keycloak;

    private final KeycloakRealmsConfig config;
    private final SMTPConfig smtpConfig;

    private final List<String> usersList = new ArrayList<>();

    public KeycloakFixtures(
            @Autowired Keycloak keycloak,
            @Autowired KeycloakRealmsConfig config,
            @Autowired SMTPConfig smtpConfig
    ) {
        this.keycloak = keycloak;
        this.config = config;
        this.smtpConfig = smtpConfig;
    }

    void createKeycloakFixtures() {
        log.info("keycloak fixtures creation starts");
        final var usersResource = this.keycloak.realm(config.getFrontOfficeRealmId()).users();
        if (shouldProceed()) {
            log.info("Creating keycloak fixtures");
            createFrontOfficeRealm();
            usersList.add(createUser(usersResource, "candidate", "Tester", "candidate@localhost"));
            usersList.add(createUser(usersResource, "ready", "Tester", "ready@localhost"));
            IntStream.range(0, 10).forEach(i -> createUser(usersResource, "candidate" + i, "front" + i, "candidate" + i + "@localhost"));
            createBackOfficeRealm();
            createBackOfficeUser();
            log.info("keycloak fixtures ended");
        } else {
            log.info("Ignoring fixtures creation");
            usersList.add(usersResource.search(CANDIDATE_USERNAME).get(0).getId());
            usersList.add(usersResource.search(READY_CANDIDATE_USERNAME).get(0).getId());
        }
    }

    private boolean shouldProceed() {
        try {
            keycloak.realm(config.getFrontOfficeRealmId()).getDefaultGroups();
            // Previous call did not fail, which means the realm already exists
            log.info("Realm for front office already exist, do not create realms & keycloak related datas");
            return false;
        } catch (ClientErrorException e) {
            if (e.getResponse().getStatus() != 404) {
                throw e;
            }
        }
        return true;
    }

    private void createBackOfficeUser() {
        log.info("Creating BO admin");
        var realmResource = this.keycloak.realm(config.getBackOfficeRealmId());
        var usersResource = realmResource.users();

        var adminGroupId = realmResource.getGroupByPath("/" + ODAS_GROUP).getId();
        log.info("Admin group id is {}", adminGroupId);
        var adminUserId = createUser(usersResource, "Admin", "Tester", "<EMAIL>");
        log.info("Admin user id is {}", adminGroupId);

        usersResource.get(adminUserId).joinGroup(adminGroupId);
        log.info("Admin joined admin group");

    }

    private void createBackOfficeRealm() {
        log.info("Creating BO Realm");
        var realm = new RealmRepresentation();
        realm.setId(config.getBackOfficeRealmId());
        realm.setRealm(config.getBackOfficeRealmId());
        realm.setPasswordPolicy(config.getPasswordPolicy());

        var rolesToGroup = new HashMap<>(Map.of(
                Role.ODAS_ADMIN, ODAS_GROUP
        ));

        var roles = buildRoles(rolesToGroup.keySet());
        realm.setRoles(roles);

        realm.setGroups(buildGroups(rolesToGroup));

        var webClient = buildBackOfficeWebClient();
        var apiClient = buildBackOfficeAPIClient();

        realm.setClients(newArrayList(webClient, apiClient));

        realm.setSmtpServer(smtpConfig.toStringMap());

        setCommonRealmSettings(realm, BO_THEME_ID);
        realm.setRegistrationAllowed(false);

        this.keycloak.realms().create(realm);
        disableOTP(config.getBackOfficeRealmId());
        log.info("BO Realm creation ended");
    }

    private String createUser(UsersResource realm, String firstName, String lastName, String email) {
        final var user = new UserRepresentation();
        user.setEmail(email);
        user.setFirstName(firstName);
        user.setUsername(email);
        user.setEnabled(true);
        user.setTotp(false);
        user.setLastName(lastName);
        var credentials = new CredentialRepresentation();
        credentials.setTemporary(false);
        credentials.setType(CredentialRepresentation.PASSWORD);
        credentials.setSecretData("{" +
                "\"value\": \"ZcC45qA9JiFQYuJau1w/pxfBmZusDDoCO0Sx4z+uFWdE4CA1FUibOqpqHh21GwRKcdRSseqrBBaaPbBznIWYRQ==\", " +
                "\"salt\": \"sTMbn/fDpC/KX549o9XoHg==\"" +
                "}");
        credentials.setCredentialData("{\"hashIterations\": 27500, \"algorithm\": \"pbkdf2-sha256\"}");
        user.setCredentials(Lists.newArrayList(credentials));

        // Create user
        try (var response = realm.create(user)) {
            return Paths.get(response.getHeaders().get(HttpHeaders.LOCATION).get(0).toString()).getFileName().toString();
        }
    }

    private List<GroupRepresentation> buildGroups(Map<String, String> roleToName) {
        return roleToName.entrySet().stream()
                .map(entry -> {
                    var group = new GroupRepresentation();
                    group.setName(entry.getValue());
                    group.setPath("/" + entry.getValue());
                    group.setRealmRoles(newArrayList(entry.getKey()));
                    return group;
                })
                .toList();
    }

    private RolesRepresentation buildRoles(Set<String> names) {
        var rolesRepresentation = new RolesRepresentation();
        rolesRepresentation.setRealm(names.stream().map(name -> {
            var role = new RoleRepresentation();
            role.setName(name);
            return role;
        }).toList());
        return rolesRepresentation;
    }

    private void createFrontOfficeRealm() {
        var realm = new RealmRepresentation();
        realm.setId(config.getFrontOfficeRealmId());
        realm.setRealm(config.getFrontOfficeRealmId());
        realm.setPasswordPolicy(config.getPasswordPolicy());

        var rolesToGroup = Map.of("CANDIDATE", CANDIDATES_GROUP);
        var roles = buildRoles(rolesToGroup.keySet());
        realm.setRoles(roles);
        var groups = buildGroups(rolesToGroup);
        realm.setGroups(groups);

        var webClient = buildFrontOfficeWebClient();
        var apiClient = buildFrontOfficeAPIClient();

        realm.setClients(newArrayList(webClient, apiClient));

        realm.setSmtpServer(smtpConfig.toStringMap());

        setCommonRealmSettings(realm, FO_THEME_ID);
        realm.setRegistrationAllowed(true);

        this.keycloak.realms().create(realm);

        String groupId = this.keycloak.realm(config.getFrontOfficeRealmId()).getGroupByPath("/" + CANDIDATES_GROUP).getId();
        this.keycloak.realm(config.getFrontOfficeRealmId()).addDefaultGroup(groupId);
        disableOTP(config.getFrontOfficeRealmId());
    }

    private ClientRepresentation buildFrontOfficeWebClient() {
        var webClient = new ClientRepresentation();
        webClient.setName("web-front");
        webClient.setClientId("web-front");
        webClient.setBaseUrl(config.getFrontOfficeBaseURL() + "/");
        webClient.setEnabled(true);
        webClient.setPublicClient(true);
        webClient.setRedirectUris(newArrayList(config.getFrontOfficeBaseURL() + "/*"));
        webClient.setWebOrigins(newArrayList(config.getFrontOfficeBaseURL()));
        return webClient;
    }

    private ClientRepresentation buildFrontOfficeAPIClient() {
        var apiClient = new ClientRepresentation();
        apiClient.setName("api");
        apiClient.setClientId("api");
        apiClient.setAdminUrl(config.getApiBaseURL() + "/");
        apiClient.setEnabled(true);
        apiClient.setBearerOnly(true);
        apiClient.setSecret(config.getFrontApiClientSecret());
        return apiClient;
    }

    private ClientRepresentation buildBackOfficeWebClient() {
        var webClient = new ClientRepresentation();
        webClient.setName("web");
        webClient.setClientId("web");
        webClient.setBaseUrl(config.getBackOfficeBaseURL() + "/");
        webClient.setEnabled(true);
        webClient.setPublicClient(true);
        webClient.setRedirectUris(newArrayList(config.getBackOfficeBaseURL() + "/*"));
        webClient.setWebOrigins(newArrayList(config.getBackOfficeBaseURL()));
        return webClient;
    }

    private ClientRepresentation buildBackOfficeAPIClient() {
        var apiClient = new ClientRepresentation();
        apiClient.setName("api");
        apiClient.setClientId("api");
        apiClient.setAdminUrl(config.getApiBaseURL() + "/");
        apiClient.setEnabled(true);
        apiClient.setBearerOnly(true);
        apiClient.setSecret(config.getBackApiClientSecret());
        return apiClient;
    }

    private void setCommonRealmSettings(RealmRepresentation realm, String themeId) {
        realm.setEnabled(true);
        realm.setAccountTheme("odas");
        realm.setAdminTheme("odas");
        realm.setEmailTheme("odas");
        realm.setLoginTheme(themeId);
        realm.setDefaultLocale("fr");
        realm.setSupportedLocales(Collections.singleton("fr"));
        realm.setInternationalizationEnabled(true);
        realm.setRegistrationEmailAsUsername(true);
        realm.setResetPasswordAllowed(true);
        realm.setVerifyEmail(false);
        realm.setRememberMe(true);
        realm.setLoginWithEmailAllowed(true);
    }

    private void disableOTP(String realmId) {
        switchAction(realmId, "CONFIGURE_TOTP", false);
    }

    private void switchAction(String realmId, String termAlias, boolean b) {
        var data = keycloak.realm(realmId).flows().getRequiredAction(termAlias);
        data.setDefaultAction(b);
        data.setEnabled(b);
        keycloak.realm(realmId).flows().updateRequiredAction(termAlias, data);
    }

    public List<String> getCandidateUserId() {
        return usersList;
    }

}
