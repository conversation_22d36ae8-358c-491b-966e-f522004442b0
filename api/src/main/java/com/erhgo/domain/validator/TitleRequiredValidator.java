package com.erhgo.domain.validator;

import com.erhgo.domain.Titleable;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class TitleRequiredValidator implements ConstraintValidator<TitleRequired, Titleable> {

    @Override
    public boolean isValid(Titleable value, ConstraintValidatorContext context) {
        if (value.getTitle() == null || value.getTitle().isEmpty()) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate("Title must be provided")
                    .addPropertyNode("title")
                    .addConstraintViolation();
            return false;
        }
        return true;
    }

}
