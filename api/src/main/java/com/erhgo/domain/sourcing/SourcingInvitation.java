package com.erhgo.domain.sourcing;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.exceptions.SourcingInvitationQuotaExceeded;
import com.erhgo.domain.referential.AbstractOrganization;
import jakarta.persistence.*;
import lombok.*;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Builder
@Entity
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true, exclude = {"subscriptions"})
public class SourcingInvitation extends AbstractAuditableEntity {

    @Id
    @Column(columnDefinition = "BINARY(16)")
    @Builder.Default
    private UUID uuid = UUID.randomUUID();

    @ManyToOne
    private AbstractOrganization host;

    @Builder.Default
    @OneToMany(mappedBy = "invitation", cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REFRESH})
    private Set<SourcingSubscription> subscriptions = new HashSet<>();

    private int maxNumberOfGuests;

    private String code;

    private int duration;

    void addSubscription(SourcingSubscription sourcingSubscription) {
        if (subscriptions.size() >= maxNumberOfGuests) {
            throw new SourcingInvitationQuotaExceeded(code);
        }
        subscriptions.add(sourcingSubscription);
    }

    public Set<AbstractOrganization> getGuests() {
        return subscriptions.stream().map(SourcingSubscription::getRecruiter).collect(Collectors.toSet());
    }

    public String getHostTitle() {
        return getHost().getTitle();
    }
}
