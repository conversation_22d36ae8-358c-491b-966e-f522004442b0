package com.erhgo.domain.sourcing;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.enums.SubscriptionType;
import com.erhgo.domain.referential.Recruiter;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.extern.slf4j.Slf4j;


import java.time.OffsetDateTime;
import java.util.UUID;

@Builder
@Entity
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(uniqueConstraints = @UniqueConstraint(name = "UK_RECRUITER_SUBSCRIPTION", columnNames = "recruiter_id"))
@Slf4j
public class SourcingSubscription extends AbstractAuditableEntity {

    @AllArgsConstructor
    public enum SourcingMailState {
        SEND_TRIAL_END(null), SEND_WELCOME(SEND_TRIAL_END);
        final SourcingMailState next;
    }

    @Id
    @Column(columnDefinition = "BINARY(16)")
    @Builder.Default
    private UUID uuid = UUID.randomUUID();

    @OneToOne
    @NotNull
    private Recruiter recruiter;

    private OffsetDateTime expirationDate;

    @ManyToOne
    private SourcingInvitation invitation;

    @Enumerated(EnumType.STRING)
    @Builder.Default
    private SourcingMailState mailState = SourcingMailState.SEND_WELCOME;

    public boolean isActiveTrial() {
        return !isExpired() && isTrial();
    }

    private boolean isTrial() {
        return invitation == null;
    }

    public SubscriptionType getSubscriptionType() {
        if (isActiveTrial()) {
            return SubscriptionType.TRIAL;
        }
        if (!isTrial()) {
            return SubscriptionType.ACTIVATED;
        }
        return SubscriptionType.EXPIRED;
    }

    public boolean isExpired() {
        return expirationDate != null && expirationDate.isBefore(OffsetDateTime.now());
    }

    public String getOrganizationCode() {
        return recruiter.getCode();
    }

    public Long getOrganizationId() {
        return recruiter.getId();
    }

    public boolean acceptInvitation(@NonNull SourcingInvitation invitation) {
        if (this.invitation != null) {
            log.warn("Trying to overload invitation {} with invitation {} on subscription {}", this.invitation.getCode(), invitation.getCode(), this.uuid);
            return false;
        }
        invitation.addSubscription(this);
        this.invitation = invitation;
        this.mailState = null;
        setExpirationDate(null);
        return true;
    }


    public boolean isActivated() {
        return getSubscriptionType() == SubscriptionType.ACTIVATED;
    }

    public void welcomeMailSent(boolean mailSent) {
        if (mailSent && mailState != null) {
            this.mailState = mailState.next;
        }
    }
}
