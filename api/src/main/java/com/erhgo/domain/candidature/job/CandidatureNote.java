package com.erhgo.domain.candidature.job;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.userprofile.UserNote;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;


import java.util.UUID;

@Data
@EqualsAndHashCode(exclude = "candidature", callSuper = false)
@ToString(exclude = "candidature", callSuper = true)
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CandidatureNote extends AbstractAuditableEntity {

    @Id
    @Column(columnDefinition = "BINARY(16)")
    @Builder.Default
    private UUID uuid = UUID.randomUUID();
    @NotNull
    @ManyToOne
    private AbstractCandidature candidature;

    @NotNull
    @Column(columnDefinition = "LONGTEXT")
    private String text;

    public boolean isRemoved() {
        return UserNote.REMOVED_NOTE_TEXT.equals(text);
    }
}
