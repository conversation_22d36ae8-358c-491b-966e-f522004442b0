package com.erhgo.domain.candidature.job;

import com.erhgo.domain.recruitment.Recruitment;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Stream;

@AllArgsConstructor
@NoArgsConstructor
public enum GlobalCandidatureState {

    MISSING_PREREQUISITE(true, null, null),
    NOT_FINALIZED(true, null, null),
    NOT_TREATED_BY_ERHGO(true, CandidatureRecruitmentState.NEW, null),
    REFUSED_ON_CALL(true, CandidatureRecruitmentState.WAITING, Recruitment.ProcessingType.REFUSE_CANDIDATURE),
    INTERNAL_POSITION(false, CandidatureRecruitmentState.SELECTED, Recruitment.ProcessingType.MEET_LATER_CANDIDATURE),
    INTRODUCE_TO_CLIENT(false, CandidatureRecruitmentState.SELECTED, Recruitment.ProcessingType.MEET_CANDIDATURE),
    SUMMARY_SHEET_SENT(false, CandidatureRecruitmentState.SELECTED, Recruitment.ProcessingType.MEET_CANDIDATURE),
    REFUSED_BY_CLIENT_WITH_SHEETS(false, CandidatureRecruitmentState.WAITING, Recruitment.ProcessingType.REFUSE_CANDIDATURE),
    ON_RECRUITMENT_CLIENT(false, CandidatureRecruitmentState.SELECTED, Recruitment.ProcessingType.FAV_CANDIDATURE),
    REFUSED_MEETING_CLIENT(false, CandidatureRecruitmentState.WAITING, Recruitment.ProcessingType.REFUSE_CANDIDATURE),
    RECRUITMENT_VALIDATED(false, CandidatureRecruitmentState.SELECTED, Recruitment.ProcessingType.FAV_CANDIDATURE),
    NEW(true, CandidatureRecruitmentState.NEW, null),
    STAND_BY(true, CandidatureRecruitmentState.WAITING, null);

    @Getter
    private boolean isAnonymous;

    @Getter
    private CandidatureRecruitmentState candidatureRecruitmentState;

    @Getter
    private Recruitment.ProcessingType recruitmentActionType;

    public static List<GlobalCandidatureState> finalizedValues() {
        return finalizedStateStream().toList();
    }


    private static Stream<GlobalCandidatureState> finalizedStateStream() {
        return Stream.of(values()).filter(c -> c.candidatureRecruitmentState != null);
    }

    public static List<GlobalCandidatureState> statesToNotifyOnRecruitmentClose() {
        return finalizedStateStream().filter(c -> !c.isRefused()).toList();
    }

    public static List<GlobalCandidatureState> untreated() {
        return List.of(
                STAND_BY, NEW, NOT_TREATED_BY_ERHGO
        );
    }

    public static List<GlobalCandidatureState> treated() {
        return List.of(
                RECRUITMENT_VALIDATED,
                ON_RECRUITMENT_CLIENT,
                SUMMARY_SHEET_SENT,
                INTRODUCE_TO_CLIENT,
                INTERNAL_POSITION
        );
    }

    public static List<GlobalCandidatureState> refused() {
        return List.of(
                REFUSED_ON_CALL,
                REFUSED_BY_CLIENT_WITH_SHEETS,
                REFUSED_MEETING_CLIENT
        );
    }
    public boolean isNew() {
        return this == NEW || this == NOT_TREATED_BY_ERHGO || this == STAND_BY;
    }

    public boolean isRefused() {
        return refused().contains(this);
    }
}
