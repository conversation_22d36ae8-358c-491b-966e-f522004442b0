package com.erhgo.domain.candidature.job;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.enums.CandidatureSynchronizationState;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.UserRegistrationState;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

import static com.erhgo.utils.StringUtils.generateRandom4CharactersString;
import static com.erhgo.utils.StringUtils.getRandomColorIndex;

@Data
@Entity
@Inheritance(strategy = InheritanceType.TABLE_PER_CLASS)
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@ToString(callSuper = true, exclude = {"candidatureNotes"})
@Slf4j
public abstract class AbstractCandidature extends AbstractAuditableEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "seqGenerator")
    @SequenceGenerator(name = "seqGenerator", sequenceName = "CandidatureSeq", allocationSize = 1, initialValue = 100000)
    private Long id;

    @OneToMany(mappedBy = "candidature", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<CandidatureNote> candidatureNotes = new ArrayList<>();

    @ManyToOne(optional = false)
    @EqualsAndHashCode.Include
    private UserProfile userProfile;

    @Column(columnDefinition = "TIMESTAMP WITH TIME ZONE")
    private OffsetDateTime submissionDate;

    @Embedded
    private CandidatureRefusalState candidatureRefusalState;

    @Enumerated(EnumType.STRING)
    private GlobalCandidatureState globalCandidatureState;

    @Column(length = 20)
    private String color;
    private String anonymousCode;
    private boolean archived;

    private boolean modifiedByUser;

    @Enumerated(EnumType.STRING)
    private CandidatureSynchronizationState synchronizationState;

    private String remoteNotifiedIdentifier;

    protected AbstractCandidature(
            Long id,
            List<CandidatureNote> candidatureNotes,
            UserProfile userProfile,
            OffsetDateTime submissionDate,
            GlobalCandidatureState globalCandidatureState
    ) {
        this.id = id;
        if (candidatureNotes != null) {
            this.candidatureNotes = candidatureNotes;
        }
        this.userProfile = userProfile;
        this.submissionDate = submissionDate;
        this.globalCandidatureState = globalCandidatureState == null ? GlobalCandidatureState.NOT_FINALIZED : globalCandidatureState;
        this.color = getRandomColorIndex();
        this.anonymousCode = generateRandom4CharactersString();
    }

    public AbstractCandidature markAsRefused(CandidatureEmailRefusalState emailState, String refusedBy) {
        this.candidatureRefusalState = new CandidatureRefusalState(emailState, refusedBy);
        this.setGlobalCandidatureState(GlobalCandidatureState.REFUSED_ON_CALL);
        return this;
    }

    public void markEmailRefusedState(CandidatureEmailRefusalState state) {
        this.candidatureRefusalState.setEmailSent(state);
    }

    public OffsetDateTime getRefusalDate() {
        return this.candidatureRefusalState != null ? this.candidatureRefusalState.getRefusalDate() : null;
    }

    public boolean isRefused() {
        return this.candidatureRefusalState != null || (globalCandidatureState != null && globalCandidatureState.isRefused());
    }
    public abstract String getOrganizationName();

    public abstract String getCodeOfRecruiter();

    public abstract String getRecruiterTitle();

    public boolean isAnonymous() {
        return globalCandidatureState != null && globalCandidatureState.isAnonymous();
    }

    public void dismiss(String refusedBy) {
        if (globalCandidatureState.isAnonymous()) {
            setGlobalCandidatureState(GlobalCandidatureState.REFUSED_ON_CALL);
        } else {
            setGlobalCandidatureState(GlobalCandidatureState.REFUSED_BY_CLIENT_WITH_SHEETS);
        }

        if (isVisibleForUser() && isRecruitmentCandidature()) {
            this.candidatureRefusalState = new CandidatureRefusalState(CandidatureEmailRefusalState.WAITING, refusedBy);
        } else {
            log.debug("Ignoring refusal email notification for generated candidature {}", getId());
            this.candidatureRefusalState = new CandidatureRefusalState(CandidatureEmailRefusalState.NONE, refusedBy);
            markEmailRefusedState(CandidatureEmailRefusalState.NONE);
        }
    }

    public void toContact() {
        candidatureRefusalState = null;
        setGlobalCandidatureState(GlobalCandidatureState.INTERNAL_POSITION);
    }

    public void contacted() {
        candidatureRefusalState = null;
        setGlobalCandidatureState(GlobalCandidatureState.INTRODUCE_TO_CLIENT);
    }

    public void favorite() {
        candidatureRefusalState = null;
        setGlobalCandidatureState(GlobalCandidatureState.ON_RECRUITMENT_CLIENT);
    }

    public abstract boolean isSourcing();

    public String getUserId() {
        return userProfile.userId();
    }

    public void setGlobalCandidatureState(GlobalCandidatureState nextState) {
        this.globalCandidatureState = nextState;
    }

    public String getLastNote() {
        return candidatureNotes.stream()
                .filter(n -> !n.isRemoved())
                .max(Comparator.comparing(AbstractAuditableEntity::getUpdatedDate))
                .map(CandidatureNote::getText)
                .orElse("");
    }

    public abstract Recruiter getRecruiter();

    public boolean isNew() {
        return globalCandidatureState != null && globalCandidatureState.isNew();
    }

    public abstract void updateLastProcessingData(Recruitment.ProcessingType processingType);

    public abstract boolean isVisibleForUser();

    public abstract boolean isRecruitmentCandidature();

    public Location getLocation() {
        return userProfile.getLocationIndication();
    }

    public void postPublish() {
        userProfile.updateRegistrationState(UserRegistrationState.RegistrationStep.NOT_AFFECTED);
    }

    public abstract String getTitle();

    public boolean relatesToATS(String eoliaAtsCode) {
        return false;
    }

    public String getCustomAnswer() {
        return null;
    }

    public void addRemoteNotifiedIdentifier(String email) {
        if (remoteNotifiedIdentifier == null) {
            remoteNotifiedIdentifier = "";
        }
        remoteNotifiedIdentifier = Joiner.on(",").join(Lists.asList(email, remoteNotifiedIdentifier.split(",")));

    }

    public boolean requiresSourcingSync() {
        return isSourcing() && globalCandidatureState == GlobalCandidatureState.NOT_TREATED_BY_ERHGO;
    }
}
