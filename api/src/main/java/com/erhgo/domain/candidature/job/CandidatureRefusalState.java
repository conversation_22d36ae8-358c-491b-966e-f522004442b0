package com.erhgo.domain.candidature.job;

import jakarta.persistence.Embeddable;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.*;

import java.time.OffsetDateTime;

import static com.erhgo.utils.DateTimeUtils.ZONE_ID;

@Embeddable
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@EqualsAndHashCode
public class CandidatureRefusalState {
    @Getter
    private OffsetDateTime refusalDate;

    @Getter
    @Setter(AccessLevel.PACKAGE)
    @Enumerated(EnumType.STRING)
    private CandidatureEmailRefusalState emailSent;

    @Getter
    private String refusedBy;

    CandidatureRefusalState(CandidatureEmailRefusalState emailState, String refusedBy) {
        this.refusedBy = refusedBy;
        this.refusalDate = OffsetDateTime.now(ZONE_ID);
        this.emailSent = emailState;
    }
}
