package com.erhgo.domain.candidature.job;

import com.erhgo.domain.enums.CandidatureSynchronizationState;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.sector.Sector;
import com.erhgo.domain.userprofile.UserProfile;
import jakarta.persistence.*;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.OffsetDateTime;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@Entity
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@ToString(callSuper = true)
@Slf4j
public class SpontaneousCandidature extends AbstractCandidature {

    @ManyToOne(optional = false)
    @EqualsAndHashCode.Include
    private Recruiter recruiter;

    @ManyToMany
    private Set<Sector> referentialSectors = new HashSet<>();

    @ElementCollection
    @Column(length = 2000)
    private Set<String> customSectors = new HashSet<>();

    @Builder
    public SpontaneousCandidature(
            Long id,
            Recruiter recruiter,
            List<CandidatureNote> candidatureNotes,
            UserProfile userProfile,
            OffsetDateTime submissionDate
    ) {
        super(id, candidatureNotes, userProfile, submissionDate, GlobalCandidatureState.NEW);
        this.recruiter = recruiter;
        setModifiedByUser(true);
        this.setSubmissionDate(OffsetDateTime.now());
        this.postPublish();
        this.setSynchronizationState(CandidatureSynchronizationState.WAITING);
    }

    @Override
    public String getOrganizationName() {
        return getRecruiterTitle();
    }

    @Override
    public String getCodeOfRecruiter() {
        return recruiter.getCode();
    }

    @Override
    public String getRecruiterTitle() {
        return recruiter.getTitle();
    }

    @Override
    public boolean isSourcing() {
        return recruiter.getOrganizationType() == AbstractOrganization.OrganizationType.SOURCING;
    }

    @Override
    public void updateLastProcessingData(Recruitment.ProcessingType processingType) {
        // no op
    }

    @Override
    public boolean isVisibleForUser() {
        return true;
    }

    @Override
    public boolean isRecruitmentCandidature() {
        return false;
    }

    @Override
    public String getTitle() {
        return "Candidature spontanée";
    }

    public void resetSectors(Collection<Sector> referentialSectors, Collection<String> customSectors) {
        log.debug("Resetting sectors - before: REF={}, CUSTOM={}, after: REF={}, CUSTOM={}", this.referentialSectors, this.customSectors, referentialSectors, customSectors);
        if (referentialSectors != null) {
            this.referentialSectors.removeIf(s -> !referentialSectors.contains(s));
            this.referentialSectors.addAll(referentialSectors);
        } else {
            this.referentialSectors.clear();
        }
        if (customSectors != null) {
            var normalized = customSectors.stream().filter(StringUtils::isNotBlank).map(String::toLowerCase).collect(Collectors.toSet());
            this.customSectors.removeIf(s -> !normalized.contains(s));
            this.customSectors.addAll(normalized);
        } else {
            this.customSectors.clear();
        }
    }
}
