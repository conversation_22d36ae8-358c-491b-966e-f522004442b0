package com.erhgo.domain.dto.event;

import com.erhgo.domain.userprofile.UserChannel;
import com.erhgo.domain.userprofile.UserProfile;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.HashSet;
import java.util.Set;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
@EqualsAndHashCode
public class UserAffectedToChannelsEvent {

    @Getter
    private Set<String> channels;
    @Getter
    private UserProfile userProfile;
    @Getter
    private UserChannel.ChannelSourceType channelSourceType;

    public static UserAffectedToChannelsEvent forChannels(Set<String> channels, UserProfile userProfile, UserChannel.ChannelSourceType channelSourceType) {
        return new UserAffectedToChannelsEvent(new HashSet<>(channels), userProfile, channelSourceType);
    }

}
