package com.erhgo.domain.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Accessors(chain = true)
@Getter
@Setter
public class EmailVerificationResultDTO {
        public static final EmailVerificationResultDTO TRUE = new EmailVerificationResultDTO().setEmailStatus(EmailStatus.VALID);

        private String suggestion;
        private EmailStatus emailStatus;
        private String invalidCauseLabel;

        public enum EmailStatus {
                VALID,
                VERIFIER_ERROR,
                INVALID_MAIL,
                INVALID_SERVER,
                UNKNOWN
        }
}
