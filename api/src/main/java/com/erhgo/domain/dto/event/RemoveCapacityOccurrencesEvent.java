package com.erhgo.domain.dto.event;

import com.erhgo.domain.userprofile.CapacityOccurrence;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class RemoveCapacityOccurrencesEvent {

    @Getter
    private List<CapacityOccurrence> capacityOccurrences;

    public static RemoveCapacityOccurrencesEvent forOccurrences(Collection<CapacityOccurrence> capacityOccurrencesRemoved) {
        return new RemoveCapacityOccurrencesEvent(new ArrayList<>(capacityOccurrencesRemoved));
    }

}
