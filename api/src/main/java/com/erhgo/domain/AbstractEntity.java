package com.erhgo.domain;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.voodoodyne.jackson.jsog.JSOGGenerator;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
@NoArgsConstructor
@JsonIdentityInfo(generator = JSOGGenerator.class)
@EqualsAndHashCode(callSuper = false)
@Deprecated
@MappedSuperclass
public abstract class AbstractEntity extends AbstractAuditableEntity implements Serializable, Titleable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @EqualsAndHashCode.Include
    private Long id;
    @EqualsAndHashCode.Include
    private String code;
    @EqualsAndHashCode.Include
    private String title;

    // Caution: overloaded in some child classes
    @Column(length = 2000)
    private String description = "";

    public String getLabel() {
        return this.getCode() + " - " + this.getTitle();
    }

    public void updateCodeOnEntityCreate() {
        setCode(getPrefix() + "-" + StringUtils.leftPad(getId().toString(), getSuffixLeftPadCount(), "0"));
    }

    public abstract String getPrefix();

    public abstract int getSuffixLeftPadCount();
}
