package com.erhgo.domain.landingpage;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Recruiter;
import com.google.common.collect.Sets;
import jakarta.persistence.*;
import lombok.*;


import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Entity
@Builder
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public class LandingPage extends AbstractAuditableEntity {

    @Id
    @Column(columnDefinition = "BINARY(16)")
    @Builder.Default
    private UUID id = UUID.randomUUID();

    @Column(unique = true, nullable = false)
    private String urlKey;

    @Column(columnDefinition = "LONGTEXT")
    private String content;

    @ManyToMany
    @Builder.Default
    private Set<Recruiter> organizations = Sets.newHashSet();

    public void updateTerritorialOrganizations(Collection<Recruiter> organizations) {
        this.organizations.clear();
        this.organizations.addAll(organizations);
    }

    public List<String> getOrganizationsTitles() {
        return getOrganizations().stream().map(AbstractOrganization::getTitle).toList();
    }

    public List<String> getOrganizationsCodes() {
        return getOrganizations().stream().map(AbstractOrganization::getCode).toList();
    }
}
