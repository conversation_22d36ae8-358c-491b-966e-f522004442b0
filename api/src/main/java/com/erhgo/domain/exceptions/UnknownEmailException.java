package com.erhgo.domain.exceptions;

import com.erhgo.domain.dto.EmailVerificationResultDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

public class UnknownEmailException extends AbstractFunctionalException {

    private final String suggestion;

    public UnknownEmailException(EmailVerificationResultDTO verificationResult, String email) {
        super("Email %s is unknown".formatted(email));
        suggestion = verificationResult.getSuggestion();
    }

    @Override
    public Map<String, String> describe() {
        return Map.of("cause", "UNKNOWN_EMAIL", "suggestion", StringUtils.trimToEmpty(suggestion));
    }
}
