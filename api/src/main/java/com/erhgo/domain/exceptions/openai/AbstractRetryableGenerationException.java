package com.erhgo.domain.exceptions.openai;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public abstract class AbstractRetryableGenerationException extends Exception {

    private final String previousResult;

    protected AbstractRetryableGenerationException(String message, String previousResult) {
        super(message);
        this.previousResult = previousResult;
    }

    protected AbstractRetryableGenerationException(String message, String previousResult, Throwable cause) {
        super(message, cause);
        this.previousResult = previousResult;
    }


}
