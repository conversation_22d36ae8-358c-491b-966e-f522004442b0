package com.erhgo.domain.exceptions;

import com.erhgo.repositories.dto.UserAndPhoneDTO;

import java.io.Serializable;
import java.util.List;

public class UserConflictException extends EntityAlreadyExistException {

    public UserConflictException(String sourceField, Class<?> type, Serializable id, List<UserAndPhoneDTO> existentUsersAndPhones) {
        super(sourceField, type, id);
        details.put("conflictedUsers", existentUsersAndPhones);
    }
}
