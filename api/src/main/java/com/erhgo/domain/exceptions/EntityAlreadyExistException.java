package com.erhgo.domain.exceptions;

import lombok.Getter;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class EntityAlreadyExistException extends AbstractFunctionalException {
    @Getter
    final transient Map<String, Object> details;

    public EntityAlreadyExistException(String sourceField, Class<?> type, Serializable id) {
        super(type + " field " + sourceField + " already exists for '" + id + "'");
        details = new HashMap<>();
        details.put("cause", getMessage());
        if (sourceField != null) {
            details.put("source", sourceField);
        }
        if (type != null) {
            details.put("type", type.getName());
        }
        if (id != null) {
            details.put("id", String.valueOf(id));
        }
    }

    public EntityAlreadyExistException(Class<?> type, Serializable id) {
        this(null, type, id);
    }
}
