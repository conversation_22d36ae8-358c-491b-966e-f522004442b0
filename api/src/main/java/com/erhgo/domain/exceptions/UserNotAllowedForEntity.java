package com.erhgo.domain.exceptions;

import com.erhgo.domain.AbstractAuditableEntity;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class UserNotAllowedForEntity extends AbstractFunctionalException {

    public UserNotAllowedForEntity(AbstractAuditableEntity entity) {
        super("User is not allowed to access entity");
        log.info("Current user tried to access {} and is not allowed", entity);
    }

    public UserNotAllowedForEntity(String message) {
        super(message);
        log.info(message);
    }

}
