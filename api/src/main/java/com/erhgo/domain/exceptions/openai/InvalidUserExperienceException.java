package com.erhgo.domain.exceptions.openai;

public class InvalidUserExperienceException extends AbstractRetryableGenerationException {
    public InvalidUserExperienceException(String jsonResult) {
        super("Expérience utilisateur invalide", jsonResult);
    }

    public InvalidUserExperienceException(String jsonResult, Exception root) {
        super("Expérience utilisateur invalide", jsonR<PERSON>ult, root);
    }
}

