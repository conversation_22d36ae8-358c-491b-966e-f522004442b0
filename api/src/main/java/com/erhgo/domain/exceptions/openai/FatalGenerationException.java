package com.erhgo.domain.exceptions.openai;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.services.generation.dto.OpenAIResponse;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class FatalGenerationException extends GenericTechnicalException {

    private transient OpenAIResponse response;

    public FatalGenerationException(String s, OpenAIResponse<?> response, Exception root) {
        super(s, root);
        this.response = response;
    }

    public FatalGenerationException(String s, OpenAIResponse<?> response) {
        super(s);
        this.response = response;
    }


    public FatalGenerationException(String s) {
        this(s, (OpenAIResponse) null);
    }

    public FatalGenerationException(String s, Exception root) {
        this(s, null, root);
    }


}
