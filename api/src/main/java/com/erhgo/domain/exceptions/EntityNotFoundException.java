package com.erhgo.domain.exceptions;

import java.util.Collection;
import java.util.stream.Collectors;

public class EntityNotFoundException extends AbstractFunctionalException {
    public EntityNotFoundException(Object id, Class<?> type) {
        super("No " + type.getName() + " found for id " + id);
    }

    public EntityNotFoundException(Collection<? extends Object> ids, Class<?> type) {
        super("No " + type.getName() + " found for id " + ids.stream().map(Object::toString).collect(Collectors.joining(", ")));
    }
}
