package com.erhgo.domain.exceptions;

import java.util.Collections;
import java.util.Map;

public abstract class AbstractFunctionalException extends RuntimeException {


    protected AbstractFunctionalException(String s) {
        super(s);
    }

    protected AbstractFunctionalException(String s, Throwable cause) {
        super(s, cause);
    }

    public Map<String, String> describe() {
        return Collections.emptyMap();
    }
}
