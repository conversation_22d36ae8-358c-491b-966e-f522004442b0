package com.erhgo.domain.exceptions;

import java.util.Map;

public class InvalidCommandException extends AbstractFunctionalException {
    private final String code;
    private final String label;

    public InvalidCommandException(String code, String label, String command) {
        super("Command " + command + " failed (" + code + " - " + label + ")");
        this.code = code;
        this.label = label;
    }

    public InvalidCommandException(String label) {
        super(label);
        this.code = "";
        this.label = label;
    }

    @Override
    public Map<String, String> describe() {
        return Map.of("code", code, "label", label, "message", getMessage());
    }

}
