package com.erhgo.domain.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <ul>
 * <li>CDD - Contrat à Durée Déterminée</li>
 * <li>CDI - Contrat à Durée Indéterminée</li>
 * <li>CTT - Contrat de Travail Temporaire</li>
 * <li>CUI - Contrat unique d’insertion</li>
 * <li>CA - Contrat d’apprentissage</li>
 * <li>CP -Contrat de professionnalisation</li>
 * </ul>
 */
@RequiredArgsConstructor
@Getter
public enum ContractType {
    CDD(TypeContractCategory.TEMPORARY, "CDD"),
    CDI(TypeContractCategory.PERMANENT, "CDI"),
    CTT(TypeContractCategory.TEMPORARY, "Interim"),
    CUI(TypeContractCategory.PRO, "Contrat d'insertion"),
    CA(TypeContractCategory.PRO, "Alternance"),
    CP(TypeContractCategory.PRO, "Contrat pro"),
    SEASONAL(TypeContractCategory.PRO, "Saisonnier"),
    FREELANCE(TypeContractCategory.PRO, "Freelance / Indépendant");

    private final TypeContractCategory typeContractCategory;
    private final String description;


    public static ContractType forCategory(TypeContractCategory typeContractCategory) {
        ContractType contractType = null;
        switch (typeContractCategory) {
            case TEMPORARY -> contractType = CDD;
            case PERMANENT -> contractType = CDI;
            case PRO -> contractType = CA;
            case FREELANCE -> contractType = FREELANCE;
            case INTERIM -> contractType = CTT;
            case SEASONAL -> contractType = SEASONAL;
        }
        return contractType;
    }

}
