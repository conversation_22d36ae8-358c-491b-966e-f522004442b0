package com.erhgo.domain.enums;

import com.google.common.annotations.VisibleForTesting;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.ArrayUtils;

@AllArgsConstructor
public enum BehaviorCategory {

    CONSTANCY(4),
    HONESTY(6),
    TENACITY(3),
    SOCIABILITY(2),
    PRAGMATISM(5),
    RIGOR(1),
    AUTONOMY(999),
    CONFIDENTIALITY(999),
    CURIOSITY(999),
    PERSEVERANCE(999),
    CRITICAL_THINKING(999),
    PUNCTUALITY(999),
    REACTIVITY(999),
    SENSE_OF_SERVICE(999),
    VIGILANCE(999);

    private static final int PRIORITY_FOR_NG_CATEGORY = 999;

    @Getter
    private final int priorityForUser;

    public static String translateBehaviorCategory(BehaviorCategory behaviorCategory) {
        return switch (behaviorCategory) {
            case RIGOR -> "Rigueur";
            case HONESTY -> "Honnêteté";
            case TENACITY -> "Engagement";
            case CONSTANCY -> "Constance";
            case PRAGMATISM -> "Pragmatisme";
            case SOCIABILITY -> "Sociabilité";
            default -> null;
        };
    }

    @VisibleForTesting
    public static BehaviorCategory[] legacyValues() {
        return new BehaviorCategory[]{
                CONSTANCY,
                HONESTY,
                TENACITY,
                SOCIABILITY,
                PRAGMATISM,
                RIGOR
        };
    }

    public boolean isLegacy() {
        return ArrayUtils.contains(legacyValues(), this);
    }
}
