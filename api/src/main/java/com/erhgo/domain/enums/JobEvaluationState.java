package com.erhgo.domain.enums;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum JobEvaluationState {

    LISTED(true),
    INFOS_PROVIDED(true),
    MISSIONS_PROVIDED(true),
    BEHAVIORS_PROVIDED(true),
    PUBLISHED(false),
    REEVALUATION_NOT_FINISHED(true),
    FINISHED(false);

    private boolean isModifiable;

    public boolean isModifiable() {
        return isModifiable;
    }
}
