package com.erhgo.domain.configuration;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public class PersonalEmailDomain {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true)
    private String domain;

    public boolean emailHasThisDomain(String email) {
        if (email == null) {
            return false;
        }
        var userDomain = email.contains("@") ? email.split("@")[1] : email;
        return domain.toLowerCase().equals(userDomain.toLowerCase().trim());
    }
}
