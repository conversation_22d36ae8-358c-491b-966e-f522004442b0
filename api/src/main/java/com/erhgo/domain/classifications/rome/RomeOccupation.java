package com.erhgo.domain.classifications.rome;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.validator.constraints.Length;
import org.jetbrains.annotations.NotNull;

import java.util.Comparator;
import java.util.HashSet;
import java.util.Set;

@Data
@Builder
@Entity
@NoArgsConstructor
@AllArgsConstructor
@ToString(exclude = {"accessibleFromRomeOccupations", "accessibleRomeOccupations"})
@EqualsAndHashCode(exclude = {"accessibleFromRomeOccupations", "accessibleRomeOccupations"})
public class RomeOccupation implements Comparable {

    @Id
    @Column(length = 10)
    private String code;

    @Column(length = 2000)
    @Length(max = 2000)
    private String title;

    @ManyToMany
    @Builder.Default
    @JoinTable(
            name = "RomeOccupation_accessibleRomeOccupations",
            joinColumns = @JoinColumn(name = "RomeOccupation_id"),
            inverseJoinColumns = @JoinColumn(name = "accessibleRomeOccupations_id")
    )
    private Set<RomeOccupation> accessibleRomeOccupations = new HashSet<>();

    @ManyToMany(mappedBy = "accessibleRomeOccupations")
    @Builder.Default
    private Set<RomeOccupation> accessibleFromRomeOccupations = new HashSet<>();


    @Override
    public int compareTo(@NotNull Object o) {
        return o instanceof RomeOccupation other ? Comparator.comparing(RomeOccupation::getCode).compare(this, other) : 1;
    }
}
