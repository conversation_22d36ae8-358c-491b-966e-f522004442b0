package com.erhgo.domain.classifications.erhgooccupation;

import com.erhgo.domain.classifications.rome.RomeOccupation;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.io.Serializable;
import java.util.Set;
import java.util.UUID;

@Entity
@EqualsAndHashCode(of = {"erhgoOccupation", "romeOccupation"})
@ToString(of = {"id", "erhgoOccupation", "romeOccupation"})
public class RomeOfErhgoOccupation {


    @Embeddable
    @EqualsAndHashCode
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    @AllArgsConstructor(access = AccessLevel.PUBLIC)
    public static class ID implements Serializable {

        @NotNull
        @Getter(AccessLevel.PRIVATE)
        private String romeCode;

        @NotNull
        @Getter(AccessLevel.PRIVATE)
        private UUID erhgoOccupationId;
    }

    public enum RomeSourceType {

        A_ESCO_EXACT,

        B_ESCO_CLOSE,

        C_ESCO_APPROX,

        D_ISCO,

        E_MANUAL,

        F_UNCHECKED
    }

    @EmbeddedId
    @Getter
    private RomeOfErhgoOccupation.ID id;

    @MapsId("erhgoOccupationId")
    @ManyToOne(optional = false)
    @Getter
    private ErhgoOccupation erhgoOccupation;

    @MapsId("romeCode")
    @ManyToOne(optional = false)
    @Getter
    private RomeOccupation romeOccupation;

    @Enumerated(EnumType.STRING)
    private RomeSourceType romeSourceType;

    private RomeOfErhgoOccupation() {
    }

    private RomeOfErhgoOccupation(ErhgoOccupation erhgoOccupation, RomeOccupation romeOccupation, RomeSourceType romeSourceType) {
        this();
        this.romeOccupation = romeOccupation;
        this.erhgoOccupation = erhgoOccupation;
        this.romeSourceType = romeSourceType;
        this.id = new RomeOfErhgoOccupation.ID(romeOccupation.getCode(), erhgoOccupation.getId());
    }

    public RomeOfErhgoOccupation(ErhgoOccupation occupation, RomeOccupation rome) {
        this(occupation, rome, RomeSourceType.E_MANUAL);
    }

    public String getRomeCode() {
        return romeOccupation.getCode();
    }

    public Set<RomeOccupation> getAccessibleFromRomeOccupations() {
        return romeOccupation.getAccessibleFromRomeOccupations();
    }

    public Set<RomeOccupation> getAccessibleRomeOccupations() {
        return romeOccupation.getAccessibleRomeOccupations();
    }

    public void remove() {
        this.erhgoOccupation = null;
    }

    public RomeOfErhgoOccupation copyToOccupation(ErhgoOccupation erhgoOccupation) {
        return new RomeOfErhgoOccupation(erhgoOccupation, this.romeOccupation, this.romeSourceType);
    }
}
