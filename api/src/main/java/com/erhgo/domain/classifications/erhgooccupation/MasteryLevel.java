package com.erhgo.domain.classifications.erhgooccupation;

public enum MasteryLevel {
    PROFESSIONAL,
    TECHNICAL,
    COMPLEX,
    EXPERT,
    STRATEGIC;

    public static final MasteryLevel MAX_LEVEL = STRATEGIC;
    public static final MasteryLevel MIN_LEVEL = PROFESSIONAL;

    public static final int DEFAULT_LEVEL_AS_INT = MasteryLevel.MIN_LEVEL.getMasteryLevel();

    // Cf. ERHGO-506: if user has no level, consider minimum level
    public static MasteryLevel forLevel(Integer level) {
        if (level == null) {
            return null;
        }
        var realLevel = Math.max(level, MIN_LEVEL.getMasteryLevel());
        realLevel = Math.min(realLevel, MAX_LEVEL.getMasteryLevel());
        return MasteryLevel.values()[realLevel - 1];
    }

    public static MasteryLevel forLevel(float level) {
        Integer floorLevel = (int) Math.floor(level);
        return forLevel(floorLevel);
    }

    public static boolean isValid(Integer masteryLevel) {
        return masteryLevel >= MIN_LEVEL.getMasteryLevel() && masteryLevel <= MAX_LEVEL.getMasteryLevel();
    }

    public MasteryLevel next() {
        return MasteryLevel.forLevel(getMasteryLevel() + 1f);
    }

    public int getMasteryLevel() {
      return this.ordinal() + 1;
    }
}
