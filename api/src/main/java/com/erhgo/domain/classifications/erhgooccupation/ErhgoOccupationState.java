package com.erhgo.domain.classifications.erhgooccupation;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
public enum ErhgoOccupationState {
    TO_CONFIRM,
    NONE,
    LOW,
    MEDIUM,
    HIGH,
    VERY_HIGH,
    QUALIFIED_V1(true),
    QUALIFIED_V2(true),
    QUALIFIED_V3(true),
    QUALIFIED_V3_CONFIRMED(true),
    ;
    public static final int LOWER_QUALIFIED_STATE_ORDINAL = 5;

    @Getter
    private boolean isQualified;

    public static ErhgoOccupationState getByActivitiesAndQualifiedSkills(long activitiesCount, double qualifiedSkillsPercent) {
        if (activitiesCount > 9 && qualifiedSkillsPercent > 0.75) {
            return ErhgoOccupationState.VERY_HIGH;
        } else if (activitiesCount > 6 && qualifiedSkillsPercent > 0.55) {
            return ErhgoOccupationState.HIGH;
        } else if (activitiesCount > 3 && qualifiedSkillsPercent > 0.25) {
            return ErhgoOccupationState.MEDIUM;
        } else if (activitiesCount > 1 && qualifiedSkillsPercent > 0.1) {
            return ErhgoOccupationState.LOW;
        } else {
            return ErhgoOccupationState.NONE;
        }
    }

    public boolean isFinalState() {
        return ordinal() == ErhgoOccupationState.values().length - 1;
    }

    public boolean isQualifiedEnough() {
        return ordinal() >= QUALIFIED_V3.ordinal();
    }
}
