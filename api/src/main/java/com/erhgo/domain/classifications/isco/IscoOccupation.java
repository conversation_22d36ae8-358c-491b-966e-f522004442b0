package com.erhgo.domain.classifications.isco;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

@Data
@Builder
@Entity
@NoArgsConstructor
@AllArgsConstructor
public class IscoOccupation {

    @Id
    private int iscoGroup;

    @Column(length = 2000)
    @Length(max = 2000)
    private String title;
}
