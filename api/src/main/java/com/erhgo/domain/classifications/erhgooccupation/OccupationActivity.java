package com.erhgo.domain.classifications.erhgooccupation;

import com.erhgo.domain.classifications.esco.EscoSkill;
import com.erhgo.domain.referential.JobActivityLabel;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import lombok.*;

import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Entity
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class OccupationActivity extends AbstractMandatoryOccupationEntity {

    @ManyToOne(optional = false)
    @Getter
    private JobActivityLabel activity;

    @Builder
    public OccupationActivity(JobActivityLabel activity, ErhgoOccupation occupation, OccupationQualificationSource occupationQualificationSource) {
        super(occupationQualificationSource, occupation);
        this.activity = activity;
    }

    @Override
    public Set<EscoSkill> filterSkillsWithEntity(Set<EscoSkill> skills) {
        return skills.stream().filter(s -> s.getActivities().stream().anyMatch(a -> a.getUuid().equals(activity.getUuid()))).collect(Collectors.toSet());
    }

    @Override
    public UUID getEntityUuid() {
        return activity.getUuid();
    }


}
