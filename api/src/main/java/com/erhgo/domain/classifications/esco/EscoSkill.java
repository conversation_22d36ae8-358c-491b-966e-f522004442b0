package com.erhgo.domain.classifications.esco;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.referential.Behavior;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.JobActivityLabel;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.validator.constraints.Length;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import java.util.function.Predicate;

import static java.util.Collections.emptySet;

@Data
@Builder
@Entity
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@ToString(callSuper = true, onlyExplicitlyIncluded = true)
public class EscoSkill extends AbstractAuditableEntity {
    @Id
    @ToString.Include
    @EqualsAndHashCode.Include
    private String uri;

    @NotNull
    @ToString.Include
    @EqualsAndHashCode.Include
    private String title;

    @Column(length = 2000)
    @Length(max = 2000)
    private String descriptionEN;

    @Column(length = 2000)
    @Length(max = 2000)
    private String descriptionFR;

    @ElementCollection
    @Builder.Default
    private Set<String> alternativeLabels = new HashSet<>();

    private String skillType;

    @ManyToMany
    private Set<JobActivityLabel> activities;

    @ManyToMany
    private Set<Context> contexts;

    @ManyToMany
    @Builder.Default
    private Set<Behavior> behaviors = new HashSet<>();

    // These three flags are null when associated entities list is empty, false otherwise.
    // They are true only if user explicitly set it to true (checkbox).
    private Boolean noActivity;
    private Boolean noContext;
    private Boolean noBehavior;

    public void computeNoActivity(boolean noActivity) {
        this.noActivity = trueOrNull(noActivity);
        if (noActivity && this.activities != null) {
            this.activities.clear();
        }
    }

    public void computeNoContext(boolean noContext) {
        this.noContext = trueOrNull(noContext);
        if (noContext) {
            this.setContexts(emptySet());
        }
    }

    public void computeNoBehavior(boolean noBehavior) {
        this.noBehavior = trueOrNull(noBehavior);
        if (noBehavior) {
            this.setBehaviors(emptySet());
        }
    }

    private Boolean trueOrNull(boolean noEntity) {
        return noEntity ? true : null;
    }

    public void linkActivity(JobActivityLabel jobActivityLabel) {
        if (this.activities == null) {
            this.activities = new HashSet<>();
        }
        this.activities.add(jobActivityLabel);
        this.noActivity = false;
    }

    public void addBehavior(Behavior behavior) {
        if (this.getBehaviors() == null) {
            this.setBehaviors(new HashSet<>());
        }
        this.getBehaviors().add(behavior);
        this.noBehavior = false;
    }

    public void addContext(Context context) {
        if (this.getContexts() == null) {
            this.setContexts(new HashSet<>());
        }
        this.getContexts().add(context);
        this.noContext = false;
    }

    public void removeContext(UUID contextId) {
        if(this.contexts != null && this.contexts.removeIf(c -> c.getId().equals(contextId))) {
            this.noContext = this.contexts.isEmpty() ? null : false;
        }
    }

    public void removeBehavior(UUID behaviorId) {
        if(this.behaviors != null && this.behaviors.removeIf(c -> c.getId().equals(behaviorId))) {
            this.noBehavior = this.behaviors.isEmpty() ? null : false;
        }
    }

    public boolean removeActivity(JobActivityLabel activity) {
        var removed = this.activities != null && this.activities.removeIf(Predicate.isEqual(activity));
        if (removed) {
            this.noActivity = this.activities.isEmpty() ? null : false;
        }
        return removed;
    }

    public void replaceActivities(Collection<JobActivityLabel> oldLabels, JobActivityLabel newLabel) {
        this.activities.removeIf(a -> oldLabels.stream().anyMatch(o -> o.getUuid().equals(a.getUuid())));
        this.activities.add(newLabel);
    }

    public boolean isQualified() {
        return noActivity != null && noContext != null && noBehavior != null;
    }
}
