package com.erhgo.domain.classifications.erhgooccupation;

import com.erhgo.domain.classifications.esco.EscoSkill;
import com.erhgo.domain.referential.Context;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import lombok.*;

import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Entity
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class OccupationContext extends AbstractMandatoryOccupationEntity {

    @ManyToOne(optional = false)
    @Getter
    private Context context;

    @Builder
    public OccupationContext(Context context, ErhgoOccupation occupation, OccupationQualificationSource occupationQualificationSource) {
        super(occupationQualificationSource, occupation);
        this.context = context;
    }

    @Override
    public Set<EscoSkill> filterSkillsWithEntity(Set<EscoSkill> skills) {
        return skills.stream().filter(s -> s.getContexts().stream()
                .anyMatch(c -> c.getId().equals(context.getId())))
                .collect(Collectors.toSet());
    }

    @Override
    public UUID getEntityUuid() {
        return context.getId();
    }
}
