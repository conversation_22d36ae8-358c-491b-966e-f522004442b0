package com.erhgo.domain.classifications.esco;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.classifications.ComparableOccupation;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.classifications.isco.IscoOccupation;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.JobActivityLabel;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.annotations.Cascade;
import org.hibernate.annotations.CascadeType;
import org.hibernate.validator.constraints.Length;

import java.util.List;
import java.util.Set;
import java.util.stream.Stream;

@Data
@Builder
@Entity
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true, onlyExplicitlyIncluded = true)
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
public class EscoOccupation extends AbstractAuditableEntity implements ComparableOccupation {

    @Id
    @ToString.Include
    @EqualsAndHashCode.Include
    private String uri;

    @NotNull
    @ToString.Include
    @EqualsAndHashCode.Include
    private String title;

    @ElementCollection
    private Set<String> alternativeLabels;

    @Column(length = 2000)
    @Length(max = 2000)
    private String descriptionEN;

    @Column(length = 2000)
    @Length(max = 2000)
    private String descriptionFR;

    @ManyToOne
    private IscoOccupation iscoOccupation;

    @ManyToMany
    @Cascade(CascadeType.MERGE)
    private Set<EscoSkill> skills;

    // FIXME: ERHGO-378 remove level in EscoOccupation
    @Deprecated
    @Enumerated(EnumType.ORDINAL)
    private MasteryLevel level;

    public boolean isFullyQualified() {
        return getSkills().stream().noneMatch(s -> s.getNoActivity() == null && (s.getActivities() == null || s.getActivities().isEmpty()));
    }

    // Return all capacities, including duplicates, for later counting
    @Override
    public List<Capacity> getAllCapacitiesWithDuplicates() {
        return getSkillsActivitiesStream()
                .flatMap(a -> a.getInducedCapacities().stream())
                .toList();
    }

    private Stream<JobActivityLabel> getSkillsActivitiesStream() {
        return getSkills().stream()
                .filter(e -> e.getActivities() != null)
                .flatMap(e -> e.getActivities().stream());
    }

    @Override
    public int getLevelAsInt() {
        return getLevel() == null ? null : getLevel().getMasteryLevel();
    }
}
