package com.erhgo.domain.classifications.erhgooccupation;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.classifications.ComparableOccupation;
import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.domain.classifications.esco.EscoOccupation;
import com.erhgo.domain.classifications.esco.EscoSkill;
import com.erhgo.domain.classifications.isco.IscoOccupation;
import com.erhgo.domain.classifications.rome.RomeOccupation;
import com.erhgo.domain.classifications.workenvironment.WorkEnvironment;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.BehaviorCategory;
import com.erhgo.domain.enums.CriteriaQuestionType;
import com.erhgo.domain.exceptions.LabelDuplicatesTitleException;
import com.erhgo.domain.exceptions.MultipleThresholdValuesException;
import com.erhgo.domain.exceptions.NoSuchEntityInOccupation;
import com.erhgo.domain.referential.Behavior;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.JobActivityLabel;
import com.google.common.collect.Sets;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.apache.logging.log4j.util.Strings;
import org.hibernate.annotations.DynamicUpdate;

import org.springframework.context.annotation.Lazy;

import java.util.*;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.apache.commons.lang3.StringUtils.trimToEmpty;

@Data
@Builder
@Entity
@NoArgsConstructor
@AllArgsConstructor
@ToString(onlyExplicitlyIncluded = true)
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@DynamicUpdate
public class ErhgoOccupation extends AbstractAuditableEntity implements ComparableOccupation {

    @Id
    @Column(columnDefinition = "BINARY(16)")
    @ToString.Include
    @EqualsAndHashCode.Include
    @Builder.Default
    private UUID id = UUID.randomUUID();

    @Column(columnDefinition = "LONGTEXT")
    private String description;

    @NotNull
    @Setter(AccessLevel.PACKAGE)
    @ToString.Include
    @EqualsAndHashCode.Include
    public String title;

    @ElementCollection
    @Builder.Default
    @Setter(AccessLevel.PACKAGE)
    private Set<String> alternativeLabels = new HashSet<>();

    @ManyToMany
    @Builder.Default
    private Set<EscoOccupation> escoOccupations = new HashSet<>();

    @ManyToMany
    @Builder.Default
    private Set<EscoSkill> skills = new HashSet<>();

    @OneToMany(mappedBy = "erhgoOccupation", cascade = CascadeType.ALL, orphanRemoval = true)
    @Builder.Default
    private Set<RomeOfErhgoOccupation> romeOfErhgoOccupations = new HashSet<>();


    @ManyToMany
    @Builder.Default
    private Set<WorkEnvironment> workEnvironments = new HashSet<>();

    @ManyToMany
    @Builder.Default
    private Set<ErhgoClassification> erhgoClassifications = new HashSet<>();

    @Enumerated(EnumType.ORDINAL)
    @Builder.Default
    private ErhgoOccupationState qualificationState = ErhgoOccupationState.NONE;

    @Enumerated(EnumType.ORDINAL)
    @Setter(AccessLevel.PRIVATE)
    private MasteryLevel level;

    @Enumerated(EnumType.STRING)
    private OccupationCreationReason occupationCreationReason;

    @OneToMany(mappedBy = "occupation", cascade = CascadeType.ALL, orphanRemoval = true)
    @Lazy
    private Set<AbstractOccupationEntity> entities;

    @Enumerated(EnumType.STRING)
    private BehaviorCategory behaviorCategory1;

    private boolean isBehaviorCategory1Overloaded;

    @Enumerated(EnumType.STRING)
    private BehaviorCategory behaviorCategory2;

    private boolean isBehaviorCategory2Overloaded;

    @Enumerated(EnumType.STRING)
    private BehaviorCategory behaviorCategory3;

    private boolean isBehaviorCategory3Overloaded;

    @Column(columnDefinition = "LONGTEXT")
    private String behaviorsDescription;

    @ManyToMany
    @Builder.Default
    private Set<CriteriaValue> criteriaValues = new HashSet<>();

    @Builder.Default
    private boolean technical = true;

    public void qualifyOccupation() {
        this.qualificationState = ErhgoOccupationState.QUALIFIED_V3_CONFIRMED;
    }

    public void unqualifyOccupation() {
        this.qualificationState = ErhgoOccupationState.TO_CONFIRM;
        computeQualificationState();
    }

    public ErhgoOccupation computeQualificationState() {
        if (this.qualificationState == ErhgoOccupationState.TO_CONFIRM
                || this.qualificationState.isQualified()) {
            return this;
        }

        if (this.skills == null || this.entities == null) {
            this.qualificationState = ErhgoOccupationState.NONE;
            return this;
        }

        var totalSkills = this.skills.size();
        var qualifiedSkills = this.skills.stream().map(EscoSkill::isQualified).count();
        var qualifiedSkillsPercent = (double) qualifiedSkills / totalSkills;
        var activitiesCount = this.entities.stream().map(OccupationActivity.class::isInstance).count();

        this.qualificationState = ErhgoOccupationState.getByActivitiesAndQualifiedSkills(activitiesCount, qualifiedSkillsPercent);
        return this;
    }

    public void addRome(RomeOccupation romeOccupation) {
        this.romeOfErhgoOccupations.add(new RomeOfErhgoOccupation(this, romeOccupation));
    }

    public void addEscoOccupation(EscoOccupation escoOccupation) {
        this.escoOccupations.add(escoOccupation);
        escoOccupation.getSkills().forEach(this::addSkillAndCascadingEntities);
    }

    public void addSkillAndCascadingEntities(EscoSkill escoSkill) {
        if (this.skills == null) {
            this.setSkills(new HashSet<>());
        }
        this.skills.add(escoSkill);

        var activities = escoSkill.getActivities();
        var contexts = escoSkill.getContexts();
        var behaviors = escoSkill.getBehaviors();

        if (activities != null) {
            activities.forEach(this::addQualifiedActivity);
        }
        if (contexts != null) {
            contexts.forEach(this::addQualifiedContext);
        }
        if (behaviors != null) {
            behaviors.forEach(this::addQualifiedBehavior);
        }
        computeQualificationState();
    }

    public void removeRome(RomeOccupation romeOccupation) {
        var romeToRemove = this.romeOfErhgoOccupations.stream().filter(r -> r.getRomeCode().equals(romeOccupation.getCode())).findFirst();
        romeToRemove.ifPresent(r -> {
            romeOfErhgoOccupations.remove(r);
            r.remove();
        });
    }

    public void removeEscoOccupation(EscoOccupation escoOccupation) {
        if (this.escoOccupations != null) {
            this.escoOccupations.remove(escoOccupation);
            escoOccupation
                    .getSkills()
                    .stream()
                    .filter(escoSkill -> !getEscoOccupationsSkills().contains(escoSkill))
                    .forEach(this::removeSkill);
        }
    }

    private List<EscoSkill> getEscoOccupationsSkills() {
        return this.escoOccupations
                .stream()
                .flatMap(escoOccupation -> escoOccupation.getSkills().stream())
                .toList();
    }

    public void removeSkill(EscoSkill skill) {
        if (this.skills != null) {
            this.skills.remove(skill);
        }
        computeQualificationState();
    }

    public void removeEntityById(UUID entityId) {
        getOptionalOccupationEntity(entityId)
                .ifPresent(AbstractOccupationEntity::remove);
    }

    public void setEntityMandatoryState(UUID entityId, MandatoryState mandatoryState) {
        getOptionalOccupationEntity(entityId)
                .ifPresentOrElse(occupationEntity -> occupationEntity.setState(mandatoryState), () -> {
                    throw new NoSuchEntityInOccupation(entityId, this.id);
                });
    }

    private <A extends AbstractOccupationEntity> Optional<A> getOptionalOccupationEntity(UUID entityId, Class<A> clazz) {
        return this.entities.stream().filter(a -> a.getEntityUuid().equals(entityId))
                .map(clazz::cast)
                .findFirst();
    }

    private Optional<AbstractOccupationEntity> getOptionalOccupationEntity(UUID entityId) {
        return getOptionalOccupationEntity(entityId, AbstractOccupationEntity.class);
    }

    private boolean hasEntity(UUID uuid) {
        return getOptionalOccupationEntity(uuid).isPresent();
    }

    public void removeQualifiedEntity(UUID entityUuid) {
        if (shouldRecoverSkillQualification()) {
            getOptionalOccupationEntity(entityUuid, AbstractOccupationEntity.class)
                    .filter(AbstractOccupationEntity::shouldRemoveFromOccupation)
                    .ifPresent(AbstractOccupationEntity::remove);
        }
    }

    private boolean shouldRecoverSkillQualification() {
        return !this.qualificationState.isQualified();
    }

    public void addContext(Context context) {
        addEntityIfAbsent(() -> OccupationContext.builder()
                .context(context)
                .occupationQualificationSource(OccupationQualificationSource.MANUAL)
                .occupation(this)
                .build(), context.getId());

    }

    public void addBehavior(Behavior behavior) {
        addEntityIfAbsent(() -> OccupationBehavior.builder()
                .behavior(behavior)
                .occupationQualificationSource(OccupationQualificationSource.MANUAL)
                .occupation(this)
                .build(), behavior.getId());
        updateBehaviorCategories();
    }

    public void addActivity(JobActivityLabel activity) {
        addEntityIfAbsent(() -> OccupationActivity.builder()
                .activity(activity)
                .occupationQualificationSource(OccupationQualificationSource.MANUAL)
                .occupation(this)
                .build(), activity.getUuid());
        computeQualificationState();
    }

    public void addActivities(List<JobActivityLabel> activities) {
        activities.forEach(a -> addEntityIfAbsent(() -> OccupationActivity.builder()
                .activity(a)
                .occupationQualificationSource(OccupationQualificationSource.MANUAL)
                .occupation(this)
                .build(), a.getUuid()));
        computeQualificationState();
    }

    private <A extends AbstractOccupationEntity> void addEntityIfAbsent(Supplier<A> supplier, UUID entityUuid) {
        if (this.entities == null) {
            this.entities = new HashSet<>();
        }
        if (!hasEntity(entityUuid)) {
            this.entities.add(supplier.get());
        }
        computeQualificationState();
    }

    private <A extends AbstractOccupationEntity> Set<A> getEntitiesOfType(Class<A> clazz) {
        return this.entities == null ? Collections.emptySet() : this.entities.stream().filter(e -> clazz.isAssignableFrom(e.getClass()))
                .map(clazz::cast)
                .collect(Collectors.toSet());
    }

    public Set<OccupationActivity> getOccupationActivities() {
        return getEntitiesOfType(OccupationActivity.class);
    }

    public List<JobActivityLabel> getActivitiesForMatching(boolean mandatoryOnly) {
        return getOccupationActivities()
                .stream()
                .filter(occupationActivity -> !mandatoryOnly || occupationActivity.getState() == MandatoryState.ESSENTIAL)
                .map(OccupationActivity::getActivity)
                .toList();
    }

    public Set<JobActivityLabel> getActivities() {
        return getOccupationActivities() == null ? Collections.emptySet() : getOccupationActivities().stream().map(OccupationActivity::getActivity).collect(Collectors.toUnmodifiableSet());
    }

    public Set<OccupationContext> getOccupationContexts() {
        return getEntitiesOfType(OccupationContext.class);
    }

    public List<Context> getContexts() {
        return getOccupationContexts() == null ? Collections.emptyList() : getOccupationContexts().stream().map(OccupationContext::getContext).toList();
    }

    public Set<OccupationBehavior> getOccupationBehaviors() {
        return getEntitiesOfType(OccupationBehavior.class);
    }

    public Set<Behavior> getBehaviors() {
        return getOccupationBehaviors() == null ? Collections.emptySet() : getOccupationBehaviors().stream().map(OccupationBehavior::getBehavior).collect(Collectors.toSet());
    }

    public void addQualifiedActivity(JobActivityLabel activity) {
        if (shouldRecoverSkillQualification() && !hasEntity(activity.getUuid())) {
            this.entities.add(OccupationActivity
                    .builder()
                    .occupation(this)
                    .occupationQualificationSource(OccupationQualificationSource.SKILL)
                    .activity(activity)
                    .build());
        }
        computeQualificationState();
    }

    public void addQualifiedContext(Context context) {
        if (shouldRecoverSkillQualification() && !hasEntity(context.getId())) {
            this.entities.add(OccupationContext
                    .builder()
                    .occupation(this)
                    .occupationQualificationSource(OccupationQualificationSource.SKILL)
                    .context(context)
                    .build());
        }
    }

    public void addQualifiedBehavior(Behavior behavior) {
        if (shouldRecoverSkillQualification() && !hasEntity(behavior.getId())) {
            this.entities.add(OccupationBehavior
                    .builder()
                    .occupation(this)
                    .occupationQualificationSource(OccupationQualificationSource.SKILL)
                    .behavior(behavior)
                    .build());
        }
        updateBehaviorCategories();
    }

    public void clearAllActivities() {
        this.entities.removeAll(getOccupationActivities());
    }

    public void clearBehaviors() {
        this.entities.removeAll(getOccupationBehaviors());
        updateBehaviorCategories();
    }

    public void replaceActivities(Collection<JobActivityLabel> oldLabels, JobActivityLabel newLabel) {
        var activitiesToReplace = this.entities.stream()
                .filter(e -> oldLabels.stream().anyMatch(a -> a.getUuid().equals(e.getEntityUuid())))
                .map(OccupationActivity.class::cast)
                .collect(Collectors.toSet());

        var state = activitiesToReplace.stream().anyMatch(a -> a.getState() == MandatoryState.ESSENTIAL) ? MandatoryState.ESSENTIAL : MandatoryState.OPTIONAL;
        var source = activitiesToReplace.stream().anyMatch(a -> a.getSource() == OccupationQualificationSource.MANUAL) ? OccupationQualificationSource.MANUAL : OccupationQualificationSource.SKILL;
        activitiesToReplace.forEach(OccupationActivity::remove);
        if (!this.hasEntity(newLabel.getUuid())) {
            var newActivity = OccupationActivity.builder()
                    .activity(newLabel)
                    .occupationQualificationSource(source)
                    .occupation(this)
                    .build();
            newActivity.setState(state);
            this.entities.add(newActivity);
        }
        computeQualificationState();
    }

    public void setTitleAndAlternativeLabels(String title, Collection<String> labels) {
        if (Strings.isBlank(title) || labels.stream().anyMatch(Strings::isBlank)) {
            throw new IllegalArgumentException("Title or label cannot be blank, null or empty");
        }
        if (labels.stream().anyMatch(s -> s.trim().equals(title.trim()))) {
            throw new LabelDuplicatesTitleException(this, title);
        }
        this.alternativeLabels.clear();
        this.alternativeLabels.addAll(labels);
        this.title = title;
        this.fixTitles();
    }

    public void updateBehaviorCategory(BehaviorCategory behaviorCategory, int occupationBehaviorCategoryIndex) {
        var overloaded = behaviorCategory != null;
        switch (occupationBehaviorCategoryIndex) {
            case 0 -> {
                this.behaviorCategory1 = behaviorCategory;
                this.isBehaviorCategory1Overloaded = overloaded;
            }
            case 1 -> {
                this.behaviorCategory2 = behaviorCategory;
                this.isBehaviorCategory2Overloaded = overloaded;
            }
            case 2 -> {
                this.behaviorCategory3 = behaviorCategory;
                this.isBehaviorCategory3Overloaded = overloaded;
            }
            default ->
                    throw new IllegalArgumentException("occupationBehaviorCategoryIndex should be between 0 & 2 inclusive");
        }
        this.updateBehaviorCategories();
    }

    public void updateBehaviorCategories() {
        if (hasAnyBehaviorCategoryUpdated()) {
            var orderedBehaviorCategories = getBehaviorsCategoriesSortedByReverseCount();

            var behaviorsIterator = orderedBehaviorCategories.iterator();
            if (!this.isBehaviorCategory1Overloaded) {
                this.behaviorCategory1 = nextOrNull(behaviorsIterator);
            }
            if (!this.isBehaviorCategory2Overloaded) {
                this.behaviorCategory2 = nextOrNull(behaviorsIterator);
            }
            if (!this.isBehaviorCategory3Overloaded) {
                this.behaviorCategory3 = nextOrNull(behaviorsIterator);
            }
        }
    }

    private BehaviorCategory nextOrNull(Iterator<BehaviorCategory> behaviorsIterator) {
        return behaviorsIterator.hasNext() ? behaviorsIterator.next() : null;
    }

    private boolean hasAnyBehaviorCategoryUpdated() {
        return !this.isBehaviorCategory1Overloaded || !this.isBehaviorCategory2Overloaded || !this.isBehaviorCategory3Overloaded;
    }

    private List<BehaviorCategory> getBehaviorsCategoriesSortedByReverseCount() {
        return getOccupationBehaviors()
                .stream()
                .map(occupationBehavior -> occupationBehavior.getBehavior().getBehaviorCategory())
                .sorted(Comparator.comparingInt(BehaviorCategory::ordinal))
                .collect(Collectors.groupingBy(behaviorCategory -> behaviorCategory, LinkedHashMap::new, Collectors.counting()))
                .entrySet()
                .stream()
                .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
                .map(Map.Entry::getKey)
                .filter(category -> (
                        !isBehaviorCategory1Overloaded || category != behaviorCategory1)
                        && (!isBehaviorCategory2Overloaded || category != behaviorCategory2)
                        && (!isBehaviorCategory3Overloaded || category != behaviorCategory3)
                )
                .toList();
    }

    @Override
    public List<Capacity> getAllCapacitiesWithDuplicates() {
        return getInducedCapacitiesStream()
                .toList();
    }

    private Stream<Capacity> getInducedCapacitiesStream() {
        return getActivities()
                .stream()
                .flatMap(a -> a.getInducedCapacities().stream());
    }

    public boolean isQualificationInFinalState() {
        return qualificationState.isFinalState();
    }

    @Override
    public int getLevelAsInt() {
        return getLevel() == null ? MasteryLevel.DEFAULT_LEVEL_AS_INT : getLevel().getMasteryLevel();
    }

    public void mergeOccupations(ErhgoOccupation occupationToDelete, boolean ignoreActivities) {

        var labelsOfOccupationToDelete = occupationToDelete.getAlternativeLabels();
        labelsOfOccupationToDelete.add(occupationToDelete.title);

        setAlternativeLabels(Sets.newTreeSet(Sets.union(this.alternativeLabels, labelsOfOccupationToDelete)));

        setEscoOccupations(Sets.union(this.escoOccupations, occupationToDelete.getEscoOccupations()).immutableCopy());

        var newRomes = occupationToDelete.romeOfErhgoOccupations.stream().filter(Predicate.not(this::containsRome)).map(r -> r.copyToOccupation(this)).toList();
        this.romeOfErhgoOccupations.addAll(newRomes);

        setSkills(Sets.union(this.skills, occupationToDelete.getSkills()).immutableCopy());

        occupationToDelete.getEntities().stream()
                .filter(entity -> !(ignoreActivities && entity instanceof OccupationActivity) && !hasEntity(entity.getEntityUuid()))
                .forEach(entity -> {
                    entity.setOccupation(this);
                    entities.add(entity);
                });

        if (qualificationState.isQualified()) {
            this.qualificationState = ErhgoOccupationState.NONE;
        }
        computeQualificationState();
    }

    private boolean containsRome(RomeOfErhgoOccupation romeOfErhgoOccupation) {
        return romeOfErhgoOccupations.stream().anyMatch(r -> r.getRomeCode().equals(romeOfErhgoOccupation.getRomeCode()));
    }

    public boolean updateLevel(MasteryLevel masteryLevel) {
        var levelUpdated = masteryLevel != this.level;
        this.level = masteryLevel;
        return levelUpdated;
    }

    public int getNumberOfCapacities() {
        return (int) getInducedCapacitiesStream()
                .distinct()
                .count();
    }

    public Integer getNumberOfActivities() {
        return getOccupationActivities().size();
    }

    public boolean fixTitles() {
        var fixedTitle = getSanitizedTitle(this.getTitle());
        var isTitleModified = !fixedTitle.equals(this.title);
        this.setTitle(fixedTitle);
        return this.fixLabels() || isTitleModified;
    }

    private String getSanitizedTitle(String title) {
        return Stream.of(title.split("/"))
                .map(String::trim)
                .map(org.apache.commons.lang3.StringUtils::capitalize)
                .distinct()
                .filter(Predicate.not(String::isEmpty))
                .collect(Collectors.joining(" / "));
    }

    private boolean fixLabels() {
        var sanitizedLabels = this.getAlternativeLabels()
                .stream()
                .map(this::getSanitizedTitle)
                .collect(Collectors.toSet());
        var isAnyLabelModified = !sanitizedLabels.containsAll(this.alternativeLabels);
        var newLabels = sanitizedLabels.stream().filter(a -> !this.alternativeLabels.contains(a)).collect(Collectors.toSet());
        this.alternativeLabels.removeIf(a -> !sanitizedLabels.contains(a));
        this.alternativeLabels.addAll(newLabels);
        return isAnyLabelModified;
    }

    public void resetCriteriaValues(Collection<CriteriaValue> criteriaValues) {
        if (criteriaValues.stream()
                .collect(Collectors.groupingBy(CriteriaValue::getCriteria))
                .entrySet()
                .stream()
                .anyMatch(e -> e.getKey().getQuestionType() == CriteriaQuestionType.THRESHOLD && e.getValue().size() > 1)) {
            throw new MultipleThresholdValuesException();
        }
        this.criteriaValues.clear();
        this.criteriaValues.addAll(criteriaValues);
    }

    private Collection<BehaviorCategory> getBehaviorCategories() {
        return Stream.of(behaviorCategory1, behaviorCategory2, behaviorCategory3).filter(Objects::nonNull).toList();
    }

    public Set<Long> getAllCapacitiesId() {
        return getAllCapacitiesWithDuplicates().stream().map(Capacity::getId).collect(Collectors.toSet());
    }

    public String getDescriptionWithBehaviorDescription() {
        return Stream.of(trimToEmpty(description), trimToEmpty(behaviorsDescription)).filter(Predicate.not(String::isBlank)).collect(Collectors.joining("<br/>"));
    }

    public void resetErhgoClassifications(List<ErhgoClassification> newClassifications) {
        this.erhgoClassifications.removeIf(a -> newClassifications.stream().noneMatch(b -> b.getCode().equals(a.getCode())));
        this.erhgoClassifications.addAll(newClassifications);
    }

    public boolean hasNoCode() {
        return this.getEscoOccupations().isEmpty() && this.getRomeOccupations().isEmpty();
    }

    public Set<RomeOccupation> getRomeOccupations() {
        return romeOfErhgoOccupations.stream().map(RomeOfErhgoOccupation::getRomeOccupation).collect(Collectors.toUnmodifiableSet());
    }

    public List<String> getRomeCodes() {
        return romeOfErhgoOccupations
                .stream()
                .map(RomeOfErhgoOccupation::getRomeCode)
                .sorted()
                .toList();
    }

    public List<Integer> getIscoCodes() {
        return this.getEscoOccupations()
                .stream()
                .map(EscoOccupation::getIscoOccupation)
                .filter(Objects::nonNull)
                .map(IscoOccupation::getIscoGroup)
                .toList();
    }

    public List<String> getAccessibleFromRomeCodes() {
        return getAccessibleFromRomeOccupations().stream().map(RomeOccupation::getCode).sorted().toList();
    }

    public Collection<RomeOccupation> getAccessibleFromRomeOccupations() {
        return getRomeOccupations().stream().flatMap(o -> o.getAccessibleFromRomeOccupations().stream()).collect(Collectors.toSet());
    }

    public Collection<RomeOccupation> getAccessibleRomeOccupations() {
        return getRomeOccupations().stream().flatMap(o -> o.getAccessibleRomeOccupations().stream()).collect(Collectors.toSet());
    }

    public boolean isQualifiedEnough() {
        return qualificationState.isQualifiedEnough();
    }

    public void resetRomeOccupations(List<RomeOccupation> newRomeOccupations) {
        this.romeOfErhgoOccupations.removeIf(r -> !newRomeOccupations.contains(r.getRomeOccupation()));
        var newRomes = newRomeOccupations.stream().filter(r -> this.romeOfErhgoOccupations.stream().noneMatch(o -> o.getRomeOccupation().equals(r))).toList();
        newRomes.forEach(this::addRome);
    }

    public boolean hasAnyCapacity() {
        return getInducedCapacitiesStream().findAny().isPresent();
    }
}
