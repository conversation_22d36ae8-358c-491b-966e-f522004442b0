package com.erhgo.domain.utils;

import com.google.common.annotations.VisibleForTesting;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class EventPublisherUtils {

    @VisibleForTesting
    public static ApplicationEventPublisher applicationEventPublisher;

    @SuppressWarnings({"java:S1118", "java:S3010"})
    public EventPublisherUtils(@Autowired ApplicationEventPublisher applicationEventPublisher) {
        EventPublisherUtils.applicationEventPublisher = applicationEventPublisher;
    }

    public static <A> void publish(A event) {
        if (applicationEventPublisher != null) {
            applicationEventPublisher.publishEvent(event);
        } else {
            log.warn("No event publisher - should occur in tests only");
        }
    }

    @VisibleForTesting
    public static void resetPublisher(ApplicationEventPublisher applicationEventPublisher) {
        EventPublisherUtils.applicationEventPublisher = applicationEventPublisher;
    }
}
