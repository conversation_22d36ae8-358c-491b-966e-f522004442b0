package com.erhgo.domain.externaloffer;

import com.erhgo.domain.AbstractAuditableEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.Comparator;
import java.util.UUID;

import static java.util.Comparator.nullsLast;

@Entity
@Slf4j
@NoArgsConstructor
@EqualsAndHashCode(of = {"uuid"}, callSuper = false)
@Getter
public class ExternalOfferContentHistoryItem extends AbstractAuditableEntity implements Comparable<ExternalOfferContentHistoryItem> {

    @Id
    @Column(columnDefinition = "BINARY(16)")
    private UUID uuid = UUID.randomUUID();

    @Column(columnDefinition = "LONGTEXT")
    private String rawContent;

    ExternalOfferContentHistoryItem(String rawContent) {
        this.rawContent = rawContent;
    }

    @Override
    public int compareTo(@NotNull ExternalOfferContentHistoryItem other) {
        return Comparator.comparing(ExternalOfferContentHistoryItem::getUpdatedDate, nullsLast(Comparator.naturalOrder()))
                .reversed()
                .compare(this, other);
    }

}
