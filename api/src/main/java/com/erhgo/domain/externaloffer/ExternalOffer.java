package com.erhgo.domain.externaloffer;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.recruitment.Recruitment;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Entity
@Slf4j
@NoArgsConstructor
@Accessors(chain = true)
@Data
@ToString(exclude = {"lastRawContent", "externalOfferContentHistory", "recruitment"})
@EqualsAndHashCode(of = {"uuid"}, callSuper = false)
@Table(uniqueConstraints = {
        @UniqueConstraint(columnNames = {"atsCode", "remoteId", "computedRecruiterCode"}, name = "UC_REMOTE_ID_ATS_CODE"),
        @UniqueConstraint(columnNames = {"recruitment_id"}, name = "UC_RECRUITMENT_OFFER"),
})
@NamedEntityGraph(
        name = ExternalOffer.ENTITY_GRAPH_WITH_HISTORY,
        attributeNodes = @NamedAttributeNode("externalOfferContentHistory")
)
public class ExternalOffer extends AbstractAuditableEntity {

    public static final int MAX_HISTORY_SIZE = 5;
    public static final String ENTITY_GRAPH_WITH_HISTORY = "Offer.externalOfferContentHistory";
    @Id
    @Column(columnDefinition = "BINARY(16)")
    private UUID uuid = UUID.randomUUID();

    @Getter
    private String remoteId;

    @Column(columnDefinition = "LONGTEXT")
    @Setter(AccessLevel.PRIVATE)
    private String lastRawContent;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "externalOffer_id", nullable = false)
    private SortedSet<ExternalOfferContentHistoryItem> externalOfferContentHistory = new TreeSet<>();

    private String atsCode;
    private String configCode;

    @Enumerated(EnumType.STRING)
    private ExternalOfferEventType lastEventType;

    @Enumerated(EnumType.STRING)
    private RecruitmentCreationState recruitmentCreationState;

    private LocalDateTime remoteLastModificationDate;

    @OneToOne(cascade = {CascadeType.MERGE, CascadeType.REFRESH})
    private Recruitment recruitment;

    private String offerTitle;
    private String offerRecruiterCode;
    private String offerLocation;
    @Getter(AccessLevel.PRIVATE)
    private String relatedUsernames;
    private String computedRecruiterCode;
    private String candidatureEmail;

    public Long getRecruitmentId() {
        return recruitment != null ? recruitment.getId() : null;
    }

    public boolean isSuspended() {
        return lastEventType == ExternalOfferEventType.SUSPENDED;
    }

    public ExternalOffer setLastRawContent(String rawContent, String offerTitle, String remoteRecruiterCode, String location, List<String> relatedUsernames, String candidatureEmail) {
        this.offerTitle = offerTitle;
        this.offerRecruiterCode = remoteRecruiterCode;
        this.offerLocation = location;
        this.relatedUsernames = Optional.ofNullable(relatedUsernames).orElse(Collections.emptyList()).stream().distinct().filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
        if (Strings.isNotBlank(lastRawContent)) {
            if (externalOfferContentHistory.size() >= MAX_HISTORY_SIZE) {
                externalOfferContentHistory.remove(externalOfferContentHistory.last());
            }
            externalOfferContentHistory.add(new ExternalOfferContentHistoryItem(lastRawContent));
        }
        this.candidatureEmail = candidatureEmail;
        lastRawContent = rawContent;
        return this;
    }

    public String getPreviousRawContent() {
        return externalOfferContentHistory.isEmpty() ? null : externalOfferContentHistory.first().getRawContent();
    }

    public List<String> relatedUsernames() {
        return Optional.ofNullable(relatedUsernames).map(StringUtils::trimToNull).map(s -> Stream.of(s.split(",")).toList()).orElse(Collections.emptyList());
    }

    public void setRecruitment(Recruitment recruitment) {
        this.recruitment = recruitment;
        if (recruitment != null) {
            recruitment.setExternalOffer(this);
        }
    }

    public boolean isRecruitmentCreationIgnored() {
        return recruitmentCreationState == RecruitmentCreationState.IGNORE;
    }

    public boolean isRecruitmentCreationManual() {
        return recruitmentCreationState == RecruitmentCreationState.MANUAL;
    }
}
