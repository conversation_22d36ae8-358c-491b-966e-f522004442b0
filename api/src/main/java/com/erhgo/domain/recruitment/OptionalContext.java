package com.erhgo.domain.recruitment;

import com.erhgo.domain.enums.AcquisitionModality;
import com.erhgo.domain.referential.Context;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;


import java.util.UUID;

@Data
@Entity
@Builder
@ToString(exclude = "recruitmentProfile")
@EqualsAndHashCode(exclude = "recruitmentProfile")
@NoArgsConstructor
@AllArgsConstructor
public class OptionalContext {

    @Id
    @Column(columnDefinition = "BINARY(16)")
    @Builder.Default
    private UUID id = UUID.randomUUID();

    @ManyToOne
    private RecruitmentProfile recruitmentProfile;

    @ManyToOne
    private Context context;

    @NotNull
    private AcquisitionModality acquisitionModality;
}
