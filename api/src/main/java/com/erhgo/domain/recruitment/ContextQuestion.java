package com.erhgo.domain.recruitment;

import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.QuestionForContexts;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.util.UUID;

@Data
@Entity
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Table(uniqueConstraints = {@UniqueConstraint(columnNames = {"context_id", "recruitmentProfile_uuid"})})
public class ContextQuestion {

    @Id
    @Column(columnDefinition = "BINARY(16)")
    @Builder.Default
    protected UUID uuid = UUID.randomUUID();

    @NotNull
    @ManyToOne
    private Context context;

    @NotNull
    @ManyToOne
    private RecruitmentProfile recruitmentProfile;

    // FIXME ERHGO-270 non nullable - ContextQuestion should be mandatory for mandatory context
    @ManyToOne(optional = false)
    private QuestionForContexts question;

    public String getQuestionTitle() {
        return question == null ? null : question.getTitle();
    }
}
