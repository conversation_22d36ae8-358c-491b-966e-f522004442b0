package com.erhgo.domain.recruitment;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.enums.AcquisitionModality;
import com.erhgo.domain.exceptions.*;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.job.Mission;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.domain.referential.QuestionForContexts;
import com.erhgo.openapi.dto.AcquisitionModalityDTO;
import com.google.common.collect.Sets;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.validator.constraints.Length;

import java.util.*;
import java.util.stream.Collectors;

@Data
@Entity
@NoArgsConstructor
@ToString(callSuper = true, exclude = {"job", "optionalActivities", "optionalContexts", "contextQuestions", "qualifiedMissions"})
@EqualsAndHashCode(callSuper = false, exclude = {"job", "optionalActivities", "optionalContexts", "contextQuestions", "qualifiedMissions"})
@Builder
@AllArgsConstructor
public class RecruitmentProfile extends AbstractAuditableEntity {

    @Id
    @Column(columnDefinition = "BINARY(16)")
    @Builder.Default
    protected UUID uuid = UUID.randomUUID();

    @NotNull
    @ManyToOne(cascade = {CascadeType.MERGE, CascadeType.REFRESH})
    private Job job;

    private String title;

    @OneToMany(mappedBy = "recruitmentProfile", cascade = CascadeType.ALL, orphanRemoval = true)
    @Builder.Default
    private Set<OptionalActivity> optionalActivities = new HashSet<>();

    @OneToMany(mappedBy = "recruitmentProfile", cascade = CascadeType.ALL, orphanRemoval = true)
    @Builder.Default
    private Set<OptionalContext> optionalContexts = new HashSet<>();

    @ManyToMany
    @Builder.Default
    private Set<Mission> qualifiedMissions = new HashSet<>();

    private boolean qualified;

    @Builder.Default
    private boolean modifiable = true;

    @OneToMany(mappedBy = "recruitmentProfile", cascade = CascadeType.ALL, orphanRemoval = true)
    @Builder.Default
    private Set<ContextQuestion> contextQuestions = Sets.newHashSet();

    @Length(max = 255)
    private String customQuestion;

    public void updateOptionalActivity(UUID activityId, AcquisitionModalityDTO acquisitionModality) {
        var activityLabel = job.getActivity(activityId).orElseThrow(() -> new NoSuchEntityInJob(activityId, job.getId()));

        if (optionalActivities == null) {
            optionalActivities = new HashSet<>(1);
        }

        var optionalOptionalActivity = optionalActivities.stream()
                .filter(o -> o.getActivityLabel().getUuid().equals(activityId))
                .findFirst();
        OptionalActivity optionalActivity;
        if (optionalOptionalActivity.isEmpty()) {
            optionalActivity = OptionalActivity.builder()
                    .id(UUID.randomUUID())
                    .activityLabel(activityLabel)
                    .recruitmentProfile(this)
                    .build();
            optionalActivities.add(optionalActivity);
        } else {
            optionalActivity = optionalOptionalActivity.get();
        }

        optionalActivity.setAcquisitionModality(AcquisitionModality.valueOf(acquisitionModality.name()));
    }

    @SuppressWarnings("java:S2259")
    public OptionalActivity deleteOptionalActivity(UUID activityId) {
        var optionalActivity = Optional.ofNullable(optionalActivities)
                .flatMap(a ->
                        a.stream()
                                .filter(o -> o.getActivityLabel().getUuid().equals(activityId))
                                .findFirst())
                .orElseThrow(() -> new NoSuchActivityInProfile(activityId, uuid.toString()));
        this.optionalActivities.remove(optionalActivity);

        return optionalActivity;
    }

    public void updateOptionalContext(UUID contextId, AcquisitionModalityDTO acquisitionModality) {
        var context = job.getContext(contextId).orElseThrow(() -> new NoSuchContextInJob(contextId, job.getId()));

        if (optionalContexts == null) {
            optionalContexts = new HashSet<>(1);
        }

        var optionalOptionalContext = optionalContexts.stream()
                .filter(o -> o.getContext().getId().equals(contextId))
                .findFirst();
        OptionalContext optionalContext;
        if (optionalOptionalContext.isEmpty()) {
            optionalContext = OptionalContext.builder()
                    .id(UUID.randomUUID())
                    .context(context)
                    .recruitmentProfile(this)
                    .build();
            optionalContexts.add(optionalContext);
        } else {
            optionalContext = optionalOptionalContext.get();
        }

        removeQuestionForContextId(contextId);

        optionalContext.setAcquisitionModality(AcquisitionModality.valueOf(acquisitionModality.name()));

    }

    private void removeQuestionForContextId(UUID contextId) {
        this.contextQuestions.stream()
                .filter(c -> c.getContext().getId().equals(contextId))
                .findFirst()
                .ifPresent(
                        questionToRemove -> {
                            this.contextQuestions.remove(questionToRemove);
                            questionToRemove.setRecruitmentProfile(null);
                        }
                );
    }

    @SuppressWarnings("java:S2259")
    public OptionalContext deleteOptionalContext(UUID contextId) {
        var optionalContext = Optional.ofNullable(optionalContexts)
                .flatMap(a ->
                        a.stream()
                                .filter(o -> o.getContext().getId().equals(contextId))
                                .findFirst())
                .orElseThrow(() -> new NoSuchContextInProfile(contextId, uuid.toString()));

        this.optionalContexts.remove(optionalContext);

        return optionalContext;
    }

    public void addQualifiedMission(Mission mission) {
        if (this.qualifiedMissions == null) {
            this.qualifiedMissions = new HashSet<>(1);
        }
        if (this.qualifiedMissions.stream().noneMatch(m -> m.getId().equals(mission.getId()))) {
            this.qualifiedMissions.add(mission);
        } else {
            throw new MissionAlreadyQualified(mission.getId(), uuid);
        }
        if (qualifiedMissions.size() == job.getMissions().size()) {
            qualified = true;
        }
    }

    public AcquisitionModality getAcquisitionModality(JobActivityLabel jobActivityLabel) {
        return optionalActivities == null ? null : optionalActivities.stream().filter(a -> a.getActivityLabel().getUuid().equals(jobActivityLabel.getUuid())).findFirst().map(OptionalActivity::getAcquisitionModality).orElse(null);
    }

    public AcquisitionModality getAcquisitionModality(Context context) {
        return optionalContexts == null ? null : optionalContexts.stream().filter(a -> a.getContext().getId().equals(context.getId())).findFirst().map(OptionalContext::getAcquisitionModality).orElse(null);
    }

    public Collection<JobActivityLabel> getMandatoryActivities() {

        Collection<JobActivityLabel> activities = this.optionalActivities == null ? Sets.newHashSet() : this.optionalActivities.stream().map(OptionalActivity::getActivityLabel).collect(Collectors.toSet());

            return job.getAllMissionsActivities().stream().filter(a -> !activities.contains(a)).collect(Collectors.toSet());
    }

    public Collection<Context> getMandatoryContexts() {

        Collection<Context> contexts = this.optionalContexts == null ? Sets.newHashSet() : this.optionalContexts.stream().map(OptionalContext::getContext).collect(Collectors.toSet());

        return job.getAllMissionsContexts().stream().filter(a -> !contexts.contains(a)).collect(Collectors.toSet());
    }

    public void updateContextQuestion(Context context, QuestionForContexts question) {
        if (!getMandatoryContexts().contains(context)) {
            throw new ContextIsOptionalInProfile(context.getId(), job.getId());
        }
        if (!question.getContexts().contains(context)) {
            throw new QuestionNotForContextException(question.getUuid(), context.getId());
        }

        var customContextLabel = getContextQuestion(context)
                .orElseGet(() -> {
                    var newContextLabel = ContextQuestion.builder().context(context).recruitmentProfile(this).build();
                    contextQuestions.add(newContextLabel);
                    return newContextLabel;
                });
        customContextLabel.setQuestion(question);
    }

    private Optional<ContextQuestion> getContextQuestion(Context context) {
        return contextQuestions.stream().filter(l -> l.getContext().getId().equals(context.getId())).findFirst();
    }

    public String getLabelForContext(Context context) {
        return getContextQuestion(context).map(ContextQuestion::getQuestionTitle).orElse(context.getTitle());
    }

    public List<Capacity> getMandatoryCapacities() {
        return getMandatoryActivities().stream().flatMap(a -> a.getInducedCapacities().stream()).toList();
    }

    public Optional<QuestionForContexts> getQuestionForContext(Context context) {
        return getContextQuestion(context).map(ContextQuestion::getQuestion);
    }

    public static RecruitmentProfile buildWithAllOptional(Job job, String recruitmentProfileCustomQuestion) {
        var profile = builder()
                .job(job)
                .title("Profil de recrutement par défaut")
                .customQuestion(recruitmentProfileCustomQuestion)
                .build();
        markAllOptional(profile, job);
        profile.qualified = true;
        profile.modifiable = false;
        return profile;
    }


    public void markAllOptional() {
        var profile = this;
        markAllOptional(profile, job);
    }

    private static void markAllOptional(RecruitmentProfile profile, Job job) {
        profile.optionalActivities.forEach(o -> o.setRecruitmentProfile(null));
        profile.optionalContexts.forEach(o -> o.setRecruitmentProfile(null));
        profile.optionalActivities.clear();
        profile.optionalContexts.clear();
        job.getAllMissionsActivities().stream().map(a ->
                OptionalActivity.builder().activityLabel(a).acquisitionModality(AcquisitionModality.INTEGRATION_PROCESS).recruitmentProfile(profile).build()
        ).forEach(profile.optionalActivities::add);
        job.getAllMissionsContexts().stream().map(a ->
                OptionalContext.builder().context(a).acquisitionModality(AcquisitionModality.INTEGRATION_PROCESS).recruitmentProfile(profile).build()
        ).forEach(profile.optionalContexts::add);
    }

    public Collection<JobActivityLabel> getActivities() {
        return job.getAllMissionsActivities();
    }
}
