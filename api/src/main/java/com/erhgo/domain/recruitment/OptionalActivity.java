package com.erhgo.domain.recruitment;

import com.erhgo.domain.enums.AcquisitionModality;
import com.erhgo.domain.referential.JobActivityLabel;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


import java.util.UUID;

@Data
@Builder
@Entity
@NoArgsConstructor
@AllArgsConstructor
public class OptionalActivity {

    @Id
    @Column(columnDefinition = "BINARY(16)")
    @Builder.Default
    private UUID id = UUID.randomUUID();

    @ManyToOne
    private RecruitmentProfile recruitmentProfile;

    @ManyToOne
    private JobActivityLabel activityLabel;

    @NotNull
    private AcquisitionModality acquisitionModality;
}
