package com.erhgo.domain.criteria;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.enums.CriteriaQuestionType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

@Accessors(chain = true)
@Entity
@EqualsAndHashCode(callSuper = true, exclude = "criteriaValues")
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public class Criteria extends AbstractAuditableEntity {

    public static final String CONTRACT_TYPE_CRITERIA_CODE = "CR-1";
    public static final String TYPE_WORKING_TIME = "CR-2";
    public static final String DRIVER_LICENCE_CRITERIA_CODE = "CR-6";
    public static final String SCHOOL_LEVEL_CRITERIA_CODE = "CR-8";
    public static final String SOFTWARE_LEVEL_CRITERIA_CODE = "CR-9";

    @Id
    private String code;

    @Setter
    @NotNull
    @Column(nullable = false)
    private String title;

    @Setter
    @NotNull
    @Column(nullable = false)
    private String questionLabel;

    @Enumerated(EnumType.STRING)
    @NotNull
    @Column(nullable = false)
    private CriteriaQuestionType questionType;

    @Column(nullable = false)
    @Getter(AccessLevel.PUBLIC)
    private boolean required;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true, mappedBy = "criteria")
    @OrderColumn(name = "valueIndex")
    private List<CriteriaValue> criteriaValues;

    private int criteriaIndex;

    @Builder
    public Criteria(String code, String title, String questionLabel, CriteriaQuestionType questionType, List<CriteriaValue> criteriaValues, int criteriaIndex, boolean required) {
        this.code = code;
        this.title = title;
        this.questionLabel = questionLabel;
        this.questionType = questionType;
        this.criteriaIndex = criteriaIndex;
        this.criteriaValues = criteriaValues;
        this.required = required;
        criteriaValues.forEach(c -> c.setCriteria(this));
    }


    public boolean isThreshold() {
        return questionType == CriteriaQuestionType.THRESHOLD;
    }
}
