package com.erhgo.domain.criteria;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.enums.*;
import com.google.common.annotations.VisibleForTesting;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Collection;
import java.util.Collections;
import java.util.EnumMap;
import java.util.Map;

import static com.erhgo.domain.enums.DiplomaLevel.*;

@Entity
@EqualsAndHashCode(of = "code", callSuper = false)
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Table(
        uniqueConstraints = @UniqueConstraint(
                columnNames = {"valueIndex", "criteria_code"},
                name = "CRITERIA_VALUE_INDEX_UNIQ")
)
@Builder
@Getter
@Accessors(chain = true)
public class CriteriaValue extends AbstractAuditableEntity {
    public static final Map<String, DiplomaLevel> DIPLOMA_LEVEL_FOR_CRITERIA_RESPONSE = Map.of(
            "REP-8-1", WITHOUT_DIPLOMA,
            "REP-8-3", CAP_BEP,
            "REP-8-4", BAC_BAC_PRO,
            "REP-8-5", BAC_2,
            "REP-8-6", BAC_3,
            "REP-8-7", BAC_5,
            "REP-8-8", DOCTORATE
    );

    public static final Map<String, DriverLicence> DRIVER_LICENCE_FOR_CRITERIA_RESPONSE = Map.of(
            "REP-6-1", DriverLicence.NONE,
            "REP-6-2", DriverLicence.LICENCE_B_IN_PROGRESS,
            "REP-6-3", DriverLicence.LICENCE_B
    );

    public static final String FULL_TIME_JOB_CODE = "REP-2-1";
    public static final String PART_TIME_JOB_CODE = "REP-2-2";
    public static final Map<String, TypeWorkingTime> WORKING_TIME_FOR_CRITERIA_RESPONSE = Map.of(
            FULL_TIME_JOB_CODE, TypeWorkingTime.FULL_TIME,
            PART_TIME_JOB_CODE, TypeWorkingTime.PART_TIME
    );

    public static final Map<String, TypeContractCategory> TYPE_CONTRACT_FOR_CRITERIA_RESPONSE = Map.of(
            "REP-1-1", TypeContractCategory.PERMANENT,
            "REP-1-2", TypeContractCategory.TEMPORARY,
            "REP-1-3", TypeContractCategory.PRO,
            "REP-1-4", TypeContractCategory.SEASONAL,
            "REP-1-5", TypeContractCategory.INTERIM,
            "REP-1-6", TypeContractCategory.FREELANCE
    );
    public static final String REMOTE_WORK_CRITERIA_VALUE_CODE_PREFIX = "REP-12";

    public static final String NIGHT_WORK_CRITERIA_VALUE_CODE = "REP-3-1";
    public static final String WEEKEND_WORK_CRITERIA_VALUE_CODE = "REP-3-2";
    public static final String REGULAR_MOVE = "REP-10-3";

    public boolean isRelatedToRemoteWork() {
        return code.startsWith(REMOTE_WORK_CRITERIA_VALUE_CODE_PREFIX);
    }

    private static final Map<DiplomaLevel, String> CRITERIA_RESPONSE_FOR_DIPLOMA_LEVEL = new EnumMap<>(DiplomaLevel.class);
    private static final Map<DriverLicence, String> CRITERIA_RESPONSE_FOR_DRIVER_LICENCE = new EnumMap<>(DriverLicence.class);
    private static final Map<TypeWorkingTime, String> CRITERIA_RESPONSE_FOR_WORKING_TIME = new EnumMap<>(TypeWorkingTime.class);
    private static final Map<TypeContractCategory, String> CRITERIA_RESPONSE_FOR_TYPE_CONTRACT = new EnumMap<>(TypeContractCategory.class);


    static {
        DIPLOMA_LEVEL_FOR_CRITERIA_RESPONSE.forEach((k, v) -> CRITERIA_RESPONSE_FOR_DIPLOMA_LEVEL.put(v, k));
        DRIVER_LICENCE_FOR_CRITERIA_RESPONSE.forEach((k, v) -> CRITERIA_RESPONSE_FOR_DRIVER_LICENCE.put(v, k));
        WORKING_TIME_FOR_CRITERIA_RESPONSE.forEach((k, v) -> CRITERIA_RESPONSE_FOR_WORKING_TIME.put(v, k));
        TYPE_CONTRACT_FOR_CRITERIA_RESPONSE.forEach((k, v) -> CRITERIA_RESPONSE_FOR_TYPE_CONTRACT.put(v, k));
    }

    public static String getValueCodeForDiplomaLevel(DiplomaLevel diplomaLevel) {
        return CRITERIA_RESPONSE_FOR_DIPLOMA_LEVEL.get(diplomaLevel);
    }

    public static String getValueCodeForDriverLicence(DriverLicence licence) {
        return CRITERIA_RESPONSE_FOR_DRIVER_LICENCE.get(licence);
    }

    public static String getValueCodeForTypeWorkingTime(TypeWorkingTime workingTime) {
        return CRITERIA_RESPONSE_FOR_WORKING_TIME.get(workingTime);
    }

    public static String getValueCodeForTypeContractCategory(TypeContractCategory typeContract) {
        return CRITERIA_RESPONSE_FOR_TYPE_CONTRACT.get(typeContract);
    }

    public static String getValueCodeForFullRemoteWork() {
        return REMOTE_WORK_CRITERIA_VALUE_CODE_PREFIX + "-3";
    }

    public static String getValueCodeForPartialRemoteWork() {
        return REMOTE_WORK_CRITERIA_VALUE_CODE_PREFIX + "-2";
    }

    @Id
    private String code;

    @ManyToOne(optional = false)
    @Setter(AccessLevel.PACKAGE)
    private Criteria criteria;

    @Setter
    @NotNull
    @Column(nullable = false)
    private String titleForQuestion;

    @Setter
    @NotNull
    @Column(nullable = false)
    private String titleForBO;

    @Setter
    @NotNull
    @Column(nullable = false)
    private String titleStandalone;

    @Setter
    @NotNull
    @Column(nullable = false)
    private String icon;

    private int valueIndex;

    @Enumerated(EnumType.STRING)
    @Setter
    @Getter
    @VisibleForTesting
    private SourcingCriteriaStep sourcingCriteriaStep;

    public boolean shouldReplaceCriteriaValue(CriteriaValue a) {
        return !this.equals(a) &&
                getCriteria().equals(a.getCriteria()) &&
                getCriteria().getQuestionType() == CriteriaQuestionType.THRESHOLD;
    }

    public Collection<CriteriaValue> getHigherValues() {
        if (getCriteria().getQuestionType() == CriteriaQuestionType.MULTIPLE) {
            return Collections.emptySet();
        }
        var allValues = getCriteria().getCriteriaValues();
        return allValues.subList(Math.min(valueIndex + 1, allValues.size()), allValues.size());
    }

    public Collection<CriteriaValue> getValueOrLower() {
        if (getCriteria().getQuestionType() == CriteriaQuestionType.MULTIPLE) {
            return Collections.emptySet();
        }
        var allValues = getCriteria().getCriteriaValues();
        return allValues.subList(0, Math.min(valueIndex + 1, allValues.size()));
    }

    public boolean isLowestThresholdValue() {
        return this.criteria.isThreshold() && valueIndex == 0;
    }

    public boolean isRelatedToContractOrWorkingTime() {
        return WORKING_TIME_FOR_CRITERIA_RESPONSE.containsKey(code) || TYPE_CONTRACT_FOR_CRITERIA_RESPONSE.containsKey(code);
    }
}
