package com.erhgo.domain;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.voodoodyne.jackson.jsog.JSOGGenerator;
import jakarta.persistence.*;
import lombok.*;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.util.Date;

@Data
@NoArgsConstructor
@JsonIdentityInfo(generator = JSOGGenerator.class)
@EqualsAndHashCode(exclude = {"createdBy", "updatedDate", "lastModifiedBy", "createdDate"})
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public abstract class AbstractAuditableEntity {

    @CreatedDate
    @Column(updatable = false)
    @Setter(AccessLevel.PRIVATE)
    private Date createdDate;

    @CreatedBy
    @Embedded
    @AttributeOverride(name = "keycloakId", column = @Column(name = "createdBy_keycloakId"))
    @Column(updatable = false)
    private KeycloakUserSummary createdBy;

    @LastModifiedDate
    private Date updatedDate;

    @LastModifiedBy
    @Embedded
    @AttributeOverride(name = "keycloakId", column = @Column(name = "lastModifiedBy_keycloakId"))
    private KeycloakUserSummary lastModifiedBy;

    @JsonIgnore
    public String getCreatedByUserId() {
        return createdBy == null ? null : createdBy.getKeycloakId();
    }
}
