package com.erhgo.domain.userprofile;

import com.erhgo.domain.AbstractAuditableEntity;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;


import java.util.UUID;

@Builder
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Accessors(fluent = true)
@ToString
@EqualsAndHashCode(of = {"id", "userChannel"}, callSuper = false)
@Table(uniqueConstraints = {@UniqueConstraint(columnNames = {"userProfile_uuid", "channel"})})
public class ChannelAffectation extends AbstractAuditableEntity {
    @Id
    @Column(columnDefinition = "BINARY(16)")
    @Getter
    @Builder.Default
    protected UUID id = UUID.randomUUID();

    @Embedded
    @NonNull
    @Builder.Default
    private UserChannel userChannel = UserChannel.builder().build();

    @ManyToOne(optional = false)
    @Getter
    private UserProfile userProfile;

    public String channel() {
        return userChannel.channel();
    }

    public UserChannel.ChannelSourceType channelSourceType() {
        return userChannel.channelSourceType();
    }

    public boolean isAffectedToChannel(String code) {
        return channel().equals(code);
    }

}
