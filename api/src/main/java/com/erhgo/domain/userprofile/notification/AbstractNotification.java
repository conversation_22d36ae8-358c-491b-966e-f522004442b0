package com.erhgo.domain.userprofile.notification;


import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.enums.NotificationState;
import com.erhgo.domain.enums.NotificationType;
import com.erhgo.domain.userprofile.UserProfile;
import jakarta.persistence.*;
import lombok.*;

import java.io.Serializable;
import java.util.UUID;

@Entity
@NoArgsConstructor(access = AccessLevel.PACKAGE)
@EqualsAndHashCode(callSuper = false, of = "id")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@Table(name = "Notification")
@ToString(callSuper = true)
public abstract class AbstractNotification extends AbstractAuditableEntity {
    @Id
    @Column(columnDefinition = "BINARY(16)")
    @Getter
    private UUID id = UUID.randomUUID();

    @ManyToOne(optional = false)
    @Getter
    private UserProfile userProfile;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Getter
    @Setter
    private NotificationType type;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Getter
    private NotificationState state;

    @Getter
    protected boolean requiresMailSending = false;

    public Serializable getEntityId() {
        return null;
    }

    public abstract String getEntityType();

    public void markAsRead() {
        state = NotificationState.NOT_INTERESTED;
    }

    protected AbstractNotification(UserProfile userProfile, NotificationType type, NotificationState state) {
        this.userProfile = userProfile;
        this.type = type != null ? type : NotificationType.NONE;
        this.state = state != null ? state : NotificationState.NEW;
    }

    public abstract void accept(NotificationVisitor visitor);

    public String getUserId() {
        return userProfile.userId();
    }

    public void mailHandled(boolean mailSent) {
        if (mailSent) {
            type = type == NotificationType.NONE ? NotificationType.EMAIL : NotificationType.BOTH;
        }
        requiresMailSending = false;
    }

    protected boolean alreadySentByMail() {
        return getType() == NotificationType.BOTH || getType() == NotificationType.EMAIL;
    }
}
