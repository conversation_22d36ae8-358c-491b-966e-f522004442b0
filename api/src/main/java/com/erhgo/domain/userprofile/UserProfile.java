package com.erhgo.domain.userprofile;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.classifications.esco.EscoOccupation;
import com.erhgo.domain.classifications.isco.IscoOccupation;
import com.erhgo.domain.classifications.rome.RomeOccupation;
import com.erhgo.domain.criteria.Criteria;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.dto.EmailVerificationResultDTO;
import com.erhgo.domain.dto.event.RemoveCapacityOccurrencesEvent;
import com.erhgo.domain.dto.event.UpdateCapacityOccurrencesEvent;
import com.erhgo.domain.dto.event.UserAffectedToChannelsEvent;
import com.erhgo.domain.enums.*;
import com.erhgo.domain.exceptions.UserNotAllowedForEntity;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.referential.*;
import com.erhgo.domain.userprofile.capacityrelatedquestion.AnswerForCapacityRelatedQuestion;
import com.erhgo.domain.userprofile.criteria.UserCriteriaValue;
import com.erhgo.domain.userprofile.experience.AbstractContextMet;
import com.erhgo.domain.userprofile.experience.JobContextMet;
import com.erhgo.domain.userprofile.experience.UserExperience;
import com.erhgo.domain.utils.EventPublisherUtils;
import com.erhgo.repositories.dto.CriteriaValueForUserDTO;
import com.erhgo.services.generation.dto.TitleAndDescription;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Sets;
import jakarta.persistence.*;
import jakarta.validation.Valid;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.set.UnmodifiableSet;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.erhgo.utils.DateTimeUtils.ZONE_ID;


@Data
@Entity
@Slf4j
@NoArgsConstructor
@Accessors(fluent = true)
@EqualsAndHashCode(of = {"uuid", "userId"}, callSuper = false)
@ToString(of = {"userId"})
public class UserProfile extends AbstractAuditableEntity {

    @Id
    @Column(columnDefinition = "BINARY(16)")
    protected UUID uuid = UUID.randomUUID();

    @OneToMany(mappedBy = "userProfile", cascade = CascadeType.ALL)
    private Set<CapacityOccurrence> capacityOccurrences;

    // User identifier in authentication provider (typically keycloak)
    @Column(unique = true, nullable = false)
    private String userId;

    @OneToOne(mappedBy = "userProfile", orphanRemoval = true, cascade = CascadeType.ALL)
    private GeneralInformation generalInformation;

    @Getter(AccessLevel.PUBLIC)
    private LocalDateTime lastConnectionDate = LocalDateTime.now();

    @Column(nullable = false)
    @Getter(AccessLevel.PUBLIC)
    private boolean taggedNoExperience;

    @OneToMany(mappedBy = "userProfile", orphanRemoval = true, cascade = CascadeType.ALL)
    private Set<UserExperience> experiences = new HashSet<>();

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "userProfile", orphanRemoval = true)
    private Set<@Valid AbstractContextMet> contextsMet = new HashSet<>();

    @OneToMany(mappedBy = "userProfile", cascade = {CascadeType.REMOVE, CascadeType.MERGE, CascadeType.REFRESH})
    private Set<AnswerForCapacityRelatedQuestion> answerForCapacityRelatedQuestions;

    // Denormalized user mastery level; 0 means user has no experience; for user with experiences: between 1 and 5
    @Setter(value = AccessLevel.PRIVATE)
    private float masteryLevel;
    @Embedded
    @Getter(AccessLevel.PUBLIC)
    private UserChannel prescriber = UserChannel.builder().build();

    @Lob
    @Column
    @Getter
    @Setter
    private String source;

    private Boolean isFromHandicap;

    private Boolean handicapModeEnabled;

    @OneToMany(mappedBy = "userProfile", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<ChannelAffectation> affectationsHistory = new HashSet<>();
    @Embedded
    @Getter(AccessLevel.PUBLIC)
    private UserRegistrationState userRegistrationState = UserRegistrationState.builder().build();

    @Embedded
    @Getter(AccessLevel.PUBLIC)
    private MailConfiguration mailConfiguration = MailConfiguration.builder().build();

    @OneToMany(mappedBy = "userProfile", orphanRemoval = true, cascade = CascadeType.ALL)
    @Setter(value = AccessLevel.PACKAGE)
    @VisibleForTesting
    private Set<UserCriteriaValue> userCriteriaValues = new HashSet<>();

    @OneToMany(mappedBy = "userProfile", orphanRemoval = true, cascade = CascadeType.ALL)
    private Set<UserNote> userNotes = new HashSet<>();


    @ManyToMany
    @Setter(value = AccessLevel.PRIVATE)
    private Set<Behavior> behaviors = new HashSet<>();

    @OneToMany(mappedBy = "userProfile", orphanRemoval = true, cascade = CascadeType.ALL)
    @Setter(value = AccessLevel.PRIVATE)
    private Set<UserErhgoClassification> erhgoClassifications = new HashSet<>();

    @ManyToMany
    @Setter(value = AccessLevel.PRIVATE)
    private Set<ErhgoOccupation> blacklistedOccupations = new HashSet<>();

    private Date indexationRequiredDate;

    private Date lastIndexationDate;

    //TODO ERHGO-1500: next flag is used to identify how an update of capacities of activities impacts user capacities;
    // => Either remove listeners mechanism or add batch to nightly update capacities of dirties users
    private Boolean requiresCapacitiesRefresh;

    @Embedded
    private TrimojiStatus trimojiStatus;

    @Embedded
    @AttributeOverride(name = "attitude", column = @Column(name = "userBehaviorDescription_attitude"))
    @AttributeOverride(name = "checksum", column = @Column(name = "userBehaviorDescription_checksum"))
    @AttributeOverride(name = "attitudeModifiedByUserInstant", column = @Column(name = "userBehaviorDescription_modifiedByUserInstant"))
    private UserExperienceGeneratedData userExperienceGeneratedData;

    @Embedded
    @AttributeOverride(name = "state", column = @Column(name = "userFileImport_state"))
    @AttributeOverride(name = "uploadedDateTime", column = @Column(name = "userFileImport_uploadedDateTime"))
    private UserFileImportState userFileImportState;

    public UserProfile addCapacities(Collection<Capacity> newCapacities) {

        if (capacityOccurrences == null) {
            capacityOccurrences = Sets.newHashSet();
        }

        if (!newCapacities.isEmpty()) {
            var capacityOccurrencesAdded = new HashSet<CapacityOccurrence>();
            newCapacities.forEach(c -> addCapacityOccurrence(c, capacityOccurrencesAdded));
            this.refreshMasteryLevelAndMarkAsIndexationRequired();
            dirtiesUserProfile();
            EventPublisherUtils.publish(UpdateCapacityOccurrencesEvent.forOccurrences(capacityOccurrencesAdded));
        }

        return this;
    }

    public Collection<CapacityOccurrence> removeCapacities(Collection<Capacity> capacitiesToRemove) {

        if (capacityOccurrences != null && !capacitiesToRemove.isEmpty()) {
            var capacityOccurrencesToUpdate = new HashSet<CapacityOccurrence>();

            capacitiesToRemove.forEach(capacity ->
                    removeCapacityOccurrence(capacity, capacityOccurrencesToUpdate));
            var toDelete = capacityOccurrences.stream()
                    .filter(CapacityOccurrence::noMoreOccurrence)
                    .collect(Collectors.toSet());
            capacityOccurrences.removeAll(toDelete);
            this.refreshMasteryLevelAndMarkAsIndexationRequired();
            dirtiesUserProfile();
            capacityOccurrencesToUpdate.removeIf(a -> toDelete.stream().anyMatch(b -> b.getCapacity().equals(a.getCapacity())));
            EventPublisherUtils.publish(UpdateCapacityOccurrencesEvent.forOccurrences(capacityOccurrencesToUpdate));
            EventPublisherUtils.publish(RemoveCapacityOccurrencesEvent.forOccurrences(toDelete));
            return toDelete;
        }
        return Collections.emptyList();
    }

    private void removeCapacityOccurrence(Capacity capacity, Collection<CapacityOccurrence> capacityOccurrencesRemoved) {
        removeCapacityOccurrence(capacity, false, capacityOccurrences, false);
        removeCapacityOccurrence(capacity, false, capacityOccurrencesRemoved, true);
    }

    private void removeCapacityOccurrence(Capacity capacity, boolean recursive, Collection<CapacityOccurrence> occurrences, boolean addIfMissing) {
        getCapacityOccurrence(capacity, occurrences).ifPresentOrElse(capacityOccurrence -> capacityOccurrence.decrementOccurrence(recursive), () -> {
            if (addIfMissing) {
                occurrences.add(CapacityOccurrence.builder().capacity(capacity).userProfile(this).recursiveOccurrence(recursive ? -1 : 0).occurrence(recursive ? 0 : -1).build());
            }
        });
        capacity.getInducedCapacities().forEach(inducedCapacity -> removeCapacityOccurrence(inducedCapacity, true, occurrences, addIfMissing));
    }

    private void addCapacityOccurrence(Capacity newCapacity, Collection<CapacityOccurrence> capacityOccurrencesAdded) {
        addCapacityOccurrence(newCapacity, false, capacityOccurrences);
        addCapacityOccurrence(newCapacity, false, capacityOccurrencesAdded);
    }

    private void addCapacityOccurrence(Capacity newCapacity, boolean recursive, Collection<CapacityOccurrence> occurrences) {
        var existingOccurrence = getCapacityOccurrence(newCapacity, occurrences);
        if (existingOccurrence.isPresent()) {
            existingOccurrence.get().incrementOccurrence(recursive);
        } else {
            occurrences.add(CapacityOccurrence.builder()
                    .capacity(newCapacity)
                    .userProfile(this)
                    .occurrence(recursive ? 0 : 1)
                    .recursiveOccurrence(recursive ? 1 : 0)
                    .build()
            );
        }
        if (newCapacity.getInducedCapacities() != null) {
            newCapacity.getInducedCapacities().forEach(c -> addCapacityOccurrence(c, true, occurrences));
        }
    }

    private void computeMasteryLevelBasedOnExperience() {
        var onlyInternshipExperience = experiences.stream().allMatch(experience -> experience.getType() == ExperienceType.INTERNSHIP);
        if (onlyInternshipExperience) {
            this.masteryLevel = 1f;
        } else {
            Predicate<UserExperience> experienceHasLevelAndIsNotInternship = xp -> xp.getErhgoOccupation() != null
                    && xp.getErhgoOccupation().getLevel() != null
                    && !xp.getType().equals(ExperienceType.INTERNSHIP);
            this.masteryLevel = (float) experiences
                    .stream()
                    .filter(experienceHasLevelAndIsNotInternship)
                    .mapToInt(UserExperience::getLevel)
                    .average()
                    .orElse(0d);
        }
    }

    public void refreshMasteryLevelSilently() {
        if (experiences == null || experiences().isEmpty()) {
            this.masteryLevel = 0f;
        } else {
            computeMasteryLevelBasedOnExperience();
        }
    }

    public void refreshMasteryLevelAndMarkAsIndexationRequired() {
        var previousMasteryLevel = this.masteryLevel;
        refreshMasteryLevelSilently();
        if (this.masteryLevel != previousMasteryLevel) {
            dirtiesUserProfile();
        }
    }

    private Optional<CapacityOccurrence> getCapacityOccurrence(Capacity capacity, Collection<CapacityOccurrence> occurrences) {
        return occurrences.stream()
                .filter(o -> capacity.equals(o.getCapacity()))
                .findFirst();
    }

    public void removeExperience(UserExperience userExperience) {
        var capacities = userExperience.getAllCapacitiesWithDuplicates();
        this.experiences.remove(userExperience);
        this.jobContextsMet().forEach(cm -> cm.removeExperience(userExperience));
        dirtiesUserProfile();
        removeCapacities(capacities);
    }

    public void removeJobContextMetById(UUID contextId) {
        Optional<JobContextMet> contextMet = jobContextsMet().stream().filter(cm -> cm.getContext().getId().equals(contextId)).findFirst();
        contextMet.ifPresent(cm -> {
            contextsMet.remove(cm);
            cm.setUserProfile(null);
        });
    }

    public Collection<JobContextMet> jobContextsMet() {
        return this.contextsMet().stream()
                .filter(AbstractContextMet::isJob)
                .map(JobContextMet.class::cast)
                .collect(Collectors.toSet());

    }

    public Optional<Frequency> getFrequencyForContext(UUID contextId) {
        var contextMet = jobContextsMet();
        return contextMet
                .stream()
                .filter(jobContextMet -> jobContextMet.getContext().getId().equals(contextId))
                .map(AbstractContextMet::getFrequency)
                .findFirst();
    }

    public Map<Capacity, Integer> buildLevelForCapacityMap() {
        var levelForCapacity = new HashMap<Capacity, Integer>();

        if (experiences != null) {
            experiences.forEach(ux -> ux.getTopLevelCapacities().forEach(c -> {
                // For capacities directly induced by activity => use ERHGO occupation level
                updateLevelUsingMaxLevel(c, levelForCapacity, ux.getLevel());
                // For capacities induced by these capacities => default level
                c.getInducedCapacitiesRecursively().forEach(induced -> updateLevelUsingMaxLevel(induced, levelForCapacity, MasteryLevel.DEFAULT_LEVEL_AS_INT));
            }));
        }

        if (answerForCapacityRelatedQuestions != null) {
            answerForCapacityRelatedQuestions.stream().flatMap(q -> q.getRelatedCapacitiesRecursively().stream()).forEach(c ->
                    updateLevelUsingMaxLevel(c, levelForCapacity, MasteryLevel.DEFAULT_LEVEL_AS_INT)
            );
        }

        return levelForCapacity;
    }

    private void updateLevelUsingMaxLevel(Capacity c, HashMap<Capacity, Integer> levelForCapacity, int level) {
        var previousLevel = levelForCapacity.get(c);
        if (previousLevel == null || previousLevel < level) {
            levelForCapacity.put(c, level);
        }
    }

    public Set<Capacity> getAllCapacities() {
        return capacityOccurrences == null ? Collections.emptySet() : capacityOccurrences.stream().map(CapacityOccurrence::getCapacity).collect(Collectors.toSet());
    }

    public void refreshProfessionalAndCapacityRelatedQuestionCapacityOccurrences() {
        if (capacityOccurrences == null) {
            this.capacityOccurrences = new HashSet<>();
        } else {
            this.capacityOccurrences.clear();
        }
        if (this.answerForCapacityRelatedQuestions == null) {
            this.answerForCapacityRelatedQuestions = new HashSet<>();
        }
        if (this.experiences == null) {
            this.experiences = new HashSet<>();
        }
        this.experiences().stream()
                .flatMap(e -> e.getAllActivities(false).stream())
                .forEach(a -> addCapacities(a.getInducedCapacities()));
        this.answerForCapacityRelatedQuestions
                .forEach(a -> addCapacities(a.getRelatedCapacitiesRecursively()));
        this.dirtiesUserProfile();
    }

    public MasteryLevel computeFloorMasteryLevel() {
        return MasteryLevel.forLevel(this.masteryLevel);
    }


    public void addAnswerForCapacityRelatedQuestion(AnswerForCapacityRelatedQuestion answerForCapacityRelatedQuestion) {
        if (this.answerForCapacityRelatedQuestions == null) {
            this.answerForCapacityRelatedQuestions = new HashSet<>();
        }
        this.answerForCapacityRelatedQuestions.add(answerForCapacityRelatedQuestion);
        dirtiesUserProfile();
    }

    public void updateRegistrationState(UserRegistrationState.RegistrationStep currentRegistrationState) {
        userRegistrationState.updateRegistrationStep(currentRegistrationState);
    }

    private boolean addNewAffectationsToHistory(Set<String> channels, UserChannel.ChannelSourceType channelSourceType) {
        return channels.stream().map(channel -> {
                            if (!this.channels().contains(channel)) {
                                return ChannelAffectation.builder()
                                        .userChannel(UserChannel.builder()
                                                .channel(channel)
                                                .channelSourceType(channelSourceType)
                                                .build())
                                        .userProfile(this)
                                        .build();
                            }
                            return null;
                        }

                )
                .filter(Objects::nonNull)
                .map(affectationsHistory::add)
                .collect(Collectors.toSet())
                .stream()
                .anyMatch(Boolean::booleanValue);
    }

    public boolean isPrescriberInAffectationsHistory() {
        return affectationsHistory.stream()
                .map(ChannelAffectation::channel)
                .anyMatch(channel -> channel.equals(prescriber.channel()));
    }

    public boolean updatedChannels(Set<String> channels, UserChannel.ChannelSourceType channelSourceType) {
        var hasBeenModified = false;
        if (prescriber.isNotDefined()) {
            prescriber = UserChannel.builder()
                    .channelSourceType(channelSourceType)
                    .channel(Optional.ofNullable(channels).map(Collection::stream).flatMap(Stream::findAny).orElse(null))
                    .build();
            hasBeenModified = true;
        }
        if (channels != null && !channels.isEmpty()) {
            EventPublisherUtils.publish(UserAffectedToChannelsEvent.forChannels(channels, this, channelSourceType));
            hasBeenModified = addNewAffectationsToHistory(channels, channelSourceType);
        }
        if (hasBeenModified) {
            dirtiesUserProfile();
        }
        return hasBeenModified;
    }

    public void removeFromChannels(Collection<String> channels) {
        affectationsHistory.removeIf(affectation ->
                channels.contains(affectation.channel())
        );
        dirtiesUserProfile();
    }

    public Set<String> channels() {
        return affectationsHistory
                .stream()
                .filter(Objects::nonNull)
                .map(ChannelAffectation::channel)
                .collect(Collectors.toSet());
    }

    public UserChannel.ChannelSourceType channelSourceType() {
        return prescriber.channelSourceType();
    }

    public boolean isSourceInitialized() {
        return channelSourceType() != UserChannel.ChannelSourceType.UNKNOWN;
    }

    public UserRegistrationState.RegistrationStep registrationStep() {
        return userRegistrationState.getRegistrationStep();
    }

    public int countExtraProfessionalQuestionsAnswered() {
        return (int) answerForCapacityRelatedQuestions().stream()
                .map(AnswerForCapacityRelatedQuestion::getResponse)
                .map(CapacityRelatedQuestionResponse::getQuestion)
                .filter(q -> q.getQuestionType() == QuestionType.EXTRAPROFESSIONAL)
                .distinct()
                .count();
    }

    public boolean hasChannel() {
        return !this.channels().isEmpty();
    }

    public void defineUserExperience(ErhgoOccupation occupation, String title) {
        this.userRegistrationState.setSelectedOccupation(occupation);
        this.userRegistrationState.setJobTitle(title);
        this.experiences.add(UserExperience.forOccupation(occupation, title, this));
        this.refreshMasteryLevelAndMarkAsIndexationRequired();
        this.dirtiesUserProfile();
    }

    public void setTaggedNoExperience(boolean taggedNoExperience) {
        this.taggedNoExperience = taggedNoExperience;
    }


    public Boolean getJobOfferOptOut() {
        return this.mailConfiguration.getJobOfferOptOut();
    }

    public Boolean getSmsBlacklisted() {
        return Optional.ofNullable(generalInformation).map(GeneralInformation::getSmsBlacklisted).orElse(null);
    }

    public UserProfile updateJobOfferOptOut(Boolean blackListed) {
        this.mailConfiguration.updateJobOfferOptOut(blackListed);
        return this;
    }

    public UserProfile sendersOptOut(Collection<String> senders) {
        this.mailConfiguration.sendersOptOut(senders);
        return this;
    }

    public void updateSmsBlacklisted(boolean blackListed) {
        this.getGeneralInformationOrCreate().setSmsBlacklisted(blackListed);
    }

    private GeneralInformation getGeneralInformationOrCreate() {
        if (generalInformation == null) {
            log.warn("Patch user {}: create non existent GeneralInformation entry - see call stack below", userId, new Exception());
            generalInformation = GeneralInformation.builder()
                    .userId(userId)
                    .userProfile(this)
                    .build();
        }
        return generalInformation;
    }

    public void updateAnswerToCriteria(Collection<CriteriaValue> selectedAnswers, Collection<CriteriaValue> unselectedAnswers) {
        updateAnswerToCriteria(selectedAnswers, true);
        updateAnswerToCriteria(unselectedAnswers, false);
        dirtiesUserProfile();
    }

    private void updateAnswerToCriteria(Collection<CriteriaValue> selectedAnswers, boolean selected) {
        removePreviousThresholdCriteriaValue(selectedAnswers);
        selectedAnswers.forEach(a -> updateExistentAnswerOrCreate(selected, a));
    }

    private void removePreviousThresholdCriteriaValue(Collection<CriteriaValue> selectedAnswers) {
        this.userCriteriaValues
                .removeIf(c -> selectedAnswers.stream().anyMatch(a -> c.getValue().shouldReplaceCriteriaValue(a)));
    }

    public void resetCriteria(Collection<CriteriaValueForUserDTO> criteriaValuesForUser) {
        this.userCriteriaValues.removeIf(x -> criteriaValuesForUser
                .stream()
                .anyMatch(y -> y.getCriteriaValue()
                        .getCode()
                        .equals(x.getValue().getCode()))
        );
    }

    private void updateExistentAnswerOrCreate(boolean selected, CriteriaValue a) {
        var existentAnswer = getExistentAnswerToCriteria(a);
        if (existentAnswer.isPresent()) {
            existentAnswer.get().setSelected(selected);
        } else {
            this.userCriteriaValues.add(new UserCriteriaValue(this, a, selected));
        }
    }

    private Optional<UserCriteriaValue> getExistentAnswerToCriteria(CriteriaValue a) {
        return this.userCriteriaValues.stream().filter(u -> u.getValue().equals(a)).findFirst();
    }

    public Set<UserCriteriaValue> getUserCriteriaValues() {
        return UnmodifiableSet.unmodifiableSet(userCriteriaValues);
    }

    public Collection<CriteriaValue> getAcceptedCriteriaValues() {
        var optIn = userCriteriaValues
                .stream()
                .filter(UserCriteriaValue::isSelected)
                .map(UserCriteriaValue::getValue)
                .collect(Collectors.toSet());

        var lower = userCriteriaValues
                .stream()
                .flatMap(cv -> cv.getValue().getValueOrLower().stream())
                .collect(Collectors.toSet());
        return Sets.union(optIn, lower);
    }

    public Set<CriteriaValue> getRefusedCriteriaValues() {
        var optOut = userCriteriaValues
                .stream()
                .filter(x -> !x.isSelected())
                .map(UserCriteriaValue::getValue)
                .collect(Collectors.toSet());

        var tooHigh = userCriteriaValues
                .stream()
                .flatMap(cv -> cv.getValue().getHigherValues().stream())
                .collect(Collectors.toSet());
        return Sets.union(optOut, tooHigh);
    }

    public DriverLicence getDriverLicenceFromCriteria() {
        return getUserCriteriaValues().stream()
                .filter(x -> x.getValue().getCriteria().getCode().equals(Criteria.DRIVER_LICENCE_CRITERIA_CODE))
                .findFirst()
                .map(v -> CriteriaValue.DRIVER_LICENCE_FOR_CRITERIA_RESPONSE.get(v.getValue().getCode()))
                .orElse(null);
    }

    public List<TypeContractCategory> getContractsFromCriteria() {
        return getUserCriteriaValues().stream()
                .filter(x -> x.getValue().getCriteria().getCode().equals(Criteria.CONTRACT_TYPE_CRITERIA_CODE) && x.isSelected())
                .map(ucv -> ucv.getValue().getCode())
                .map(CriteriaValue.TYPE_CONTRACT_FOR_CRITERIA_RESPONSE::get)
                .toList();
    }

    public List<TypeWorkingTime> getTypeWorkingTimeFromCriteria() {
        return getUserCriteriaValues().stream()
                .filter(x -> x.getValue().getCriteria().getCode().equals(Criteria.TYPE_WORKING_TIME) && x.isSelected())
                .map(ucv -> ucv.getValue().getCode())
                .map(CriteriaValue.WORKING_TIME_FOR_CRITERIA_RESPONSE::get)
                .toList();
    }


    // This method is used to mark UserProfile as 'dirty' (from an indexation point of view)
    // Dirties user profile will be re-indexed
    public void dirtiesUserProfile() {
        this.indexationRequiredDate = new Date();
    }

    public UserNote editNote(UUID id, AbstractOrganization organization, String text) {
        var note = userNotes.stream().filter(n -> n.getId().equals(id)).findFirst().orElse(null);
        if (note == null) {
            note = UserNote.builder().id(id).userProfile(this).build();
            userNotes.add(note);
        }
        note.setOrganization(organization).setContent(text);
        return note;
    }

    public void deleteNote(UUID id) {
        userNotes.removeIf(n -> n.getId().equals(id));
    }

    public String getCity() {
        return Optional.ofNullable(generalInformation).map(GeneralInformation::getLocation).map(Location::getCity).orElse(null);
    }

    public String getDepartmentCode() {
        return Optional.ofNullable(generalInformation).map(GeneralInformation::getLocation).map(Location::getDepartmentCode).orElse(null);
    }

    public Set<JobActivityLabel> getAllActivities() {
        return experiences.stream().flatMap(u -> u.getAllActivities(true).stream()).collect(Collectors.toSet());
    }

    public Set<String> getOccupationsTitle() {
        return experiences.stream().map(UserExperience::getErhgoOccupationTitle).filter(Objects::nonNull).collect(Collectors.toSet());
    }

    public void indexed() {
        this.indexationRequiredDate = null;
        this.lastIndexationDate = new Date();
    }


    public void markNewConnection() {
        this.lastConnectionDate = LocalDateTime.now(ZONE_ID);
    }

    public Set<String> getExperienceTitles() {
        return experiences.stream().map(UserExperience::getJobTitle).collect(Collectors.toSet());
    }

    public Set<UUID> getExperiencesOccupationsId() {
        return experiences.stream().map(UserExperience::getOccupationId).filter(Objects::nonNull).collect(Collectors.toSet());
    }

    public boolean isExportable() {
        return hasSoftSkills() ||
                (!experiences().isEmpty() && !capacityOccurrences.isEmpty());
    }

    public boolean isWaitingForBOTermination() {
        return userRegistrationState.getRegistrationStep() == UserRegistrationState.RegistrationStep.BO_INITIALIZED;
    }

    public boolean isEmailConfirmationRequired() {
        return generalInformation != null && generalInformation.getMailVerificationState().requiresConfirmation();
    }

    public void updateMailVerificationStateWith(EmailVerificationResultDTO verificationResult) {
        getGeneralInformationOrCreate().getMailVerificationState().updateWith(verificationResult);
    }

    public boolean requiresVerificationNow() {
        return generalInformation != null && generalInformation.getMailVerificationState().requiresVerificationNow();
    }

    public void markEmailAsForced() {
        getGeneralInformationOrCreate().getMailVerificationState().markAsForced();
    }

    public void ensureUserHasAccessToJob(Job job) {
        if (job != null && job.isPrivate() && !channels().contains(job.getRecruiterCode())) {
            var logLine = "User " + userId() + " is not allowed to candidate to " + job.getId() + " because he does not belong to channel " + job.getRecruiterCode() + ".";
            log.warn(logLine);
            throw new UserNotAllowedForEntity(logLine);
        }
    }

    public List<String> getBehaviorsKeys() {
        return getBehaviorsCategories().stream().map(Enum::name).toList();
    }

    public List<BehaviorCategory> getBehaviorsCategories() {
        return behaviors == null ? Collections.emptyList() : behaviors.stream().map(Behavior::getBehaviorCategory).toList();
    }

    public void updateBehaviors(Collection<Behavior> behaviors) {
        if (this.behaviors == null) {
            this.behaviors = new HashSet<>();
        }
        this.behaviors.removeIf(b -> !behaviors.contains(b));
        this.behaviors.addAll(behaviors);
    }

    public boolean shouldAskForMailOptIn() {
        return getJobOfferOptOut() == null;
    }

    public void setJobOfferOptOut(Boolean optOut) {
        if (mailConfiguration.updateJobOfferOptOut(optOut)) {
            dirtiesUserProfile();
        }
    }

    public Set<String> getSendersOptOut() {
        return StringUtils.isBlank(mailConfiguration.getSendersOptOut()) ? Sets.newHashSet() : Stream.of(mailConfiguration.getSendersOptOut().split(",")).collect(Collectors.toSet());
    }

    public void addSendersOptOut(String email) {
        mailConfiguration.sendersOptOut(Sets.union(Set.of(email), getSendersOptOut()));
    }

    public void removeSenderOptOut(String email) {
        mailConfiguration.sendersOptOut(getSendersOptOut().stream().filter(e -> !e.equals(email)).collect(Collectors.toSet()));
    }

    public String getPhoneNumber() {
        return Optional.ofNullable(generalInformation).map(GeneralInformation::getPhoneNumber)
                .map(StringUtils::trimToNull)
                .orElse(null);
    }

    public void confirmWelcomeEmailSent(MailConfiguration.ConfirmationMailState mailState) {
        mailConfiguration.setConfirmationMailSent(mailState);
    }

    public void updateSituation(Integer availabilityDelayInMonth) {
        if (generalInformation != null && generalInformation.getSituation() == null) {
            generalInformation.setSituation(Situation.RESEARCHING);
            generalInformation.setDelayInMonth(availabilityDelayInMonth);
        }
    }

    public List<ErhgoClassification> getAcceptedErhgoClassifications() {
        return getErhgoClassifications(UserErhgoClassification::isAccepted);
    }

    @NotNull
    private List<ErhgoClassification> getErhgoClassifications(Predicate<UserErhgoClassification> predicate) {
        return erhgoClassifications.stream().filter(predicate).map(UserErhgoClassification::getErhgoClassification).toList();
    }

    public List<ErhgoClassification> getRefusedErhgoClassifications() {
        return getErhgoClassifications(Predicate.not(UserErhgoClassification::isAccepted));
    }

    public void updateErhgoClassificationAcceptedStatus(ErhgoClassification erhgoClassification, Boolean isAccepted) {
        Predicate<UserErhgoClassification> userSelectedValueForClassificationPredicate = c -> c.getErhgoClassification().getCode().equals(erhgoClassification.getCode());
        if (isAccepted == null) {
            erhgoClassifications.removeIf(userSelectedValueForClassificationPredicate);
        } else {
            erhgoClassifications.stream().filter(userSelectedValueForClassificationPredicate).findFirst().ifPresentOrElse(
                    uec -> uec.setAccepted(isAccepted),
                    () -> erhgoClassifications.add(new UserErhgoClassification(this, erhgoClassification, isAccepted))
            );
        }
        dirtiesUserProfile();
    }

    public Set<String> getRomeOccupationCodes() {
        return this.experiences
                .stream()
                .map(UserExperience::getErhgoOccupation)
                .filter(Objects::nonNull)
                .flatMap(o -> o.getRomeOccupations()
                        .stream()
                        .map(RomeOccupation::getCode))
                .collect(Collectors.toSet());
    }

    public Set<Integer> getIscoOccupationCodes() {
        return this.experiences
                .stream()
                .map(UserExperience::getErhgoOccupation)
                .filter(Objects::nonNull)
                .flatMap(o -> o.getEscoOccupations()
                        .stream()
                        .map(EscoOccupation::getIscoOccupation)
                        .filter(Objects::nonNull)
                        .map(IscoOccupation::getIscoGroup))
                .collect(Collectors.toSet());
    }

    public boolean isAvailableNow() {
        return generalInformation != null && generalInformation.getDelayInMonth() == null && generalInformation.getSituation() == Situation.RESEARCHING;
    }

    public Integer getDelayInMonth() {
        return generalInformation != null ? generalInformation.getDelayInMonth() : null;
    }

    public void addOccupationToBlacklist(ErhgoOccupation occupation) {
        this.blacklistedOccupations.add(occupation);
        dirtiesUserProfile();
    }

    public void removeOccupationToBlacklist(ErhgoOccupation occupation) {
        this.blacklistedOccupations.remove(occupation);
        dirtiesUserProfile();
    }

    public Set<ErhgoOccupation> getBlacklistedOccupations() {
        return this.blacklistedOccupations;
    }

    public void markAsCreatedFromBO() {
        updatedChannels(Collections.emptySet(), UserChannel.ChannelSourceType.CREATION_BO);
        userRegistrationState().updateRegistrationStep(UserRegistrationState.RegistrationStep.BO_INITIALIZED);
        // first mail is sent by BO creator, from BO
        mailConfiguration().setConfirmationMailSent(MailConfiguration.ConfirmationMailState.FIRST_SENT);
    }

    public boolean isFirstConfirmationMailSent() {
        return mailConfiguration.isFirstConfirmationMailSent();
    }


    public boolean isSecondConfirmationMailSent() {
        return mailConfiguration.isSecondConfirmationMailSent();
    }

    public int numberOfExperiences() {
        return experiences.size();
    }

    public Location getLocationIndication() {
        return Optional.ofNullable(generalInformation).map(GeneralInformation::getLocation).orElse(null);
    }

    public UserProfile startSoftSkillTest() {
        initTrimojiStatus().startedDate(OffsetDateTime.now());
        return this;
    }

    @VisibleForTesting
    public UserProfile trimojiPdfUrl(String url) {
        initTrimojiStatus().trimojiPdfUrl(url);
        return this;
    }

    public String trimojiPdfUrl() {
        return Optional.ofNullable(trimojiStatus).map(TrimojiStatus::trimojiPdfUrl).orElse(null);
    }

    private TrimojiStatus initTrimojiStatus() {
        if (trimojiStatus == null) {
            trimojiStatus = new TrimojiStatus();
        }
        return trimojiStatus;
    }

    private UserExperienceGeneratedData initUserExperienceGeneratedData() {
        if (userExperienceGeneratedData == null) {
            userExperienceGeneratedData = new UserExperienceGeneratedData();
        }
        userExperienceGeneratedData.refreshChecksum(getExperiencesOccupationsId());
        return userExperienceGeneratedData;
    }

    public void dirtyUserExperiencesGeneratedData() {
        if (userExperienceGeneratedData == null) {
            userExperienceGeneratedData = new UserExperienceGeneratedData();
        }
        userExperienceGeneratedData.resetIfRequired(getExperiencesOccupationsId());
    }

    public void setGeneratedUserBehaviorDescription(String description) {
        initUserExperienceGeneratedData().setGeneratedAttitude(description);
    }

    public void setForcedUserBehaviorDescription(String description) {
        initUserExperienceGeneratedData().setForcedAttitude(description);
    }

    private UserFileImportState initUserFileImport() {
        if (userFileImportState == null) {
            userFileImportState = new UserFileImportState();
        }
        return userFileImportState;
    }

    public boolean canImportFile() {
        var state = initUserFileImport();
        return state.canUpload() && !state.hasReachedTimeLimit();
    }

    public boolean hasImportReachedRateLimit() {
        return initUserFileImport().hasReachedTimeLimit();
    }

    public FileImportState getFileImportState() {
        return initUserFileImport().state();
    }

    public void setUserFileImportState(FileImportState fileImportState) {
        initUserFileImport().setState(fileImportState);
    }

    public String sendersBlacklisted() {
        return Optional.ofNullable(mailConfiguration).map(MailConfiguration::getSendersOptOut).orElse("");
    }

    public Set<String> hashtags() {
        return Optional.ofNullable(userExperienceGeneratedData).map(UserExperienceGeneratedData::hashtags).orElse(Collections.emptySet());
    }

    public void updateHashtags(Collection<String> generatedHashtags) {
        initUserExperienceGeneratedData().hashtags(new HashSet<>(generatedHashtags));
    }

    public void updateSoftSkillsDescriptions(List<TitleAndDescription> generatedDescriptions) {
        initUserExperienceGeneratedData().generatedSoftSkillDescription(generatedDescriptions);
    }

    public String getPostcode() {
        return Optional.ofNullable(generalInformation).map(GeneralInformation::getLocation).map(Location::getPostcode).orElse(null);
    }

    public void markNewMobileConnection() {
        getGeneralInformationOrCreate().markNewMobileConnection();
    }

    public void setHardSkills(Map<HardSkillType, String> hardSkills) {
        initUserExperienceGeneratedData().resetHardSkills(hardSkills);
    }

    public Map<HardSkillType, String> getHardSkills() {
        return initUserExperienceGeneratedData().hardSkills();
    }


    public List<String> getSoftSkillsTitles() {
        return Optional.ofNullable(userExperienceGeneratedData)
                .map(UserExperienceGeneratedData::softSkills)
                .map(Collection::stream)
                .map(s -> s.map(SoftSkill::getTitle).toList())
                .orElse(Collections.emptyList());
    }

    public boolean hasSoftSkills() {
        return !Optional.ofNullable(userExperienceGeneratedData)
                .map(UserExperienceGeneratedData::softSkills)
                .map(Collection::isEmpty)
                .orElse(true)
                ;
    }

    public List<TitleAndDescription> getSoftSkills() {
        return Optional.ofNullable(userExperienceGeneratedData)
                .map(UserExperienceGeneratedData::softSkills)
                .map(l -> l.stream().map(a -> new TitleAndDescription(a.getTitle(), a.getDescription())).toList())
                .orElse(Collections.emptyList());
    }

    public String getPrescriberLabel(Map<String, String> organizationTitleForCode) {
        var prescriberCode = Optional.ofNullable(prescriber().channel());
        var prescriberName = prescriberCode.map(organizationTitleForCode::get).orElse("Inconnu");
        return prescriberCode.map(code -> "%s (%s)".formatted(prescriberName, code)).orElse("Aucun");
    }

    public String getChannelSourceType() {
        return Optional.ofNullable(prescriber().channelSourceType())
                .map(UserChannel.ChannelSourceType::text)
                .orElse(UserChannel.ChannelSourceType.UNKNOWN.text());
    }
}
