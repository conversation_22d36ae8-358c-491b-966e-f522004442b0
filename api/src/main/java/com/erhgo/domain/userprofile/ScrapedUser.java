package com.erhgo.domain.userprofile;

import com.erhgo.domain.AbstractAuditableEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.UUID;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(of = {"uuid", "candidateId"}, callSuper = false)
@ToString(of = {"candidateId", "email"})
@Accessors(chain = true)
public class ScrapedUser extends AbstractAuditableEntity {

    @Id
    @Column(columnDefinition = "BINARY(16)")
    @Builder.Default
    private UUID uuid = UUID.randomUUID();

    @NotNull
    @Column(nullable = false, unique = true)
    private String candidateId;

    private String firstName;

    private String lastName;

    private String jobTitle;

    private String location;

    private String email;

    private String cvDownloadLink;

    @Column(columnDefinition = "LONGTEXT")
    private String cvContent;

    private String profileUrl;

    @Enumerated(EnumType.STRING)
    private UserProfileCreationState creationState;

    private String errorMessage;
}
