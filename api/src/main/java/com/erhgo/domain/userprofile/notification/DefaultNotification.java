package com.erhgo.domain.userprofile.notification;

import com.erhgo.domain.enums.NotificationState;
import com.erhgo.domain.enums.NotificationType;
import com.erhgo.domain.userprofile.UserProfile;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.*;

import java.net.URI;

@Entity
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@EqualsAndHashCode(callSuper = true)
@DiscriminatorValue(DefaultNotification.ENTITY_TYPE)
@ToString
public class DefaultNotification extends AbstractNotification {
    public static final String ENTITY_TYPE = "DEFAULT";

    @Getter
    @Column(length = 1000)
    private String link;
    @Getter
    @Column(length = 1500)
    private String content;
    @Getter
    @Column(length = 500)
    private String subject;

    @Override
    public String getEntityType() {
        return ENTITY_TYPE;
    }

    @Override
    public void accept(NotificationVisitor visitor) {
        visitor.visit(this);
    }

    @Builder
    public DefaultNotification(UserProfile userProfile, URI uri, NotificationType type, String subject, String content) {
        super(userProfile, type, NotificationState.NEW);
        this.link = uri == null ? null : uri.toString();
        this.content = content;
        this.subject = subject;
    }
}
