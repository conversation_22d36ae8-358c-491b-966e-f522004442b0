package com.erhgo.domain.userprofile.event;

import com.erhgo.domain.candidature.job.AbstractCandidature;
import com.erhgo.domain.userprofile.UserProfile;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public class UserNewCandidaturePublishedEvent implements UserRelatedEvent {

    private AbstractCandidature candidature;

    @Override
    public UserProfile getUserProfile() {
        return candidature.getUserProfile();
    }
}
