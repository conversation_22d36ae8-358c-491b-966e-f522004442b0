package com.erhgo.domain.userprofile.notification;

import com.erhgo.domain.enums.NotificationState;
import com.erhgo.domain.enums.NotificationType;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.userprofile.UserProfile;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import lombok.*;

import java.io.Serializable;

@Entity
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@EqualsAndHashCode(callSuper = true)
@DiscriminatorValue(SuspendedRecruitmentNotification.ENTITY_TYPE)
@ToString(callSuper = true)
public class SuspendedRecruitmentNotification extends AbstractNotification {
    public static final String ENTITY_TYPE = "SUSPENDED_RECRUITMENT";

    @ManyToOne(optional = false)
    @Getter
    private Recruitment recruitment;

    @Override
    public Serializable getEntityId() {
        return recruitment.getId();
    }

    @Override
    public String getEntityType() {
        return ENTITY_TYPE;
    }

    @Override
    public void accept(NotificationVisitor visitor) {
        visitor.visit(this);
    }

    @Builder
    public SuspendedRecruitmentNotification(UserProfile userProfile, Recruitment recruitment, NotificationType type, NotificationState state) {
        super(userProfile, type, state);
        this.recruitment = recruitment;
        this.requiresMailSending = false;
    }
}
