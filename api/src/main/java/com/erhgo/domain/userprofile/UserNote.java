package com.erhgo.domain.userprofile;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.referential.AbstractOrganization;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


import java.util.UUID;

@Slf4j
@Entity
@Builder
@EqualsAndHashCode(callSuper = true)
@Getter(AccessLevel.PUBLIC)
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Accessors(chain = true)
public class UserNote extends AbstractAuditableEntity {
    public static String REMOVED_NOTE_TEXT = "Cette note a été automatiquement supprimée";
    @Id
    @Column(columnDefinition = "BINARY(16)")
    @Builder.Default
    private UUID id = UUID.randomUUID();

    @ManyToOne(optional = false)
    @JoinColumn(nullable = false)
    private UserProfile userProfile;

    @Column(columnDefinition = "LONGTEXT")
    @Setter()
    private String content;

    @ManyToOne
    @Setter(AccessLevel.PACKAGE)
    private AbstractOrganization organization;

}
