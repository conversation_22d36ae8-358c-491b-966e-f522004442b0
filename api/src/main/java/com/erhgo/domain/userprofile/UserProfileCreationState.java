package com.erhgo.domain.userprofile;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum UserProfileCreationState {
    ALREADY_EXISTS("Utilisateur disposant déjà d'un compte"),
    CREATED("Compte créé pour l'utilisateur"),
    ERROR("Erreur lors de la création du compte"),
    NOT_INTERESTED("Utilisateur non intéressé"),
    PENDING("En attente de création de compte");


    @Getter
    private final String label;
}
