package com.erhgo.domain.userprofile;

import com.google.common.annotations.VisibleForTesting;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Builder
@ToString
@Embeddable
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MailConfiguration {


    public static final Pattern MAIL_RECIPIENT_PATTERN = Pattern.compile("[^,<>\\s]+@[^,<>\\s]+");

    public enum ConfirmationMailState {
        NEW,
        FIRST_SENT,
        SECOND_SENT,
        ERROR,
    }

    @Getter
    private Boolean jobOfferOptOut;

    @Getter
    @Builder.Default
    @Column(length = 2000)
    private String sendersOptOut = "";

    @Getter
    @Setter
    @Builder.Default
    @Enumerated(EnumType.STRING)
    private ConfirmationMailState confirmationMailSent = ConfirmationMailState.NEW;


    public boolean updateJobOfferOptOut(Boolean blackListed) {
        if (Objects.equals(this.jobOfferOptOut, blackListed)) {
            return false;
        }
        this.jobOfferOptOut = blackListed;
        return true;
    }

    public void sendersOptOut(Collection<String> senders) {
        this.sendersOptOut = senders == null ? "" : senders.stream().map(MailConfiguration::normalize)
                .distinct()
                .collect(Collectors.joining(","));
    }

    @VisibleForTesting
    public static String normalize(String textIn) {
        var text = StringUtils.trimToEmpty(textIn);
        var matcher = MAIL_RECIPIENT_PATTERN.matcher(text);
        if (matcher.find()) {
            return matcher.group().trim();
        }
        return text;
    }

    public boolean isFirstConfirmationMailSent() {
        return confirmationMailSent == MailConfiguration.ConfirmationMailState.FIRST_SENT;
    }

    public boolean isSecondConfirmationMailSent() {
        return confirmationMailSent == ConfirmationMailState.SECOND_SENT;
    }
}
