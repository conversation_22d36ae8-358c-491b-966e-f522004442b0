package com.erhgo.domain.userprofile;

import jakarta.persistence.Embeddable;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.OffsetDateTime;


@Embeddable
@NoArgsConstructor
@Accessors(fluent = true)
@Data
public class TrimojiStatus {

    private String trimojiPdfUrl;

    private OffsetDateTime startedDate;

    private OffsetDateTime endedDate;
}
