package com.erhgo.domain.userprofile.capacityrelatedquestion;


import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.CapacityRelatedQuestionResponse;
import com.erhgo.domain.userprofile.UserProfile;
import com.google.common.collect.Lists;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.io.Serializable;
import java.util.Collection;
import java.util.Collections;
import java.util.UUID;

@Entity
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@EqualsAndHashCode(callSuper = false, of = "id")
public class AnswerForCapacityRelatedQuestion extends AbstractAuditableEntity {

    @Embeddable
    @EqualsAndHashCode
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    @AllArgsConstructor(access = AccessLevel.PUBLIC)
    public static class ID implements Serializable {

        @NotNull
        @Getter(AccessLevel.PRIVATE)
        private UUID responseId;

        @NotNull
        @Getter(AccessLevel.PRIVATE)
        private UUID userProfileId;

    }

    @EmbeddedId
    @Getter
    private AnswerForCapacityRelatedQuestion.ID id;

    @MapsId("userProfileId")
    @ManyToOne(optional = false)
    @Getter
    private UserProfile userProfile;

    @MapsId("responseId")
    @ManyToOne(optional = false)
    @Getter
    private CapacityRelatedQuestionResponse response;

    @Builder
    public AnswerForCapacityRelatedQuestion(UserProfile userProfile, CapacityRelatedQuestionResponse response) {
        this.id = new ID(response.getId(), userProfile.uuid());
        this.userProfile = userProfile;
        this.response = response;
        this.userProfile.addAnswerForCapacityRelatedQuestion(this);
    }

    public Collection<Capacity> getRelatedCapacitiesRecursively() {
        var capacities = Lists.newArrayList(response.getCapacities());
        var inducedCapacities = capacities.stream().flatMap(Capacity::getInducedCapacitiesRecursively)
                .toList();
        capacities.addAll(inducedCapacities);
        return Collections.unmodifiableCollection(capacities);
    }
}
