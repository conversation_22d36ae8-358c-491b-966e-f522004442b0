package com.erhgo.domain.userprofile;

import jakarta.annotation.Nullable;
import jakarta.persistence.Embeddable;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.*;
import lombok.experimental.Accessors;


@Embeddable
@NoArgsConstructor
@Builder
@AllArgsConstructor
@Accessors(fluent = true)
@ToString
public class UserChannel {

    @AllArgsConstructor
    public enum ChannelSourceType {
        LANDING_PAGE("Page d'accueil"),
        ORGANIZATION("Organisation"),
        CANDIDATURE("Candidature"),
        URL("url"),
        OTHER("Autre"),
        NOTHING("Aucun"),
        UNKNOWN("Inconnu"),
        NOT_UNDERSTOOD("Pas compris"),
        ADMIN("Action admin"),
        CREATION_BO("Création depuis le BO"),
        ENTERPRISE_PAGE("Page entreprise");

        @Getter
        private String text;

        public boolean requiresSpontaneousCandidature() {
            return this == ENTERPRISE_PAGE;
        }
    }

    @Setter
    @Getter
    @Nullable
    private String channel;

    @Enumerated(EnumType.STRING)
    @Getter
    @Setter
    @Builder.Default
    private ChannelSourceType channelSourceType = ChannelSourceType.UNKNOWN;

    public boolean isUnknown() {
        return channelSourceType() == UserChannel.ChannelSourceType.UNKNOWN;
    }

    public boolean isNotDefined() {
        return isUnknown() && channel() == null;
    }
}
