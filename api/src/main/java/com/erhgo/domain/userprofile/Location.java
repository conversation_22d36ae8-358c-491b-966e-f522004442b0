package com.erhgo.domain.userprofile;

import com.erhgo.openapi.dto.LocationDTO;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.*;

@Embeddable
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Builder
@EqualsAndHashCode
public class Location {
    public static final int ANYWHERE_RADIUS = 200;
    @Column(length = 2000)
    @Getter
    @Setter
    private String city;

    @Column(length = 10)
    @Getter
    @Setter
    private String citycode;

    @Column(length = 10)
    @Getter
    @Setter
    private String postcode;

    @Column(length = 2000)
    @Getter
    @Setter
    private String departmentCode;

    @Column(length = 2000)
    @Getter
    @Setter
    private String regionName;

    @Getter
    @Setter
    private Float longitude;

    @Getter
    @Setter
    private Float latitude;

    @Getter
    @Setter
    private Integer radiusInKm;

    private static final double EARTH_RADIUS = 6371;

    public static Location buildLocationFromDTO(LocationDTO dto) {
        return dto == null ? null : Location.builder()
                .city(dto.getCity())
                .citycode(dto.getCitycode())
                .postcode(dto.getPostcode())
                .departmentCode(dto.getDepartmentCode())
                .regionName(dto.getRegionName())
                .longitude(dto.getLongitude())
                .latitude(dto.getLatitude())
                .radiusInKm(dto.getRadiusInKm())
                .build();
    }

    public LocationDTO buildDTO() {
        return new LocationDTO()
                .city(city)
                .citycode(citycode)
                .postcode(postcode)
                .departmentCode(departmentCode)
                .regionName(regionName)
                .longitude(longitude)
                .latitude(latitude)
                .radiusInKm(radiusInKm);
    }

    public double getDistance(Location otherLocation) {
        if (this.isLocationSpecified() && otherLocation != null && otherLocation.isLocationSpecified()) {

            double latitude1 = Math.toRadians(this.getLatitude());
            double longitude1 = Math.toRadians(this.getLongitude());
            double latitude2 = Math.toRadians(otherLocation.getLatitude());
            double longitude2 = Math.toRadians(otherLocation.getLongitude());

            double diffLongitude = longitude2 - longitude1;

            return EARTH_RADIUS * Math.acos(Math.sin(latitude1) * Math.sin(latitude2)
                    + Math.cos(latitude1) * Math.cos(latitude2) * Math.cos(diffLongitude));
        }
        return 0;
    }

    private boolean isLocationSpecified() {
        return getLatitude() != null && getLongitude() != null;
    }

    public Location duplicates() {
        return Location.builder()
                .city(this.getCity())
                .citycode(this.getCitycode())
                .postcode(this.getPostcode())
                .departmentCode(this.getDepartmentCode())
                .regionName(this.getRegionName())
                .longitude(this.getLongitude())
                .latitude(this.getLatitude())
                .radiusInKm(this.getRadiusInKm())
                .build();
    }

    public boolean anywhere() {
        return this.getRadiusInKm() != null && this.getRadiusInKm() >= ANYWHERE_RADIUS;
    }
}
