package com.erhgo.domain.userprofile;

import com.erhgo.domain.enums.FileImportState;
import jakarta.persistence.Embeddable;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.OffsetDateTime;

@Embeddable
@NoArgsConstructor
@Accessors(fluent = true)
@Data
public class UserFileImportState {
    private FileImportState state = FileImportState.NONE;

    private OffsetDateTime uploadedDateTime;

    protected void setState(FileImportState state) {
        this.state = state;
        if (state == FileImportState.COMPLETED) {
            uploadedDateTime = OffsetDateTime.now();
        }
    }

    protected boolean canUpload() {
        return (state == FileImportState.NONE || state == FileImportState.COMPLETED || state == FileImportState.ERROR);
    }

    protected boolean hasReachedTimeLimit() {
        return uploadedDateTime != null && uploadedDateTime.plusHours(1).isAfter(OffsetDateTime.now());
    }
}
