package com.erhgo.domain.userprofile;

import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.io.Serializable;
import java.util.UUID;

@Entity
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class UserErhgoClassification {


    @Embeddable
    @EqualsAndHashCode
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    @AllArgsConstructor(access = AccessLevel.PUBLIC)
    public static class ID implements Serializable {

        @NotNull
        @Getter(AccessLevel.PRIVATE)
        private String erhgoClassificationCode;

        @NotNull
        @Getter(AccessLevel.PRIVATE)
        private UUID userProfileId;
    }

    @EmbeddedId
    private UserErhgoClassification.ID id;

    @MapsId("erhgoClassificationCode")
    @ManyToOne(optional = false, cascade = {CascadeType.MERGE, CascadeType.REFRESH})
    @Getter
    private ErhgoClassification erhgoClassification;

    @MapsId("userProfileId")
    @ManyToOne(optional = false, cascade = {CascadeType.MERGE, CascadeType.REFRESH})
    private UserProfile userProfile;

    @Column(nullable = false)
    @Getter
    @Setter
    private boolean accepted;

    public UserErhgoClassification(UserProfile userProfile, ErhgoClassification erhgoClassification, boolean accepted) {
        this.id = new ID(erhgoClassification.getCode(), userProfile.uuid());
        this.userProfile = userProfile;
        this.accepted = accepted;
        this.erhgoClassification = erhgoClassification;
    }

    public boolean isRefused() {
        return !accepted;
    }

}
