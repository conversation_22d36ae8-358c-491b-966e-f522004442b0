package com.erhgo.domain.userprofile;

import com.erhgo.domain.AbstractAuditableEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


import java.time.OffsetDateTime;
import java.util.UUID;

@Slf4j
@Entity
@Builder
@EqualsAndHashCode(callSuper = true)
@Getter(AccessLevel.PUBLIC)
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Accessors(chain = true)
@Table(uniqueConstraints = @UniqueConstraint(columnNames = {"token"}))
public class UserMobileToken extends AbstractAuditableEntity {
    @Id
    @Column(columnDefinition = "BINARY(16)")
    @Builder.Default
    private UUID id = UUID.randomUUID();

    @Column(columnDefinition = "LONGTEXT")
    @Setter(AccessLevel.PACKAGE)
    @NotNull
    private String token;

    @ManyToOne(optional = false)
    @JoinColumn(nullable = false)
    private UserProfile userProfile;

    @Setter(AccessLevel.PUBLIC)
    @NotNull
    private OffsetDateTime timestamp;

    public UUID getUserProfileUUID() {
        return userProfile.uuid();
    }
}
