package com.erhgo.domain.userprofile.notification;

import com.erhgo.domain.enums.NotificationState;
import com.erhgo.domain.enums.NotificationType;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.userprofile.UserProfile;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import lombok.*;

import java.io.Serializable;

@Entity
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@EqualsAndHashCode(callSuper = true)
@DiscriminatorValue(RecruitmentNotification.ENTITY_TYPE)
@ToString(callSuper = true)
public class RecruitmentNotification extends AbstractNotification {
    public static final String ENTITY_TYPE = "RECRUITMENT";

    @ManyToOne(optional = false)
    @Getter
    private Recruitment recruitment;

    @Override
    public Serializable getEntityId() {
        return recruitment.getId();
    }

    @Override
    public String getEntityType() {
        return ENTITY_TYPE;
    }

    @Override
    public void accept(NotificationVisitor visitor) {
        visitor.visit(this);
    }

    @Builder
    public RecruitmentNotification(UserProfile userProfile, Recruitment recruitment, NotificationType type, NotificationState state) {
        super(userProfile, type, state);
        this.recruitment = recruitment;
        this.requiresMailSending = true;
    }

    public boolean requiresMailSendingActually() {
        return requiresMailSending
                && recruitment.isCandidatureReceivable()
                && !alreadySentByMail()
                && !getUserProfile().getBlacklistedOccupations().contains(recruitment.getErhgoOccupation());
    }
}
