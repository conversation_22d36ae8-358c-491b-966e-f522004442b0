package com.erhgo.domain.userprofile.experience;

import com.erhgo.domain.enums.Frequency;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.userprofile.UserProfile;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@NoArgsConstructor
@EqualsAndHashCode(of = {"context", "userProfile", "frequency"})
@Entity
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@Table(name = "ContextMet",
        uniqueConstraints = @UniqueConstraint(name = "UC_CONTEXT_MET", columnNames = {"context_id", "DTYPE", "userProfile_uuid"}))
public abstract class AbstractContextMet {

    @Id
    @Column(columnDefinition = "BINARY(16)")
    private UUID uuid = UUID.randomUUID();

    @ManyToOne(optional = false)
    @NotNull
    private Context context;

    @ManyToOne(optional = false)
    @NotNull
    private UserProfile userProfile;

    @NotNull
    private Frequency frequency;

    protected AbstractContextMet(@NotNull Context context, @NotNull UserProfile userProfile, @NotNull Frequency frequency) {
        this.context = context;
        this.userProfile = userProfile;
        this.frequency = frequency;
    }

    public abstract boolean isFormation();

    public abstract boolean isJob();

    public boolean matches() {
        return frequency.isMatches();
    }
}
