package com.erhgo.domain.userprofile.experience;

import com.erhgo.domain.enums.Frequency;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.userprofile.UserProfile;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToMany;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.HashSet;
import java.util.Set;

@Data
@Accessors(fluent = true)
@EqualsAndHashCode(callSuper = true, exclude = "userExperiences")
@Entity
@DiscriminatorValue("JOB")
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class JobContextMet extends AbstractContextMet {

    @Builder
    public JobContextMet(@NotNull Context context, @NotNull UserProfile userProfile, @NotNull Frequency frequency, @NotNull Set<UserExperience> userExperiences) {
        super(context, userProfile, frequency);
        this.userExperiences = userExperiences;
    }

    @ManyToMany
    @NotNull
    @Size(min = 1)
    private Set<UserExperience> userExperiences = new HashSet<>();

    public void removeExperience(UserExperience userExperience) {
        this.userExperiences.remove(userExperience);
        if (this.userExperiences.isEmpty()) {
            this.remove();
        }
    }

    public void remove() {
        this.getUserProfile().removeJobContextMetById(this.getContext().getId());
    }

    public void resetUserExperiences(Set<UserExperience> experiences) {
        if (this.userExperiences == null) {
            this.userExperiences = new HashSet<>();
        } else {
            this.userExperiences.clear();
        }
        this.userExperiences.addAll(experiences);
    }

    @Override
    public boolean isFormation() {
        return false;
    }

    @Override
    public boolean isJob() {
        return true;
    }
}

