package com.erhgo.domain.userprofile;

import com.erhgo.domain.enums.ContactTime;
import com.erhgo.domain.enums.DriverLicence;
import com.erhgo.domain.enums.Situation;
import com.erhgo.utils.StringUtils;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

@Accessors(chain = true)
@Data
@Entity
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class GeneralInformation implements GeneralInformationHolder {

    private static final Integer DEFAULT_WORKING_RADIUS_IN_KM = 50;
    private static final Integer MAX_RADIUS = 200;
    private static final String WHOLE_FRANCE_CODE = "00";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NonNull
    private String userId;

    @OneToOne(cascade = {CascadeType.MERGE, CascadeType.REFRESH})
    @JoinColumn(unique = true, nullable = false)
    private UserProfile userProfile;

    private String phoneNumber;

    private ContactTime contactTime;

    private Boolean smsBlacklisted;

    @Embedded
    @Getter(AccessLevel.PUBLIC)
    private Location location;

    private LocalDate birthDate;

    private Integer salary;

    @Setter
    @Getter(AccessLevel.PUBLIC)
    private Integer delayInMonth;

    @Enumerated(EnumType.STRING)
    private Situation situation;

    @Getter(AccessLevel.PUBLIC)
    private LocalDateTime lastMobileConnectionDate;

    @Embedded
    @Builder.Default
    private MailVerification mailVerificationState = new MailVerification().setState(MailVerification.MailVerificationState.UNKNOWN);

    @Override
    public Integer getContactTimeOrdinal() {
        return contactTime == null ? null : contactTime.ordinal();
    }

    @Override
    public Set<String> getChannels() {
        return userProfile.channels();
    }

    @Override
    public Boolean getTransactionalBlacklisted() {
        return userProfile.getJobOfferOptOut();
    }

    public DriverLicence getDriverLicence() {
        return userProfile.getDriverLicenceFromCriteria();
    }

    @Override
    public LocalDateTime getLastConnectionDate() {
        return userProfile.lastConnectionDate();
    }

    @Override
    public String getSource() {
        return Optional.ofNullable(userProfile).map(UserProfile::source).orElse(null);
    }

    public void setLocation(Location location) {
        if (!Objects.equals(this.location, location)) {
            this.userProfile.dirtiesUserProfile();
        }
        this.location = location;
        if (this.hasGeoLocInformationButNoRadius()) {
            this.location.setRadiusInKm(DEFAULT_WORKING_RADIUS_IN_KM);
        }
    }

    private boolean hasGeoLocInformationButNoRadius() {
        return this.location != null && this.location.getLongitude() != null && this.location.getRadiusInKm() == null;
    }

    public void setSalary(Integer salary) {
        if ((salary == null) != (this.salary == null)
                || (salary != null && !salary.equals(this.salary))) {
            userProfile.dirtiesUserProfile();
        }
        this.salary = salary;
    }

    public String getPhoneLabel(boolean forcePhone) {
        return StringUtils.getPhoneLabel(forcePhone, getSmsBlacklisted(), getPhoneNumber());
    }

    @Override
    public Integer getRadiusInKm() {
        return location != null ? location.getRadiusInKm() : null;
    }

    public void setRadiusInKm(Integer workingRadius) {
        location.setRadiusInKm(workingRadius);
    }

    public void markNewMobileConnection() {
        this.lastMobileConnectionDate = LocalDateTime.now();
    }
}
