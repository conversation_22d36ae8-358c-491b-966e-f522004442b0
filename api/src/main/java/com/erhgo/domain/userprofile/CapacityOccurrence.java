package com.erhgo.domain.userprofile;

import com.erhgo.domain.referential.Capacity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.io.Serializable;
import java.util.UUID;

@Data
@Entity
@EqualsAndHashCode(of = "id")
@NoArgsConstructor(access = AccessLevel.PRIVATE)
// Update and insert of this class are done through CapacityOccurrenceEventListener to handle occurrences increment/decrement
public class CapacityOccurrence {

    @Embeddable
    @EqualsAndHashCode
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    @AllArgsConstructor(access = AccessLevel.PUBLIC)
    public static class ID implements Serializable {

        @NotNull
        @Getter(AccessLevel.PRIVATE)
        private Long capacityId;

        @NotNull
        @Getter(AccessLevel.PRIVATE)
        private UUID userProfileId;
    }

    @EmbeddedId
    private CapacityOccurrence.ID id;

    @MapsId("capacityId")
    @ManyToOne(optional = false)
    private Capacity capacity;

    @MapsId("userProfileId")
    @ManyToOne(optional = false)
    private UserProfile userProfile;

    private int occurrence;

    private int recursiveOccurrence;

    void incrementOccurrence(boolean recursive) {
        if (recursive) {
            recursiveOccurrence++;
        } else {
            occurrence++;
        }
    }

    void decrementOccurrence(boolean recursive) {
        if (recursive) {
            recursiveOccurrence--;
        } else {
            occurrence--;
        }
    }

    boolean noMoreOccurrence() {
        return occurrence == 0 && recursiveOccurrence == 0;
    }

    @Builder
    public CapacityOccurrence(UserProfile userProfile, Capacity capacity, int occurrence, int recursiveOccurrence) {
        this.id = new CapacityOccurrence.ID(capacity.getId(), userProfile.uuid());
        this.userProfile = userProfile;
        this.capacity = capacity;
        this.occurrence = occurrence;
        this.recursiveOccurrence = recursiveOccurrence;
    }

    public int getScore() {
        return getOccurrence();
    }
}
