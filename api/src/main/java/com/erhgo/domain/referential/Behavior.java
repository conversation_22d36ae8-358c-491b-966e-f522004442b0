package com.erhgo.domain.referential;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.Titleable;
import com.erhgo.domain.enums.BehaviorCategory;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.util.UUID;


@Data
@Entity
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class Behavior extends AbstractAuditableEntity implements Titleable {

    @Id
    @Column(columnDefinition = "BINARY(16)")
    private UUID id = UUID.randomUUID();

    private String code;

    private String title;

    @Enumerated(EnumType.STRING)
    private BehaviorCategory behaviorCategory;

    private int categoryIndex;

    @Length(max = 2000)
    private String description;

}
