package com.erhgo.domain.referential;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.voodoodyne.jackson.jsog.JSOGGenerator;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

@Data
@Entity
@NoArgsConstructor
@JsonIdentityInfo(generator = JSOGGenerator.class)
@EqualsAndHashCode(exclude = "category")
@ToString(exclude = "category")
@lombok.Generated
public class CategoryLevel implements Serializable, Comparable<CategoryLevel> {

    public static final int HIGH_LEVEL_SCORE = 15;
    public static final int MEDIUM_LEVEL_SCORE = 9;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;


    private int score;

    @NonNull
    private String title;

    @NonNull
    @Column(length = 1024)
    @Length(max = 1024)
    private String description;
    @NonNull
    @ManyToOne
    private Category category;

    @Override
    public int compareTo(CategoryLevel categoryLevel) {
        return this.score - categoryLevel.getScore();
    }
}
