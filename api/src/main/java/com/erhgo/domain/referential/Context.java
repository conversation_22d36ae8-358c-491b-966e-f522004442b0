package com.erhgo.domain.referential;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.Titleable;
import com.erhgo.domain.validator.TitleRequired;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.Generated;
import org.hibernate.annotations.GenerationTime;


import java.util.UUID;

@Data
@Entity
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true, exclude = "categoryLevel")
@ToString(callSuper = true, exclude = "categoryLevel")
@TitleRequired
public class Context extends AbstractAuditableEntity implements Titleable {
    @Id
    @Column(columnDefinition = "BINARY(16)")
    private UUID id = UUID.randomUUID();

    @Generated(GenerationTime.INSERT)
    @Column(name = "indexForCode", nullable = false, unique = true, updatable = false, insertable = false)
    private Long index;

    private String title;

    @Column(length = 2000)
    private String description = "";

    @ManyToOne(cascade = CascadeType.MERGE, optional = false)
    @NotNull
    private CategoryLevel categoryLevel;

    private Origin origin;

    public String getCode() {
        return "CT-" + this.getIndex().toString();
    }

    public String getCategoryCode() {
        if (this.categoryLevel != null) {
            return this.categoryLevel.getCategory().getCode();
        }
        return null;
    }
}
