package com.erhgo.domain.referential;

import com.erhgo.domain.AbstractAuditableEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.*;

import org.hibernate.validator.constraints.Length;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

@Data
@Entity
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@ToString(callSuper = true, exclude = "inducedCapacities")
public class Activity extends AbstractAuditableEntity {

    @Id
    @Column(columnDefinition = "BINARY(16)")
    @Builder.Default
    protected UUID uuid = UUID.randomUUID();

    @Size(min = 1)
    @ManyToMany
    @Builder.Default
    private Set<Capacity> inducedCapacities = new HashSet<>();

    private Origin origin;

    @Column(length = 2000)
    @Length(max = 2000)
    private String description;

}
