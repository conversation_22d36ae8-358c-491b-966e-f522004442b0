package com.erhgo.domain.referential;

import com.erhgo.domain.AbstractAuditableEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import lombok.*;
import org.hibernate.validator.constraints.Length;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@EqualsAndHashCode(callSuper = false)
public class CustomEmailTemplate extends AbstractAuditableEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Length(max = 100)
    private String subject;

    @Length(max = 10000)
    private String content;

    @Length(max = 64)
    @Email
    @Column(nullable = false)
    private String emailFrom;

    @Length(max = 255)
    private String authorAlias;
}
