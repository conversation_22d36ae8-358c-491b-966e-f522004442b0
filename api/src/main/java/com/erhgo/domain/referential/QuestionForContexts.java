package com.erhgo.domain.referential;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.enums.Frequency;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.*;


import java.util.Set;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder
public class QuestionForContexts extends AbstractAuditableEntity {

    @Id
    @Column(columnDefinition = "BINARY(16)")
    @Builder.Default
    private UUID uuid = UUID.randomUUID();

    @Column(nullable = false)
    private String title;

    private String titleForNone;

    private String titleForLow;

    private String titleForMedium;

    private String titleForHigh;

    @ManyToMany
    @Size(min = 1)
    private Set<Context> contexts;

    public String getAnswerLabel(Frequency frequency) {
        switch (frequency) {
            case LOW:
                return titleForLow;
            case NONE:
                return titleForNone;
            case MEDIUM:
                return titleForMedium;
            case HIGH:
                return titleForHigh;
            default:
                return "";
        }

    }
}
