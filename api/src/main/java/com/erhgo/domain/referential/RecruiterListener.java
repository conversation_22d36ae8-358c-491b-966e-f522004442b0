package com.erhgo.domain.referential;

import com.erhgo.repositories.ConfigurablePropertyRepository;
import jakarta.persistence.PrePersist;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class RecruiterListener {

    private static final String DEFAULT_GDPR_MENTION_KEY = "default_gdpr_mention";

    private final ApplicationContext applicationContext;

    @PrePersist
    public void prePersist(Recruiter recruiter) {
        var repository = applicationContext.getBean(ConfigurablePropertyRepository.class);
        if (StringUtils.isBlank(recruiter.getGdprMention())) {
            var defaultGdprMention = repository.findOneByPropertyKey(DEFAULT_GDPR_MENTION_KEY).getPropertyValue();
            recruiter.setGdprMention(defaultGdprMention);
        }
    }
}
