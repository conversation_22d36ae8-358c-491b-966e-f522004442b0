package com.erhgo.domain.referential;

import com.erhgo.domain.AbstractEntity;
import com.erhgo.domain.validator.TitleRequired;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.voodoodyne.jackson.jsog.JSOGGenerator;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Data
@Entity
@NoArgsConstructor
@JsonIdentityInfo(generator = JSOGGenerator.class)
@EqualsAndHashCode(callSuper = true, exclude = {"inducedCapacities"})
@TitleRequired
@Builder
@AllArgsConstructor
@ToString(callSuper = true, exclude = {"inducedCapacities"})
@Cacheable
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class Capacity extends AbstractEntity {

    public static final String PREFIX_CODE_CAPABILITY = "CA";

    @ManyToMany(fetch = FetchType.EAGER) // @FIXME: https://odas-project.atlassian.net/browse/ODAS3-673
    @JoinTable(joinColumns = @JoinColumn(referencedColumnName = "id", name = "capacity_id"), inverseJoinColumns = @JoinColumn(referencedColumnName = "id", name = "induced_capacity_id"))
    @org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
    @Builder.Default
    private Set<Capacity> inducedCapacities = new HashSet<>();

    @Override
    public String getPrefix() {
        return PREFIX_CODE_CAPABILITY;
    }

    @Override
    public int getSuffixLeftPadCount() {
        return 3;
    }

    public Stream<Capacity> getInducedCapacitiesRecursively() {
        Stream<Capacity> allCapacities;
        if (getInducedCapacities() != null && !getInducedCapacities().isEmpty()) {
            allCapacities = Stream.concat(getInducedCapacities().stream().flatMap(Capacity::getInducedCapacitiesRecursively), getInducedCapacities().stream());
        } else {
            allCapacities = Stream.empty();
        }
        return allCapacities;
    }

    public int getLevel() {
        return Integer.parseInt("" + getCode().replace(getPrefix(), "").charAt(0));
    }

    public int getUniversalId() {
        var beginIndex = getPrefix().length() + 1;
        return Integer.parseInt(getCode().substring(beginIndex, beginIndex + 2));
    }

    public static Map<Integer, Long> buildBitmaskPerLevelForCapacities(Collection<Capacity> capacities) {
        Map<Integer, Long> bitmaskMap = capacities.stream()
                .distinct()
                .collect(Collectors.groupingBy(
                        Capacity::getLevel,
                        HashMap::new,
                        Collectors.mapping(
                                cap -> 1L << (cap.getUniversalId() - 1),
                                Collectors.reducing(0L, Long::sum)
                        )
                ));

        bitmaskMap.putIfAbsent(1, 0L);
        bitmaskMap.putIfAbsent(2, 0L);
        bitmaskMap.putIfAbsent(3, 0L);
        return bitmaskMap;
    }
}
