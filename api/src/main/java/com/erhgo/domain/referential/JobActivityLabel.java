package com.erhgo.domain.referential;

import com.erhgo.domain.validator.TitleRequired;
import jakarta.persistence.Entity;
import lombok.*;

import java.util.UUID;

@Data
@Entity
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TitleRequired
@ToString(callSuper = true)
public class JobActivityLabel extends AbstractActivityLabel {

    @Builder
    public JobActivityLabel(UUID uuid, Activity activity, String title, Integer position) {
        super(uuid == null ? UUID.randomUUID() : uuid, activity, title, position);
    }

    @Override
    public boolean isJob() {
        return true;
    }
}
