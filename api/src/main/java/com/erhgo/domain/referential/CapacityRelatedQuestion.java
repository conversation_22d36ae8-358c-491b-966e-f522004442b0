package com.erhgo.domain.referential;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.enums.QuestionType;
import jakarta.persistence.*;
import lombok.*;


import java.util.Collections;
import java.util.SortedSet;
import java.util.TreeSet;
import java.util.UUID;

@Entity
@Builder
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@EqualsAndHashCode(of = "id", callSuper = false)
@ToString(exclude = "responses")
public class CapacityRelatedQuestion extends AbstractAuditableEntity {

    @Id
    @Column(columnDefinition = "BINARY(16)")
    @Getter
    @Builder.Default
    private UUID id = UUID.randomUUID();

    @Getter
    private String title;

    @Getter
    @Setter
    private int questionIndex;

    @OneToMany(mappedBy = "question", cascade = CascadeType.ALL, orphanRemoval = true)
    @Builder.Default
    private SortedSet<CapacityRelatedQuestionResponse> responses = new TreeSet<>();

    @Enumerated(EnumType.STRING)
    @Column(updatable = false)
    @Getter
    private QuestionType questionType;

    void addResponse(CapacityRelatedQuestionResponse response) {
        if (responses == null) {
            responses = new TreeSet<>();
        }
        this.responses.add(response);
    }

    public SortedSet<CapacityRelatedQuestionResponse> getResponses() {
        return Collections.unmodifiableSortedSet(responses);
    }

}
