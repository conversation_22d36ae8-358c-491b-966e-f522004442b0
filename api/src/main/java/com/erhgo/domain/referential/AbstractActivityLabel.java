package com.erhgo.domain.referential;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.Titleable;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Set;
import java.util.UUID;

@Data
@MappedSuperclass
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true, exclude = "activity")
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
public abstract class AbstractActivityLabel extends AbstractAuditableEntity implements Titleable {

    @Id
    @Column(columnDefinition = "BINARY(16)")
    protected UUID uuid = UUID.randomUUID();

    @ManyToOne(cascade = {CascadeType.MERGE}, optional = false)
    private Activity activity;

    private String title;

    // Index of given Activity label in activity detail view
    private Integer position;

    public Set<Capacity> getInducedCapacities() {
        return activity.getInducedCapacities();
    }

    public String getDescription() {
        return activity.getDescription();
    }

    public abstract boolean isJob();


    public Collection<Capacity> getInducedCapacitiesRecursively(boolean distinct) {
        Collection<Capacity> allCapacities = getActivity().getInducedCapacities().stream().distinct().collect(
                ArrayList::new,
                (capacities, capacity) -> {
                    capacities.add(capacity);
                    capacities.addAll(capacity.getInducedCapacitiesRecursively().toList());
                },
                ArrayList::addAll
        );

        if (distinct) {
            allCapacities = allCapacities.stream().distinct().toList();
        }
        return allCapacities;
    }

}
