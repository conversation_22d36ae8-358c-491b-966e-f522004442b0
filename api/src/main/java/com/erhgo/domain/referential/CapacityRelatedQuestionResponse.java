package com.erhgo.domain.referential;

import jakarta.persistence.*;
import lombok.*;

import java.util.Collections;
import java.util.Comparator;
import java.util.Set;
import java.util.UUID;

@Entity
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@EqualsAndHashCode(of = "id")
@ToString(exclude = "question")
public class CapacityRelatedQuestionResponse implements Comparable<CapacityRelatedQuestionResponse> {

    @Builder
    public CapacityRelatedQuestionResponse(UUID id, String title, int position, Set<Capacity> capacities, CapacityRelatedQuestion question) {
        this.id = id == null ? UUID.randomUUID() : id;
        this.title = title;
        this.position = position;
        this.capacities = capacities;
        this.question = question;
        question.addResponse(this);
    }

    @Id
    @Column(columnDefinition = "BINARY(16)")
    @Getter
    private UUID id = UUID.randomUUID();

    @Getter
    private String title;

    @Getter
    private int position;

    @ManyToMany
    private Set<Capacity> capacities;

    @ManyToOne(optional = false)
    @Getter
    private CapacityRelatedQuestion question;

    @Override
    public int compareTo(CapacityRelatedQuestionResponse o) {
        return Comparator.comparing(CapacityRelatedQuestionResponse::getPosition)
                .thenComparing(r -> r.getQuestion().getId())
                .compare(this, o);
    }

    public Set<Capacity> getCapacities() {
        return Collections.unmodifiableSet(capacities);
    }

}
