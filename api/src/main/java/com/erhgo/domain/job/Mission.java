package com.erhgo.domain.job;

import com.erhgo.domain.AbstractEntity;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.referential.*;
import com.erhgo.domain.validator.TitleRequired;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@Data
@Entity
@NoArgsConstructor
@TitleRequired
@ToString(exclude = "job", callSuper = true)
@EqualsAndHashCode(exclude = {"job"}, callSuper = true)
@Builder
@AllArgsConstructor
public class Mission extends AbstractEntity implements Comparable<Mission> {
    private static final String DEFAULT_TITLE = "Mission principale";

    @NotNull
    @ManyToOne
    private Job job;

    @ManyToMany
    @NotNull
    @Builder.Default
    private Set<JobActivityLabel> activities = new HashSet<>();

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @NotNull
    @Builder.Default
    private Set<ContextsForCategory> contextsForCategory = new HashSet<>();

    private Integer position;

    public Mission(Job job,
                   String title,
                   Set<JobActivityLabel> activities,
                   Set<ContextsForCategory> contextsForCategory) {
        job.addMission(this);
        setTitle(title);
        this.activities = activities;
        this.resetContexts(contextsForCategory);
    }

    @Override
    public String getPrefix() {
        return "M";
    }

    @Override
    public int getSuffixLeftPadCount() {
        return 5;
    }

    public void resetActivitiesIds(Set<JobActivityLabel> activities) {
        if (this.activities == null) {
            this.activities = new HashSet<>(activities.size());
        } else {
            this.activities.clear();
        }
        this.activities.addAll(activities);
    }

    public List<ContextsForCategory> resetContexts(Set<ContextsForCategory> contextsForCategory) {
        List<ContextsForCategory> toDelete = Lists.newArrayList();
        if (this.contextsForCategory == null) {
            this.contextsForCategory = Sets.newHashSet();
        } else {
            toDelete.addAll(this.contextsForCategory);
            this.contextsForCategory.clear();
        }
        for (var context : contextsForCategory) {
            // Next lines to cleanup contextsForCategory parameter, and ensure only one contextsForCategory element exists for one category
            var savedContext = this.contextsForCategory.stream()
                    .filter(c -> c.getCategory().getId().equals(context.getCategory().getId()))
                    .findFirst();
            if (savedContext.isEmpty()) {
                this.contextsForCategory.add(context);
            } else {
                savedContext.get().setContexts(context.getContexts());
                savedContext.get().setNoContextForCategory(context.getNoContextForCategory());
            }
        }
        job.refreshLevel();
        return toDelete;
    }

    @Override
    public int compareTo(Mission otherMission) {
        return Comparator
                .comparing(Mission::getPosition, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(Mission::getId, Comparator.nullsLast(Comparator.naturalOrder()))
                .compare(this, otherMission);

    }

    static Mission createForErhgo(ErhgoOccupation occupation, Job job, Iterable<Category> allCategories) {

        var contextsForCategoryMap = job.getJobType() == JobType.SIMPLE ? new HashMap<Category, List<Context>>() : occupation.getContexts()
                .stream()
                .collect(Collectors.groupingBy(c -> c.getCategoryLevel().getCategory()));

        var contextsForCategories = StreamSupport.stream(allCategories.spliterator(), false)
                .map(category -> {
                    var contexts = contextsForCategoryMap.get(category);
                    return ContextsForCategory.builder()
                            .contexts(contexts == null ? null : Sets.newHashSet(contexts))
                            .category(category)
                            .noContextForCategory(contexts == null || contexts.isEmpty())
                            .build();
                })
                .collect(Collectors.toSet());

        var mission = Mission.builder()
                .activities(occupation.getActivities() == null ? new HashSet<>() : occupation.getActivities())
                .contextsForCategory(contextsForCategories)
                .job(job)
                .build();

        mission.setTitle(DEFAULT_TITLE);

        return mission;
    }

    public int computeLevel() {
        var score = MasteryLevel.DEFAULT_LEVEL_AS_INT;
        if (contextsForCategory != null) {
            score = contextsForCategory.stream().mapToInt(ContextsForCategory::getScore).sum();
        }
        return getLevelFromScore(score);
    }

    private int getLevelFromScore(int score) {
        var level = 1;
        if (score > 20) {
            level++;
        }
        if (score > 40) {
            level++;
        }
        if (score > 70) {
            level++;
        }
        if (score > 90) {
            level++;
        }
        return level;
    }

    public void replaceActivities(Collection<JobActivityLabel> oldLabels, JobActivityLabel newLabel) {
        this.activities.removeIf(a -> oldLabels.stream().anyMatch(o -> o.getUuid().equals(a.getUuid())));
        this.activities.add(newLabel);
    }

    private void addContext(Context context) {
        var targetContextForCategory = this.getContextsForCategory().stream()
                .filter(cfc -> cfc.getCategory().getCode().equals(context.getCategoryCode()))
                .findFirst()
                .orElseGet(() -> buildContextsForCategory(context));
        targetContextForCategory.addContext(context);
    }

    private ContextsForCategory buildContextsForCategory(Context context) {
        var contextForCategory = ContextsForCategory.builder().category(context.getCategoryLevel().getCategory()).build();
        this.contextsForCategory.add(contextForCategory);
        return contextForCategory;
    }

    public void replaceContextCategory(Context context) {
        var contextsForOldCategory = getContextsForCategory().stream().filter(cfc -> cfc.getContexts().stream().anyMatch(c -> c.getId().equals(context.getId())));
        contextsForOldCategory.forEach(c -> c.removeContext(context));
        addContext(context);
        job.refreshLevel();
    }

    public Collection<Capacity> getAllCapacities() {
        return getActivities().stream().map(AbstractActivityLabel::getActivity).flatMap(a -> a.getInducedCapacities().stream()).collect(Collectors.toSet());
    }

    public Mission duplicates(Job job) {
        var mission = Mission.builder()
                .job(job)
                .activities(new HashSet<>(activities))
                .position(position)
                .contextsForCategory(contextsForCategory.stream().map(cfc -> ContextsForCategory.builder()
                        .category(cfc.getCategory())
                        .contexts(new HashSet<>(cfc.getContexts()))
                        .noContextForCategory(cfc.getNoContextForCategory())
                        .build()).collect(Collectors.toSet()))
                .build();
        mission.setTitle(getTitle());
        mission.setDescription(getDescription());
        return mission;
    }
}
