package com.erhgo.domain.job;

import com.erhgo.domain.referential.Category;
import com.erhgo.domain.referential.CategoryLevel;
import com.erhgo.domain.referential.Context;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ContextsForCategory implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToMany
    private Set<Context> contexts;

    @ManyToOne(optional = false)
    private Category category;

    @Builder.Default
    private Boolean noContextForCategory = false;

    public int getScore() {
        return Boolean.TRUE.equals(noContextForCategory) ? 0 : contexts.stream().map(Context::getCategoryLevel).mapToInt(CategoryLevel::getScore).max().orElse(0);
    }

    public void removeContext(Context context) {
        contexts.removeIf(c -> c.getId().equals(context.getId()));
        if (contexts.isEmpty()) {
            this.noContextForCategory = true;
        }
    }

    public void addContext(Context context) {
        if (contexts == null) {
            contexts = new HashSet<>();
        }
        contexts.add(context);
        this.noContextForCategory = false;
    }
}
