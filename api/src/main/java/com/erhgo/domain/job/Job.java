package com.erhgo.domain.job;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.Titleable;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.JobEvaluationState;
import com.erhgo.domain.enums.SourcingCriteriaStep;
import com.erhgo.domain.enums.TypeContractCategory;
import com.erhgo.domain.enums.TypeWorkingTime;
import com.erhgo.domain.exceptions.NoSuchMissionInJob;
import com.erhgo.domain.exceptions.UnexpectedNumberOfMissionsException;
import com.erhgo.domain.referential.*;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.validator.TitleRequired;
import com.google.common.annotations.VisibleForTesting;
import jakarta.persistence.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.validator.constraints.Length;

import java.time.OffsetDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true, exclude = {"missions", "observators", "behaviors", "criteriaValues"})
@Builder
@TitleRequired
public class Job extends AbstractAuditableEntity implements Titleable {

    @Embedded
    private Location location;

    @Id
    @Column(columnDefinition = "BINARY(16)")
    @Builder.Default
    private UUID id = UUID.randomUUID();

    @NotNull
    private String title;

    @Length(max = 2000)
    private String description;

    @JoinColumn(nullable = false)
    @ManyToOne(optional = false, fetch = FetchType.LAZY, cascade = {CascadeType.MERGE, CascadeType.REFRESH})
    private Recruiter recruiter;

    @ManyToOne
    private Employer employer;

    @Length(max = 50)
    private String service;

    @ManyToOne
    private ErhgoOccupation erhgoOccupation;

    @ElementCollection
    private Set<String> observators;

    private OffsetDateTime observationDate;
    private OffsetDateTime publicationDate;

    @Builder.Default

    private JobEvaluationState state = JobEvaluationState.LISTED;

    @ManyToMany
    @Builder.Default
    @ToString.Exclude
    private Set<Behavior> behaviors = new HashSet<>();

    @ManyToMany
    @Builder.Default
    @ToString.Exclude
    private Set<CriteriaValue> criteriaValues = new HashSet<>();

    @OneToMany(mappedBy = "job", cascade = CascadeType.ALL, orphanRemoval = true)
    @ToString.Exclude
    @Builder.Default
    private SortedSet<@Valid Mission> missions = new TreeSet<>();

    // Recommendation may have more than 20k chars, use TEXT column definition
    @Column(columnDefinition = "LONGTEXT")
    private String recommendation;

    private MasteryLevel masteryLevel;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private JobType jobType;

    @VisibleForTesting
    public void addMission(Mission mission) {
        if (this.missions == null) {
            this.missions = new TreeSet<>();
        }
        if (mission.getPosition() == null) {
            // if no position is given, we put it last
            mission.setPosition(this.missions.size());
        }
        this.missions.add(mission);
        mission.setJob(this);
    }

    public Optional<JobActivityLabel> getActivity(UUID activityId) {
        return getMissions().stream().flatMap(m -> m.getActivities().stream())
                .filter(a -> a.getUuid().equals(activityId))
                .findFirst();
    }

    public Optional<Context> getContext(UUID contextId) {
        return getMissions().stream()
                .filter(m -> m.getContextsForCategory() != null)
                .flatMap(m -> m.getContextsForCategory().stream())
                .filter(m -> m.getContexts() != null)
                .flatMap(c -> c.getContexts().stream())
                .filter(a -> a.getId().equals(contextId))
                .findFirst();
    }

    public Set<JobActivityLabel> getAllMissionsActivities() {
        return getMissions() == null ? Collections.emptySet() : getMissions().stream()
                .filter(m -> m.getActivities() != null)
                .flatMap(m -> m.getActivities().stream())
                .collect(Collectors.toSet());
    }

    public Set<Context> getAllMissionsContexts() {
        return getMissions() == null ? Collections.emptySet() : getMissions().stream()
                .filter(m -> m.getContextsForCategory() != null)
                .flatMap(m -> m.getContextsForCategory().stream())
                .filter(m -> m.getContexts() != null)
                .flatMap(m -> m.getContexts().stream())
                .collect(Collectors.toSet());
    }

    public boolean isModifiable() {
        return state == null || state.isModifiable();
    }

    public void reorderMissions(List<Long> missionsIds) {
        var missionsMap = getMissions().stream().collect(Collectors.toMap(Mission::getId, java.util.function.Function.identity()));

        if (missionsIds.size() != missionsMap.size()) {
            throw new UnexpectedNumberOfMissionsException(missionsMap.size(), missionsIds.size());
        }

        IntStream.range(0, missionsIds.size()).forEach(position -> {
            var missionId = missionsIds.get(position);
            var mission = missionsMap.get(missionId);
            if (mission == null) {
                throw new NoSuchMissionInJob(missionId, this.getId());
            }

            mission.setPosition(position);
        });

    }

    public static Job createForErhgoOccupation(
            ErhgoOccupation erhgoOccupation,
            Recruiter recruiter,
            Iterable<Category> allCategories,
            JobType jobType
    ) {
        var job = Job.builder()
                .recruiter(recruiter)
                .behaviors(erhgoOccupation.getBehaviors())
                .state(JobEvaluationState.LISTED)
                .jobType(jobType)
                .build();

        return job.reworkWithErhgoOccupation(erhgoOccupation, allCategories);
    }

    public Job reworkWithErhgoOccupation(ErhgoOccupation erhgoOccupation, Iterable<Category> allCategories) {
        if (this.erhgoOccupation != null && this.erhgoOccupation.getId().equals(erhgoOccupation.getId())) {
            return this;
        }
        if (missions != null) {
            missions.forEach(m -> m.setJob(null));
            missions.clear();
        }
        setTitle(erhgoOccupation.getTitle());
        addMission(Mission.createForErhgo(erhgoOccupation, this, allCategories));
        setErhgoOccupation(erhgoOccupation);
        setMasteryLevel(erhgoOccupation.getLevel());
        return this;
    }

    public Map<Activity, LevelForActivity> getLevelsForActivities() {
        Map<Activity, LevelForActivity> map = new HashMap<>();

        missions.forEach(m -> {
            var level = m.computeLevel();
            m.getActivities().forEach(activity -> putActivityIfLevelGreater(map, level, activity.getActivity(), m));
        });

        return map;
    }

    private void putActivityIfLevelGreater(Map<Activity, LevelForActivity> map, int level, Activity activity, Mission referenceMission) {
        var levelForActivity = map.get(activity);
        if (levelForActivity == null) {
            levelForActivity = LevelForActivity.builder().level(level).referenceMission(referenceMission).build();
            map.put(activity, levelForActivity);
        } else if (levelForActivity.getLevel() < level) {
            levelForActivity.setLevel(level);
            levelForActivity.setReferenceMission(referenceMission);
        }
    }

    public List<Capacity> getAllCapacities() {
        return getAllMissionsActivities().stream().map(JobActivityLabel::getActivity).map(Activity::getInducedCapacities).flatMap(Collection::stream).toList();
    }

    public Map<Capacity, Integer> getLevelForCapacities() {
        var levelForCapacities = new HashMap<Capacity, Integer>();
        getMissions().forEach(m -> m.getAllCapacities().forEach(capacity -> putCapacityIfLevelGreater(levelForCapacities, m.computeLevel(), capacity)));
        return levelForCapacities;
    }

    private void putCapacityIfLevelGreater(Map<Capacity, Integer> levelForCapacities, int level, Capacity capacity) {
        var levelForCapacity = levelForCapacities.get(capacity);
        if (levelForCapacity == null || levelForCapacity < level) {
            levelForCapacities.put(capacity, level);
        }
    }

    public void refreshLevel() {
        if (erhgoOccupation == null) {
            this.masteryLevel = this.missions.stream()
                    .mapToInt(Mission::computeLevel)
                    .max()
                    .stream()
                    .mapToObj(MasteryLevel::forLevel)
                    .findFirst()
                    .orElse(null);
        }
    }

    public void resetCriteriaValues(Collection<CriteriaValue> criteriaValues) {
        this.resetCriteriaValues(criteriaValues, null);
    }

    public void resetCriteriaValues(Collection<CriteriaValue> criteriaValues, SourcingCriteriaStep considerOnlyCriteriaOfStep) {
        this.criteriaValues.removeIf(cv -> !criteriaValues.contains(cv) && (considerOnlyCriteriaOfStep == null || considerOnlyCriteriaOfStep == cv.getSourcingCriteriaStep()));
        criteriaValues.stream().filter(cv -> considerOnlyCriteriaOfStep == null || considerOnlyCriteriaOfStep == cv.getSourcingCriteriaStep()).forEach(this.criteriaValues::add);
    }

    public void clearCriteria(SourcingCriteriaStep onlyValueOfStep) {
        this.criteriaValues.removeIf(c -> onlyValueOfStep == null || c.getSourcingCriteriaStep() == onlyValueOfStep);
    }

    public String getOrganizationName() {
        return Optional.ofNullable((AbstractOrganization) employer).orElse(recruiter).getTitle();
    }

    public String getEmployerCode() {
        return employer == null ? null : employer.getCode();
    }

    public String getRecruiterCode() {
        return recruiter == null ? null : recruiter.getCode();
    }

    public String getRecruiterTitle() {
        return recruiter.getTitle();
    }

    public String getEmployerTitle() {
        return employer == null ? null : employer.getTitle();
    }

    public boolean isPrivate() {
        return getRecruiter() != null && getRecruiter().isPrivateJobs();
    }

    public Collection<TypeWorkingTime> getWorkingTimes() {
        return getValueForCriteria(CriteriaValue.WORKING_TIME_FOR_CRITERIA_RESPONSE);
    }

    public Collection<TypeContractCategory> getContractTypes() {
        return getValueForCriteria(CriteriaValue.TYPE_CONTRACT_FOR_CRITERIA_RESPONSE);
    }

    private <A extends Enum<A>> Set<A> getValueForCriteria(Map<String, A> provider) {
        return criteriaValues
                .stream()
                .map(CriteriaValue::getCode)
                .map(provider::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    public List<String> getCriteriaValuesCodes() {
        return criteriaValues.stream().map(CriteriaValue::getCode).toList();
    }

    public void publish() {
        if (state != JobEvaluationState.PUBLISHED) {
            state = JobEvaluationState.PUBLISHED;
            publicationDate = OffsetDateTime.now();
        }
    }

    public boolean hasOccupation(ErhgoOccupation occupation) {
        return occupation != null && getErhgoOccupation() != null && getErhgoOccupation().getId().equals(occupation.getId());
    }

    public Job duplicates() {
        var job = Job.builder()
                .jobType(jobType)
                .behaviors(new HashSet<>(behaviors))
                .criteriaValues(new HashSet<>(criteriaValues))
                .erhgoOccupation(erhgoOccupation)
                .description(description)
                .employer(employer)
                .recruiter(recruiter)
                .masteryLevel(masteryLevel)
                .title(title)
                .build();
        job.missions = missions.stream().map(m -> m.duplicates(job)).collect(Collectors.toCollection(TreeSet::new));
        return job;
    }


    @Data
    @Builder
    public static class LevelForActivity {
        private int level;
        private Mission referenceMission;
    }
}
