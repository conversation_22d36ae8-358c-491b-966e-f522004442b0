package com.erhgo.security;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class AuthorizeExpression {

    private static final String HAS_PERMISSION = "hasPermission(#";

    private static final String HAS_ROLE_ADMIN_OR_HAS_PERMISSION = "hasRole('" + Role.ODAS_ADMIN + "') or " + HAS_PERMISSION;
    private static final String HAS_ROLE_ADMIN = "hasRole('" + Role.ODAS_ADMIN + "')";
    private static final String READ = ", 'read')";
    private static final String WRITE = ", 'write')";
    private static final String COMMAND = "command.";

    private static final String ORGANIZATION_TYPE = ", 'Recruiter'";
    private static final String ORGANIZATION_CODE = "organizationCode";
    private static final String ORGANIZATION_CODES = "organizationCodes";

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class JOB {
        private static final String JOB_TYPE = ", 'Job'";
        private static final String JOB_ID = "jobId";
        public static final String JOB_WRITE = HAS_ROLE_ADMIN_OR_HAS_PERMISSION + JOB_ID + JOB_TYPE + WRITE;
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class CANDIDATURE {
        private static final String CANDIDATURE_TYPE = ", 'RecruitmentCandidature'";
        private static final String CANDIDATURE_ID = "candidatureId";

        public static final String CANDIDATURE_READ = HAS_ROLE_ADMIN_OR_HAS_PERMISSION + CANDIDATURE_ID + CANDIDATURE_TYPE + READ;
        public static final String CANDIDATURE_WRITE = HAS_ROLE_ADMIN_OR_HAS_PERMISSION + CANDIDATURE_ID + CANDIDATURE_TYPE + WRITE;
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class RECRUITMENT {

        private static final String RECRUITMENT_TYPE = ", 'Recruitment'";
        private static final String RECRUITMENT_ID = "recruitmentId";

        public static final String RECRUITMENT_READ = HAS_ROLE_ADMIN_OR_HAS_PERMISSION + RECRUITMENT_ID + RECRUITMENT_TYPE + READ;
        public static final String RECRUITMENT_WRITE = HAS_ROLE_ADMIN_OR_HAS_PERMISSION + RECRUITMENT_ID + RECRUITMENT_TYPE + WRITE;
        public static final String RECRUITMENT_OF_COMMAND_WRITE = HAS_ROLE_ADMIN_OR_HAS_PERMISSION + COMMAND + RECRUITMENT_ID + RECRUITMENT_TYPE + WRITE;
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class USER {
        private static final String USER_ID = "userId";
        private static final String COMMAND_USER_ID = COMMAND + USER_ID;
        private static final String USER_PROFILE_TYPE = ", 'UserProfile'";

        public static final String USER_PROFILE_OF_COMMAND_WRITE = HAS_ROLE_ADMIN_OR_HAS_PERMISSION + COMMAND_USER_ID + USER_PROFILE_TYPE + WRITE;
        public static final String USER_PROFILE_WRITE = HAS_ROLE_ADMIN_OR_HAS_PERMISSION + USER_ID + USER_PROFILE_TYPE + WRITE;
        public static final String USER_PROFILE_READ = HAS_ROLE_ADMIN_OR_HAS_PERMISSION + USER_ID + USER_PROFILE_TYPE + READ;
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class ORGANIZATION {

        private static final String COMMAND_ORGANIZATION_CODE = COMMAND + ORGANIZATION_CODE;
        public static final String ORGANIZATION_OF_COMMAND_WRITE = HAS_ROLE_ADMIN_OR_HAS_PERMISSION + COMMAND_ORGANIZATION_CODE + ORGANIZATION_TYPE + READ;
        public static final String ORGANIZATION_READ = HAS_ROLE_ADMIN + "or hasRole (#" + ORGANIZATION_CODE + ")";
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class USER_EXPERIENCE {
        private static final String EXPERIENCE_ID = "experienceId";
        private static final String USER_EXPERIENCE_TYPE = ", 'UserExperience'";
        public static final String USER_EXPERIENCE_WRITE = HAS_ROLE_ADMIN_OR_HAS_PERMISSION + EXPERIENCE_ID + USER_EXPERIENCE_TYPE + WRITE;
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class USER_NOTIFICATION {
        private static final String NOTIFICATION_TYPE = ", 'AbstractNotification'";
        private static final String NOTIFICATION_ID = "notificationId";

        public static final String NOTIFICATION_WRITE = HAS_ROLE_ADMIN_OR_HAS_PERMISSION + NOTIFICATION_ID + NOTIFICATION_TYPE + WRITE;
    }
}
