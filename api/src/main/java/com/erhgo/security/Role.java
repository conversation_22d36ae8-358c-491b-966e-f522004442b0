package com.erhgo.security;

import com.erhgo.domain.referential.Recruiter;
import com.google.common.base.Strings;

public final class Role {
    private Role() {
    }

    public static final String ODAS_ADMIN = "ODAS_ADMIN";
    public static final String CANDIDATE = "CANDIDATE";
    public static final String SOURCING = "SOURCING";

    public static final String CANDIDATES_GROUP = "CANDIDATES";

    public static boolean isOrganizationRoleOrGroup(String role) {
        return !Strings.isNullOrEmpty(role) && Recruiter.PREFIXES.contains(role.substring(0, 2));
    }
}
