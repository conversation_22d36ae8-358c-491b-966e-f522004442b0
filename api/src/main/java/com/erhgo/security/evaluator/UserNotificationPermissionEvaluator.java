package com.erhgo.security.evaluator;

import com.erhgo.domain.userprofile.notification.AbstractNotification;
import com.erhgo.security.PermissionLevel;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

@Service
public class UserNotificationPermissionEvaluator extends AbstractEntityPermissionEvaluator<AbstractNotification> {
    public UserNotificationPermissionEvaluator(SecurityService securityService) {
        super(AbstractNotification.class, securityService);
    }

    @Override
    public boolean hasPermission(Authentication authentication, AbstractNotification notification, PermissionLevel permissionLevel) {
        var permitted = false;
        if (securityService.hasRole(authentication, Role.CANDIDATE)) {
            permitted = securityService.getKeycloakUserId(authentication).filter(id -> notification.getUserProfile().userId().equals(id)).isPresent();
        }
        return permitted;
    }

}
