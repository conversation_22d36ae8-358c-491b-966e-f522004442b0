package com.erhgo.security.evaluator;

import com.erhgo.domain.recruitment.RecruitmentProfile;
import com.erhgo.security.PermissionLevel;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

@Service
public class RecruitmentProfilePermissionEvaluator extends AbstractEntityPermissionEvaluator<RecruitmentProfile> {

    public RecruitmentProfilePermissionEvaluator(SecurityService securityService) {
        super(RecruitmentProfile.class, securityService);
    }

    @Override
    public boolean hasPermission(Authentication authentication, RecruitmentProfile recruitment, PermissionLevel permissionLevel) {

        return securityService.hasAnyRole(authentication, recruitment.getJob().getRecruiterCode(), recruitment.getJob().getEmployerCode()) &&
                (securityService.hasRole(authentication, Role.SOURCING));

    }

}
