package com.erhgo.security.evaluator;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.security.PermissionLevel;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

@Service
public class RecruitmentCandidaturePermissionEvaluator extends AbstractEntityPermissionEvaluator<RecruitmentCandidature> {

    public RecruitmentCandidaturePermissionEvaluator(SecurityService securityService) {
        super(RecruitmentCandidature.class, securityService);
    }

    @Override
    public boolean hasPermission(Authentication authentication, RecruitmentCandidature candidature, PermissionLevel permissionLevel) {
        var permitted = false;
        if (securityService.hasRole(authentication, Role.CANDIDATE)) {
            permitted = securityService.getKeycloakUserId(authentication).filter(id -> candidature.getUserProfile().userId().equals(id)).isPresent();
        }
        if (!permitted && securityService.hasAnyRole(authentication, Role.SOURCING)) {
            permitted = securityService.hasAnyRole(authentication, candidature.getRecruitment().getOrganizationCode(), candidature.getRecruitment().getEmployerCode());
        }
        return permitted;
    }

}
