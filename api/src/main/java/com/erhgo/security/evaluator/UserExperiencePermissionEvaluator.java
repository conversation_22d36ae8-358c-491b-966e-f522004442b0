package com.erhgo.security.evaluator;

import com.erhgo.domain.userprofile.experience.UserExperience;
import com.erhgo.security.PermissionLevel;
import com.erhgo.services.SecurityService;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

@Service
public class UserExperiencePermissionEvaluator extends AbstractEntityPermissionEvaluator<UserExperience> {

    private final UserProfilePermissionEvaluator userProfilePermissionEvaluator;

    public UserExperiencePermissionEvaluator(SecurityService securityService, UserProfilePermissionEvaluator userProfilePermissionEvaluator) {
        super(UserExperience.class, securityService);
        this.userProfilePermissionEvaluator = userProfilePermissionEvaluator;
    }

    @Override
    public boolean hasPermission(Authentication authentication, UserExperience userExperience, PermissionLevel permissionLevel) {
        return userProfilePermissionEvaluator.hasPermission(authentication, userExperience.getUserProfile(), permissionLevel);
    }

}
