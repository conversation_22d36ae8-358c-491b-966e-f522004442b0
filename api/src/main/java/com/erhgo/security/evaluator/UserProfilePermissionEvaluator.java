package com.erhgo.security.evaluator;

import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.security.PermissionLevel;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

@Service
public class UserProfilePermissionEvaluator extends AbstractEntityPermissionEvaluator<UserProfile> {

    public UserProfilePermissionEvaluator(SecurityService securityService) {
        super(UserProfile.class, securityService);
    }

    @Override
    public boolean hasPermission(Authentication authentication, UserProfile userProfile, PermissionLevel permissionLevel) {
        return isSourcingUserAuthorized(authentication, userProfile) || isFOUserAuthorized(authentication, userProfile);
    }

    private boolean isFOUserAuthorized(Authentication authentication, UserProfile userProfile) {
        return securityService.getKeycloakUserId(authentication).filter(id -> userProfile.userId().equals(id)).isPresent();
    }

    private boolean isSourcingUserAuthorized(Authentication authentication, UserProfile userProfile) {
        return userProfile.channels() != null
                && !userProfile.channels().isEmpty()
                && securityService.hasRole(authentication, Role.SOURCING)
                && securityService.hasAnyRole(authentication, userProfile.channels().toArray(String[]::new));
    }

}
