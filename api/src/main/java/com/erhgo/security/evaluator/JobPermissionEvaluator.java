package com.erhgo.security.evaluator;

import com.erhgo.domain.job.Job;
import com.erhgo.security.PermissionLevel;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class JobPermissionEvaluator extends AbstractEntityPermissionEvaluator<Job> {

    public JobPermissionEvaluator(SecurityService securityService) {
        super(Job.class, securityService);
    }

    @Override
    public boolean hasPermission(Authentication authentication, Job job, PermissionLevel permissionLevel) {
        var emptyOrganizationCode = Strings.isNullOrEmpty(job.getRecruiterCode());
        if (emptyOrganizationCode) {
            log.warn("Unable to check permission for job {}, no organizationCode provided - consider unauthorized", job);
        }
        var hasRole = !emptyOrganizationCode && securityService.hasAnyRole(authentication, job.getRecruiterCode(), job.getEmployerCode());
        return
                hasRole && securityService.hasRole(authentication, Role.SOURCING)
                        && (permissionLevel == PermissionLevel.WRITE || permissionLevel == PermissionLevel.READ);
    }

}
