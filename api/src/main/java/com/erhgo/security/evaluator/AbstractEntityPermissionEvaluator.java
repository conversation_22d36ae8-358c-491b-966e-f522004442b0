package com.erhgo.security.evaluator;

import com.erhgo.security.PermissionLevel;
import com.erhgo.services.SecurityService;
import lombok.AllArgsConstructor;
import org.springframework.security.core.Authentication;

@AllArgsConstructor
public abstract class AbstractEntityPermissionEvaluator<A> {

    private final Class<A> entityType;

    SecurityService securityService;

    public abstract boolean hasPermission(Authentication authentication, A targetDomainObject, PermissionLevel permissionLevel);

    public String entityTypeAsString() {
        return entityType().getSimpleName();
    }

    public Class<A> entityType() {
        return entityType;
    }
}
