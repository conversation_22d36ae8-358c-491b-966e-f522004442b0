package com.erhgo.security;

import com.erhgo.security.evaluator.AbstractEntityPermissionEvaluator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.Serializable;
import java.util.Optional;
import java.util.Set;

@Service
@Slf4j
@RequiredArgsConstructor
// Ignore unchecked raw types here (root cause: injection of every AbstractEntityPermissionEvaluator beans)
@SuppressWarnings({"java:S3740", "rawtypes", "unchecked"})
public class ErhgoCompositePermissionEvaluator implements PermissionEvaluator {
    private final Set<AbstractEntityPermissionEvaluator> evaluators;
    private final EntityFetcher entityFetcher;
    private final TransactionTemplate transactionTemplate;

    private Optional<AbstractEntityPermissionEvaluator> getPermissionEvaluatorForType(String type) {
        return evaluators.stream().filter(p -> p.entityTypeAsString().equals(type)).findFirst();
    }

    @Override
    public boolean hasPermission(Authentication authentication, Object targetDomainObject, Object permission) {
        return targetDomainObject == null || getPermissionEvaluatorForType(targetDomainObject.getClass().getSimpleName())
                .filter(p -> p.hasPermission(authentication, targetDomainObject, PermissionLevel.valueOf(permission.toString().toUpperCase())))
                .isPresent();
    }

    @Override
    public boolean hasPermission(Authentication authentication, Serializable targetId, String targetType, Object permission) {
        var permissionEvaluatorOpt = getPermissionEvaluatorForType(targetType);
        if (permissionEvaluatorOpt.isEmpty()) {
            log.warn("No permission evaluator found for type {}, consider {} with id {} as not permitted", targetType, targetType, targetId);
            return false;
        }
        var permissionEvaluator = permissionEvaluatorOpt.get();
        return transactionTemplate.execute((unused) -> {
            var entity = Optional.ofNullable(targetId).map(id -> entityFetcher.fetchEntity(permissionEvaluator.entityType(), id)).orElse(null);
            return entity == null || permissionEvaluator.hasPermission(authentication,
                    entity,
                    PermissionLevel.valueOf(permission.toString().toUpperCase()));
        });
    }

}
