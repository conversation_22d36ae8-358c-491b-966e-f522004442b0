package com.erhgo.security;

import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.repositories.RecruiterRepository;
import com.erhgo.repositories.RecruitmentRepository;
import com.erhgo.repositories.UserProfileRepository;
import jakarta.persistence.EntityManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;

@Service
public class EntityFetcher {

    @Autowired
    private EntityManager entityManager;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private RecruiterRepository recruiterRepository;

    @Autowired
    private RecruitmentRepository recruitmentRepository;

    <A> A fetchEntity(Class<A> targetType, Serializable targetId) {
        if (targetType.isAssignableFrom(UserProfile.class)) {
            return (A) userProfileRepository.findByUserId((String) targetId).orElse(null);
        }
        if (targetType.isAssignableFrom(Recruiter.class)) {
            return (A) recruiterRepository.findOneByCode((String) targetId);
        }
        if (targetType.isAssignableFrom(Recruitment.class) && targetId instanceof String) {
            return (A) recruitmentRepository.findOneByCode((String) targetId);
        }
        return entityManager.find(targetType, targetId);
    }

}
