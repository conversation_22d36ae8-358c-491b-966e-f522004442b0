# This configuration is only used by Spring cache provider,
# NOT hibernate 2nd level cache (see hazelcast-default.xml for that purpose)
hazelcast:
  integrity-checker:
    enabled: false
  jet:
    enabled: false
  metrics:
    jmx:
      enabled: true
  map:
    default:
      in-memory-format: OBJECT
      time-to-live-seconds: 3600
  # that configuration is overloaded in kubernetes pods by environment variables
  network:
    port:
      auto-increment: false
    join:
      auto-detection:
        enabled: false
      multicast:
        enabled: false
      kubernetes:
        enabled: false

