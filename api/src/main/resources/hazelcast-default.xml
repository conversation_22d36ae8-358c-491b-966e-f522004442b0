<?xml version="1.0" encoding="UTF-8"?>
<!--
see hazelcast-default.xml in hazelcast lib
No way to overload that name : it is used to configure an hazelcast
instance for Hibernate second level cache, which remains local (by disabling all join methods)
-->
<hazelcast xmlns="http://www.hazelcast.com/schema/config"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://www.hazelcast.com/schema/config
   http://www.hazelcast.com/schema/config/hazelcast-config-5.1.xsd">
    <network>
        <port auto-increment="false" port-count="100">5702</port>
        <join>
            <auto-detection enabled="false"/>
            <multicast enabled="false"/>
            <tcp-ip enabled="false"/>
            <kubernetes enabled="false"/>
        </join>
    </network>
</hazelcast>
