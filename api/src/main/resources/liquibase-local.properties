# referenceUrl=******************************************
# referenceUsername=odas
# referencePassword=password
referenceDriver=org.mariadb.jdbc.Driver
# url=******************************************************
# username=odas
# password=k6nw!UT1dL
url=******************************************
username=odas
password=password
referenceUrl=***********************************
referenceUsername=test
referencePassword=test
driver=org.mariadb.jdbc.Driver
# Used for `mvn liquibase:diff` against testing db
changeLogFile=classpath:liquibase-changeLog.xml
outputChangeLogFile=src/main/resources/liquibase-outputChangeLog.mariadb.sql
diffChangeLogFile=src/main/resources/liquibase-diffChangeLog.mariadb.sql
# Next config is not used by Liquibase :-(
changeSetAuthor=odas
verbose=true
