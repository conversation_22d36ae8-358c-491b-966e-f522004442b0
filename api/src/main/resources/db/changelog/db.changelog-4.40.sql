-- liquibase formatted sql

-- changeset amir:20250702:1
INSERT INTO ConfigurableProperty(propertyKey, propertyValue)
values ('ats.limit.global.FIRECRAWL--APICIL', '20'),
       ('ats.limit.global.FIRECRAWL--EST_METROPOLE', '20')
ON DUPLICATE KEY UPDATE propertyValue = propertyValue;
-- changeset marin:20250630:1
ALTER TABLE UserExperience
    ADD COLUMN durationInMonths SMALLINT DEFAULT NULL NULL;

-- changeset marin:20250630:2
UPDATE UserExperience
SET durationInMonths = CASE
                           WHEN duration = 'DURATION_1' THEN 3   -- "Moins de 6 mois" -> 3 months
                           WHEN duration = 'DURATION_2' THEN 9   -- "6 mois à 1 an" -> 9 months
                           WHEN duration = 'DURATION_3' THEN 30  -- "1 an à 5 ans" -> 30 months (2.5 years)
                           WHEN duration = 'DURATION_4' THEN 72  -- "Plus de 5 ans" -> 72 months (6 years)
                           ELSE NULL
    END
WHERE duration IS NOT NULL;

-- changeset marin:20250630:3
ALTER TABLE UserExperience
    ADD COLUMN startDate DATETIME DEFAULT NULL NULL;

-- changeset marin:20250630:4
ALTER TABLE UserExperience
    ADD COLUMN endDate DATETIME DEFAULT NULL NULL;
