-- liquibase formatted sql

-- changeset odas:*************-1
CREATE SEQUENCE CandidatureSeq INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775806 START WITH 100000;

-- changeset odas:*************-2
CREATE TABLE Activity
(
    uuid                      BINARY(16)            NOT NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    `description`             VARCHAR(2000)         NULL,
    origin                    TINYINT  DEFAULT NULL NULL,
    CONSTRAINT PK_ACTIVITY PRIMARY KEY (uuid)
);

-- changeset odas:*************-3
CREATE TABLE Activity_Capacity
(
    Activity_uuid        BINARY(16) NOT NULL,
    inducedCapacities_id BIGINT     NOT NULL,
    CONSTRAINT PK_ACTIVITY_CAPACITY PRIMARY KEY (Activity_uuid, inducedCapacities_id)
);

-- changeset odas:*************-4
CREATE TABLE AnswerForCapacityRelatedQuestion
(
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    response_id               BINARY(16)            NOT NULL,
    userProfile_uuid          BINARY(16)            NOT NULL,
    CONSTRAINT PK_ANSWERFORCAPACITYRELATEDQUESTION PRIMARY KEY (response_id, userProfile_uuid)
);

-- changeset odas:*************-5
CREATE TABLE Behavior
(
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    code                      VARCHAR(255)          NULL,
    `description`             VARCHAR(2000)         NULL,
    title                     VARCHAR(255)          NULL,
    id                        BINARY(16)            NOT NULL,
    behaviorCategory          VARCHAR(255)          NULL,
    categoryIndex             INT                   NOT NULL,
    CONSTRAINT PK_BEHAVIOR PRIMARY KEY (id),
    UNIQUE (code)
);

-- changeset odas:*************-6
CREATE TABLE CandidatureActivity
(
    uuid           BINARY(16)    NOT NULL,
    inOwn          BIT DEFAULT 0 NULL,
    level          INT           NOT NULL,
    activity_uuid  BINARY(16)    NOT NULL,
    candidature_id BIGINT        NOT NULL,
    isMandatory    BIT           NOT NULL,
    expectedLevel  INT           NOT NULL,
    mission_id     BIGINT        NOT NULL,
    CONSTRAINT PK_CANDIDATUREACTIVITY PRIMARY KEY (uuid)
);

-- changeset odas:*************-7
CREATE TABLE CandidatureNote
(
    uuid                      BINARY(16)            NOT NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    text                      LONGTEXT              NOT NULL,
    candidature_id            BIGINT                NOT NULL,
    CONSTRAINT PK_CANDIDATURENOTE PRIMARY KEY (uuid)
);

-- changeset odas:*************-8
CREATE TABLE Capacity
(
    id                        BIGINT AUTO_INCREMENT          NOT NULL,
    createdBy_keycloakId      VARCHAR(255)                   NULL,
    createdDate               datetime     DEFAULT NULL      NULL,
    lastModifiedBy_keycloakId VARCHAR(255)                   NULL,
    updatedDate               datetime     DEFAULT NULL      NULL,
    code                      VARCHAR(255)                   NULL,
    `description`             VARCHAR(2000)                  NULL,
    title                     VARCHAR(255)                   NULL,
    Family                    VARCHAR(255) DEFAULT 'ANALYZE' NOT NULL,
    CONSTRAINT PK_CAPACITY PRIMARY KEY (id),
    UNIQUE (code)
);

-- changeset odas:*************-9
CREATE TABLE CapacityOccurrence
(
    occurrence          INT        NOT NULL,
    recursiveOccurrence INT        NOT NULL,
    capacity_id         BIGINT     NOT NULL,
    userProfile_uuid    BINARY(16) NOT NULL,
    CONSTRAINT PK_CAPACITYOCCURRENCE PRIMARY KEY (capacity_id, userProfile_uuid)
);

-- changeset odas:*************-10
CREATE TABLE CapacityRelatedQuestion
(
    id                        BINARY(16)            NOT NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    questionIndex             INT                   NOT NULL,
    title                     VARCHAR(255)          NULL,
    recruiter_id              BIGINT   DEFAULT NULL NULL,
    questionType              VARCHAR(255)          NOT NULL,
    CONSTRAINT PK_CAPACITYRELATEDQUESTION PRIMARY KEY (id)
);

-- changeset odas:*************-11
CREATE TABLE CapacityRelatedQuestionResponse
(
    id          BINARY(16)   NOT NULL,
    position    INT          NOT NULL,
    title       VARCHAR(255) NULL,
    question_id BINARY(16)   NOT NULL,
    CONSTRAINT PK_CAPACITYRELATEDQUESTIONRESPONSE PRIMARY KEY (id)
);

-- changeset odas:*************-12
CREATE TABLE CapacityRelatedQuestionResponse_Capacity
(
    CapacityRelatedQuestionResponse_id BINARY(16) NOT NULL,
    capacities_id                      BIGINT     NOT NULL,
    CONSTRAINT PK_CAPACITYRELATEDQUESTIONRESPONSE_CAPACITY PRIMARY KEY (CapacityRelatedQuestionResponse_id, capacities_id)
);

-- changeset odas:*************-13
CREATE TABLE Capacity_Capacity
(
    capacity_id         BIGINT NOT NULL,
    induced_capacity_id BIGINT NOT NULL,
    CONSTRAINT PK_CAPACITY_CAPACITY PRIMARY KEY (capacity_id, induced_capacity_id)
);

-- changeset odas:*************-14
CREATE TABLE Category
(
    id                        BIGINT AUTO_INCREMENT NOT NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    code                      VARCHAR(255)          NULL,
    `description`             VARCHAR(2000)         NULL,
    title                     VARCHAR(255)          NULL,
    CONSTRAINT PK_CATEGORY PRIMARY KEY (id)
);

-- changeset odas:*************-15
CREATE TABLE CategoryLevel
(
    id            BIGINT AUTO_INCREMENT NOT NULL,
    `description` VARCHAR(1024)         NULL,
    score         INT                   NOT NULL,
    title         VARCHAR(255)          NULL,
    category_id   BIGINT DEFAULT NULL   NULL,
    CONSTRAINT PK_CATEGORYLEVEL PRIMARY KEY (id)
);

-- changeset odas:*************-16
CREATE TABLE ChannelAffectation
(
    id                        BINARY(16)            NOT NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    channel                   VARCHAR(255)          NOT NULL,
    channelSourceType         VARCHAR(255)          NOT NULL,
    userProfile_uuid          BINARY(16)            NOT NULL,
    CONSTRAINT PK_CHANNELAFFECTATION PRIMARY KEY (id)
);

-- changeset odas:*************-17
CREATE TABLE ConfigurableProperty
(
    propertyKey               VARCHAR(255)          NOT NULL,
    propertyValue             LONGTEXT              NOT NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    CONSTRAINT PK_CONFIGURABLEPROPERTY PRIMARY KEY (propertyKey)
);

-- changeset odas:*************-18
CREATE TABLE Context
(
    indexForCode              BIGINT AUTO_INCREMENT NOT NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    code                      VARCHAR(255)          NULL,
    `description`             VARCHAR(2000)         NULL,
    title                     VARCHAR(255)          NULL,
    origin                    TINYINT  DEFAULT NULL NULL,
    categoryLevel_id          BIGINT                NOT NULL,
    id                        BINARY(16)            NOT NULL,
    CONSTRAINT PK_CONTEXT PRIMARY KEY (id),
    UNIQUE (indexForCode)
);

-- changeset odas:*************-19
CREATE TABLE ContextMet
(
    frequency        TINYINT DEFAULT NULL NULL,
    userProfile_uuid BINARY(16)           NOT NULL,
    DTYPE            VARCHAR(31)          NOT NULL,
    uuid             BINARY(16)           NOT NULL,
    context_id       BINARY(16)           NOT NULL,
    CONSTRAINT PK_CONTEXTMET PRIMARY KEY (uuid)
);

-- changeset odas:*************-20
CREATE TABLE ContextMet_UserExperience
(
    userExperiences_uuid BINARY(16) NOT NULL,
    JobContextMet_uuid   BINARY(16) NOT NULL,
    CONSTRAINT PK_CONTEXTMET_USEREXPERIENCE PRIMARY KEY (userExperiences_uuid, JobContextMet_uuid)
);

-- changeset odas:*************-21
CREATE TABLE ContextQuestion
(
    uuid                    BINARY(16)                      NOT NULL,
    customLabel             VARCHAR(255)                    NULL,
    recruitmentProfile_uuid BINARY(16)                      NOT NULL,
    answerType              VARCHAR(30) DEFAULT 'FREQUENCY' NULL,
    question_uuid           BINARY(16)                      NOT NULL,
    context_id              BINARY(16)                      NOT NULL,
    CONSTRAINT PK_CONTEXTQUESTION PRIMARY KEY (uuid)
);

-- changeset odas:*************-22
CREATE TABLE ContextsForCategory
(
    id                   BIGINT AUTO_INCREMENT NOT NULL,
    noContextForCategory BIT DEFAULT 0         NULL,
    category_id          BIGINT                NOT NULL,
    CONSTRAINT PK_CONTEXTSFORCATEGORY PRIMARY KEY (id)
);

-- changeset odas:*************-23
CREATE TABLE ContextsForCategory_Context
(
    ContextsForCategory_id BIGINT     NOT NULL,
    contexts_id            BINARY(16) NOT NULL,
    CONSTRAINT PK_CONTEXTSFORCATEGORY_CONTEXT PRIMARY KEY (ContextsForCategory_id, contexts_id)
);

-- changeset odas:*************-24
CREATE TABLE Criteria
(
    code                      VARCHAR(255)          NOT NULL,
    criteriaIndex             INT                   NOT NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    questionLabel             VARCHAR(255)          NOT NULL,
    questionType              VARCHAR(255)          NOT NULL,
    title                     VARCHAR(255)          NOT NULL,
    required                  BIT      DEFAULT 0    NULL,
    CONSTRAINT PK_CRITERIA PRIMARY KEY (code)
);

-- changeset odas:*************-25
CREATE TABLE CriteriaValue
(
    code                      VARCHAR(255)          NOT NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    icon                      VARCHAR(255)          NOT NULL,
    titleForQuestion          VARCHAR(255)          NOT NULL,
    titleStandalone           VARCHAR(255)          NOT NULL,
    valueIndex                INT                   NOT NULL,
    criteria_code             VARCHAR(255)          NOT NULL,
    titleForBO                VARCHAR(255)          NOT NULL,
    sourcingCriteriaStep      VARCHAR(255)          NULL,
    CONSTRAINT PK_CRITERIAVALUE PRIMARY KEY (code)
);

-- changeset odas:*************-26
CREATE TABLE CustomEmailTemplate
(
    id                        BIGINT AUTO_INCREMENT NOT NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    subject                   VARCHAR(100)          NULL,
    content                   VARCHAR(10000)        NOT NULL,
    emailFrom                 VARCHAR(255)          NOT NULL,
    authorAlias               VARCHAR(255)          NOT NULL,
    CONSTRAINT PK_CUSTOMEMAILTEMPLATE PRIMARY KEY (id)
);

-- changeset odas:*************-27
CREATE TABLE DataHealthChecker
(
    id           BINARY(16)   NOT NULL,
    title        VARCHAR(255) NOT NULL,
    query        LONGTEXT     NOT NULL,
    errorMessage VARCHAR(255) NOT NULL
);

-- changeset odas:*************-28
CREATE TABLE ErhgoClassification
(
    code         VARCHAR(255)  NOT NULL,
    title        VARCHAR(255)  NOT NULL,
    orderIndex   INT           NOT NULL,
    icon         VARCHAR(255)  NULL,
    highPriority BIT DEFAULT 0 NOT NULL,
    CONSTRAINT PK_ERHGOCLASSIFICATION PRIMARY KEY (code)
);

-- changeset odas:*************-29
CREATE TABLE ErhgoOccupation
(
    id                            BINARY(16)            NOT NULL,
    createdBy_keycloakId          VARCHAR(255)          NULL,
    createdDate                   datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId     VARCHAR(255)          NULL,
    updatedDate                   datetime DEFAULT NULL NULL,
    `description`                 LONGTEXT              NULL,
    level                         TINYINT  DEFAULT NULL NULL,
    title                         VARCHAR(255)          NOT NULL,
    priority                      INT      DEFAULT 0    NULL,
    behaviorCategory1             VARCHAR(255)          NULL,
    behaviorCategory2             VARCHAR(255)          NULL,
    isBehaviorCategory1Overloaded BIT      DEFAULT 0    NOT NULL,
    isBehaviorCategory2Overloaded BIT      DEFAULT 0    NOT NULL,
    isVisibleForOrientation       BIT      DEFAULT 1    NOT NULL,
    qualificationState            TINYINT  DEFAULT NULL NULL,
    workDomain_code               VARCHAR(255)          NULL,
    behaviorsDescription          LONGTEXT              NULL,
    behaviorCategory3             VARCHAR(255)          NULL,
    isBehaviorCategory3Overloaded BIT      DEFAULT 0    NOT NULL,
    technical                     BIT      DEFAULT 0    NOT NULL,
    CONSTRAINT PK_ERHGOOCCUPATION PRIMARY KEY (id)
);

-- changeset odas:*************-30
CREATE TABLE ErhgoOccupationCapacity
(
    occupationId               BINARY(16)           NOT NULL,
    title                      VARCHAR(255)         NOT NULL,
    level                      TINYINT DEFAULT NULL NULL,
    behaviorCategory1 VARCHAR(40) NULL,
    behaviorCategory2 VARCHAR(40) NULL,
    behaviorCategory3 VARCHAR(40) NULL,
    capacityCode               VARCHAR(255)         NOT NULL,
    numberOfCapacities         INT     DEFAULT NULL NULL,
    requiresMediumLevelContext BIT                  NOT NULL,
    requiresHighLevelContext   BIT                  NOT NULL,
    CONSTRAINT PK_ERHGOOCCUPATIONCAPACITY PRIMARY KEY (occupationId, capacityCode)
);

-- changeset odas:*************-31
CREATE TABLE ErhgoOccupation_CriteriaValue
(
    erhgoOccupation_id  BINARY(16)   NOT NULL,
    criteriaValues_code VARCHAR(255) NOT NULL,
    CONSTRAINT PK_ERHGOOCCUPATION_CRITERIAVALUE PRIMARY KEY (erhgoOccupation_id, criteriaValues_code)
);

-- changeset odas:*************-32
CREATE TABLE ErhgoOccupation_ErhgoClassification
(
    ErhgoOccupation_id        BINARY(16)   NOT NULL,
    erhgoClassifications_code VARCHAR(255) NULL
);

-- changeset odas:*************-33
CREATE TABLE ErhgoOccupation_EscoOccupation
(
    ErhgoOccupation_id  BINARY(16)   NOT NULL,
    escoOccupations_uri VARCHAR(255) NOT NULL,
    CONSTRAINT PK_ERHGOOCCUPATION_ESCOOCCUPATION PRIMARY KEY (ErhgoOccupation_id, escoOccupations_uri)
);

-- changeset odas:*************-34
CREATE TABLE ErhgoOccupation_EscoSkill
(
    ErhgoOccupation_id BINARY(16)   NOT NULL,
    skills_uri         VARCHAR(255) NOT NULL,
    CONSTRAINT PK_ERHGOOCCUPATION_ESCOSKILL PRIMARY KEY (ErhgoOccupation_id, skills_uri)
);

-- changeset odas:*************-35
CREATE TABLE ErhgoOccupation_WorkEnvironment
(
    ErhgoOccupation_id    BINARY(16)   NOT NULL,
    workEnvironments_code VARCHAR(255) NULL
);

-- changeset odas:*************-36
CREATE TABLE ErhgoOccupation_alternativeLabels
(
    ErhgoOccupation_id BINARY(16)   NOT NULL,
    alternativeLabels  VARCHAR(255) NULL COLLATE utf8mb4_nopad_bin
);

-- changeset odas:*************-37
CREATE TABLE ErhgoOccupation_capacityFamilies
(
    ErhgoOccupation_id     BINARY(16)           NOT NULL,
    capacityFamilies       TINYINT DEFAULT NULL NULL,
    capacityFamilies_ORDER INT                  NOT NULL,
    CONSTRAINT PK_ERHGOOCCUPATION_CAPACITYFAMILIES PRIMARY KEY (ErhgoOccupation_id, capacityFamilies_ORDER)
);

-- changeset odas:*************-38
CREATE TABLE EscoOccupation
(
    uri                       VARCHAR(255)          NOT NULL,
    descriptionEN             VARCHAR(2000)         NULL,
    IscoOccupation_iscoGroup  INT                   NOT NULL,
    title                     VARCHAR(255)          NOT NULL,
    level                     TINYINT  DEFAULT NULL NULL,
    createdDate               datetime DEFAULT NULL NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    descriptionFR             VARCHAR(2000)         NULL,
    CONSTRAINT PK_ESCOOCCUPATION PRIMARY KEY (uri)
);

-- changeset odas:*************-39
CREATE TABLE EscoOccupation_EscoSkill
(
    EscoOccupation_uri VARCHAR(255) NOT NULL,
    skills_uri         VARCHAR(255) NOT NULL,
    CONSTRAINT PK_ESCOOCCUPATION_ESCOSKILL PRIMARY KEY (EscoOccupation_uri, skills_uri)
);

-- changeset odas:*************-40
CREATE TABLE EscoOccupation_alternativeLabels
(
    EscoOccupation_uri VARCHAR(255) NOT NULL,
    alternativeLabels  VARCHAR(255) NULL
);

-- changeset odas:*************-41
CREATE TABLE EscoSkill
(
    uri                       VARCHAR(255)          NOT NULL,
    descriptionEN             VARCHAR(2000)         NULL,
    descriptionFR             VARCHAR(2000)         NULL,
    noActivity                BIT      DEFAULT 0    NULL,
    noBehavior                BIT      DEFAULT 0    NULL,
    noContext                 BIT      DEFAULT 0    NULL,
    skillType                 VARCHAR(255)          NULL,
    title                     VARCHAR(255)          NOT NULL,
    createdDate               datetime DEFAULT NULL NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    CONSTRAINT PK_ESCOSKILL PRIMARY KEY (uri)
);

-- changeset odas:*************-42
CREATE TABLE EscoSkill_Behavior
(
    EscoSkill_uri VARCHAR(255) NOT NULL,
    behaviors_id  BINARY(16)   NOT NULL,
    CONSTRAINT PK_ESCOSKILL_BEHAVIOR PRIMARY KEY (EscoSkill_uri, behaviors_id)
);

-- changeset odas:*************-43
CREATE TABLE EscoSkill_Context
(
    EscoSkill_uri VARCHAR(255) NOT NULL,
    contexts_id   BINARY(16)   NOT NULL,
    CONSTRAINT PK_ESCOSKILL_CONTEXT PRIMARY KEY (EscoSkill_uri, contexts_id)
);

-- changeset odas:*************-44
CREATE TABLE EscoSkill_JobActivityLabel
(
    EscoSkill_uri   VARCHAR(255) NOT NULL,
    activities_uuid BINARY(16)   NOT NULL,
    CONSTRAINT PK_ESCOSKILL_JOBACTIVITYLABEL PRIMARY KEY (EscoSkill_uri, activities_uuid)
);

-- changeset odas:*************-45
CREATE TABLE EscoSkill_alternativeLabels
(
    EscoSkill_uri     VARCHAR(255) NOT NULL,
    alternativeLabels VARCHAR(255) NULL
);

-- changeset odas:*************-46
CREATE TABLE ExternalOffer
(
    uuid                       BINARY(16)            NOT NULL,
    remoteId                   BIGINT   DEFAULT NULL NULL,
    lastXmlContent             LONGTEXT              NULL,
    atsCode                    VARCHAR(255)          NULL,
    lastEventType              VARCHAR(255)          NULL,
    createdDate                datetime DEFAULT NULL NULL,
    createdBy_keycloakId       VARCHAR(255)          NULL,
    lastModifiedBy_keycloakId  VARCHAR(255)          NULL,
    updatedDate                datetime DEFAULT NULL NULL,
    remoteLastModificationDate datetime DEFAULT NULL NULL,
    CONSTRAINT PK_EXTERNALOFFER PRIMARY KEY (uuid)
);

-- changeset odas:*************-47
CREATE TABLE GeneralInformation
(
    id                              BIGINT AUTO_INCREMENT NOT NULL,
    contactTime                     TINYINT  DEFAULT NULL NULL,
    phoneNumber                     VARCHAR(255)          NULL,
    userId                          VARCHAR(255)          NULL,
    userProfile_uuid                BINARY(16)            NOT NULL,
    city                            VARCHAR(2000)         NULL,
    postcode                        VARCHAR(10)           NULL,
    birthDate                       date     DEFAULT NULL NULL,
    salaryRange                     VARCHAR(255)          NULL,
    situation                       VARCHAR(255)          NULL,
    citycode                        VARCHAR(10)           NULL,
    longitude                       FLOAT    DEFAULT NULL NULL,
    latitude                        FLOAT    DEFAULT NULL NULL,
    departmentCode                  VARCHAR(2000)         NULL,
    regionName                      VARCHAR(2000)         NULL,
    radiusInKm                      INT      DEFAULT NULL NULL,
    smsBlacklisted                  BIT      DEFAULT 0    NULL,
    salary                          INT      DEFAULT NULL NULL,
    numberOfVerificationsForAccount INT                   NOT NULL,
    state                           VARCHAR(255)          NOT NULL,
    lastVerificationDate            datetime DEFAULT NULL NULL,
    delayInMonth                    INT      DEFAULT NULL NULL,
    CONSTRAINT PK_GENERALINFORMATION PRIMARY KEY (id),
    UNIQUE (userProfile_uuid)
);

-- changeset odas:*************-48
CREATE TABLE IscoOccupation
(
    iscoGroup INT           NOT NULL,
    title     VARCHAR(2000) NOT NULL,
    CONSTRAINT PK_ISCOOCCUPATION PRIMARY KEY (iscoGroup)
);

-- changeset odas:*************-49
CREATE TABLE Job
(
    createdBy_keycloakId      VARCHAR(255)            NULL,
    createdDate               datetime   DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)            NULL,
    updatedDate               datetime   DEFAULT NULL NULL,
    `description`             VARCHAR(2000)           NULL,
    title                     VARCHAR(255)            NULL,
    observationDate           datetime   DEFAULT NULL NULL,
    publicationDate           datetime   DEFAULT NULL NULL,
    recommendation            LONGTEXT                NULL,
    service                   VARCHAR(50)             NULL,
    state                     TINYINT    DEFAULT NULL NULL,
    id                        BINARY(16)              NOT NULL,
    masteryLevel              TINYINT    DEFAULT NULL NULL,
    erhgoOccupation_Id        BINARY(16) DEFAULT NULL NULL,
    jobType                   VARCHAR(255)            NOT NULL,
    recruiter_id              BIGINT                  NOT NULL,
    employer_id               BIGINT     DEFAULT NULL NULL,
    city                      VARCHAR(2000)           NULL,
    postcode                  VARCHAR(10)             NULL,
    citycode                  VARCHAR(10)             NULL,
    departmentCode            VARCHAR(2000)           NULL,
    regionName                VARCHAR(2000)           NULL,
    longitude                 FLOAT      DEFAULT NULL NULL,
    latitude                  FLOAT      DEFAULT NULL NULL,
    radiusInKm                INT        DEFAULT NULL NULL,
    CONSTRAINT PK_JOB PRIMARY KEY (id)
);

-- changeset odas:*************-50
CREATE TABLE JobActivityLabel
(
    uuid                      BINARY(16)            NOT NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    position                  INT      DEFAULT NULL NULL,
    title                     VARCHAR(255)          NULL,
    activity_uuid             BINARY(16)            NOT NULL,
    CONSTRAINT PK_JOBACTIVITYLABEL PRIMARY KEY (uuid)
);

-- changeset odas:*************-51
CREATE TABLE JobDating
(
    id       BINARY(16)   NOT NULL,
    code     VARCHAR(255) NOT NULL,
    date     datetime     NOT NULL,
    label    VARCHAR(255) NOT NULL,
    location VARCHAR(255) NOT NULL,
    CONSTRAINT PK_JOBDATING PRIMARY KEY (id),
    UNIQUE (code)
);

-- changeset odas:*************-52
CREATE TABLE JobDatingSlot
(
    id           BINARY(16)   NOT NULL,
    label        VARCHAR(255) NOT NULL,
    position     INT          NOT NULL,
    jobDating_id BINARY(16)   NOT NULL,
    CONSTRAINT PK_JOBDATINGSLOT PRIMARY KEY (id)
);

-- changeset odas:*************-53
CREATE TABLE JobDatingSlotForUserProfile
(
    jobDatingSlot_id BINARY(16) NOT NULL,
    userProfile_uuid BINARY(16) NOT NULL,
    CONSTRAINT PK_JOBDATINGSLOTFORUSERPROFILE PRIMARY KEY (jobDatingSlot_id, userProfile_uuid)
);

-- changeset odas:*************-54
CREATE TABLE Job_Behavior
(
    job_id       BINARY(16) NOT NULL,
    behaviors_id BINARY(16) NOT NULL,
    CONSTRAINT PK_JOB_BEHAVIOR PRIMARY KEY (job_id, behaviors_id)
);

-- changeset odas:*************-55
CREATE TABLE Job_CriteriaValue
(
    job_id              BINARY(16)   NOT NULL,
    criteriaValues_code VARCHAR(255) NOT NULL,
    CONSTRAINT PK_JOB_CRITERIAVALUE PRIMARY KEY (job_id, criteriaValues_code)
);

-- changeset odas:*************-56
CREATE TABLE Job_observators
(
    observators VARCHAR(255) NULL,
    job_id      BINARY(16)   NOT NULL
);

-- changeset odas:*************-57
CREATE TABLE LandingPage
(
    id                        BINARY(16)            NOT NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    content                   LONGTEXT              NULL,
    urlKey                    VARCHAR(255)          NOT NULL,
    CONSTRAINT PK_LANDINGPAGE PRIMARY KEY (id),
    UNIQUE (urlKey)
);

-- changeset odas:*************-58
CREATE TABLE LandingPage_Organization
(
    landingPage_id   BINARY(16) NOT NULL,
    organizations_id BIGINT     NOT NULL,
    CONSTRAINT PK_LANDINGPAGE_ORGANIZATION PRIMARY KEY (landingPage_id, organizations_id)
);

-- changeset odas:*************-59
CREATE TABLE Mission
(
    id                        BIGINT AUTO_INCREMENT NOT NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    code                      VARCHAR(255)          NULL,
    `description`             VARCHAR(2000)         NULL,
    title                     VARCHAR(255)          NULL,
    position                  INT      DEFAULT NULL NULL,
    job_id                    BINARY(16)            NOT NULL,
    CONSTRAINT PK_MISSION PRIMARY KEY (id)
);

-- changeset odas:*************-60
CREATE TABLE Mission_ContextsForCategory
(
    Mission_id             BIGINT NOT NULL,
    contextsForCategory_id BIGINT NOT NULL,
    CONSTRAINT PK_MISSION_CONTEXTSFORCATEGORY PRIMARY KEY (Mission_id, contextsForCategory_id),
    UNIQUE (contextsForCategory_id)
);

-- changeset odas:*************-61
CREATE TABLE Mission_JobActivityLabel
(
    Mission_id      BIGINT     NOT NULL,
    activities_uuid BINARY(16) NOT NULL,
    CONSTRAINT PK_MISSION_JOBACTIVITYLABEL PRIMARY KEY (Mission_id, activities_uuid)
);

-- changeset odas:*************-62
CREATE TABLE Notification
(
    recruitment_id            BIGINT   DEFAULT NULL NULL,
    userProfile_uuid          BINARY(16)            NOT NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    type                      VARCHAR(255)          NOT NULL,
    id                        BINARY(16)            NOT NULL,
    DTYPE                     VARCHAR(31)           NOT NULL,
    state                     VARCHAR(255)          NOT NULL,
    content                   VARCHAR(1500)         NULL,
    link                      VARCHAR(1000)         NULL,
    subject                   VARCHAR(500)          NULL,
    CONSTRAINT PK_NOTIFICATION PRIMARY KEY (id)
);

-- changeset odas:*************-63
CREATE TABLE OccupationActivity
(
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    source                    VARCHAR(255)          NOT NULL,
    state                     VARCHAR(255)          NOT NULL,
    activity_uuid             BINARY(16)            NOT NULL,
    occupation_id             BINARY(16)            NOT NULL,
    id                        BINARY(16)            NOT NULL,
    CONSTRAINT PK_OCCUPATIONACTIVITY PRIMARY KEY (id)
);

-- changeset odas:*************-64
CREATE TABLE OccupationBehavior
(
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    source                    VARCHAR(255)          NOT NULL,
    behavior_id               BINARY(16)            NOT NULL,
    occupation_id             BINARY(16)            NOT NULL,
    id                        BINARY(16)            NOT NULL,
    CONSTRAINT PK_OCCUPATIONBEHAVIOR PRIMARY KEY (id)
);

-- changeset odas:*************-65
CREATE TABLE OccupationContext
(
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    source                    VARCHAR(255)          NOT NULL,
    state                     VARCHAR(255)          NOT NULL,
    context_id                BINARY(16)            NOT NULL,
    occupation_id             BINARY(16)            NOT NULL,
    id                        BINARY(16)            NOT NULL,
    CONSTRAINT PK_OCCUPATIONCONTEXT PRIMARY KEY (id)
);

-- changeset odas:*************-66
CREATE TABLE OptionalActivity
(
    id                      BINARY(16)              NOT NULL,
    acquisitionModality     TINYINT    DEFAULT NULL NULL,
    activityLabel_uuid      BINARY(16) DEFAULT NULL NULL,
    recruitmentProfile_uuid BINARY(16) DEFAULT NULL NULL,
    CONSTRAINT PK_OPTIONALACTIVITY PRIMARY KEY (id)
);

-- changeset odas:*************-67
CREATE TABLE OptionalContext
(
    id                      BINARY(16)              NOT NULL,
    acquisitionModality     TINYINT    DEFAULT NULL NULL,
    recruitmentProfile_uuid BINARY(16) DEFAULT NULL NULL,
    context_id              BINARY(16)              NOT NULL,
    CONSTRAINT PK_OPTIONALCONTEXT PRIMARY KEY (id)
);

-- changeset odas:*************-68
CREATE TABLE `Organization`
(
    id                        BIGINT AUTO_INCREMENT NOT NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    code                      VARCHAR(255)          NULL,
    `description`             LONGTEXT              NULL,
    title                     VARCHAR(255)          NULL,
    address                   VARCHAR(255)          NULL,
    latitude                  DOUBLE   DEFAULT NULL NULL,
    longitude                 DOUBLE   DEFAULT NULL NULL,
    siren                     VARCHAR(255)          NULL,
    siret                     VARCHAR(255)          NULL,
    organizationType          VARCHAR(255)          NULL,
    refererRecruiter_id       BIGINT   DEFAULT NULL NULL,
    DTYPE                     VARCHAR(31)           NOT NULL,
    privateUsers              BIT                   NOT NULL,
    privateJobs               BIT                   NOT NULL,
    defaultProject_id         BIGINT   DEFAULT NULL NULL,
    refusalEmailTemplate_id   BIGINT   DEFAULT NULL NULL,
    mandatoryIdentity         BIT                   NOT NULL,
    internal                  BIT      DEFAULT 0    NOT NULL,
    siretVerificationStatus   VARCHAR(255)          NULL,
    externalUrl               VARCHAR(255)          NULL,
    forcedUrl                 VARCHAR(255)          NULL,
    gdprMention               LONGTEXT              NULL,
    CONSTRAINT PK_ORGANIZATION PRIMARY KEY (id)
);

-- changeset odas:*************-69
CREATE TABLE Organization_Organization
(
    Employer_id    BIGINT NOT NULL,
    consortiums_id BIGINT NOT NULL,
    CONSTRAINT PK_ORGANIZATION_ORGANIZATION PRIMARY KEY (Employer_id, consortiums_id)
);

-- changeset odas:*************-70
CREATE TABLE PersonalEmailDomain
(
    id     BIGINT AUTO_INCREMENT NOT NULL,
    domain VARCHAR(255)          NOT NULL,
    CONSTRAINT PK_PERSONALEMAILDOMAIN PRIMARY KEY (id)
);

-- changeset odas:*************-71
CREATE TABLE QuestionForContexts
(
    uuid                      BINARY(16)            NOT NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    title                     VARCHAR(255)          NOT NULL,
    titleForNone              VARCHAR(255)          NULL,
    titleForLow               VARCHAR(255)          NULL,
    titleForMedium            VARCHAR(255)          NULL,
    titleForHigh              VARCHAR(255)          NULL,
    CONSTRAINT PK_QUESTIONFORCONTEXTS PRIMARY KEY (uuid)
);

-- changeset odas:*************-72
CREATE TABLE QuestionForContexts_Context
(
    questionForContexts_uuid BINARY(16) NOT NULL,
    contexts_id              BINARY(16) NOT NULL,
    CONSTRAINT PK_QUESTIONFORCONTEXTS_CONTEXT PRIMARY KEY (questionForContexts_uuid, contexts_id)
);

-- changeset odas:*************-73
CREATE TABLE Recruitment
(
    id                        BIGINT AUTO_INCREMENT   NOT NULL,
    createdBy_keycloakId      VARCHAR(255)            NULL,
    createdDate               datetime   DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)            NULL,
    updatedDate               datetime   DEFAULT NULL NULL,
    code                      VARCHAR(255)            NULL,
    `description`             LONGTEXT                NULL,
    title                     VARCHAR(255)            NULL,
    baseSalary                INT        DEFAULT NULL NULL,
    city                      VARCHAR(2000)           NULL,
    organizationDescription   LONGTEXT                NULL,
    externalUrl               VARCHAR(255)            NULL,
    hideSalary                BIT        DEFAULT 0    NULL,
    maxSalary                 INT        DEFAULT NULL NULL,
    publicationDate           datetime   DEFAULT NULL NULL,
    startingDate              datetime   DEFAULT NULL NULL,
    state                     TINYINT    DEFAULT NULL NULL,
    typeContract              TINYINT    DEFAULT NULL NULL,
    workContractDuration      INT        DEFAULT NULL NULL,
    workContractDurationUnit  TINYINT    DEFAULT NULL NULL,
    workingWeeklyTime         INT        DEFAULT NULL NULL,
    recruitmentProfile_uuid   BINARY(16) DEFAULT NULL NULL,
    postcode                  VARCHAR(10)             NULL,
    citycode                  VARCHAR(10)             NULL,
    departmentCode            VARCHAR(2000)           NULL,
    regionName                VARCHAR(2000)           NULL,
    longitude                 FLOAT      DEFAULT NULL NULL,
    latitude                  FLOAT      DEFAULT NULL NULL,
    radiusInKm                INT        DEFAULT NULL NULL,
    sourcingStep              INT        DEFAULT NULL NULL,
    modularWorkingTime        BIT        DEFAULT 0    NULL,
    sendMailState             VARCHAR(255)            NULL,
    sendMailDate              datetime   DEFAULT NULL NULL,
    publicationEndDate        datetime   DEFAULT NULL NULL,
    lastProcessingDate        datetime   DEFAULT NULL NULL,
    lastProcessingType        VARCHAR(255)            NULL,
    CONSTRAINT PK_RECRUITMENT PRIMARY KEY (id)
);

-- changeset odas:*************-74
CREATE TABLE RecruitmentCandidature
(
    id                          BIGINT AUTO_INCREMENT NOT NULL,
    createdBy_keycloakId        VARCHAR(255)          NULL,
    createdDate                 datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId   VARCHAR(255)          NULL,
    updatedDate                 datetime DEFAULT NULL NULL,
    code                        VARCHAR(255)          NULL,
    candidatureRecruitmentState TINYINT  DEFAULT NULL NULL,
    effort                      INT      DEFAULT NULL NULL,
    state                       TINYINT  DEFAULT NULL NULL,
    submissionDate              datetime DEFAULT NULL NULL,
    valid                       BIT      DEFAULT 0    NULL,
    recruitment_id              BIGINT                NOT NULL,
    userProfile_uuid            BINARY(16)            NOT NULL,
    customAnswer                LONGTEXT              NULL,
    refusalDate                 datetime DEFAULT NULL NULL,
    emailSent                   VARCHAR(255)          NULL,
    refusedBy                   VARCHAR(255)          NULL,
    globalCandidatureState      VARCHAR(255)          NULL,
    color                       VARCHAR(20)           NULL,
    anonymousCode               VARCHAR(20)           NULL,
    isAvailable                 BIT      DEFAULT 0    NULL,
    availabilityDelayInMonth    INT      DEFAULT NULL NULL,
    generatedForSourcing        BIT      DEFAULT 0    NOT NULL,
    modifiedByUser              BIT      DEFAULT 0    NOT NULL,
    archived                    BIT      DEFAULT 0    NOT NULL,
    CONSTRAINT PK_RECRUITMENTCANDIDATURE PRIMARY KEY (id)
);

-- changeset odas:*************-75
CREATE TABLE RecruitmentProfile
(
    uuid                      BINARY(16)            NOT NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    modifiable                BIT                   NOT NULL,
    qualified                 BIT                   NOT NULL,
    title                     VARCHAR(255)          NULL,
    customQuestion            VARCHAR(255)          NULL,
    job_id                    BINARY(16)            NOT NULL,
    CONSTRAINT PK_RECRUITMENTPROFILE PRIMARY KEY (uuid)
);

-- changeset odas:*************-76
CREATE TABLE RecruitmentProfile_Mission
(
    RecruitmentProfile_uuid BINARY(16) NOT NULL,
    qualifiedMissions_id    BIGINT     NOT NULL,
    CONSTRAINT PK_RECRUITMENTPROFILE_MISSION PRIMARY KEY (RecruitmentProfile_uuid, qualifiedMissions_id)
);

-- changeset odas:*************-77
CREATE TABLE Recruitment_ErhgoClassification
(
    erhgoClassifications_code VARCHAR(255) NOT NULL,
    recruitment_id            BIGINT       NOT NULL,
    CONSTRAINT PK_RECRUITMENT_ERHGOCLASSIFICATION PRIMARY KEY (erhgoClassifications_code, recruitment_id)
);

-- changeset odas:*************-78
CREATE TABLE Recruitment_sourcingUsersIdToNotify
(
    sourcingUsersIdToNotify VARCHAR(255) NOT NULL,
    recruitment_id          BIGINT       NOT NULL,
    CONSTRAINT PK_RECRUITMENT_SOURCINGUSERSIDTONOTIFY PRIMARY KEY (sourcingUsersIdToNotify, recruitment_id)
);

-- changeset odas:*************-79
CREATE TABLE Recruitment_usersIdToNotify
(
    usersIdToNotify VARCHAR(255) NOT NULL,
    recruitment_id  BIGINT       NOT NULL,
    CONSTRAINT PK_RECRUITMENT_USERSIDTONOTIFY PRIMARY KEY (usersIdToNotify, recruitment_id)
);

-- changeset odas:*************-80
CREATE TABLE RomeOccupation
(
    code  VARCHAR(10)   NOT NULL,
    title VARCHAR(2000) NOT NULL,
    CONSTRAINT PK_ROMEOCCUPATION PRIMARY KEY (code)
) collate = utf8mb4_unicode_ci;;

-- changeset odas:*************-81
CREATE TABLE RomeOccupation_accessibleRomeOccupations
(
    RomeOccupation_id            VARCHAR(255) NOT NULL,
    accessibleRomeOccupations_id VARCHAR(255) NOT NULL,
    CONSTRAINT PK_ROMEOCCUPATION_ACCESSIBLEROMEOCCUPATIONS PRIMARY KEY (RomeOccupation_id, accessibleRomeOccupations_id)
) collate = utf8mb4_unicode_ci;

-- changeset odas:*************-82
CREATE TABLE RomeOfErhgoOccupation
(
    erhgoOccupation_id  BINARY(16)   NOT NULL,
    romeOccupation_code VARCHAR(10)  NOT NULL collate utf8mb4_unicode_ci,
    romeSourceType      VARCHAR(255) NOT NULL,
    CONSTRAINT PK_ROMEOFERHGOOCCUPATION PRIMARY KEY (erhgoOccupation_id, romeOccupation_code)
);

-- changeset odas:*************-83
CREATE TABLE SCHEDULER_LOCK
(
    name       VARCHAR(64)              NOT NULL,
    lock_until timestamp DEFAULT NOW(3) NOT NULL,
    locked_at  timestamp DEFAULT NOW(3) NOT NULL,
    locked_by  VARCHAR(255)             NOT NULL,
    CONSTRAINT PK_SCHEDULER_LOCK PRIMARY KEY (name)
);

-- changeset odas:*************-84
CREATE TABLE Sector
(
    uuid                      BINARY(16)            NOT NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    abbreviation              VARCHAR(255)          NULL,
    code                      VARCHAR(255)          NULL,
    label                     VARCHAR(255)          NULL,
    CONSTRAINT PK_SECTOR PRIMARY KEY (uuid),
    UNIQUE (code)
);

-- changeset odas:*************-85
CREATE TABLE SourcingInvitation
(
    uuid                      BINARY(16)            NOT NULL,
    code                      VARCHAR(255)          NULL,
    maxNumberOfGuests         INT                   NOT NULL,
    host_id                   BIGINT   DEFAULT NULL NULL,
    createdDate               datetime DEFAULT NULL NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    duration                  INT                   NOT NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    CONSTRAINT PK_SOURCINGINVITATION PRIMARY KEY (uuid),
    UNIQUE (code)
);

-- changeset odas:*************-86
CREATE TABLE SourcingPreferences
(
    isoWeekDay                     INT DEFAULT NULL NULL,
    uuid                           BINARY(16)       NOT NULL,
    mailFrequency                  VARCHAR(255)     NOT NULL,
    userId                         VARCHAR(255)     NOT NULL,
    notifyOnSpontaneousCandidature BIT DEFAULT 0    NOT NULL,
    CONSTRAINT PK_SOURCINGPREFERENCES PRIMARY KEY (uuid),
    UNIQUE (userId)
);

-- changeset odas:*************-87
CREATE TABLE SourcingSubscription
(
    uuid                      BINARY(16)              NOT NULL,
    createdBy_keycloakId      VARCHAR(255)            NULL,
    createdDate               datetime   DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)            NULL,
    updatedDate               datetime   DEFAULT NULL NULL,
    expirationDate            datetime   DEFAULT NULL NULL,
    recruiter_id              BIGINT                  NOT NULL,
    invitation_uuid           BINARY(16) DEFAULT NULL NULL,
    mailState                 VARCHAR(255)            NULL,
    CONSTRAINT PK_SOURCINGSUBSCRIPTION PRIMARY KEY (uuid),
    UNIQUE (recruiter_id)
);

-- changeset odas:*************-88
CREATE TABLE SpontaneousCandidature
(
    id                        BIGINT                NOT NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    submissionDate            datetime DEFAULT NULL NULL,
    recruiter_id              BIGINT                NOT NULL,
    userProfile_uuid          BINARY(16)            NOT NULL,
    color                     VARCHAR(20)           NULL,
    anonymousCode             VARCHAR(20)           NULL,
    refusalDate               datetime DEFAULT NULL NULL,
    emailSent                 VARCHAR(255)          NULL,
    refusedBy                 VARCHAR(255)          NULL,
    globalCandidatureState    VARCHAR(255)          NOT NULL,
    archived                  BIT      DEFAULT 0    NOT NULL,
    modifiedByUser            BIT      DEFAULT 1    NOT NULL,
    CONSTRAINT PK_SPONTANEOUSCANDIDATURE PRIMARY KEY (id)
);

-- changeset odas:*************-89
CREATE TABLE SpontaneousCandidature_Sector
(
    SpontaneousCandidature_id BIGINT     NOT NULL,
    referentialSectors_uuid   BINARY(16) NOT NULL,
    CONSTRAINT PK_SPONTANEOUSCANDIDATURE_SECTOR PRIMARY KEY (SpontaneousCandidature_id, referentialSectors_uuid)
);

-- changeset odas:*************-90
CREATE TABLE SpontaneousCandidature_customSectors
(
    SpontaneousCandidature_id BIGINT        NOT NULL,
    customSectors             VARCHAR(2000) NULL
);

-- changeset odas:*************-91
CREATE TABLE UserCriteriaValue
(
    selected         BIT          NOT NULL,
    userProfile_uuid BINARY(16)   NOT NULL,
    value_code       VARCHAR(255) NOT NULL,
    CONSTRAINT PK_USERCRITERIAVALUE PRIMARY KEY (userProfile_uuid, value_code)
);

-- changeset odas:*************-92
CREATE TABLE UserErhgoClassification
(
    accepted                 BIT          NOT NULL,
    userProfile_uuid         BINARY(16)   NOT NULL,
    erhgoClassification_code VARCHAR(255) NOT NULL,
    CONSTRAINT PK_USERERHGOCLASSIFICATION PRIMARY KEY (userProfile_uuid, erhgoClassification_code)
);

-- changeset odas:*************-93
CREATE TABLE UserExperience
(
    uuid               BINARY(16)              NOT NULL,
    organizationName   VARCHAR(255)            NULL,
    fromYear           INT        DEFAULT NULL NULL,
    inYear             INT        DEFAULT NULL NULL,
    jobTitle           VARCHAR(255)            NOT NULL,
    toYear             INT        DEFAULT NULL NULL,
    type               TINYINT    DEFAULT NULL NULL,
    userProfile_uuid   BINARY(16)              NOT NULL,
    erhgoOccupation_id BINARY(16) DEFAULT NULL NULL,
    durationType       VARCHAR(255)            NULL,
    duration           VARCHAR(255)            NULL,
    CONSTRAINT PK_USEREXPERIENCE PRIMARY KEY (uuid)
);

-- changeset odas:*************-94
CREATE TABLE UserMobileToken
(
    id                        BINARY(16)            NOT NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    timestamp                 datetime              NOT NULL,
    token                     LONGTEXT              NOT NULL,
    userProfile_uuid          BINARY(16)            NOT NULL,
    CONSTRAINT PK_USERMOBILETOKEN PRIMARY KEY (id),
    UNIQUE (token)
);

-- changeset odas:*************-95
CREATE TABLE UserNote
(
    id                        BINARY(16)            NOT NULL,
    createdBy_keycloakId      VARCHAR(255)          NULL,
    createdDate               datetime DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)          NULL,
    updatedDate               datetime DEFAULT NULL NULL,
    content                   LONGTEXT              NULL,
    organization_id           BIGINT   DEFAULT NULL NULL,
    userProfile_uuid          BINARY(16)            NOT NULL,
    CONSTRAINT PK_USERNOTE PRIMARY KEY (id)
);

-- changeset odas:*************-96
CREATE TABLE UserProfile
(
    uuid                      BINARY(16)                 NOT NULL,
    userId                    VARCHAR(255)               NOT NULL,
    masteryLevel              FLOAT                      NOT NULL,
    registrationStep          TINYINT                    NULL,
    selectedOccupation_Id     BINARY(16)   DEFAULT NULL  NULL,
    selectedActivity_Uuid     BINARY(16)   DEFAULT NULL  NULL,
    jobTitle                  VARCHAR(2000)              NULL,
    createdBy_keycloakId      VARCHAR(255)               NULL,
    createdDate               datetime     DEFAULT NULL  NULL,
    lastModifiedBy_keycloakId VARCHAR(255)               NULL,
    updatedDate               datetime     DEFAULT NULL  NULL,
    taggedNoExperience        BIT          DEFAULT 0     NOT NULL,
    jobOfferOptOut            BIT          DEFAULT 0     NULL,
    indexationRequiredDate    datetime     DEFAULT NULL  NULL,
    lastIndexationDate        datetime     DEFAULT NULL  NULL,
    isLegacyBO                BIT          DEFAULT 0     NOT NULL,
    lastConnectionDate        datetime     DEFAULT NULL  NULL,
    source                    LONGTEXT                   NULL,
    confirmationMailSent      VARCHAR(256) DEFAULT 'NEW' NOT NULL,
    sendersOptOut             VARCHAR(255)               NULL,
    requiresCapacitiesRefresh BIT          DEFAULT 0     NULL,
    channel                   VARCHAR(255)               NULL,
    channelSourceType         VARCHAR(255)               NOT NULL,
    softSkillsPdfUrl          VARCHAR(255)               NULL,
    startedDate               datetime     DEFAULT NULL  NULL,
    endedDate                 datetime     DEFAULT NULL  NULL,
    CONSTRAINT PK_USERPROFILE PRIMARY KEY (uuid),
    UNIQUE (userId)
);

-- changeset odas:*************-97
CREATE TABLE UserProfile_Behavior
(
    UserProfile_uuid BINARY(16) NOT NULL,
    behaviors_id     BINARY(16) NOT NULL,
    CONSTRAINT PK_USERPROFILE_BEHAVIOR PRIMARY KEY (UserProfile_uuid, behaviors_id)
);

-- changeset odas:*************-98
CREATE TABLE UserProfile_ErhgoOccupation
(
    UserProfile_uuid          BINARY(16) NOT NULL,
    blacklistedOccupations_id BINARY(16) NOT NULL,
    CONSTRAINT PK_USERPROFILE_ERHGOOCCUPATION PRIMARY KEY (UserProfile_uuid, blacklistedOccupations_id)
);

-- changeset odas:*************-99
CREATE TABLE UserProfile_WorkDomain
(
    UserProfile_uuid BINARY(16)   NOT NULL,
    workDomains_code VARCHAR(255) NOT NULL,
    CONSTRAINT PK_USERPROFILE_WORKDOMAIN PRIMARY KEY (UserProfile_uuid, workDomains_code)
);

-- changeset odas:*************-100
CREATE TABLE WorkDomain
(
    code          VARCHAR(255) NOT NULL,
    title         VARCHAR(255) NOT NULL,
    `description` LONGTEXT     NOT NULL,
    CONSTRAINT PK_WORKDOMAIN PRIMARY KEY (code)
);

-- changeset odas:*************-101
CREATE TABLE WorkEnvironment
(
    code          VARCHAR(255) NOT NULL,
    title         VARCHAR(255) NOT NULL,
    `description` LONGTEXT     NOT NULL,
    CONSTRAINT PK_WORKENVIRONMENT PRIMARY KEY (code)
);

-- changeset odas:*************-102
ALTER TABLE CriteriaValue
    ADD CONSTRAINT CRITERIA_VALUE_INDEX_UNIQ UNIQUE (valueIndex, criteria_code);

-- changeset odas:*************-103
ALTER TABLE ContextMet
    ADD CONSTRAINT UC_CONTEXT_MET UNIQUE (context_id, userProfile_uuid, DTYPE);

-- changeset odas:*************-104
ALTER TABLE OccupationActivity
    ADD CONSTRAINT UC_OCCUPATION_ACTIVITY UNIQUE (occupation_id, activity_uuid);

-- changeset odas:*************-105
ALTER TABLE OccupationBehavior
    ADD CONSTRAINT UC_OCCUPATION_BEHAVIOR UNIQUE (occupation_id, behavior_id);

-- changeset odas:*************-106
ALTER TABLE OccupationContext
    ADD CONSTRAINT UC_OCCUPATION_CONTEXT UNIQUE (occupation_id, context_id);

-- changeset odas:*************-107
ALTER TABLE ExternalOffer
    ADD CONSTRAINT UC_REMOTE_ID_ATS_CODE UNIQUE (remoteId, atsCode);

-- changeset odas:*************-108
ALTER TABLE Behavior
    ADD CONSTRAINT UKBehaviorFamilyNumber UNIQUE (behaviorCategory, categoryIndex);

-- changeset odas:*************-109
ALTER TABLE RecruitmentCandidature
    ADD CONSTRAINT UK_CANDIDATURE_USER_RECRUITMENT UNIQUE (userProfile_uuid, recruitment_id);

-- changeset odas:*************-110
ALTER TABLE QuestionForContexts_Context
    ADD CONSTRAINT UK_QUESTIONFORCONTEXTS_CONTEXT_QUESTION UNIQUE (questionForContexts_uuid, contexts_id);

-- changeset odas:*************-111
ALTER TABLE JobDatingSlot
    ADD CONSTRAINT UKa819feb4c34a3k58i1dw7in58 UNIQUE (jobDating_id, label);

-- changeset odas:*************-112
ALTER TABLE CandidatureActivity
    ADD CONSTRAINT UKb0fehby154smwwn87sjp107v2 UNIQUE (candidature_id, activity_uuid);

-- changeset odas:*************-113
ALTER TABLE ChannelAffectation
    ADD CONSTRAINT UKbkkawr32lihqbb8m96sn96pou UNIQUE (userProfile_uuid, channel);

-- changeset odas:*************-114
ALTER TABLE ContextQuestion
    ADD CONSTRAINT UKf9dxht09x7lswtpcx4gyc6dq2 UNIQUE (context_id, recruitmentProfile_uuid);

-- changeset odas:*************-115
ALTER TABLE JobDatingSlot
    ADD CONSTRAINT UKgaumc9wknjd3bhku5vb9cbhxj UNIQUE (jobDating_id, position);

-- changeset odas:*************-117
ALTER TABLE CapacityRelatedQuestionResponse
    ADD CONSTRAINT UQ_CapacityRelatedQuestionResponse_Title UNIQUE (question_id, title);

-- changeset odas:*************-118
CREATE INDEX Behavior_EscoSkill_FOREIGN_KEY ON EscoSkill_Behavior (behaviors_id);

-- changeset odas:*************-119
CREATE INDEX Behavior_Job_FOREIGN_KEY ON Job_Behavior (behaviors_id);

-- changeset odas:*************-120
CREATE INDEX CriteriaValue_ErhgoOccupation_FOREIGN_KEY ON ErhgoOccupation_CriteriaValue (criteriaValues_code);

-- changeset odas:*************-121
CREATE INDEX CriteriaValue_Job_FOREIGN_KEY ON Job_CriteriaValue (criteriaValues_code);

-- changeset odas:*************-122
CREATE INDEX ERHGO_OCCUPATION_INDEX ON Job (erhgoOccupation_Id);

-- changeset odas:*************-123
CREATE INDEX ERHGO_OCCUPATION_USER_INDEX ON UserProfile (selectedOccupation_Id);

-- changeset odas:*************-124
CREATE INDEX ErhgoClassification_Recruitment_FOREIGN_KEY ON Recruitment_ErhgoClassification (erhgoClassifications_code);

-- changeset odas:*************-125
CREATE INDEX ErhgoOccupationCapacityIndex ON ErhgoOccupationCapacity (level, capacityCode, behaviorCategory1,
                                                                      behaviorCategory2, behaviorCategory3);

-- changeset odas:*************-126
CREATE INDEX FK10gevpu61p08mgdkeymsjp5j0 ON ContextsForCategory_Context (contexts_id);

-- changeset odas:*************-127
CREATE INDEX FK17ie2f4houpwht34ul23fn44j ON UserExperience (userProfile_uuid);

-- changeset odas:*************-128
CREATE INDEX FK1bly50va6xrtgxryd59t0lbwn ON EscoSkill_alternativeLabels (EscoSkill_uri);

-- changeset odas:*************-129
CREATE INDEX FK2ebev4eik56m94qj44n7v0aeb ON RomeOccupation_accessibleRomeOccupations (accessibleRomeOccupations_id);

-- changeset odas:*************-130
CREATE INDEX FK2u9jxaehwlb4i8l2uoqtmkx8x ON RecruitmentProfile_Mission (qualifiedMissions_id);

-- changeset odas:*************-131
CREATE INDEX FK38w5rpppwmrfx3g2hk5yqjj58 ON UserNote (userProfile_uuid);

-- changeset odas:*************-132
CREATE INDEX FK3l16j5evym6ira8u73wv2fbf4 ON AnswerForCapacityRelatedQuestion (userProfile_uuid);

-- changeset odas:*************-133
CREATE INDEX FK3y4b2crsj7w9g4o3ykdsotn1b ON OptionalContext (context_id);

-- changeset odas:*************-134
CREATE INDEX FK44pvk5dqq6m0nmbgudhya6lrp ON QuestionForContexts_Context (contexts_id);

-- changeset odas:*************-135
CREATE INDEX FK4bx6nzefof8yijk2f5cwo20uk ON ErhgoOccupation_WorkEnvironment (ErhgoOccupation_id);

-- changeset odas:*************-136
CREATE INDEX FK4g6utn1b3xcg4x6yvtqje2oyg ON Organization_Organization (consortiums_id);

-- changeset odas:*************-137
CREATE INDEX FK4gx46fup66nyp30npqr575r5m ON ContextMet_UserExperience (JobContextMet_uuid);

-- changeset odas:*************-138
CREATE INDEX FK4nhi0cqe1ymv85j25tnwlo4o9 ON CandidatureNote (candidature_id);

-- changeset odas:*************-139
CREATE INDEX FK4u38gdgwxywxjll15cpbar1o4 ON Capacity_Capacity (induced_capacity_id);

-- changeset odas:*************-140
CREATE INDEX FK565glswo8cf9m0elb5m9c0aqe ON SpontaneousCandidature_Sector (referentialSectors_uuid);

-- changeset odas:*************-141
CREATE INDEX FK5fdp4xck7ghlba4p7r4w8s3hh ON UserCriteriaValue (value_code);

-- changeset odas:*************-142
CREATE INDEX FK6ecrvpmwdr822g14x3athp0lc ON CapacityRelatedQuestionResponse (question_id);

-- changeset odas:*************-143
CREATE INDEX FK6qf6i220gx5u0ljndva84acks ON SourcingSubscription (invitation_uuid);

-- changeset odas:*************-144
CREATE INDEX FK71yiy0lpv4q8acp7yksh4u0lj ON ContextsForCategory (category_id);

-- changeset odas:*************-145
CREATE INDEX FK79sr61wiyaw9vsnjye7q93kig ON ContextQuestion (recruitmentProfile_uuid);

-- changeset odas:*************-146
CREATE INDEX FK7nvpurw0yeu8leldgj58xig86 ON Mission_JobActivityLabel (activities_uuid);

-- changeset odas:*************-147
CREATE INDEX FK832q2yuhnht7kcw40q479dwui ON EscoOccupation_alternativeLabels (EscoOccupation_uri);

-- changeset odas:*************-148
CREATE INDEX FK88xhnbwyovwlnslok8u6mnhl0 ON UserProfile_Behavior (behaviors_id);

-- changeset odas:*************-149
CREATE INDEX FK9jrv6kj3hhhc8fsasd8itsvfc ON ErhgoOccupation_EscoSkill (skills_uri);

-- changeset odas:*************-150
CREATE INDEX FKOccupation_Classification_Classification ON ErhgoOccupation_ErhgoClassification (erhgoClassifications_code);

-- changeset odas:*************-151
CREATE INDEX FKOccupation_Classification_Occupation ON ErhgoOccupation_ErhgoClassification (ErhgoOccupation_id);

-- changeset odas:*************-152
CREATE INDEX FK_CONTEXTQUESTION_QUESTION ON ContextQuestion (question_uuid);

-- changeset odas:*************-153
CREATE INDEX FK_ERHGO_OCCUPATION_LABELS ON ErhgoOccupation_alternativeLabels (ErhgoOccupation_id);

-- changeset odas:*************-154
CREATE INDEX FK_ESCO_ISCO ON EscoOccupation (IscoOccupation_iscoGroup);

-- changeset odas:*************-155
CREATE INDEX FK_ESCO_OCCUPATION_URI ON ErhgoOccupation_EscoOccupation (escoOccupations_uri);

-- changeset odas:*************-156
CREATE INDEX FK_JOB_EMPLOYER ON Job (employer_id);

-- changeset odas:*************-157
CREATE INDEX FK_JOB_RECRUITER ON Job (recruiter_id);

-- changeset odas:*************-158
CREATE INDEX FK_OCCUPATION_BEHAVIOR_BEHAVIOR_ID ON OccupationBehavior (behavior_id);

-- changeset odas:*************-159
CREATE INDEX FK_OCCUPATION_BEHAVIOR_OCCUPATION_ID ON OccupationBehavior (occupation_id);

-- changeset odas:*************-160
CREATE INDEX FK_OCCUPATION_CONTEXT_CONTEXT_ID ON OccupationContext (context_id);

-- changeset odas:*************-161
CREATE INDEX FK_OCCUPATION_WORK_DOMAIN ON ErhgoOccupation (workDomain_code);

-- changeset odas:*************-162
CREATE INDEX FK_ORGANIZATION_DEFAULT_PROJECT ON `Organization` (defaultProject_id);

-- changeset odas:*************-163
CREATE INDEX FK_ORGANIZATION_REFERER ON `Organization` (refererRecruiter_id);

-- changeset odas:*************-164
CREATE INDEX FK_SKILL_ACTIVITY ON EscoSkill_JobActivityLabel (activities_uuid);

-- changeset odas:*************-165
CREATE INDEX FK_SPONT_CAND_ORGA ON SpontaneousCandidature (recruiter_id);

-- changeset odas:*************-166
CREATE INDEX FK_SPONT_CAND_USER ON SpontaneousCandidature (userProfile_uuid);

-- changeset odas:*************-167
CREATE INDEX FK_USEREXPERIENCE_OCCUPATION ON UserExperience (erhgoOccupation_id);

-- changeset odas:*************-168
CREATE INDEX FK_USER_DOMAINS_DOMAINS ON UserProfile_WorkDomain (workDomains_code);

-- changeset odas:*************-169
CREATE INDEX FKa198yh4vltckqk99tvtbwkcjd ON EscoSkill_Context (contexts_id);

-- changeset odas:*************-170
CREATE INDEX FKa8dn1sxg0llb5d82aysjl273n ON OptionalActivity (recruitmentProfile_uuid);

-- changeset odas:*************-171
CREATE INDEX FKato79chx27gmoy31o20b5x3i1 ON UserMobileToken (userProfile_uuid);

-- changeset odas:*************-172
CREATE INDEX FKdxvfutfgbmq3aserhym28axy7 ON Mission_JobActivityLabel (Mission_id);

-- changeset odas:*************-173
CREATE INDEX FKe1csry2del0bqj082u2px0jf2 ON SpontaneousCandidature_customSectors (SpontaneousCandidature_id);

-- changeset odas:*************-174
CREATE INDEX FKejhtvbap2dmm7nc2tn9vrn08q ON Activity_Capacity (inducedCapacities_id);

-- changeset odas:*************-175
CREATE INDEX FKf18mm3cm77av7waw8gihw38vr ON RecruitmentCandidature (recruitment_id);

-- changeset odas:*************-176
CREATE INDEX FKfm912164jjga09hyuk9unrcbp ON CriteriaValue (criteria_code);

-- changeset odas:*************-177
CREATE INDEX FKfnu6qdytfsyuh6xsbu8lr838k ON JobDatingSlotForUserProfile (userProfile_uuid);

-- changeset odas:*************-178
CREATE INDEX FKfqxkfuaett9gr2jjec28ahipa ON UserNote (organization_id);

-- changeset odas:*************-179
CREATE INDEX FKfybh2i4emgg2gdwqkk3l4rcyo ON EscoOccupation_EscoSkill (skills_uri);

-- changeset odas:*************-180
CREATE INDEX FKh5okeuipdlbk0v4uokhtc1833 ON CategoryLevel (category_id);

-- changeset odas:*************-181
CREATE INDEX FKh5qm229pbcyb20t807zrj2mte ON ErhgoOccupation_WorkEnvironment (workEnvironments_code);

-- changeset odas:*************-182
CREATE INDEX FKhkvw29ns0j3lw0vicqasr3ag1 ON CandidatureActivity (activity_uuid);

-- changeset odas:*************-183
CREATE INDEX FKhoaw3uinbry7p79ovo0s694rr ON RecruitmentProfile_Mission (RecruitmentProfile_uuid);

-- changeset odas:*************-184
CREATE INDEX FKhqlyujbyyqo4esupad7uhayv4 ON CapacityRelatedQuestion (recruiter_id);

-- changeset odas:*************-185
CREATE INDEX FKiu427ibjx1cqdpip6nsxkj93p ON Recruitment (recruitmentProfile_uuid);

-- changeset odas:*************-186
CREATE INDEX FKj77rud87b34txtqb4lprk7b3n ON ContextMet (userProfile_uuid);

-- changeset odas:*************-187
CREATE INDEX FKjjr03yei2ipd4lbuor8486euv ON RomeOfErhgoOccupation (erhgoOccupation_id);

-- changeset odas:*************-188
CREATE INDEX FKkq10jkevdyq59f1e42jjh96qm ON ContextMet_UserExperience (userExperiences_uuid);

-- changeset odas:*************-189
CREATE INDEX FKkq8wdblrh72cpkwugwdm4vovr ON RecruitmentCandidature (userProfile_uuid);

-- changeset odas:*************-190
CREATE INDEX FKmjqsa8921giwblkrj2ucc1xmr ON CapacityRelatedQuestionResponse_Capacity (capacities_id);

-- changeset odas:*************-191
CREATE INDEX FKmort8c2yyscbb4bptgtf1xrfo ON RomeOfErhgoOccupation (romeOccupation_code);

-- changeset odas:*************-192
CREATE INDEX FKn1vkgx96gnjcith6utfxxbhln ON EscoSkill_Context (EscoSkill_uri);

-- changeset odas:*************-193
CREATE INDEX FKnhv1vjokro289bp37jmhiolis ON `Organization` (refusalEmailTemplate_id);

-- changeset odas:*************-194
CREATE INDEX FKo79pvgdc8cfnxv0oh0ykbhuph ON UserErhgoClassification (erhgoClassification_code);

-- changeset odas:*************-195
CREATE INDEX FKowaoxkw3a40jp4bcv090ubqcu ON OptionalActivity (activityLabel_uuid);

-- changeset odas:*************-196
CREATE INDEX FKq3yn42voexcanja8djk77ma3j ON UserErhgoClassification (userProfile_uuid);

-- changeset odas:*************-197
CREATE INDEX FKr8u5k8869ccx3qcvowmi38kn6 ON CandidatureActivity (mission_id);

-- changeset odas:*************-198
CREATE INDEX FKriqo19512ynvmnmhf93vdqk86 ON OccupationActivity (occupation_id);

-- changeset odas:*************-199
CREATE INDEX FKrpk94t0dtoi7x3ppdj9i8tg3t ON JobActivityLabel (activity_uuid);

-- changeset odas:*************-200
CREATE INDEX FKs00gxcxjfs4reo4qdbg64u4ah ON OccupationActivity (activity_uuid);

-- changeset odas:*************-201
CREATE INDEX FKsaa6yw5k05d596cvfsd4a6uc3 ON OptionalContext (recruitmentProfile_uuid);

-- changeset odas:*************-202
CREATE INDEX FKsworb2bo2ncxjbd4w0ikb52iw ON SourcingInvitation (host_id);

-- changeset odas:*************-203
CREATE INDEX FKsygby0l4jt0fhyylrrc0tc1kj ON Context (categoryLevel_id);

-- changeset odas:*************-204
CREATE INDEX IDXCapacityOccurrenceOccurrence ON CapacityOccurrence (userProfile_uuid, occurrence);

-- changeset odas:*************-205
CREATE INDEX IDX_USERPROFILE_ERHGO_OCCUPATION_BLACKLISTED_OCCUPATIONS_ID ON UserProfile_ErhgoOccupation (blacklistedOccupations_id);

-- changeset odas:*************-206
CREATE INDEX IDX_USERPROFILE_ERHGO_OCCUPATION_USERPROFILE_UUID ON UserProfile_ErhgoOccupation (UserProfile_uuid);

-- changeset odas:*************-207
CREATE INDEX JOB_ACTIVITY_LABEL_USER_INDEX ON UserProfile (selectedActivity_Uuid);

-- changeset odas:*************-208
CREATE INDEX Job_observators_FOREIGN_KEY ON Job_observators (job_id);

-- changeset odas:*************-209
CREATE INDEX LandingPage_Organization_Organization_FOREIGN_KEY ON LandingPage_Organization (organizations_id);

-- changeset odas:*************-210
CREATE INDEX Mission_FOREIGN_KEY ON Mission (job_id);

-- changeset odas:*************-211
CREATE INDEX NotifiedRecruitment_Recruitment_FOREIGN_KEY ON Notification (recruitment_id);

-- changeset odas:*************-212
CREATE INDEX NotifiedRecruitment_User_FOREIGN_KEY ON Notification (userProfile_uuid);

-- changeset odas:*************-213
CREATE INDEX RecruitmentProfile_FOREIGN_KEY ON RecruitmentProfile (job_id);

-- changeset odas:*************-214
CREATE INDEX Recruitment_ErhgoClassification_FOREIGN_KEY ON Recruitment_ErhgoClassification (recruitment_id);

-- changeset odas:*************-215
CREATE INDEX Recruitment_usersIdToNotify_FOREIGN_KEY ON Recruitment_usersIdToNotify (recruitment_id);

-- changeset odas:*************-216
CREATE INDEX SEARCH_OCCUPATION_BY_ACTIVITY_IDX_1 ON ErhgoOccupation (isVisibleForOrientation);

-- changeset odas:*************-217
CREATE INDEX SEARCH_OCCUPATION_BY_ACTIVITY_IDX_2 ON OccupationActivity (state);

-- changeset odas:*************-218
CREATE INDEX SEARCH_OCCUPATION_BY_ACTIVITY_IDX_3 ON ErhgoOccupation (level, title);

-- changeset odas:*************-219
CREATE INDEX UserProfile_Remind_INDEX ON UserProfile (createdDate);

-- changeset odas:*************-220
ALTER TABLE EscoSkill_Behavior
    ADD CONSTRAINT Behavior_EscoSkill_FOREIGN_KEY FOREIGN KEY (behaviors_id) REFERENCES Behavior (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-221
ALTER TABLE Job_Behavior
    ADD CONSTRAINT Behavior_Job_FOREIGN_KEY FOREIGN KEY (behaviors_id) REFERENCES Behavior (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-222
ALTER TABLE ErhgoOccupation_CriteriaValue
    ADD CONSTRAINT CriteriaValue_ErhgoOccupation_FOREIGN_KEY FOREIGN KEY (criteriaValues_code) REFERENCES CriteriaValue (code) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-223
ALTER TABLE Job_CriteriaValue
    ADD CONSTRAINT CriteriaValue_Job_FOREIGN_KEY FOREIGN KEY (criteriaValues_code) REFERENCES CriteriaValue (code) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-224
ALTER TABLE Job
    ADD CONSTRAINT ERHGO_OCCUPATION_FOREIGN_KEY FOREIGN KEY (erhgoOccupation_Id) REFERENCES ErhgoOccupation (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-225
ALTER TABLE UserProfile
    ADD CONSTRAINT ERHGO_OCCUPATION_USER_FOREIGN_KEY FOREIGN KEY (selectedOccupation_Id) REFERENCES ErhgoOccupation (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-226
ALTER TABLE Recruitment_ErhgoClassification
    ADD CONSTRAINT ErhgoClassification_Recruitment_FOREIGN_KEY FOREIGN KEY (erhgoClassifications_code) REFERENCES ErhgoClassification (code) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-227
ALTER TABLE ErhgoOccupation_CriteriaValue
    ADD CONSTRAINT ErhgoOccupation_CriteriaValue_FOREIGN_KEY FOREIGN KEY (erhgoOccupation_id) REFERENCES ErhgoOccupation (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-228
ALTER TABLE ContextsForCategory_Context
    ADD CONSTRAINT FK10gevpu61p08mgdkeymsjp5j0 FOREIGN KEY (contexts_id) REFERENCES Context (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-229
ALTER TABLE UserExperience
    ADD CONSTRAINT FK17ie2f4houpwht34ul23fn44j FOREIGN KEY (userProfile_uuid) REFERENCES UserProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-230
ALTER TABLE EscoSkill_alternativeLabels
    ADD CONSTRAINT FK1bly50va6xrtgxryd59t0lbwn FOREIGN KEY (EscoSkill_uri) REFERENCES EscoSkill (uri) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-231
ALTER TABLE ContextMet_UserExperience
    ADD CONSTRAINT FK1j5qcaoe1hp586ppe83k68itw FOREIGN KEY (JobContextMet_uuid) REFERENCES ContextMet (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-232
ALTER TABLE RomeOccupation_accessibleRomeOccupations
    ADD CONSTRAINT FK2ebev4eik56m94qj44n7v0aeb FOREIGN KEY (accessibleRomeOccupations_id) REFERENCES RomeOccupation (code) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-233
ALTER TABLE RecruitmentProfile_Mission
    ADD CONSTRAINT FK2u9jxaehwlb4i8l2uoqtmkx8x FOREIGN KEY (qualifiedMissions_id) REFERENCES Mission (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-234
ALTER TABLE UserNote
    ADD CONSTRAINT FK38w5rpppwmrfx3g2hk5yqjj58 FOREIGN KEY (userProfile_uuid) REFERENCES UserProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-235
ALTER TABLE AnswerForCapacityRelatedQuestion
    ADD CONSTRAINT FK3l16j5evym6ira8u73wv2fbf4 FOREIGN KEY (userProfile_uuid) REFERENCES UserProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-236
ALTER TABLE OptionalContext
    ADD CONSTRAINT FK3y4b2crsj7w9g4o3ykdsotn1b FOREIGN KEY (context_id) REFERENCES Context (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-237
ALTER TABLE ErhgoOccupation_WorkEnvironment
    ADD CONSTRAINT FK4bx6nzefof8yijk2f5cwo20uk FOREIGN KEY (ErhgoOccupation_id) REFERENCES ErhgoOccupation (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-238
ALTER TABLE Organization_Organization
    ADD CONSTRAINT FK4g6utn1b3xcg4x6yvtqje2oyg FOREIGN KEY (consortiums_id) REFERENCES `Organization` (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-239
ALTER TABLE Capacity_Capacity
    ADD CONSTRAINT FK4u38gdgwxywxjll15cpbar1o4 FOREIGN KEY (capacity_id) REFERENCES Capacity (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-240
ALTER TABLE SpontaneousCandidature_Sector
    ADD CONSTRAINT FK565glswo8cf9m0elb5m9c0aqe FOREIGN KEY (referentialSectors_uuid) REFERENCES Sector (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-241
ALTER TABLE UserCriteriaValue
    ADD CONSTRAINT FK5fdp4xck7ghlba4p7r4w8s3hh FOREIGN KEY (value_code) REFERENCES CriteriaValue (code) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-242
ALTER TABLE RomeOfErhgoOccupation
    ADD CONSTRAINT FK5rtecsktynil8s9xqck7owsap FOREIGN KEY (romeOccupation_code) REFERENCES RomeOccupation (code) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-243
ALTER TABLE CapacityOccurrence
    ADD CONSTRAINT FK68nkn83unhg9rmd39dd4tifc2 FOREIGN KEY (userProfile_uuid) REFERENCES UserProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-244
ALTER TABLE CapacityRelatedQuestionResponse
    ADD CONSTRAINT FK6ecrvpmwdr822g14x3athp0lc FOREIGN KEY (question_id) REFERENCES CapacityRelatedQuestion (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-245
ALTER TABLE CapacityRelatedQuestionResponse_Capacity
    ADD CONSTRAINT FK6mnxq7lom0rjm01phfurwx4se FOREIGN KEY (CapacityRelatedQuestionResponse_id) REFERENCES CapacityRelatedQuestionResponse (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-246
ALTER TABLE SourcingSubscription
    ADD CONSTRAINT FK6qf6i220gx5u0ljndva84acks FOREIGN KEY (invitation_uuid) REFERENCES SourcingInvitation (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-247
ALTER TABLE ContextsForCategory
    ADD CONSTRAINT FK71yiy0lpv4q8acp7yksh4u0lj FOREIGN KEY (category_id) REFERENCES Category (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-248
ALTER TABLE ContextQuestion
    ADD CONSTRAINT FK79sr61wiyaw9vsnjye7q93kig FOREIGN KEY (recruitmentProfile_uuid) REFERENCES RecruitmentProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-249
ALTER TABLE Mission_JobActivityLabel
    ADD CONSTRAINT FK7nvpurw0yeu8leldgj58xig86 FOREIGN KEY (activities_uuid) REFERENCES JobActivityLabel (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-250
ALTER TABLE EscoOccupation_alternativeLabels
    ADD CONSTRAINT FK832q2yuhnht7kcw40q479dwui FOREIGN KEY (EscoOccupation_uri) REFERENCES EscoOccupation (uri) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-251
ALTER TABLE UserProfile_Behavior
    ADD CONSTRAINT FK88xhnbwyovwlnslok8u6mnhl0 FOREIGN KEY (behaviors_id) REFERENCES Behavior (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-252
ALTER TABLE RomeOccupation_accessibleRomeOccupations
    ADD CONSTRAINT FK998j78wt844sss59wvppdimi4 FOREIGN KEY (RomeOccupation_id) REFERENCES RomeOccupation (code) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-253
ALTER TABLE ErhgoOccupation_EscoSkill
    ADD CONSTRAINT FK9jrv6kj3hhhc8fsasd8itsvfc FOREIGN KEY (skills_uri) REFERENCES EscoSkill (uri) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-254
ALTER TABLE ErhgoOccupation_ErhgoClassification
    ADD CONSTRAINT FKOccupation_Classification_Classification FOREIGN KEY (erhgoClassifications_code) REFERENCES ErhgoClassification (code) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-255
ALTER TABLE ErhgoOccupation_ErhgoClassification
    ADD CONSTRAINT FKOccupation_Classification_Occupation FOREIGN KEY (ErhgoOccupation_id) REFERENCES ErhgoOccupation (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-256
ALTER TABLE ContextQuestion
    ADD CONSTRAINT FK_CONTEXTQUESTION_QUESTION FOREIGN KEY (question_uuid) REFERENCES QuestionForContexts (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-257
ALTER TABLE ErhgoOccupation_EscoOccupation
    ADD CONSTRAINT FK_ERHGO_OCCUPATION_ID FOREIGN KEY (ErhgoOccupation_id) REFERENCES ErhgoOccupation (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-258
ALTER TABLE ErhgoOccupation_alternativeLabels
    ADD CONSTRAINT FK_ERHGO_OCCUPATION_LABELS FOREIGN KEY (ErhgoOccupation_id) REFERENCES ErhgoOccupation (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-259
ALTER TABLE EscoOccupation
    ADD CONSTRAINT FK_ESCO_ISCO FOREIGN KEY (IscoOccupation_iscoGroup) REFERENCES IscoOccupation (iscoGroup) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-260
ALTER TABLE ErhgoOccupation_EscoOccupation
    ADD CONSTRAINT FK_ESCO_OCCUPATION_URI FOREIGN KEY (escoOccupations_uri) REFERENCES EscoOccupation (uri) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-261
ALTER TABLE Job
    ADD CONSTRAINT FK_JOB_EMPLOYER FOREIGN KEY (employer_id) REFERENCES `Organization` (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-262
ALTER TABLE Job
    ADD CONSTRAINT FK_JOB_RECRUITER FOREIGN KEY (recruiter_id) REFERENCES `Organization` (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-263
ALTER TABLE OccupationBehavior
    ADD CONSTRAINT FK_OCCUPATION_BEHAVIOR_BEHAVIOR_ID FOREIGN KEY (behavior_id) REFERENCES Behavior (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-264
ALTER TABLE OccupationBehavior
    ADD CONSTRAINT FK_OCCUPATION_BEHAVIOR_OCCUPATION_ID FOREIGN KEY (occupation_id) REFERENCES ErhgoOccupation (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-265
ALTER TABLE OccupationContext
    ADD CONSTRAINT FK_OCCUPATION_CONTEXT_CONTEXT_ID FOREIGN KEY (context_id) REFERENCES Context (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-266
ALTER TABLE OccupationContext
    ADD CONSTRAINT FK_OCCUPATION_CONTEXT_OCCUPATION_ID FOREIGN KEY (occupation_id) REFERENCES ErhgoOccupation (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-267
ALTER TABLE ErhgoOccupation
    ADD CONSTRAINT FK_OCCUPATION_WORK_DOMAIN FOREIGN KEY (workDomain_code) REFERENCES WorkDomain (code) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-268
ALTER TABLE `Organization`
    ADD CONSTRAINT FK_ORGANIZATION_DEFAULT_PROJECT FOREIGN KEY (defaultProject_id) REFERENCES `Organization` (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-269
ALTER TABLE `Organization`
    ADD CONSTRAINT FK_ORGANIZATION_REFERER FOREIGN KEY (refererRecruiter_id) REFERENCES `Organization` (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-270
ALTER TABLE QuestionForContexts_Context
    ADD CONSTRAINT FK_QUESTIONFORCONTEXTS_CONTEXT FOREIGN KEY (contexts_id) REFERENCES Context (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-271
ALTER TABLE QuestionForContexts_Context
    ADD CONSTRAINT FK_QUESTIONFORCONTEXTS_QUESTION FOREIGN KEY (questionForContexts_uuid) REFERENCES QuestionForContexts (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-272
ALTER TABLE EscoSkill_JobActivityLabel
    ADD CONSTRAINT FK_SKILL_ACTIVITY FOREIGN KEY (activities_uuid) REFERENCES JobActivityLabel (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-273
ALTER TABLE EscoSkill_JobActivityLabel
    ADD CONSTRAINT FK_SKILL_SKILL FOREIGN KEY (EscoSkill_uri) REFERENCES EscoSkill (uri) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-274
ALTER TABLE SpontaneousCandidature
    ADD CONSTRAINT FK_SPONT_CAND_ORGA FOREIGN KEY (recruiter_id) REFERENCES `Organization` (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-275
ALTER TABLE SpontaneousCandidature
    ADD CONSTRAINT FK_SPONT_CAND_USER FOREIGN KEY (userProfile_uuid) REFERENCES UserProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-276
ALTER TABLE UserExperience
    ADD CONSTRAINT FK_USEREXPERIENCE_OCCUPATION FOREIGN KEY (erhgoOccupation_id) REFERENCES ErhgoOccupation (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-277
ALTER TABLE UserProfile_ErhgoOccupation
    ADD CONSTRAINT FK_USERPROFILE_ERHGO_OCCUPATION_ERHGO_OCCUPATION FOREIGN KEY (blacklistedOccupations_id) REFERENCES ErhgoOccupation (id) ON UPDATE RESTRICT ON DELETE CASCADE;

-- changeset odas:*************-278
ALTER TABLE UserProfile_ErhgoOccupation
    ADD CONSTRAINT FK_USERPROFILE_ERHGO_OCCUPATION_USERPROFILE FOREIGN KEY (UserProfile_uuid) REFERENCES UserProfile (uuid) ON UPDATE RESTRICT ON DELETE CASCADE;

-- changeset odas:*************-279
ALTER TABLE UserProfile_WorkDomain
    ADD CONSTRAINT FK_USER_DOMAINS_DOMAINS FOREIGN KEY (workDomains_code) REFERENCES WorkDomain (code) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-280
ALTER TABLE UserProfile_WorkDomain
    ADD CONSTRAINT FK_USER_DOMAINS_USER FOREIGN KEY (UserProfile_uuid) REFERENCES UserProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-281
ALTER TABLE EscoSkill_Context
    ADD CONSTRAINT FKa198yh4vltckqk99tvtbwkcjd FOREIGN KEY (contexts_id) REFERENCES Context (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-282
ALTER TABLE CandidatureActivity
    ADD CONSTRAINT FKa1t4x381udichcw5n8xy1d99h FOREIGN KEY (candidature_id) REFERENCES RecruitmentCandidature (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-283
ALTER TABLE OptionalActivity
    ADD CONSTRAINT FKa8dn1sxg0llb5d82aysjl273n FOREIGN KEY (recruitmentProfile_uuid) REFERENCES RecruitmentProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-284
ALTER TABLE UserMobileToken
    ADD CONSTRAINT FKato79chx27gmoy31o20b5x3i1 FOREIGN KEY (userProfile_uuid) REFERENCES UserProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-285
ALTER TABLE Activity_Capacity
    ADD CONSTRAINT FKcu9px0yov8mrpytbtdr1b6udg FOREIGN KEY (Activity_uuid) REFERENCES Activity (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-286
ALTER TABLE ContextMet
    ADD CONSTRAINT FKd0rwft40rje868ld6miy8tdls FOREIGN KEY (context_id) REFERENCES Context (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-287
ALTER TABLE Mission_ContextsForCategory
    ADD CONSTRAINT FKd3dxr0btiaqvx3dyahgoh72mh FOREIGN KEY (Mission_id) REFERENCES Mission (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-288
ALTER TABLE Organization_Organization
    ADD CONSTRAINT FKdphvrsbekj5pb1y3nf1rwq6ut FOREIGN KEY (Employer_id) REFERENCES `Organization` (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-289
ALTER TABLE Mission_JobActivityLabel
    ADD CONSTRAINT FKdxvfutfgbmq3aserhym28axy7 FOREIGN KEY (Mission_id) REFERENCES Mission (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-290
ALTER TABLE SpontaneousCandidature_customSectors
    ADD CONSTRAINT FKe1csry2del0bqj082u2px0jf2 FOREIGN KEY (SpontaneousCandidature_id) REFERENCES SpontaneousCandidature (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-291
ALTER TABLE JobDatingSlot
    ADD CONSTRAINT FKe6l632s1frh5m8pqy4sc9agfq FOREIGN KEY (jobDating_id) REFERENCES JobDating (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-292
ALTER TABLE Activity_Capacity
    ADD CONSTRAINT FKejhtvbap2dmm7nc2tn9vrn08q FOREIGN KEY (inducedCapacities_id) REFERENCES Capacity (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-293
ALTER TABLE RecruitmentCandidature
    ADD CONSTRAINT FKf18mm3cm77av7waw8gihw38vr FOREIGN KEY (recruitment_id) REFERENCES Recruitment (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-294
ALTER TABLE CriteriaValue
    ADD CONSTRAINT FKfm912164jjga09hyuk9unrcbp FOREIGN KEY (criteria_code) REFERENCES Criteria (code) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-295
ALTER TABLE JobDatingSlotForUserProfile
    ADD CONSTRAINT FKfnu6qdytfsyuh6xsbu8lr838k FOREIGN KEY (userProfile_uuid) REFERENCES UserProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-296
ALTER TABLE UserNote
    ADD CONSTRAINT FKfqxkfuaett9gr2jjec28ahipa FOREIGN KEY (organization_id) REFERENCES `Organization` (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-297
ALTER TABLE EscoOccupation_EscoSkill
    ADD CONSTRAINT FKfybh2i4emgg2gdwqkk3l4rcyo FOREIGN KEY (skills_uri) REFERENCES EscoSkill (uri) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-298
ALTER TABLE ErhgoOccupation_EscoSkill
    ADD CONSTRAINT FKgmnlum1fpsix3fo1fafgpwwyj FOREIGN KEY (ErhgoOccupation_id) REFERENCES ErhgoOccupation (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-299
ALTER TABLE Recruitment_sourcingUsersIdToNotify
    ADD CONSTRAINT FKgvax4p8m9jab9fi2eiln0f6ye FOREIGN KEY (recruitment_id) REFERENCES Recruitment (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-300
ALTER TABLE CategoryLevel
    ADD CONSTRAINT FKh5okeuipdlbk0v4uokhtc1833 FOREIGN KEY (category_id) REFERENCES Category (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-301
ALTER TABLE ErhgoOccupation_WorkEnvironment
    ADD CONSTRAINT FKh5qm229pbcyb20t807zrj2mte FOREIGN KEY (workEnvironments_code) REFERENCES WorkEnvironment (code) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-302
ALTER TABLE ErhgoOccupation_capacityFamilies
    ADD CONSTRAINT FKhihypxk4bc86mnkrsi20inpld FOREIGN KEY (ErhgoOccupation_id) REFERENCES ErhgoOccupation (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-303
ALTER TABLE CandidatureActivity
    ADD CONSTRAINT FKhkvw29ns0j3lw0vicqasr3ag1 FOREIGN KEY (activity_uuid) REFERENCES Activity (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-304
ALTER TABLE RecruitmentProfile_Mission
    ADD CONSTRAINT FKhoaw3uinbry7p79ovo0s694rr FOREIGN KEY (RecruitmentProfile_uuid) REFERENCES RecruitmentProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-305
ALTER TABLE CapacityRelatedQuestion
    ADD CONSTRAINT FKhqlyujbyyqo4esupad7uhayv4 FOREIGN KEY (recruiter_id) REFERENCES `Organization` (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-306
ALTER TABLE Capacity_Capacity
    ADD CONSTRAINT FKisa0bkwl2f5ojfq88jg7a7pma FOREIGN KEY (induced_capacity_id) REFERENCES Capacity (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-307
ALTER TABLE Recruitment
    ADD CONSTRAINT FKiu427ibjx1cqdpip6nsxkj93p FOREIGN KEY (recruitmentProfile_uuid) REFERENCES RecruitmentProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-308
ALTER TABLE ContextMet
    ADD CONSTRAINT FKj77rud87b34txtqb4lprk7b3n FOREIGN KEY (userProfile_uuid) REFERENCES UserProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-309
ALTER TABLE RomeOfErhgoOccupation
    ADD CONSTRAINT FKjjr03yei2ipd4lbuor8486euv FOREIGN KEY (erhgoOccupation_id) REFERENCES ErhgoOccupation (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-310
ALTER TABLE ChannelAffectation
    ADD CONSTRAINT FKjl3doi7uimbdleomuvsxsa3t1 FOREIGN KEY (userProfile_uuid) REFERENCES UserProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-311
ALTER TABLE ContextQuestion
    ADD CONSTRAINT FKk48uasv4ce5f9s1j2ph22o5qs FOREIGN KEY (context_id) REFERENCES Context (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-312
ALTER TABLE ContextMet_UserExperience
    ADD CONSTRAINT FKkq10jkevdyq59f1e42jjh96qm FOREIGN KEY (userExperiences_uuid) REFERENCES UserExperience (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-313
ALTER TABLE RecruitmentCandidature
    ADD CONSTRAINT FKkq8wdblrh72cpkwugwdm4vovr FOREIGN KEY (userProfile_uuid) REFERENCES UserProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-314
ALTER TABLE AnswerForCapacityRelatedQuestion
    ADD CONSTRAINT FKkrybqdpugbbsmsxpwx6tsitr6 FOREIGN KEY (response_id) REFERENCES CapacityRelatedQuestionResponse (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-315
ALTER TABLE SourcingSubscription
    ADD CONSTRAINT FKku3i3pvq0shl3i2hsrlfxxdxm FOREIGN KEY (recruiter_id) REFERENCES `Organization` (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-316
ALTER TABLE ContextsForCategory_Context
    ADD CONSTRAINT FKlyii5r50nhd92mugqdh5tgme5 FOREIGN KEY (ContextsForCategory_id) REFERENCES ContextsForCategory (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-317
ALTER TABLE GeneralInformation
    ADD CONSTRAINT FKm992qlaicmk6u1kslpx9mohuv FOREIGN KEY (userProfile_uuid) REFERENCES UserProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-318
ALTER TABLE Mission_ContextsForCategory
    ADD CONSTRAINT FKmeusqf5d5ajscc83po66bvi9h FOREIGN KEY (contextsForCategory_id) REFERENCES ContextsForCategory (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-319
ALTER TABLE CapacityRelatedQuestionResponse_Capacity
    ADD CONSTRAINT FKmjqsa8921giwblkrj2ucc1xmr FOREIGN KEY (capacities_id) REFERENCES Capacity (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-320
ALTER TABLE EscoSkill_Context
    ADD CONSTRAINT FKn1vkgx96gnjcith6utfxxbhln FOREIGN KEY (EscoSkill_uri) REFERENCES EscoSkill (uri) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-321
ALTER TABLE `Organization`
    ADD CONSTRAINT FKnhv1vjokro289bp37jmhiolis FOREIGN KEY (refusalEmailTemplate_id) REFERENCES CustomEmailTemplate (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-322
ALTER TABLE EscoOccupation_EscoSkill
    ADD CONSTRAINT FKnuagrvd5na615bq456ft5tfk4 FOREIGN KEY (EscoOccupation_uri) REFERENCES EscoOccupation (uri) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-323
ALTER TABLE UserErhgoClassification
    ADD CONSTRAINT FKo79pvgdc8cfnxv0oh0ykbhuph FOREIGN KEY (erhgoClassification_code) REFERENCES ErhgoClassification (code) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-324
ALTER TABLE OptionalActivity
    ADD CONSTRAINT FKowaoxkw3a40jp4bcv090ubqcu FOREIGN KEY (activityLabel_uuid) REFERENCES JobActivityLabel (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-325
ALTER TABLE UserCriteriaValue
    ADD CONSTRAINT FKpnh5kl6uco9u9nxitvmvl64nb FOREIGN KEY (userProfile_uuid) REFERENCES UserProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-326
ALTER TABLE UserProfile_Behavior
    ADD CONSTRAINT FKq2q53r1deja51iah4q0jbyat5 FOREIGN KEY (UserProfile_uuid) REFERENCES UserProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-327
ALTER TABLE UserErhgoClassification
    ADD CONSTRAINT FKq3yn42voexcanja8djk77ma3j FOREIGN KEY (userProfile_uuid) REFERENCES UserProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-328
ALTER TABLE EscoSkill_Behavior
    ADD CONSTRAINT FKqwadnwwrgu03blaq6eso62mbc FOREIGN KEY (EscoSkill_uri) REFERENCES EscoSkill (uri) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-329
ALTER TABLE CandidatureActivity
    ADD CONSTRAINT FKr8u5k8869ccx3qcvowmi38kn6 FOREIGN KEY (mission_id) REFERENCES Mission (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-330
ALTER TABLE CapacityOccurrence
    ADD CONSTRAINT FKrhe9oyxb9jih94d7fes58jg0v FOREIGN KEY (capacity_id) REFERENCES Capacity (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-331
ALTER TABLE OccupationActivity
    ADD CONSTRAINT FKriqo19512ynvmnmhf93vdqk86 FOREIGN KEY (occupation_id) REFERENCES ErhgoOccupation (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-332
ALTER TABLE JobActivityLabel
    ADD CONSTRAINT FKrpk94t0dtoi7x3ppdj9i8tg3t FOREIGN KEY (activity_uuid) REFERENCES Activity (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-333
ALTER TABLE OccupationActivity
    ADD CONSTRAINT FKs00gxcxjfs4reo4qdbg64u4ah FOREIGN KEY (activity_uuid) REFERENCES JobActivityLabel (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-334
ALTER TABLE OptionalContext
    ADD CONSTRAINT FKsaa6yw5k05d596cvfsd4a6uc3 FOREIGN KEY (recruitmentProfile_uuid) REFERENCES RecruitmentProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-335
ALTER TABLE SpontaneousCandidature_Sector
    ADD CONSTRAINT FKsjgcqxr75bv15284yaj78cvnu FOREIGN KEY (SpontaneousCandidature_id) REFERENCES SpontaneousCandidature (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-336
ALTER TABLE SourcingInvitation
    ADD CONSTRAINT FKsworb2bo2ncxjbd4w0ikb52iw FOREIGN KEY (host_id) REFERENCES `Organization` (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-337
ALTER TABLE Context
    ADD CONSTRAINT FKsygby0l4jt0fhyylrrc0tc1kj FOREIGN KEY (categoryLevel_id) REFERENCES CategoryLevel (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-338
ALTER TABLE JobDatingSlotForUserProfile
    ADD CONSTRAINT FKtdhw7cuh6mueaawpep2u48rvs FOREIGN KEY (jobDatingSlot_id) REFERENCES JobDatingSlot (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-339
ALTER TABLE UserProfile
    ADD CONSTRAINT JOB_ACTIVITY_LABEL_USER_FOREIGN_KEY FOREIGN KEY (selectedActivity_Uuid) REFERENCES JobActivityLabel (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-340
ALTER TABLE Job_Behavior
    ADD CONSTRAINT Job_Behavior_FOREIGN_KEY FOREIGN KEY (job_id) REFERENCES Job (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-341
ALTER TABLE Job_CriteriaValue
    ADD CONSTRAINT Job_CriteriaValue_FOREIGN_KEY FOREIGN KEY (job_id) REFERENCES Job (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-342
ALTER TABLE Job_observators
    ADD CONSTRAINT Job_observators_FOREIGN_KEY FOREIGN KEY (job_id) REFERENCES Job (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-343
ALTER TABLE LandingPage_Organization
    ADD CONSTRAINT LandingPage_Organization_LandingPage_FOREIGN_KEY FOREIGN KEY (landingPage_id) REFERENCES LandingPage (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-344
ALTER TABLE LandingPage_Organization
    ADD CONSTRAINT LandingPage_Organization_Organization_FOREIGN_KEY FOREIGN KEY (organizations_id) REFERENCES `Organization` (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-345
ALTER TABLE Mission
    ADD CONSTRAINT Mission_FOREIGN_KEY FOREIGN KEY (job_id) REFERENCES Job (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-346
ALTER TABLE Notification
    ADD CONSTRAINT NotifiedRecruitment_Recruitment_FOREIGN_KEY FOREIGN KEY (recruitment_id) REFERENCES Recruitment (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-347
ALTER TABLE Notification
    ADD CONSTRAINT NotifiedRecruitment_User_FOREIGN_KEY FOREIGN KEY (userProfile_uuid) REFERENCES UserProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-348
ALTER TABLE RecruitmentProfile
    ADD CONSTRAINT RecruitmentProfile_FOREIGN_KEY FOREIGN KEY (job_id) REFERENCES Job (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-349
ALTER TABLE Recruitment_ErhgoClassification
    ADD CONSTRAINT Recruitment_ErhgoClassification_FOREIGN_KEY FOREIGN KEY (recruitment_id) REFERENCES Recruitment (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-350
ALTER TABLE Recruitment_usersIdToNotify
    ADD CONSTRAINT Recruitment_usersIdToNotify_FOREIGN_KEY FOREIGN KEY (recruitment_id) REFERENCES Recruitment (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:*************-351
CREATE OR REPLACE FUNCTION UuidToBin(s CHAR(36))
    RETURNS BINARY(16)
    DETERMINISTIC
    RETURN UNHEX(CONCAT(LEFT(s, 8), MID(s, 10, 4), MID(s, 15, 4), MID(s, 20, 4), RIGHT(s, 12)));

-- changeset odas:*************-352
CREATE OR REPLACE FUNCTION BinToUuid(b binary(16))
    RETURNS CHAR(36)
    RETURN CONCAT(LEFT(HEX(b), 8), '-', MID(HEX(b), 9, 4), '-', MID(HEX(b), 13, 4), '-', MID(HEX(b), 17, 4), '-',
                  RIGHT(HEX(b), 12));
