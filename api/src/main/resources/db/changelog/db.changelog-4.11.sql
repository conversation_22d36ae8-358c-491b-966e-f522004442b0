-- liquibase formatted sql

-- changeset eric:20240611:0
UPDATE ExternalOffer
SET remoteId = SUBSTRING_INDEX(SUBSTRING_INDEX(lastRawContent, '<ref type="varchar(50)">', -1), '</ref>', 1)
WHERE atsCode = 'eolia';
-- changeset eric:20240611:1
ALTER TABLE ExternalOffer
    ADD CONSTRAINT UC_RECRUITMENT_OFFER UNIQUE (recruitment_id);
-- changeset eric:20240612:2
ALTER TABLE RecruitmentCandidature
    ADD COLUMN atsSynchronizationState VARCHAR(255);
-- changeset eric:20240612:3
UPDATE RecruitmentCandidature
SET atsSynchronizationState='WAITING';
-- changeset eric:20240612:4
INSERT INTO ConfigurableProperty(propertyKey, propertyValue)
VALUES ('ats.eolia.disableCandidatureNotification', 'true');
