-- liquibase formatted sql

-- changeset eric:20250221:1
UPDATE ExternalOffer
SET configCode='MTAG'
where atsCode = 'softy';

-- changeset eric:20250221:2
INSERT INTO PerOfferATSConfigurationItem(id, recruiterCode, atsCode, remoteRecruiterCode, configCode, locationCode)
VALUES (UuidToBin(uuid()), 'S-21786', 'softy', 'groupeares', 'ARES', ''),
       (UuidToBin(uuid()), 'S-21691', 'softy', 'm-tag', 'MTAG', '');
-- changeset eric:20250217:1
UPDATE UserProfile_hardSkills s
SET hardSkills = CONCAT(hardSkills, (select IF(hardSkills IS NULL, '', CONCAT(', ', s2.hardSkills))
                                     from UserProfile_hardSkills s2
                                     where s2.hardSkills_KEY = 2
                                       AND s2.UserProfile_uuid = s.UserProfile_uuid));
-- changeset eric:20250217:2
DELETE
FROM UserProfile_hardSkills
WHERE hardSkills_KEY = 2;
-- changeset eric:20250217:3
UPDATE UserProfile_hardSkills
SET hardSkills_KEY=2
where hardSkills_KEY = 3;
