-- liquibase formatted sql

-- changeset eric:20241112:1
RENAME TABLE PerLocationATSConfigurationItem TO PerOfferATSConfigurationItem;

-- changeset eric:20241112:2
INSERT INTO PerOfferATSConfigurationItem(id, atsCode, configCode, locationCode, recruiterCode)
VALUES (uuidToBin('19c6cd67-d4ff-40aa-97de-72e57a942daa'), 'taleez', 'CONDAT', '', 'S-21690'),
       (uuidToBin('19c6cd67-d4ff-40aa-97de-72e57a942dab'), 'taleez', 'ACHIL', '', 'S-21746');

-- changeset eric:20241018:1
alter table UserProfile_descriptionForCapacity
    modify descriptionForCapacity varchar(2000) null;

-- changeset eric:20241018:2
CREATE TABLE UserProfile_hardSkills
(
    UserProfile_uuid BINARY(16)    NOT NULL,
    hardSkills       VARCHAR(2000) NULL,
    hardSkills_KEY   VARCHAR(500)  NOT NULL,
    CONSTRAINT PK_USERPROFILE_HARDSKILLS PRIMARY KEY (UserProfile_uuid, hardSkills_KEY)
);

-- changeset odas:20241018-2a
ALTER TABLE UserProfile_hardSkills
    ADD CONSTRAINT FK_USER_HARDSKILLS FOREIGN KEY (UserProfile_uuid) REFERENCES UserProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset eric:202411241:1
CREATE TABLE UserProfile_softSkills
(
    UserProfile_uuid BINARY(16)    NOT NULL,
    title            VARCHAR(256)  NULL,
    description      VARCHAR(2000) NULL,
    softSkills_ORDER INT           NOT NULL,
    CONSTRAINT PK_USERPROFILE_SOFTSKILLS PRIMARY KEY (UserProfile_uuid, title)
);

-- changeset eric:202411241:2
ALTER TABLE UserProfile RENAME COLUMN softSkillsPdfUrl TO trimojiPdfUrl;

-- changeset odas:202411241-2a
ALTER TABLE UserProfile_hardSkills
    ADD CONSTRAINT FK_USER_SOFTSKILLS FOREIGN KEY (UserProfile_uuid) REFERENCES UserProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset eric:202411241:3
DROP TABLE UserProfile_descriptionForCapacity;

-- changeset eric:202411241:4
DROP TABLE ErhgoOccupation_capacityFamilies;

-- changeset eric:202411241:5
ALTER TABLE Capacity
    drop column family;
