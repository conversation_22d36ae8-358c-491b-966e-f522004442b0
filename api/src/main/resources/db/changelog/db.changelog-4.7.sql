-- liquibase formatted sql

-- changeset odas:1714479605233-43
CREATE TABLE UserProfile_descriptionForCapacity
(
    UserProfile_uuid           BINARY(16)   NOT NULL,
    descriptionForCapacity     VARCHAR(255) NULL,
    descriptionForCapacity_KEY TINYINT      NOT NULL,
    CONSTRAINT PK_USERPROFILE_DESCRIPTIONFORCAPACITY PRIMARY KEY (UserProfile_uuid, descriptionForCapacity_KEY)
);

-- changeset odas:1714479605233-44
CREATE TABLE UserProfile_hashtags
(
    UserProfile_uuid BINARY(16)   NOT NULL,
    hashtags         VARCHAR(255) NULL,
    CONSTRAINT PK_USERPROFILE_HASHTAGS PRIMARY KEY (UserProfile_uuid, hashtags)
);

-- changeset odas:1714479605233-45
ALTER TABLE UserProfile
    CHANGE userBehaviorDescription_description userBehaviorDescription_attitude VARCHAR(2000);

-- changeset odas:1714479605233-46
ALTER TABLE UserProfile
    CHANGE userBehaviorDescription_modifiedByUser userBehaviorDescription_modifiedByUserInstant datetime DEFAULT null NULL;

-- changeset odas:1714479605233-47
CREATE INDEX FKgvax4p8m9jab9fi2eiln0f6ye ON Recruitment_sourcingUsersIdToNotify (recruitment_id);

-- changeset odas:1714479605233-48
CREATE INDEX FKgw56brv44dcvv97s0estvvwt1 ON UserProfile_hashtags (UserProfile_uuid);

-- changeset odas:1714479605233-49
ALTER TABLE UserProfile_descriptionForCapacity
    ADD CONSTRAINT FKbp0tiyq1ent59r43n72oqhjif FOREIGN KEY (UserProfile_uuid) REFERENCES UserProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:1714479605233-50
ALTER TABLE UserProfile_hashtags
    ADD CONSTRAINT FKgw56brv44dcvv97s0estvvwt1 FOREIGN KEY (UserProfile_uuid) REFERENCES UserProfile (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset odas:1714479605233-51
UPDATE CriteriaValue
SET titleForBO='Télétravail non autorisé'
WHERE code = 'REP-12-1';

-- changeset odas:1714479605233-52
UPDATE CriteriaValue
SET titleForBO='Télétravail occasionnel'
WHERE code = 'REP-12-2';
