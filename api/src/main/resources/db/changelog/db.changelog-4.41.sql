-- liquibase formatted sql

-- changeset amir:20250715:1
CREATE TABLE ScrapedUser
(
    uuid                      BINARY(16)                NOT NULL,
    createdBy_keycloakId      VARCHAR(255)              NULL,
    createdDate               datetime     DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)              NULL,
    updatedDate               datetime     DEFAULT NULL NULL,
    candidateId               VARCHAR(255)              NOT NULL,
    firstName                 VARCHAR(255)              NULL,
    lastName                  VARCHAR(255)              NULL,
    location                  VARCHAR(255)              NULL,
    jobTitle                  VARCHAR(255)              NULL,
    email                     VARCHAR(255)              NULL,
    cvDownloadLink            VARCHAR(500)              NULL,
    cvContent                 LONGTEXT                  NULL,
    errorMessage              LONGTEXT                  NULL,
    profileUrl                VARCHAR(500)              NULL,
    creationState             VARCHAR(250) DEFAULT NULL NULL,
    CONSTRAINT PK_SCRAPEDUSER PRIMARY KEY (uuid),
    CONSTRAINT UC_SCRAPED_USER_CANDIDATE_ID UNIQUE (candidateId)
);

-- changeset amir:20250715:2
INSERT INTO ConfigurableProperty(propertyKey, propertyValue)
VALUES ('agefiph.auth.token', 'bGF1cmVuxxxxxxxxxxxxxxxx');

