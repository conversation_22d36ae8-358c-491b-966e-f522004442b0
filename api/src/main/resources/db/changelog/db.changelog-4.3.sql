-- liquibase formatted sql

-- changeset odas:1710753075-1
ALTER TABLE Recruitment
    ADD COLUMN managerUserId VARCHAR(255);

-- changeset odas:1710753075-2
UPDATE Recruitment
SET managerUserId=createdBy_keycloakId;

-- changeset odas:1710753075-3
ALTER TABLE Recruitment
    MODIFY managerUserId VARCHAR(255) NOT NULL;

-- changeset clement:1710753075-4
ALTER TABLE ExternalOffer
    ADD COLUMN recruitment_id BIGINT NULL DEFAULT NULL;

-- changeset eric:1710753075-5
CREATE INDEX ExternalOffer_Recruitment_FOREIGN_KEY ON ExternalOffer (recruitment_id);

-- changeset eric:1710753075-6
ALTER TABLE ExternalOffer
    ADD CONSTRAINT ExternalOffer_Recruitment_FOREIGN_KEY FOREIGN KEY (recruitment_id) REFERENCES Recruitment (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

