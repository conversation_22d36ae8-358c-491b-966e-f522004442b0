-- liquibase formatted sql

-- changeset amir:20240628:0
ALTER TABLE UserExperience
ADD COLUMN createdDate DATETIME DEFAULT NULL,
ADD COLUMN createdBy_keycloakId VARCHAR(255) DEFAULT NULL,
ADD COLUMN updatedDate DATETIME DEFAULT NULL,
ADD COLUMN lastModifiedBy_keycloakId VARCHAR(255) DEFAULT NULL;
-- changeset eric:20240628:0
CREATE TABLE PerLocationATSConfigurationItem
(
    id              BINARY(16)   NOT NULL,
    atsCode         VARCHAR(255) NOT NULL,
    atsCustomerCode VARCHAR(255) NOT NULL,
    locationCode    VARCHAR(255) NOT NULL,
    recruiterCode   VARCHAR(255) NOT NULL,
    CONSTRAINT PK_PERLOCATIONATSCONFIGURATIONITEM PRIMARY KEY (id),
    CONSTRAINT UC_PERLOCATIONCONFIGITEM UNIQUE (atsCode, atsCustomerCode, locationCode)
);

-- changeset eric:20240629:1
ALTER TABLE ExternalOffer
    ADD COLUMN computedRecruiterCode VARCHAR(255);
