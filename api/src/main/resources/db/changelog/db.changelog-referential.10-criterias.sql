--liquibase formatted sql

--changeset eric:1632328583-1
insert into Criteria (code, criteriaIndex, questionLabel, questionType, title, required) VALUES
                    ('CR-1', 0, 'Le contrat que je recherche : ', 'MULTIPLE', 'Type et contrat de travail', true),
                    ('CR-10', 9, 'Dans le cadre de mon travail, j\'accepte de me déplacer :', 'THRESHOLD', 'Déplacements professionnels', false),
                    ('CR-11', 10, 'Les espaces où j\'accepte de travailler :', 'MULTIPLE', 'Espace de travail', false),
                    ('CR-12', 11, 'Le télétravail :', 'MULTIPLE', 'Télétravail', false),
                    ('CR-13', 12, 'J\'accepte de travailler avec un public international :', 'THRESHOLD', 'Public international', false),
                    ('CR-2', 1, 'Je recherche un emploi :', 'MULTIPLE', 'Temps de travail', true),
                    ('CR-3', 2, 'J\'accepte de travailler : ', 'MULTIPLE', 'Horai<PERSON> et rythme de travail', false),
                    ('CR-4', 3, 'Pour me rendre au travail : ', 'MULTIPLE', 'Localisation', false),
                    ('CR-5', 4, 'Je peux accepter un travail avec les conditions suivantes : ', 'MULTIPLE', 'Conditions de travail', false),
                    ('CR-6', 5, 'J\'ai le permis B (permis voiture) :', 'THRESHOLD', 'Permis de conduire', true),
                    ('CR-8', 7, 'Le dernier diplôme que j\'ai validé :', 'THRESHOLD', 'Diplômes', true),
                    ('CR-9', 8, 'Les logiciels bureautiques que je peux utiliser :', 'THRESHOLD', 'Aisance en informatique', true)
ON DUPLICATE KEY UPDATE questionLabel = VALUES(questionLabel), title = VALUES(title), questionType=VALUES(questionType), criteriaIndex = VALUES(criteriaIndex), required = VALUES(required);

--changeset eric:20250507-2
INSERT INTO CriteriaValue (code, icon, titleForQuestion, titleStandalone, valueIndex, criteria_code, titleForBO, sourcingCriteriaStep) VALUES
                                                                                    ('REP-1-1', 'file-signature', 'en CDI uniquement', 'un CDI', 0, 'CR-1', 'un CDI', null),
                                                                                    ('REP-1-2', 'file-signature', 'en CDD', 'un CDD', 1, 'CR-1', 'un CDD', null),
                                                                                    ('REP-1-3', 'file-signature', 'une alternance', 'une alternance', 2, 'CR-1', 'une Alternance', null),
                                                                                    ('REP-1-4', 'file-signature', 'un contrat saisonnier', 'un contrat saisonnier', 3, 'CR-1', 'un contrat saisonnier', null),
                                                                                    ('REP-1-5', 'file-signature', 'un contrat d\'interim', 'un contrat d\'interim', 4, 'CR-1', 'un contrat d\'interim', null),
                                                                                    ('REP-1-6', 'file-signature', 'un contrat d\'indépendant-freelance', 'un contrat d\'indépendant-freelance', 5, 'CR-1', 'un contrat d\'indépendant-freelance', null),
                                                                                    ('REP-10-1', 'parking-circle-slash', 'Jamais', 'pas de déplacement professionnel', 0, 'CR-10', 'Aucun', null),
                                                                                    ('REP-10-2', 'car', 'Rarement (une fois par mois ou moins)', 'déplacements professionnels rares', 1, 'CR-10', 'Max. une fois par mois', null),
                                                                                    ('REP-10-3', 'car', 'Régulièrement (jusqu\'à une fois par semaine)', 'déplacements professionnels réguliers', 2, 'CR-10', 'Max. une fois par semaine', null),
                                                                                    ('REP-10-4', 'car', 'Quotidiennement', 'déplacements professionnels quotidiens', 3, 'CR-10', 'Plusieurs fois par semaine', null),
                                                                                    ('REP-11-1', 'chair-office', 'En bureau', 'en bureau', 0, 'CR-11', 'Bureau', null),
                                                                                    ('REP-11-2', 'store', 'En magasin', 'en magasin', 1, 'CR-11', 'Magasin', null),
                                                                                    ('REP-11-3', 'industry-alt', 'En usine', 'en usine', 2, 'CR-11', 'Usine', null),
                                                                                    ('REP-11-4', 'sun-cloud', 'En plein air', 'en plein air', 3, 'CR-11', 'Plein air', null),
                                                                                    ('REP-12-1', 'car-building', 'J\'accepte de me rendre sur mon lieu de travail', 'présence sur site', 0, 'CR-12', 'Présence requise', 'STEP1'),
                                                                                    ('REP-12-2', 'laptop-house', 'J\'accepte les organisations mélant présence sur site et télétravail', 'présence sur site et de chez moi', 1, 'CR-12', 'Organisation hybride', 'STEP1'),
                                                                                    ('REP-12-3', 'house-user', 'J\'accepte les postes principalement à distance', 'travail de chez moi', 2, 'CR-12', 'Travail entièrement à distance', 'STEP1'),
                                                                                    ('REP-13-1', 'comment-alt-times', 'Non', 'pas de public international', 0, 'CR-13', 'Non', null),
                                                                                    ('REP-13-2', 'language', 'Oui', 'public international', 1, 'CR-13', 'Oui', null),
                                                                                    ('REP-2-1', 'hourglass-half', 'à temps plein', 'un temps plein', 0, 'CR-2', 'un temps plein', null),
                                                                                    ('REP-2-2', 'hourglass-half', 'à temps partiel', 'un temps partiel', 1, 'CR-2', 'un temps partiel', null),
                                                                                    ('REP-3-1', 'moon', 'de nuit', 'un travail de nuit', 0, 'CR-3', 'Travail de nuit', 'STEP1'),
                                                                                    ('REP-3-2', 'loveseat', 'les week-end', 'un travail le week-end', 1, 'CR-3', 'Travail le week-end', 'STEP1'),
                                                                                    ('REP-3-4', 'history', 'avec des horaires décalés', 'un travail avec des horaires décalés', 2, 'CR-3', 'Travail avec des horaires décalés', 'STEP1'),
                                                                                    ('REP-4-1', 'car', 'J\'accepte un travail non accessible en transports en commun', 'un travail non accessible en transports en communs', 0, 'CR-4', 'Non accessible en tranport en commun', 'STEP1'),
                                                                                    ('REP-5-1', 'chair-office', 'Position prolongée assise', 'une position prolongée assise', 0, 'CR-5', 'Position prolongée assise', null),
                                                                                    ('REP-5-2', 'male', 'Travail debout', 'un travail debout', 1, 'CR-5', 'Travail debout', null),
                                                                                    ('REP-5-3', 'person-carry', 'Charges lourdes', 'charges lourdes', 2, 'CR-5', 'Charges lourdes', null),
                                                                                    ('REP-5-4', 'conveyor-belt-alt', 'Activités répétitives', 'des activités répétitives', 3, 'CR-5', 'Activités répétitives', null),
                                                                                    ('REP-5-6', 'tv', 'Exposition aux écrans (informatique)', 'une exposition permanente aux écrans (informatique)', 4, 'CR-5', 'Exposition aux écrans (informatique)', null),
                                                                                    ('REP-6-1', 'ban', 'non', 'Je n\'ai pas le permis B', 0, 'CR-6', 'Pas besoin de permis B', 'STEP1'),
                                                                                    ('REP-6-2', 'id-card', 'je suis en train de le passer ', 'Je suis en train de passer le permis', 1, 'CR-6', 'Permis B en cours toléré', 'STEP1'),
                                                                                    ('REP-6-3', 'id-card', 'oui', 'J\'ai le permis B', 2, 'CR-6', 'Permis B nécessaire', 'STEP1'),
                                                                                    ('REP-8-1', 'file-certificate', 'Je n\'ai pas de diplôme', 'Je n\'ai pas de diplôme', 0, 'CR-8', 'Sans diplôme', null),
                                                                                    ('REP-8-2', 'file-certificate', 'J\'ai le Brevet des Collèges', 'J\'ai le Brevet des Collèges', 1, 'CR-8', 'Brevet des collèges', null),
                                                                                    ('REP-8-3', 'file-certificate', 'J\'ai un CAP ou BEP', 'J\'ai un CAP ou BEP', 2, 'CR-8', 'CAP, BEP', null),
                                                                                    ('REP-8-4', 'file-certificate', 'J\'ai un Bac ou un Bac pro', 'J\'ai un Bac ou un Bac pro', 3, 'CR-8', 'Bac, Bac Pro', null),
                                                                                    ('REP-8-5', 'file-certificate', 'J\'ai un diplôme niveau DEUG, DUT, BTS ou DEUST', 'J\'ai un diplôme niveau DEUG, DUT, BTS ou DEUST', 4, 'CR-8', 'DEUG, DUT, BTS, DEUST', null),
                                                                                    ('REP-8-6', 'file-certificate', 'J\'ai une licence ou une licence pro', 'J\'ai une licence ou une licence pro', 5, 'CR-8', 'Licence, licence professionnelle', null),
                                                                                    ('REP-8-7', 'file-certificate', 'J\'ai un master ou un diplôme d\'ingénieur', 'J\'ai un master ou un diplôme d\'ingénieur', 6, 'CR-8', 'Master 2, Diplôme d’ingénieur', null),
                                                                                    ('REP-8-8', 'file-certificate', 'J\'ai un doctorat', 'J\'ai un doctorat', 7, 'CR-8', 'Doctorat', null),
                                                                                    ('REP-9-1', 'ban', 'Je ne me sers pas de l\'informatique', 'Je ne me sers pas de l\'informatique', 0, 'CR-9', 'Pas d\'informatique', null),
                                                                                    ('REP-9-2', 'envelope-open-text', 'Je sais utiliser une boite mail et des outils bureautiques simples', 'Je sais utiliser une boite mail et des outils bureautiques simples', 1, 'CR-9', 'Utilisation des mails et bureautique simple', null),
                                                                                    ('REP-9-3', 'laptop-code', 'Je suis à l\'aise en général avec les outils bureautiques avancés', 'Je suis à l\'aise en général avec les outils bureautiques', 2, 'CR-9', 'Utilisation bureautique avancée', null)
ON DUPLICATE KEY UPDATE icon = VALUES(icon), titleStandalone = VALUES(titleStandalone), titleForQuestion = VALUES(titleForQuestion), titleForBO = VALUES(titleForBO), valueIndex = VALUES(valueIndex), criteria_code = VALUES(criteria_code);
