-- liquibase formatted sql

-- changeset eric:1711727660897-35
CREATE TABLE ExternalOfferContentHistoryItem
(
    uuid                      BINARY(16)              NOT NULL,
    createdBy_keycloakId      VARCHAR(255)            NULL,
    createdDate               datetime   DEFAULT NULL NULL,
    lastModifiedBy_keycloakId VARCHAR(255)            NULL,
    updatedDate      datetime   NOT NULL,
    xmlContent       LONGTEXT   NOT NULL,
    externalOffer_id BINARY(16) NOT NULL,
    CONSTRAINT PK_EXTERNALOFFERCONTENTHISTORYITEM PRIMARY KEY (uuid)
);

-- changeset eric:1711727660897-37
CREATE INDEX FKl4lc2bgafkir4c96ai3iroaug ON ExternalOfferContentHistoryItem (externalOffer_id);

-- changeset eric:1711727660897-38
ALTER TABLE ExternalOfferContentHistoryItem
    ADD CONSTRAINT FKl4lc2bgafkir4c96ai3iroaug FOREIGN KEY (externalOffer_id) REFERENCES ExternalOffer (uuid) ON UPDATE RESTRICT ON DELETE RESTRICT;


