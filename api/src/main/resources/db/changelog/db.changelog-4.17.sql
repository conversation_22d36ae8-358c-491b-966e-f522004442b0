-- liquibase formatted sql

-- changeset amir:20240809:1
ALTER TABLE ErhgoOccupation
    ADD COLUMN occupationCreationReason VARCHAR(255) DEFAULT NULL NULL;
-- changeset amir:20240809:2
UPDATE ErhgoOccupation SET occupationCreationReason = 'ESCO' WHERE id IN (SELECT ErhgoOccupation_id FROM ErhgoOccupation_EscoOccupation);

-- changeset amir:20240809:3
UPDATE ErhgoOccupation
SET occupationCreationReason = 'CV' WHERE id IN (SELECT DISTINCT erhgoOccupation_id FROM UserExperience);

-- changeset amir:20240809:4
UPDATE ErhgoOccupation
SET occupationCreationReason = 'CSV'
WHERE id IN (
    SELECT DISTINCT j.erhgoOccupation_id
    FROM Recruitment r
             INNER JOIN RecruitmentProfile rp ON r.recruitmentProfile_uuid = rp.uuid
             INNER JOIN Job j ON rp.job_id = j.id
             LEFT JOIN ExternalOffer eo ON eo.recruitment_id = r.id
    WHERE eo.uuid IS NULL
);

-- changeset amir:20240809:5
UPDATE ErhgoOccupation
SET occupationCreationReason = 'ATS'
WHERE id IN (
    SELECT DISTINCT j.erhgoOccupation_id
    FROM Recruitment r
             INNER JOIN RecruitmentProfile rp ON r.recruitmentProfile_uuid = rp.uuid
             INNER JOIN Job j ON rp.job_id = j.id
             INNER JOIN ExternalOffer eo ON eo.recruitment_id = r.id
);
