-- liquibase formatted sql

-- changeset eric:20250401:1
ALTER TABLE RecruitmentCandidature
    RENAME COLUMN atsSynchronizationState TO synchronizationState;

-- changeset eric:20250401:1-2
ALTER TABLE SpontaneousCandidature
    RENAME COLUMN atsSynchronizationState TO synchronizationState;

-- changeset eric:20250401:2-2
UPDATE RecruitmentCandidature
set synchronizationState = 'IGNORE'
WHERE id in
      (SELECT distinct rc.id
       FROM Organization o
                inner join Job j on o.id = j.recruiter_id
                inner join RecruitmentProfile rp on j.id = rp.job_id
                inner join Recruitment r on rp.uuid = r.recruitmentProfile_uuid
                inner join RecruitmentCandidature rc on r.id = rc.recruitment_id
                left join SourcingSubscription ss on ss.recruiter_id = o.id
                left join SourcingInvitation sc on sc.uuid = ss.invitation_uuid
       where r.id not in (select eo.recruitment_id from ExternalOffer eo where eo.recruitment_id is not null)
         and o.organizationType = 'SOURCING'
         and rc.state = 4
         and (sc.code not like '%MOIS' OR sc.code IS NULL)
         and rc.synchronizationState = 'WAITING'
         and (submissionDate < DATE_ADD(SYSDATE(), INTERVAL - 10 DAY) OR
              rc.globalCandidatureState <> 'NOT_TREATED_BY_ERHGO'))
;

-- changeset eric:20250401:3-2
UPDATE SpontaneousCandidature
set synchronizationState = 'IGNORE'
WHERE id in
      (SELECT distinct rc.id
       FROM Organization o
                inner join SpontaneousCandidature rc on o.id = rc.recruiter_id
                left join SourcingSubscription ss on ss.recruiter_id = o.id
                left join SourcingInvitation sc on sc.uuid = ss.invitation_uuid
       where o.organizationType = 'SOURCING'
         and (sc.code not like '%MOIS' OR sc.code IS NULL)
         and (submissionDate < DATE_ADD(SYSDATE(), INTERVAL - 10 DAY) OR
              rc.globalCandidatureState <> 'NOT_TREATED_BY_ERHGO')
         and rc.synchronizationState = 'WAITING')
;
-- changeset eric:20250401:4
INSERT INTO ConfigurableProperty(propertyKey, propertyValue)
values ('ats.limit.per-recruiter.SUCCESS_FACTORS--KEOLIS.default', '0'),
       ('ats.limit.per-recruiter.SUCCESS_FACTORS--KEOLIS.S-21436', '12'),
       ('ats.limit.per-recruiter.SUCCESS_FACTORS--KEOLIS.S-21697', '12'),
       ('ats.limit.per-recruiter.SUCCESS_FACTORS--KEOLIS.S-21698', '12'),
       ('ats.limit.per-recruiter.SUCCESS_FACTORS--KEOLIS.S-21699', '12'),
       ('ats.limit.per-recruiter.SUCCESS_FACTORS--KEOLIS.S-21700', '12'),
       ('ats.limit.per-recruiter.SUCCESS_FACTORS--KEOLIS.S-21701', '12'),
       ('ats.limit.per-recruiter.SUCCESS_FACTORS--KEOLIS.S-21702', '12'),
       ('ats.limit.per-recruiter.SUCCESS_FACTORS--KEOLIS.S-21704', '12'),
       ('ats.limit.per-recruiter.TEAMTAILOR--ALL.default', '0')
;
-- changeset eric:20250401:5
INSERT INTO PerOfferATSConfigurationItem(id, recruiterCode, atsCode, remoteRecruiterCode, configCode, locationCode)
VALUES (UuidToBin(uuid()), 'S-21871', 'taleez', 'GROUPELG', '', '');
