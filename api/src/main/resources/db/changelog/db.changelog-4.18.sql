-- liquibase formatted sql

-- changeset amir:20240820:1
DROP TABLE IF EXISTS JobDatingSlotForUserProfile;

-- changeset amir:20240820:2
DROP TABLE IF EXISTS JobDatingSlot;

-- changeset amir:20240820:3
DROP TABLE IF EXISTS JobDating;

-- changeset amir:20240821:1
ALTER TABLE UserProfile_WorkDomain DROP FOREIGN KEY FK_USER_DOMAINS_USER;
ALTER TABLE UserProfile_WorkDomain DROP FOREIGN KEY FK_USER_DOMAINS_DOMAINS;
ALTER TABLE ErhgoOccupation DROP FOREIGN KEY FK_OCCUPATION_WORK_DOMAIN;

-- changeset amir:20240821:2
DROP TABLE IF EXISTS WorkDomain;

-- changeset amir:20240821:3
DROP TABLE IF EXISTS UserProfile_WorkDomain;

-- changeset amir:20240821:4
ALTER TABLE UserProfile DROP COLUMN IF EXISTS workDomains;

-- changeset amir:20240821:5
ALTER TABLE ErhgoOccupation DROP COLUMN IF EXISTS workDomain_code;

-- changeset amir:20240905:1
DROP TABLE IF EXISTS ErhgoOccupationCapacity;
-- changeset eric:20240906:1
insert into Notification(recruitment_id, userProfile_uuid, createdBy_keycloakId, createdDate, lastModifiedBy_keycloakId,
                         updatedDate, type, id, DTYPE, state)
SELECT rc.recruitment_id,
       rc.userProfile_uuid,
       'ADMIN',
       r.updatedDate,
       null,
       null,
       'NONE',
       UuidToBin(UUID()),
       'SUSPENDED_RECRUITMENT',
       'NOT_INTERESTED'
FROM RecruitmentCandidature rc
         inner join Recruitment r on rc.recruitment_id = r.id
where r.state in (2, 3)
  and r.updatedDate <= DATE_SUB(SYSDATE(), INTERVAL 3 DAY)

-- changeset eric:20240909:1
ALTER TABLE ErhgoOccupation
    DROP COLUMN isVisibleForOrientation,
    DROP COLUMN priority;

-- changeset eric:20240924:1
DELETE FROM GeneralInformation
       WHERE userProfile_uuid IN (SELECT uuid FROM UserProfile WHERE isLegacyBO IS TRUE);

DELETE FROM UserProfile
       WHERE isLegacyBO IS TRUE;

ALTER TABLE UserProfile
    DROP COLUMN isLegacyBO;
