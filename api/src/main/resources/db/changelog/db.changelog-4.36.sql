-- liquibase formatted sql

-- changeset eric:20250507:3
UPDATE Recruitment
set typeContract=7
where typeContract = 0
  and title like '%(freelance)%'
  and id in (select recruitment_id from ExternalOffer where computedRecruiterCode in ('S-21879', 'S-21679'));

-- changeset eric:20250507:6
UPDATE Job_CriteriaValue
set criteriaValues_code = 'REP-1-6'
where job_id in
      (select j.id
       from Job j
                inner join RecruitmentProfile rp on j.id = rp.job_id
                inner join Recruitment r on j.id = rp.job_id
                inner join ExternalOffer eo on r.id = eo.recruitment_id
       where eo.computedRecruiterCode in ('S-21879', 'S-21679')
         and j.title like '%freelance%')
  AND criteriaValues_code = 'REP-1-2';
