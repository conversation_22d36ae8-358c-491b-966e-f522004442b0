-- liquibase formatted sql

-- changeset eric:20240522:0
ALTER TABLE ExternalOffer
    DROP INDEX UC_REMOTE_ID_ATS_CODE;

-- changeset eric:20240522:1
ALTER TABLE ExternalOfferContentHistoryItem
    CHANGE xmlContent rawContent LONGTEXT;

-- changeset eric:20240522:2
ALTER TABLE ExternalOffer
    CHANGE lastXmlContent lastRawContent LONGTEXT NOT NULL;

-- changeset eric:20240522:3
ALTER TABLE ExternalOffer
    ADD COLUMN remoteId_varchar VARCHAR(255);

-- changeset eric:20240522:3-2
UPDATE ExternalOffer
SET remoteId_varchar = CAST(remoteId AS CHAR);

-- changeset eric:20240522:4
ALTER TABLE ExternalOffer
    DROP COLUMN remoteId;

-- changeset eric:20240522:5
ALTER TABLE ExternalOffer
    CHANGE remoteId_varchar remoteId VARCHAR(255);

-- changeset eric:20240522:6
ALTER TABLE ExternalOffer
    ADD CONSTRAINT UC_REMOTE_ID_ATS_CODE UNIQUE (remoteId, atsCode);

-- changeset eric:20240522:7
ALTER TABLE ExternalOffer
    ADD COLUMN offerTitle VARCHAR(1024);
-- changeset eric:20240522:8
ALTER TABLE ExternalOffer
    ADD COLUMN offerRecruiter VARCHAR(1024);
-- changeset eric:20240522:9
ALTER TABLE ExternalOffer
    ADD COLUMN offerLocation VARCHAR(1024);
-- changeset eric:20240522:10
ALTER TABLE ExternalOffer
    ADD COLUMN relatedUsernames VARCHAR(2048);
-- changeset eric:20240522:11
ALTER TABLE ExternalOffer
    MODIFY lastRawContent LONGTEXT CHARACTER SET utf8mb4;
