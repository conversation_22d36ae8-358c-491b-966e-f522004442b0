-- liquibase formatted sql

-- changeset eric:20240923:1
DROP TABLE IF EXISTS CandidatureActivity;
-- changeset amir:20240921:1
INSERT INTO ErhgoOccupation_alternativeLabels (ErhgoOccupation_id, alternativeLabels)
SELECT o.id,
       TRIM(SUBSTRING(o.title FROM INSTR(o.title, '/') + 1))
FROM ErhgoOccupation o
WHERE o.title LIKE '%/%'
  AND NOT (
    LOWER(RIGHT(TRIM(o.title), 10)) LIKE '%nb%'
        AND TRIM(o.title) LIKE '%)'
    )
  AND NOT EXISTS (SELECT 1
                  FROM ErhgoOccupation_alternativeLabels a
                  WHERE a.ErhgoOccupation_id = o.id
                    AND a.alternativeLabels = TRIM(SUBSTRING(o.title FROM INSTR(o.title, '/') + 1)));

DELETE a
FROM ErhgoOccupation_alternativeLabels a
         JOIN ErhgoOccupation o ON a.ErhgoOccupation_id = o.id
WHERE o.title LIKE '%/%'
  AND a.alternativeLabels = TRIM(SUBSTRING_INDEX(o.title, '/', 1))
  AND NOT (
    LOWER(RIGHT(TRIM(o.title), 10)) LIKE '%nb%'
        AND TRIM(o.title) LIKE '%)'
    );


UPDATE ErhgoOccupation
SET title = TRIM(SUBSTRING_INDEX(title, '/', 1))
WHERE title LIKE '%/%'
  AND NOT (
    LOWER(RIGHT(TRIM(title), 10)) LIKE '%nb%'
        AND TRIM(title) LIKE '%)'
    );

DELETE a
FROM ErhgoOccupation_alternativeLabels a
         JOIN ErhgoOccupation o ON a.ErhgoOccupation_id = o.id
WHERE LOWER(RIGHT(TRIM(o.title), 10)) LIKE '%nb%'
  AND TRIM(o.title) LIKE '%)'
  AND a.alternativeLabels =
      TRIM(REGEXP_REPLACE(o.title, '\\( *([FfHh]|[Nn][Bb])( *\\/ *([FfHh]|[Nn][Bb]))* *\\) *$', ''));

UPDATE ErhgoOccupation
SET title = TRIM(REGEXP_REPLACE(title, '\\( *([FfHh]|[Nn][Bb])( *\\/ *([FfHh]|[Nn][Bb]))* *\\) *$', ''))
WHERE LOWER(RIGHT(TRIM(title), 10)) LIKE '%nb%'
  AND TRIM(title) LIKE '%)';

-- changeset amir:20240920:2
UPDATE ErhgoOccupation_alternativeLabels a
SET a.alternativeLabels = CONCAT('XXX  ', a.alternativeLabels)
where a.alternativeLabels like '%NB)'
  and exists(select 1
             from ErhgoOccupation_alternativeLabels a2
             where a.ErhgoOccupation_id = a2.ErhgoOccupation_id
               and trim(LOWER(a2.alternativeLabels)) =
                   LOWER(TRIM(REGEXP_REPLACE(a.alternativeLabels,
                                             '\\( *([FfHh]|[Nn][Bb])( *\\/ *([FfHh]|[Nn][Bb]))* *\\) *$', ''))));
delete
from ErhgoOccupation_alternativeLabels
where alternativeLabels like 'XXX  %';

UPDATE ErhgoOccupation_alternativeLabels a
SET a.alternativeLabels = TRIM(REGEXP_REPLACE(a.alternativeLabels,
                                              '\\( *([FfHh]|[Nn][Bb])( *\\/ *([FfHh]|[Nn][Bb]))* *\\) *$', ''))
where a.alternativeLabels like '%NB)'
  and not exists(select 1
                 from ErhgoOccupation_alternativeLabels a2
                 where a.ErhgoOccupation_id = a2.ErhgoOccupation_id
                   and trim(LOWER(a2.alternativeLabels)) = LOWER(TRIM(REGEXP_REPLACE(a.alternativeLabels,
                                                                                     '\\( *([FfHh]|[Nn][Bb])( *\\/ *([FfHh]|[Nn][Bb]))* *\\) *$',
                                                                                     ''))));

-- changeset amir:20240921:4
INSERT INTO ErhgoOccupation_alternativeLabels(alternativeLabels, ErhgoOccupation_id)
SELECT TRIM(SUBSTRING_INDEX(alternativeLabels, '/', 1)),
       ErhgoOccupation_id
FROM ErhgoOccupation_alternativeLabels
WHERE alternativeLabels LIKE '% / %'
  AND alternativeLabels NOT LIKE '%NB)'
  AND (LENGTH(SUBSTRING_INDEX(alternativeLabels, '/', 1)) -
       LENGTH(REPLACE(SUBSTRING_INDEX(alternativeLabels, '/', 1), ' ', '')) + 1)
    = (LENGTH(SUBSTRING_INDEX(alternativeLabels, '/', -1)) -
       LENGTH(REPLACE(SUBSTRING_INDEX(alternativeLabels, '/', -1), ' ', '')) + 1)
  AND NOT EXISTS(SELECT 1
                 FROM ErhgoOccupation_alternativeLabels l2
                 where l2.alternativeLabels =
                       TRIM(SUBSTRING_INDEX(ErhgoOccupation_alternativeLabels.alternativeLabels, '/', 1)))
  AND NOT EXISTS(SELECT 1
                 FROM ErhgoOccupation l2
                 where l2.title = TRIM(SUBSTRING_INDEX(ErhgoOccupation_alternativeLabels.alternativeLabels, '/', 1)));

INSERT INTO ErhgoOccupation_alternativeLabels(alternativeLabels, ErhgoOccupation_id)
SELECT TRIM(SUBSTRING(alternativeLabels FROM INSTR(alternativeLabels, '/') + 1)),
       ErhgoOccupation_id
FROM ErhgoOccupation_alternativeLabels
WHERE alternativeLabels LIKE '% / %'
  AND alternativeLabels NOT LIKE '%NB)'
  AND (LENGTH(SUBSTRING_INDEX(alternativeLabels, '/', 1)) -
       LENGTH(REPLACE(SUBSTRING_INDEX(alternativeLabels, '/', 1), ' ', '')) + 1)
    = (LENGTH(SUBSTRING_INDEX(alternativeLabels, '/', -1)) -
       LENGTH(REPLACE(SUBSTRING_INDEX(alternativeLabels, '/', -1), ' ', '')) + 1)
  AND NOT EXISTS(SELECT 1
                 FROM ErhgoOccupation_alternativeLabels l2
                 where l2.alternativeLabels = TRIM(SUBSTRING(alternativeLabels FROM INSTR(alternativeLabels, '/') + 1)))
  AND NOT EXISTS(SELECT 1
                 FROM ErhgoOccupation l2
                 where l2.title = TRIM(SUBSTRING(alternativeLabels FROM INSTR(alternativeLabels, '/') + 1)));

delete
from ErhgoOccupation_alternativeLabels
WHERE alternativeLabels LIKE '% / %'
  AND alternativeLabels NOT LIKE '%NB)'
  AND (LENGTH(SUBSTRING_INDEX(alternativeLabels, '/', 1)) -
       LENGTH(REPLACE(SUBSTRING_INDEX(alternativeLabels, '/', 1), ' ', '')) + 1)
    = (LENGTH(SUBSTRING_INDEX(alternativeLabels, '/', -1)) -
       LENGTH(REPLACE(SUBSTRING_INDEX(alternativeLabels, '/', -1), ' ', '')) + 1);
