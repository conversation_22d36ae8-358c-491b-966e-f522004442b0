<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.6.xsd">
    <!-- Initial physical scripts -->
    <include file="db.changelog-4.0.sql"
             relativeToChangelogFile="true"
             context="!master and !staging and !demo and !testing and !default"
    />
    <!-- Catch up missing constraints -->
    <include file="db.changelog-4.1.sql" relativeToChangelogFile="true" context="!test and !e2e"/>
    <include file="db.changelog-4.2.sql" relativeToChangelogFile="true" context="!default and !test and !e2e"/>
    <!-- ###################################################################################################### -->
    <!-- #######################################   Migration scripts   ######################################## -->
    <!-- ###################################################################################################### -->
    <include file="db.changelog-4.3.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.4.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.5.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.6.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.7.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.8.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.9.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.10.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.11.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.12.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.13.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.14.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.15.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.16.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.17.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.18.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.19.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.20.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.21.sql" relativeToChangelogFile="true" context="!test and !e2e"/>
    <include file="db.changelog-4.22.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.23.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.24.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.25.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.26.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.27.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.28.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.29.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.30.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.31.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.32.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.33.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.34.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.35.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.36.sql" relativeToChangelogFile="true"
             context="master or staging"/>
    <include file="db.changelog-4.37.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.38.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.39.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.40.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-4.41.sql" relativeToChangelogFile="true"/>
    <!-- ###################################################################################################### -->
    <!-- #######################################   Utils scripts   ############################################ -->
    <!-- ###################################################################################################### -->
    <include file="db.changelog-utils.data-health-checks.sql" relativeToChangelogFile="true" context="!test"/>

    <!-- ###################################################################################################### -->
    <!-- #######################################   Referential datas   ######################################## -->
    <!-- ###################################################################################################### -->

    <include file="db.changelog-referential.1-behaviors-capacities.sql" relativeToChangelogFile="true"
             context="!master and !staging and !demo and !testing and !default and !test"/>
    <include file="db.changelog-referential.2-isco-rome-occupations.sql" relativeToChangelogFile="true"
             context="!master and !staging and !demo and !testing and !default and !e2e and !test"/>
    <include file="db.changelog-referential.5-configurable-properties.sql" relativeToChangelogFile="true"/>
    <include file="db.changelog-referential.6-work-environments.sql" relativeToChangelogFile="true"
             context="!master and !staging and !demo and !testing and !default"/>
    <include file="db.changelog-referential.7-erhgo-classifications.sql" relativeToChangelogFile="true"
             context="!master and !staging and !demo and !testing and !default"/>
    <include file="db.changelog-referential.8-sectors.sql" relativeToChangelogFile="true"
             context="!master and !staging and !demo and !testing and !default"/>
    <include file="db.changelog-referential.9-personal-domains.sql" relativeToChangelogFile="true"
             context="!master and !staging and !demo and !testing and !default"/>
    <include file="db.changelog-referential.10-criterias.sql" relativeToChangelogFile="true"
             context="!master and !staging and !demo and !testing and !default and !test"/>

</databaseChangeLog>
