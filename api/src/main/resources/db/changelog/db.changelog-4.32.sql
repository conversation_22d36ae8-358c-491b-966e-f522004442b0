-- liquibase formatted sql

-- changeset eric:20250127:1
ALTER TABLE RecruitmentCandidature
    DROP INDEX IF EXISTS idx_rc1_0_user_recruitment_state;
-- changeset eric:20250203:2
ALTER TABLE Notification
    DROP INDEX IF EXISTS IDX_NOTIFICATION_TYPE_AND_RECRUITMENT;
-- changeset eric:20250203:3
ALTER TABLE Notification
    DROP INDEX IF EXISTS IDX_NOTIFICATION_TYPE;
-- changeset eric:20250203:4
ALTER TABLE RecruitmentCandidature
    DROP INDEX IF EXISTS IDX_CANDIDATURE_STATE_AND_RECRUITMENT;
-- changeset eric:20250203:4-1
ALTER TABLE RecruitmentCandidature
    DROP INDEX IF EXISTS IDX_CANDIDATURE_STATE_AND_RECRUITMENT_AND_USER;
-- changeset eric:20250203:5
ALTER TABLE Recruitment
    DROP INDEX IF EXISTS IDX_RECRUITMENT_PUBLICATION_END;
-- changeset eric:20250203:5-0
ALTER TABLE Recruitment
    DROP INDEX IF EXISTS IDX_NOTIF_TYPE_AND_USER_AND_RECRUITMENT;
-- changeset eric:20250203:5-1
ALTER TABLE ExternalOffer
    DROP INDEX IF EXISTS idx_external_offer_ats_code;
-- changeset eric:20250203:6
CREATE INDEX idx_rc1_0_user_recruitment_state ON RecruitmentCandidature (userProfile_uuid, recruitment_id, globalCandidatureState);
-- changeset eric:20250203:7-1
CREATE INDEX IDX_NOTIF_TYPE_AND_USER_AND_RECRUITMENT ON Notification (DTYPE, userProfile_uuid, recruitment_id);
-- changeset eric:20250203:8
CREATE INDEX IDX_NOTIFICATION_TYPE ON Notification (DTYPE);
-- changeset eric:20250203:9
CREATE INDEX IDX_CANDIDATURE_STATE_AND_RECRUITMENT ON RecruitmentCandidature (globalCandidatureState, userProfile_uuid, recruitment_id);
-- changeset eric:20250203:10
CREATE INDEX IDX_RECRUITMENT_PUBLICATION_END on Recruitment (publicationEndDate);
-- changeset eric:20250203:11
CREATE INDEX IDX_NOTIF_TYPE_AND_RECRUITMENT ON Notification (DTYPE, recruitment_id);
-- changeset eric:20250203:12
CREATE INDEX idx_external_offer_ats_code ON ExternalOffer(atsCode);
