-- liquibase formatted sql

-- changeset eric:20241224:1
ALTER TABLE ExternalOfferContentHistoryItem
    MODIFY COLUMN rawContent LONGTEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- changeset eric:20241224:2
ALTER TABLE ExternalOffer
    MODIFY COLUMN lastRawContent LONGTEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- changeset eric:20241224:22
DROP INDEX description on Recruitment;
-- changeset eric:20241224:3
ALTER TABLE Recruitment
    MODIFY COLUMN description LONGTEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- changeset eric:20241224:4
ALTER TABLE Recruitment
    MODIFY COLUMN organizationDescription LONGTEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- changeset eric:20241224:5
ALTER TABLE Recruitment
    MODIFY COLUMN title VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- changeset eric:20241224:6
ALTER TABLE Job
    MODIFY COLUMN title VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- changeset eric:20241224:7
ALTER TABLE Recruitment
    ADD FULLTEXT (description, organizationDescription);
-- changeset eric:20241224:8
ALTER TABLE ExternalOffer
    MODIFY COLUMN offerTitle varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
