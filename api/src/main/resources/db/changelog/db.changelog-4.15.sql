-- liquibase formatted sql

-- changeset eric:20240705:0
INSERT INTO ConfigurableProperty(propertyKey, propertyValue)
    VALUE ('ats.limit.per-recruiter.default', 3);

-- changeset eric:20240705:1
UPDATE ConfigurableProperty
SET propertyKey = 'ats.per-aura-in-out.in.adecco'
WHERE propertyKey = 'ats.adecco.max_offer_in_aura';

UPDATE ConfigurableProperty
SET propertyKey = 'ats.per-aura-in-out.out.adecco'
WHERE propertyKey = 'ats.adecco.max_offer_out_aura';

UPDATE ConfigurableProperty
SET propertyKey = 'ats.per-aura-in-out.in.default'
WHERE propertyKey = 'ats.default.max_offer_in_aura';

UPDATE ConfigurableProperty
SET propertyKey = 'ats.per-aura-in-out.out.default'
WHERE propertyKey = 'ats.default.max_offer_out_aura';

UPDATE ConfigurableProperty
SET propertyKey = 'ats.notification-delay-in-hours.default'
WHERE propertyKey = 'ats.default.notification_delay_in_hours';

UPDATE ConfigurableProperty
SET propertyKey = 'ats.per-aura-in-out.in.DIGITAL_RECRUITERS'
WHERE propertyKey = 'ats.DIGITAL_RECRUITERS.max_offer_in_aura';

UPDATE ConfigurableProperty
SET propertyKey = 'ats.per-aura-in-out.out.DIGITAL_RECRUITERS'
WHERE propertyKey = 'ats.DIGITAL_RECRUITERS.max_offer_out_aura';

UPDATE ConfigurableProperty
SET propertyKey = 'ats.disable-candidature-notification.eolia'
WHERE propertyKey = 'ats.eolia.disableCandidatureNotification';

UPDATE ConfigurableProperty
SET propertyKey = 'ats.per-aura-in-out.in.eolia'
WHERE propertyKey = 'ats.eolia.max_offer_in_aura';

UPDATE ConfigurableProperty
SET propertyKey = 'ats.per-aura-in-out.out.eolia'
WHERE propertyKey = 'ats.eolia.max_offer_out_aura';

-- changeset marin:20240704:0
UPDATE UserProfile SET registrationStep = registrationStep - 1 WHERE registrationStep >= 3;
