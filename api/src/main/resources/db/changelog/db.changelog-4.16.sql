-- liquibase formatted sql

-- changeset eric:20240709:0
UPDATE Organization
SET gdprMention='<p>Pour vous permettre de candidater à cette offre, merci de prendre connaissance des Mentions légales du Groupe Adecco, disponibles ici&nbsp;: <a href="https://www.adecco.fr/infos-legales/" target="_blank">https://www.adecco.fr/infos-legales/</a>. Vos coordonnées lui sont automatiquement transmises.</p>'
WHERE code = 'S-21712';
-- changeset eric:20240709:1
INSERT INTO ConfigurableProperty(propertyKey, propertyValue) VALUE ('ats.disable-candidature-notification.adecco', 'true');
-- changeset eric:20240724:0
ALTER TABLE ExternalOffer
    DROP CONSTRAINT UC_REMOTE_ID_ATS_CODE;

-- changeset eric:20240724:1
ALTER TABLE ExternalOffer
    ADD CONSTRAINT UC_REMOTE_ID_ATS_CODE UNIQUE (remoteId, atsCode, computedRecruiterCode);
-- changeset eric:20240802:1
INSERT INTO ConfigurableProperty(propertyKey, propertyValue)
VALUES ('ats.disable-candidature-notification.beetween', 'true');

-- changeset eric:20240802:2
INSERT INTO ConfigurableProperty(propertyKey, propertyValue)
VALUES ('ats.per-aura-in-out.in.beetween', '0'),
       ('ats.per-aura-in-out.out.beetween', '0');
-- changeset eric:20240805:1
ALTER TABLE RecruitmentCandidature
    ADD COLUMN remoteNotifiedIdentifier VARCHAR(255) DEFAULT NULL NULL;
-- changeset eric:20240802:3
INSERT INTO ConfigurableProperty(propertyKey, propertyValue)
VALUES ('ats.disable-candidature-notification.taleez', 'true');

-- changeset eric:20240802:4
DELETE
FROM ConfigurableProperty
WHERE propertyKey like 'ats.per-aura-in-out.%.beetween';

-- changeset eric:20240802:5
INSERT INTO ConfigurableProperty(propertyKey, propertyValue)
VALUES ('ats.per-aura-in-out.in.taleez--', '0'),
       ('ats.per-aura-in-out.out.taleez--', '0'),
       ('ats.per-aura-in-out.in.beetween--', '0'),
       ('ats.per-aura-in-out.out.beetween--', '0');
