-- liquibase formatted sql

-- changeset eric:20241001:1
DELETE
FROM ConfigurableProperty
where propertyKey like 'ats.per-aura-in-out.%.adecco%'
   OR propertyKey like 'ats.per-aura-in-out.%.HELLO_WORK';

INSERT INTO ConfigurableProperty (propertyKey, propertyValue)
VALUES ('ats.per-aura-in-out.out.adecco', '1')
     , ('ats.per-aura-in-out.in.adecco', '1')
     , ('ats.per-aura-in-out.in.adecco--ain', '3')
     , ('ats.per-aura-in-out.in.adecco--allier', '3')
     , ('ats.per-aura-in-out.in.adecco--ardèche', '3')
     , ('ats.per-aura-in-out.in.adecco--cantal', '3')
     , ('ats.per-aura-in-out.in.adecco--dr<PERSON>', '3')
     , ('ats.per-aura-in-out.in.adecco--haute-loire', '3')
     , ('ats.per-aura-in-out.in.adecco--haute-savoie', '3')
     , ('ats.per-aura-in-out.in.adecco--isère', '3')
     , ('ats.per-aura-in-out.in.adecco--loire', '3')
     , ('ats.per-aura-in-out.in.adecco--puy-de-dôme', '3')
     , ('ats.per-aura-in-out.in.adecco--rhône', '6')
     , ('ats.per-aura-in-out.in.adecco--savoie', '3')
     , ('ats.per-aura-in-out.in.HELLO_WORK', '0')
     , ('ats.per-aura-in-out.out.HELLO_WORK', '0');

-- changeset eric:20242410:1
UPDATE ExternalOffer
SET configCode='JP'
where atsCode = 'beetween';
