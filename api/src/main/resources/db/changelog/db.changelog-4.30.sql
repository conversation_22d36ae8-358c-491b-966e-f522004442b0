-- liquibase formatted sql

-- changeset eric:20250114:1
ALTER TABLE PerOfferATSConfigurationItem
    ADD COLUMN remoteRecruiterCode VARCHAR(255);

-- changeset eric:20250114:3
ALTER TABLE ExternalOffer
    RENAME COLUMN offerRecruiter TO offerRecruiterCode;

-- changeset eric:20250114:4
ALTER TABLE PerOfferATSConfigurationItem
    DROP CONSTRAINT UC_PERLOCATIONCONFIGITEM;

-- changeset eric:20250114:5
ALTER TABLE PerOfferATSConfigurationItem
    ADD CONSTRAINT UC_PERLOCATIONCONFIGITEM UNIQUE (atsCode, configCode, remoteRecruiterCode, locationCode);

-- changeset eric:20250114:2
DELETE
FROM PerOfferATSConfigurationItem
WHERE atsCode IN ('TALENT_PLUG', 'taleez');
INSERT INTO PerOfferATSConfigurationItem(id, recruiterCode, atsCode, remoteRecruiterCode, configCode, locationCode)
VALUES (UuidToBin(uuid()), 'S-21749', 'TALENT_PLUG', 'HAVAS VOYAGES', 'MARIETTON', ''),
       (UuidToBin(uuid()), 'S-21757', 'TALENT_PLUG', 'HAVAS VOYAGES GROUPES', 'MARIETTON', ''),
       (UuidToBin(uuid()), 'S-21762', 'TALENT_PLUG', 'AUCHAN VOYAGES', 'MARIETTON', ''),
       (UuidToBin(uuid()), 'S-21750', 'TALENT_PLUG', 'HAVAS VOYAGES SPORT', 'MARIETTON', ''),
       (UuidToBin(uuid()), 'S-21763', 'TALENT_PLUG', 'HAVAS VOYAGES MEETING & EVENT', 'MARIETTON', ''),
       (UuidToBin(uuid()), 'S-21752', 'TALENT_PLUG', 'VOYAMAR', 'MARIETTON', ''),
       (UuidToBin(uuid()), 'S-21753', 'TALENT_PLUG', 'HELIADES', 'MARIETTON', ''),
       (UuidToBin(uuid()), 'S-21754', 'TALENT_PLUG', 'AUSTRAL LAGONS', 'MARIETTON', ''),
       (UuidToBin(uuid()), 'S-21755', 'TALENT_PLUG', 'AKILANGA', 'MARIETTON', ''),
       (UuidToBin(uuid()), 'S-21759', 'TALENT_PLUG', 'AMAHUACA', 'MARIETTON', ''),
       (UuidToBin(uuid()), 'S-21756', 'TALENT_PLUG', 'AILLEURS BUSINESS', 'MARIETTON', ''),
       (UuidToBin(uuid()), 'S-21760', 'TALENT_PLUG', 'SELECTOUR AILLEURS VOYAGES', 'MARIETTON', ''),
       (UuidToBin(uuid()), 'S-21761', 'TALENT_PLUG', 'AILLEURS EVENTS', 'MARIETTON', ''),
       (UuidToBin(uuid()), 'S-21718', 'TALENT_PLUG', 'MARIETTON DEVELOPPEMENT', 'MARIETTON', ''),
       (UuidToBin(uuid()), 'S-21758', 'TALENT_PLUG', 'ALTANUEVA', 'MARIETTON', '');
INSERT INTO PerOfferATSConfigurationItem(id, atsCode, configCode, remoteRecruiterCode, locationCode, recruiterCode)
VALUES (uuidToBin('19c6cd67-d4ff-40aa-97de-72e57a942daa'), 'taleez', '', 'CONDAT', '', 'S-21690'),
       (uuidToBin('19c6cd67-d4ff-40aa-97de-72e57a942dab'), 'taleez', '', 'ACHIL', '', 'S-21746');

-- changeset amir:20250116:1
ALTER TABLE ExternalOffer
    ADD COLUMN recruitmentCreationState VARCHAR(255);

-- changeset amir:20250116:2
UPDATE ExternalOffer
SET recruitmentCreationState='IGNORE'
WHERE lastEventType = 'CANCELED';

-- changeset amir:20250116:3
UPDATE ExternalOffer
SET recruitmentCreationState='DONE'
WHERE recruitment_id IS NOT NULL;

-- changeset amir:20250116:31
UPDATE ExternalOffer
SET recruitmentCreationState='ERROR'
WHERE recruitmentCreationState is NULL;

-- changeset amir:20250116:4
UPDATE ExternalOffer
SET lastEventType='SUSPENDED'
WHERE lastEventType = 'CANCELED';

-- changeset amir:20250116:5
ALTER TABLE ExternalOffer
    MODIFY COLUMN recruitmentCreationState VARCHAR(255) NOT NULL;

