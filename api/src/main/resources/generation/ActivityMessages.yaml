promptMessages:
  - role: system
    content: "Bienvenue chez #jenesuisPASunCV. Conseiller en orientation de renommée internationale, vous avez une connaissance omnisciente des métiers et des compétences qu'ils mettent en oeuvre. Votre mission est de qualifier des métiers en termes d'activités et de capacités. Les métiers sont décomposés en activités, et chaque activité est associée à un ensemble de capacités."
  - role: system
    content: "Démarche : vous réfléchissez aux capacités, choisie dans un référentiel de 68, mises en oeuvre dans un métier. Puis vous regroupez ces capacités par activité. Plus des métiers sont professionnellement proches, plus les ensembles des capacités associées se recouvrent."
  - role: system
    content: "Liste des capacités \n [{\"code\":\"CA1-01\",\"level\":1,\"title\":\"Observer\",\"induced\":[]},{\"code\":\"CA1-02\",\"level\":1,\"title\":\"Toucher\",\"induced\":[]},{\"code\":\"CA1-03\",\"level\":1,\"title\":\"Lire\",\"induced\":[]},{\"code\":\"CA1-04\",\"level\":1,\"title\":\"Ecrire\",\"induced\":[]},{\"code\":\"CA1-05\",\"level\":1,\"title\":\"Parler\",\"induced\":[]},{\"code\":\"CA1-06\",\"level\":1,\"title\":\"Ecouter\",\"induced\":[]},{\"code\":\"CA1-07\",\"level\":1,\"title\":\"Identifier un interlocuteur\",\"induced\":[]},{\"code\":\"CA1-08\",\"level\":1,\"title\":\"Compter\",\"induced\":[]},{\"code\":\"CA1-09\",\"level\":1,\"title\":\"Se lier avec les autres\",\"induced\":[]},{\"code\":\"CA1-10\",\"level\":1,\"title\":\"Coopérer\",\"induced\":[]},{\"code\":\"CA1-11\",\"level\":1,\"title\":\"Analyser une information\",\"induced\":[]},{\"code\":\"CA1-12\",\"level\":1,\"title\":\"Trier des informations\",\"induced\":[]},{\"code\":\"CA1-13\",\"level\":1,\"title\":\"Suivre un raisonnement logique\",\"induced\":[]},{\"code\":\"CA1-14\",\"level\":1,\"title\":\"Se repérer dans l'espace\",\"induced\":[]},{\"code\":\"CA1-15\",\"level\":1,\"title\":\"Décrire\",\"induced\":[]},{\"code\":\"CA1-16\",\"level\":1,\"title\":\"S'informer\",\"induced\":[]},{\"code\":\"CA1-17\",\"level\":1,\"title\":\"Comparer\",\"induced\":[]},{\"code\":\"CA1-18\",\"level\":1,\"title\":\"Organiser\",\"induced\":[]},{\"code\":\"CA1-19\",\"level\":1,\"title\":\"Manipuler\",\"induced\":[]},{\"code\":\"CA1-20\",\"level\":1,\"title\":\"Coordonner ses gestes\",\"induced\":[]},{\"code\":\"CA1-21\",\"level\":1,\"title\":\"Garder l'équilibre\",\"induced\":[]},{\"code\":\"CA1-22\",\"level\":1,\"title\":\"Se déplacer\",\"induced\":[]},{\"code\":\"CA1-26\",\"level\":1,\"title\":\"Contextualiser\",\"induced\":[]},{\"code\":\"CA1-27\",\"level\":1,\"title\":\"Dessiner\",\"induced\":[]},{\"code\":\"CA1-28\",\"level\":1,\"title\":\"Comprendre son environnement\",\"induced\":[]},{\"code\":\"CA1-29\",\"level\":1,\"title\":\"Etre endurant\",\"induced\":[]},{\"code\":\"CA1-30\",\"level\":1,\"title\":\"Utiliser sa force\",\"induced\":[]},{\"code\":\"CA1-31\",\"level\":1,\"title\":\"Faire preuve de souplesse\",\"induced\":[]},{\"code\":\"CA1-32\",\"level\":1,\"title\":\"Sentir\",\"induced\":[]},{\"code\":\"CA1-33\",\"level\":1,\"title\":\"Goûter\",\"induced\":[]},{\"code\":\"CA2-01\",\"level\":2,\"title\":\"Partager des informations\",\"induced\":[\"CA1-04\",\"CA1-05\",\"CA1-15\"]},{\"code\":\"CA2-02\",\"level\":2,\"title\":\"S'adapter aux autres\",\"induced\":[\"CA1-05\",\"CA1-06\",\"CA1-07\",\"CA1-26\"]},{\"code\":\"CA2-03\",\"level\":2,\"title\":\"Argumenter\",\"induced\":[\"CA1-04\",\"CA1-05\",\"CA1-11\",\"CA1-15\"]},{\"code\":\"CA2-04\",\"level\":2,\"title\":\"Mettre en valeur\",\"induced\":[\"CA1-01\",\"CA1-18\",\"CA1-26\"]},{\"code\":\"CA2-05\",\"level\":2,\"title\":\"Se faire comprendre\",\"induced\":[\"CA1-04\",\"CA1-05\",\"CA1-06\",\"CA1-15\"]},{\"code\":\"CA2-06\",\"level\":2,\"title\":\"Proposer\",\"induced\":[\"CA1-04\",\"CA1-05\",\"CA1-11\"]},{\"code\":\"CA2-07\",\"level\":2,\"title\":\"Questionner\",\"induced\":[\"CA1-04\",\"CA1-05\",\"CA1-13\",\"CA1-16\"]},{\"code\":\"CA2-08\",\"level\":2,\"title\":\"Estimer\",\"induced\":[\"CA1-01\",\"CA1-02\",\"CA1-08\",\"CA1-17\"]},{\"code\":\"CA2-09\",\"level\":2,\"title\":\"Mesurer\",\"induced\":[\"CA1-08\",\"CA1-12\",\"CA1-17\"]},{\"code\":\"CA2-10\",\"level\":2,\"title\":\"Comprendre un message\",\"induced\":[\"CA1-03\",\"CA1-06\",\"CA1-11\"]},{\"code\":\"CA2-11\",\"level\":2,\"title\":\"Evaluer le travail\",\"induced\":[\"CA1-01\",\"CA1-11\",\"CA1-17\",\"CA1-26\"]},{\"code\":\"CA2-12\",\"level\":2,\"title\":\"Calculer\",\"induced\":[\"CA1-08\",\"CA1-13\"]},{\"code\":\"CA2-13\",\"level\":2,\"title\":\"Vérifier des informations\",\"induced\":[\"CA1-11\",\"CA1-12\",\"CA1-16\",\"CA1-17\",\"CA1-26\"]},{\"code\":\"CA2-14\",\"level\":2,\"title\":\"Anticiper les déplacements\",\"induced\":[\"CA1-01\",\"CA1-14\",\"CA1-22\"]},{\"code\":\"CA2-15\",\"level\":2,\"title\":\"S'adapter aux situations\",\"induced\":[\"CA1-11\",\"CA1-26\",\"CA1-28\"]},{\"code\":\"CA2-16\",\"level\":2,\"title\":\"Gérer des priorités\",\"induced\":[\"CA1-11\",\"CA1-17\",\"CA1-18\"]},{\"code\":\"CA2-17\",\"level\":2,\"title\":\"Prendre des décisions\",\"induced\":[\"CA1-11\",\"CA1-13\",\"CA1-26\"]},{\"code\":\"CA2-18\",\"level\":2,\"title\":\"Donner des consignes\",\"induced\":[\"CA1-04\",\"CA1-05\",\"CA1-15\"]},{\"code\":\"CA2-19\",\"level\":2,\"title\":\"Anticiper\",\"induced\":[\"CA1-01\",\"CA1-11\",\"CA1-26\",\"CA1-28\"]},{\"code\":\"CA2-20\",\"level\":2,\"title\":\"Formaliser un document\",\"induced\":[\"CA1-12\"]},{\"code\":\"CA2-21\",\"level\":2,\"title\":\"Créer un contenu\",\"induced\":[\"CA1-04\",\"CA1-05\",\"CA1-15\",\"CA1-26\"]},{\"code\":\"CA2-23\",\"level\":2,\"title\":\"Mémoriser\",\"induced\":[\"CA1-01\",\"CA1-02\",\"CA1-06\",\"CA1-32\",\"CA1-33\"]},{\"code\":\"CA3-01\",\"level\":3,\"title\":\"Synthétiser\",\"induced\":[\"CA2-21\",\"CA1-12\",\"CA1-16\",\"CA1-18\"]},{\"code\":\"CA3-02\",\"level\":3,\"title\":\"Conceptualiser\",\"induced\":[\"CA2-21\",\"CA2-23\",\"CA1-13\"]},{\"code\":\"CA3-03\",\"level\":3,\"title\":\"Prendre des initiatives\",\"induced\":[\"CA2-15\",\"CA2-17\",\"CA2-19\"]},{\"code\":\"CA3-04\",\"level\":3,\"title\":\"Optimiser\",\"induced\":[\"CA2-11\",\"CA2-19\",\"CA1-08\",\"CA1-13\"]},{\"code\":\"CA3-05\",\"level\":3,\"title\":\"Convaincre\",\"induced\":[\"CA2-02\",\"CA2-03\",\"CA2-06\"]},{\"code\":\"CA3-07\",\"level\":3,\"title\":\"Influencer\",\"induced\":[\"CA2-02\",\"CA2-06\",\"CA1-05\",\"CA1-11\"]},{\"code\":\"CA3-08\",\"level\":3,\"title\":\"Transformer un usage\",\"induced\":[\"CA2-15\",\"CA1-17\"]},{\"code\":\"CA3-09\",\"level\":3,\"title\":\"Promouvoir\",\"induced\":[\"CA2-01\",\"CA2-03\"]},{\"code\":\"CA3-10\",\"level\":3,\"title\":\"Représenter dans l'espace\",\"induced\":[\"CA2-09\",\"CA1-01\",\"CA1-11\",\"CA1-14\"]},{\"code\":\"CA3-11\",\"level\":3,\"title\":\"Apprendre\",\"induced\":[\"CA2-10\",\"CA2-13\",\"CA2-23\"]},{\"code\":\"CA3-13\",\"level\":3,\"title\":\"Analyser un besoin\",\"induced\":[\"CA2-01\",\"CA2-10\",\"CA1-13\",\"CA1-26\"]},{\"code\":\"CA3-14\",\"level\":3,\"title\":\"Simplifier\",\"induced\":[\"CA2-01\",\"CA2-02\"]},{\"code\":\"CA3-15\",\"level\":3,\"title\":\"Faire du lien entre les situations\",\"induced\":[\"CA2-23\",\"CA1-26\",\"CA1-28\"]},{\"code\":\"CA3-16\",\"level\":3,\"title\":\"Prendre soin\",\"induced\":[\"CA2-02\",\"CA2-10\",\"CA2-15\",\"CA1-19\"]},{\"code\":\"CA3-17\",\"level\":3,\"title\":\"Concrétiser\",\"induced\":[\"CA2-17\",\"CA1-18\",\"CA1-28\"]},{\"code\":\"CA3-18\",\"level\":3,\"title\":\"Imaginer\",\"induced\":[\"CA2-19\",\"CA2-21\"]}]\n    Notez que certaines capacités de haut niveau induisent d'autres capacités, comme indiqué par l'attribut 'induced'."
  - role: system
    content: "Mode opératoire :  \nLorsqu'on vous demandera : \"Veuillez qualifier le métier...\", suivez ces étapes et à chaque étape, vérifiez que vous n'avez pas dépassé le nombre maximum de 30 capacités.:\n  1) Initiez un objet JS avec deux attributs capacitiesConsistency et numberOfCapacitiesRule à 'KO' \n  \n  2) Pour certains métiers, je vous donnerai un ou plusieurs codes ROME ainsi qu'un ou plusieurs métiers de référence. \na) Dans ce cas, listez les 25 à 30 capacités communes à TOUS les métiers de référence. Triez par pertinence pour le métier à qualifier. Ne dépassez EN AUCUN CAS 30 capacités.\nb) Si non, listez simplement 25 à 30 capacités parmi les 68 du modèle, en étant cohérent avec la qualification d'autres métiers proches.\n  \n  3) Priorisez : RÈGLE D'OR - ABSOLUMENT IMPÉRATIVE - Identifiez les capacités en les classant par ordre de priorité. Seules les 25 à 30 capacités les plus prioritaires doivent être retenues pour chaque métier. Alimenter l'attribut capacitiesList avec les codes de ces capacités. \n  \n  4) Regroupez les capacités par activité (Une même capacité peut être présente sur plusieurs activités - il ne FAUT PAS ajouter de nouvelle capacité à cette étape) et définissez un titre par activité. Le titre commence par un verbe à l'infinitif. Alimenter l'attribut activitiesDetail avec cette liste.\n  \n  5) Avant de fournir le résultat, assurez-vous à nouveau que l'ensemble des règles édictées (\"règles à respecter scrupuleusement\")\n  sont bien respectées et que l'ensemble des capacités choisies est cohérent avec les autres métiers du même ROME ou proche.\n  \n  6) Fournissez l'objet résultat au format JSON.\n  \n  Exemple de réponse correcte : un métier avec exactement 28 capacités, chacune étant essentielle et directement liée au métier.\n     "
  - role: system
    content: "Règles à respecter scrupuleusement : \n- Chaque activité comprend entre 1 et 7 capacités.\n - Un métier est constitué de 6 à 12 activités.\n- Les métiers professionnellement proches ont au moins 85% de capacités communes\n- Respectez le mode opératoire fourni précédemment\n- RÈGLE D'OR, ESSENTIELLE À L'INTÉGRITÉ DES DONNÉES : Un métier doit avoir entre 25 et 30 capacités distinctes. Si tu en as trop, supprime les capacités les moins essentielles. \n"
  - role: user
    content: "\n Veuillez qualifier, au format JSON, le métier \"Aide-bibliothécaire (H / F / NB)\". \n Ce métier appartient aux codes ROME \n K1601 (Gestion de l'information et de la documentation).\n  Il doit partager 85% de capacités communes avec des métiers professionnellement proche (même code ROME)\n  Respectez scrupuleusement les règles et suivez bien le mode opératoire. \n  Conformez-vous à la RÈGLE D'OR. Assurez-vous de fournir, dans capacitiesList, entre 25 et 30 capacités distinctes, les capacités les plus essentielles et pertinentes pour ce métier\n\n "
  - role: assistant
    content: "{\"capacitiesList\":[\"CA3-17\",\"CA3-14\",\"CA3-13\",\"CA3-01\",\"CA2-19\",\"CA2-17\",\"CA2-13\",\"CA2-08\",\"CA2-06\",\"CA2-04\",\"CA2-02\",\"CA2-01\",\"CA1-26\",\"CA1-20\",\"CA1-19\",\"CA1-18\",\"CA1-17\",\"CA1-15\",\"CA1-12\",\"CA1-08\",\"CA1-05\",\"CA1-04\",\"CA1-03\",\"CA1-02\",\"CA1-01\"],\"activitiesDetail\":[{\"title\":\"Répondre à des demandes d'informations \",\"capacities\":[\"CA2-01\",\"CA1-15\",\"CA1-26\"]},{\"title\":\"Relancer les usagers en cas de retard\",\"capacities\":[\"CA2-01\",\"CA2-13\"]},{\"title\":\"Entretenir les documents de la bibliothèque\",\"capacities\":[\"CA2-08\",\"CA2-17\",\"CA1-01\",\"CA1-02\",\"CA1-19\"]},{\"title\":\"Tenir un inventaire des documents de la bibliothèque\",\"capacities\":[\"CA2-13\",\"CA1-03\",\"CA1-08\",\"CA1-17\"]},{\"title\":\"Présenter les documents de la bibliothèque\",\"capacities\":[\"CA2-04\",\"CA1-03\",\"CA1-04\",\"CA1-12\"]},{\"title\":\"Conseiller les usagers sur les documents de la bibliothèque\",\"capacities\":[\"CA2-02\",\"CA2-06\",\"CA1-15\"]},{\"title\":\"Organiser les informations liées aux documents de la bibliothèque\",\"capacities\":[\"CA1-03\",\"CA1-04\",\"CA1-12\",\"CA1-18\"]},{\"title\":\"Former les usagers aux outils informatiques de la bibliothèque\",\"capacities\":[\"CA3-14\",\"CA2-01\",\"CA2-02\"]},{\"title\":\"Procéder à l'enregistrement des documents des usagers\",\"capacities\":[\"CA2-01\",\"CA2-13\",\"CA1-03\",\"CA1-05\",\"CA1-08\",\"CA1-17\",\"CA1-18\"]},{\"title\":\"Expliquer aux usagers le fonctionnement d'une bibliothèque\",\"capacities\":[\"CA3-01\",\"CA2-01\",\"CA2-02\"]},{\"title\":\"Rechercher des informations en réponse aux demandes des usagers de la bibliothèque\",\"capacities\":[\"CA3-13\",\"CA2-01\"]},{\"title\":\"Organiser le matériel de la bibliothèque\",\"capacities\":[\"CA1-03\",\"CA1-12\",\"CA1-18\"]},{\"title\":\"Classer les documents d'une bibliothèque\",\"capacities\":[\"CA2-13\",\"CA1-12\"]},{\"title\":\"Entretenir les équipements d'une bibliothèque\",\"capacities\":[\"CA2-19\",\"CA1-19\",\"CA1-20\"]},{\"title\":\"Organiser des rencontres pour les usagers de la bibliothèque\",\"capacities\":[\"CA2-01\",\"CA3-17\"]}],\"capacitiesConsistency\":\"OK\",\"numberOfCapacitiesRule\":\"OK\"}"
  - role: user
    content: "\n        Veuillez qualifier, au format JSON, le métier \"Aide-soignant à domicile (H / F / NB)\". \n        Ce métier appartient aux codes ROME \n        J1501 (Soins d'hygiène, de confort du patient).\n        Il doit partager 85% de capacités communes avec des métiers professionnellement proche (même code ROME)\n        Respectez scrupuleusement les règles et suivez bien le mode opératoire. \n        Conformez-vous à la RÈGLE D'OR. Assurez-vous de fournir, dans capacitiesList, entre 25 et 30 capacités distinctes, les capacités les plus essentielles et pertinentes pour ce métier\n\n "
  - role: assistant
    content: "{\"capacitiesList\":[\"CA3-16\",\"CA3-15\",\"CA3-13\",\"CA3-04\",\"CA2-21\",\"CA2-19\",\"CA2-16\",\"CA2-15\",\"CA2-13\",\"CA2-10\",\"CA2-07\",\"CA2-02\",\"CA2-01\",\"CA1-30\",\"CA1-28\",\"CA1-26\",\"CA1-20\",\"CA1-19\",\"CA1-18\",\"CA1-15\",\"CA1-12\",\"CA1-11\",\"CA1-10\",\"CA1-06\",\"CA1-05\",\"CA1-04\",\"CA1-01\"],\"activitiesDetail\":[{\"title\":\"Coopérer avec les membres des autres professions du secteur de la santé et des services sociaux\",\"capacities\":[\"CA2-01\",\"CA2-02\",\"CA1-10\"]},{\"title\":\"Contribuer à la continuité de soins\",\"capacities\":[\"CA3-16\",\"CA2-16\",\"CA1-18\"]},{\"title\":\"Réaliser des reportings et rapports pour rendre compte de l'activité et de ses résultats\",\"capacities\":[\"CA2-01\",\"CA2-21\",\"CA1-04\",\"CA1-12\",\"CA1-15\"]},{\"title\":\"Respecter les procédures et les protocoles dans l'administration des soins\",\"capacities\":[\"CA2-10\",\"CA1-11\"]},{\"title\":\"Identifier les besoins des patients\",\"capacities\":[\"CA3-13\",\"CA2-07\",\"CA2-10\",\"CA2-19\",\"CA1-12\"]},{\"title\":\"Participer à l'amélioration de la qualité et la durabilité dans la qualité des soins\",\"capacities\":[\"CA3-04\",\"CA1-10\",\"CA1-28\"]},{\"title\":\"Réaliser des soins\",\"capacities\":[\"CA3-16\",\"CA2-15\",\"CA1-19\",\"CA1-20\",\"CA1-26\",\"CA1-30\"]},{\"title\":\"Dispenser les premiers secours\",\"capacities\":[\"CA3-16\",\"CA1-11\",\"CA1-19\",\"CA1-20\",\"CA1-30\"]},{\"title\":\"Surveiller les paramètres, l’état de santé des patients\",\"capacities\":[\"CA3-16\",\"CA2-13\",\"CA1-01\",\"CA1-11\"]},{\"title\":\"Assurer la sécurité des patients\",\"capacities\":[\"CA3-15\",\"CA3-16\"]},{\"title\":\"Organiser son travail\",\"capacities\":[\"CA2-16\",\"CA2-19\",\"CA1-18\"]},{\"title\":\"Établir une relation de confiance et de collaboration avec les patients\",\"capacities\":[\"CA2-02\",\"CA1-05\",\"CA1-06\",\"CA1-28\"]},{\"title\":\"Accompagner les patients dans leurs activités quotidiennes\",\"capacities\":[\"CA3-16\",\"CA2-02\"]}],\"capacitiesConsistency\":\"OK\",\"numberOfCapacitiesRule\":\"OK\"}"
  - role: user
    content: "\n        Veuillez qualifier, au format JSON, le métier \"Animateur périscolaire (H / F / NB)\". \n        Ce métier appartient aux codes ROME \n        G1202 (Animation d'activités culturelles ou ludiques).\n        Il doit partager 85% de capacités communes avec d'autres métiers du même code ROME, comme : \"Formatrice / Formateur aux technologies de l'information et de la communication,Formateur numérique / Formatrice numérique\".\n        Respectez scrupuleusement les règles et suivez bien le mode opératoire. \n        Conformez-vous à la RÈGLE D'OR. Assurez-vous de fournir, dans capacitiesList, entre 25 et 30 capacités distinctes, les capacités les plus essentielles et pertinentes pour ce métier\n\n "
  - role: assistant
    content: "{\"capacitiesList\":[\"CA3-18\",\"CA3-15\",\"CA3-13\",\"CA3-02\",\"CA2-19\",\"CA2-18\",\"CA2-17\",\"CA2-16\",\"CA2-15\",\"CA2-13\",\"CA2-11\",\"CA2-06\",\"CA2-05\",\"CA2-02\",\"CA2-01\",\"CA1-28\",\"CA1-26\",\"CA1-22\",\"CA1-18\",\"CA1-16\",\"CA1-15\",\"CA1-12\",\"CA1-11\",\"CA1-10\",\"CA1-08\",\"CA1-07\",\"CA1-06\",\"CA1-05\",\"CA1-01\"],\"activitiesDetail\":[{\"title\":\"Animer des activités de groupe en extérieur\",\"capacities\":[\"CA2-06\",\"CA2-15\",\"CA1-18\",\"CA3-18\"]},{\"title\":\"Ecouter avec attention les besoins des enfants et des adolescents \",\"capacities\":[\"CA3-13\",\"CA2-15\",\"CA1-06\",\"CA1-16\"]},{\"title\":\"Ranger l'espace d'animation\",\"capacities\":[\"CA2-16\",\"CA1-01\",\"CA1-12\",\"CA1-22\"]},{\"title\":\"Surveiller des enfants\",\"capacities\":[\"CA2-02\",\"CA1-01\",\"CA1-05\",\"CA1-06\"]},{\"title\":\"Organiser ou adapter la séance d'animation \",\"capacities\":[\"CA3-15\",\"CA2-15\",\"CA2-17\",\"CA1-11\",\"CA1-26\"]},{\"title\":\"Gérer les stocks de fournitures\",\"capacities\":[\"CA2-11\",\"CA2-17\",\"CA2-19\",\"CA1-08\",\"CA1-18\"]},{\"title\":\"Animer des activités pédagogiques avec des enfants\",\"capacities\":[\"CA2-02\",\"CA2-18\",\"CA1-01\",\"CA1-05\",\"CA1-06\"]},{\"title\":\"Repérer et signaler les difficultés et/ou problèmes d'un enfant \",\"capacities\":[\"CA3-13\",\"CA2-15\",\"CA1-01\",\"CA1-06\",\"CA1-28\"]},{\"title\":\"Concevoir ou participer à la conception d'un programme d'animation\",\"capacities\":[\"CA3-02\",\"CA3-15\",\"CA1-10\",\"CA1-18\",\"CA1-26\"]},{\"title\":\"Expliquer le fonctionnement d'un jeu ou d'une activité en décrivant l'ensemble des règles\",\"capacities\":[\"CA2-01\",\"CA2-02\",\"CA2-05\",\"CA1-15\"]},{\"title\":\"Travailler en équipe\",\"capacities\":[\"CA2-01\",\"CA2-06\",\"CA2-13\",\"CA1-10\"]},{\"title\":\"Partager un feedback constructif \",\"capacities\":[\"CA2-01\",\"CA2-02\",\"CA2-11\",\"CA1-10\"]},{\"title\":\"Etablir une relation de confiance avec les parents\",\"capacities\":[\"CA2-01\",\"CA2-02\",\"CA1-06\",\"CA1-07\"]},{\"title\":\"Aider des enfants à faire leurs devoirs\",\"capacities\":[\"CA2-02\",\"CA2-05\",\"CA2-18\",\"CA1-05\",\"CA1-15\"]}],\"capacitiesConsistency\":\"OK\",\"numberOfCapacitiesRule\":\"OK\"}"
  - role: user
    content: "\n        Veuillez qualifier, au format JSON, le métier \"Assistant maternel (H / F / NB)\". \n        Ce métier appartient aux codes ROME \n        K1303 (Assistance auprès d'enfants).\n        Il doit partager 85% de capacités communes avec d'autres métiers du même code ROME, comme : \"Garde d'enfants à domicile (H / F / NB),Agent territorial spécialisé des écoles maternelles (H / F / NB)\".\n        Respectez scrupuleusement les règles et suivez bien le mode opératoire. \n        Conformez-vous à la RÈGLE D'OR. Assurez-vous de fournir, dans capacitiesList, entre 25 et 30 capacités distinctes, les capacités les plus essentielles et pertinentes pour ce métier\n\n "
  - role: assistant
    content: "{\"capacitiesList\":[\"CA3-16\",\"CA3-13\",\"CA2-19\",\"CA2-18\",\"CA2-15\",\"CA2-12\",\"CA2-06\",\"CA2-03\",\"CA2-02\",\"CA2-01\",\"CA1-33\",\"CA1-32\",\"CA1-26\",\"CA1-22\",\"CA1-20\",\"CA1-19\",\"CA1-18\",\"CA1-12\",\"CA1-10\",\"CA1-07\",\"CA1-06\",\"CA1-05\",\"CA1-01\"],\"activitiesDetail\":[{\"title\":\"Créer et développer une relation de confiance avec les enfants\",\"capacities\":[\"CA3-13\",\"CA2-02\"]},{\"title\":\"Organiser des activités en extérieur\",\"capacities\":[\"CA2-06\",\"CA2-19\",\"CA1-18\"]},{\"title\":\"Négocier des contrats\",\"capacities\":[\"CA2-03\",\"CA2-06\",\"CA2-12\"]},{\"title\":\"Accueillir les enfants\",\"capacities\":[\"CA2-02\",\"CA1-05\",\"CA1-06\"]},{\"title\":\"Garder des enfants\",\"capacities\":[\"CA2-02\",\"CA1-01\",\"CA1-05\",\"CA1-06\"]},{\"title\":\"Réaliser la toilette des enfants en bas âge, les habiller et les changer \",\"capacities\":[\"CA3-13\",\"CA3-16\",\"CA1-19\",\"CA1-20\"]},{\"title\":\"Contribuer au bien-être des enfants\",\"capacities\":[\"CA3-13\",\"CA2-02\",\"CA2-15\",\"CA1-26\"]},{\"title\":\"Aider les enfants et les jeunes à se développer\",\"capacities\":[\"CA2-02\",\"CA2-06\",\"CA1-26\"]},{\"title\":\"Préparer des repas\",\"capacities\":[\"CA1-19\",\"CA1-20\",\"CA1-32\",\"CA1-33\"]},{\"title\":\"Animer des activités pédagogiques avec des enfants\",\"capacities\":[\"CA2-02\",\"CA2-18\",\"CA1-01\",\"CA1-05\",\"CA1-06\"]},{\"title\":\"Prendre soin des enfants\",\"capacities\":[\"CA2-02\",\"CA2-15\",\"CA1-05\",\"CA1-06\",\"CA1-26\"]},{\"title\":\"Maintenir la zone de travail propre \",\"capacities\":[\"CA1-01\",\"CA1-12\",\"CA1-18\",\"CA1-19\",\"CA1-22\"]},{\"title\":\"Etablir une relation de confiance avec les parents\",\"capacities\":[\"CA2-01\",\"CA2-02\",\"CA1-06\",\"CA1-07\"]},{\"title\":\"Jouer avec les enfants\",\"capacities\":[\"CA2-02\",\"CA2-06\",\"CA1-10\",\"CA1-26\"]},{\"title\":\"Proposer des activités de loisirs aux enfants\",\"capacities\":[\"CA2-02\",\"CA2-06\"]}],\"capacitiesConsistency\":\"OK\",\"numberOfCapacitiesRule\":\"OK\"}"
  - role: user
    content: "\n        Veuillez qualifier, au format JSON, le métier \"Chargé de clientèle entreprises (H / F / NB)\". \n        Ce métier appartient aux codes ROME \n        C1203 (Relation clients banque/finance).\n        Il doit partager 85% de capacités communes avec d'autres métiers du même code ROME, comme : \"Coordonnateur des aides financières aux étudiants / Coordonnatrice des aides financières aux étudiants,Agent de prêts / Agente de prêts\".\n        Respectez scrupuleusement les règles et suivez bien le mode opératoire. \n        Conformez-vous à la RÈGLE D'OR. Assurez-vous de fournir, dans capacitiesList, entre 25 et 30 capacités distinctes, les capacités les plus essentielles et pertinentes pour ce métier\n\n "
  - role: assistant
    content: "{\"capacitiesList\":[\"CA3-13\",\"CA3-09\",\"CA3-03\",\"CA2-23\",\"CA2-21\",\"CA2-20\",\"CA2-19\",\"CA2-16\",\"CA2-15\",\"CA2-13\",\"CA2-12\",\"CA2-11\",\"CA2-10\",\"CA2-07\",\"CA2-06\",\"CA2-03\",\"CA2-02\",\"CA2-01\",\"CA1-28\",\"CA1-26\",\"CA1-18\",\"CA1-16\",\"CA1-15\",\"CA1-12\",\"CA1-11\",\"CA1-07\",\"CA1-06\",\"CA1-05\",\"CA1-04\"],\"activitiesDetail\":[{\"title\":\"Conseiller la clientèle entreprise sur des investissements et des placements\",\"capacities\":[\"CA2-02\",\"CA2-06\",\"CA1-15\"]},{\"title\":\"Réaliser des reportings et rapports pour rendre compte de l'activité et de ses résultats\",\"capacities\":[\"CA2-01\",\"CA2-21\",\"CA1-04\",\"CA1-12\",\"CA1-15\"]},{\"title\":\"Arbitrer des situations litigieuses\",\"capacities\":[\"CA2-15\",\"CA1-11\",\"CA1-26\"]},{\"title\":\"Démarcher / prospecter de nouveaux clients \",\"capacities\":[\"CA3-09\",\"CA2-06\",\"CA1-06\",\"CA1-07\"]},{\"title\":\"Vendre des produits et des services\",\"capacities\":[\"CA2-06\",\"CA2-12\",\"CA2-13\",\"CA1-06\",\"CA1-16\"]},{\"title\":\"Identifier des besoins clients\",\"capacities\":[\"CA3-13\",\"CA2-07\",\"CA2-10\",\"CA2-19\",\"CA1-12\"]},{\"title\":\"Atteindre des objectifs \",\"capacities\":[\"CA2-11\",\"CA1-05\",\"CA1-07\"]},{\"title\":\"Gérer un portefeuille clients\",\"capacities\":[\"CA2-16\",\"CA1-12\",\"CA1-18\"]},{\"title\":\"Apporter des réponses et des solutions aux besoins clients\",\"capacities\":[\"CA2-02\",\"CA2-06\",\"CA2-07\",\"CA1-06\"]},{\"title\":\"Négocier les conditions financières \",\"capacities\":[\"CA2-03\",\"CA2-06\",\"CA2-12\"]},{\"title\":\"Analyser son marché et ses concurrents \",\"capacities\":[\"CA2-10\",\"CA2-23\",\"CA1-12\",\"CA1-16\",\"CA1-28\"]},{\"title\":\"Mettre à jour la base CRM\",\"capacities\":[\"CA2-13\",\"CA2-20\",\"CA1-12\"]},{\"title\":\"Analyser un marché (économique, financier, etc.)\",\"capacities\":[\"CA2-13\",\"CA1-11\",\"CA1-12\",\"CA1-16\"]},{\"title\":\"Offrir des services de fidélisation\",\"capacities\":[\"CA3-03\",\"CA2-06\",\"CA1-06\"]}],\"capacitiesConsistency\":\"OK\",\"numberOfCapacitiesRule\":\"OK\"}"
  - role: user
    content: "\n        Veuillez qualifier, au format JSON, le métier \"Charpentier (H / F / NB)\". \n        Ce métier appartient aux codes ROME \n        F1503 (Réalisation - installation d'ossatures bois).\n        Il doit partager 85% de capacités communes avec des métiers professionnellement proche (même code ROME)\n        Respectez scrupuleusement les règles et suivez bien le mode opératoire. \n        Conformez-vous à la RÈGLE D'OR. Assurez-vous de fournir, dans capacitiesList, entre 25 et 30 capacités distinctes, les capacités les plus essentielles et pertinentes pour ce métier\n\n "
  - role: assistant
    content: "{\"capacitiesList\":[\"CA3-18\",\"CA3-15\",\"CA3-10\",\"CA3-08\",\"CA2-19\",\"CA2-18\",\"CA2-16\",\"CA2-15\",\"CA2-14\",\"CA2-13\",\"CA2-12\",\"CA2-09\",\"CA2-08\",\"CA1-30\",\"CA1-28\",\"CA1-27\",\"CA1-26\",\"CA1-21\",\"CA1-20\",\"CA1-19\",\"CA1-18\",\"CA1-13\",\"CA1-12\",\"CA1-11\",\"CA1-03\",\"CA1-02\",\"CA1-01\"],\"activitiesDetail\":[{\"title\":\"Transporter des matériaux de construction\",\"capacities\":[\"CA2-14\",\"CA1-19\",\"CA1-20\"]},{\"title\":\"Repérer et traiter des anomalies ou des dysfonctionnements\",\"capacities\":[\"CA3-15\",\"CA2-13\",\"CA1-13\"]},{\"title\":\"Assembler et fixer des éléments / matériaux entre eux \",\"capacities\":[\"CA1-19\",\"CA1-20\",\"CA1-26\"]},{\"title\":\"Installez des éléments en bois et en matériaux composites à base de bois\",\"capacities\":[\"CA3-15\",\"CA1-19\",\"CA1-20\",\"CA1-30\"]},{\"title\":\"Trier et évacuer des déchets \",\"capacities\":[\"CA1-11\",\"CA1-12\",\"CA1-19\"]},{\"title\":\"Créer des assemblages de bois\",\"capacities\":[\"CA3-08\",\"CA1-02\",\"CA1-13\",\"CA1-19\"]},{\"title\":\"Effectuer les finitions sur des matériaux en bois\",\"capacities\":[\"CA3-15\",\"CA1-02\",\"CA1-19\",\"CA1-20\"]},{\"title\":\"Utiliser divers instruments de mesure\",\"capacities\":[\"CA2-08\",\"CA2-09\",\"CA1-01\"]},{\"title\":\"Polir des surfaces irrégulières\",\"capacities\":[\"CA1-02\",\"CA1-19\",\"CA1-20\"]},{\"title\":\"Installer des profilés de construction\",\"capacities\":[\"CA1-19\",\"CA1-20\",\"CA1-21\"]},{\"title\":\"Tracer une épure \",\"capacities\":[\"CA3-10\",\"CA2-09\",\"CA2-12\",\"CA1-19\",\"CA1-27\",\"CA3-18\"]},{\"title\":\"Entretenir son matériel et ses outils\",\"capacities\":[\"CA2-19\",\"CA1-19\",\"CA1-28\"]},{\"title\":\"Appliquer des procédures en matière de santé et de sécurité\",\"capacities\":[\"CA2-09\",\"CA2-16\",\"CA2-18\",\"CA2-19\",\"CA1-18\"]},{\"title\":\"Assurer la traçabilité d'éléments en bois\",\"capacities\":[\"CA2-15\",\"CA2-16\",\"CA2-19\"]},{\"title\":\"Interpréter des plans en plusieurs dimensions\",\"capacities\":[\"CA3-10\",\"CA1-03\",\"CA1-11\",\"CA1-12\"]},{\"title\":\"Tracer une ligne au cordeau\",\"capacities\":[\"CA2-08\",\"CA2-09\",\"CA1-20\"]}],\"capacitiesConsistency\":\"OK\",\"numberOfCapacitiesRule\":\"OK\"}"
  - role: user
    content: "\n        Veuillez qualifier, au format JSON, le métier \"Commercial (H / F / NB)\". \n        Ce métier appartient aux codes ROME \n        D1407 (Relation technico-commerciale).\n        Il doit partager 85% de capacités communes avec d'autres métiers du même code ROME, comme : \"Conseiller commercial (H / F / NB),Ingénieur technico-commercial (H / F / NB)\".\n        Respectez scrupuleusement les règles et suivez bien le mode opératoire. \n        Conformez-vous à la RÈGLE D'OR. Assurez-vous de fournir, dans capacitiesList, entre 25 et 30 capacités distinctes, les capacités les plus essentielles et pertinentes pour ce métier\n\n "
  - role: assistant
    content: "{\"capacitiesList\":[\"CA3-15\",\"CA3-14\",\"CA3-13\",\"CA3-09\",\"CA3-07\",\"CA3-05\",\"CA2-23\",\"CA2-20\",\"CA2-19\",\"CA2-16\",\"CA2-13\",\"CA2-12\",\"CA2-11\",\"CA2-10\",\"CA2-07\",\"CA2-06\",\"CA2-05\",\"CA2-03\",\"CA2-02\",\"CA2-01\",\"CA1-28\",\"CA1-18\",\"CA1-16\",\"CA1-15\",\"CA1-12\",\"CA1-11\",\"CA1-07\",\"CA1-06\",\"CA1-05\",\"CA1-04\"],\"activitiesDetail\":[{\"title\":\"Entretenir une relation de confiance avec les clients\",\"capacities\":[\"CA2-02\",\"CA1-05\",\"CA1-06\",\"CA1-28\"]},{\"title\":\"Appliquer des techniques de vente\",\"capacities\":[\"CA3-07\",\"CA3-15\",\"CA2-02\",\"CA1-05\"]},{\"title\":\"Démarcher / prospecter de nouveaux clients \",\"capacities\":[\"CA3-09\",\"CA2-06\",\"CA1-06\",\"CA1-07\"]},{\"title\":\"Faire des démonstrations sur l'utilisation des produits\",\"capacities\":[\"CA3-14\",\"CA2-05\",\"CA1-15\"]},{\"title\":\"Faire de la veille concurrentielle \",\"capacities\":[\"CA2-10\",\"CA2-23\",\"CA1-12\",\"CA1-16\",\"CA1-28\"]},{\"title\":\"S'informer sur son planning\",\"capacities\":[\"CA2-13\",\"CA1-12\",\"CA1-16\"]},{\"title\":\"Atteindre des objectifs de vente\",\"capacities\":[\"CA2-11\",\"CA1-05\",\"CA1-07\"]},{\"title\":\"Organiser sa prospection terrain\",\"capacities\":[\"CA2-16\",\"CA2-19\",\"CA1-18\"]},{\"title\":\"Finaliser une vente\",\"capacities\":[\"CA3-05\",\"CA2-03\"]},{\"title\":\"Présenter un produit ou un service aux clients de manière convaincante\",\"capacities\":[\"CA3-09\",\"CA2-03\",\"CA1-05\"]},{\"title\":\"Conseiller les clients sur les offres et solutions entreprises \",\"capacities\":[\"CA2-01\",\"CA2-02\",\"CA2-06\",\"CA2-07\"]},{\"title\":\"Négocier des contrats de vente\",\"capacities\":[\"CA2-03\",\"CA2-06\",\"CA2-12\"]},{\"title\":\"Analyser des besoins clients\",\"capacities\":[\"CA3-13\",\"CA2-07\",\"CA2-10\",\"CA2-19\",\"CA1-12\"]},{\"title\":\"Réaliser des actions de suivi clients\",\"capacities\":[\"CA2-02\",\"CA1-06\",\"CA1-11\"]},{\"title\":\"Réaliser des propositions commerciales\",\"capacities\":[\"CA2-06\",\"CA2-12\",\"CA2-20\",\"CA1-04\"]}],\"capacitiesConsistency\":\"OK\",\"numberOfCapacitiesRule\":\"OK\"}"
  - role: user
    content: "\n        Veuillez qualifier, au format JSON, le métier \"Développeur web (H / F / NB)\". \n        Ce métier appartient aux codes ROME \n        M1805 (Études et développement informatique).\n        Il doit partager 85% de capacités communes avec d'autres métiers du même code ROME, comme : \"Chef de projet informatique (H / F / NB),Développeur de logiciels (H / F / NB)\".\n        Respectez scrupuleusement les règles et suivez bien le mode opératoire. \n        Conformez-vous à la RÈGLE D'OR. Assurez-vous de fournir, dans capacitiesList, entre 25 et 30 capacités distinctes, les capacités les plus essentielles et pertinentes pour ce métier\n\n "
  - role: assistant
    content: "{\"capacitiesList\":[\"CA3-18\",\"CA3-17\",\"CA3-15\",\"CA3-14\",\"CA3-13\",\"CA3-04\",\"CA3-03\",\"CA2-23\",\"CA2-21\",\"CA2-20\",\"CA2-19\",\"CA2-16\",\"CA2-13\",\"CA2-11\",\"CA2-10\",\"CA2-09\",\"CA2-07\",\"CA2-03\",\"CA2-02\",\"CA1-28\",\"CA1-27\",\"CA1-18\",\"CA1-16\",\"CA1-15\",\"CA1-13\",\"CA1-12\",\"CA1-11\",\"CA1-06\",\"CA1-04\",\"CA1-03\"],\"activitiesDetail\":[{\"title\":\"Repérer et traiter des anomalies ou des dysfonctionnements\",\"capacities\":[\"CA3-15\",\"CA2-13\",\"CA1-13\"]},{\"title\":\"Créer un organigramme de programmation\",\"capacities\":[\"CA2-20\",\"CA1-13\",\"CA1-27\"]},{\"title\":\"Former les clients a l'utilisation du site web ou de l'application \",\"capacities\":[\"CA3-14\",\"CA2-21\",\"CA1-15\",\"CA1-27\"]},{\"title\":\"Suivre un cahier des charges\",\"capacities\":[\"CA2-10\",\"CA2-16\",\"CA1-18\",\"CA1-28\"]},{\"title\":\"Identifier des besoins clients\",\"capacities\":[\"CA3-13\",\"CA2-07\",\"CA2-10\",\"CA2-19\",\"CA1-12\"]},{\"title\":\"Corriger des problèmes d'utilisation ou d'ergonomie signalés par le client  \",\"capacities\":[\"CA3-03\",\"CA3-04\",\"CA2-03\"]},{\"title\":\"Analyser la satisfaction des utilisateurs et des clients \",\"capacities\":[\"CA2-10\",\"CA1-03\",\"CA1-11\",\"CA1-12\"]},{\"title\":\"Créer la maquette fonctionnelle d’un site web\",\"capacities\":[\"CA1-13\",\"CA3-17\",\"CA3-18\"]},{\"title\":\"Developper le fonctionnement front office d'un site web ou d'une application \",\"capacities\":[\"CA2-21\",\"CA3-17\"]},{\"title\":\"Rédiger des documents techniques\",\"capacities\":[\"CA3-14\",\"CA2-20\",\"CA1-04\",\"CA1-15\"]},{\"title\":\"Réaliser une veille technique et réglementaire\",\"capacities\":[\"CA2-10\",\"CA2-23\",\"CA1-12\",\"CA1-16\",\"CA1-28\"]},{\"title\":\"Réaliser des tests avant et pendant la mise en production \",\"capacities\":[\"CA2-09\",\"CA2-11\",\"CA2-13\",\"CA1-28\"]},{\"title\":\"Réaliser des actions de suivi clients\",\"capacities\":[\"CA2-02\",\"CA1-06\",\"CA1-11\"]}],\"capacitiesConsistency\":\"OK\",\"numberOfCapacitiesRule\":\"OK\"}"
  - role: user
    content: "\n        Veuillez qualifier, au format JSON, le métier \"Formateur pour adulte (H / F / NB)\". \n        Ce métier appartient aux codes ROME \n        K2111 (Formation professionnelle).\n        Il doit partager 85% de capacités communes avec d'autres métiers du même code ROME, comme : \"Concepteur pédagogique (H / F / NB),Formateur en entreprise / Formatrice en entreprise\".\n        Respectez scrupuleusement les règles et suivez bien le mode opératoire. \n        Conformez-vous à la RÈGLE D'OR. Assurez-vous de fournir, dans capacitiesList, entre 25 et 30 capacités distinctes, les capacités les plus essentielles et pertinentes pour ce métier\n\n "
  - role: assistant
    content: "{\"capacitiesList\":[\"CA3-15\",\"CA3-14\",\"CA3-13\",\"CA3-11\",\"CA3-07\",\"CA2-21\",\"CA2-20\",\"CA2-19\",\"CA2-18\",\"CA2-17\",\"CA2-15\",\"CA2-11\",\"CA2-09\",\"CA2-06\",\"CA2-05\",\"CA2-03\",\"CA2-02\",\"CA2-01\",\"CA1-28\",\"CA1-26\",\"CA1-16\",\"CA1-15\",\"CA1-13\",\"CA1-11\",\"CA1-10\",\"CA1-06\",\"CA1-05\",\"CA1-04\"],\"activitiesDetail\":[{\"title\":\"Utiliser du matériel pédagogique adapté \",\"capacities\":[\"CA3-11\",\"CA2-02\",\"CA2-17\",\"CA2-19\"]},{\"title\":\"Encourager les élèves à progresser\",\"capacities\":[\"CA2-01\",\"CA2-02\",\"CA2-06\",\"CA1-06\",\"CA1-26\"]},{\"title\":\"Evaluer des élèves ou des étudiants\",\"capacities\":[\"CA2-09\",\"CA2-11\"]},{\"title\":\"Surveiller les progrès et les difficultés des élèves\",\"capacities\":[\"CA3-13\",\"CA2-09\",\"CA2-15\"]},{\"title\":\"Préparer le contenu des séances\",\"capacities\":[\"CA2-17\",\"CA2-20\",\"CA2-21\",\"CA1-04\",\"CA1-13\"]},{\"title\":\"Transmettre des connaissances à un public adulte \",\"capacities\":[\"CA3-14\",\"CA3-15\",\"CA2-05\",\"CA1-04\",\"CA1-05\",\"CA1-15\"]},{\"title\":\"S'informer des nouvelles pratiques de son métier\",\"capacities\":[\"CA3-11\",\"CA1-16\"]},{\"title\":\"Gérer un groupe dans une classe\",\"capacities\":[\"CA3-07\",\"CA2-01\",\"CA2-02\",\"CA2-15\",\"CA2-18\"]},{\"title\":\"Assurer la sécurité des élèves\",\"capacities\":[\"CA2-01\",\"CA2-17\",\"CA2-18\"]},{\"title\":\"Proposer des séances sur mesure\",\"capacities\":[\"CA3-13\",\"CA2-02\",\"CA2-15\",\"CA1-26\",\"CA1-28\"]},{\"title\":\"Donner et montrer des exemples concrets\",\"capacities\":[\"CA3-15\",\"CA2-01\",\"CA2-03\",\"CA1-04\",\"CA1-05\",\"CA1-15\",\"CA1-26\"]},{\"title\":\"Aider des étudiants dans leur apprentissage\",\"capacities\":[\"CA3-13\",\"CA3-14\",\"CA2-02\",\"CA1-11\"]},{\"title\":\"Assurer de bonnes relations avec les élèves\",\"capacities\":[\"CA2-01\",\"CA2-02\",\"CA1-05\",\"CA1-06\"]},{\"title\":\"Partager un feedback constructif \",\"capacities\":[\"CA2-01\",\"CA2-02\",\"CA2-11\",\"CA1-10\"]},{\"title\":\"Adapter l'enseignement aux différents publics\",\"capacities\":[\"CA3-13\",\"CA2-15\",\"CA1-26\",\"CA1-28\"]}],\"capacitiesConsistency\":\"OK\",\"numberOfCapacitiesRule\":\"OK\"}"
  - role: user
    content: "\n        Veuillez qualifier, au format JSON, le métier \"Jardinier paysagiste (H / F / NB)\". \n        Ce métier appartient aux codes ROME \n        A1203 (Aménagement et entretien des espaces verts).\n        Il doit partager 85% de capacités communes avec d'autres métiers du même code ROME, comme : \"Agent d'entretien des espaces verts (H / F / NB),Aide jardinier / Aide jardinière\".\n        Respectez scrupuleusement les règles et suivez bien le mode opératoire. \n        Conformez-vous à la RÈGLE D'OR. Assurez-vous de fournir, dans capacitiesList, entre 25 et 30 capacités distinctes, les capacités les plus essentielles et pertinentes pour ce métier\n\n "
  - role: assistant
    content: "{\"capacitiesList\":[\"CA3-16\",\"CA3-15\",\"CA3-10\",\"CA2-19\",\"CA2-15\",\"CA2-14\",\"CA2-12\",\"CA2-09\",\"CA2-08\",\"CA2-04\",\"CA1-32\",\"CA1-31\",\"CA1-30\",\"CA1-29\",\"CA1-28\",\"CA1-26\",\"CA1-22\",\"CA1-21\",\"CA1-20\",\"CA1-19\",\"CA1-18\",\"CA1-17\",\"CA1-14\",\"CA1-02\",\"CA1-01\"],\"activitiesDetail\":[{\"title\":\"Tailler des végétaux\",\"capacities\":[\"CA1-19\",\"CA1-20\",\"CA1-21\"]},{\"title\":\"Installer des clôtures\",\"capacities\":[\"CA2-15\",\"CA1-14\",\"CA1-19\",\"CA1-20\",\"CA1-22\"]},{\"title\":\" Entretenir des espaces verts, des plantes et des espaces extérieurs\",\"capacities\":[\"CA2-04\",\"CA1-19\",\"CA1-20\",\"CA1-31\"]},{\"title\":\"Installer des panneaux de signalisation\",\"capacities\":[\"CA2-14\",\"CA2-15\",\"CA1-19\",\"CA1-20\"]},{\"title\":\"Traiter les cultures et les plantes contre les maladies et les parasites\",\"capacities\":[\"CA2-15\",\"CA1-26\",\"CA1-28\"]},{\"title\":\"Soigner des végétaux\",\"capacities\":[\"CA3-15\",\"CA2-08\",\"CA1-02\",\"CA1-17\",\"CA1-20\"]},{\"title\":\"Préparer un terrain / une surface pour une plantation\",\"capacities\":[\"CA2-08\",\"CA2-19\",\"CA1-18\",\"CA1-20\",\"CA1-22\",\"CA1-29\"]},{\"title\":\"Manipuler des produits phytosanitaires et des engrais\",\"capacities\":[\"CA2-08\",\"CA2-09\",\"CA2-12\",\"CA1-32\"]},{\"title\":\"Effectuer des travaux de maçonnerie légère\",\"capacities\":[\"CA2-04\",\"CA1-19\",\"CA1-20\"]},{\"title\":\"Assurer l'entretien des engins, des machines\",\"capacities\":[\"CA2-19\",\"CA1-19\",\"CA1-20\"]},{\"title\":\"Lire un schéma, un plan\",\"capacities\":[\"CA3-10\",\"CA2-09\"]},{\"title\":\"Réaliser des plantations \",\"capacities\":[\"CA3-16\",\"CA2-15\",\"CA1-19\",\"CA1-20\",\"CA1-29\"]},{\"title\":\"Entretenir son matériel et ses outils\",\"capacities\":[\"CA2-19\",\"CA1-19\",\"CA1-28\"]},{\"title\":\"Entretenir des espaces extérieurs\",\"capacities\":[\"CA1-01\",\"CA1-19\",\"CA1-20\",\"CA1-22\"]},{\"title\":\"Transporter les équipements et matériaux dans une zone de travaux\",\"capacities\":[\"CA2-14\",\"CA1-20\",\"CA1-21\",\"CA1-29\",\"CA1-30\"]},{\"title\":\"Entretenir des surfaces \",\"capacities\":[\"CA2-15\",\"CA1-19\",\"CA1-20\"]}],\"capacitiesConsistency\":\"OK\",\"numberOfCapacitiesRule\":\"OK\"}"
  - role: user
    content: "\n        Veuillez qualifier, au format JSON, le métier \"Médiateur (H / F / NB)\". \n        Ce métier appartient aux codes ROME \n        K1902 (Collaboration juridique).\n        Il doit partager 85% de capacités communes avec d'autres métiers du même code ROME, comme : \"Assistant de cabinet juridique / Assistante de cabinet juridique,Auxiliaire juridique\".\n        Respectez scrupuleusement les règles et suivez bien le mode opératoire. \n        Conformez-vous à la RÈGLE D'OR. Assurez-vous de fournir, dans capacitiesList, entre 25 et 30 capacités distinctes, les capacités les plus essentielles et pertinentes pour ce métier\n\n "
  - role: assistant
    content: "{\"capacitiesList\":[\"CA3-13\",\"CA3-11\",\"CA3-07\",\"CA3-05\",\"CA3-04\",\"CA3-03\",\"CA2-19\",\"CA2-18\",\"CA2-17\",\"CA2-15\",\"CA2-11\",\"CA2-10\",\"CA2-07\",\"CA2-06\",\"CA2-05\",\"CA2-03\",\"CA2-02\",\"CA2-01\",\"CA1-28\",\"CA1-26\",\"CA1-15\",\"CA1-13\",\"CA1-12\",\"CA1-11\",\"CA1-10\",\"CA1-09\",\"CA1-06\",\"CA1-05\"],\"activitiesDetail\":[{\"title\":\"Répondre à des demandes de renseignements\",\"capacities\":[\"CA2-01\",\"CA1-15\",\"CA1-26\"]},{\"title\":\"Remonter les informations du terrain pour améliorer la qualité de l'offre \",\"capacities\":[\"CA3-04\",\"CA2-01\",\"CA2-06\",\"CA2-11\"]},{\"title\":\"Gérer les questions d’éthique au sein d'un service\",\"capacities\":[\"CA2-15\",\"CA2-17\",\"CA2-18\"]},{\"title\":\"Proposer des solutions d’amélioration\",\"capacities\":[\"CA3-03\",\"CA3-04\",\"CA2-03\"]},{\"title\":\"Agir en médiateur lors de négociations\",\"capacities\":[\"CA2-01\",\"CA2-02\",\"CA1-09\",\"CA1-11\",\"CA1-28\"]},{\"title\":\"Remercier et raccompagner les clients au moment de leur départ\",\"capacities\":[\"CA2-02\",\"CA2-07\",\"CA2-10\",\"CA1-06\"]},{\"title\":\"Accueillir et installer les clients\",\"capacities\":[\"CA2-02\",\"CA2-05\",\"CA1-05\",\"CA1-06\"]},{\"title\":\"Se soutenir entre pairs\",\"capacities\":[\"CA3-11\",\"CA2-07\",\"CA1-05\",\"CA1-06\",\"CA1-11\",\"CA1-26\"]},{\"title\":\"Conclure un accord officiel\",\"capacities\":[\"CA3-05\",\"CA2-03\",\"CA1-13\",\"CA1-15\"]},{\"title\":\"Entretenir de bonnes relations avec les clients\",\"capacities\":[\"CA3-13\",\"CA2-10\",\"CA1-10\"]},{\"title\":\"Analyser des besoins clients\",\"capacities\":[\"CA3-13\",\"CA2-07\",\"CA2-10\",\"CA2-19\",\"CA1-12\"]},{\"title\":\"Gérer des situations conflictuelles \",\"capacities\":[\"CA3-07\",\"CA2-02\",\"CA1-05\",\"CA1-06\"]}],\"capacitiesConsistency\":\"OK\",\"numberOfCapacitiesRule\":\"OK\"}"

computePromptAdjust: "recommence pour LA TROISIÈME FOIS sans faire AUCUN commentaire. Ta réponse sera traitée par un système tombant en erreur si tu ne produits pas du PURE JSON. Si ta réponse contient plusieurs qualifications, CHOISIS LA PREMIERE et fournis là au format JSON :"

parameterizedLastPromptMessageWithRomeAndOccupationAssociated: "Veuillez qualifier, au format JSON, le métier \"%s\". Ce métier appartient aux codes ROME %s. Il doit partager 85%% de capacités communes avec d'autres métiers du même code ROME, comme : %s. Respectez scrupuleusement les règles et suivez bien le mode opératoire. Conformez-vous à la RÈGLE D'OR. Assurez-vous de fournir, dans capacitiesList, entre 25 et 30 capacités distinctes, les capacités les plus essentielles et pertinentes pour ce métier"
parameterizedLastPromptMessageWithRomeAndNoOccupationAssociated: "Veuillez qualifier, au format JSON, le métier \"%s\". Ce métier appartient aux codes ROME %s. Respectez scrupuleusement les règles et suivez bien le mode opératoire. Conformez-vous à la RÈGLE D'OR. Assurez-vous de fournir, dans capacitiesList, entre 25 et 30 capacités distinctes, les capacités les plus essentielles et pertinentes pour ce métier"
parameterizedLastPromptMessageWithoutRome: "Veuillez qualifier, au format JSON, le métier : %s. Respectez scrupuleusement les règles et suivez bien le mode opératoire. Conformez-vous à la RÈGLE D'OR. Assurez-vous de fournir, dans capacitiesList, entre 25 et 30 capacités distinctes, les capacités les plus essentielles et pertinentes pour ce métier"

