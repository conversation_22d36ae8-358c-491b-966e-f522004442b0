<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="LOGS_PATH" value="./logs" />
    <property name="LOG_FILE_NAME" value="app" />

    <include resource="org/springframework/boot/logging/logback/base.xml"/>
    <springProperty scope="context" name="ACTIVE_PROFILE" source="spring.profiles.active"/>

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <!-- <appender-ref ref="FILE_JSON"/> -->
    </root>

    <appender name="FILE_JSON" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOGS_PATH}/${LOG_FILE_NAME}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOGS_PATH}/archived/${LOG_FILE_NAME}-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>3</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <customFields>{"app_name":"API","env":"${ACTIVE_PROFILE}"}</customFields>
            <includeMdc>true</includeMdc>
            <includeCallerData>true</includeCallerData>
            <fieldNames>
                <timestamp>timestamp</timestamp>
                <version>[ignore]</version>
            </fieldNames>
            <shortenedLoggerNameLength>36</shortenedLoggerNameLength>
            <includeContext>true</includeContext>
        </encoder>
    </appender>
</configuration>
