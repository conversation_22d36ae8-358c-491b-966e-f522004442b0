FROM eclipse-temurin:21-alpine as jre-build

WORKDIR /usr/local/odas/api/target

COPY ./target/erhgo-api.jar ./

RUN jlink --verbose \
--compress 2 \
--strip-java-debug-attributes \
--no-header-files \
--no-man-pages \
--output ./jre \
--add-modules java.base,java.desktop,java.instrument,java.management.rmi,java.naming,java.prefs,java.scripting,java.security.jgss,java.sql.rowset,jdk.httpserver,jdk.unsupported,jdk.crypto.ec,jdk.localedata,java.net.http
# Les modules sont trouvables via l'outil jdeps, ils correspondent aux dépendances java de spring boot
# cf. https://linuxtut.com/how-to-make-spring-boot-docker-image-smaller-51713/

FROM alpine:latest

COPY --from=jre-build /usr/local/odas/api/target/jre ./jre

COPY --from=jre-build /usr/local/odas/api/target/erhgo-api.jar ./

# Expose volumes and port
EXPOSE 8080

ENTRYPOINT echo "Starting jar at $(date '+%d/%m/%Y %H:%M:%S')" && exec jre/bin/java -Djava.security.egd=file:/dev/./urandom $JAVA_OPTS -jar erhgo-api.jar

ARG VERSION
ENV VERSION ${VERSION}
