<!--<?xml version="1.0" encoding="UTF-8"?>-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.3</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <artifactId>erhgo.platform.back.api</artifactId>
    <name>ERHGO platform backend API</name>
    <packaging>jar</packaging>

    <properties>
        <java.version>21</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>

        <!-- Dependency versions -->
        <keycloak.version>26.0.2</keycloak.version>
        <jackson-jsog.version>1.1.2</jackson-jsog.version>
        <lombok.version>1.18.36</lombok.version>
        <logbook-spring-boot-starter.version>3.9.0</logbook-spring-boot-starter.version>
        <!-- Test dependencies versions -->
        <javafaker.version>1.0.2</javafaker.version>

        <swagger-annotations.version>2.2.22</swagger-annotations.version>
        <springfox-version>3.0.0</springfox-version>

        <algolia.version>3.16.9</algolia.version>
        <sendinblue.version>7.0.0</sendinblue.version>


        <!-- Quick fix for problems with 3.8 version of maven plugin -->
        <guava.version>33.3.0-jre</guava.version>
        <logstash-logback-encoder.version>8.0</logstash-logback-encoder.version>
        <modelmapper.version>3.2.1</modelmapper.version>
        <shedlock.version>5.14.0</shedlock.version>
        <!-- faux-pas : must match version in https://mvnrepository.com/artifact/org.zalando/logbook-parent/ ; required to correct org.apiguardian:apiguardian-api version handling -->
        <faux-pas.version>0.9.0</faux-pas.version>
        <jacoco-maven-plugin.version>0.8.13</jacoco-maven-plugin.version>
        <openhtml.version>1.1.26</openhtml.version>
        <opencsv.version>5.9</opencsv.version>
        <openai.version>0.18.2</openai.version>
        <jsoup.version>1.18.1</jsoup.version>
        <commons-io.version>2.16.1</commons-io.version>
        <slack-api-client.version>1.40.3</slack-api-client.version>
        <firebase-admin.version>9.3.0</firebase-admin.version>
        <google-http-client.version>1.44.2</google-http-client.version>
        <spring-ai.version>1.0.0</spring-ai.version>
        <google-auth.version>1.37.1</google-auth.version>
        <okhttp.version>5.1.0</okhttp.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp-bom</artifactId>
                <version>${okhttp.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>${spring-ai.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.checkerframework</groupId>
                <artifactId>checker-qual</artifactId>
                <version>3.49.5</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>4.31.1</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>33.4.8-jre</version>
            </dependency>
            <dependency>
                <groupId>com.google.api-client</groupId>
                <artifactId>google-api-client</artifactId>
                <version>2.8.0</version>
            </dependency>
            <dependency>
                <groupId>com.google.api.grpc</groupId>
                <artifactId>proto-google-iam-v1</artifactId>
                <version>1.54.1</version>
            </dependency>
            <dependency>
                <groupId>com.google.api.grpc</groupId>
                <artifactId>proto-google-common-protos</artifactId>
                <version>2.59.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>3.0.5</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-context</artifactId>
                <version>1.73.0</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-grpclb</artifactId>
                <version>1.73.0</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-stub</artifactId>
                <version>1.73.0</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-xds</artifactId>
                <version>1.73.0</version>
            </dependency>
            <dependency>
                <groupId>com.google.errorprone</groupId>
                <artifactId>error_prone_annotations</artifactId>
                <version>2.40.0</version>
            </dependency>
            <dependency>
                <groupId>com.google.auto.value</groupId>
                <artifactId>auto-value-annotations</artifactId>
                <version>1.11.0</version>
            </dependency>
            <dependency>
                <groupId>com.google.api</groupId>
                <artifactId>gax</artifactId>
                <version>2.68.1</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.20.0</version>
            </dependency>
            <dependency>
                <groupId>com.google.api</groupId>
                <artifactId>gax-grpc</artifactId>
                <version>2.68.1</version>
            </dependency>
            <dependency>
                <groupId>com.google.api</groupId>
                <artifactId>gax-httpjson</artifactId>
                <version>2.68.1</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio</artifactId>
                <version>3.15.0</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>2.2.34</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-googleapis</artifactId>
                <version>1.73.0</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-inprocess</artifactId>
                <version>1.73.0</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>2.0.0-rc2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>1.13.1</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.findbugs</groupId>
                <artifactId>jsr305</artifactId>
                <version>3.0.2</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.plexus</groupId>
                <artifactId>plexus-utils</artifactId>
                <version>4.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java-util</artifactId>
                <version>4.31.1</version>
            </dependency>
            <dependency>
                <groupId>com.google.http-client</groupId>
                <artifactId>google-http-client-apache-v2</artifactId>
                <version>1.47.1</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>animal-sniffer-annotations</artifactId>
                <version>1.24</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>2.4</version>
            </dependency>
            <dependency>
                <groupId>com.google.api</groupId>
                <artifactId>api-common</artifactId>
                <version>2.51.1</version>
            </dependency>
            <dependency>
                <groupId>com.google.auth</groupId>
                <artifactId>google-auth-library-oauth2-http</artifactId>
                <version>${google-auth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.auth</groupId>
                <artifactId>google-auth-library-credentials</artifactId>
                <version>${google-auth.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-util</artifactId>
                <version>1.73.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.14</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-alts</artifactId>
                <version>1.73.0</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-core</artifactId>
                <version>1.73.0</version>
            </dependency>
            <dependency>
                <groupId>org.threeten</groupId>
                <artifactId>threetenbp</artifactId>
                <version>1.7.1</version>
            </dependency>
            <dependency>
                <groupId>com.github.victools</groupId>
                <artifactId>jsonschema-module-jackson</artifactId>
                <version>4.38.0</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-api</artifactId>
                <version>1.73.0</version>
            </dependency>
            <dependency>
                <groupId>org.antlr</groupId>
                <artifactId>antlr4-runtime</artifactId>
                <version>4.13.2</version>
            </dependency>
            <dependency>
                <groupId>com.github.victools</groupId>
                <artifactId>jsonschema-generator</artifactId>
                <version>4.38.0</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-auth</artifactId>
                <version>1.73.0</version>
            </dependency>
            <dependency>
                <groupId>com.google.http-client</groupId>
                <artifactId>google-http-client-gson</artifactId>
                <version>1.47.1</version> <!-- Forced to 1.45.0 -->
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-netty-shaded</artifactId>
                <version>1.73.0</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-services</artifactId>
                <version>1.73.0</version>
            </dependency>
            <dependency>
                <groupId>org.apiguardian</groupId>
                <artifactId>apiguardian-api</artifactId>
                <version>1.1.2</version>
            </dependency>
            <dependency>
                <groupId>com.google.http-client</groupId>
                <artifactId>google-http-client</artifactId>
                <version>1.47.1</version>
            </dependency>
            <dependency>
                <groupId>com.google.j2objc</groupId>
                <artifactId>j2objc-annotations</artifactId>
                <version>3.0.0</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-protobuf</artifactId>
                <version>1.73.0</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.google.oauth-client</groupId>
                <artifactId>google-oauth-client</artifactId>
                <version>1.36.0</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>failureaccess</artifactId>
                <version>1.0.3</version>
            </dependency>
            <dependency>
                <groupId>com.google.re2j</groupId>
                <artifactId>re2j</artifactId>
                <version>1.7</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <!---  Dépendances transitives -->
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-protobuf</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.j2objc</groupId>
            <artifactId>j2objc-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>org.checkerframework</groupId>
            <artifactId>checker-qual</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.api-client</groupId>
            <artifactId>google-api-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.api.grpc</groupId>
            <artifactId>proto-google-iam-v1</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.api.grpc</groupId>
            <artifactId>proto-google-common-protos</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-context</artifactId>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-grpclb</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.grpc</groupId>
                    <artifactId>grpc-protobuf-lite</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-stub</artifactId>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-xds</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.errorprone</groupId>
            <artifactId>error_prone_annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.auto.value</groupId>
            <artifactId>auto-value-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.api</groupId>
            <artifactId>gax</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.api</groupId>
            <artifactId>gax-grpc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.api</groupId>
            <artifactId>gax-httpjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okio</groupId>
            <artifactId>okio</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-googleapis</artifactId>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-inprocess</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
        </dependency>
        <dependency>
            <groupId>org.codehaus.plexus</groupId>
            <artifactId>plexus-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.http-client</groupId>
            <artifactId>google-http-client-apache-v2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>animal-sniffer-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.api</groupId>
            <artifactId>api-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.auth</groupId>
            <artifactId>google-auth-library-oauth2-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.auth</groupId>
            <artifactId>google-auth-library-credentials</artifactId>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-util</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-alts</artifactId>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.threeten</groupId>
            <artifactId>threetenbp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.victools</groupId>
            <artifactId>jsonschema-module-jackson</artifactId>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.antlr</groupId>
            <artifactId>antlr4-runtime</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.victools</groupId>
            <artifactId>jsonschema-generator</artifactId>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-auth</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.http-client</groupId>
            <artifactId>google-http-client-gson</artifactId>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-netty-shaded</artifactId>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-services</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apiguardian</groupId>
            <artifactId>apiguardian-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.http-client</groupId>
            <artifactId>google-http-client</artifactId>
        </dependency>
        <!---  /Dépendances transitives -->


        <!--Spring-boot dependencies-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openhtmltopdf</groupId>
            <artifactId>openhtmltopdf-core</artifactId>
            <version>${openhtml.version}</version>
        </dependency>
        <dependency>
            <groupId>io.github.openhtmltopdf</groupId>
            <artifactId>openhtmltopdf-slf4j</artifactId>
            <version>${openhtml.version}</version>
        </dependency>
        <dependency>
            <!-- Required for PDF output. -->
            <groupId>io.github.openhtmltopdf</groupId>
            <artifactId>openhtmltopdf-pdfbox</artifactId>
            <version>${openhtml.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-core</artifactId>
            <version>9.2.0</version>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>${jsoup.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.github.stephenc.jcip</groupId>
                    <artifactId>jcip-annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-oauth2-jose</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.github.stephenc.jcip</groupId>
                    <artifactId>jcip-annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- adjust spring-boot-starter-oauth2-client dependency -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-sftp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-openai</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-model</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-autoconfigure-model-chat-client</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-model-openai</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mariadb.jdbc</groupId>
            <artifactId>mariadb-java-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>jcl-over-slf4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>testcontainers</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- jna: Required by testcontainers -->
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
            <version>5.17.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>mariadb</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>${logstash-logback-encoder.version}</version>
        </dependency>

        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-admin-client</artifactId>
            <version>${keycloak.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <!-- OpenCSV for Data Initialization -->
        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>${opencsv.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--Keycloak dependencies-->
        <dependency>
            <groupId>org.zalando</groupId>
            <artifactId>logbook-spring-boot-starter</artifactId>
            <version>${logbook-spring-boot-starter.version}</version>
        </dependency>

        <dependency>
            <groupId>org.zalando</groupId>
            <artifactId>faux-pas</artifactId>
            <version>${faux-pas.version}</version>
        </dependency>

        <!--Lombok dependency-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>

        <dependency>
            <groupId>com.sendinblue</groupId>
            <artifactId>sib-api-v3-sdk</artifactId>
            <version>${sendinblue.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.voodoodyne.jackson.jsog</groupId>
            <artifactId>jackson-jsog</artifactId>
            <version>${jackson-jsog.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-yaml</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
        </dependency>

        <!--Metrics dependencies-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!--Test dependencies-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.github.javafaker</groupId>
            <artifactId>javafaker</artifactId>
            <version>${javafaker.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.modelmapper</groupId>
            <artifactId>modelmapper</artifactId>
            <version>${modelmapper.version}</version>
        </dependency>
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- OpenAPI -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>${springfox-version}</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>${springfox-version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <!-- Algolia -->
        <dependency>
            <groupId>com.algolia</groupId>
            <artifactId>algoliasearch-core</artifactId>
            <version>${algolia.version}</version>
        </dependency>
        <dependency>
            <groupId>com.algolia</groupId>
            <artifactId>algoliasearch-apache</artifactId>
            <version>${algolia.version}</version>
        </dependency>
        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-spring</artifactId>
            <version>${shedlock.version}</version>
        </dependency>
        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-provider-jdbc-template</artifactId>
            <version>${shedlock.version}</version>
        </dependency>
        <dependency>
            <groupId>com.hazelcast</groupId>
            <artifactId>hazelcast-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hazelcast</groupId>
            <artifactId>hazelcast-hibernate53</artifactId>
            <version>5.2.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.code.findbugs</groupId>
                    <artifactId>annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.slack.api</groupId>
            <artifactId>slack-api-client</artifactId>
            <version>${slack-api-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.firebase</groupId>
            <artifactId>firebase-admin</artifactId>
            <version>${firebase-admin.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.grpc</groupId>
                    <artifactId>grpc-protobuf-lite</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.google.apis</groupId>
            <artifactId>google-api-services-gmail</artifactId>
            <version>v1-rev20250630-2.0.0</version>
        </dependency>
    </dependencies>
    <build>
        <finalName>erhgo-api</finalName>
        <defaultGoal>spring-boot:run</defaultGoal>
        <testResources>
            <testResource>
                <directory>src/test/resources/</directory>
            </testResource>
            <testResource>
                <directory>src/test/features</directory>
            </testResource>
        </testResources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.14.0</version>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>exec-maven-plugin</artifactId>
                <groupId>org.codehaus.mojo</groupId>
                <version>3.5.1</version>
                <executions>
                    <execution>
                        <id>Generate api.yaml</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <executable>${basedir}/../buildOpenApi.sh</executable>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.6.1</version>
                <executions>
                    <execution>
                        <id>enforce</id>
                        <configuration>
                            <rules>
                                <dependencyConvergence/>
                            </rules>
                        </configuration>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>enforce-ban-duplicate-classes</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <banDuplicateClasses>
                                    <scopes>
                                        <scope>compile</scope>
                                        <scope>provided</scope>
                                    </scopes>
                                    <findAllDuplicates>true</findAllDuplicates>
                                    <ignoreWhenIdentical>true</ignoreWhenIdentical>
                                </banDuplicateClasses>
                                <requireMavenVersion>
                                    <version>3.0</version>
                                </requireMavenVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>extra-enforcer-rules</artifactId>
                        <version>1.10.0</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-test</id>
                        <configuration>
                            <argLine>
                                @{argLine} --illegal-access=deny
                            </argLine>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <version>7.14.0</version>
                <executions>
                    <execution>
                        <id>erhgo-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/../common/generated/api.yaml</inputSpec>
                            <generatorName>spring</generatorName>
                            <generateApiTests>false</generateApiTests>
                            <apiPackage>com.erhgo.openapi.controller</apiPackage>
                            <modelPackage>com.erhgo.openapi.dto</modelPackage>
                            <library>spring-boot</library>
                            <verbose>false</verbose>
                            <configOptions>
                                <useSpringBoot3>true</useSpringBoot3>
                                <library>spring-boot</library>
                                <useBeanValidation>true</useBeanValidation>
                                <prependFormOrBodyParameters>true</prependFormOrBodyParameters>
                                <interfaceOnly>true</interfaceOnly>
                                <delegatePattern>true</delegatePattern>
                                <sourceFolder>src/gen/java/main</sourceFolder>
                                <useSettersForCollections>false</useSettersForCollections>
                                <openApiNullable>false</openApiNullable>
                            </configOptions>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <supportingFilesToGenerate>ApiUtil.java</supportingFilesToGenerate>
                            <modelNameSuffix>DTO</modelNameSuffix>
                        </configuration>
                    </execution>
                    <execution>
                        <id>ats-adecco</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/misc/external/adecco.yaml</inputSpec>
                            <generatorName>java</generatorName>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <library>native</library>
                            <verbose>false</verbose>
                            <apiPackage>com.erhgo.openapi.client.adecco.api</apiPackage>
                            <modelPackage>com.erhgo.openapi.client.adecco.api.model</modelPackage>
                            <configOptions>
                                <useJakartaEe>true</useJakartaEe>
                                <sourceFolder>src/gen/java/main</sourceFolder>
                                <java8>false</java8>
                                <java17>true</java17>
                                <dateLibrary>java8</dateLibrary>
                                <useRecordConstructors>true</useRecordConstructors>
                                <useEnumCaseInsensitive>true</useEnumCaseInsensitive>
                                <openApiNullable>false</openApiNullable>
                                <additionalModelTypeAnnotations>
                                    @com.fasterxml.jackson.annotation.JsonIgnoreProperties(ignoreUnknown = true)
                                </additionalModelTypeAnnotations>
                            </configOptions>
                        </configuration>
                    </execution>
                    <execution>
                        <id>ats-inrecruiting</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/misc/external/inrecruiting.yaml</inputSpec>
                            <generatorName>java</generatorName>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <library>native</library>
                            <verbose>false</verbose>
                            <apiPackage>com.erhgo.openapi.client.inrecruiting.api</apiPackage>
                            <modelPackage>com.erhgo.openapi.client.inrecruiting.api.model</modelPackage>
                            <configOptions>
                                <useJakartaEe>true</useJakartaEe>
                                <sourceFolder>src/gen/java/main</sourceFolder>
                                <java8>false</java8>
                                <java17>true</java17>
                                <dateLibrary>java8</dateLibrary>
                                <useRecordConstructors>true</useRecordConstructors>
                                <useEnumCaseInsensitive>true</useEnumCaseInsensitive>
                                <openApiNullable>false</openApiNullable>
                                <additionalModelTypeAnnotations>
                                    @com.fasterxml.jackson.annotation.JsonIgnoreProperties(ignoreUnknown = true)
                                </additionalModelTypeAnnotations>
                            </configOptions>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.liquibase</groupId>
                <artifactId>liquibase-maven-plugin</artifactId>
                <configuration>
                    <propertyFile>src/main/resources/liquibase-local.properties</propertyFile>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco-maven-plugin.version}</version>
                <configuration>
                    <excludes>
                        <exclude>**/*com/erhgo/migrations/**</exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <id>pre-unit-tests</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>post-unit-test</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
