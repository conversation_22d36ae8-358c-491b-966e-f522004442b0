# yamllint disable-line rule:line-length
.getDockerSourceTag: &getDockerSourceTag |
  # We are looking for the docker tag of the image we want to deploy. If we
  # just merged into master, we actually tagged it using the hash of the tip of
  # the merge request. Thankfully we can find it: it's the second parent of the
  # current HEAD (which should be a merge commit). Just in case we're not on a
  # merge commit (for an emergency rollback to a previous version for exemple),
  # we fallback to the hash of the current HEAD.
  export DOCKER_SOURCE_TAG="$CI_REGISTRY_IMAGE/api:$(git log --pretty="%H" -n 1 HEAD^2 2>/dev/null || git log --pretty="%H" -n 1 HEAD)"
build API:
  only: [ merge_requests ]
  needs: [ "generate api.yaml" ]
  stage: build
  interruptible: true
  image: "maven:3.9-eclipse-temurin-21-alpine"
  tags:
    - docker
  services:
    - docker:dind-rootless
  variables:
    # Uncommentnext line to disable <PERSON><PERSON><PERSON> and avoid dependency to quay.io
    TESTCONTAINERS_RYUK_DISABLED: "true"

  # Cache downloaded dependencies and plugins between builds.
  cache:
    key: "API m2"
    paths:
      - .m2/repository
  artifacts:
    expire_in: 3 hours
    paths:
      - api/target
  script:
    # yamllint disable-line rule:line-length
    - export DOCKERHUB_AUTH="$(echo -n ${CI_REGISTRY_USER}:${CI_REGISTRY_PASSWORD} | base64)"
    - export DOCKER_AUTH_CONFIG="{\"auths\":{\"https://index.docker.io/v1/\":{\"auth\":\"${DOCKERHUB_AUTH}\"}}}"
    - mkdir -p /root/.docker && echo "${DOCKER_AUTH_CONFIG}" > /root/.docker/config.json # here to suppress a testcontainers warning
    - cd api && mvn $MAVEN_CLI_OPTS package

dockerize API:
  only: [ merge_requests ]
  stage: dockerize
  interruptible: true
  needs:
    - job: "build API"
      optional: true
  image: "docker:stable"
  tags:
    - docker
  services:
    - docker:dind
  dependencies:
    - "build API"
  script:
    - docker login -u "${DOCKERHUB_USERNAME}" -p "${DOCKERHUB_PASSWORD}"
    - export DOCKER_TAG="$CI_REGISTRY_IMAGE/api:$CI_COMMIT_SHA"
    # yamllint disable-line rule:line-length
    - cd api && docker build --build-arg VERSION=$CI_COMMIT_SHA -t "$DOCKER_TAG" .
    - docker push "$DOCKER_TAG"

tag testing API:
  stage: dockerize
  interruptible: true
  only: [ "master", "/^fix-api-testing/", "/^testing-/" ]
  image: "docker:stable-git"
  tags:
    - docker
  services:
    - docker:dind
  script:
    - docker login -u "${DOCKERHUB_USERNAME}" -p "${DOCKERHUB_PASSWORD}"
    - *getDockerSourceTag
    - docker pull "$DOCKER_SOURCE_TAG"
    # yamllint disable-line rule:line-length
    - docker tag "$DOCKER_SOURCE_TAG" "$CI_REGISTRY_IMAGE/api:testing" && docker push "$CI_REGISTRY_IMAGE/api:testing"

tag staging API:
  stage: push
  interruptible: true
  needs:
    - job: "dockerize API"
      optional: true
  only: [ "/^staging-/", "/^fix-api-staging/" ]
  except: [ "branches" ]
  image: "docker:stable-git"
  tags:
    - docker
  services:
    - docker:dind
  script:
    - docker login -u "${DOCKERHUB_USERNAME}" -p "${DOCKERHUB_PASSWORD}"
    - *getDockerSourceTag
    - docker pull "$DOCKER_SOURCE_TAG"
    # yamllint disable-line rule:line-length
    - docker tag "$DOCKER_SOURCE_TAG" "$CI_REGISTRY_IMAGE/api:staging" && docker push "$CI_REGISTRY_IMAGE/api:staging"

tag prod API:
  stage: push
  interruptible: true
  needs:
    - job: "dockerize API"
      optional: true
  only: [ "/^prod-/", "/^fix-api-prod/" ]
  except: [ "branches" ]
  image: "docker:stable-git"
  tags:
    - docker
  services:
    - docker:dind
  script:
    - docker login -u "${DOCKERHUB_USERNAME}" -p "${DOCKERHUB_PASSWORD}"
    - *getDockerSourceTag
    - docker pull "$DOCKER_SOURCE_TAG"
    # yamllint disable-line rule:line-length
    - docker tag "$DOCKER_SOURCE_TAG" "$CI_REGISTRY_IMAGE/api:master" && docker push "$CI_REGISTRY_IMAGE/api:master"
