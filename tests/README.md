# Tests e2e (end-to-end)

Les tests e2e sont executés à l'aide de [Nighwatch.js](https://nightwatchjs.org/) et Selenium Server.
L'extension [nighwatch-vrt](https://github.com/Crunch-io/nightwatch-vrt) est utilisée pour faire du Visual Regression Testing, basé sur des copies d'écran du résultat attendu.

## Lancer les tests

`yarn test`

## Mettre à jour une copie d'écran de référence

Lorsque le résultat attendu d'une page change, il est nécessaire de mettre à jour la copie d'écran de référence pour mettre à jour le test associé.

Pour cela, supprimer la copie d'écran concernée dans le répertoire `vrt/baseline` puis exécuter de nouveau le test.

## Ajouter une nouvelle copie d'écran de référence ?
**L'environnement local dans lequel est lancé le test pour la mise à jour doit être tel qu'il sera joué lors de l'exécution automatique sur l'intégration continue (c'est-à-dire sur une base de données vierge, seulement initialisée avec les données de test), au risque d'enregistrer un résultat attendu différent.**

## Visualiser les tests

* Dans les `chromeOptions` de `nightwatch.conf.js` :
    * Supprimer le `headless`
    * Ajouter `disable-dev-shm-usage`
    * Passer `w3c` à true
* Dans `docker-compose-e2e.yml` :
  * Supprimer la mention START_XVFB
  * Ajouter à `docker-compose-e2e.yml` au service `selenium` (ou tester sans, ça ne devrait pas être utile...) :
    ```
        ports:
            - "5900:5900"
            - "4444:4444"
        environment:
           "webdriver.chrome.whitelistedIps": ""          
          ```
* Quand tout est démarré, pendant l'exécution des tests : se connecter en VNC à localhost:5900 / secret

## Debug hostname

Accéder (via vnc) à http://keycloak-e2e/realms/master/hostname-debug 

## Accéder à l'env des tests e2e

`/opt/google/chrome/chrome --incognito --host-resolver-rules="MAP back-office-e2e.localhost back-office-e2e:8080,MAP front-office-e2e.localhost front-office-e2e:8080,MAP sourcing-office-e2e.localhost sourcing-office-e2e:8080,MAP keycloak-e2e.localhost keycloak-e2e:8080,MAP api-e2e.localhost api-e2e:8080"`
