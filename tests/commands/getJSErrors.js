module.exports.command = function(callback) {
  this.execute(function() {
    return window.jsErrors;
  }, [], function(result) {
    if (typeof callback === 'function') {
      callback(result.value);
    }
  });
  return this;
};
/**
 * Usage - to log console content, simply add:
 * .getLog('browser', function(logEntriesArray) {
 *    logEntriesArray.forEach(log => console.log(`[JS msg] ${log.message}`));
 * })
 */
