module.exports.command = function(selector, newValue) {
  // FIXME: replacing value is complicated (clearValue is buggy) - see https://github.com/nightwatchjs/nightwatch/issues/1592
  const { END, BACK_SPACE } = this.Keys;
  return this.getValue(selector, (result) => {
    const chars = result.value.split('');
    // Make sure we are at the end of the input
    this.setValue(selector, END);
    // Delete all the existing characters
    chars.forEach(() => this.setValue(selector, BACK_SPACE));
    // Set new value
    this.setValue(selector, newValue);
  });
};
