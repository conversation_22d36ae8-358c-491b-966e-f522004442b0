module.exports.command = async function(username, password, checkScreenshot) {
  let nbRetry = 50;
  while (nbRetry-- && (await this.element('css selector', 'input[name="password"]')).status === -1) {
    console.log('No password...retry', 50 - nbRetry);
    this.pause(1000);
  }
  if ((await this.element('css selector', 'input[name="password"]')).status === -1) {
    return Promise.reject({ abortOnFailure: false });
  }
  if (checkScreenshot) {
    // Je vérifie que je suis bien sur la page d'authentification
    if (username.includes('admin')) {
      this.assert.screenshotIdenticalToBaseline('main', 'page-authentification-admin');
    } else {
      this.assert.screenshotIdenticalToBaseline('.main-wrapper', 'page-authentification');
    }
  }
  // Puis je me connecte
  this
    .setValue('input[name="username"]', username)
    .setValue('input[name="password"]', [
      this.Keys.CONTROL,
      'a',
      this.Keys.DELETE,
    ])
    .setValue('input[name="password"]', password)
    .click('[type=submit]')
  ;

  return this;
};
