module.exports = {
  '@tags': ['test', 'back-office'],

  /**
   * @param {NightwatchAPI} client
   */
  'ESCO occupation details': function(client) {
    
    client
      // Quand j'ouvre le détail d'un job esco
      .loginBOAndGoURL('/repository/esco-occupation/http:%2F%2Fdata.europa.eu%2Fesco%2Foccupation%2F9729c0f3-c9bc-482e-ac59-a82ec3b67ba3')
      .waitForElementVisible('#skills')
      // Avec la classification ouverte
      .assert.screenshotIdenticalToBaseline('body', 'page-detail-esco-skills')
      // Puis je clique sur la classification
      .click('#skillTitle')
      // La classification est fermée
      .waitForElementNotVisible('#skills')
      // J'attends la fin de l'animation
      .pause(300)
      // J'ouvre le détail
      .click('#detailTitle')
      // Le dernier élément du détail est visible
      .waitForElementVisible('#behaviorList')
      .assert.screenshotIdenticalToBaseline('body', 'page-detail-esco-detail')
      .end();
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  beforeEach: function (client, done) {
    client.resizeWindow(1920, 1080, done);
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  afterEach: function(client, done) {
    setTimeout(function() {
      done();
    }, 1000);
  },
};
