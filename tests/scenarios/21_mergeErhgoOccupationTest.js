module.exports = {
  '@tags': ['test', 'back-office'],

  /**
   * @param {NightwatchAPI} client
   */
  'Merge erhgo occupations': function(client) {
    client
      .loginBOAndGoURL('/repository/erhgo-occupation/merge')
      // Je cherche le métier opérateur de centre de données pour le métier servant de base
      .waitForElementVisible('#targetOccupation')
      .focusVselect('#targetOccupation')
      .keys('opérateur')
      .pause(1500)
      // je sélectionne le 1er résultat
      .keys([client.Keys.ARROW_DOWN, client.Keys.ENTER, client.Keys.NULL])
      // Je cherche le métier Accessoiriste pour le métier supprimé
      .focusVselect('#mergedOccupation')
      .keys('access')
      .pause(1500)
      // je sélectionne le 1er résultat
      .keys([client.Keys.ARROW_DOWN, client.Keys.ENTER, client.Keys.NULL])
      .assert.screenshotIdenticalToBaseline('body', 'page-merge-erhgo-occupations')
      .end();
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  beforeEach: function (client, done) {
    client.resizeWindow(1920, 1080, done);
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  afterEach: function(client, done) {
    setTimeout(function() {
      done();
    }, 1000);
  },
};
