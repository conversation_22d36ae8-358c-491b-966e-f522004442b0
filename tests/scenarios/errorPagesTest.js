const {back_office_base_url} = require('../env');

module.exports = {
  '@tags': ['test', 'back-office'],

  /**
   * @param {NightwatchAPI} client
   */
  '404': function(client) {
    client
      // Quand je me connecte et que j'ouvre une URL qui n'existe pas
      .loginBOAndGoURL('/404')
      .pause(500)
      // Je vois une belle page d'erreur 404
      .waitForElementVisible('body')
      .assert.screenshotIdenticalToBaseline('body', 'page-BO-404')
      .end();
  },

  /**
   * @param {NightwatchAPI} client
   */
  '401': function(client) {
    client
      // Quand j'ouvre une page du BO
      .url(back_office_base_url+'/setup/organization/create').waitForElementVisible('body')
      // FIXME: ERHGO-147 - add fixtures & restore this test
      // Et que je me connecte en tant que candidat
      //.login('candidate@localhost', 'test')
      // Je vois une belle page d'erreur 401
      // .waitForElementVisible('body')
      //.assert.screenshotIdenticalToBaseline('body', 'page-BO-401')
      .end();
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  beforeEach: function (client, done) {
    client.resizeWindow(1920, 1080, done);
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  afterEach: function(client, done) {
    setTimeout(function() {
      done();
    }, 1000);
  },
};
