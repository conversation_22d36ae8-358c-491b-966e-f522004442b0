module.exports = {
  '@tags': ['test', 'back-office'],

  /**
   * @param {NightwatchAPI} client
   */
  'jobCreation': function(client) {
    client
      // Quand j'ouvre la page de création d'un job
      .loginBOAndGoURL('/setup/organization/E-0001/job/create')
      .waitForElementVisible('#jobDetails')
      // Je renseigne les infos générales
      .setValue('input[id="jobTitle"]', 'Peintre en batiment')
      .setValue('input[id="serviceTitle"]', 'Peinture')
      .click('#searchCity')
      .keys('Lyon')
      .waitForElementVisible('#city-for-code69383', 60000)
      .keys([client.Keys.ARROW_DOWN, client.Keys.ENTER, client.Keys.NULL])
      .pause(500)
      .assert.screenshotIdenticalToBaseline('body', 'page-create-job-start')
      // Je passe à l'étape suivante
      .click('#nextStep')
      // Je crée une activité
      .waitForElementVisible('.missionTitle input')
      .setValue('.missionTitle input', 'Peindre des batiments')
      .waitForElementVisible('#activitySelector input')
      .setValue('#activitySelector input', 'Prendre un pinceau')
      // J'attends que les modifications soient prises en compte (éviter le 'test qui échoue parfois')
      .pause(200)
      .waitForElementVisible('button[aria-label="$vuetify.input.appendAction"]')
      .click('button[aria-label="$vuetify.input.appendAction"]')
      .waitForElementVisible('.capacityCheckbox')
      // Je sélectionne une capacité
      .click('.capacityCheckbox input + div')
      // Je clique sur le bouton de sauvegarde
      .getLocationInView('#saveActivityButton')
      .click('#saveActivityButton')
      .waitForElementNotVisible('#saveActivityButton')
      .pause(200)
      .execute('window.scrollTo(0,document.body.scrollHeight);') // scroll to bottom of page
      .pause(200)
      // L'activité doit être ajoutée au job
      .assert.screenshotIdenticalToBaseline('#mission0', 'page-create-job-activity-added')
      // Je crée un contexte
      .setValue('#contextSelector input', 'Travailler en extérieur')
      .click('button[aria-label="$vuetify.input.appendAction"]')
      .setValue('textarea#contextDescription_textArea', 'Supporter le chaud et le froid.')
      // Je sélectionne une catégorie
      .contextCategorySelector()
      .waitForElementVisible('#saveContextButton')
      .click('#saveContextButton')
      .pause(200);
    // Je sélectionne 'aucun contexte' pour les autres catégories
    const clickNoContext = (index) => client.useXpath().click(`(.//*[contains(@class,"no-context")])[${index}]`);
    [1, 2].forEach(clickNoContext);
    client.execute('window.scrollTo(0,document.body.scrollHeight);'); // scroll to bottom of page
    [3, 4, 5, 6, 7, 8, 9].forEach(clickNoContext);
    // Les contextes doivent être ajoutés au poste
    client
      .pause(800)
      .useCss()
      .assert.screenshotIdenticalToBaseline('#mission0', 'page-create-job-activity-context-added')
      .end();
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  beforeEach: function (client, done) {
    client.resizeWindow(1920, 1080, done);
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  afterEach: function(client, done) {
    setTimeout(function() {
      done();
    }, 1000);
  },
};
