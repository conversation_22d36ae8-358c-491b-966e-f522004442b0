module.exports = {
  '@tags': ['test', 'back-office'],

  /**
   * @param {NightwatchAPI} client
   */
  'Erhgo list': function(client) {
    
    client
      // Quand j'ouvre la liste des jobs erhgo
      .loginBOAndGoURL('/repository/erhgo-occupation')
      // Puis je vois la liste des jobs erhgo
      .waitForElementPresent('#erhgoList')
      .waitForElementNotPresent('.v-progress-linear')
      .assert.screenshotIdenticalToBaseline('body', 'page-liste-erhgo-occupation')
      .end();
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  beforeEach: function (client, done) {
    client.resizeWindow(1920, 1080, done);
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  afterEach: function(client, done) {
    setTimeout(function() {
      done();
    }, 1000);
  },
};
