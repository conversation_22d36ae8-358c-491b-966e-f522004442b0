module.exports = {
  '@tags': ['test', 'back-office'],

  /**
   * @param {NightwatchAPI} client
   */
  'Context creation': function(client) {
    const title = 'Travailler en open-space';

    client
      // Quand j'ouvre la liste des contextes
      .loginBOAndGoURL('/repository/contexts')
      // Alors je vois la liste des contextes
      .waitForElementPresent('#contextList')
      .waitForElementNotPresent('.v-progress-linear')
      // Puis je trie la liste des contextes
      .click('th:nth-of-type(2)')
      .pause(300)
      .assert.screenshotIdenticalToBaseline('body', 'page-liste-contextes')
      // Puis je clique sur le bouton d'ajout
      .click('#contextList .v-icon.mdi-plus')
      // Et je vois le formulaire de création d'activité, vierge
      .assert.screenshotIdenticalToBaseline('body', 'page-creation-contexte')
      // Je saisis 1 titre
      .setValue('input#contextTitle_textInput', title)
      // Je saisis 1 description
      .setValue('textarea#contextDescription_textArea', 'Supporter le bruit et la convivialité.')
      // Je sélectionne catégorie et niveau
      .contextCategorySelector()
      // Je vérifie que le formulaire est rempli
      .assert.screenshotIdenticalToBaseline('body', 'page-creation-contexte-remplie')
      // Puis je clique sur le bouton de sauvegarde
      .execute('window.scrollTo(0,document.body.scrollHeight);') // scroll to bottom of page
      .click('#saveContextButton')
      .waitForElementNotPresent('#saveContextButton.v-btn--loading')
      .waitForElementPresent('button.success')
      // Et je clique sur le bouton de retour à la liste
      .click('#backToContextListButton')
      // Et je vois la liste des contextes
      .waitForElementPresent('#contextList')
      .end();
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  beforeEach: function (client, done) {
    client.resizeWindow(1920, 1080, done);
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  afterEach: function(client, done) {
    setTimeout(function() {
      done();
    }, 1000);
  },
};
