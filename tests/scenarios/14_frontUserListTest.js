module.exports = {
  '@tags': ['test', 'back-office'],

  /**
   * @param {NightwatchAPI} client
   */
  'Front user list': function(client) {

    client
      .loginBOAndGoURL('/repository/front-users')
      // Puis je vois la liste des utilisateurs front-office
      .waitForElementVisible('#user-1', 10000)
      .assert.screenshotIdenticalToBaseline('body', 'page-front-user-list')
      .end();
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  beforeEach: function (client, done) {
    client.resizeWindow(1920, 1080, done);
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  afterEach: function(client, done) {
    setTimeout(function() {
      done();
    }, 1000);
  },
};
