module.exports = {
  '@tags': ['test', 'back-office'],

  /**
   * @param {NightwatchAPI} client
   */
  'Compare capacities': function(client) {
    client
      .loginBOAndGoURL('/repository/compare')
      .waitForElementVisible('#left-compare')
      .focusVselect('#left-compare #erhgo-search')
      .keys('opérateur')
      // Je cherche le métier opérateur de centre de données sur le pannel de gauche
      .pause(1500)
      // je sélectionne le 1er résultat
      .keys([client.Keys.ARROW_DOWN, client.Keys.ENTER])
      .waitForElementVisible('#left-compare .compare-row', 60000)
      // Je cherche l'individu candidate' sur le pannel de droite
      .focusVselect('#right-compare #userSearch')
      .keys('candidate')
      .pause(1500)
      // je sélectionne le dernier résultat
      .keys([client.Keys.ARROW_DOWN, client.Keys.ARROW_UP, client.Keys.ENTER])
      .waitForElementVisible('#right-compare .compare-row')
      .assert.screenshotIdenticalToBaseline('body', 'page-compare-capacities-result')
      .end();
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  beforeEach: function (client, done) {
    client.resizeWindow(1920, 1080, done);
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  afterEach: function(client, done) {
    setTimeout(function() {
      done();
    }, 1000);
  },
};
