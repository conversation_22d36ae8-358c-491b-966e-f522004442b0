module.exports = {
  '@tags': ['test', 'back-office'],

  /**
   * @param {NightwatchAPI} client
   */
  'invitationCodeList': function (client) {
    client
      .loginBOAndGoURL('/setup/invitationCode')
      .waitForElementPresent('#btnAddInvitationCode', 30000)
      .assert.screenshotIdenticalToBaseline('body', 'page-list-invitation-code')
      .end();
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  beforeEach: function (client, done) {
    client.resizeWindow(1920, 1080, done);
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  afterEach: function (client, done) {
    setTimeout(function () {
      done();
    }, 1000);
  },
};
