module.exports = {
  '@tags': ['test', 'back-office'],

  /**
   * @param {NightwatchAPI} client
   */
  'job list for project': function (client) {
    client
      .loginBOAndGoURL('/repository/front-users')
      .waitForElementVisible('#create-fo-user')
      .click('#create-fo-user')
      .waitForElementVisible('#submit-fo-creation')
      .setValue('#new-user-email', '<EMAIL>')
      .setValue('#new-user-phone input[type="tel"]', '0612345678')
      .click('#submit-fo-creation')
      .assert.screenshotIdenticalToBaseline('body', 'page-create-user-conflict')
      .replaceValue('#new-user-phone input[type="tel"]', '0612345679')
      .click('#submit-fo-creation')
      .waitForElementPresent('#matching-recruitment-bo-user')
      .assert.screenshotIdenticalToBaseline('body', 'page-edit-user-bo-top')
      .click('#matching-recruitment-bo-user')
      .pause(500)
      .assert.screenshotIdenticalToBaseline('body', 'page-matching-recruitment-bo-user')
      .waitForElementPresent('#close-matching-recruitment')
      .click('#close-matching-recruitment')
      .pause(500)
      .execute('window.scrollTo(0,document.body.scrollHeight);') // scroll to bottom of page
      .pause(500)
      .assert.screenshotIdenticalToBaseline('body', 'page-edit-user-bo')
      .end();
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  beforeEach: function (client, done) {
    client.resizeWindow(1920, 1080, done);
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  afterEach: function (client, done) {
    setTimeout(function () {
      done();
    }, 1000);
  },
};
