const {api_base_url} = require('../env');
const fetch = require('node-fetch');

module.exports = {
  '@tags': ['test'],

  /**
   * @param {NightwatchAPI} client
   */
  'Check the API\'s health': async function (client) {
    const oneTry = async function() {

      try {
        await fetch(`${api_base_url}/actuator/health`);
        return true;
      } catch(e) {
        return new Promise((resolve) => setTimeout(() => {resolve(e);}, 3000));
      }
    };
    let success = false, index = 100;

    while (success !== true && index-- > 0) {
      console.log('API not healthy - try nb ' + (100 - index));
      success = await oneTry();
    }
    console.log('API up and running');
    client.perform(async (browser, done) => {

      if(success !== true) {
        browser.assert.fail('API should be healthy');
      }
      done();
    });
  },
};
