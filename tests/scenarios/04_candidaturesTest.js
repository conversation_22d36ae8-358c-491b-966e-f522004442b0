module.exports = {
  '@tags': ['test', 'back-office'],

  /**
   * @param {NightwatchAPI} client
   */
  'Candidatures list': function(client) {
    client
      // Quand j'ouvre la page de détail d'un recrutement
      .loginBOAndGoURL('/setup/organization/E-0001/recruitment/1/result')
      .waitForElementVisible('#candidateResultMatching tr')
      .waitForElementVisible('#candidateResultNotMatching .v-data-table__empty-wrapper')
      .waitForElementVisible('#candidateResultUnfinished .v-data-table__empty-wrapper')
      .assert.screenshotIdenticalToBaseline('body', 'page-detail-recrutement-tous')
      // Je clique alors sur "Rencontrer cette personne"
      .click('#seeCandidature')
      // Et je suis redirigé sur le détail du candidat
      .waitForElementVisible('#job-candidature-detail', 10000)
      .assert.screenshotIdenticalToBaseline('body', 'page-detail-candidature-anonymous')
      // Et je vois la liste de ses missions
      .waitForElementVisible('#candidatureExperiences')
      .click('#candidatureExperiences')
      // Et je vois les expériences
      .waitForElementVisible('#candidatureExperience0')
      // j'attends la fin de l'animation
      .pause(400)
      // J'ouvre les informations de contact
      .click('#contactCandidateButton')
      // J'attends que l'adresse mail soit visible
      .waitForElementVisible('#contactInfos a[href="mailto:candidate@localhost"]')
      // j'attends la fin de l'animation
      .pause(400)
      .assert.screenshotIdenticalToBaseline('body', 'page-detail-candidature')
      .click('#candidatureExperiences')
      .waitForElementNotVisible('#candidatureExperience0')
      .click('#candidatureContextsPositioning')
      .waitForElementVisible('#contextPositioning0')
      // j'attends la fin de l'animation
      .pause(400)
      .assert.screenshotIdenticalToBaseline('body', 'page-detail-context-positioning')
      .end();
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  beforeEach: function (client, done) {
    client.resizeWindow(1920, 1080, done);
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  afterEach: function(client, done) {
    setTimeout(function() {
      done();
    }, 1000);
  },
};
