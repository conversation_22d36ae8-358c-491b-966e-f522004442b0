module.exports = {
  '@tags': ['test', 'back-office'],

  /**
   * @param {NightwatchAPI} client
   */
  'Territorial organization users test': function(client) {
    client
      //Quand j'accède à la liste des utilisateur affiliés à mon canal
      .loginBOAndGoURL('/setup/organization/P-0002/front-users')
      // FIXME : à corriger avec l'ajout des tests e2e du nouveau FO
      // .waitForElementVisible('#user-0')
      // Je vois l'utilisateur ayant précédemment accédé à ma landing page (cf. test 22_landingPageTest)
      .pause(1000)
      .assert.screenshotIdenticalToBaseline('body', 'page-front-user-by-organization')
      .end();

  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  beforeEach: function (client, done) {
    client.resizeWindow(1920, 1080, done);
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  afterEach: function(client, done) {
    setTimeout(function() {
      done();
    }, 1000);
  },
};
