module.exports = {
  '@tags': ['test', 'back-office'],

  /**
   * @param {NightwatchAPI} client
   */
  'Erhgo qualification': function(client) {

    client
      // j'accède à la liste des métiers ERHGO
      .loginBOAndGoURL('/repository/erhgo-occupation')
      // puis je vois la liste
      .waitForElementPresent('#erhgoList')
      // je prévisualise les détails de la popin imprimable
      .click('#erhgoList tbody tr:nth-child(2) .showOccupationButton')
      .waitForElementVisible('#exportOccupationAsPDFButton')
      .pause(200)
      .assert.screenshotIdenticalToBaseline('body', 'page-detail-erhgo-print-popin')
      // je ferme la popin
      .click('#closeOccupationPopin')
      .pause(200)
      // et je clique sur la 2eme ligne du tableau
      .click('#erhgoList tbody tr:nth-child(2) .editOccupationButton')
      .waitForElementVisible('#detailTitle')
      // j'ouvre les aptitudes
      .click('#skillTitle')
      .waitForElementVisible('#skills')
      // J'attends la fin de l'animation
      .pause(200)
      .assert.screenshotIdenticalToBaseline('body', 'page-detail-erhgo-skills')
      // je ferme les aptitudes
      .click('#skillTitle')
      // Puis je clique sur la classification
      .click('#classificationTitle')
      // Avec la classification ouverte
      .waitForElementVisible('#escoOccupations')
      // J'attends la fin de l'animation
      .pause(200)
      .assert.screenshotIdenticalToBaseline('body', 'page-detail-erhgo-classification')
      // La classification est fermée
      .click('#classificationTitle')
      .waitForElementNotVisible('#escoOccupations')
      .pause(200)
      // J'ouvre le détail
      .click('#detailTitle')
      // Le dernier élément du détail est visible
      .waitForElementVisible('#behaviorList')
      .assert.screenshotIdenticalToBaseline('body', 'page-detail-erhgo-detail')
      // Je referme le détail
      .click('#detailTitle')
      .waitForElementNotVisible('#occupationDescription')
      // J'attends la fin de l'animation
      .pause(200)
      // J'ouvre les libellés alternatifs
      .click('#labelsTitle')
      // J'attends la fin de l'animation
      .pause(200)
      .waitForElementVisible('#occupationTitle')
      .assert.screenshotIdenticalToBaseline('body', 'page-detail-erhgo-labels')
      // Je referme les libellés
      .click('#labelsTitle')
      .waitForElementNotVisible('#occupationTitle')
      // J'attends la fin de l'animation
      .pause(200)
      // J'ouvre les activités
      .click('#activitiesTitle')
      .pause(200)
      .waitForElementVisible('#activities')
      .assert.screenshotIdenticalToBaseline('body', 'page-detail-erhgo-activities')
      .click('#activitiesTitle')
      .waitForElementNotVisible('#activities')
      .pause(200)
      // J'ouvre les contextes
      .click('#contextsTitle')
      .pause(200)
      .waitForElementVisible('#contexts')
      .assert.screenshotIdenticalToBaseline('body', 'page-detail-erhgo-contexts')
      .click('#contextsTitle')
      .waitForElementNotVisible('#contexts')
      // J'ouvre les comportements
      .click('#behaviorsTitle')
      .pause(400)
      .waitForElementVisible('#behaviors')
      .execute('window.scrollTo(0,document.body.scrollHeight);') // scroll to bottom of page
      .pause(200)
      .assert.screenshotIdenticalToBaseline('body', 'page-detail-erhgo-behaviors')
      .end();
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  beforeEach: function (client, done) {
    client.resizeWindow(1920, 1080, done);
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  afterEach: function(client, done) {
    setTimeout(function() {
      done();
    }, 1000);
  },
};
