module.exports = {
  '@tags': ['test', 'back-office'],

  /**
   * @param {NightwatchAPI} client
   */
  'Landing page': function(client) {
    client
      .loginBOAndGoURL('/repository/landing-page/list')
      .waitForElementVisible('#landingPageList')
      .assert.screenshotIdenticalToBaseline('body', 'page-landing-pages')
      .click('#createLandingPageButton')
      .waitForElementVisible('#landingPageForm')
      .click('#landingPageFormContent')
      .keys('Un nouveau contenu !')
      .setValue('#landingPageFormUrlKey', 'newContent')
      .focusVselect('#landingPageOrganizations')
      // je sélectionne le 1er résultat
      .keys([client.Keys.ARROW_DOWN, client.Keys.ENTER, client.Keys.NULL])
      .pause(500)
      .assert.screenshotIdenticalToBaseline('body', 'page-landing-page-form')
      .end();
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  beforeEach: function (client, done) {
    client.resizeWindow(1920, 1080, done);
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  afterEach: function(client, done) {
    setTimeout(function() {
      done();
    }, 1000);
  },
};
