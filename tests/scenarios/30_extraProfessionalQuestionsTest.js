const {back_office_base_url} = require('../env');

module.exports = {
  '@tags': ['test', 'back-office'],

  /**
   * @param {NightwatchAPI} client
   */
  'extra pro questions test': function (client) {
    const questionUrl = '/repository/capacity-related-question/question/list';
    client
      // Quand j'ouvre la liste des questions relatives aux AEP
      .loginBOAndGoURL(questionUrl)
      // Et que la liste s'affiche
      .waitForElementVisible('#editCapacityRelatedQuestion-0')
      // Alors la liste doit être correcte
      .assert.screenshotIdenticalToBaseline('body', 'page-liste-questions-AEP')
      // Puis quand je clique sur le bouton d'ajout d'une question
      .click('#createCapacityRelatedQuestionButton')
      // Et que le formulaire s'affiche
      .waitForElementVisible('#questionTitle')
      .setValue('#questionTitle', 'Faites-vous le ménage :')
      .keys([client.Keys.TAB, client.Keys.NULL])
      .keys('Tous les jours')
      .keys([client.Keys.TAB, client.Keys.NULL])
      .keys([client.Keys.TAB, client.Keys.NULL])
      .keys('Jamais')
      .click('#submitQuestion')
      .waitForElementNotPresent('.v-btn__loader')
      .url(back_office_base_url + questionUrl)
      .waitForElementVisible('#editCapacityRelatedQuestion-0')
      .assert.screenshotIdenticalToBaseline('body', 'page-liste-questions-added-AEP')
      .click('#reorderCapacityRelatedQuestion-1')
      .waitForElementNotPresent('#reorderCapacityRelatedQuestion-1.v-btn--disabled')
      .assert.screenshotIdenticalToBaseline('body', 'page-liste-questions-reordered-AEP')
      .end();
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  beforeEach: function (client, done) {
    client.resizeWindow(1920, 1080, done);
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  afterEach: function (client, done) {
    setTimeout(function () {
      done();
    }, 1000);
  },
};
