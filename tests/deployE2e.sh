#!/bin/sh
set -e

kill_all() {
  echo "-Stop the containers and down volumes-"
  docker-compose -f docker-compose-e2e.yml -p $CI_COMMIT_SHA down --volumes --remove-orphans
  if [ $CI_REGISTRY_IMAGE = "local" ]; then
    echo "-Restart local containers-"
    cd ..
    docker-compose up -d
  fi
}

export CI_REGISTRY_IMAGE=${CI_REGISTRY_IMAGE:-local}
export CI_COMMIT_SHA=${CI_COMMIT_SHA:-local}
export CURRENT_UID=$(id -u):$(id -g)

trap 'kill_all' INT EXIT

if [ $CI_REGISTRY_IMAGE = "local" ]; then
  cd ..
  echo "-Stop local containers-"
  docker-compose down
  cd ./tests
fi

for param in "$@"; do
  if [ $param = "down" ]; then
    echo "-Stop the containers and down volumes-"
    kill_all
    exit 0
  fi

  if [ $param = "build" ]; then
    echo "-Build the images-"
    echo "--Build common--"
    cd ../common
    ./generateClient.sh
    cd ..
    echo "--Build BO--"
    docker build --build-arg APP=back-office -t local/back-office:local .
    echo "--Build Sourcing--"
    docker build -t local/sourcing:local -f sourcing/Dockerfile .
    echo "--Build API jar--"
    cd api
    mvn clean install -DskipTests
    echo "--Build API image--"
    docker build -t local/api:local ./
    cd ../tests
    echo "--Build test image--"
    docker build -t local/test_e2e_launcher:local .
    echo "--Refresh keycloak image--"
    docker pull erhgo/authentication:develop
  fi
done

# pre-create folders to ensure tests output will be accessible for container (and present in artifact)
rm -fr tests_output vrt/latest vrt/diff
mkdir tests_output vrt/latest vrt/diff

if [ $CI_REGISTRY_IMAGE != "local" ]; then
  echo "-Fetching Keycloak-"
  docker pull erhgo/authentication:develop
else
  echo "--Processing local build - ignore Dockerhub registry authentication--"
fi
echo "-Starting keycloak-db-"
docker-compose -f docker-compose-e2e.yml -p $CI_COMMIT_SHA up -d keycloak-db-e2e
sleep 3s
echo "-Starting keycloak-"
docker-compose -f docker-compose-e2e.yml -p $CI_COMMIT_SHA up -d keycloak-e2e
sleep 3s
echo "-Starting mariadb-"
docker-compose -f docker-compose-e2e.yml -p $CI_COMMIT_SHA up -d mariadb-e2e
sleep 2s
echo "-Starting api-"
docker-compose -f docker-compose-e2e.yml -p $CI_COMMIT_SHA up -d api-e2e
echo "-Starting back-office-"
docker-compose -f docker-compose-e2e.yml -p $CI_COMMIT_SHA up -d back-office-e2e
echo "-Starting Sourcing-"
docker-compose -f docker-compose-e2e.yml -p $CI_COMMIT_SHA up -d sourcing-e2e
echo "-selenium-"
docker-compose -f docker-compose-e2e.yml -p $CI_COMMIT_SHA up -d selenium
sleep 5s
echo "-yarn test-"
docker-compose -f docker-compose-e2e.yml -p $CI_COMMIT_SHA up -d yarn_test

set +e

docker exec "${CI_COMMIT_SHA}_yarn_test_1" sh -c 'yarn test'
RESULT=$?
docker logs "${CI_COMMIT_SHA}_api-e2e_1" > tests_output/api.log

set -e

exit $RESULT
