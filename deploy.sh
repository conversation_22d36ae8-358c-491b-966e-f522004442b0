#!/bin/bash
set -euo pipefail

DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"

die () {
    echo -e >&2 "$@"
    exit 1
}

if [[ ($# != 2 && $# != 3) ]]
  then
    die "Deploy command - Syntax : deploy.sh <commit-id> <target> [application]\nAllowed target: testing, staging, prod.\nAllowed environment: fo, bo, sourcing, api, all. Defaults to all."
fi

targetBranch="${1}"
targetEnv="${2}"
application="${3:-all}"

if ! [[ "$targetEnv" =~ ^(testing|staging|prod)$ ]];
  then
    die "Incorrect target environment. Accepted values are: testing, staging, prod"
fi
if ! [[ "$application" =~ ^(fo|bo|sourcing|api|all)$ ]];
  then
    die "Incorrect target application. Accepted values are: fo, bo, sourcing, api, all. Default value is all."
fi

cd ${DIR}

if ! git diff-index --quiet HEAD --; then
    die "Error: You have local changes - aborting."
fi

git checkout $targetBranch
git fetch
git pull
if ! git diff-index --quiet @{upstream} --; then
  die "Error: You have not pushed to remote $targetBranch - aborting."
fi
[[ $application == "all" ]] && prefix="" || prefix="fix-$application-"
TAGNAME=$prefix$targetEnv-`date +"%Y-%m-%d"`-`git log --format="%H" -n 1`
git tag $TAGNAME
if [ $? -ne 0 ]; then
    die "Unable to tag $targetBranch - aborting."
fi
git push origin $TAGNAME
if [ $? -ne 0 ]; then
    die "Unable to push tag for $targetBranch - aborting."
fi

echo "Deployment was launched: see CI for next step."
