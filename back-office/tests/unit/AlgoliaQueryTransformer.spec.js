import UserIndexSearchService from '@/views/repository/user/search/UserIndexSearchService';

test('get empty query', () => {
  expect(UserIndexSearchService.computeQuery({configure: {query: '', filters: ''}}))
    .toStrictEqual({
      'aroundLatLng': undefined,
      'aroundRadius': undefined,
      'filters': '',
      'query': '',
    });
});

test('get complex query', () => {
  const query = UserIndexSearchService.computeQuery({
    refinementList: {'criteria': ['b', 'a'], 'f4': ['e', 'f'], 'f5': ['i'], 'f6': []},
    configure: {
      query: 'pipo',
      aroundRadius: 3200,
      aroundLatLng: '45,55',
      filters: 'minSalary>5 AND maxSalary<6',
    },
  });
  expect(query).toStrictEqual({
    'aroundLatLng': '45,55',
    'aroundRadius': 3200,
    'filters': `criteria:"b" AND criteria:"a" AND (f4:"e" OR f4:"f") AND (f5:"i") AND minSalary>5 AND maxSalary<6`,
    'query': 'pipo',
  });
});
