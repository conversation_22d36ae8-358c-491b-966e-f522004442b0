import { baseUrl } from 'odas-plugins/base-url';

const mockLocation = (protocol, host) => {
  const oldWindow = window.location;
  delete window.location;
  window.location = {
    ...oldWindow,
    protocol,
    host,
  };

};

test('getting auth url from localhost should return localhost', () => {
  mockLocation('http:', 'localhost:8080');
  const authUrl = baseUrl('auth');

  expect(authUrl).toBe('http://auth.localhost');
});

test('getting auth url from demo pipolo should return demo-auth', () => {
  mockLocation('https:', 'demo.pipolo.fr');
  const authUrl = baseUrl('auth');

  expect(authUrl).toBe('https://demo-auth.pipolo.fr');
});

test('getting auth url from demo erhgo should return demo-auth', () => {
  mockLocation('https:', 'demo.jenesuispasuncv.fr');
  const authUrl = baseUrl('auth');

  expect(authUrl).toBe('https://demo-auth.jenesuispasuncv.fr');
});

test('getting auth url from testing erhgo bo should return testing-auth', () => {
  mockLocation('https:', 'testing-bo.jenesuispasuncv.fr');
  const authUrl = baseUrl('auth');

  expect(authUrl).toBe('https://testing-auth.jenesuispasuncv.fr');
});

test('getting auth url from prod bo erhgo should return auth', () => {
  mockLocation('https:', 'bo.jenesuispasuncv.fr');
  const authUrl = baseUrl('auth');

  expect(authUrl).toBe('https://auth.jenesuispasuncv.fr');
});

test('getting auth url from prod fo john should return auth', () => {
  mockLocation('https:', 'app.john.lol');
  const authUrl = baseUrl('auth');

  expect(authUrl).toBe('https://auth.john.lol');
});

test('getting auth url from prod bo odas should return auth', () => {
  mockLocation('https:', 'bo.odas.app');
  const authUrl = baseUrl('auth');

  expect(authUrl).toBe('https://auth.odas.app');
});

test('getting auth url from prod fo erhgo should return auth', () => {
  mockLocation('https:', 'app.jenesuispasuncv.fr');
  const authUrl = baseUrl('auth');

  expect(authUrl).toBe('https://auth.jenesuispasuncv.fr');
});

test('getting fo url from prod bo erhgo should return erhgo', () => {
  mockLocation('https:', 'bo.jenesuispasuncv.fr');
  const authUrl = baseUrl('fo');

  expect(authUrl).toBe('https://app.jenesuispasuncv.fr');
});

test('getting fo url from prod bo odas should return erhgo', () => {
  mockLocation('https:', 'bo.odas.app');
  const authUrl = baseUrl('fo');

  expect(authUrl).toBe('https://app.odas.app');
});


test('getting api url from prod bo odas should return odas', () => {
  mockLocation('https:', 'bo.odas.app');
  const authUrl = baseUrl('api');

  expect(authUrl).toBe('https://api.odas.app');
});


test('getting api url from prod bo erhgo should return erhgo', () => {
  mockLocation('https:', 'bo.jenesuispasuncv.fr');
  const authUrl = baseUrl('api');

  expect(authUrl).toBe('https://api.jenesuispasuncv.fr');
});

test('getting api url from localhost fo should return localhost:8080', () => {
  mockLocation('http:', 'localhost:8060');
  const authUrl = baseUrl('api');

  expect(authUrl).toBe('http://localhost:8080');
});


test('getting api url from localhost bo should return localhost:8080', () => {
  mockLocation('http:', 'localhost:8070');
  const authUrl = baseUrl('api');

  expect(authUrl).toBe('http://localhost:8080');
});

test('getting api url from e2e bo should return api-e2e:8080', () => {
  mockLocation('http:', 'back-office-e2e:8080');
  const authUrl = baseUrl('api');

  expect(authUrl).toBe('http://api-e2e:8080');
});

test('getting api url from e2e fo should return api-e2e:8080', () => {
  mockLocation('http:', 'front-office-e2e:8080');
  const authUrl = baseUrl('api');

  expect(authUrl).toBe('http://api-e2e:8080');
});

test('getting api url from e2e fo should return keycloak-e2e:8080', () => {
  mockLocation('http:', 'front-office-e2e:8080');
  const authUrl = baseUrl('auth');

  expect(authUrl).toBe('http://keycloak-e2e:8080');
});


test('getting api url from prod bo erhgo should return erhgo', () => {
  mockLocation('https:', 'bo.erhgo.fr');
  const authUrl = baseUrl('api');

  expect(authUrl).toBe('https://api.erhgo.fr');
});


test('getting auth url from prod fo erhgo should return auth', () => {
  mockLocation('https:', 'app.erhgo.fr');
  const authUrl = baseUrl('auth');

  expect(authUrl).toBe('https://auth.erhgo.fr');
});

test('getting fo url from prod bo erhgo should return erhgo', () => {
  mockLocation('https:', 'bo.erhgo.fr');
  const authUrl = baseUrl('fo');

  expect(authUrl).toBe('https://app.erhgo.fr');
});
