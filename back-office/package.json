{"name": "backoffice-odas", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint --config=../.eslintrc.js", "build-serve": "vue-cli-service build --mode=prod-local && cd dist && npx serve . -l 8070"}, "dependencies": {"@fortawesome/fontawesome-pro": "^6.4.0", "@mdi/font": "^4.7.95", "algoliasearch": "^4.11.0", "async-debounce": "*", "axios": "^1.3.4", "core-js": "^3.4.4", "erhgo-api-client": "*", "global": "^4.3.2", "jsog": "^1.0.7", "lodash": "^4.17.11", "material-design-icons-iconfont": "^5.0.1", "moment": "^2.22.2", "odas-plugins": "*", "prettier": "^2.8.7", "quill-image-resize-vue": "^1.0.4", "uuid": "^9.0.0", "vue": "^2.6.11", "vue-i18n": "^8.16.0", "vue-instantsearch": "^4.0.1", "vue-router": "^3.1.6", "vue-tel-input-vuetify": "^1.5.0", "vue-truncate-filter": "^1.1.7", "vue2-editor": "^2.10.2", "vuetify": "^2.6.0", "vuex": "^3.1.3"}, "devDependencies": {"@babel/core": "^7.22.5", "@types/jest": "^29.5.0", "@types/lodash": "^4.14.149", "@types/node": "^18.15.11", "@types/uuid": "^9.0.1", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-typescript": "~5.0.8", "@vue/cli-plugin-unit-jest": "~5.0.8", "@vue/cli-plugin-vuex": "~5.0.8", "@vue/cli-service": "~5.0.8", "@vue/eslint-config-airbnb": "^7.0.0", "@vue/eslint-config-typescript": "^11.0.2", "@vue/vue2-jest": "^29.2.3", "babel-core": "^6.26.3", "babel-jest": "^29.5.0", "eslint": "^8.37.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-vue": "^9.10.0", "eslint-plugin-vuejs-accessibility": "^2.1.0", "eslint-plugin-vuetify": "^1.0.1", "jest": "^29.5.0", "sass": "~1.63.0", "sass-loader": "^13.2.2", "ts-jest": "^29.0.5", "typescript": "~5.1.0", "vue-cli-plugin-axios": "^0.0.4", "vue-cli-plugin-i18n": "~2.3.2", "vue-cli-plugin-vuetify": "~2.5.8", "vue-jest": "^3.0.7", "vue-template-compiler": "^2.6.10", "vuetify-loader": "^1.7.0"}, "browserslist": ["defaults"]}