import Vue from 'vue';
import SafeService from 'odas-plugins/SafeService';
import { LandingPageDetail, LandingPagePage } from 'erhgo-api-client';

export default class LandingPageSearchService extends SafeService {

  private _landingPages: LandingPagePage | null = null;
  private _landingPageDetail: LandingPageDetail | null = null;

  async fetchLandingPages(page: number, size: number) {
    await this.safeCall(async () => {
      this._landingPages = (await Vue.$api.getLandingPages(page, size)).data;
    });
  }

  async fetchLandingPageDetail(id: string) {
    await this.safeCall(async () => {
      this._landingPageDetail = (await Vue.$api.getLandingPageById(id)).data;
    });
  }

  get landingPages(): LandingPagePage | null {
    return this._landingPages;
  }

  get landingPageDetail(): LandingPageDetail | null {
    return this._landingPageDetail;
  }
}
