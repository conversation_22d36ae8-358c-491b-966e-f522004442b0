<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card flat>
          <v-card-title>
            <h2 class="d-flex align-center">
              <v-icon color="primary" class="mr-2" large right>edit</v-icon>
              Edition de page d'accueil
              <v-icon v-if="editService.success" color="success" class="ml-2" large right>check</v-icon>
            </h2>
          </v-card-title>
          <v-card-text>
            <v-form ref="form" id="landingPageForm">
              <v-row>
                <v-col cols="12">
                  <v-alert type="info" v-if="!editService.isValid" outlined dense>
                    Le contenu et la clé sont indispensables
                  </v-alert>
                  <v-alert type="error" v-if="editService.showAPIError && !editService.isConflict" outlined dense>
                    Une erreur est survenue, veuillez réessayer.
                  </v-alert>
                  <v-alert type="error" v-if="editService.isConflict" outlined dense>
                    La clé est déjà utilisée sur une autre page.
                  </v-alert>
                  <vue-editor
                    id="landingPageFormContent"
                    v-model="editService.content"
                    :customModules="customModulesForWysiwyg"
                    :editorOptions="editorSettings"
                  />
                </v-col>
              </v-row>
              <v-row>
                <v-col>
                  <v-text-field
                    id="landingPageFormUrlKey"
                    v-model="editService.urlKey"
                    max-length="30"
                    :counter="30"
                    label="Clé de la landing page, qu'on retrouvera dans l'URL"
                    :rules="[rules.required, rules.isURLValid]"
                  />
                </v-col>
              </v-row>
              <v-row>
                <v-col>
                  <v-autocomplete v-model="editService.organizationCodes"
                                  multiple
                                  id="landingPageOrganizations"
                                  menu-props="closeOnContentClick"
                                  :items="organizationsItems"
                                  :loading="autocompleteLoading"
                                  label="Rattacher à un opérateur territorial, un projet ou un groupement d'employeurs"
                                  hint="Permet l'affectation des individus transitant par cette landing page"
                                  persistent-hint
                                  deletable-chips
                                  chips
                                  small-chips
                                  hide-no-data
                                  hide-selected
                                  :item-text="o => `${o.title}${o.organizationType === 'SOURCING' ? ' (sourcing)':''}`"
                                  item-value="code"/>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12">
                  <h4>Aperçu
                    <v-btn class="ml-2"
                           id="goFrontOfficeLandingPage"
                           x-small
                           v-if="editService.isValid"
                           outlined
                           color="primary"
                           target="_blank"
                           :href="editService.url">
                      <v-icon class="mr-2" small>
                        visibility
                      </v-icon>
                      Voir l'aperçu en ligne
                    </v-btn>
                  </h4>
                </v-col>
                <v-col cols="12">
                  <div class='ql-editor' v-html="editService.content"/>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import ImageResize from 'quill-image-resize-vue';
import LandingPageEditService from './LandingPageEditService';
import LandingPageSearchService from './LandingPageSearchService';

export default {
  name: 'edit_landing_page',
  props: {
    id: {
      type: String,
      required: false,
    },
  },
  data() {
    return {
      rules: {
        required: value => !!value || 'Requis',
        isURLValid: value => !LandingPageEditService.isUrlKeyInvalid(value) || 'URL non valide',
      },
      customModulesForWysiwyg: [{alias: 'imageResize', module: ImageResize}],
      editorSettings: {
        modules: {
          imageResize: {},
        },
      },
      editService: LandingPageEditService,
      searchService: LandingPageSearchService,
      organizationsItems: [],
      autocompleteLoading: false,
    };
  },
  async created() {
    this.searchService = new LandingPageSearchService();
    if (this.id) {
      await this.searchService.fetchLandingPageDetail(this.id);
    }
    this.editService = new LandingPageEditService(this.searchService.landingPageDetail);
    await this.getOrganizationFromAPI();
  },
  methods: {
    async getOrganizationFromAPI() {
      try {
        this.autocompleteLoading = true;
        const res = await this.$api.getAllRecruiters();
        this.organizationsItems = res.data;
      } finally {
        this.autocompleteLoading = false;
      }
    },
  },
};
</script>
