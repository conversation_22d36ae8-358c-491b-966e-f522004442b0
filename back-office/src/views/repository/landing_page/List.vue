<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card flat>
          <v-card-title>
            <h1>
              <v-icon large
                      color="primary">edit</v-icon>
              Pages d'accueil
            </h1>
          </v-card-title>
          <v-card-text>
            <v-col>
              <v-data-table :headers="headers"
                            :items="items"
                            :sort-by.sync="pagination.sortBy"
                            :page.sync="pagination.page"
                            :items-per-page.sync="pagination.rowsPerPage"
                            :footer-props="{
                              'items-per-page-options': rowsPerPage,
                              'items-per-page-text': 'Nb lignes par page',
                            }"
                            class="elevation-15"
                            item-key="id"
                            id="landingPageList"
                            no-data-text="Aucun résultat disponible"
                            :server-items-length="totalNumberOfElements">
                <template v-slot:item="props">
                  <tr>
                    <td class="text-left">{{ props.item.urlKey }}</td>
                    <td class="text-left">{{
                        props.item.organizations && props.item.organizations.join(', ') || 'Aucune'
                      }}
                    </td>
                    <td class="text-right">
                      <v-btn icon
                             :id="`editLandingPage-${items.indexOf(props.item)}`"
                             :to="{ name: 'edit_landing_page', params: { id: props.item.id } }"
                             class="mx-1">
                        <v-icon color="primary">
                          edit
                        </v-icon>
                      </v-btn>
                    </td>
                  </tr>
                </template>
                <template v-slot:footer.page-text="props">
                  Lignes de {{ props.pageStart }} à {{ props.pageStop }} sur {{ props.itemsLength }}
                </template>
              </v-data-table>
              <v-row class="mx-1 my-3">
                <v-spacer/>
                <v-tooltip color="red darken-1"
                           left>
                  <template v-slot:activator="{ on }">
                    <v-btn fab
                           dark
                           color="red darken-1"
                           v-on="on"
                           class="text-center"
                           id="createLandingPageButton"
                           :to="{ name: 'edit_landing_page' }">
                      <v-icon>mdi-plus</v-icon>
                    </v-btn>
                  </template>
                  <span class="font-weight-bold">{{$t('action.create')}}</span>
                </v-tooltip>
              </v-row>
            </v-col>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import LandingPageEditService from './LandingPageSearchService';
import _ from 'lodash';

export default {
  computed: {
    totalNumberOfElements() {
      return this.landingPageService.landingPages?.totalNumberOfElements;
    },
    items() {
      return this.landingPageService.landingPages?.content;
    },
  },
  async created() {
    this.landingPageService = new LandingPageEditService();
    await this.getDataFromApi();
  },
  data() {
    return {
      landingPageService: LandingPageEditService,
      headers: [
        {
          text: 'Clé d\'URL',
          align: 'left',
          sortable: false,
        },
        {
          text: 'Organisations',
          align: 'left',
          sortable: false,
        },
        {
          text: this.$t('ref.headers.actions'),
          align: 'right',
          icon: '',
          sortable: false,
        },
      ],
      rowsPerPage: [10, 25, 50, 100, 200],
      pagination: {
        page: 1,
        rowsPerPage: 25,
      },
    };
  },
  methods: {
    async getDataFromApi() {
      await this.landingPageService.fetchLandingPages(this.pagination.page - 1, this.pagination.rowsPerPage);
    },
  },
  watch: {
    pagination: {
      handler: _.debounce(async function () {
        await this.getDataFromApi();
      }, 200),
      deep: true,
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
