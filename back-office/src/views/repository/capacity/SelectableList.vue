<template>
  <v-row>
    <v-col cols="12">
      <v-row class="py-3">
        <capacity-chip v-for="capacity in sortedSelectedCapacities"
                       :capacity="capacity"
                       :key="capacity.id"
        />
      </v-row>
      <h4 v-if="inducedCapacitiesWithoutSelected.length > 0">Autres capacités induites</h4>
      <v-row class="py-3">
        <capacity-chip v-for="capacity in inducedCapacitiesWithoutSelected"
                       :capacity="capacity"
                       :key="capacity.id"
        />
      </v-row>
      <v-alert type="error" v-if="error" :value="true">
        {{ error }}
      </v-alert>
    </v-col>
    <v-col cols="12">
      <v-list two-line>
        <template v-for="capacity in sortedCapacities">
          <v-list-group
            :key="capacity.id"
            v-if="capacity.inducedCapacities.length > 0"
          >
            <template v-slot:activator>
              <v-list-item>
                <v-list-item-action>
                  <v-checkbox
                    v-model="capacity.checked"
                    class="capacityCheckbox"
                    @click.native.stop="() => handleCheck(capacity)"
                  />
                </v-list-item-action>
                <v-list-item-content>
                  <v-list-item-title>{{ capacity.code }} - {{ capacity.title }}</v-list-item-title>
                  <v-list-item-subtitle>{{ capacity.description }}</v-list-item-subtitle>
                </v-list-item-content>
              </v-list-item>
            </template>
            <v-container fluid>
              <v-row>
                <v-col class="xs-10 offset-xs-2">
                  <ul>
                    <li v-for="inducedCapacity in capacity.inducedCapacities" :key="inducedCapacity.id">
                      {{ inducedCapacity.code }} - {{ inducedCapacity.title }}
                      <ul v-if="inducedCapacity.inducedCapacities.length > 0">
                        <li
                          v-for="inducedCapacityLevel2 in inducedCapacity.inducedCapacities"
                          :key="inducedCapacityLevel2.id"
                        >
                          {{ inducedCapacityLevel2.code }} - {{ inducedCapacityLevel2.title }}
                        </li>
                      </ul>
                    </li>
                  </ul>
                </v-col>
              </v-row>
            </v-container>
          </v-list-group>
          <v-list-item v-else :key="capacity.id">
            <v-list-item-action>
              <v-checkbox
                v-model="capacity.checked"
                class="capacityCheckbox"
                @click.native.prevent="() => handleCheck(capacity)"
              />
            </v-list-item-action>
            <v-list-item-content>
              <v-list-item-title>{{ capacity.code }} - {{ capacity.title }} <b v-if="isCapacityInduced(capacity)"> -
                Capacité induite par {{getTopLevelCapacities(capacity, capacities.filter(c => c.checked)).map(c =>
                c.code).join(', ')}}</b></v-list-item-title>
              <v-list-item-subtitle>{{ capacity.description }}</v-list-item-subtitle>
            </v-list-item-content>
          </v-list-item>
        </template>
      </v-list>
    </v-col>
  </v-row>
</template>

<script>
import jsog from 'jsog';

import CapacityUtils from '@/components/mixins/CapacityUtils';
import CapacityChip from '@/components/capacity/CapacityChip.vue';

export default {
  mixins: [CapacityUtils],
  components: {CapacityChip},
  props: {
    initialCapacities: Array,
    required: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      capacities: [],
      error: null,
      selectedCapacities: [],
    };
  },
  computed: {
    inducedCapacities() {
      return this.groupAndSort(this.getAllUniqueInducedCapacities(this.selectedCapacities));
    },
    inducedCapacitiesWithoutSelected() {
      return this.inducedCapacities.filter(c => !this.selectedCapacities.map(ca => ca.id).includes(c.id));
    },
    sortedCapacities() {
      return this.groupAndSort(this.capacities);
    },
    sortedSelectedCapacities() {
      return this.groupAndSort(this.selectedCapacities);
    },
  },
  watch: {
    initialCapacities() {
      if (this.selectedCapacities.length === 0) {
        this.selectedCapacities = this.initialCapacities;
        this.updateCapacitiesStatus();
      }
    },
  },
  created() {
    this.queryCapacities();
  },
  methods: {
    queryCapacities() {
      this.$axios
        .get('/api/odas/capacity/list/all')
        .then((response) => {
          this.capacities = jsog.decode(response.data);
          this.updateCapacitiesStatus();
        });
    },
    isCapacitySelected(capacity) {
      return this.selectedCapacities.some(cap => cap.id === capacity.id);
    },
    isCapacityInduced(capacity) {
      return this.inducedCapacitiesWithoutSelected.some(cap => cap.id === capacity.id);
    },
    updateCapacitiesStatus() {
      this.capacities.forEach((capacity) => {
        capacity.checked = this.isCapacitySelected(capacity);
      });
    },
    handleCheck(capacity) {
      // capacity.checked has already been updated by checkbox (v-model)
      if (capacity.checked) {
        this.error = null;
        this.selectedCapacities.push(capacity);
      } else {
        this.selectedCapacities.splice(this.selectedCapacities.findIndex(cap => cap.id === capacity.id), 1);
      }
      this.updateCapacitiesStatus();
      this.$emit('change', this.selectedCapacities);
    },
    reset() {
      this.error = null;
      this.selectedCapacities.splice(0);
      this.updateCapacitiesStatus();
    },
    validate() {
      if (this.required && this.selectedCapacities.length === 0) {
        this.error = 'Vous devez sélectionner au moins une capacité';
        return false;
      }
      return true;
    },
    resetValidation() {
      this.error = null;
    },
  },
};
</script>
