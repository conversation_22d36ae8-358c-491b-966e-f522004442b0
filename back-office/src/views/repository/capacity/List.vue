<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            <h1>
              <v-icon large
                      color="primary">mdi-certificate
              </v-icon>
              {{ $t('ref.capacity.list') }}
            </h1>
            <v-spacer/>
            <v-text-field v-model="search"
                          append-icon="search"
                          label="Search"
                          single-line
                          hide-details/>
          </v-card-title>
          <v-card-text>
            <v-data-table :headers="headers"
                          :items="items.content"
                          :sort-by.sync="pagination.sortBy"
                          :page.sync="pagination.page"
                          :sort-desc.sync="pagination.descending"
                          :items-per-page.sync="pagination.rowsPerPage"
                          :footer-props="{
                            'items-per-page-options': rowsPerPage,
                            'items-per-page-text': 'Nb lignes par page',
                          }"
                          :loading="loading"
                          item-key="id"
                          :server-items-length="items.totalNumberOfElements">
              <v-progress-linear slot="progress"
                                 color="blue"
                                 indeterminate/>
              <template v-slot:item="props">
                <tr @click="props.expand(!props.isExpanded)">
                  <td class="justify-center">
                    <v-tooltip v-if="props.item.inducedCapacities.length > 0"
                               color="grey"
                               bottom>
                      <template v-slot:activator="{ on }">
                        <v-icon v-on="on"
                                class="mr-2">
                          mdi-file-tree
                        </v-icon>
                      </template>
                      <span>Inclut {{props.item.inducedCapacities.length}} capacités</span>
                    </v-tooltip>
                  </td>
                  <td>{{ props.item.code }}</td>
                  <td class="text-left">{{ props.item.title }}</td>
                </tr>
              </template>
              <template v-slot:expanded-item="props">
                <tr>
                  <td :colspan="props.headers.length">
                    <v-row justify="center">
                      <v-col>
                        <v-col cols="12">
                          <b>{{$t('ref.headers.capacity.description')}}:</b>
                          {{ props.item.description }}
                        </v-col>
                        <v-col cols="12"
                               v-if="props.item.inducedCapacities.length > 0">
                          <b>{{$t('ref.headers.capacity.capacities')}}:</b><br/>
                          <capacity-chip v-for="capacity in groupAndSort(props.item.inducedCapacities)"
                                         :key="capacity.id"
                                         :capacity="capacity"
                          />
                        </v-col>
                        <v-col cols="12"
                               v-if="getInducedCapacities(props.item.inducedCapacities).length > 0">
                          <b>{{$t('ref.headers.capacity.inducedCapacities')}}:</b><br/>
                          <capacity-chip v-for="capacity in getInducedCapacities(props.item.inducedCapacities)"
                                         :key="capacity.id"
                                         :capacity="capacity"
                          />
                        </v-col>
                      </v-col>
                    </v-row>
                  </td>
                </tr>
              </template>
              <template slot="no-data">
                <v-alert :value="true"
                         v-if="!loading"
                         outlined
                         color="error"
                         icon="warning">
                  Aucun résultat disponible {{
                  search !== '' ? `pour la recherche suivante: "${ search }"` : ''
                  }}
                </v-alert>
                <div v-else class="text-center">
                  <v-progress-circular indeterminate/>
                </div>
              </template>
              <template v-slot:footer.page-text="props">
                Lignes de {{ props.pageStart }} à {{ props.pageStop }} sur {{ props.itemsLength }}
              </template>
            </v-data-table>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import CapacityUtils from '@/components/mixins/CapacityUtils';
import CapacityChip from '@/components/capacity/CapacityChip.vue';
import jsog from 'jsog';
import _ from 'lodash';

export default {
  mixins: [CapacityUtils],
  components: {CapacityChip},
  data() {
    return {
      items: [],
      search: '',
      loading: true,
      pagination: {
        page: 1,
        sortBy: [], // By default, do not apply sorting. This allows search ranking to do its job.
        descending: [false],
        rowsPerPage: 10,
      },
      rowsPerPage: [10, 25, 50, 100],
      headers: [
        {
          text: '',
          icon: 'mdi-file-tree',
          align: 'left',
          sortable: false,
        },
        {
          text: this.$t('ref.headers.capacity.code'),
          icon: '',
          align: 'left',
          sortable: true,
          value: 'code',
        },
        {
          text: this.$t('ref.headers.capacity.title'),
          icon: '',
          align: 'left',
          sortable: true,
          value: 'title',
        },
      ],
    };
  },
  watch: {
    search: _.debounce(function () {
      this.pagination.page = 1;
      this.getDataFromApi();
    }, 200),
    pagination: {
      handler: _.debounce(function () {
        this.getDataFromApi();
      }, 200),
      deep: true,
    },
  },
  mounted() {
    this.getDataFromApi();
  },
  methods: {
    getDataFromApi() {
      this.loading = true;
      this.$axios.get('/api/odas/capacity/list', {
        params: {
          page: this.pagination.page - 1,
          size: this.pagination.rowsPerPage,
          by: this.pagination.sortBy[0],
          direction: !this.pagination.descending[0] ? 'ASC' : 'DESC',
          filter: this.search,
        },
      })
        .then(result => {
          this.items = jsog.decode(result.data);
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style>
</style>
