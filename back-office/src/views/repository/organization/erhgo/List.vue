<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            <h1>
              <v-icon large color="primary">mdi-web</v-icon>
              {{ $t('ref.erhgoOccupation.list') }}
            </h1>
            <v-spacer/>
            <v-text-field v-model="query"
                          append-icon="search"
                          label="Rehercher"
            />
          </v-card-title>
          <v-card-text>
            <v-data-table id="erhgoList"
                          :headers="headers"
                          :items="occupationPage ? occupationPage.content : []"
                          :loading="erhgoOccupationsService.loading"
                          multi-sort
                          class="elevation-15"
                          item-key="uri"
                          :page.sync="pagination.page"
                          :sort-desc.sync="pagination.descending"
                          :items-per-page.sync="pagination.rowsPerPage"
                          :server-items-length="occupationPage ? occupationPage.totalNumberOfElements : null"
                          :footer-props="{
                            'items-per-page-options': rowsPerPage,
                            'items-per-page-text': 'Nb lignes par page',
                          }"
                          loading-text="Chargement en cours..."
            >
              <template v-slot:item="props">
                <tr>
                  <td class="text-left">{{ props.item.title }}</td>
                  <td class="d-flex align-center">
                    <v-tooltip right>
                      <template v-slot:activator="{ on }">
                        <v-btn text
                               icon
                               color="primary"
                               v-on="on"
                               class="showOccupationButton ml-auto"
                               @click="openOccupationPopin(props.item.id)"
                        >
                          <v-icon>visibility</v-icon>
                        </v-btn>
                      </template>
                      <span>Visualiser</span>
                    </v-tooltip>
                  </td>
                </tr>
              </template>
              <template v-slot:footer.page-text="props">
                Lignes de {{ props.pageStart }} à {{ props.pageStop }} sur {{ props.itemsLength }}
              </template>
              <template slot="no-data">
                <v-alert :value="true"
                         outlined
                         color="error"
                         icon="warning">
                  Aucun résultat disponible {{
                    query !== '' ? `pour la recherche suivante: "${query}"` : ''
                  }}
                </v-alert>
              </template>
            </v-data-table>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-dialog v-model="showPrintPopin" v-if="showPrintPopin" max-width="75%">
      <erhgo-occupation-details-popin
        :occupation-id='this.popinOccupationId'
        @onClose="closeOccupationPopin"
      />
    </v-dialog>
  </v-container>
</template>

<script>
import _ from 'lodash';
import {ErhgoOccupationState} from 'erhgo-api-client';
import ErhgoOccupationsOTService from './ErhgoOccupationsOTService';
import ErhgoOccupationDetailsPopin from '../../erhgo/ErhgoOccupationDetailPopin';

export default {
  components: {ErhgoOccupationDetailsPopin},
  data() {
    return {
      erhgoOccupationsService: ErhgoOccupationsOTService,
      erhgoOccupationState: ErhgoOccupationState,
      showPrintPopin: false,
      popinOccupationId: null,
      headers: [
        {
          text: this.$t('ref.headers.erhgoOccupation.title'),
          sortable: true,
          value: 'title',
        },
        {
          text: this.$t('ref.headers.actions'),
          align: 'right',
          sortable: false,
        },
      ],
      rowsPerPage: [10, 25, 50, 100, 200],
      pagination: {
        page: 1,
        descending: false,
        rowsPerPage: 100,
      },
      query: null,
    };
  },
  watch: {
    pagination: {
      handler: _.debounce(function () {
        this.getDataFromApi();
      }, 200),
      deep: true,
    },
    query: _.debounce(function () {
      this.pagination.page = 1;
      this.getDataFromApi();
    }, 200),
  },
  async created() {
    this.erhgoOccupationsService = new ErhgoOccupationsOTService();
    await this.getDataFromApi();
  },
  methods: {
    async getDataFromApi() {
      await this.erhgoOccupationsService.fetchOccupationPage(
        this.pagination.rowsPerPage,
        this.pagination.page - 1,
        this.pagination.descending,
        this.query?.trim());
    },
    openOccupationPopin(occupationId) {
      this.popinOccupationId = occupationId;
      this.showPrintPopin = true;
    },
    closeOccupationPopin() {
      this.popinOccupationId = null;
      this.showPrintPopin = false;
    },
  },
  computed: {
    occupationPage() {
      return this.erhgoOccupationsService.occupationPage;
    },
  },
};
</script>

<style>
.theme--light.v-data-table .v-data-table-header__sort-badge {
  background-color: rgba(255, 255, 255, 0.18) !important;
  color: rgba(255, 255, 255, 0.87) !important;
}
</style>
