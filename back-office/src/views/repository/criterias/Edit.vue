<template v-if="criteriaService">
  <v-container fluid class="pa-0">
    <v-card>
      <v-form v-if="!!criteria" ref="form" @submit.prevent="submit">
        <v-card-title>
          <h2>Editer un critère</h2>
        </v-card-title>
        <v-card-text>
          <v-row>
            <v-col cols="12">
              <v-text-field
                :rules="[rules.required]"
                v-model="criteria.title"
                label="Titre"
                id="criteriaTitle"
              />
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12">
              <v-text-field
                :rules="[rules.required]"
                v-model="criteria.questionLabel"
                label="Intitulé de la question"
                id="criteriaQuestionLabel"
              />
            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <h3>Valeurs disponibles pour ce critère</h3>
              <span class="text-caption"><a target="_blank" href="https://fontawesome.com/v5.15/icons?d=gallery&p=2">Cliquer ici pour voir les icones disponibles</a></span>
            </v-col>
          </v-row>
          <v-row v-for="criteriaValue in criteria.criteriaValues" :key="criteriaValue.code">
            <v-col>
              <v-text-field
                :rules="[rules.required]"
                v-model="criteriaValue.icon"
                label="Icone"
                id="criteriaIcon"
                :append-icon="`fa-${ criteriaValue.icon }`"
              />
            </v-col>
            <v-col>
              <v-text-field
                v-model="criteriaValue.titleForBO"
                :value="criteriaValue.titleForBO"
                :rules="[value => !!value || 'Requis']"
                label="Titre pour BO :"
              />
            </v-col>
            <v-col>
              <v-text-field
                v-model="criteriaValue.titleForQuestion"
                :value="criteriaValue.titleForQuestion"
                :rules="[value => !!value || 'Requis']"
                label="Titre Pour question :"
              />
            </v-col>
            <v-col>
              <v-text-field
                v-model="criteriaValue.titleStandalone"
                :value="criteriaValue.titleStandalone"
                :rules="[value => !!value || 'Requis']"
                label="Titre seul :"
              />
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-btn large
                 type="submit"
                 :color="submitColor"
                 :loading="criteriaService.loading"
                 id="saveCriteriaButton">
            <span class="mr-2">
               Enregistrer </span>
            <v-icon large
                    right>
              save
            </v-icon>
          </v-btn>
          <v-btn
            outlined
            large
            id="backToCriteriaListButton"
            color="primary"
            @click.stop="$router.push({ name: 'criterias'})">
            <v-icon large
                    left>undo
            </v-icon>
            {{ $t('action.return') }}
          </v-btn>
        </v-card-actions>
      </v-form>
      <div v-else class="text-center">
        <v-progress-circular
          class="my-2"
          :size="50"
          color="primary"
          indeterminate
        />
      </div>
    </v-card>
    <errorDialog v-model="criteriaService.showAPIError"/>
  </v-container>
</template>

<script>
import ErrorDialog from '@/components/common/ErrorDialog.vue';
import CriteriaService from '../../../components/criteria/CriteriaService';

export default {
  name: 'Edit.vue',
  props: {
    criteriaCode: String,
  },
  components: {
    ErrorDialog,
  },
  data() {
    return {
      criteriaService: null,
      rules: {
        required: value => !!value || 'Requis',
        min1: value => !value || value > 0 || 'Minimum 1',
      },
      criteriaTitle: '',
      submitColor: 'primary',
    };
  },
  computed: {
    criteria() {
      return this.criteriaService.criteria?.filter(c => c.code === this.criteriaCode)[0];
    },
  },
  methods: {
    async submit() {
      if (this.$refs.form.validate() && !this.success) {
        await this.criteriaService.saveEditedCriteria(this.criteria);
        this.submitColor = 'success';
        setTimeout(() => this.clear(), 1000);
      }
    },
    clear() {
      this.submitColor = 'primary';
    },
  },
  async created() {
    this.criteriaService = new CriteriaService([]);
  },
};
</script>
