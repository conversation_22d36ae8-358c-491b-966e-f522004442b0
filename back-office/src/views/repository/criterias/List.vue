<template>
  <v-container fluid>
    <v-card>
      <v-card-title>
        <h1 class="my-1" id="questionsTitleCriterias">
          <v-icon large
                  color="primary">question_answer
          </v-icon>
          Questions relatives aux critères
        </h1>
        <v-spacer/>
      </v-card-title>
      <v-card-text>
        <v-data-table
          :headers="headers"
          :items="service.criteria"
          :footer-props="{
                        'items-per-page-options': rowsPerPage,
                        'items-per-page-text': 'Nb lignes par page',
                      }"
          item-key="code"
          class="elevation-15"
          disable-sort
        >
          <template v-slot:item="service">
            <tr>
              <td>{{ service.item.code }}</td>
              <td>{{ service.item.title }}</td>
              <td>{{ service.item.questionLabel }}</td>
              <td>
                <v-btn text
                       icon
                       color="primary"
                       class="mx-1"
                       :to="{ name: 'question_for_criterias_edit', params: {criteriaCode: service.item.code}}">
                  <v-icon>edit</v-icon>
                </v-btn>
              </td>
            </tr>
          </template>
          <template slot="no-data">
            <v-alert :value="true"
                     outlined
                     color="error"
                     icon="warning">
              Aucun résultat disponible
            </v-alert>
          </template>
          <template v-slot:footer.page-text="props">
            Lignes de {{ props.pageStart }} à {{ props.pageStop }} sur {{ props.itemsLength }}
          </template>
        </v-data-table>
      </v-card-text>
    </v-card>
    <v-row class="mr-4 my-3">
      <v-spacer/>
    </v-row>
  </v-container>
</template>

<script>

import CriteriaService from '@/components/criteria/CriteriaService';

export default {
  name: 'List',
  data() {
    return {
      service: null,
      rowsPerPage: [10, 15, 30, 45, 60, 75, 100, 200],
      headers: [
        {
          text: 'Critéres',
        },
        {
          text: 'Intitulé du critère ',
        },
        {
          text: 'Libellé de la question',
        },
        {
          text: 'Action',
        },
      ],
    };
  },
  computed: {
    criterias() {
      return this.service.criterias;
    },
    titleCriteria() {
      return this.service.title;
    },
    questionCriteria() {
      return this.service.questionLabel;
    },
    criteriaValue() {
      return this.service.questionLabel.criteriaValues;
    },
  },
  async created() {
    this.service = new CriteriaService([]);
  },
};
</script>
