<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <list-card :headers="headers"
                   :speedDial=false
                   listRestUrlApi="/api/odas/category/list"
                   countRestUrlApi="/api/odas/category/count">
          <template slot="title">
            <h1>
              <v-icon large
                      color="primary">mdi-star-outline
              </v-icon>
              {{ $t('ref.category.list') }}
            </h1>
          </template>

          <template v-slot:row="{row}">
            <td>{{ row.code }}</td>
            <td class="text-left">{{ row.title }}</td>
          </template>

          <template v-slot:details="{item}">
            <v-col cols="12">
              {{ item.description }}
              <v-col v-for="(level, index) in item.levels"
                     :key="level.id">
                <b>Niveau {{index}} ({{level.score}} points) : {{ level.title }}</b><br/>
                {{ level.description }}
              </v-col>
            </v-col>
          </template>
        </list-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import ListCard from '@/components/common/crud/List.vue';

export default {
  components: {ListCard},
  data() {
    return {
      headers: [
        {
          text: this.$t('ref.headers.category.code'),
          icon: '',
          align: 'left',
          sortable: true,
          value: 'code',
        },
        {
          text: this.$t('ref.headers.category.title'),
          icon: '',
          align: 'left',
          sortable: true,
          value: 'title',
        },
      ],
      actions: [],
    };
  },
};
</script>
