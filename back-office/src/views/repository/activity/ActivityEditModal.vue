<template>
  <v-dialog v-model="show" scrollable>
    <activity-edit-form
      @submit="onActivityUpdated"
      :on-back="close"
      :initial-title="title"
      :origin="origin"
      :type="activityType"
      :id="editActivityId"
    />
  </v-dialog>
</template>

<script>
import { ActivityType } from 'erhgo-api-client';
import Edit from './Edit.vue';

export default {
  components: {ActivityEditForm: Edit},
  props: {
    value: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      default: '',
    },
    editActivityId: {
      type: String,
      default: null,
    },
    origin: String,
    activityType: ActivityType,
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(value) {
        if (!value) {
          this.close();
        }
      },
    },
  },
  methods: {
    onActivityUpdated(activity) {
      this.$emit('submit', activity);
    },
    close() {
      this.$emit('closed');
    },
  },
};
</script>

