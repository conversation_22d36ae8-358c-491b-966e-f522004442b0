<template>
  <v-tooltip top nudge-bottom="50" :disabled="!$slots.tooltip">
    <template v-slot:activator="{ on }">
      <v-list-item-content v-on="on">
        <v-list-item-title>{{ activity.title }}</v-list-item-title>
        <v-list-item-subtitle class="font-italic pl-1" v-if="activity.inducedCapacities">
          &nbsp;{{ activity.inducedCapacities.map((o) => `${o.code} - ${o.title}`).join(', ') | truncate(200) }}
        </v-list-item-subtitle>
      </v-list-item-content>
    </template>
    <span>
      <slot name="tooltip" v-bind:activity="activity"/>
    </span>
  </v-tooltip>
</template>
<script>

export default {
  props: {
    activity: Object,
  },
};
</script>
