import Vue from 'vue';
import MergeActivities from './MergeActivities';
import {v4 as uuid} from 'uuid';
import _ from 'lodash';

export default class MergedActivity {

  mergeActivities: MergeActivities;

  readonly id: string;

  private _loading = false;
  private _description = '';
  private _error = false;

  constructor(mergeActivities: MergeActivities) {
    this.id = uuid();
    this.mergeActivities = mergeActivities;
  }

  async submitMerge() {
    const mergedActivity = {
      id: this.id,
      labels: this.labels,
      inducedCapacities: this.capacities.map(c => c.code!),
      description: this.description,
    };

    const sourceActivities = this.mergeActivities.activities.map(a => a.id!);

    try {
      this._error = false;
      this._loading = true;
      await Vue.$api.mergeActivities({mergedActivity, sourceActivities});
      this.mergeActivities.reset();
    } catch (e) {
      this._error = true;
    } finally {
      this._loading = false;
    }
  }

  get loading() {
    return this._loading || this.mergeActivities.loading;
  }

  get isInvalid() {
    return !this.capacities.length || this.mergeActivities.activities.length < 2;
  }

  get labels() {
    return this.mergeActivities.activities.flatMap(a => a.labels || []);
  }

  get capacities() {
    return _.uniqBy(this.mergeActivities.activities.flatMap(a => a.capacities || []), 'code');
  }

  get description() {
    return this._description || this.mergeActivities.activities.flatMap(a => a.description).filter(a => a).join('\n');
  }

  set description(description) {
    this._description = description;
  }

  get error() {
    return !!this._error;
  }
}
