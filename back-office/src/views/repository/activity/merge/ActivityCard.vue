<template>
  <v-card>
    <v-system-bar
      color="primary"
    >
      <template v-if="!editable">
        Activité {{index+1}}
      </template>
      <template v-else>
        Nouvelle activité
      </template>
      <v-spacer/>
      <v-icon v-if="!editable" @click="activity.remove()">mdi-close</v-icon>
    </v-system-bar>

    <v-container fluid grid-list-lg class="pa-0">
      <v-layout wrap>
        <v-flex xs12>
          <v-card class="flat">
            <v-card-title class="pa-2">
              <b>Libellés</b>
            </v-card-title>
            <ul>
              <li :key="label.uuid" v-for="label in activity.labels">{{label.title}}</li>
            </ul>
          </v-card>
        </v-flex>
        <v-flex xs12>
          <v-divider/>
          <v-card class="flat">
            <v-card-title class="pa-2">
              <b>Capacités</b>
            </v-card-title>
            <capacity-chip v-for="capacity in activity.capacities"
                           :small="true"
                           :key="capacity.id"
                           :capacity="capacity"
            />
          </v-card>
        </v-flex>
        <v-flex xs12>
          <v-divider/>
          <v-card class="flat">
            <v-card-title class="pa-2">
              <b>Descriptif</b>
            </v-card-title>
            <v-textarea :readonly="!editable" v-model="activity.description"/>
          </v-card>
        </v-flex>
        <v-flex v-if="editable">
          <v-dialog v-model="showDialog" :max-width="500">
            <template v-slot:activator="{ on }">
              <v-btn v-on="on" color="primary" @click="showDialog = true" :loading="activity.loading"
                     :disabled="activity.isInvalid">Valider
              </v-btn>
            </template>
            <v-card>
              <v-card-title
                class="text-h5 grey lighten-2"
                primary-title
              >Attention
                <v-spacer/>
                <v-icon color="warning">mdi-warning</v-icon>
              </v-card-title>
              <v-card-text>
                Vous allez fusionner ces activités&nbsp;:
                <ul>
                  <li>Chacune des activités sélectionnées sera remplacée par l'activité fusionnée</li>
                  <li>Les libellés sont préservés et associées à la nouvelle activité</li>
                  <li>Cette action est irréversible</li>
                  <li>Vous aurez ensuite la possibilité de modifier l'activité créée</li>
                </ul>
              </v-card-text>
              <v-divider/>
              <v-card-actions>
                <v-btn
                  color="error"
                  @click="showDialog = false"
                >
                  Annuler
                </v-btn>
                <v-spacer/>
                <v-btn
                  color="success"
                  @click="showDialog = false;activity.submitMerge()"
                >
                  Continuer
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
        </v-flex>

      </v-layout>
    </v-container>
  </v-card>
</template>

<script>
import CapacityChip from '@/components/capacity/CapacityChip';
import Activity from './Activity';
import MergedActivity from './MergedActivity';

export default {
  components: {
    CapacityChip,
  },
  props: {
    activity: [Activity, MergedActivity],
    index: Number,
  },
  data() {
    return {
      editable: false,
      showDialog: false,
    };
  },
  created() {
    this.editable = this.activity instanceof MergedActivity;
  },
};
</script>

<style lang="scss">
.flat {
  box-shadow: none;
}
</style>
