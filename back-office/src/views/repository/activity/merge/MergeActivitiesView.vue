<template>
  <v-layout wrap>
    <v-alert color="success" icon="info" :value="true" v-if="!!mergeActivities.lastCreatedActivityId">
      Les activitiés ont été correctement fusionnées dans une nouvelle activité.
      <v-btn small dark
             :to="{ name: 'activities_edit', params: {id: mergeActivities.lastCreatedActivityId, type: ActivityType.JOB}}">
        modifier cette activité
      </v-btn>
    </v-alert>
    <v-alert color="error" icon="error" :value="mergeActivities.newActivity.error">
      Une erreur technique est survenue&nbsp;: impossible de fusionner ces activités.
    </v-alert>

    <v-flex xs12>
      <autocomplete
        v-model="mergeActivities.selectedActivitiesLabels"
        empty
        searchRestUrlApi="/api/odas/activityLabel/search"
        label="Label de l'activité (maximum 6 activités sélectionnables)"
        :hide-details="false"
        :disabled="mergeActivities.activities.length >= 6"
        :closeOnSelect="mergeActivities.activities.length >= 6"
        item-value="id"
      >
        <template slot="listItem" slot-scope="slotProps">
          <activity-list-item :activity="slotProps.listItem"/>
        </template>
      </autocomplete>
    </v-flex>
    <v-flex xs8>
      <v-layout wrap>
        <v-flex xs4 class="pa-2" v-for="(activity, index) in mergeActivities.activities" :key="activity.uuid">
          <activity-card :activity="activity" :index="index"/>
        </v-flex>
      </v-layout>
    </v-flex>
    <v-flex xs4 class="pl-2" style="border-left: grey solid 2px">
      <activity-card :activity="mergeActivities.newActivity" v-if="mergeActivities.hasActivity()"/>
    </v-flex>
  </v-layout>
</template>

<script>

import { ActivityType } from 'erhgo-api-client';
import Autocomplete from '@/components/common/crud/Autocomplete';
import ActivityListItem from '@/views/repository/activity/ActivityListItem';
import MergeActivities from './MergeActivities';
import ActivityCard from './ActivityCard';

export default {
  components: {
    Autocomplete, ActivityCard, ActivityListItem,
  },
  data: () => ({
    ActivityType,
    mergeActivities: null,
  }),
  created() {
    this.mergeActivities = new MergeActivities();
  },
};
</script>

<style>

</style>
