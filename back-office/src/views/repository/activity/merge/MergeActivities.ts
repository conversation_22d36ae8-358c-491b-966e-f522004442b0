import Activity from './Activity';
import MergedActivity from './MergedActivity';

export default class MergeActivities {

  activities: Activity[] = [];
  newActivity: MergedActivity = new MergedActivity(this);
  lastCreatedActivityId?: string;

  hasActivity() {
    return !!this.activities.length;
  }

  reset() {
    this.lastCreatedActivityId = this.newActivity.labels[0].id;
    this.activities = [];
    this.newActivity = new MergedActivity(this);
  }

  set selectedActivitiesLabels(newSelectedActivitiesLabels) {
    const previousSelectedActivitiesLabelIds = this.selectedActivitiesLabels.map(a => a.id);
    const selectedActivitiesLabelIds = newSelectedActivitiesLabels.map(a => a.id);
    const newActivities = selectedActivitiesLabelIds.filter(labelId => !previousSelectedActivitiesLabelIds.includes(labelId)).map(labelId => new Activity(labelId, this));
    this.activities = [...this.activities, ...newActivities];
    newActivities.forEach(a => a.fetch());
  }

  get selectedActivitiesLabels() {
    return this.activities.flatMap(a => a.labels || []);
  }

  removeActivity(activity: Activity) {
    this.activities = this.activities.filter(a => a.labelId !== activity.labelId);
  }

  get loading() {
    return !!this.activities.filter(a => a.loading).length;
  }
}
