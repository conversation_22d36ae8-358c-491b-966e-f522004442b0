import {ActivityLabel, CapacityDetail} from 'erhgo-api-client';
import Vue from 'vue';
import MergeActivities from './MergeActivities';

export default class Activity {

  capacities: CapacityDetail[] | undefined = [];
  readonly labelId: string;
  id?: string;
  labels: ActivityLabel[] | undefined = [];
  description: string | undefined = '';
  loading = false;
  mergeActivities: MergeActivities;

  constructor(labelId: string, mergeActivities: MergeActivities) {
    this.labelId = labelId;
    this.mergeActivities = mergeActivities;
  }

  async fetch() {
    this.loading = true;
    try {
      const activity = (await Vue.$api.getActivity(this.labelId)).data;
      this.capacities = activity.inducedCapacities;
      this.labels = activity.labels;
      this.description = activity.description;
      this.id = activity.id;
    } finally {
      this.loading = false;
    }
    return this;
  }

  remove() {
    this.mergeActivities.removeActivity(this);
  }
}
