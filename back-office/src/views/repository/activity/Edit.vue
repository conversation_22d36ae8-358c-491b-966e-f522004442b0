<template>
  <v-container fluid class="pa-0">
    <v-form ref="form" lazy-validation @submit.prevent="submit">
      <v-card>
        <v-card-title>
          <h2>
            <v-icon large
                    right>mdi-briefcase-outline
            </v-icon>
            {{ $t(`ref.activity.${isCreate ? 'create' : 'edit'}.${type}`) }}
          </h2>
        </v-card-title>
        <v-card-text>
          <v-row class="fill-height">
            <v-col cols="12">
              <div>
                <h3>Libellés</h3>
                <v-text-field :prepend-icon="index > 0 ? 'delete_forever' : null"
                              v-for="(alternativeLabel, index) in labels"
                              @click:prepend="removeAlternativeLabel(alternativeLabel)"
                              :append-icon="index > 0 ? 'star' : null"
                              @click:append="setAsDefault(alternativeLabel)"
                              :key="alternativeLabel.id"
                              v-model="alternativeLabel.title"
                              :label="$t(`form.activity.${index === 0 && labels.length > 1?'default':'alternative'}Label`)"
                              class="alternativeLabel_textInput"
                              :rules="[v => (!!v || $t('form.activity.message.title.required'))]"
                />
                <v-btn color="primary" title="Ajouter un libellé alternatif" @click="addAlternativeLabel()"
                       id="alternativeLabel_addButton">
                  <v-icon>add</v-icon>
                  Ajouter un libellé alternatif
                </v-btn>
              </div>
              <div>
                <h3>Description</h3>
                <v-textarea v-model="description"
                            :counter="2000"
                            :rules="[v => v.length <= 2000 || 'Texte trop long']"
                            :label="$t('form.activity.description')"/>
              </div>
              <div>
                <h3>Capacités associées à cette activité</h3>
                <selectable-list ref="capacities" @change="onCapacityListChanged"
                                 :initial-capacities="this.activity.inducedCapacities" required/>
              </div>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-btn large
                 color="grey lighten-5"
                 id="backToActivityListButton"
                 @click="onBack ? onBack() : $router.push({ name: 'activities_repository', params: {type}})">
            <v-icon small
                    left>undo
            </v-icon>
            {{$t('action.return')}}
          </v-btn>
          <v-spacer/>
          <v-btn width="50%"
                 large
                 type="submit"
                 :color="submitColor"
                 id="saveActivityButton"
                 :loading="loading">
            {{submitColor === 'success' ? $t(`action.${isCreate?'created':'updated'}`) :
            $t(`action.${isCreate?'create':'update'}`)}} &nbsp;
            <v-icon large
                    right>{{submitIcon}}
            </v-icon>
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-form>
    <errorDialog v-model="showAPIError"/>
    <v-dialog :value="true" v-if="!!aboutToDelete" :max-width="500">
      <v-card>
        <v-card-title
          class="text-h5 grey lighten-2"
          primary-title
        >Attention
          <v-spacer/>
          <v-icon color="warning">mdi-warning</v-icon>
        </v-card-title>
        <v-card-text>
          Vous allez supprimer un libellé utilisé&nbsp;:
          <ul>
            <li>Soit dans la qualification d'un job ESCO</li>
            <li>Soit sur un métier erhgo</li>
            <li>Soit par un candidat, pour définir une expérience</li>
            <li>Soit dans une cartographie de poste</li>
          </ul>
          <br/>
          Là où il est utilisé, le libellé supprimé sera remplacé par le libellé par défaut (premier de la liste).
          <br/>
          <b>Cette action sera appliquée après avoir cliqué sur "mettre à jour" et sera alors irréversible.</b>
        </v-card-text>
        <v-divider/>
        <v-card-actions>
          <v-btn
            color="error"
            @click="aboutToDelete = null"
          >
            Annuler
          </v-btn>
          <v-spacer/>
          <v-btn
            color="success"
            @click="doDelete(aboutToDelete);aboutToDelete = null;"
          >
            Continuer
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { v4 as uuid } from 'uuid';
import { ActivityType } from 'erhgo-api-client';
import ErrorDialog from '@/components/common/ErrorDialog.vue';

import SelectableList from '../capacity/SelectableList.vue';

const getEmptyActivity = () => ({
  id: uuid(),
  description: '',
  inducedCapacities: [],
  labels: [],
});

export default {
  components: {
    SelectableList, ErrorDialog,
  },
  props: {
    onBack: Function,
    initialTitle: String,
    id: String,
    origin: String,
    type: ActivityType,
  },
  data: () => ({
    activity: getEmptyActivity(),

    labels: [{
      id: uuid(), title: '', isNew: true,
    }],
    description: '',

    dialog: false,

    submitColor: 'primary',
    submitIcon: 'save',
    loading: false,
    showAPIError: false,
    aboutToDelete: null,
  }),
  watch: {
    initialTitle() {
      this.refreshInitialTitle();
    },
  },
  created() {
    if (this.isCreate) {
      this.refreshInitialTitle();
    } else {
      this.getActivity();
    }
  },
  computed: {
    isCreate() {
      return !this.id;
    },
  },
  methods: {
    refreshInitialTitle() {
      this.labels[0].title = this.initialTitle;
    },
    async getActivity() {
      this.loading = true;

      try {
        this.activity = (await this.$api.getActivity(this.id)).data;
        if (this.activity.description) {
          this.description = this.activity.description;
        }
        this.labels = this.activity.labels.map(l => ({...l, isNew: false}));
      } catch (e) {
        this.showAPIError = true;
        this.logError(e);
      } finally {
        this.loading = false;
      }
    },
    onCapacityListChanged(capacities) {
      this.activity.inducedCapacities = capacities;
    },
    async submit() {
      if (this.$refs.form.validate() && this.$refs.capacities.validate()) {
        const {description, origin} = this;
        const {id} = this.activity;
        const labels = this.labels.filter(l => l.title.length).map((l, position) => ({...l, position}));
        const inducedCapacities = this.activity.inducedCapacities.map(c => c.code);

        this.loading = true;

        try {
          await this.$api.saveActivity(this.type, {
            id, description, origin, labels, inducedCapacities,
          });
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          this.labels = labels.map(({isNew, ...l}) => l);
          this.loading = false;
          this.submitColor = 'success';
          this.submitIcon = 'mdi-check-outline';
          const activityLabel = {...this.labels.filter(l => l.id === this.id)[0] || this.labels[0], inducedCapacities: [...this.activity.inducedCapacities]};
          this.$emit('submit', activityLabel);
          if (this.isCreate) {
            setTimeout(() => this.clear(), 1000);
          }
        } catch (e) {
          this.showAPIError = true;
          this.logError(e);
        } finally {
          this.loading = false;
        }
      }
    },
    clear() {
      this.description = '';
      this.labels = [];
      this.addAlternativeLabel();
      this.submitColor = 'primary';
      this.submitIcon = 'save';
      this.activity = getEmptyActivity();
      // Ensure component is still mounted
      if (this.$refs.form) {
        this.$refs.capacities.reset();
        this.$refs.form.resetValidation();
        this.$refs.capacities.resetValidation();
      }
    },
    addAlternativeLabel() {
      this.labels = [...this.labels, {
        id: uuid(), title: '', isNew: true,
      }];
    },
    async removeAlternativeLabel(label) {
      const isDeletable = label.isNew || (await this.ensureLabelIsDeletable(label));
      if (isDeletable) {
        this.doDelete(label);
      } else {
        this.aboutToDelete = label;
      }
    },
    doDelete(label) {
      this.labels = this.labels.filter(l => l.id !== label.id);
    },
    async ensureLabelIsDeletable(label) {
      let deletable = false;
      try {
        this.loading = true;
        deletable = (await this.$api.isActivityLabelDeletable(label.id)).data;
      } catch (e) {
        this.showAPIError = true;
        this.logError(e);
      } finally {
        this.loading = false;
      }
      return deletable;
    },
    setAsDefault(label) {
      this.labels = [label, ...this.labels.filter(l => l !== label)];
      this.labels.forEach((l, index) => l.position = index);
    },
  },
};
</script>

<style>

</style>
