<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card flat>
          <v-card-title>
            <h1>
              <v-icon large
                      color="primary">mdi-briefcase-outline
              </v-icon>
              {{ $t('ref.activity.list') }}
            </h1>
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="6">
                <v-text-field
                  v-model="query"
                  append-icon="search"
                  label="Rechercher"
                  single-line
                  hide-details/>
              </v-col>
              <v-col cols="6">
                <v-select :items="users"
                          clearable
                          v-model="userId"
                          placeholder="Filtrer par administrateur"
                          :menu-props="{ offsetY: true }"
                          :item-text="backOfficeUserService.getUserName"
                          item-value="id"
                          hide-details />
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="6">
                <v-autocomplete
                  v-model="activitySearchService.selectedCapacities"
                  label="Capacités"
                  :items="capacities"
                  clearable
                  deletable-chips
                  hide-selected
                  hint="Sélectionner des capacités pour filtrer les activités associées "
                  persistent-hint
                  chips
                  small-chips
                  multiple
                  :search-input.sync="capacitySearch"
                  @change="capacitySearch = ''"
                  no-data-text="Aucun résultat"
                />
              </v-col>
              <v-col cols="6">
                <v-checkbox
                  v-model="activitySearchService.isCapacityRecursive"
                  label="Considérer les capacités induites récursivement"
                />
              </v-col>
            </v-row>
            <v-col>
              <v-data-table :headers="headers"
                            :items="items"
                            :sort-by.sync="pagination.sortBy"
                            :page.sync="pagination.page"
                            :sort-desc.sync="pagination.descending"
                            :items-per-page.sync="pagination.rowsPerPage"
                            :footer-props="{
                              'items-per-page-options': rowsPerPage,
                              'items-per-page-text': 'Nb lignes par page',
                            }"
                            class="elevation-15"
                            item-key="id"
                            id="activityList"
                            :server-items-length="totalNumberOfElements">
                <template v-slot:item="props">
                  <tr @click="props.expand(!props.isExpanded)">
                    <td class="justify-center">
                      <v-tooltip v-if="props.item.inducedCapacities.length > 0"
                                 color="grey"
                                 bottom>
                        <template v-slot:activator="{ on }">
                          <v-icon v-on="on"
                                  class="mr-2">
                            mdi-file-tree
                          </v-icon>
                        </template>
                        <span>Inclut {{props.item.inducedCapacities.length}} capacités</span>
                      </v-tooltip>
                    </td>
                    <td class="text-left">{{ props.item.title }}</td>
                    <td class="text-left">
                      <span>{{ `Modifiée par ${props.item.lastModifiedBy ? props.item.lastModifiedBy : 'inconnu' }` }}</span>
                    </td>
                    <td class="text-left">
                      <span>Modifiée le {{ props.item.updatedDate | formatDateTime }}</span>
                    </td>
                    <td class="text-right">
                      <v-btn text
                             icon
                             color="primary"
                             class="mr-2 text-center"
                             :to="{ name: 'activities_edit', params: {id:props.item.id, type}}">
                        <v-icon>edit</v-icon>
                      </v-btn>
                    </td>
                  </tr>
                </template>
                <template v-slot:expanded-item="props">
                  <tr>
                    <td :colspan="props.headers.length">
                      <v-row>
                        <v-col>
                          <v-col cols="12">
                            <b>
                              {{$t('ref.headers.activity.description')}}:
                            </b> {{ props.item.description }}
                          </v-col>
                          <v-col cols="12"
                                 v-if="props.item.inducedCapacities.length > 0">
                            <b>{{$t('ref.headers.activity.capacities')}}:</b><br/>
                            <capacity-chip v-for="capacity in groupAndSort(props.item.inducedCapacities)"
                                           :key="capacity.id"
                                           :capacity="capacity"
                            />
                          </v-col>
                          <v-col cols="12"
                                 v-if="getInducedCapacities(props.item.inducedCapacities).length > 0">
                            <b>{{$t('ref.headers.activity.inducedCapacities')}}:</b><br/>
                            <capacity-chip v-for="capacity in getInducedCapacities(props.item.inducedCapacities)"
                                           :key="capacity.id"
                                           :capacity="capacity"
                            />
                          </v-col>
                        </v-col>
                      </v-row>
                    </td>
                  </tr>
                </template>
                <template slot="no-data">
                  <v-alert :value="true"
                           v-if="!loading && items"
                           outlined
                           color="error"
                           icon="warning">
                    Aucun résultat disponible {{
                      query !== '' ? `pour la recherche suivante: "${ query }"` : ''
                    }}
                  </v-alert>
                  <div v-else class="text-center">
                    <v-progress-circular id="activityList_loading" indeterminate/>
                  </div>
                </template>
                <template v-slot:footer.page-text="props">
                  Lignes de {{ props.pageStart }} à {{ props.pageStop }} sur {{ props.itemsLength }}
                </template>
              </v-data-table>

              <v-row class="mx-1 my-3">
                <v-spacer/>
                <v-tooltip color="red darken-1"
                           left>
                  <template v-slot:activator="{ on }">
                    <v-btn fab
                           dark
                           color="red darken-1"
                           v-on="on"
                           class="text-center"
                           id="createActivityButton"
                           :to="{ name: 'activities_create', params: {type}}">
                      <v-icon>mdi-plus</v-icon>
                    </v-btn>
                  </template>
                  <span class="font-weight-bold">{{$t('action.create')}}</span>
                </v-tooltip>
              </v-row>
            </v-col>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import {ActivityType} from 'erhgo-api-client';
import CapacityUtils from '@/components/mixins/CapacityUtils';
import CapacityChip from '@/components/capacity/CapacityChip.vue';

import ActivitySearchService from './ActivitySearchService';
import BackOfficeUserService from '../../../components/services/BackOfficeUserService';

export default {
  mixins: [CapacityUtils],
  components: {CapacityChip},
  props: {
    type: ActivityType,
  },
  computed: {
    users() {
      return this.backOfficeUserService.userList;
    },
    query: {
      get() {
        return this.activitySearchService.query;
      },
      set(query) {
        this.activitySearchService.query = query;
      },
    },
    userId: {
      get() {
        return this.activitySearchService.userId;
      },
      set(userId) {
        this.activitySearchService.userId = userId;
      },
    },
    loading() {
      return this.activitySearchService.loading;
    },
    pagination() {
      return this.activitySearchService.pagination;
    },
    items() {
      return this.activitySearchService.activities;
    },
    capacities() {
      return this.activitySearchService.capacities;
    },
    totalNumberOfElements() {
      return this.activitySearchService.totalNumberOfElements;
    },
  },
  async created() {
    this.backOfficeUserService = new BackOfficeUserService();
    await this.backOfficeUserService.fetchBackOfficeUserList();
  },
  data() {
    return {
      backOfficeUserService: BackOfficeUserService,
      activitySearchService: new ActivitySearchService(this.type),
      ActivityType,
      capacitySearch: '',
      rowsPerPage: [10, 25, 50, 100, 200],
      headers: [
        {
          text: '',
          icon: 'mdi-file-tree',
          align: 'left',
          sortable: false,
          value: 'isTree',
        },
        {
          text: this.$t('ref.headers.activity.title'),
          icon: '',
          align: 'left',
          sortable: true,
          value: 'title',
        },
        {
          text: this.$t('ref.headers.who'),
          icon: '',
          align: 'left',
          sortable: false,
        },
        {
          text: this.$t('ref.headers.when'),
          icon: '',
          align: 'left',
          sortable: false,
        },
        {
          text: this.$t('ref.headers.actions'),
          align: 'right',
          icon: '',
          sortable: false,
        },
      ],
    };
  },
};
</script>

<style lang="scss" scoped>
</style>
