<template>
  <v-container fluid>
    <v-row>
      <v-col>
        <v-card class="pt-3">
          <v-card-title class="mb-3">
            <h1 class="d-flex align-center">
              <v-icon large class="mr-2" color="primary">recent_actors</v-icon>
              {{ $t(`ref.user.list`) }}
              {{ userPage ? `(${userPage.totalNumberOfElements})` : "" }}
              <v-btn
                :loading="loadingUsers"
                color="secondary"
                dark
                @click="exportUsers"
                class="ml-3"
                small
              >
                <v-icon>archive</v-icon>
                Export complet
              </v-btn>
            </h1>
            <v-spacer />
            <v-btn
              :loading="loadingSimpleUsers"
              color="secondary"
              dark
              @click="exportSimpleUsersInfo"
              class="ml-3"
              small
            >
              <v-icon>archive</v-icon>
              Export simple
            </v-btn>
            <v-spacer />
            <downloader-btn
              outlined
              class="ml-3"
              small
              :download="exportCandidatures"
              :title="`candidatures-global.csv`"
              extension="csv"
            >
              <v-icon>archive</v-icon>
              Exporter toutes les candidatures
            </downloader-btn>
            <v-spacer />
            <user-create-btn />
            <v-spacer />
            <v-text-field
              v-model="query"
              append-icon="search"
              label="Chercher par nom / prénom / email / id"
            />
            <v-spacer />
          </v-card-title>
          <v-card-text>
            <user-channel-selector-btn
              :selected-users="userSearchService.selectedUsers"
              :user-management-service="userManagementService"
              :organizations-to-add="organizations"
              :organizations-to-remove="organizations"
              @finish="userSearchService.selectedUsers = []"
              class="pb-8"
            />
            <v-data-table
              :headers="headers"
              :items="userPage ? userPage.content : []"
              :server-items-length="
                userPage ? userPage.totalNumberOfElements : null
              "
              :loading="userSearchService.loading"
              class="elevation-15"
              :no-data-text="
                isEmptyPage
                  ? 'Pas d\'autre individu dans le système'
                  : 'Aucun résultat'
              "
              :page.sync="pagination.page"
              :items-per-page.sync="pagination.rowsPerPage"
              item-key="id"
              :footer-props="{
                'items-per-page-options': rowsPerPage,
                'items-per-page-text': 'Nb lignes par page',
              }"
              v-model="userSearchService.selectedUsers"
              loading-text="Chargement en cours..."
              show-select
              id="userList"
            >
              <template v-slot:item="props">
                <user-list-item
                  :item-props="props"
                  :index="userPage.content.indexOf(props.item)"
                />
              </template>
              <template v-slot:footer.page-text="props">
                <span v-if="showFooter">
                  Lignes de {{ props.pageStart }} à
                  {{ props.pageStart + userPage.numberOfElementsInPage - 1 }}
                </span>
              </template>
            </v-data-table>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import UserSearchService from '@/components/services/UserSearchService';
import _ from 'lodash';
import moment from 'moment';
import UserListItem from './ListItem';
import UserCreateBtn from './edit/UserCreateBtn';
import UserChannelSelectorBtn from '@/views/setup/user/UserChannelSelectorBtn';
import UserManagementService from '@/components/services/UserManagementService';
import DownloaderBtn from 'odas-plugins/DownloaderBtn.vue';
import DownloadUtils from '@/components/mixins/DownloadUtils';

export default {
  name: 'FrontUserList',
  components: {
    UserListItem,
    UserChannelSelectorBtn,
    UserCreateBtn,
    DownloaderBtn,
  },
  mixins: [DownloadUtils],
  data() {
    return {
      userManagementService: new UserManagementService(),
      userSearchService: UserSearchService,
      rowsPerPage: [10, 25, 50, 100, 200],
      pagination: {
        page: 1,
        rowsPerPage: 10,
      },
      query: '',
      headers: [
        {
          text: this.$t('ref.headers.user.createdAt'),
          sortable: false,
        },
        {
          text: this.$t('ref.headers.user.lastName'),
          sortable: false,
        },
        {
          text: this.$t('ref.headers.user.firstName'),
          sortable: false,
        },
        {
          text: this.$t('ref.headers.user.email'),
          sortable: false,
        },
        {
          text: this.$t('ref.headers.user.enabled'),
          sortable: false,
        },
        {
          text: this.$t('ref.headers.user.isFromHandicap'),
          sortable: false,
        },
        {
          text: this.$t('ref.headers.user.handicapModeEnabled'),
          sortable: false,
        },
        {
          text: this.$t('ref.headers.user.candidaturesCount'),
          sortable: false,
        },
        {
          text: 'Étape',
          sortable: false,
        },
        {
          text: 'Taux complétude',
          sortable: false,
        },
        {
          text: this.$t('ref.headers.actions'),
          sortable: false,
        },
      ],
      loadingUsers: false,
      loadingSimpleUsers: false,
      organizations: [],
    };
  },
  async created() {
    this.userSearchService = new UserSearchService();
    await this.getDataFromApi();
    this.organizations = (await this.$api.getAllRecruiters()).data;
  },
  watch: {
    pagination: {
      handler: _.debounce(async function () {
        await this.getDataFromApi();
      }, 200),
      deep: true,
    },
    query: _.debounce(function () {
      this.pagination.page = 1;
      this.getDataFromApi();
    }, 200),
  },
  methods: {
    async getDataFromApi() {
      await this.userSearchService.fetchUserPage(
        this.pagination.rowsPerPage,
        this.pagination.page - 1,
        this.query,
      );
    },
    async exportUsers() {
      this.loadingUsers = true;
      try {
        await this.download(
          async () => await this.$api.exportUsers({ deanonymizedUser: true }),
          `utilisateurs_${moment().format('MM-DD-YYYY_HH-mm-ss')}.csv`,
        );
      } finally {
        this.loadingUsers = false;
      }
    },
    async exportSimpleUsersInfo() {
      this.loadingSimpleUsers = true;
      try {
        await this.download(
          async () => await this.$api.getSimpleUsersInfoExport(),
          `utilisateurs_simple_info_${new Date()
            .toISOString()
            .slice(0, 10)}.csv`,
        );
      } finally {
        this.loadingSimpleUsers = false;
      }
    },
    async exportCandidatures() {
      return (
        await this.$api.exportCandidatures(undefined, undefined, {
          responseType: 'blob',
        })
      ).data;
    },
  },
  computed: {
    userPage() {
      return this.userSearchService.userPage;
    },
    // hack for keycloak not handling count with search
    isEmptyPage() {
      return (
        this.userPage &&
        this.userPage.numberOfElementsInPage === 0 &&
        this.pagination.page > 1
      );
    },
    showFooter() {
      return (
        !this.isEmptyPage &&
        this.userPage &&
        this.userPage.numberOfElementsInPage > 0
      );
    },
  },
};
</script>
