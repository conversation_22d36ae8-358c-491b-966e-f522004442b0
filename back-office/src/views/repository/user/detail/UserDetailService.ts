import SafeService from 'odas-plugins/SafeService';
import asyncDebounce from 'async-debounce';

import {
  Behavior,
  OrganizationSummary,
  QuestionsSumupForUser,
  UserContactInfo,
  UserCriteriaValue,
  UserNote as UserNoteAPI,
  UserProfileDetailWithCapacities,
  UserProfileProgress,
  UserRegistrationState,
} from 'erhgo-api-client';
import Vue from 'vue';
import keycloakService from 'odas-plugins/KeycloakService';
import { v4 as uuid } from 'uuid';
//@ts-ignore
import store from '@/store';
import { AxiosError } from 'axios';

class UserNote extends SafeService {

  private _savedNote = false;

  constructor(private _userNote: UserNoteAPI, private _isNew: boolean) {
    super();
  }

  get text() {
    return this._userNote.text;
  }

  set text(text) {
    this._userNote.text = text;
    this.debouncedSaveNote.fn();
  }

  get id() {
    return this._userNote.id;
  }

  get organizationId() {
    return this._userNote.organizationId;
  }

  set organizationId(organizationId) {
    this._userNote.organizationId = organizationId;
    this.debouncedSaveNote.fn();
  }

  get userId() {
    return this._userNote.userId;
  }

  get createdBy() {
    return this._userNote.createdBy;
  }

  get createdDate() {
    return this._userNote.createdDate;
  }

  get modificationDate() {
    return this._userNote.modificationDate;
  }

  get modifiedBy() {
    return this._userNote.modifiedBy;
  }

  get savedNote() {
    return this._savedNote;
  }

  private debouncedSaveNote = asyncDebounce(async () => {
    await this.safeCall(async () => {
      const {id, userId, text, organizationId} = this._userNote;
      await Vue.$api.saveUserNote({id, userId, text, organizationId});
      this._savedNote = true;
      if (this._isNew) {
        this._isNew = false;
      } else {
        this._userNote.modifiedBy = keycloakService.fullname;
        this._userNote.modificationDate = new Date();
      }
      setTimeout(() => {
        this._savedNote = false;
      }, 5000);
    });
  }, 500);

  async remove() {
    await this.safeCall(async () => {
      const {id, userId} = this._userNote;
      await Vue.$api.deleteUserNote({id, userId});
      this._savedNote = true;
      setTimeout(() => {
        this._savedNote = false;
      }, 5000);
    });
  }
}

export default class UserDetailService extends SafeService {

  private _receiveJobOfferEmails = false;
  private _mailUser = '';
  private _userInfo: UserContactInfo | null = null;
  private _userDetail: UserProfileDetailWithCapacities | null = null;
  private _progress: UserProfileProgress | null = null;
  private _behaviors: Behavior[] | null = null;
  private _extraProfessional: QuestionsSumupForUser | null = null;
  private _registrationState: UserRegistrationState | null = null;
  private _userCriteria: UserCriteriaValue[] | null = null;
  private _initialized = false;
  private _recruiters: OrganizationSummary[] = [];
  private _notes: UserNote[] = [];


  async fetchUserInfo() {
    await this.safeCall(async () => {
      try {
        this._userInfo = (await Vue.$api.getUserContactInfo(this._userId)).data;
        this._mailUser = this._userInfo.contactInformation.email;
      } catch (e) {
        // Avoid logging error for 404
        if ((e as AxiosError)?.response?.status !== 404) {
          throw e;
        }
      }
    });
  }

  async fetchUserDetailWithCapacities() {
    await this.safeCall(async () => {
      this._userDetail = (await Vue.$api.getUserProfileDetailWithCapacities(this._userId)).data;
    });
  }

  async fetchProgress() {
    await this.safeCall(async () => {
      this._progress = (await Vue.$api.getUserProfileProgress(this._userId, this._organizationCode || undefined)).data;
    });
  }

  async fetchBehaviors() {
    await this.safeCall(async () => {
      this._behaviors = (await Vue.$api.getUserBehaviorDetails(this._userId)).data;
    });
  }

  async fetchExtraProfessional() {
    await this.safeCall(async () => {
      this._extraProfessional = (await Vue.$api.getQuestionsSumupForUser(this._userId)).data;
    });
  }

  async fetchRegistrationState() {
    await this.safeCall(async () => {
      this._registrationState = (await Vue.$api.getUserRegistrationState(this._userId)).data;
    });
  }

  async fetchNotes() {
    await this.safeCall(async () => {
      this._notes = (await Vue.$api.getUserNotes(this._userId)).data.map(u => new UserNote(u, false));
    });
  }

  async fetchUserCriteria() {
    await this.safeCall(async () => {
      this._userCriteria = (await Vue.$api.getUserCriterias(this._userId)).data;
    });
  }


  async fetchRecruiters() {
    await this.safeCall(async () => {
      this._recruiters = (await Vue.$api.getAllRecruiters()).data;
    });

  }

  async fetchReceiveJobOffers() {
    await this.safeCall(async () => {
      this._receiveJobOfferEmails = !!(await Vue.$api.getUserJobOffersOptIn(this._userId)).data.value;
    });
  }

  async fetchAll() {
    try {
      await this.fetchUserInfo();
      if (this._userInfo) {
        await Promise.all([
          this.fetchUserDetailWithCapacities(),
          this.fetchProgress(),
          this.fetchBehaviors(),
          this.fetchExtraProfessional(),
          this.fetchRegistrationState(),
          this.fetchNotes(),
          this.fetchUserCriteria(),
          this.fetchRecruiters(),
          this.fetchReceiveJobOffers(),
        ]);
      }
    } finally {
      this._initialized = true;
    }
  }

  async deleteUser() {
    await this.safeCall(async () => {
      await Vue.$api.deleteUser(this._userId);
    });
  }

  constructor(private _userId: string, private _organizationCode: string | undefined) {
    super();
  }

  get userInfo() {
    return this._userInfo;
  }

  get experiences() {
    return this._userDetail?.experiences || [];
  }

  get channels() {
    return this._userDetail?.generalInformation?.channels || [];
  }

  get isPrivate() {
    return !!this._userDetail?.generalInformation?.isPrivate;
  }

  get progress() {
    return this._progress;
  }

  get behaviors() {
    return this._behaviors;
  }

  get extraProfessional() {
    return this._extraProfessional;
  }

  get userId() {
    return this._userId;
  }

  get registrationState() {
    return this._registrationState;
  }

  get initialized() {
    return this._initialized;
  }

  get userCriteria() {
    return this._userCriteria;
  }

  get notes() {
    return this._notes;
  }

  get organizationIdOfNotes() {
    return this._notes.map(n => n.organizationId);
  }

  get recruiters() {
    return this._recruiters.filter(r => this.organizationIdOfNotes.includes(r.id) || this.channels.includes(r.title));
  }

  addNote() {
    let organizationId = this.recruiters.filter(r => r.code === store.getters.organizationLoaded?.code)[0]?.defaultProject?.id;
    organizationId = organizationId || this.recruiters[0]?.id;
    const note = new UserNote({
      userId: this.userId,
      id: uuid(),
      text: '',
      organizationId,
      createdDate: new Date(),
      createdBy: keycloakService.fullname,
      modifiedBy: '',
      modificationDate: new Date(),
    }, true);
    this._notes = [...this._notes, note];
    return note;
  }

  get mayAddNote() {
    return !this._notes.length || this._notes.every(n => n.id && n.text);
  }

  async removeNote(note: UserNote) {
    await note.remove();
    this._notes = this._notes.filter(n => n.id !== note.id);
  }
}

