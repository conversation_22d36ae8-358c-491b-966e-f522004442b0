<template>
  <v-dialog v-model="showDialog" persistent width="50%">
    <template v-slot:activator="{ on, attrs }">
      <v-btn class="mr-4"
             color="red"
             v-bind="attrs"
             v-on="on"
             @click="showDialog = true"
      >
        <v-icon left>
          delete
        </v-icon>
        Supprimer l'utilisateur
      </v-btn>
    </template>
    <v-card :disabled="userDetailService.loading">
      <v-card-title
        class="text-h5 grey lighten-2"
        primary-title
      >
        Supprimer l'utilisateur
        <v-spacer/>
      </v-card-title>
      <v-card-text>
        <div class="my-4">
          <v-icon color="error">error</v-icon>
          Attention, vous êtes sur le point de supprimer l'utilisateur.
          Cette action a pour conséquences&nbsp;:
          <ul>
            <li>De supprimer toutes les informations associées à l'utilisateur (candidatures, expériences,...)</li>
            <li>D'empêcher l'envoi de mail à l'utilisateur (email blacklisté dans sendinblue)</li>
          </ul>
        </div>
      </v-card-text>
      <v-divider/>
      <v-card-actions>
        <v-btn
          color="primary"
          class='float-left'
          @click="deleteUser()"
          :loading="userDetailService.loading"
        >
          Valider
        </v-btn>
        <v-spacer/>
        <v-btn
          color="error"
          class='float-right'
          @click="showDialog = false"
        >
          Annuler
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import UserDetailService from './UserDetailService';

export default {
  name: 'DeleteUserDialog',
  data() {
    return {
      showDialog: false,
    };
  },
  props: {
    userDetailService: {
      type: UserDetailService,
      required: true,
    },
  },
  methods: {
    async deleteUser() {
      await this.userDetailService.deleteUser();
      await this.$router.push({path: '/repository/front-users'});
    },
  },
};
</script>
