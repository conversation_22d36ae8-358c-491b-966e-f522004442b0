<template>
  <v-card outlined width="100%">
    <v-card-title class="justify-space-between">
      <div class="clickable" @click="visible=!visible">
        <v-icon v-if="visible">mdi-minus</v-icon>
        <v-icon v-else>mdi-plus</v-icon>
        [{{ $t(`experience.${experience.experienceType}`) }}] {{ experience.jobTitle }}
        {{
          experience.organizationName ? ` chez ${experience.organizationName}` : ''
        }}
        <em class="ml-2" v-if="experience.duration">
          ({{
            `${$t(`durationType.${experience.durationType}`)} ${$t(`duration.${experience.duration}`)}`
          }})
        </em>
      </div>
      <div>
        <card-btn v-if="experience.erhgoOccupationId" no-border @click="displayModal = true">
          <em class="text-body-1">
            Métier erhgo associé&nbsp;:&nbsp;
            <span class="text-decoration-underline">
              {{ experience.erhgoOccupationTitle }}
            </span>
          </em>
        </card-btn>
      </div>
    </v-card-title>
    <v-card-text v-if="visible">
      <v-row v-if="experience.erhgoOccupationId">
        <ul class="text-body-2" style="list-style: none;">
          <li class="my-2">
            <v-icon small color="primary">arrow_forward_ios</v-icon>
            Niveau de maîtrise du métier&nbsp;: {{ masteryLevel }}
          </li>
          <li class="my-2">
            <v-icon small color="primary">arrow_forward_ios</v-icon>
            Nombre de capacités du métier&nbsp;: {{ experience.erhgoOccupationTotalCapacities }}
          </li>
          <li v-if="experience.activities && experience.activities.length">
            <v-icon small color="primary">arrow_forward_ios</v-icon>
            Activités du métier&nbsp;:
            <ul class="pt-2">
              <li v-for="activity in experience.activities" :key="activity.id">
                <template>
                  {{ activity.title }}
                </template>
              </li>
            </ul>
          </li>
        </ul>
      </v-row>
    </v-card-text>
    <v-dialog v-model="displayModal" v-if="experience.erhgoOccupationId" max-width="75%">
      <erhgo-occupation-detail-popin
        v-if="experience.erhgoOccupationId"
        :occupation-id="experience.erhgoOccupationId"
        @onClose="displayModal = false"
      />
    </v-dialog>
  </v-card>
</template>
<script>
import {ExperienceDetailsWithCapacities, MasteryLevel} from 'erhgo-api-client';
import CardBtn from 'odas-plugins/CardBtn';
import ErhgoOccupationDetailPopin from '@/views/repository/erhgo/ErhgoOccupationDetailPopin';

export default {
  name: 'ExperienceView',
  components: {CardBtn, ErhgoOccupationDetailPopin},
  data() {
    return {
      displayModal: false,
      visible: false,
    };
  },
  props: {
    experience: {
      type: ExperienceDetailsWithCapacities,
      required: true,
    },
  },
  computed: {
    masteryLevel() {
      const level = this.experience.erhgoOccupationMasteryLevel;
      return level ? this.$t(`masteryLevels.${level}.labelM`) + ' (' + (Object.values(MasteryLevel).indexOf(level) + 1) + ') ' : 'inconnu';
    },
  },
};
</script>
<style lang="scss" scoped>
.clickable {
  cursor: pointer;
}
</style>
