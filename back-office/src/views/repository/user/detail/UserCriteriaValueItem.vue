<template>
  <li v-if="criteriaValueIconAndColor.show">
    <v-icon small :color="criteriaValueIconAndColor.color">{{criteriaValueIconAndColor.icon}}</v-icon>
    {{ criteriaValue.titleStandalone }}
  </li>
</template>
<script>
export default {
  name: 'user-criteria-value-item',
  props: {
    criteriaValue: {
      required: true,
      type: Object,
    },
    userCriteria: {
      required: true,
      type: Array,
    },
    questionType: {
      required: true,
      type: String,
    },
  },
  computed: {
    criteriaCode() {
      return this.criteriaValue.code;
    },
    criteriaValueIconAndColor() {
      let criteria = this.userCriteria.filter(c => c.code === this.criteriaCode);
      let color = 'red', icon = 'close', show = true;
      if (!criteria.length) {
        icon = 'mdi-help';
        color = 'purple';
        show = this.questionType === 'MULTIPLE';
      } else if (criteria[0].selected) {
        icon = 'mdi-check';
        color = 'green';
      }
      return {icon, color, show};
    },
  },
};
</script>
