<template>
  <v-row>
    <v-col class="text-body-2">
      <v-icon small color="primary">arrow_forward_ios</v-icon>
      {{ `${candidatures.length || 'Aucune'} candidature${candidatures.length > 1 ? 's' : ''}` }}
    </v-col>
    <v-col cols="12">
      <user-candidatures :service="candidaturesService"/>
    </v-col>
  </v-row>
</template>
<script>
import UserCandidatures from '@/views/setup/user/UserCandidatures';
import CandidaturesService from '@/views/setup/user/CandidaturesService';

export default {
  name: 'UserDetailCandidaturesView',
  components: {UserCandidatures},
  props: {
    userId: {
      type: String,
      required: true,
    },
    organization_code: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      displayOccupations: false,
      displayJobs: false,
      candidaturesService: null,
    };
  },
  computed: {
    candidatures() {
      return this.candidaturesService?.candidatures || [];
    },
  },
  created() {
    this.candidaturesService = new CandidaturesService(this.userId, this.organization_code || undefined);
    this.candidaturesService.fetchCandidatures();
  },
};
</script>
