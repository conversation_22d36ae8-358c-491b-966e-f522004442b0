<template>
  <v-row no-gutters>
    <v-col cols="12">
      <v-btn
        color="primary"
        @click="showDialog = true"
      >
        Modifier
      </v-btn>
    </v-col>
    <v-dialog v-model="showDialog" scrollable v-if="!!service">
      <v-card :loading="service.loading" :disabled="service.loading" color="white">
        <v-card-title>
          <v-toolbar color="primary">
            <v-toolbar-title>Éditer les critères</v-toolbar-title>
            <v-spacer/>
            <v-btn
              icon
              dark
              @click="showDialog = false"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
        </v-card-title>
        <v-card-text>
          <criteria-selector v-model="service.selectedValues" :showAsFO="true">
            <template v-slot:thresholdTitle>
              <p class="text-caption">
                Indiquez le niveau maximal atteint par le candidat pour chaque critère
              </p>
            </template>
            <template v-slot:multipleTitle>
              <p class="text-caption">
                Vous pouvez choisir tous les éléments acceptés par le candidat pour chaque critère
              </p>
            </template>
          </criteria-selector>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn color="shiny" @click="submitCriteria">Valider</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <template v-for="criterion in service.allCriteria">
      <user-criteria-item
        :key="criterion.code"
        :criterion="criterion"
        :user-criteria="service.userCriteria"
      />
    </template>

  </v-row>
</template>

<script>
import UserCriteriaItem from './UserCriteriaItem';
import CriteriaSelector from '@/components/criteria/CriteriaSelector';
import UserCriteriaService from '@/components/criteria/UserCriteriaService';

export default {
  name: 'user-criteria',
  components: {CriteriaSelector, UserCriteriaItem},
  props: {
    userCriteria: {
      required: true,
      type: Array,
    },
    userId: {
      required: true,
      type: String,
    },
  },
  data() {
    return {
      showDialog: false,
      service: null,
    };
  },
  async created() {
    this.service = new UserCriteriaService(this.userId);
    this.service.userCriteria = this.userCriteria;
    await this.service.fetchAllCriteria();
  },
  methods: {
    async submitCriteria() {
      const success = await this.service.submitCriteria();
      this.showDialog = !success;
    },
  },
};
</script>

