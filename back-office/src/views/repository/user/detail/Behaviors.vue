<template>
  <ul class="text-body-2" v-if="behaviors.length">
    <i><PERSON><PERSON><PERSON>, je souhaite que mon métier mette en avant&nbsp;:</i>
    <li class="my-2" v-for="behavior in behaviors" :key="behavior.id">
      <v-icon small color="primary">arrow_forward_ios</v-icon>
      <b>{{ behavior.title }}</b>
      <p class="font-italic pl-5 pt-1">{{ behavior.description }}</p>
    </li>
  </ul>
  <span v-else>L'utilisateur n'a pas précisé de profil affinitaire.</span>
</template>
<script>

export default {
  name: 'behaviors',
  props: {
    behaviors: {
      type: Array,
      required: true,
    },
  },
};
</script>
