<template>
  <v-row no-gutters>
    <v-col cols="12" md="6">
      <ul class="text-body-2" style="list-style: none;">
        <template v-if="this.isAffected">
          <li class="my-2" v-for="({name, label, value}) in states" :key="name">
            <template v-if="name !=='CONFIRMED_PROFESSION_DIRECT'">
              <v-icon small color="primary">arrow_forward_ios</v-icon>
              {{ label }}&nbsp;: <strong v-if="value">Oui ({{ value }})</strong>
              <strong v-else>Non</strong>
            </template>
            <template v-else>
              <v-icon small color="primary">arrow_forward_ios</v-icon>
              {{ label }}&nbsp;:
              <template v-if="value">
                <strong>{{ registrationStateService.jobTitle }}</strong>
                <div class="text-caption ml-5">
                  <div v-if="!registrationStateService.occupation">
                    <v-icon color="error" small>warning</v-icon>
                    Aucun métier erhgo sélectionné
                  </div>
                  <div v-else>Métier erhgo choisi&nbsp;:
                    {{ registrationStateService.occupation }}
                    <v-btn target="_blank" icon x-small
                           :to="{name: 'erhgo_occupation_detail', params: {'id': registrationStateService.occupationId}}">
                      <v-icon>visibility</v-icon>
                    </v-btn>
                  </div>
                </div>
              </template>
              <strong v-else>Non</strong>
            </template>
          </li>
        </template>
        <div v-else>
          <li class="my-2">
            <v-icon small color="primary">arrow_forward_ios</v-icon>
            <strong>Non concerné (anciens comptes ou comptes non concernés par l’inscription
              simplifiée)</strong>
          </li>
        </div>
      </ul>
    </v-col>
  </v-row>
</template>

<script>
import { UserRegistrationState, UserRegistrationStateStep } from 'erhgo-api-client';
import RegistrationStateService from '../RegistrationStateService';

export default {
  name: 'RegistrationProgressDetails',
  props: {
    userRegistrationState: {
      required: true,
      type: UserRegistrationState,
    },
  },
  data() {
    return {
      registrationStateService: null,
    };
  },
  computed: {
    currentState() {
      return this.userRegistrationState && this.states[this.userRegistrationState.userRegistrationStateStep].label;
    },
    isAffected() {
      return this.userRegistrationState?.userRegistrationStateStep !== UserRegistrationStateStep.NOT_AFFECTED && this.userRegistrationState?.userRegistrationStateStep !== UserRegistrationStateStep.LEGACY_ACCOUNT;
    },
    states() {
      return [
        {
          name: 'CONFIRMED_SITUATION',
          label: 'Situation validée',
          value: this.registrationStateService.situation && this.$t(`situation.${this.registrationStateService.situation}`),
        },
        {
          name: 'CONFIRMED_PROFESSION_DIRECT',
          label: 'Métier validé directement',
          value: !!this.registrationStateService.occupation,
        },
        {
          name: 'CONFIRMED_CITY',
          label: 'Ville validée',
          value: this.registrationStateService.city,
        },
        {
          name: 'CONFIRMED_SALARY',
          label: 'Salaire validé',
          value: this.registrationStateService.salary && this.$t(`salary.${this.registrationStateService.salary}`),
        },
      ];
    },
  },
  created() {
    this.registrationStateService = new RegistrationStateService(this.userRegistrationState);
  },
};
</script>
