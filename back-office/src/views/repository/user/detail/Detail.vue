<template>
  <v-container fluid id="userDetail" ref="content">
    <v-row>
      <card-btn no-border
                id="back"
                @click="$router.go(-1)">
        <v-icon>chevron_left</v-icon>
        Retour
      </card-btn>
    </v-row>
    <v-row no-gutters>
      <template v-if="contactInformation">
        <v-col cols="12">
          <h1 class="d-flex align-center">
            <v-icon large
                    class="mr-2"
                    color="primary">people
            </v-icon>
            {{ contactInformation.firstName }} {{ lastName }}
          </h1>
        </v-col>
        <v-col cols v-if="contactInformation">
          <v-menu offset-y>
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                large
                outlined
                dark
                color="primary"
                v-bind="attrs"
                v-on="on"
                class="mr-2 float-left"
              >
                <v-icon left>fa-file-pdf</v-icon>
                Télécharger profil
                <v-icon right>mdi-menu-down</v-icon>
              </v-btn>
            </template>

            <v-list>
              <v-list-item>
                <downloader-btn
                  text
                  color="primary"
                  extension="pdf"
                  :title="`jenesuisPASunCV_${contactInformation.firstName}_${lastName}`"
                  :download="exportAsPdf(false)"
                >
                  <v-icon left>fa-file-pdf</v-icon>
                  Profil #jenesuisPASunCV
                </downloader-btn>
              </v-list-item>

              <v-list-item>
                <downloader-btn
                  text
                  color="primary"
                  extension="pdf"
                  :title="`jnspuHandicap_${contactInformation.firstName}_${lastName}`"
                  :download="exportAsPdf(true)"
                >
                  <v-icon left>fa-file-pdf</v-icon>
                  Profil jnspuHandicap
                </downloader-btn>
              </v-list-item>
            </v-list>
          </v-menu>
        </v-col>
      </template>
      <v-spacer/>
      <v-btn outlined
             :to="{
                name: 'user-edit',
                params: {
                  userId,
                },
             }"
             class="mr-4"
      >
        <v-icon small class="mr-2">fa-pencil</v-icon>
        Modifier cet utilisateur
      </v-btn>
      <delete-user-dialog
        :user-detail-service="userDetailService"
      />
      <reset-password-dialog
        :userId="userId"
      />
    </v-row>

    <errorDialog v-model="userDetailService.showAPIError"/>
    <v-row v-if="!userDetailService || !userDetailService.initialized" justify="center">
      <v-progress-circular
        :size="50"
        color="primary"
        indeterminate
      />
    </v-row>
    <v-row align-content="end" v-else no-gutters>
      <v-expansion-panels class="mt-3" tile flat :value="[0,3,4,5]" multiple v-if="userInfo">
        <v-expansion-panel>
          <v-expansion-panel-header class="py-3 pl-2 primary size-unset">
            <template v-slot:actions>
              <v-icon>$expand</v-icon>
            </template>
            <h3>
              Informations générales
            </h3>
          </v-expansion-panel-header>
          <v-expansion-panel-content class="pt-2">
            <template v-if="userInfo">
              <progress-view v-if="progress && extraProfessional"
                             :progress="progress"
                             :extra-professional="extraProfessional"
                             :candidate-details="userInfo.contactInformation"
                             :transactional-blacklisted="isTransactionalBlacklisted"
                             :channels="channels"
                             :is-private="isPrivate"
                             :wanna-receive-job-offers="wannaReceiveJobOffers"
                             :organization-code="organization_code"
                             :user-id="userId"
                             @updated="userDetailService.fetchUserDetailWithCapacities()"
              />
            </template>
          </v-expansion-panel-content>
        </v-expansion-panel>
        <v-expansion-panel>
          <v-expansion-panel-header class="py-3 pl-2 primary size-unset">
            <template v-slot:actions>
              <v-icon>$expand</v-icon>
            </template>
            <h3>
              État de l'inscription
            </h3>
          </v-expansion-panel-header>
          <v-expansion-panel-content class="pt-2">
            <registration-progress-details :user-registration-state="registrationState"/>
          </v-expansion-panel-content>
        </v-expansion-panel>
        <v-expansion-panel>
          <v-expansion-panel-header class="py-3 pl-2 primary size-unset">
            <template v-slot:actions>
              <v-icon>$expand</v-icon>
            </template>
            <h3>
              Critères recherchés
            </h3>
          </v-expansion-panel-header>
          <v-expansion-panel-content class="pt-2">
            <user-criteria :user-criteria="userCriteria" :user-id="userId"/>
          </v-expansion-panel-content>
        </v-expansion-panel>
        <v-expansion-panel>
          <v-expansion-panel-header class="py-3 pl-2 primary size-unset">
            <template v-slot:actions>
              <v-icon>$expand</v-icon>
            </template>
            <h3>
              Expériences
            </h3>
          </v-expansion-panel-header>
          <v-expansion-panel-content class="pt-2">
            <v-row dense>
              <v-col cols="12" lg="6" class="d-flex" v-for="experience in experiences"
                     :key="experience.id">
                <experience :experience="experience"/>
              </v-col>
            </v-row>
          </v-expansion-panel-content>
        </v-expansion-panel>
        <v-expansion-panel>
          <v-expansion-panel-header class="py-3 pl-2 primary size-unset">
            <template v-slot:actions>
              <v-icon>$expand</v-icon>
            </template>
            <h3>
              Profil affinitaire
            </h3>
          </v-expansion-panel-header>
          <v-expansion-panel-content class="pt-2">
            <behaviors v-if="behaviors" :behaviors="behaviors"/>
          </v-expansion-panel-content>
        </v-expansion-panel>
        <v-expansion-panel>
          <v-expansion-panel-header class="py-3 pl-2 primary size-unset">
            <template v-slot:actions>
              <v-icon>$expand</v-icon>
            </template>
            <h3>Candidatures</h3>
          </v-expansion-panel-header>
          <v-expansion-panel-content class="pt-3">
            <candidatures :user-id="userId" :organization_code="organization_code"/>
          </v-expansion-panel-content>
        </v-expansion-panel>
        <v-expansion-panel>
          <v-row align-content="end" no-gutters>
            <v-col>
              <h3 class="pl-2 mt-3 py-2 primary">
                <v-icon class="mr-2">edit</v-icon>
                Prise de notes
              </h3>
            </v-col>
          </v-row>
          <template v-for="note in userDetailService.notes">
            <user-note
              :user-detail-service="userDetailService"
              :note="note"
              :key="note.id"
            />
            <v-divider :key="`div${note.id}`" class="my-10"/>
          </template>
          <v-row no-gutters>
            <v-btn class="ma-3"
                   color="primary"
                   outlined
                   @click="userDetailService.addNote()"
                   :disabled="!userDetailService.mayAddNote"
            >
              <v-icon>add</v-icon>
              Ajouter une note
            </v-btn>
          </v-row>
        </v-expansion-panel>
      </v-expansion-panels>
    </v-row>
  </v-container>
</template>

<script>
import ErrorDialog from '@/components/common/ErrorDialog';
import UserDetailService from './UserDetailService';
import Experience from './Experience';
import Candidatures from './Candidatures';
import ProgressView from './Progress';
import CardBtn from 'odas-plugins/CardBtn';
import Behaviors from './Behaviors';
import DeleteUserDialog from './DeleteUserDialog';
import ResetPasswordDialog from './ResetPasswordDialog';
import RegistrationProgressDetails from './RegistrationProgress';
import UserCriteria from './UserCriteria';
import UserNote from './UserNote';
import DownloaderBtn from 'odas-plugins/DownloaderBtn';

export default {
  components: {
    UserNote,
    DeleteUserDialog,
    ErrorDialog,
    RegistrationProgressDetails,
    ResetPasswordDialog,
    Behaviors,
    Experience,
    Candidatures,
    ProgressView,
    UserCriteria,
    CardBtn,
    DownloaderBtn,
  },
  props: {
    userId: {
      required: true,
      type: String,
    },
    organization_code: {
      default: null,
      type: String,
    },

  },
  data() {
    return {
      userDetailService: null,
    };
  },
  methods: {
    exportAsPdf(handicap) {
      return async () => (await this.$api.getUserProfileFOPdf(this.userId, false, handicap, { responseType: 'blob' })).data;
    },
  },
  computed: {
    userInfo() {
      return this.userDetailService?.userInfo || null;
    },
    experiences() {
      return this.userDetailService?.experiences || [];
    },
    progress() {
      return this.userDetailService?.progress || null;
    },
    behaviors() {
      return this.userDetailService?.behaviors || null;
    },
    extraProfessional() {
      return this.userDetailService?.extraProfessional || null;
    },
    contactInformation() {
      return this.userDetailService?.userInfo?.contactInformation || null;
    },
    registrationState() {
      return this.userDetailService?.registrationState || null;
    },
    userCriteria() {
      return this.userDetailService?.userCriteria || null;
    },
    channels() {
      return this.userDetailService?.channels || [];
    },
    isPrivate() {
      return !!this.userDetailService?.isPrivate;
    },
    wannaReceiveJobOffers() {
      return !!this.userDetailService?._receiveJobOfferEmails;
    },
    lastName() {
      return `${this.contactInformation?.lastName?.substring(0, 1) || ''}.`;
    },
    isTransactionalBlacklisted() {
      return !!this.contactInformation?.transactionalBlacklisted;
    },
    isSmsBlacklisted() {
      return !!this.contactInformation?.smsBlacklisted;
    },
  },
  async created() {
    this.userDetailService = new UserDetailService(this.userId, this.organization_code);
    await this.userDetailService.fetchAll();
  },
};
</script>
<style>
.size-unset {
  min-height: unset !important;
}
</style>
