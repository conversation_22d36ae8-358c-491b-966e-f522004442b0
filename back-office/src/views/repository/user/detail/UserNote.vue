<template>
  <v-row no-gutters>
    <v-col cols="4">
      <v-autocomplete label="Sélectionner l'organisation concernée par cette note"
                class="pt-5"
                dense
                      clearable
                persistent-hint
                hint="Cette note sera visible par l'équipe erhgo et par les autres membres de cette organisation"
                      :item-text="o => `${o.title}${o.organizationType === 'SOURCING' ? ' (sourcing)' : ''}`"
                item-value="id"
                v-model="note.organizationId"
                no-data-text="Aucune organisation disponible"
                :items="userDetailService.recruiters">
        <template v-slot:prepend>
          <v-tooltip top>
            <template v-slot:activator="{ on }">
              <v-btn icon v-on="on" @click="userDetailService.removeNote(note)">
                <v-icon color="error">cancel</v-icon>
              </v-btn>
            </template>
            Supprimer cette note
          </v-tooltip>
        </template>
        <template v-slot:append-outer>
          <v-icon color="success" :class="{'invisible': !note.savedNote}">check</v-icon>
        </template>
      </v-autocomplete>
    </v-col>
    <v-col cols="8">
      <v-textarea v-model="note.text"
                  label="Prenez ici vos notes relatives à cet utilisateur"
                  outlined
                  no-resize
                  hide-details
                  class="pa-3"
                  rows="3"/>
      <div class="pl-3 font-italic" v-if="createdBy || modifiedBy">
        <span class="text-caption" v-if="createdBy">
          {{ traceabilityCreationText }}
        </span>
        <template v-if="createdBy && modifiedBy"> //</template>
        <span class="text-caption" v-if="modifiedBy">
          {{ traceabilityModificationText }}
        </span>
      </div>
    </v-col>
  </v-row>
</template>
<script>
import UserDetailService from './UserDetailService';
import moment from 'moment';

export default {
  name: 'user-note',
  props: {
    userDetailService: {
      type: UserDetailService,
      required: true,
    },
    note: {
      type: Object,
      required: true,
    },
  },
  computed: {
    createdBy() {
      return this.note.createdBy ? `Créée par ${this.note.createdBy}` : '';
    },
    createdDate() {
      return this.note.createdDate ? `le ${moment(this.note.createdDate).format('DD/MM/YYYY')}` : '';
    },
    modifiedBy() {
      return this.note.modifiedBy ? `Modifiée par ${this.note.modifiedBy} ` : '';
    },
    modifiedDate() {
      return this.note.modificationDate ? `le ${moment(this.note.modificationDate).format('DD/MM/YYYY')}` : '';
    },
    traceabilityCreationText() {
      return `${this.createdBy} ${this.createdDate}`;
    },
    traceabilityModificationText() {
      return `${this.modifiedBy} ${this.modifiedDate}`;
    },
  },
};
</script>
