<template>
  <v-container fluid>
    <v-row>
      <v-col>
        <v-card class="pt-3">
          <v-card-title class="mb-3">
            <h1 class="d-flex align-center">
              <v-icon large class="mr-2" color="primary"
                >mdi-account-search
              </v-icon>
              {{ $t(`menu.scraped_users`) }}
              {{ scrapedUsers ? `(${scrapedUsers.length})` : "" }}
            </h1>
            <v-spacer />
            <v-text-field
              v-model="searchQuery"
              append-icon="search"
              label="Chercher par nom / prénom / email"
              clearable
            />
            <v-spacer />
            <v-btn
              :loading="exportLoading"
              color="secondary"
              dark
              @click="exportScrapedUsers"
              class="ml-3"
              small
            >
              <v-icon>archive</v-icon>
              Exporter les candidats scrapés
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-data-table
              :headers="headers"
              :items="filteredScrapedUsers"
              :loading="loading"
              class="elevation-15"
              :no-data-text="'Aucun candidat scrapé trouvé'"
              :items-per-page="25"
              item-key="uuid"
              :footer-props="{
                'items-per-page-options': [10, 25, 50, 100],
                'items-per-page-text': 'Nb lignes par page',
              }"
              loading-text="Chargement en cours..."
              id="scrapedUsersList"
            >
              <template v-slot:item="props">
                <scraped-user-list-item
                  :item-props="props"
                  :index="filteredScrapedUsers.indexOf(props.item)"
                  @show-details="showUserDetails"
                />
              </template>
            </v-data-table>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <scraped-user-detail-modal
      v-if="selectedUser"
      :user="selectedUser"
      :show="showDetailModal"
      @close="closeDetailModal"
    />
  </v-container>
</template>

<script>
import ScrapedUserListItem from './ListItem.vue';
import ScrapedUserDetailModal from './DetailModal.vue';
import moment from 'moment';
import DownloadUtils from '@/components/mixins/DownloadUtils';

export default {
  name: 'ScrapedUserList',
  components: {
    ScrapedUserListItem,
    ScrapedUserDetailModal,
  },
  mixins: [DownloadUtils],
  data() {
    return {
      scrapedUsers: [],
      loading: false,
      exportLoading: false,
      searchQuery: '',
      selectedUser: null,
      showDetailModal: false,
      headers: [
        {
          text: 'Nom',
          align: 'start',
          sortable: true,
          value: 'lastName',
        },
        {
          text: 'Prénom',
          value: 'firstName',
          sortable: true,
        },
        {
          text: 'Email',
          value: 'email',
          sortable: true,
        },
        {
          text: 'Lien de DL du CV',
          value: 'cvDownloadLink',
          sortable: false,
        },
        {
          text: 'Actions',
          value: 'actions',
          sortable: false,
          width: '15%',
        },
      ],
    };
  },
  async created() {
    await this.fetchScrapedUsers();
  },
  computed: {
    filteredScrapedUsers() {
      if (!this.searchQuery || !this.scrapedUsers) {
        return this.scrapedUsers || [];
      }

      const query = this.searchQuery.toLowerCase();
      return this.scrapedUsers.filter((user) => {
        return (
          (user.lastName && user.lastName.toLowerCase().includes(query)) ||
          (user.firstName && user.firstName.toLowerCase().includes(query)) ||
          (user.email && user.email.toLowerCase().includes(query))
        );
      });
    },
  },
  methods: {
    async fetchScrapedUsers() {
      this.loading = true;
      try {
        const response = await this.$api.getScrapedUsers();
        this.scrapedUsers = response.data;
      } finally {
        this.loading = false;
      }
    },
    async showUserDetails(user) {
      this.selectedUser = await (
        await this.$api.getScrapedUserDetail(user.uuid)
      ).data;
      this.showDetailModal = true;
    },
    closeDetailModal() {
      this.showDetailModal = false;
      this.selectedUser = null;
    },
    async exportScrapedUsers() {
      this.exportLoading = true;
      try {
        await this.download(
          async () => await this.$api.exportScrapedUsers(),
          `candidats-scrapes_${moment().format('MM-DD-YYYY_HH-mm-ss')}.csv`,
        );
      } finally {
        this.exportLoading = false;
      }
    },
  },
};
</script>

<style scoped></style>
