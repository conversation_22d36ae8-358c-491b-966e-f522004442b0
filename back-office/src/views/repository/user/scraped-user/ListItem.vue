<template>
  <tr :id="`scraped-user-${index}`" @click="showDetails" class="clickable-row">
    <td class="text-left">{{ item.lastName || "N/A" }}</td>
    <td class="text-left">{{ item.firstName || "N/A" }}</td>
    <td class="text-left">
      <template v-if="item.email">
        <a :href="`mailto:${item.email}`">{{ item.email }}</a>
      </template>
      <template v-else> N/A </template>
    </td>
    <td class="text-left">
      <template v-if="item.cvDownloadLink">
        <v-tooltip bottom>
          <template v-slot:activator="{ on, attrs }">
            <v-btn
              icon
              small
              color="primary"
              v-bind="attrs"
              v-on="on"
              :href="item.cvDownloadLink"
              target="_blank"
              @click.stop
            >
              <v-icon>mdi-download</v-icon>
            </v-btn>
          </template>
          <span>Télécharger le CV</span>
        </v-tooltip>
      </template>
      <template v-else> N/A </template>
    </td>
    <scraped-user-actions-cell
      :item="item"
      @show-details="showDetails"
      @click.stop
    />
  </tr>
</template>

<script>
import ScrapedUserActionsCell from './ActionsCell.vue';

export default {
  name: 'ScrapedUserListItem',
  components: { ScrapedUserActionsCell },
  props: {
    index: {
      required: true,
      type: Number,
    },
    itemProps: {
      required: true,
      type: Object,
    },
  },
  computed: {
    item() {
      return this.itemProps.item;
    },
  },
  methods: {
    showDetails() {
      this.$emit('show-details', this.item);
    },
  },
};
</script>

<style scoped>
.clickable-row {
  cursor: pointer;
}

.clickable-row:hover {
  background-color: #f5f5f5;
}
</style>
