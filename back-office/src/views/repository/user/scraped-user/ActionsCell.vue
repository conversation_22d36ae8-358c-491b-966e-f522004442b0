<template>
  <td style="width: 15%">
    <v-tooltip top>
      <template v-slot:activator="{ on }">
        <v-btn icon small v-on="on" color="primary" @click="showDetails">
          <v-icon>visibility</v-icon>
        </v-btn>
      </template>
      <span>Voir les détails</span>
    </v-tooltip>

    <v-tooltip top>
      <template v-slot:activator="{ on }">
        <v-btn
          icon
          small
          color="secondary"
          v-on="on"
          @click="createUser"
          disabled
        >
          <v-icon>mdi-account-plus</v-icon>
        </v-btn>
      </template>
      <span>Créer un utilisateur</span>
    </v-tooltip>

    <v-tooltip top v-if="item.profileUrl">
      <template v-slot:activator="{ on }">
        <v-btn
          icon
          small
          color="info"
          v-on="on"
          :href="item.profileUrl"
          target="_blank"
        >
          <v-icon>mdi-open-in-new</v-icon>
        </v-btn>
      </template>
      <span>Voir le profil source</span>
    </v-tooltip>
  </td>
</template>

<script>
export default {
  name: 'ScrapedUserActionsCell',
  props: {
    item: {
      type: Object,
      required: true,
    },
  },
  methods: {
    showDetails() {
      this.$emit('show-details', this.item);
    },
    createUser() {
      this.$emit('create-user', this.item);
    },
  },
};
</script>

<style scoped></style>
