<template>
  <v-dialog v-model="showDialog" max-width="800px" persistent>
    <v-card v-if="user">
      <v-card-title class="text-h5 grey lighten-2">
        <v-icon class="mr-2" color="primary">mdi-account-details</v-icon>
        Détails du candidat scrapé
        <v-spacer />
        <v-btn icon @click="close">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>

      <v-card-text class="pt-4">
        <v-container>
          <v-row>
            <v-col cols="12" md="6">
              <v-card outlined class="mb-3">
                <v-card-subtitle class="pb-1">
                  <v-icon small class="mr-1">mdi-account</v-icon>
                  Informations personnelles
                </v-card-subtitle>
                <v-card-text>
                  <div class="mb-2">
                    <strong>ID Candidat&nbsp;:</strong> {{ user.candidateId }}
                  </div>
                  <div class="mb-2">
                    <strong>Nom&nbsp;:</strong> {{ user.lastName || "N/A" }}
                  </div>
                  <div class="mb-2">
                    <strong>Prénom&nbsp;:</strong> {{ user.firstName || "N/A" }}
                  </div>
                  <div class="mb-2">
                    <strong>Email&nbsp;:</strong>
                    <template v-if="user.email">
                      <a :href="`mailto:${user.email}`">{{ user.email }}</a>
                    </template>
                    <template v-else>N/A</template>
                  </div>
                </v-card-text>
              </v-card>
            </v-col>

            <v-col cols="12" md="6">
              <v-card outlined class="mb-3">
                <v-card-subtitle class="pb-1">
                  <v-icon small class="mr-1">mdi-briefcase</v-icon>
                  Informations professionnelles
                </v-card-subtitle>
                <v-card-text>
                  <div class="mb-2">
                    <strong>Poste&nbsp;:</strong> {{ user.jobTitle || "N/A" }}
                  </div>
                  <div class="mb-2">
                    <strong>Localisation&nbsp;:</strong>
                    {{ user.location || "N/A" }}
                  </div>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>

          <v-row>
            <v-col cols="12">
              <v-card outlined class="mb-3">
                <v-card-subtitle class="pb-1">
                  <v-icon small class="mr-1">mdi-link</v-icon>
                  Liens et ressources
                </v-card-subtitle>
                <v-card-text>
                  <div class="mb-2">
                    <strong>Lien de téléchargement CV&nbsp;:</strong>
                    <template v-if="user.cvDownloadLink">
                      <br />
                      <v-btn
                        small
                        color="primary"
                        :href="user.cvDownloadLink"
                        target="_blank"
                        class="mt-1"
                      >
                        <v-icon left small>mdi-download</v-icon>
                        Télécharger le CV
                      </v-btn>
                    </template>
                    <template v-else>N/A</template>
                  </div>
                  <div class="mb-2">
                    <strong>URL du profil&nbsp;:</strong>
                    <template v-if="user.profileUrl">
                      <br />
                      <v-btn
                        small
                        color="info"
                        :href="user.profileUrl"
                        target="_blank"
                        class="mt-1"
                      >
                        <v-icon left small>mdi-open-in-new</v-icon>
                        Voir le profil source
                      </v-btn>
                    </template>
                    <template v-else>N/A</template>
                  </div>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>

          <v-row v-if="user.cvContent">
            <v-col cols="12">
              <v-card outlined>
                <v-card-subtitle class="pb-1">
                  <v-icon small class="mr-1">mdi-file-document</v-icon>
                  Contenu CV analysé (JSON)
                </v-card-subtitle>
                <v-card-text>
                  <v-expansion-panels>
                    <v-expansion-panel>
                      <v-expansion-panel-header>
                        Voir le contenu analysé
                      </v-expansion-panel-header>
                      <v-expansion-panel-content>
                        <pre class="cv-content">{{
                          formatCvContent(user.cvContent)
                        }}</pre>
                      </v-expansion-panel-content>
                    </v-expansion-panel>
                  </v-expansion-panels>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>

      <v-divider />

      <v-card-actions>
        <v-spacer />
        <v-btn color="primary" @click="close"> Fermer </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'ScrapedUserDetailModal',
  props: {
    user: {
      type: Object,
      default: null,
    },
    show: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    showDialog: {
      get() {
        return this.show;
      },
      set(value) {
        if (!value) {
          this.close();
        }
      },
    },
  },
  methods: {
    close() {
      this.$emit('close');
    },
    formatCvContent(content) {
      if (!content) return 'N/A';
      try {
        return JSON.stringify(JSON.parse(content), null, 2);
      } catch (e) {
        return content;
      }
    },
  },
};
</script>

<style scoped>
.cv-content {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-word;
}
</style>
