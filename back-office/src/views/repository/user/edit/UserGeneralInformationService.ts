import SafeService from 'odas-plugins/SafeService';
import Vue from 'vue';
import {GeneralInformation, Situation} from 'erhgo-api-client';
import asyncDebounce from 'async-debounce';
import {AxiosError} from 'axios';

export default class UserGeneralInformationService extends SafeService {

  private _emailConflict = false;

  private _contactInfo: GeneralInformation | null = null;

  private _jobOffersOptIn = false;
  private _jobDatingOptIn = false;

  constructor(private _userId: string) {
    super();
  }

  async fetchGeneralInformations() {
    return this.safeCall(async () => {
      this._contactInfo = (await Vue.$api.getUserContactInfo(this._userId)).data.contactInformation;
      Vue.$api.getUserJobDatingNotifyOptIn(this._userId).then(res => {
        this._jobDatingOptIn = !!res.data.value;
      });
      Vue.$api.getUserJobOffersOptIn(this._userId).then(res => {
        this._jobOffersOptIn = !!res.data.value;
      });
    }, false, true);
  }

  private debouncedUpdate = asyncDebounce(async () => {
    return this.safeCall(async () => {
      try {
        this._emailConflict = false;
        const {
          firstName,
          lastName,
          phoneNumber,
          contactTime,
          location,
          birthDate,
          salary,
          situation,
          email,
          source,
          delayInMonth,
        } = this._contactInfo!;
        await Vue.$api.setUserContactInfo(this._userId, {
          firstName,
          lastName,
          phoneNumber,
          contactTime,
          location,
          birthDate,
          salary,
          situation,
          receiveJobOfferEmails: this._jobOffersOptIn,
          email,
          source,
          delayInMonth,
        });
      } catch (e) {
        if ((e as AxiosError)?.response?.status !== 409) {
          throw e;
        }
        this._emailConflict = true;
      }
    });
  }, 500);

  get isAvailable() {
    return this._contactInfo?.situation === Situation.RESEARCHING;
  }

  get situation() {
    return (this._contactInfo && this._contactInfo.situation) ? this._contactInfo.situation : null;
  }

  set situation(situation: Situation | null) {
    if (this._contactInfo?.situation !== situation) {
      this._contactInfo!.situation = situation || undefined;
      if (this.situation) {
        this.setUserSituation(this.situation, 0);
      }
    }
  }

  async setUserSituation(situation: Situation, delayInMonth: number | null) {
    await this.safeCall(async () => {
      await Vue.$api.setUserSituation({situation: situation, delayInMonth: delayInMonth || undefined});
    });
  }

  get delayInMonth() {
    return this.situation === Situation.RESEARCHING ? (this._contactInfo!.delayInMonth || null) : null;
  }

  set delayInMonth(delay: number | null) {
    if (this._contactInfo?.delayInMonth !== delay) {
      this._contactInfo!.delayInMonth = delay || undefined;
      if (this.situation) {
        this.setUserSituation(this.situation, this.delayInMonth);
      }
    }
  }

  async update() {
    this.debouncedUpdate.fn();
  }

  get info() {
    return this._contactInfo;
  }

  get emailError() {
    const missingEmailError = !this._contactInfo?.email ? 'Ce champ ne peut être vide' : '';
    return this._emailConflict ? 'Cette adresse email est déjà utilisée par un autre compte' : missingEmailError;
  }

  get disabled() {
    return !!this.emailError;
  }

  get jobOffersOptIn(): boolean {
    return this._jobOffersOptIn;
  }

  set jobOffersOptIn(value: boolean) {
    this._jobOffersOptIn = value;
    this.safeCall(async () => {
      const userId = this._userId;
      await Vue.$api.updateUserJobOffersOptIn({userId, value});
    });
  }

  get jobDatingOptIn(): boolean {
    return this._jobDatingOptIn;
  }

  set jobDatingOptIn(value: boolean) {
    this._jobDatingOptIn = value;
    this.safeCall(async () => {
      const userId = this._userId;
      await Vue.$api.updateUserJobDatingNotifyOptIn({userId, value});
    });
  }

}

