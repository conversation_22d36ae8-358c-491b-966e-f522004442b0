<template>
  <v-progress-circular v-if="!userDetailService.userInfo"/>
  <v-card v-else>
    <v-row no-gutters>
      <v-col cols="9">
        <v-card-title class="justify-center font-weight-bold text-h4 mb-10">
          Qualifier un contact
        </v-card-title>
        <v-card-text class="pa-0">
          <v-row no-gutters class=" user-form">
            <v-col>
              <v-expansion-panels class="px-0" flat multiple aria-expanded="true"
                                  v-model="panel">
                <personal-informations-user :user-id="userId" @emailChanged="refreshDisabledState"/>

                <v-expansion-panel>
                  <v-expansion-panel-header class="ma-0 pa-2  font-weight-bold">
                    <v-row no-gutters justify="center" class="d-flex">
                      <v-col class="d-flex  align-center header justify-center">
                        <v-icon class="pr-2">fa-folder-tree</v-icon>
                        <span class="text-decoration-underline text-h5">Classification Erhgo</span>
                      </v-col>
                    </v-row>
                  </v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <user-erhgo-classifications-selector :user-id="userId"/>
                  </v-expansion-panel-content>
                </v-expansion-panel>

                <v-expansion-panel>
                  <v-expansion-panel-header class="ma-0 pa-2  font-weight-bold">
                    <v-row no-gutters justify="center" class="d-flex">
                      <v-col class="d-flex  align-center header justify-center">
                        <v-icon class="pr-2">fa-clipboard-check</v-icon>
                        <span class="text-decoration-underline text-h5">Critères</span>
                      </v-col>
                    </v-row>
                  </v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <user-criteria-selector :user-id="userId"/>
                  </v-expansion-panel-content>
                </v-expansion-panel>


                <v-expansion-panel>
                  <v-expansion-panel-header class="ma-0 pa-2 font-weight-bold">
                    <v-row no-gutters justify="center" class="d-flex">
                      <v-col class="d-flex align-center header justify-center">
                        <v-icon class="pr-2">fa-list</v-icon>
                        <span class="text-decoration-underline text-h5">Expériences professionnelles</span>
                      </v-col>
                    </v-row>
                  </v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <user-experiences :user-id="userId"/>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header class="ma-0 pa-2 font-weight-bold">
                    <v-row no-gutters justify="center" class="d-flex">
                      <v-col class="d-flex align-center header justify-center">
                        <v-icon class="pr-2">fa-briefcase</v-icon>
                        <span class="text-decoration-underline text-h5">Offres proposées et/ou candidatées</span>
                      </v-col>
                    </v-row>
                  </v-expansion-panel-header>
                  <v-expansion-panel-content class="my-6">
                    <list-user-recruitment-report :user-id="userId"/>
                  </v-expansion-panel-content>
                </v-expansion-panel>

                <v-expansion-panel>
                  <v-expansion-panel-header class="ma-0 pa-2 font-weight-bold">
                    <v-row no-gutters justify="center" class="d-flex">
                      <v-col class="d-flex align-center header justify-center">
                        <v-icon class="pr-2">fa-chart-bar</v-icon>
                        <span class="text-decoration-underline text-h5">Canaux affectés</span>
                      </v-col>
                    </v-row>
                  </v-expansion-panel-header>
                  <v-expansion-panel-content class="my-6">
                    <list-user-channel-affectations :user-id="userId"/>
                  </v-expansion-panel-content>
                </v-expansion-panel>

              </v-expansion-panels>
            </v-col>
          </v-row>
        </v-card-text>
      </v-col>
      <v-col cols="3" class="pt-10 justify-center">
        <div class=" sticky-top">
          <v-menu offset-y>
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                large
                outlined
                dark
                color="primary"
                v-bind="attrs"
                v-on="on"
                class="mb-2 float-left"
              >
                <v-icon left>fa-file-pdf</v-icon>
                Télécharger profil
                <v-icon right>mdi-menu-down</v-icon>
              </v-btn>
            </template>

            <v-list>
              <v-list-item>
                <downloader-btn
                  text
                  color="primary"
                  extension="pdf"
                  :title="`compétences`"
                  :download="exportAsPdf(false)"
                >
                  <v-icon left>fa-file-pdf</v-icon>
                  Profil #jenesuisPASunCV
                </downloader-btn>
              </v-list-item>

              <v-list-item>
                <downloader-btn
                  text
                  color="primary"
                  extension="pdf"
                  :title="`compétences`"
                  :download="exportAsPdf(true)"
                >
                  <v-icon left>fa-file-pdf</v-icon>
                  Profil #jnspuHandicap
                </downloader-btn>
              </v-list-item>
            </v-list>
          </v-menu>

          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <div v-on="on">
                <v-btn color="primary"
                       outlined
                       v-bind="attrs"
                       v-on="on"
                       :disabled="isDisabled"
                       @click="confirmUser">
                  <v-icon>send</v-icon>
                  Envoyer le mail
                </v-btn>
              </div>
            </template>
            <span v-if="userHasBeenConfirmed">Compte utilisateur déjà confirmé, vous pouvez modifier son mot de passe depuis le détail de l'utilisateur</span>
            <span
              v-if="isTemporaryEmail">Veuillez renseigner un email valide pour confirmer le compte de ce candidat</span>
            <span v-if="!isDisabled">Un mail sera envoyé au candidat avec un mot de passe temporaire</span>
          </v-tooltip>
          <v-textarea
            v-if="note"
            :append-icon="note.success?'check':null"
            v-model="note.text"
            full-width
            class="pt-3 justify-center"
            auto-grow
            label="Saisissez ici vos notes"
            outlined
          />
          <matching-recruitments-user-btn @candidaturesGenerated="updateCandidaturesGenerated" :userId="userId"
                                          :userDetailService="userDetailService"/>
          <v-alert type="info" dismissible v-if="!!nbCandidatures" outlined dense>
            {{ numberOfCandidaturesLabel }}
          </v-alert>

        </div>
      </v-col>
    </v-row>
  </v-card>
</template>

<script>
import PersonalInformationsUser from './PersonalInformationsUser';
import UserExperiences from './experiences/UserExperiences';
import UserCriteriaSelector from './UserCriteriaSelector';
import UserCreateService from './UserCreateService';
import UserDetailService from '../detail/UserDetailService';
import ConfirmUserService from '../../../../components/services/ConfirmUserService';
import MatchingRecruitmentsUserBtn from './MatchingRecruitmentsUserBtn';
import UserErhgoClassificationsSelector from './erhgoClassifications/UserErhgoClassificationsSelector.vue';
import ListUserRecruitmentReport from '@/views/repository/user/edit/ListUserRecruitmentReport';
import ListUserChannelAffectations from '@/views/repository/user/edit/ListUserChannelAffectations';
import DownloaderBtn from 'odas-plugins/DownloaderBtn.vue';


export default {
  components: {
    DownloaderBtn,
    ListUserChannelAffectations,
    ListUserRecruitmentReport,
    PersonalInformationsUser,
    UserCriteriaSelector,
    UserExperiences,
    MatchingRecruitmentsUserBtn,
    UserErhgoClassificationsSelector,
  },
  props: {
    userId: {
      required: true,
      type: String,
    },
  },
  data() {
    return {
      nbCandidatures: 0,
      userDetailService: new UserDetailService(this.userId, null),
      service: new UserCreateService(),
      note: null,
      panel: [0, 1, 2, 3, 4, 5],
      confirmUserService: new ConfirmUserService(),
      userHasBeenConfirmed: false,
      isTemporaryEmail: false,
    };
  },
  computed: {
    isDisabled() {
      return this.userHasBeenConfirmed || this.isTemporaryEmail;
    },
    numberOfCandidaturesLabel() {
      return `${this.nbCandidatures} candidature${this.nbCandidatures <= 1 ? ' a été générée' : 's ont été générées'}`;
    },
  },
  methods: {
    updateCandidaturesGenerated(numberOfCandidatures) {
      this.nbCandidatures = numberOfCandidatures;
    },

    async confirmUser() {
      await this.confirmUserService.confirmUserFromBo(this.userId);
      this.userHasBeenConfirmed = true;
    },
    refreshDisabledState(email) {
      this.isTemporaryEmail = !email || email.toLowerCase().startsWith('tmp_');
    },
    exportAsPdf(handicap) {
      return async () => (await this.$api.getUserProfileFOPdf(this.userId, false, handicap, { responseType: 'blob' })).data;
    },
  },
  async created() {
    await this.userDetailService.fetchRegistrationState();
    await this.userDetailService.fetchUserInfo();
    this.userHasBeenConfirmed = this.userDetailService?.registrationState?.userRegistrationStateStep !== 'BO_INITIALIZED';
    this.note = this.userDetailService.addNote();
  },
};
</script>
<style scoped>

.sticky-top {
  position: -webkit-sticky;
  position: sticky;
  top: 200px;
}

</style>
