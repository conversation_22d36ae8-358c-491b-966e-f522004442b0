import Vue from 'vue';
import SafeService from 'odas-plugins/SafeService';

import {ErhgoClassification} from 'erhgo-api-client';

export class UserErhgoClassificationItem extends SafeService {

  private _selected: boolean|null;
  constructor(private _classification: ErhgoClassification, private _userId: string, selected?: boolean) {
    super();
    this._selected = selected === undefined ? null : selected;
  }

  set selected(selected: boolean|null) {
    if (selected !== this._selected) {
      this._selected = selected;
      this.safeCall(() => Vue.$api.setUserErhgoClassification({userId: this._userId, erhgoClassificationCode: this._classification.code, isAccepted: selected === null ? undefined : selected}));
    }
  }

  get selected() {
    return this._selected;
  }

  get code() {
    return this._classification.code;
  }

  get title() {
    return this._classification.title;
  }

  get orderIndex() {
    return this._classification.orderIndex;
  }
}

export default class UserErhgoClassificationService extends SafeService {
  private _userErhgoClassifications: UserErhgoClassificationItem[] = [];

  constructor(private _userId: string) {
    super();
  }

  async fetchUserErhgoClassifications() {
    await this.safeCall(async () => {
      const userErhgoClassifications = (await Vue.$api.getUserErhgoClassifications(this._userId)).data;
      const remainingClassifications = (await Vue.$api.listErhgoClassifications()).data
        .filter(e => ![...userErhgoClassifications.acceptedErhgoClassifications, ...userErhgoClassifications.refusedErhgoClassifications]
          .find(u => u.code === e.code));
      this._userErhgoClassifications = [
        ...userErhgoClassifications.acceptedErhgoClassifications.map(e => new UserErhgoClassificationItem(e, this._userId, true)),
        ...userErhgoClassifications.refusedErhgoClassifications.map(e => new UserErhgoClassificationItem(e, this._userId, false)),
        ...remainingClassifications.map(e => new UserErhgoClassificationItem(e, this._userId)),
      ].sort((a, b) => a.orderIndex - b.orderIndex);
    });
  }

  get userErhgoClassifications() {
    return this._userErhgoClassifications;
  }
}
