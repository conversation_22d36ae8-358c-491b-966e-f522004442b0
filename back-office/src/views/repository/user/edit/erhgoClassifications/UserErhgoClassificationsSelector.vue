<template>
  <div v-if="initialized">
    <v-row no-gutters>
      <v-col cols="4" v-for="item in service.userErhgoClassifications" :key="`key-${item.code}`" class="px-5"  style="border-right: 2px solid black">
        <user-erhgo-classifications-selector-item :item="item" />
      </v-col>
    </v-row>
  </div>
  <v-progress-circular indeterminate v-else />
</template>

<script>
import UserErhgoClassificationService from './UserErhgoClassificationService';
import UserErhgoClassificationsSelectorItem from './UserErhgoClassificationsSelectorItem.vue';

export default {
  name: 'user-erhgo-classification-selector',
  components: {UserErhgoClassificationsSelectorItem},
  props: {
    userId: {
      required: true,
      type: String,
    },
  },
  data() {
    return {
      service: null,
      initialized: false,
    };
  },
  async created() {
    this.service = new UserErhgoClassificationService(this.userId);
    await this.service.fetchUserErhgoClassifications();
    this.initialized = true;
  },
};
</script>

