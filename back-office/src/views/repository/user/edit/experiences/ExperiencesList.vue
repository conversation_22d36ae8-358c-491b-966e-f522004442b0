<template>
  <v-list class="d-inline-block" two-line subheader dense v-if="service.experiences.length">
    <v-list-item v-for="experience in service.experiences" :key="experience.id">
      <v-list-item-content>
        <v-list-item-title>
          {{ experience.jobTitle }}
        </v-list-item-title>
        <v-list-item-subtitle>
          {{ experience.experienceType ? $t(`experience.${experience.experienceType}`) : '' }}{{ formatDurationInfo(experience) }}{{
            experience.organizationName ? ` chez ${experience.organizationName}` : ''
          }}
        </v-list-item-subtitle>
      </v-list-item-content>
      <v-list-item-action class="ma-0 pa-0">
        <v-btn icon @click="removeExperience(experience.id)">
          <v-icon small color="red">fa-trash-alt</v-icon>
        </v-btn>
      </v-list-item-action>
    </v-list-item>
  </v-list>
  <v-alert type="info" outlined dense v-else>Aucune expérience pour ce candidat.</v-alert>
</template>

<script>
import UserExperiencesService from './UserExperiencesService';

export default {
  name: 'user-experience',
  props: {
    service: {
      required: true,
      type: UserExperiencesService,
    },
  },
  methods: {
    removeExperience(id) {
      this.service.removeExperience(id);
    },
    formatDateToFrench(dateString) {
      return dateString ? new Date(dateString).toLocaleDateString('fr-FR') : '';
    },
    formatDurationMonths(durationInMonths) {
      return durationInMonths ? `${durationInMonths} mois` : '';
    },
    formatDateRange(startDate, endDate) {
      const formattedStartDate = this.formatDateToFrench(startDate);
      const formattedEndDate = this.formatDateToFrench(endDate);

      if (formattedStartDate && formattedEndDate) {
        return `du ${formattedStartDate} au ${formattedEndDate}`;
      }
      if (formattedStartDate) {
        return `depuis le ${formattedStartDate}`;
      }
      if (formattedEndDate) {
        return `jusqu'au ${formattedEndDate}`;
      }
      return '';
    },
    formatDurationInfo(experience) {
      const hasAnyDurationInfo = experience.durationInMonths || experience.startDate || experience.endDate;
      if (!hasAnyDurationInfo) {
        return '';
      }

      const parts = [];

      const durationText = this.formatDurationMonths(experience.durationInMonths);
      if (durationText) {
        parts.push(durationText);
      }

      const dateRangeText = this.formatDateRange(experience.startDate, experience.endDate);
      if (dateRangeText) {
        parts.push(dateRangeText);
      }

      return parts.length > 0 ? `, ${parts.join(', ')}` : '';
    },
  },
};
</script>

