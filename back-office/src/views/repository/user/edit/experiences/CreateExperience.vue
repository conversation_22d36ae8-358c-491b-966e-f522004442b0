<template>
  <v-row>
    <v-col cols="6">
      <v-select
        dense
        hide-details
        label="Type d'expérience"
        v-model="service.experienceType"
        :items="[{text: 'Stage', value: 'INTERNSHIP'}, {text: 'Emploi', value: 'JOB'}]"/>
    </v-col>
    <v-col cols="12">
      <v-combobox
        dense
        hide-details
        id="selectorJob"
        class="pt-3"
        v-model="service.occupation"
        :items="service.occupations"
        :loading="service.loading"
        :search-input.sync="service.query"
        hide-selected
        placeholder="Rechercher un métier"
        no-filter
        label="Sélectionner le métier erhgo correspondant"
        no-data-text="Aucun métier trouvé"
        item-text="title"
        clearable
      >
        <template v-slot:selection="data">
          <span v-html="data.item.title"/>
        </template>
        <template v-slot:item="data">
          <span v-html="data.item.title"/>
          <template v-if="data.item.snippet"><span class="pl-3 text-caption font-italic" v-html="data.item.snippet"/>
          </template>
        </template>
      </v-combobox>
    </v-col>
    <v-col cols="4">
      <v-text-field
        hide-details
        dense
        type="number"
        min="0"
        max="600"
        v-model.number="service.durationInMonths"
        label="Durée (mois, optionnel)"
        placeholder="ex: 12"
      />
    </v-col>
    <v-col cols="4">
      <v-text-field
        hide-details
        dense
        type="date"
        v-model="service.startDate"
        label="Date de début (optionnel)"
      />
    </v-col>
    <v-col cols="4">
      <v-text-field
        hide-details
        dense
        type="date"
        v-model="service.endDate"
        label="Date de fin (optionnel)"
      />
    </v-col>
    <v-col cols="8">
      <v-text-field
        hide-details
        dense
        v-model="service.organizationName"
        label="Employeur (optionnel)"
      />
    </v-col>
    <v-col cols="4">
      <v-btn @click="() => service.submitExperience()" outlined :loading="service.loading"
             :disabled="!service.occupation" class="full-width" style="width: 100%">Valider
      </v-btn>
    </v-col>
  </v-row>
</template>

<script>
import UserExperiencesService from './UserExperiencesService';

export default {
  name: 'user-experience',
  props: {
    service: {
      required: true,
      type: UserExperiencesService,
    },
  },
  methods: {
    removeExperience(id) {
      this.service.removeExperience(id);
    },
  },
};
</script>

