import SafeService from 'odas-plugins/SafeService';
import { ErhgoOccupationSearch, ExperienceDetails, ExperienceType } from 'erhgo-api-client';
import Vue from 'vue';
import asyncDebounce from 'async-debounce';
import { v4 as uuid } from 'uuid';


export default class UserExperiencesService extends SafeService {

  private _experiences: ExperienceDetails[] = [];
  private _occupations: ErhgoOccupationSearch[] = [];
  private _query = '';

  private _fileImportState = '';

  private _experienceId = uuid();

  public occupation: ErhgoOccupationSearch | null = null;
  public experienceType = ExperienceType.JOB;
  public durationInMonths: number | null = null;
  public startDate: string | null = null;
  public endDate: string | null = null;
  public organizationName = '';


  constructor(private _userId: string) {
    super();
  }

  async fetchExperiences() {
    await this.safeCall(async () => {
      this._experiences = (await Vue.$api.getUserExperiences(this._userId)).data;
    });
  }

  get experiences() {
    return this._experiences;
  }

  async generateExperiencesFromFile(file: File) {
    await this.safeCall(async () => {
      await Vue.$api.generateExperiencesFromCV(this._userId, file);
    });

    await this.fetchExperiences();
  }

  async fetchFileImportState() {
    await this.safeCall(async () => {
      this._fileImportState = (await Vue.$api.getUserFileImportState(this._userId)).data;
    });
  }

  async removeExperience(id: string) {
    await this.safeCall(async () => {
      await Vue.$api.deleteExperience(id);
      this._experiences = this._experiences.filter(x => x.id !== id);
    });
  }

  private debouncedSearch = asyncDebounce(async () => {
    await this.safeCall(async () => {
      if (this._query) {
        this._occupations = (await Vue.$api.searchOccupations(this._query, true)).data;
      }
    });
  }, 700);

  set query(query: string) {
    this._query = query;
    this.debouncedSearch.fn();
  }

  get fileImportState() {
    return this._fileImportState;
  }

  set fileImportState(fileImportState) {
    this._fileImportState = fileImportState;
  }

  get occupations() {
    return this._occupations;
  }

  get erhgoOccupationId() {
    return this.occupation?.code;
  }

  get jobTitle() {
    return this.occupation?.title || '';
  }

  get experienceId() {
    return this._experienceId;
  }

  private getInnerText() {
    const tmp = document.createElement('div');
    tmp.innerHTML = this.jobTitle;
    tmp.remove();
    return tmp.textContent || tmp.innerText || '';
  }

  async submitExperience() {
    await this.safeCall(async () => {
      const {
        _userId: userId,
        experienceType,
        durationInMonths,
        startDate,
        endDate,
        organizationName,
        erhgoOccupationId,
        experienceId,
      } = this;
      const experience = {
        userId,
        experienceType,
        durationInMonths: durationInMonths ?? undefined,
        startDate: startDate ?? undefined,
        endDate: endDate ?? undefined,
        organizationName,
        erhgoOccupationId,
        experienceId,
        jobTitle: this.getInnerText(),
      };
      await Vue.$api.saveExperience(experience);
      this._experiences = [...this.experiences, {...experience, id: experienceId}];
      this.occupation = null;
      this.organizationName = '';
      this.durationInMonths = null;
      this.startDate = null;
      this.endDate = null;
      this._experienceId = uuid();
    });
  }
}
