<template>
  <div>
    <div class="my-5">
      <v-row>
        <user-channel-selector-btn
          :selected-users="candidates"
          :user-management-service="userManagementService"
          :organizations-to-remove="organizationsToRemove"
          :organizations-to-add="organizationsToAdd"
          @finish="retrieveChannelAffectationInfo"
        />
        <v-spacer/>
        <p v-if="!prescriberIsNotDefined"
           class="text-caption primary--text font-weight-bold">
          {{ prescriberText }}
        </p>
      </v-row>
    </div>
    <v-data-table
      :headers="headers"
      :items="channelAffectations"
      :footer-props="{
        'items-per-page-text': 'Lignes par page',
        'items-per-page-options': rowsPerPage
      }"
      sort-by="isThePrescriber"
      :sort-desc="true"
      class="elevation-1"
    >
      <template v-slot:item.affectedAt="{ item }">
        {{ item.affectedAt | formatDate }}
      </template>

      <template v-slot:item.channelSourceType="{ item }">
        {{ channelSourceText[item?.channelSourceType] }}
      </template>
      <template v-slot:footer.page-text="props">
        Lignes de {{ props.pageStart }} à {{ props.pageStop }} sur {{ props.itemsLength }}
      </template>
    </v-data-table>
  </div>
</template>
<script>
import UserChannelAffectationsService from '@/views/repository/user/edit/UserChannelAffectationsService';
import UserChannelSelectorBtn from '@/views/setup/user/UserChannelSelectorBtn';
import UserManagementService from '@/components/services/UserManagementService';
import _ from 'lodash';
import { UserChannelSource } from 'erhgo-api-client';

const ChannelSourceText = {
  LANDING_PAGE: 'Via la page entreprise',
  ORGANIZATION: 'Via l\'organisation',
  CANDIDATURE: 'Via une candidature',
  URL: 'Via une url taguée',
  OTHER: 'Raison technique',
  NOTHING: 'Aucune raison',
  UNKNOWN: 'Raison inconnue',
  NOT_UNDERSTOOD: 'Pas compris',
  CREATION_BO: 'Via une création depuis le BO',
  ADMIN: 'Via une action admin',
  ENTERPRISE_PAGE: 'Via une page entreprise',
};
export default {
  name: 'list-user-channel-affectations',
  components: {UserChannelSelectorBtn},
  props: {
    userId: {
      required: true,
      type: String,
    },
  },
  data() {
    return {
      channelAffectations: [],
      headers: [
        {text: 'Code entreprise', value: 'organizationCode'},
        {text: 'Nom de l\'entreprise', value: 'organizationName'},
        {text: 'Raison de l\'affectation', value: 'channelSourceType'},
        {text: 'Date de création', value: 'affectedAt'},
      ],
      rowsPerPage: [10, 25, 50, 100, 200],
      service: null,
      channelSourceText: ChannelSourceText,
      userManagementService: null,
      organizationsToRemove: [],
      organizationsToAdd: [],
      prescriber: null,
    };
  },
  async created() {
    this.service = new UserChannelAffectationsService(this.userId);
    this.userManagementService = new UserManagementService();
    await this.retrieveChannelAffectationInfo();
  },
  methods: {
    async retrieveChannelAffectationInfo() {
      await this.service.fetchChannelAffectationInformation();
      this.channelAffectations = this.service.userChannelAffectations;
      this.prescriber = this.service.prescriber;
      const codes = this.service.affectationOrganizationCodes;
      this.organizationsToRemove = codes?.length ? (await this.$api.getAllOrganizationsByCodes(this.service.affectationOrganizationCodes)).data : [];
      this.organizationsToAdd = (await this.$api.getAllRecruiters()).data.filter(add => !this.organizationsToRemove.some(remove => remove.code === add.code),
      );
    },
  },
  computed: {
    prescriberText() {
      if (this.prescriber?.channelSourceType === UserChannelSource.CREATION_BO) {
        return 'Utilisateur créé depuis le BO';
      }
      const channelSourceTextFormatted = _.lowerFirst(ChannelSourceText[this.prescriber?.channelSourceType]);
      return `Le prescripteur est ${this.prescriber?.organizationName} (${this.prescriber?.organizationCode}) ${channelSourceTextFormatted}`;
    },
    candidates() {
      return [{id: this.userId}];
    },
    prescriberIsNotDefined() {
      return !!this.prescriber &&
        this.prescriber.organizationName == null &&
        this.prescriber.organizationCode == null &&
        this.prescriber.channelSourceType === UserChannelSource.UNKNOWN;
    },
  },
};
</script>
