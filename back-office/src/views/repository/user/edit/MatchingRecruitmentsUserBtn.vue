<template>
  <div>
    <v-btn
      id="matching-recruitment-bo-user"
      outlined
      small
      @click="displayPopup = true">
      Voir les recrutements correspondant
      <v-icon>people</v-icon>
    </v-btn>
      <v-dialog v-model="displayPopup" v-if="displayPopup" max-width="1200">
        <v-card>
          <div class="d-inline-flex mt-3 " v-if="matchingRecruitmentsService.showAPIError">
            <v-alert type="error" outlined dense>
              Une erreur est survenue, veuillez réessayer.
            </v-alert>
          </div>
          <v-data-table
            :headers="headers"
            :items="recruitmentItems"
            :items-per-page="5"
            class="elevation-1"
            disable-sort
          >
            <template v-slot:item="data">
              <tr>
                <td>{{ data.item.jobTitle }}</td>
                <td>{{ data.item.city ?? "Recrutement non localisé" }}</td>
                <td>{{ data.item.organizationName }}</td>
                <td>
                  <v-checkbox :input-value="recruitments.includes(data.item.code)"
                              @click="handleCheckboxClick(data.item.code)" />
                </td>
              </tr>
            </template>
            <template slot="no-data">
              <v-alert :value="true"
                       outlined
                       color="error"
                       icon="warning">
                Aucun résultat disponible
              </v-alert>
            </template>
            <template v-slot:footer.page-text="props">
              Lignes de {{ props.pageStart }} à {{ props.pageStop }} sur {{ props.itemsLength }}
            </template>
          </v-data-table>
          <div class="d-flex mt-5">
            <v-btn
              id="close-matching-recruitment"
              class="ml-16 mb-4"
              color="error"
              outlined
              @click="displayPopup = false"
            >
              <v-icon>cancel</v-icon>
              <strong>Annuler</strong>
            </v-btn>
            <v-spacer/>
            <span class="text-caption" v-if="!!userLocation">
              <v-icon small>mdi-information</v-icon>
             Seuls les recrutements à moins de 150km du candidat sont remontés
            </span>
            <v-spacer/>
            <v-btn
              :loading="matchingRecruitmentsService.loading"
              class="mr-16 mb-4"
              id="submit-matching-recruitment"
              outlined
              primary
              @click="generateCandidatures"
            > Positionner le candidat
              <v-icon>check</v-icon>
            </v-btn>
          </div>
        </v-card>
      </v-dialog>
  </div>
</template>
<script>

import { MatchingRecruitmentsService } from './MatchingRecruitmentsService';
import UserDetailService from '../detail/UserDetailService';

export default {
  name: 'matching-recruitments-user-btn',
  data() {
    return {
      generatingCandidature: false,
      recruitments: [],
      displayPopup: false,
      headers: [
        {
          text: 'Recrutement',
          align: 'start',
          sortable: false,
          value: 'name',
        },
        {text: 'Localisation'},
        { text: 'Organisation' },
        {text: 'Action'},
      ],
      matchingRecruitmentsService: null,
    };
  },
  props: {
    userId: {
      required: true,
      type: String,
    },
    userDetailService: {
      required: true,
      type: UserDetailService,
    },
  },
  computed: {
    recruitmentItems() {
      return this.matchingRecruitmentsService.matchingRecruitmentsPage?.content;
    },
    userContactInfo() {
      return this.userDetailService?.userInfo;
    },
    userLocation() {
      return this.userContactInfo?.contactInformation.location;
    },
  },
  methods: {
    handleCheckboxClick(id) {
      if (this.recruitments.includes(id)) {
        this.recruitments = this.recruitments.filter(a => a !== id);
      } else {
        this.recruitments = [id, ...this.recruitments];
      }
    },

    async generateCandidatures() {
      await this.matchingRecruitmentsService.generateCandidaturesOnRecruitment(this.userId, this.recruitments);
      if (!this.matchingRecruitmentsService.showAPIError) {
        this.$emit('candidaturesGenerated', this.recruitments.length);
        this.displayPopup = false;
      }
    },

    displayDetailModal(job) {
      this.modalRecruitment = job;
      this.displayModal = true;
    },
  },
  watch: {
    async displayPopup(newValue, oldValue) {
      if (newValue !== oldValue) {
        await this.userDetailService.fetchUserInfo();
        await this.matchingRecruitmentsService.fetchMatchingRecruitments(300);
      }
    },
  },
  async created() {
    this.matchingRecruitmentsService = new MatchingRecruitmentsService();
  },
};
</script>
