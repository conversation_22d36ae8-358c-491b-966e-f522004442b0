<template>
  <criteria-selector v-model="service.selectedValues" :showAsFO="true" v-if="initialized">
    <template v-slot:thresholdTitle>
      <p class="text-caption">
        Indiquez le niveau maximal atteint par le candidat pour chaque critère
      </p>
    </template>
    <template v-slot:multipleTitle>
      <p class="text-caption">
        Vous pouvez choisir tous les éléments acceptés par le candidat pour chaque critère
      </p>
    </template>
  </criteria-selector>
</template>

<script>
import CriteriaSelector from '@/components/criteria/CriteriaSelector';
import UserCriteriaService from '@/components/criteria/UserCriteriaService';

export default {
  name: 'user-criteria',
  components: {CriteriaSelector},
  props: {
    userId: {
      required: true,
      type: String,
    },
  },
  data() {
    return {
      service: null,
      initialized: false,
    };
  },
  async created() {
    this.service = new UserCriteriaService(this.userId, true);
    await this.service.fetchAllCriteria();
    await this.service.fetchUserCriteria();
    this.initialized = true;
  },
};
</script>

