import SafeService from 'odas-plugins/SafeService';
import {ChannelAffectationInformations, ChannelAffectationItem, Prescriber} from 'erhgo-api-client';
import Vue from 'vue';

export default class UserChannelAffectationsService extends SafeService {
  private _affectationsInfo: ChannelAffectationInformations | null = null;

  constructor(private _userId: string) {
    super();
  }

  async fetchChannelAffectationInformation() {
    await this.safeCall(async () => {
      this._affectationsInfo = (await Vue.$api.getUserChannelAffectations(this._userId)).data;
    });
  }

  get userChannelAffectations(): Array<ChannelAffectationItem> | undefined {
    return this._affectationsInfo?.userChannelAffectations;
  }

  get prescriber(): Prescriber | undefined {
    return this._affectationsInfo?.prescriber;
  }

  get affectationOrganizationCodes() {
    return this.userChannelAffectations?.map((a) => a.organizationCode);
  }
}
