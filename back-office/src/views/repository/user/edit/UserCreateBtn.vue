<template>
  <div>
    <v-btn
      id="create-fo-user"
      outlined
      small
      @click="displayPopup = true">
      <PERSON>réer un utilisateur
      <v-icon>people</v-icon>
    </v-btn>
    <v-dialog v-model="displayPopup" v-if="displayPopup" max-width="800">
      <v-form ref="form">
        <v-card>
          <v-card-title class="text-h5">
            Informations générales
          </v-card-title>
          <v-card-text>
            <v-alert type="error" outlined v-if="service.conflictSourceLabel">
              Au moins un utilisateur existe déjà pour {{ service.conflictSourceLabel }}.
              <ul v-if="service.conflictedUsers.length">
                <li v-for="(user, index) in service.conflictedUsers" :key="user.userId">
                  <router-link
                    target="_blank"
                    :to="{
                    name: 'front_user_detail',
                    params: {
                      userId: user.userId,
                    }}">
                    Utilisateur {{ index + 1 }}
                    {{ user.phoneNumber ? ` (Numéro de téléphone&nbsp;: ${user.phoneNumber}) ` : '' }}
                    <v-icon x-small>fa-external-link-alt</v-icon>
                  </router-link>
                </li>
              </ul>
            </v-alert>
            <v-row>
              <v-col>
                <v-text-field v-model="service.firstName"
                              label="Prénom :"/>
                <v-text-field v-model="service.lastName"
                              label="Nom :"/>
              </v-col>
              <v-divider vertical/>
              <v-col>
                <v-text-field v-model="service.email"
                              id="new-user-email"
                              label="Email :"
                              :rules="emailRules()"
                />
                <phone-field v-model="service.phoneNumber"
                             id="new-user-phone"
                             placeholder="Saisissez un n° de téléphone"
                             :rules="[v => !!(service.phoneNumber && service.phoneNumber.length) || 'Le numéro de téléphone est obligatoire']"/>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-btn
              color="error"
              outlined
              @click="displayPopup = false"
              :loading="service.loading"
            >
              <v-icon>cancel</v-icon>
              <strong>Annuler</strong>
            </v-btn>
            <v-spacer/>
            <v-btn
              id="submit-fo-creation"
              outlined
              primary
              link
              @click="submit"
              :loading="service.loading"
            > Valider
              <v-icon>check</v-icon>
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-form>
    </v-dialog>
  </div>
</template>
<script>
import UserCreateService from './UserCreateService';
import PhoneField from 'odas-plugins/PhoneField';

export default {
  name: 'user-create-btn',
  components: {PhoneField},
  data() {
    return {
      service: new UserCreateService(),
      displayPopup: false,
    };
  },
  methods: {
    async submit() {
      if (this.$refs.form.validate()) {
        const userId = await this.service.createUser();
        if (userId) {
          this.$router.push({
            name: 'user-edit',
            params: {
              userId,
            },
          });
        }
      }
    },
    emailRules() {
      return [
        v => !!v || 'Ce champ est requis',
        v => /^[a-zA-Z0-9_][a-zA-Z0-9._+-]*@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/.test(v) || 'Adresse email invalide',
      ];
    },
  },
};
</script>
