<template>
  <div>
    <v-data-table
      :headers="headers"
      :items="recruitmentReports"
      :items-per-page="5"
      :footer-props="{
        'items-per-page-text': 'Lignes par page',
        'items-per-page-options': rowsPerPage
      }"
      class="elevation-1"
    >
      <template v-slot:item.candidature="{ item }">
        <v-tooltip bottom>
          <template v-slot:activator="{ on, attrs }">
            <div v-on="on"
                 v-bind="attrs">
              <v-btn v-if="item.candidature && item.candidature.state && item.candidature.state != CandidatureState.NOT_FINALIZED"
                     icon
                     small
                     :to="{ name: 'candidature_details', params: {
                     candidature_id: item.candidature.id,
                     organization_code: item.recruiterCode
                     },
                   }"
                     target="_blank"
              >
                <v-icon color="success">fa-check-square</v-icon>
              </v-btn>
              <v-icon v-else-if="item.candidature" color="blue" class="pl-1">fal fa-hourglass-half</v-icon>
              <v-icon v-else color="error">fa-times-circle</v-icon>
            </div>
          </template>
          <div v-if="item.candidature">
            <ul>
              <li>État de la candidature&nbsp;: {{
                  item.candidature.state ? $t(`candidaturesRealStates.title.${item.candidature.state}`) : 'Non finalisée'
                }}
              </li>
              <li>Publiée le&nbsp;: {{ item.candidature.publishedAt | formatDateTime }}</li>
              <li>Générée&nbsp;: {{ item.candidature.generatedFromSourcing ? 'Oui':'Non' }}</li>
              <li>Visible du candidat&nbsp;: {{ item.candidature.visibleForUser ? 'Oui':'Non' }}</li>
            </ul>
          </div>
          <span v-else>L'individu n'a pas candidaté pour cette offre</span>
        </v-tooltip>
      </template>
      <template v-slot:item.recruiterType="{ item }">
        {{ $t(`organizationType.${item.recruiterType}`) }}
      </template>
      <template v-slot:item.notifications="{ item }">
        <v-tooltip bottom>
          <template v-slot:activator="{ on, attrs }">
            <v-icon :color="item.notifications.length ? 'success': 'error'"
                    v-bind="attrs"
                    v-on="on"
            >
              {{ item.notifications.length ? 'fa-check-square' : 'fa-times-circle' }}
            </v-icon>
          </template>
          <div v-if="item.notifications.length">
            <ul style="list-style-type: none" v-for="n in item.notifications" v-bind:key="n.createdAt">
              <li>Reçue&nbsp;: {{ notificationType[n.type] }}</li>
              <li>Envoyée le&nbsp;: {{ n.createdAt | formatDateTime }}</li>
            </ul>
          </div>
          <span v-else>L'individu n'a pas été notifié pour cette offre</span>
        </v-tooltip>
      </template>

      <template v-slot:item.candidature.archived="{ item }">
        <v-icon :color="item.candidature?.archived ? 'success': 'error'">
          {{ item.candidature?.archived ? "fa-check-square" : "fa-times-circle" }}
        </v-icon>
      </template>

      <template v-slot:footer.page-text="props">
        Lignes de {{ props.pageStart }} à {{ props.pageStop }} sur {{ props.itemsLength }}
      </template>
    </v-data-table>
  </div>
</template>
<script>
import UserRecruitmentReportService from '@/views/repository/user/edit/UserRecruitmentReportService';
import { CandidatureState } from 'erhgo-api-client';

const notificationType = {
  MOBILE: 'via mobile',
  EMAIL: 'via email',
  BOTH: 'via mobile et email',
};

export default {
  name: 'list-user-recruitment-report',
  props: {
    userId: {
      required: true,
      type: String,
    },
  },
  data() {
    return {
      service: null,
      recruitmentReports: [],
      headers: [
        {text: 'Titre du recrutement', value: 'recruitmentTitle'},
        {text: 'Nom recruteur', value: 'recruiterName'},
        {text: 'Type du recruteur', value: 'recruiterType'},
        {text: 'Candidaté', value: 'candidature'},
        {text: 'Notifié', value: 'notifications'},
        {text: 'Archivé', value: 'candidature.archived'},
      ],
      rowsPerPage: [10, 25, 50, 100, 200],
      notificationType,
      CandidatureState,
    };
  },
  async created() {
    this.service = new UserRecruitmentReportService(this.userId);
    await this.service.fetchRecruitmentReportsByUser();
    this.recruitmentReports = this.service.recruitmentReportsByUser;
  },
};
</script>
