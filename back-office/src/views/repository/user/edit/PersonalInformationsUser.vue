<template>
  <v-expansion-panel v-if="initialized">
    <v-expansion-panel-header
      class="ma-0 pa-2 font-weight-bold">
      <v-row no-gutters>
        <v-col class="d-flex  align-center header justify-center">
          <span class="pr-2">
            <v-icon
              v-if="service.success"
              color="success">fa-check-circle
            </v-icon>
            <v-progress-circular
              :size="25"
              color="primary"
              indeterminate
              v-else-if="service.loading"
            />
            <v-icon v-else>fa-info-circle</v-icon>
          </span>
          <div class="text-decoration-underline text-h5">Informations personnelles</div>

        </v-col>
      </v-row>
    </v-expansion-panel-header>
    <v-expansion-panel-content>
      <v-alert v-if="service.showAPIError" type="error" outlined>
        Une erreur est survenue - veuillez rafraichir la page et
        contacter le support si le problème persiste.
      </v-alert>
      <v-row no-gutters class="pt-5" justify="center">
        <v-col cols="12" sm="4" class="pr-sm-2">
          <div class="text-h5 center-text text-center pb-5">
            <v-icon class="pr-2">fal fa-user</v-icon>
            Infos perso
          </div>
          <div class="personalInformation__line">
            <label class="text-body-2 font-weight-bold">
              Prénom
            </label>
            <v-text-field dense
                          :disabled="service.disabled"
                          placeholder="Saisissez le prénom"
                          class="mb-3"
                          v-model="info.firstName"
                          autocomplete="off"
                          hide-details
            />
          </div>
          <div class="personalInformation__line">
            <label class="text-body-2 font-weight-bold">
              Nom
            </label>
            <v-text-field dense
                          :disabled="service.disabled"
                          placeholder="Saisissez le nom de famille"
                          v-ripple="false"
                          class="mb-3"
                          name="lastname"
                          v-model="info.lastName"
                          autocomplete="off"
                          hide-details
            />
          </div>
          <div class="personalInformation__line">
            <label class="text-body-2 font-weight-bold">
              Ville
            </label>
            <vuetify-geo-places
              persistent-hint
              :show-radius="true"
              :radius-for-candidates="true"
              @input="updateCity"
              :value="info.location"
              no-data-text="Aucune correspondance"
              outlined
            />
          </div>
        </v-col>
          <v-col cols="12" sm="4" class="px-sm-2 align-center grey lighten-3">
            <div class="text-h5 center-text text-center pb-5">
              <v-icon class=" pr-2">fal fa-phone</v-icon>
              Contact
            </div>
            <div>
              <div class="personalInformation__line">
                <label class="align-center text-body-2 font-italic">
                  <template v-if="info.lastMobileConnexionDate">
                    Dernière utilisation de l’application mobile le
                    <strong>{{ moment(info.lastMobileConnexionDate).format("DD/MM/YY") }}</strong>
                  </template>
                  <template v-else>
                    Pas d'utilisation récente de l'application mobile.
                  </template>
                </label>
              </div>
            </div>
            <div class="mb-3">
              <div class="personalInformation__line">
                <label class="align-center text-body-2 font-weight-bold">
                  Numéro de téléphone
                </label>
                <phone-field v-model="info.phoneNumber"
                             :disabled="service.disabled"
                             placeholder="Numéro de téléphone"
                />
              </div>
            </div>
            <div class="personalInformation__line">
              <label class="text-body-2 font-weight-bold">Adresse email</label>
              <v-tooltip top>
                {{
                  emailEditable ? 'Cliquer sur l\'icone pour confirmer l\'adresse mail' : 'Cliquer sur le crayon pour modifier l\'adresse mail'
                }}
                <template v-slot:activator="{ on }">
                  <v-text-field
                    v-on="on"
                    outlined
                    v-model="email"
                    dense
                    :readonly="!emailEditable"
                    @click:append-outer="editOrValidateEmail"
                    :append-outer-icon="emailEditable?'send':'fa-pencil'"
                    :loading="service.loading"
                    :error-messages="service.emailError"
                  />
                </template>
              </v-tooltip>
              <v-checkbox
                class="ma-0 pa-0"
                v-model="service.jobOffersOptIn"
                label="Accepte de recevoir des offres d'emploi qui me correspondent et des informations de #jenesuisPASunCV par mail"
              />
              <v-checkbox
                class="ma-0 pa-0"
                dense
                v-model="service.jobDatingOptIn"
                label="L'informer quand un évènement #jenesuisPASunCV se déroule à proximité (jobdating)"
              />
            </div>
          </v-col>
          <v-col cols="12" sm="4" class="pl-sm-2 align-center">
            <div class="text-h5 center-text text-center pb-5">
              <v-icon class=" pr-2">fal fa-ellipsis</v-icon>
              Autres
            </div>
            <div class="personalInformation__line">
              <label class="text-body-2 font-weight-bold">Source</label>
              <v-text-field
                :disabled="service.disabled"
                v-model="info.source"
                persistent-hint
                hint="Lien/Adresse web de la source (Indeed, Pôle emploi...)"
                dense
                v-ripple="false"/>
            </div>

            <div class="personalInformation__line">
              <label class="text-body-2 font-weight-bold">Salaire brut annuel souhaité</label>
              <integer-field v-model="info.salary" append-icon="fa-euro-sign" :min="10000" />
            </div>
            <div class="personalInformation__line">
              <label class="text-body-2 font-weight-bold">
                Ma situation professionnelle
              </label>
              <v-select
                  hide-details
                  dense
                  class="white mb-3"
                  :menu-props="{ offsetY: true }"
                  name="situation"
                  placeholder="Quelle est votre situation ?"
                  :items="selectSituationStep1Items"
                  v-model="info.situation"

              />
            </div>
            <div class="personalInformation__line" v-if="service.isAvailable">
              <label class="text-body-2 font-weight-bold">
                Ma disponibilité
              </label>
              <v-select
                hide-details
                :menu-props="{ offsetY: true }"
                name="delay"
                v-model="info.delayInMonth"
                dense
                class="select-sm white"
                :items="Array.from({length:7},(_unused,key)=> key)
                        .map(i => ({text: i ? `Sous ${i} mois`: 'Disponible de suite', value: i || null}))"
              />
            </div>
          </v-col>
        </v-row>
    </v-expansion-panel-content>
  </v-expansion-panel>
</template>
<script>
import VuetifyGeoPlaces from 'odas-plugins/VuetifyGeoPlaces';
import IntegerField from 'odas-plugins/IntegerField';
import UserGeneralInformationService from './UserGeneralInformationService';
import PhoneField from 'odas-plugins/PhoneField';
import { Situation } from 'erhgo-api-client';
import moment from 'moment/moment';

const selectSituationStep1Items = [
  {text: 'Je suis en recherche active', value: Situation.RESEARCHING},
  {text: 'Je suis à l\'écoute du marché', value: Situation.STANDBY},
  {text: 'Je ne suis pas en recherche', value: Situation.EMPLOYEE},
];

export default {
  name: 'personal-informations-user',
  components: {VuetifyGeoPlaces, IntegerField, PhoneField},
  props: {
    userId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      moment,
      service: null,
      initialized: false,
      emailEditable: false,
      email: '',
      selectSituationStep1Items,
    };
  },
  async created() {
    this.service = new UserGeneralInformationService(this.userId);
    await this.service.fetchGeneralInformations();
    this.initialized = true;
    this.email = this.info.email;
  },
  methods: {
    updateCity(place) {
      if (!!place) {
        const {city, citycode, postcode, departmentCode, regionName, latitude, longitude, radiusInKm} = place;
        this.info.location = {
          city,
          citycode,
          postcode,
          departmentCode,
          regionName,
          latitude,
          longitude,
          radiusInKm,
        };
      } else {
        this.info.location = null;
      }
    },
    editOrValidateEmail() {
      if (this.emailEditable) {
        this.info.email = this.email;
      }
      this.emailEditable = !this.emailEditable;
    },
  },
  computed: {
    info() {
      return this.service?.info || null;
    },
  },
  watch: {
    info: {
      deep: true,
      handler() {
        if (this.initialized) {
          this.service.update();
        }
      },
    },
    email(newValue) {
      this.$emit('emailChanged', newValue);
    },
  },
};
</script>
<style>

.v-expansion-panel-header {
  min-height: inherit !important;
}

.personalInformation__line {
  padding: 0.25em 0;
}

.link--two-lines {
  text-decoration: none;
}

.flex-center {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.link--two-lines > span {
  display: inline-block;
}

</style>
