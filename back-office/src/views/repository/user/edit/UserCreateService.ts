import Vue from 'vue';
import SafeService from 'odas-plugins/SafeService';
import {AxiosError} from 'axios';

interface ConflictError {
  id: string;
  source: string;
  conflictedUsers: { userId: string; phoneNumber: string }[];
}
export default class UserCreateService extends SafeService {

  public firstName: string | null = null;
  public lastName: string | null = null;
  public phoneNumber: string | null = null;
  public email: string | null = null;

  private _conflictError: ConflictError | null = null;

  private static _CONFLICT_SOURCE_LABELS = new Map<string, string>([
    ['email', "l'email"],
    ['phoneNumber', 'le numéro de téléphone'],
  ]);

  async createUser() {
    return this.safeCall(async () => {
      try {
        return (await Vue.$api.createUserFO({
          firstName: this.firstName || undefined,
          lastName: this.lastName || undefined,
          phoneNumber: this.phoneNumber!,
          email: this.email || undefined,
        })).data.userId;
      } catch (e) {
        if ((e as AxiosError)?.response?.status !== 409) {
          throw e;
        }
        this._conflictError = ((e as AxiosError)?.response?.data as ConflictError);
      }
    });
  }

  get conflictSourceLabel() {
    const source = UserCreateService._CONFLICT_SOURCE_LABELS.get(this._conflictError?.source || '');
    return source ? `${source} ${this._conflictError?.id}` : '';
  }

  get conflictedUsers() {
    return this._conflictError?.conflictedUsers || [];
  }
}
