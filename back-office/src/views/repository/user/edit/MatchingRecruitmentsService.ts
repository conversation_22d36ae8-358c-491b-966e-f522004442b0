import SafeService from 'odas-plugins/SafeService';
import Vue from 'vue';
import { SimpleRecruitmentPage } from 'erhgo-api-client';

export class MatchingRecruitmentsService extends SafeService {

  private _matchingRecruitmentsPage: SimpleRecruitmentPage | null = null;

  constructor() {
    super();
  }

  async fetchMatchingRecruitments(size: number) {
    await this.safeCall(async () => {
      this._matchingRecruitmentsPage = (await Vue.$api.getRecruitments(
        0,
        size,
      )).data;
    });
  }

  async generateCandidaturesOnRecruitment(userId: string, recruitmentsId: number[]) {
    await this.safeCall(async () =>
      Vue.$api.generateCandidaturesOnRecruitments({userId, recruitmentsId}),
    );
  }

  get matchingRecruitmentsPage(): SimpleRecruitmentPage | null {
    return this._matchingRecruitmentsPage;
  }
}
