<template>
  <v-card flat>
      <v-card-title>
          <h3>
              <v-icon
                      class="mx-1"
                      large
                      right>
                  mdi-account-search
              </v-icon>
              Simulation de recherche de candidat "je recrute"
          </h3>
    </v-card-title>
    <v-card-text>
        <div class="d-flex justify-start">
            <v-row no-gutters>
                <v-col cols="4" class="form-part">
                    <sourcing-simulator-form :service="service"/>
                </v-col>
                <v-col cols="8" class="result-part">
                    <div class="scrollable-content">
                        <sourcing-simulator-result :service="service"/>
                    </div>
                </v-col>
            </v-row>
        </div>
    </v-card-text>
  </v-card>
</template>
<script>
import SourcingSimulatorForm from './SourcingSimulatorForm';
import SourcingSimulatorResult from './SourcingSimulatorResult';
import SourcingSimulatorService from './SourcingSimulatorService';

export default {
  components: {SourcingSimulatorForm, SourcingSimulatorResult},
  data() {
    return {
      service: new SourcingSimulatorService(),
    };
  },
  created() {
    this.service.fetchAllErhgoClassifications();
    this.service.simulateSourcingFilters(false);
  },
};
</script>
<style scoped>
.form-part,
.result-part {
    height: 80vh;
    overflow-y: auto;
}

</style>
