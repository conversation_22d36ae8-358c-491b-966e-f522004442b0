<template>
  <div>
    <v-card
      v-if="candidatesResult && !service.showAPIError && !service.loading"
      flat
    >
      <v-row no-gutters>
        <div class="mx-auto text-h4 font-weight-medium">Le
          nombre de candidats est de
          <span class="primary--text font-weight-black">{{ candidatesResult.candidatesCount }}</span>
        </div>
      </v-row>
      <v-row no-gutters>
        <template v-if="candidatesResult.candidatesCount <= 2000">
          <v-col>
            <v-switch class="mx-5" v-model="withDetails">
              <template v-slot:label>
                {{ sourcingSimulatorQuery.withDetails ? 'Cacher' : 'Afficher' }} les détails
              </template>
            </v-switch>
          </v-col>
          <v-col>
            <v-switch v-if="withDetails" v-model="sourcingSimulatorQuery.showTopTen">
              <template v-slot:label>
                {{ sourcingSimulatorQuery.showTopTen ? 'Cacher' : 'Afficher' }} le top ten des candidats
              </template>
            </v-switch>
          </v-col>
        </template>
        <v-col v-else>

          <v-alert outlined
                   dense
                   color="error"
                   class="text-center">
            Le nombre de candidat est trop important pour afficher les détails des candidats, vous pouvez préciser
            votre
            recherche pour les afficher
          </v-alert>
        </v-col>
      </v-row>
      <v-row no-gutters>
        <v-col v-if="withDetails && candidatesResult.candidatesCount <= 2000">
          <v-data-table
            dense
            :headers="headers" :items="candidatesResult.candidateDetail ? candidatesResult.candidateDetail : []"
            class="elevation-1"
            no-data-text="Aucun candidat trouvé"
            :items-per-page="100"
            :footer-props="{
                        'items-per-page-options': rowsPerPage,
                        'items-per-page-text': 'Lignes par page',
                      }"
          >
            <template v-slot:item="{item}">
              <tr>
                <td class="text-left">{{ item.city }}</td>
                <td class="text-left">{{ item.masteryLevelAsFloat }}</td>
                <td class="text-left">{{ item.romeExperiencesCount ? item.romeExperiencesCount : 0 }}</td>
                <td class="text-left">{{ item.capacityScore ? item.capacityScore : 0 }}</td>
                <td class="text-left">{{ getRefusedClassificationsAsString(item.refusedClassifications) }}</td>
                <td class="text-left">
                  <v-tooltip top>
                    <template v-slot:activator="{ on }">
                      <v-btn icon
                             small
                             :disabled="!item.id"
                             color="primary"
                             v-on="on"
                             :to="{
                   name: 'user-edit',
                   params: {
                     userId:item.id
                   },
                }"
                             target="_blank"
                      >
                        <v-icon small>fa-pencil</v-icon>
                      </v-btn>
                    </template>
                    <span>Modifier l'utilisateur</span>
                  </v-tooltip>
                </td>
              </tr>
            </template>
            <template v-slot:footer.page-text="props">
              Lignes de {{ props.pageStart }} à {{ props.pageStop }} sur {{ props.itemsLength }}
            </template>
          </v-data-table>
        </v-col>
      </v-row>
    </v-card>
    <div v-if="service.loading" class="d-flex justify-center">
      <v-progress-circular indeterminate :size="50" :width="5"/>
    </div>
    <v-alert outlined
             v-if="service.showAPIError"
             dense
             color="error"
             class="text-center">Problème de serveur, veuillez rafraîchir la page
    </v-alert>
  </div>
</template>
<script>

export default {
  props: {
    service: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      withDetails: null,
      rowsPerPage: [10, 25, 50, 100, 200],
    };
  },
  computed: {
    sourcingSimulatorQuery() {
      return this.service.sourcingSimulatorQuery;
    },
    candidatesResult() {
      return this.service.candidatesResult;
    },
    headers() {
      return [
        {text: 'Ville', sortable: false},
        {text: 'Niveau de maîtrise', sortable: false},
        {text: 'Nb expériences associés à des codes ROME', sortable: false},
        {text: 'Nb capacités associées au métier', sortable: false},
        {text: 'Souhaits refusés', sortable: false},
        {text: 'Action', sortable: false},

      ];
    },
  },
  created() {
    this.withDetails = this.sourcingSimulatorQuery.withDetails;
  },
  methods: {
    getRefusedClassificationsAsString(refusedClassifications) {
      return refusedClassifications.length ?
        refusedClassifications.map(rc => this.service.erhgoClassifications.find(eC => eC.code === rc).title).join(', ') :
        'Aucun';
    },
  },
  watch: {
    withDetails(curr) {
      if (!curr) {
        this.sourcingSimulatorQuery.showTopTen = false;
      }
      this.sourcingSimulatorQuery.withDetails = curr;
    },
  },
};
</script>
