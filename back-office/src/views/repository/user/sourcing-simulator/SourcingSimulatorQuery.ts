import {CriteriaValue, ErhgoOccupationDetail, Location, TypeContractCategory, WorkingTime} from 'erhgo-api-client';

export default class SourcingSimulatorQuery {
  occupation: ErhgoOccupationDetail | null = null;
  capacityTolerance = 0;
  masteryLevel: number | null = null;
  activeSearch = true;
  forcedTechnical = false;
  showTopTen = false;
  location: Location | null = null;
  radius = 50;
  salaryRange = [0, 999999];
  lastConnectionDatetime: string = new Date(new Date().setFullYear(new Date().getFullYear() - 2)).toISOString().substring(0, 10);
  typeContractCategory: TypeContractCategory | null = null;
  workingTimeType: WorkingTime | null = null;
  selectedErhgoClassificationsCodes: string[] = [];
  selectedCriteria: CriteriaValue[] = [];
  enableMasteryLevel = false;
  withDetails = false;
  enableRadius = false;
  enableSalaries = false;
  enableClassifications = false;
  enableCapacityTolerance = false;

  get isOccupationTechnical() {
    return !!this.occupation?.isTechnical;
  }

  get locationCity() {
    return this.location?.city || null;
  }
}
