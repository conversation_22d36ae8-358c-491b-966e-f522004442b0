<template>
  <tr :id="`user-${index}`">
    <td class="text-left">
      <v-checkbox
        :input-value="itemProps.isSelected"
        @change="itemProps.select($event)"
      />
    </td>
    <td class="text-left">{{ item.createdAt | formatDate }}</td>
    <td class="text-left">{{ item.lastName }}</td>
    <td class="text-left">{{ item.firstName }}</td>
    <td class="text-left">
      <user-email-wrapper
        :transactional-blacklisted="!!item.transactionalBlacklisted"
      >
        {{ item.email }}
      </user-email-wrapper>
    </td>
    <td class="text-left">
      <v-chip :color="item.enabled ? 'green accent-1' : 'grey lighten-1'">
        {{ item.enabled ? "Activé" : "Désactivé" }}
      </v-chip>
    </td>
    <td class="text-left">{{ formatHandicapField(item.isFromHandicap) }}</td>
    <td class="text-left">
      {{ formatHandicapField(item.handicapModeEnabled) }}
    </td>
    <td class="text-left">
      {{
        !!item.userProfileProgress
          ? item.userProfileProgress.candidaturesCount
          : 0
      }}
    </td>
    <td class="text-left">
      {{ userRegistrationStateValue }}
    </td>
    <td style="width: 5%" class="text-left">
      <v-tooltip bottom>
        <template v-slot:activator="{ on, attrs }">
          <span v-bind="attrs" v-on="on">
            <v-progress-linear
              :color="item.completionRate.color"
              rounded
              height="20"
              :value="item.completionRate.progress"
            />
          </span>
        </template>
        <span>{{ item.completionRate.title }}</span>
      </v-tooltip>
    </td>
    <user-actions-cell :item="item" />
  </tr>
</template>
<script>
import { UserContactInfo, UserRegistrationStateStep } from 'erhgo-api-client';
import UserEmailWrapper from '@/components/user/UserEmailWrapper';
import UserActionsCell from './UserActionsCell';

export default {
  name: 'UserListItem',
  components: { UserActionsCell, UserEmailWrapper },
  props: {
    index: {
      required: true,
      type: Number,
    },
    itemProps: {
      required: true,
      type: UserContactInfo,
    },
  },
  computed: {
    item() {
      return this.itemProps.item;
    },
    userRegistrationStateValue() {
      if (!this.item.userProfileProgress) {
        return 'En erreur';
      }
      let text = '/6';
      switch (
        this.item.userProfileProgress.userRegistrationState
          .userRegistrationStateStep
      ) {
        case UserRegistrationStateStep.CREATED_ACCOUNT:
          text = `1${text}`;
          break;
        case UserRegistrationStateStep.CONFIRMED_SITUATION:
          text = `2${text}`;
          break;
        case UserRegistrationStateStep.CONFIRMED_PROFESSION_DIRECT:
          text = `3${text}`;
          break;
        case UserRegistrationStateStep.CONFIRMED_CITY:
          text = `4${text}`;
          break;
        case UserRegistrationStateStep.CONFIRMED_SALARY:
          text = `5${text}`;
          break;
        case UserRegistrationStateStep.CONFIRMED_CONTACT_DETAILS:
          text = `6${text}`;
          break;
        case UserRegistrationStateStep.NOT_AFFECTED:
          text = 'Ancien compte';
          break;
        default:
          text = 'Inconnu';
      }
      return text;
    },
  },
  methods: {
    formatHandicapField(value) {
      return value ? 'Oui' : 'Non';
    },
  },
};
</script>
