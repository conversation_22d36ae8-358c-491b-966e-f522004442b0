<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            <v-icon left color="primary">mdi-wheelchair-accessibility</v-icon>
            Utilisateurs Handicap ({{ UsersHandicapInfo.length }})
            <v-spacer />
            <v-btn
              :loading="exporting"
              outlined
              @click="exportSimpleUsersInfo"
              small
            >
              <v-icon left>mdi-download</v-icon>
              export simple
            </v-btn>
          </v-card-title>

          <v-card-text>
            <v-row>
              <v-col cols="12">
                <v-text-field
                  v-model="search"
                  label="Rechercher un utilisateur"
                  prepend-icon="mdi-magnify"
                  clearable
                />
              </v-col>
              <v-col cols="12">
                <v-data-table
                  :headers="headers"
                  :items="UsersHandicapInfo"
                  :search="search"
                  :loading="loading"
                  class="elevation-1 clickable-rows"
                  :items-per-page="25"
                  :footer-props="{
                    'items-per-page-options': [10, 25, 50, 100],
                  }"
                  @click:row="openUserEdit"
                >
                  <template v-slot:item.creationDate="{ item }">
                    {{ formatDate(item.creationDate) }}
                  </template>

                  <template v-slot:item.isFromHandicap="{ item }">
                    <v-chip
                      :color="item.isFromHandicap ? 'success' : 'default'"
                      small
                    >
                      {{ item.isFromHandicap ? "Oui" : "Non" }}
                    </v-chip>
                  </template>

                  <template v-slot:item.handicapModeEnabled="{ item }">
                    <v-chip
                      :color="item.handicapModeEnabled ? 'success' : 'default'"
                      small
                    >
                      {{ item.handicapModeEnabled ? "Oui" : "Non" }}
                    </v-chip>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import DownloadUtils from '@/components/mixins/DownloadUtils';

export default {
  name: 'UsersHandicapInfoTable',
  mixins: [DownloadUtils],
  data() {
    return {
      UsersHandicapInfo: [],
      loading: false,
      exporting: false,
      search: '',
      headers: [
        {
          text: 'Identifiant',
          value: 'identifier',
          sortable: true,
        },
        {
          text: 'Date de création',
          value: 'creationDate',
          sortable: true,
        },
        {
          text: 'Prescripteur',
          value: 'prescriber',
          sortable: true,
        },
        {
          text: 'Source',
          value: 'source',
          sortable: true,
        },
        {
          text: 'jenesuispasunhandicap (origine) O/N',
          value: 'isFromHandicap',
          sortable: true,
          align: 'center',
        },
        {
          text: 'jenesuispasunhandicap (actif) O/N',
          value: 'handicapModeEnabled',
          sortable: true,
          align: 'center',
        },
      ],
    };
  },
  async created() {
    await this.getDataFromApi();
  },
  methods: {
    async loadUsersHandicapInfo() {
      try {
        this.loading = true;
        this.UsersHandicapInfo = (await this.$api.getUsersHandicapInfo()).data;
      } finally {
        this.loading = false;
      }
    },
    async getDataFromApi() {
      await this.loadUsersHandicapInfo();
    },
    formatDate(dateString) {
      if (!dateString) return 'N/A';

      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return 'Date invalide';
      }

      return date.toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
      });
    },
    async exportSimpleUsersInfo() {
      this.exporting = true;
      try {
        await this.download(
          async () => await this.$api.getSimpleUsersInfoExport(),
          `utilisateurs_handicap_${new Date().toISOString().slice(0, 10)}.csv`,
        );
      } finally {
        this.exporting = false;
      }
    },
    openUserEdit(item) {
      const route = this.$router.resolve({
        name: 'user-edit',
        params: {
          userId: item.identifier,
        },
      });
      window.open(route.href, '_blank');
    },
  },
};
</script>

<style scoped>
.v-data-table {
  background-color: white;
}

.clickable-rows tbody tr {
  cursor: pointer;
}
</style>
