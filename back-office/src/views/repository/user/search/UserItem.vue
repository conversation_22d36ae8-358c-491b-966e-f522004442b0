<template>
  <v-tooltip top :disabled="!highlight">
    <span v-html="highlight"/>
    <template v-slot:activator="{ on }">
      <tr v-on="on" class="result-item">
        <td>
          {{
            result.item.erhgoOccupations.filter(a => !!(a && a.trim())).join(', ') || 'Aucun'
          }}
        </td>
        <td>
          {{
            channelLabels
          }}
        </td>
        <td>
          {{ result.item.city }}&nbsp;
        </td>
        <td>
          {{
            getLevelLabel(`${result.item.masteryLevel}`)
          }}
        </td>
        <user-actions-cell
          :item="result.item"
          id-key="objectID"
          :organization-code="organizationCode"
        />
      </tr>
    </template>
  </v-tooltip>
</template>
<script>
import UserActionsCell from '../UserActionsCell';

export default {
  name: 'user-item',
  components: {UserActionsCell},
  props: {
    getChannelLabel: {
      type: Function,
      required: true,
    },
    getLevelLabel: {
      type: Function,
      required: true,
    },
    result: {
      type: Object,
      required: true,
    },
    organizationCode: {
      type: String,
      default: null,
    },
  },
  computed: {
    channelLabels() {
      return this.result.item.channels.filter(a => !!(a && a.trim())).map(a => this.getChannelLabel(a))
        .filter(a => !!a)
        .join(', ') || 'Aucun';
    },
    highlight() {
      if (this.result.item._highlightResult) {
        const city = this.highlightForCity();
        const activityAndOccupation = ['experienceTitles', 'activities', 'erhgoOccupations'].map(a => this.highlightForOther(a));
        return [city, ...activityAndOccupation].filter(a => !!a).join(', ');
      } else {
        return null;
      }
    },
  },
  methods: {
    highlightForCity() {
      return (this.result.item._highlightResult['city'] && this.result.item._highlightResult['city'].matchLevel !== 'none') ? `Ville : ${this.result.item._highlightResult['city'].value}` : '';
    },
    highlightForOther(key) {
      const label = {'erhgoOccupations': 'Métier', 'activities': 'Activité', 'experienceTitles': 'Expérience'}[key];
      const value = this.result.item._highlightResult[key] ? this.result.item._highlightResult[key].filter(a => a.matchLevel !== 'none').map(a => a.value).join(', ') : '';
      return value ? `${label} : ${value}` : '';
    },
  },
};
</script>

