<template>
  <v-card v-if="job">
    <v-card-title>
      <h2>
        <v-icon large
                right>people
        </v-icon>
        Candidats pour le poste {{ job.title }}
      </h2>
      <v-spacer/>
      <v-btn outlined
             v-if="!!organization_code"
             :to="{name: 'front_user_list_by_job', params: {organization_code, job_id: jobId}}"
             class="ml-3"
             small>
        Vue "liste"
      </v-btn>
    </v-card-title>
    <v-card-text>
      <user-list
        :prefilter="prefilter"
        :initial-service="service"
      />
    </v-card-text>
  </v-card>
</template>
<script>
import UserList from './List';
import JobSearchService from '../../../setup/job/JobSearchService';
import UserIndexSearchService from '@/views/repository/user/search/UserIndexSearchService';

export default {
  components: {UserList},
  props: {
    jobId: {
      required: true,
    },
    organization_code: {
      required: false,
      default: null,
    },
  },
  data() {
    return {
      jobService: new JobSearchService(true),
      service: new UserIndexSearchService(this.organization_code),
    };
  },
  async created() {
    await this.jobService.fetchJob(this.jobId);
  },
  computed: {
    loading() {
      return this.jobService.loading;
    },
    capacities() {
      return this.job.missions.flatMap(m => m.activities).flatMap(a => a.inducedCapacities).map(c => c.code);
    },
    criteria() {
      return this.job.criteriaValues.map(cv => cv.code);
    },
    masteryLevel() {
      return this.$t('masteryLevels')[this.job.level]?.labelM;
    },
    location() {
      return this.job.location;
    },
    job() {
      return this.jobService.job;
    },
    prefilter() {
      const {criteria, location, capacities, masteryLevel} = this;
      return {criteria, location, capacities, masteryLevel};
    },
  },
};</script>
