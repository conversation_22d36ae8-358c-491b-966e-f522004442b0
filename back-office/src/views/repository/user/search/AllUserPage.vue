<template>
  <v-card>
    <v-card-title>
      <h2>
        <v-icon large right>people</v-icon>
        Sourcing candidat
      </h2>
    </v-card-title>
    <v-card-text>
      <v-dialog v-model="showSendNotificationDialog" v-if="showSendNotificationDialog" max-width="50%" persistent>
        <send-notification :service="service" @cancel="showSendNotificationDialog = false"/>
      </v-dialog>
      <user-list :initial-service="service">
        <template v-slot:otherActions="{hasAnyResult}">
          <v-btn :disabled="!hasAnyResult"
                 color="primary"
                 outlined
                 class="ml-10"
                 small
                 @click="showSendNotificationDialog=true">
            Envoyer une notification
          </v-btn>
        </template>
      </user-list>
    </v-card-text>
  </v-card>
</template>
<script>
import UserList from './List';
import SendNotification from '@/components/notification/SendNotification.vue';
import UserIndexSearchService from '@/views/repository/user/search/UserIndexSearchService';

export default {
  components: {SendNotification, UserList},
  data() {
    return {
      service: new UserIndexSearchService(undefined),
      showSendNotificationDialog: false,
    };
  },
};</script>
