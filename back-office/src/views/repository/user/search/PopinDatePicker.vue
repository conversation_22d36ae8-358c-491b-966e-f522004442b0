<template>
  <v-menu
    ref="menu"
    v-model="displayPicker"
    transition="scale-transition"
    offset-y
    min-width="auto"
    :close-on-content-click="false"
  >
    <template v-slot:activator="{ on, attrs }">
      <v-text-field
        class="pt-4"
        :value="dateMoment ? dateMoment.format('DD/MM/YYYY') : ''"
        :label="label"
        prepend-icon="mdi-calendar"
        readonly
        hide-details
        v-bind="attrs"
        v-on="on"
        dense
        clearable
        @click:clear="date = null"
      />
    </template>
    <v-date-picker
      :max="new Date().toISOString()"
      min="2018-01-01"
      v-model="date"
      no-title
      scrollable
      locale="fr"
      first-day-of-week="1"
    />
  </v-menu>
</template>
<script>
import moment from 'moment';

export default {
  name: 'popin-date-picker',
  props: {
    value: {
      required: false,
      default: null,
    },
    label: {
      required: false,
      default: 'Choisissez une date...',
    },
  },
  created() {
    this.date = this.value;
  },
  data() {
    return {
      date: null,
      displayPicker: false,
    };
  },
  computed: {
    dateMoment() {
      return this.date ? moment(this.date) : null;
    },
  },
  watch: {
    dateMoment() {
      this.displayPicker = false;
      this.$emit('input', this.dateMoment);
    },
    value(newVal) {
      if (!newVal) {
        this.date = null;
      }
    },
  },
};
</script>
Zz
