<template>
  <td style="width: 15%;">
    <v-tooltip top>
      <template v-slot:activator="{ on }">
        <v-btn icon
               small
               target="_blank"
               v-on="on"
               id="detail"
               :to="organizationCode ?
                  {
                    name: 'front_user_detail_for_organization',
                    params: {
                      userId: idValue,
                      organization_code: organizationCode
                    }} : {
                    name: 'front_user_detail',
                    params: {
                      userId: idValue,
                    }
                  }"
               color="primary">
          <v-icon>visibility</v-icon>
        </v-btn>
      </template>
      <span>Informations détaillées</span>
    </v-tooltip>
    <v-tooltip top>
      <template v-slot:activator="{ on }">
        <v-btn icon
               small
               color="primary"
               v-on="on"
               :to="{
                   name: 'user-edit',
                   params: {
                     userId: idValue
                   },
                }"
               target="_blank"
        >
          <v-icon small>fa-pencil</v-icon>
        </v-btn>
      </template>
      <span>Modifier l'utilisateur</span>
    </v-tooltip>
    <v-tooltip top>
      <template v-slot:activator="{ on }">
        <v-menu offset-y>
          <template v-slot:activator="{ on: menuOn, attrs }">
            <v-btn
              icon
              small
              color="primary"
              v-bind="attrs"
              v-on="{ ...on, ...menuOn }"
            >
              <v-icon>mdi-pdf-box</v-icon>
            </v-btn>
          </template>

          <v-list>
            <v-list-item>
              <downloader-btn
                text
                color="primary"
                :title="`jenesuisPASunCV_${item.firstName ?? ''}_${item.lastName ?? ''}`"
                :download="exportUser(idValue, false)"
                extension="pdf"
              >
                <v-icon left>mdi-pdf-box</v-icon>
                Profil #jenesuisPASunCV
              </downloader-btn>
            </v-list-item>

            <v-list-item>
              <downloader-btn
                text
                color="primary"
                :title="`jnspuHandicap_${item.firstName ?? ''}_${item.lastName?? ''}`"
                :download="exportUser(idValue, true)"
                extension="pdf"
              >
                <v-icon left>mdi-pdf-box</v-icon>
                Profil jnspuHandicap
              </downloader-btn>
            </v-list-item>
          </v-list>
        </v-menu>
      </template>
      <span>Exporter en PDF</span>
    </v-tooltip>
  </td>
</template>
<script>
import DownloaderBtn from 'odas-plugins/DownloaderBtn';

export default {
  name: 'user-actions-cell',
  components: {DownloaderBtn},
  props: {
    item: {
      type: Object,
      required: true,
    },
    idKey: {
      default: 'id',
    },
    organizationCode: {
      type: String,
      default: null,
    },
  },
  methods: {
    exportUser(idValue, handicap) {
      return async () => (await this.$api.getUserProfileFOPdf(idValue, false, handicap, { responseType: 'blob' })).data;
    },
  },
  computed: {
    idValue() {
      return this.item[this.idKey];
    },
  },
};
</script>
