import Vue from 'vue';
import SafeService from 'odas-plugins/SafeService';
import {CapacityRelatedQuestionPage, QuestionType} from 'erhgo-api-client';

export default class CapacityRelatedQuestionSearchService extends SafeService {

  private _capacityRelatedQuestions: CapacityRelatedQuestionPage | null = null;

  constructor(private _questionType: QuestionType, private _organizationCode: string | undefined) {
    super();
  }

  async fetchQuestionPages(page: number, size: number) {
    await this.safeCall(async () => {
      this._capacityRelatedQuestions = (await Vue.$api.getCapacityRelatedQuestionPage(this._questionType, page, size, this._organizationCode || undefined)).data;
    });
  }

  get questionPages(): CapacityRelatedQuestionPage | null {
    return this._capacityRelatedQuestions;
  }

  async moveUpQuestionIndex(questionIndex: number) {
    await this.safeCall(async () => {
      const questionList = this._capacityRelatedQuestions?.content || [];
      const previousQuestionForIndex = questionList[questionIndex];

      questionList[questionIndex] = questionList[questionIndex - 1];
      questionList[questionIndex].questionIndex++;
      questionList[questionIndex - 1] = previousQuestionForIndex;
      questionList[questionIndex - 1].questionIndex--;

      // Next line to help VUe to detect array changes
      this._capacityRelatedQuestions!.content = [...questionList];
      const questionsToReorder: { [key: string]: number } = {};
      questionsToReorder[questionList[questionIndex - 1].id] = questionList[questionIndex - 1].questionIndex;
      questionsToReorder[questionList[questionIndex].id] = questionList[questionIndex].questionIndex;
      await Vue.$api.reorderCapacityRelatedQuestions(questionsToReorder);
    });
  }
}
