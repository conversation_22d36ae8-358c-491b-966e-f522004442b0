<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card flat>
          <v-card-title>
            <h1>
              <v-icon large
                      color="primary">mdi-briefcase-plus-outline
              </v-icon>
              Liste des questions
            </h1>
          </v-card-title>
          <v-card-text>
            <v-col>
              <v-data-table :headers="headers"
                            :items="items"
                            :page.sync="pagination.page"
                            :items-per-page.sync="pagination.rowsPerPage"
                            :footer-props="{
                              'items-per-page-options': rowsPerPage,
                              'items-per-page-text': 'Nb lignes par page',
                            }"
                            class="elevation-15"
                            item-key="id"
                            id="landingPageList"
                            no-data-text="Aucun résultat disponible"
                            :server-items-length="totalNumberOfElements">
                <template v-slot:item="props">
                  <tr>
                    <td class="text-left">{{ props.item.questionIndex + 1 }}</td>
                    <td class="text-left">{{ props.item.title }}</td>
                    <td class="text-right">
                      <v-btn icon
                             :id="`editCapacityRelatedQuestion-${props.item.questionIndex}`"
                             :to="{
                               name: targetName,
                               params: { id: props.item.id, organizationCode }
                             }"
                             class="mx-1">
                        <v-icon color="primary">
                          edit
                        </v-icon>
                      </v-btn>
                    </td>
                    <td class="text-right">
                      <v-btn small
                             text
                             :id="`reorderCapacityRelatedQuestion-${props.item.questionIndex}`"
                             @click.stop="moveQuestionUp(props.item.questionIndex)"
                             :disabled="props.item.questionIndex === 0 || capacityRelatedQuestionService.loading">
                        <v-icon>arrow_upward</v-icon>
                      </v-btn>
                    </td>
                  </tr>
                </template>
                <template v-slot:footer.page-text="props">
                  Lignes de {{ props.pageStart }} à {{ props.pageStop }} sur {{ props.itemsLength }}
                </template>
              </v-data-table>
              <v-row class="mx-1 my-3">
                <v-spacer/>
                <v-tooltip color="red darken-1"
                           left>
                  <template v-slot:activator="{ on }">
                    <v-btn fab
                           dark
                           color="red darken-1"
                           v-on="on"
                           class="text-center"
                           id="createCapacityRelatedQuestionButton"
                           :to="{
                             name: targetName,
                             params: { organizationCode }
                           }">
                      <v-icon>mdi-plus</v-icon>
                    </v-btn>
                  </template>
                  <span class="font-weight-bold">{{$t('action.create')}}</span>
                </v-tooltip>
              </v-row>
            </v-col>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import CapacityRelatedQuestionSearchService from './CapacityRelatedQuestionSearchService';
import _ from 'lodash';

export default {
  props: {
    questionType: {
      type: String,
      required: true,
    },
    organizationCode: {
      type: String,
      default: null,
    },
  },
  computed: {
    totalNumberOfElements() {
      return this.capacityRelatedQuestionService.questionPages?.totalNumberOfElements;
    },
    items() {
      return this.capacityRelatedQuestionService.questionPages?.content;
    },
    targetName() {
      return this.organizationCode ? 'edit_formation_question' : 'edit_extra_pro_question';
    },
  },
  async created() {
    this.capacityRelatedQuestionService = new CapacityRelatedQuestionSearchService(this.questionType, this.organizationCode);
    await this.getDataFromApi();
  },
  data() {
    return {
      capacityRelatedQuestionService: CapacityRelatedQuestionSearchService,
      headers: [
        {
          text: 'Index',
          align: 'left',
          sortable: false,
        },
        {
          text: 'Titre',
          align: 'left',
          sortable: false,
        },
        {
          text: this.$t('ref.headers.actions'),
          align: 'right',
          sortable: false,
        },
        {
          text: 'Ordre',
          align: 'right',
          sortable: false,
        },
      ],
      rowsPerPage: [10, 25, 50, 100, 200],
      pagination: {
        page: 1,
        rowsPerPage: 25,
      },
    };
  },
  methods: {
    async getDataFromApi() {
      await this.capacityRelatedQuestionService.fetchQuestionPages(this.pagination.page - 1, this.pagination.rowsPerPage);
    },
    async moveQuestionUp(index) {
      await this.capacityRelatedQuestionService.moveUpQuestionIndex(index);
    },
  },
  watch: {
    pagination: {
      handler: _.debounce(async function () {
        await this.getDataFromApi();
      }, 200),
      deep: true,
    },
  },
};
</script>
