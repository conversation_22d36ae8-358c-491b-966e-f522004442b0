<template>
  <v-row>
    <v-col cols="12">
      <v-card elevation="0" outlined class="pa-4">
        <v-text-field
          :rules='[rules.required, rules.max255]'
          label='Libellé de la réponse'
          counter="255"
          v-model='response.title'
          :append-outer-icon='deletable ? "mdi-trash-can" : ""'
          @click:append-outer="$emit('remove')"
        />
        <capacity-selector
          :value="initialCapacities"
          @input="updateCapacities"
        />
      </v-card>
    </v-col>
  </v-row>
</template>

<script>

import {ResponseForCapacityRelatedQuestion} from 'erhgo-api-client';
import CapacitySelector from '@/components/capacity/CapacitySelector';

export default {
  name: 'Response',

  components: {
    CapacitySelector,
  },

  props: {
    response: {
      type: ResponseForCapacityRelatedQuestion,
      required: true,
    },
    deletable: {
      type: Boolean,
      required: true,
    },
    initialCapacities: {
      default: () => [],
    },
  },
  data() {
    return {
      rules: {
        required: value => !!value || 'Requis',
        min1: value => !value || value > 0 || 'Minimum 1 caractère',
        max255: value => !value || value.length <= 255 || 'Maximum 255 caractères',
      },
      title: '',
      capacities: [],
    };
  },

  methods: {
    updateCapacities(capacities) {
      this.response.capacities = capacities.map(c => c.id);
    },
  },

};
</script>

