<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-form :key="question.id">
            <v-card-title>
              <h1>
                <v-icon large
                        color="primary">mdi-briefcase-plus-outline
                </v-icon>
                Activité extra-professionnelle
              </h1>
            </v-card-title>
            <v-card-text>
              <v-text-field
                :rules='[rules.required, rules.max255]'
                label='Titre'
                counter="255"
                v-model='question.title'
                id="questionTitle"
              />
              <response :response='response'
                        :deletable="question.responses.length > 2"
                        v-for='response in question.responses'
                        :key='response.id'
                        :initialCapacities="question.capacitiesForResponse.get(response.id)"
                        @remove="question.removeResponse(response)"
              />
              <v-alert v-if="question.showAPIError" type="error" outlined>
                Erreur : Veuillez contacter le support.
              </v-alert>
              <v-alert v-if="!question.responsesTitleAreUnique" type="error" outlined>
                Erreur : Il y a plusieurs réponses possédant le même titre
              </v-alert>
              <v-row class="mt-4 mx-1">
                <v-btn
                  @click="question.addResponse()"
                  :disabled="question.containsEmptyResponse"
                  color="secondary"
                  outlined
                >
                  Ajouter une réponse
                </v-btn>
                <v-spacer />
                <v-btn
                  id="submitQuestion"
                  @click="question.saveQuestion()"
                  :loading="question.loading"
                  :disabled="!question.isValid"
                  color="secondary"
                >
                  {{question.success ? 'Question validée' : 'Valider la question'}}
                </v-btn>
              </v-row>
            </v-card-text>
          </v-form>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>

import CapacityRelatedQuestion from './CapacityRelatedQuestion';
import Response from './Response';
import {QuestionType} from 'erhgo-api-client';

export default {
  components: {Response},
  props: {
    id: {
      type: String,
      required: false,
    },
  },
  data() {
    return {
      rules: {
        required: value => !!value || 'Requis',
        min1: value => !value || value > 0 || 'Minimum 1 caractère',
        max255: value => !value || value.length <= 255 || 'Maximum 255 caractères',
      },
      question: null,
    };
  },
  async created() {
    this.question = new CapacityRelatedQuestion(this.questionType);
    if (this.id) {
      await this.question.fetchDetail(this.id);
    }
  },
  computed: {
    questionSuccess() {
      return this.question?.success;
    },
  },
  watch: {
    questionSuccess(newSuccessStatus, oldSuccessStatus) {
      if (!this.id && !oldSuccessStatus && newSuccessStatus) {
        this.question = new CapacityRelatedQuestion(QuestionType.EXTRAPROFESSIONAL);
      }
    },
  },
};

</script>
