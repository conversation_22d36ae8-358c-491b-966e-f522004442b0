import SafeService from 'odas-plugins/SafeService';
import {v4 as uuid} from 'uuid';
import {QuestionType, ResponseForCapacityRelatedQuestion} from 'erhgo-api-client';
import Vue from 'vue';

export default class CapacityRelatedQuestion extends SafeService {

  public id = uuid();
  public title = '';
  public responses: ResponseForCapacityRelatedQuestion[] = [];
  private _capacitiesForResponse = new Map();

  constructor() {
    super();
    this.addResponse();
    this.addResponse();
  }

  addResponse() {
    const response = {
      id: uuid(),
      title: '',
      capacities: [],
    };
    this.responses.push(response);
  }

  removeResponse(response: ResponseForCapacityRelatedQuestion) {
    this.responses = this.responses.filter(r => r.id !== response.id);
  }

  get containsEmptyResponse() {
    return !!this.responses.filter(r => !r.title).length;
  }

  get anyResponseExceedsMaxLength() {
    return this.responses.some(r => r.title.length > 255);
  }

  async saveQuestion() {
    const questionType = QuestionType.EXTRAPROFESSIONAL;
    const {id, title, responses} = this;
    await this.safeCall(async () => Vue.$api.saveCapacityRelatedQuestion({
      id,
      title,
      responses,
      questionType,
    }));
  }

  async fetchDetail(id: string) {
    this.id = id;
    const question = (await Vue.$api.getCapacityRelatedQuestionDetails(this.id)).data;
    this.title = question.title;
    question.responses.forEach(r => (this._capacitiesForResponse.set(r.id, r.capacities)));
    this.responses = question.responses.map(r => ({...r, capacities: r.capacities.map(c => c.id) || []}));
  }

  get capacitiesForResponse() {
    return this._capacitiesForResponse;
  }

  get responsesTitleAreUnique() {
    return this.containsEmptyResponse || (new Set(this.responses.map(r => r.title))).size === this.responses.length;
  }

  get isValid() {
    return !!this.title
      && this.title.length <= 255
      && !this.containsEmptyResponse
      && !this.anyResponseExceedsMaxLength
      && this.responsesTitleAreUnique
      && !this.success;
  }
}
