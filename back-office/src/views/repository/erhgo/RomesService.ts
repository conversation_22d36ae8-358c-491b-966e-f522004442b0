import SafeService from 'odas-plugins/SafeService';
import { RomePage } from 'erhgo-api-client';
import Vue from 'vue';

export default class RomesService extends SafeService {

  private _romePage: RomePage | null = null;

  async fetchRomePage(size: number, page: number, query: string) {
    await this.safeCall(async () => {
      this._romePage = (await Vue.$api.romePage(size, page, query)).data;
    });
  }

  get romePage(): RomePage | null {
    return this._romePage;
  }
}
