import SafeService from 'odas-plugins/SafeService';
import {Behavior, BehaviorCategory, ErhgoOccupationDetail, OccupationQualificationSource} from 'erhgo-api-client';
import Vue from 'vue';

export default class ErhgoOccupationBehaviorService extends SafeService {
  loading = false;

  constructor(private _occupation: ErhgoOccupationDetail) {
    super();
  }

  async updateBehaviorsDescription(description: string) {
    return this.safeCall(async () => {
      await Vue.$api.updateBehaviorsDescription({
        id: this._occupation.id,
        description,
      });
      return true;
    });
  }

  async generateBehaviorsDescription() {
    this.loading = true;
    return this.safeCall(async () => {
      if (this._occupation?.id) {
        const result = (await Vue.$api.generateOccupationBehaviorsDescription(this._occupation.id)).data;
        return result.description;
      }
    }).finally(() => {
      this.loading = false;
    });
  }

  async generateBehaviors() {
    this.loading = true;
    return this.safeCall(async () => {
      if (this._occupation?.id) {
        this._occupation.behaviorsCategories = (await Vue.$api.generateOccupationBehaviors(this._occupation.id)).data;
      }
    }).finally(() => {
      this.loading = false;
    });
  }

  async addBehaviorToOccupation(behavior: Behavior) {
    await this.safeCall(async () => {
      this._occupation.behaviorsCategories = (await Vue.$api.addBehaviorToOccupation({
        referentialEntityId: behavior.id,
        erhgoOccupationId: this._occupation.id,
      })).data;
      this._occupation.behaviors.push(ErhgoOccupationBehaviorService.createOccupationBehavior(behavior));
    });
  }
  //reset local behaviors
  async removeBehaviorFromOccupation(behaviorId: string) {
    await this.safeCall(async () => {
      this._occupation.behaviorsCategories = (await Vue.$api.removeBehaviorFromOccupation({
        referentialEntityId: behaviorId,
        erhgoOccupationId: this._occupation.id,
      })).data;
      this._occupation.behaviors = this._occupation.behaviors.filter(value => value.behavior.id != behaviorId);
    });
  }

  async setOccupationBehaviorCategory(occupationCategoryIndex: number, behaviorCategory: BehaviorCategory) {
    return this.safeCall(async () => {
      return Vue.$api.setOccupationBehaviorCategory({
        id: this._occupation.id,
        occupationCategoryIndex,
        behaviorCategory,
      });
    });
  }

  get behaviors() {
    return this._occupation.behaviors.map(value => (
      {
        ...value.behavior,
        source: value.source,
        skillsLabels: value.skills?.map(skill => skill.title).join(', '),
        modificationDate: value.updatedDate,
        modifiedBy: value.lastModifiedBy,
      }));
  }

  private static createOccupationBehavior(behavior: Behavior) {
    return {
      source: OccupationQualificationSource.MANUAL,
      behavior: behavior,
    };
  }
}
