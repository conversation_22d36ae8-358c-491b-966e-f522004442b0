<template>
  <v-row no-gutters>
    <v-col>
      <!-- FIXME: Can't use 'clearable' feature due to bugous behavior when clearing user defined value same as computed one -->
      <v-select :items="items"
                :menu-props="{ offsetY: true }"
                :value="behaviorCategoryByIndex.category"
                @change="updateBehaviorCategory"
                :prepend-inner-icon="behaviorCategoryByIndex.overloaded ? 'cancel' : null"
                @click:prepend-inner="updateBehaviorCategory()"
                :label="`Modifier la ${['principale', 'deuxième', 'troisième'][categoryIndex]} catégorie ${behaviorCategoryByIndex.category ? ` (actuellement ${behaviorCategoryByIndex.overloaded ? 'définie manuellement' : 'calculée'})` : ''}`">
        <template v-slot:item="props">
          <span>{{ $t(`behaviorCategory.${props.item}`) }}</span>
        </template>
        <template v-slot:selection="props">
          <span>{{ $t(`behaviorCategory.${props.item}`) }}</span>
        </template>
        <template v-slot:append-outer>
          <v-icon color="success" :class="{'invisible': !updateCategorySuccess}">check</v-icon>
        </template>
      </v-select>
    </v-col>
  </v-row>
</template>
<script>
import {BehaviorCategory, ErhgoOccupationBehaviorsCategories} from 'erhgo-api-client';
import ErhgoOccupationBehaviorService from './ErhgoOccupationBehaviorService';

export default {
  name: 'erhgo-occupation-behavior-category',
  props: {
    occupationCategory: ErhgoOccupationBehaviorsCategories,
    categoryIndex: {
      type: Number,
      required: true,
    },
    behaviorService: ErhgoOccupationBehaviorService,
  },
  data() {
    return {
      BehaviorCategory,
      updateCategorySuccess: false,
      sortedItems: Object.values(BehaviorCategory)
        .sort((a, b) => this.$t(`behaviorCategory.${a}`).localeCompare(this.$t(`behaviorCategory.${b}`))),
    };
  },
  methods: {
    async updateBehaviorCategory(newCategory) {
      const categoryUpdated = (await this.behaviorService.setOccupationBehaviorCategory(this.categoryIndex, newCategory)).data;
      if (!this.behaviorService.showAPIError) {
        this.$nextTick(() => this.$emit('update:occupation-category', categoryUpdated));
        this.updateCategorySuccess = true;
        window.setTimeout(() => {
          this.updateCategorySuccess = false;
        }, 3000);
      }
    },
  },
  computed: {
    items() {
      return this.sortedItems
        .filter(c => (this.categoryIndex === 0 || c !== this.occupationCategory.behaviorCategory1)
          && (this.categoryIndex === 1 || c !== this.occupationCategory.behaviorCategory2)
          && (this.categoryIndex === 2 || c !== this.occupationCategory.behaviorCategory3),
        );
    },
    behaviorCategoryByIndex() {
      let category, overloaded;
      switch (this.categoryIndex) {
        case 0:
          category = this.occupationCategory.behaviorCategory1;
          overloaded = this.occupationCategory.isBehaviorCategory1Overloaded;
          break;
        case 1:
          category = this.occupationCategory.behaviorCategory2;
          overloaded = this.occupationCategory.isBehaviorCategory2Overloaded;
          break;
        case 2:
          category = this.occupationCategory.behaviorCategory3;
          overloaded = this.occupationCategory.isBehaviorCategory3Overloaded;
          break;
      }
      return {category, overloaded};
    },
  },
};
</script>
