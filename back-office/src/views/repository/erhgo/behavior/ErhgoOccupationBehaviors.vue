<template>
  <div id="behaviors">
    <v-row no-gutters>
      <v-col cols="12">
        <span class="text--secondary text-caption mb-0">Descriptif des attitudes&nbsp;:</span>
        <v-tooltip bottom>
          <template v-slot:activator="{ on }">
            <v-btn icon
                   v-on="on"
                   small
                   color="primary"
                   :loading="ErhgoOccupationBehaviorService.loading"
                   @click="generateBehaviorsDescription"
                   class="ml-auto">
              <v-icon small
                      color="primary">
                fa-recycle
              </v-icon>
            </v-btn>
          </template>
          <span class="text-center">le descriptif des attitudes sera généré automatiquement par l'IA </span>
        </v-tooltip>
      </v-col>
      <v-col cols="12">
        <v-textarea v-model="behaviorsDescription"
                    label="Descriptif des attitudes"
                    outlined
                    no-resize
                    hide-details
                    class="pa-3"
                    :append-icon="updateBehaviorsDescriptionSuccess?'check':null"
                    rows="3"/>
      </v-col>
      <v-col cols="12">
        <span class="font-weight-bold">Catégories de comportements représentatives du métier&nbsp;:</span>
        <v-tooltip bottom>
          <template v-slot:activator="{ on }">
            <v-btn icon
                   v-on="on"
                   small
                   color="primary"
                   :loading="ErhgoOccupationBehaviorService.loading"
                   @click="generateBehaviorsCategories"
                   class="ml-auto">
              <v-icon small
                      color="primary">
                fa-recycle
              </v-icon>
            </v-btn>
          </template>
          <span class="text-center">les comportements seront générés automatiquement par l'IA </span>
        </v-tooltip>
      </v-col>
      <v-col cols="12" md="4">
        <erhgo-occupation-behavior-category :category-index="0"
                                            :behavior-service="ErhgoOccupationBehaviorService"
                                            :occupation-category.sync="occupation.behaviorsCategories"/>
      </v-col>
      <v-col cols="12" md="4">
        <erhgo-occupation-behavior-category :category-index="1"
                                            :behavior-service="ErhgoOccupationBehaviorService"
                                            :occupation-category.sync="occupation.behaviorsCategories"/>
      </v-col>
      <v-col cols="12" md="4">
        <erhgo-occupation-behavior-category :category-index="2"
                                            :behavior-service="ErhgoOccupationBehaviorService"
                                            :occupation-category.sync="occupation.behaviorsCategories"/>
      </v-col>

    </v-row>
    <errorDialog v-model="ErhgoOccupationBehaviorService.showAPIError"/>
  </div>
</template>

<script>
import ErrorDialog from '@/components/common/ErrorDialog.vue';
import BehaviorSearchService from './BehaviorsService';
import ErhgoOccupationBehaviorService from './ErhgoOccupationBehaviorService';
import ErhgoOccupationBehaviorCategory from './ErhgoOccupationBehaviorCategory';
import _ from 'lodash';

export default {
  name: 'ErhgoOccupationBehaviors',
  components: {ErhgoOccupationBehaviorCategory, ErrorDialog},
  props: {
    occupation: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      ErhgoOccupationBehaviorService: ErhgoOccupationBehaviorService,
      behaviorSearchService: BehaviorSearchService,
      behaviorsDescription: '',
      updateBehaviorsDescriptionSuccess: false,
    };
  },
  watch: {
    behaviorsDescription: _.debounce(async function updateBehaviorsDescription(value) {
      if (value !== null) {
        await this.updateBehaviorsDescription(value);
      }
    }, 700),
  },
  methods: {
    async updateBehaviorsDescription(description) {
      this.updateBehaviorsDescriptionSuccess = false;
      this.updateBehaviorsDescriptionSuccess = (await this.ErhgoOccupationBehaviorService.updateBehaviorsDescription(description)) === true;
      setTimeout(() => {
        this.updateBehaviorsDescriptionSuccess = false;
      }, 5000);
      this.occupation.behaviorsDescription = description;
    },
    async generateBehaviorsDescription() {
      this.behaviorsDescription = await this.ErhgoOccupationBehaviorService.generateBehaviorsDescription(this.occupation);
    },

    async generateBehaviorsCategories() {
      await this.ErhgoOccupationBehaviorService.generateBehaviors(this.occupation);
    },
  },
  created() {
    this.ErhgoOccupationBehaviorService = new ErhgoOccupationBehaviorService(this.occupation);
    this.behaviorSearchService = new BehaviorSearchService();
    this.behaviorsDescription = this.occupation.behaviorsDescription;
  },
};
</script>
