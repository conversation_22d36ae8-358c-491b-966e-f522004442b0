<template>
  <v-dialog v-model="value" v-if="title" persistent shrink width="50%">
    <v-card>
      <v-card-title>
        {{`Retrait ${isUnlinkSkill ? `de l\'aptitude ${title}` : `du métier ESCO ${title}`}`}}
      </v-card-title>
      <v-card-text>
        Vous allez supprimer le lien entre ce métier ERHGO et {{isUnlinkSkill ? 'l’aptitude' : 'le métier ESCO'}} <span class="font-weight-bold">{{title}}</span>.
        Par conséquent&nbsp;:
        <ul class="my-4">
          <li v-if="!isUnlinkSkill">
            L'ensemble des aptitudes du métier ESCO seront supprimées du métier ERHGO.
          </li>
          <li>
            Les activités, comportements et contextes issus de la qualification actuelle {{isUnlinkSkill ? 'de l’aptitude' : 'des aptitudes'}} restent associés
            au métier. Vous pouvez les supprimer manuellement.
          </li>
          <li>
            Ce métier ne sera plus impacté par les qualifications futures {{isUnlinkSkill ? 'de l’aptitude' : 'des aptitudes'}}.
          </li>
        </ul>
        <span class="font-weight-bold">Souhaitez-vous supprimer le lien entre ce métier et {{isUnlinkSkill ? 'cette aptitude' : 'le métier ESCO'}}&nbsp;?</span> <span v-if="isUnlinkSkill">Cette
        action est irréversible.</span>
      </v-card-text>
      <v-divider/>
      <v-card-actions>
        <v-btn
          color="primary"
          :loading="loading"
          @click="unlinkSkill">
          Oui, Supprimer le lien
        </v-btn>
        <v-spacer/>
        <v-btn
          :loading="loading"
          @click="close">
          Non, Fermer
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>

export default {
  name: 'UnlinkModal',
  props: {
    value: Boolean,
    loading: Boolean,
    title: String,
    isUnlinkSkill: Boolean,
  },
  methods: {
    close() {
      this.$emit('close');
    },
    unlinkSkill() {
      if (this.isUnlinkSkill) {
        this.$emit('unlinkSkill');
      } else {
        this.$emit('unlinkEsco');
      }
    },
  },
};
</script>
