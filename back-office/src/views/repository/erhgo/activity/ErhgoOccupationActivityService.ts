import SafeService from 'odas-plugins/SafeService';
import {
  ActivityLabelWithCapacities,
  ErhgoOccupationDetail,
  MandatoryState,
  OccupationQualificationSource,
} from 'erhgo-api-client';
import Vue from 'vue';

export default class ErhgoOccupationActivityService extends SafeService {
  loading = false;
  constructor(private _occupation: ErhgoOccupationDetail) {
    super();
  }

  async updateMandatoryState(activityId: string, selected: boolean) {
    await this.safeCall(async () => {
      const state = selected ? MandatoryState.ESSENTIAL : MandatoryState.OPTIONAL;
      await Vue.$api.setOccupationActivityMandatoryState({
        // Quick fix useless inheritance
        // @ts-ignore
        entityType: 'occupationReferentialEntityEditWithStateCommand',
        referentialEntityId: activityId,
        state,
        erhgoOccupationId: this._occupation.id,
      });
    });
  }

  async generateOccupationActivities() {
    this.loading = true;
    await this.safeCall(async () => {
      const result = (await Vue.$api.generateErghoOccupationActivities(this._occupation.id)).data;
      this._occupation.activities = result;
    }).finally(() => {
      this.loading = false;
    });
  }

  async addActivitiesToOccupation(activities: [ActivityLabelWithCapacities]) {
    await this.safeCall(async () => {
      await Vue.$api.addActivitiesToOccupation({
        referentialEntityIds: activities.map(a => a.id),
        erhgoOccupationId: this._occupation.id,
      });
    });
    const activitiesToUpdate = [...this._occupation.activities];
    activities.forEach(a => activitiesToUpdate.push(this.createOccupationActivity(a, MandatoryState.OPTIONAL)));
    this._occupation.activities = activitiesToUpdate;
  }


  async removeActivitiesFromOccupation(activityIds: [string]) {
    await this.safeCall(async () => {
      await Vue.$api.removeActivitiesFromOccupation({
        referentialEntityIds: activityIds,
        erhgoOccupationId: this._occupation.id,
      });
      this._occupation.activities = this._occupation.activities.filter(value => !activityIds.includes(value.activity.id));
    });
  }

  get activities() {
    return this._occupation.activities.map(value => (
      {
        ...value.activity,
        selected: value.state === MandatoryState.ESSENTIAL,
        source: value.source,
        skillsLabels: value.skills?.map(skill => skill.title).join(', '),
        modificationDate: value.updatedDate,
        modifiedBy: value.lastModifiedBy,
      }));
  }

  async updateActivities(activity: ActivityLabelWithCapacities) {
    const activities = this._occupation.activities.filter(value => value.activity.id !== activity.id);
    if (activities.length === this.activities.length) {
      await this.addActivitiesToOccupation([activity]);
    } else {
      const updatedActivity = this._occupation.activities.filter(value => value.activity.id === activity.id)[0];
      this._occupation.activities = [this.createOccupationActivity(activity, updatedActivity.state), ...activities];
    }
  }

  private createOccupationActivity(activity: ActivityLabelWithCapacities, state: MandatoryState) {
    return {
      state: state,
      source: OccupationQualificationSource.MANUAL,
      activity: activity,
    };
  }
}
