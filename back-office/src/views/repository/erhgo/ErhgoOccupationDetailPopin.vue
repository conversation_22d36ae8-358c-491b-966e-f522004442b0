<template>
  <v-card v-if="occupation">
    <v-card-title>
      <div class="text-center capitalize-first-letter d-flex align-center">
        <v-icon color="primary" class="mr-2">
          work
        </v-icon>
        <span class="capitalize-first-letter">
          {{ occupation.title }}
        </span>
      </div>
    </v-card-title>
    <v-card-text class="pb-0">
      <v-row v-if="occupationService.loading" justify="center" class="mt-3">
        <v-progress-circular indeterminate/>
      </v-row>
      <v-row no-gutters justify="center" class="pt-2" v-else>
        <v-col class="pl-sm-2">
          <div class="ma-0 mt-2 occupation-description-wrapper" v-html="occupation.description"/>
          <div class="my-4 d-flex justify-space-around">
            <downloader-btn
              id="exportOccupationAsPDFButton"
              color="primary"
              :download="exportPDF()"
              :title="pdfTitle"
              extension="pdf"
            >
              Exporter en PDF
            </downloader-btn>
            <v-btn
              id="closeOccupationPopin"
              color="error"
              @click="closePopin"
            >
              Fermer
            </v-btn>
          </div>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
  <v-row justify="center" v-else>
    <v-progress-circular indeterminate/>
  </v-row>
</template>

<script>
import ErhgoOccupationService from './ErhgoOccupationService';
import DownloaderBtn from 'odas-plugins/DownloaderBtn';

export default {
  name: 'ErhgoOccupationDetailsPopin',
  components: {DownloaderBtn},
  props: {
    occupationId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      occupationService: null,
    };
  },
  async created() {
    this.occupationService = new ErhgoOccupationService(this.occupationId);
    await this.fetchErhgoOccupation();
  },
  watch: {
    async occupationId() {
      await this.fetchErhgoOccupation();
    },
  },
  computed: {
    occupation() {
      return this.occupationService.occupationSummary;
    },
    pdfTitle() {
      return `fiche-métier-${this.occupation.title.replace(/[|&;$%@"<>()+,]/g, ' ').replaceAll(' ', '-').substring(0, 30).toLowerCase()}.pdf`;
    },
  },
  methods: {
    closePopin() {
      this.$emit('onClose');
    },
    async fetchErhgoOccupation() {
      await this.occupationService.fetchOccupationSummary();
    },
    exportPDF() {
      return async () => (await this.$api.getErhgoOccupationDetailsPdf(this.occupationId, {responseType: 'blob'})).data;
    },
  },
};
</script>

<style>
.occupation-description-wrapper p {
  margin-bottom: 0 !important;
}
</style>
