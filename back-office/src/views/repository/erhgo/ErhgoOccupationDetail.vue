<template>
  <v-container fluid>
    <v-row v-if="!occupation" justify="center">
      <v-progress-circular indeterminate/>
    </v-row>
    <v-row v-else>
      <v-col cols="12">
        <v-card flat>
          <v-card-title>
            <v-icon class="mr-2"
                    large
                    color="primary">
              mdi-web
            </v-icon>
            <h1>
              Métier erhgo
            </h1>
          </v-card-title>
          <v-card-subtitle>
            <v-row no-gutters>
              <v-col cols="12" class="d-flex align-center">
                <h2 class="my-4 black--text">{{ upperFirstTitleCase }}</h2>
                <v-chip
                  class="ma-1 white--text"
                  color="black"
                  v-if="isToConfirm">
                  À confirmer
                </v-chip>
                <v-chip
                  class="ma-1 black--text"
                  color="warning"
                  v-else-if="occupation.erhgoOccupationState === 'QUALIFIED_V2'">
                  Qualifié V2
                </v-chip>
                <v-chip
                  class="ma-1 black--text"
                  color="warning"
                  v-else-if="occupation.erhgoOccupationState === 'QUALIFIED_V1'">
                  Qualifié V1
                </v-chip>
                <v-chip
                  class="ma-1 white--text"
                  color="interaction"
                  v-else-if="occupation.erhgoOccupationState === 'QUALIFIED_V3'">
                  Qualif. auto
                </v-chip>
                <v-chip
                  class="ma-1"
                  color="primary"
                  v-else-if="occupation.erhgoOccupationState === 'QUALIFIED_V3_CONFIRMED'">
                  Qualif. confirmée
                </v-chip>
                <v-chip class="ma-1" color="error"
                        v-else>
                  À qualifier
                </v-chip>
                <v-chip
                  class="ma-1 black--text"
                  color="warning">
                  {{ occupation.occupationCreationReason ? $t(`occupationCreationReason.${occupation.occupationCreationReason}`) : 'Raison de creation inconnue' }}
                </v-chip>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col>
               <span v-if="occupation.lastModifiedBy && occupation.updatedDate" class="text-caption">
              Modifié par {{
                   occupation.lastModifiedBy ? occupation.lastModifiedBy : 'inconnu'
                 }} le {{ occupation.updatedDate | formatDateTime }}
            </span>
              </v-col>
            </v-row>
          </v-card-subtitle>
          <v-card-text>
            <v-progress-circular v-if="loading"/>
            <div v-if="!loading && !error">
              <classification-detail
                ref="classificationDetail"
                @onRemoveESCO="unlinkEsco"
                :is-qualified="isFinalState"
                :erhgo-occupation-service="occupationService"/>
              <erhgo-occupation-labels :initial-occupation="occupation"
                                       v-if="occupation"/>
              <v-row>
                <v-col cols="12">
                  <v-expansion-panels>
                    <v-expansion-panel>
                      <v-expansion-panel-header>
                        <h2 id="detailTitle" class="d-flex align-center">
                          <v-icon class="mr-2"
                                  color="primary">
                            mdi-settings
                          </v-icon>
                          Détails
                        </h2>
                      </v-expansion-panel-header>
                      <v-expansion-panel-content>
                        <v-row>
                          <v-col cols="12">
                            <p class="text--secondary text-caption mb-0">
                              Description
                              <v-icon v-if="updateDescriptionSuccess"
                                      small
                                      color="success">
                                {{ updateDescriptionSuccess ? 'check' : 'edit' }}
                              </v-icon>
                              <v-tooltip bottom>
                                <template v-slot:activator="{ on }">
                                  <v-btn icon
                                         v-on="on"
                                         small
                                         color="primary"
                                         @click="generateOccupationDescription"
                                         :loading="occupationService.loading"
                                         class="ml-auto">
                                    <v-icon small
                                            color="primary">
                                      fa-recycle
                                    </v-icon>
                                  </v-btn>
                                </template>
                                <span class="text-center">la description sera générée automatiquement par l'IA </span>
                              </v-tooltip>
                            </p>
                            <vue-editor class="mt-1"
                                        id="occupationDescription"
                                        v-model="occupation.description"/>
                          </v-col>
                          <v-col cols="12">
                            <p class="text--secondary text-caption mb-0">
                              Niveau de maîtrise:
                              <v-icon v-if="updateDescriptionSuccess"
                                      small
                                      color="success">
                                {{ updateDescriptionSuccess ? 'check' : 'edit' }}
                              </v-icon>
                              <v-tooltip bottom>
                                <template v-slot:activator="{ on }">
                                  <v-btn icon
                                         v-on="on"
                                         small
                                         color="primary"
                                         @click="generateSepecificationAndMasteryLevel"
                                         :loading="occupationService.loading"
                                         class="ml-auto">
                                    <v-icon small
                                            color="primary">
                                      fa-recycle
                                    </v-icon>
                                  </v-btn>
                                </template>
                                <span class="text-center">le niveau de maîtrise et l'aspect "métier technique" seront générés automatiquement par l'IA</span>
                              </v-tooltip>
                            </p>
                          </v-col>
                          <v-col cols="6">
                            <v-select :items="jobsMasteryLevels"
                                      @change="updateMasteryLevel"
                                      hide-details
                                      v-model="occupation.level"
                                      :menu-props="{ offsetY: true }"
                                      label="Niveau de maîtrise du métier eRHgo">
                              <template v-slot:item="props">
                                <span>{{ levelDescription(props.item) }}</span>
                              </template>
                              <template v-slot:selection="props">
                                <span>{{ levelDescription(props.item) }}</span>
                              </template>
                              <template v-slot:prepend>
                                <v-icon :color="updateMasterySuccess ? 'success' : 'primary'">{{
                                    updateMasterySuccess ?
                                      'check' : 'edit'
                                  }}
                                </v-icon>
                              </template>
                            </v-select>
                          </v-col>
                          <v-col cols="6">
                            <v-text-field
                              :value="`${computeSkillContextScore} (${computeContextMasteryLevel(computeSkillContextScore)})`"
                              label="Score (et niveau de maîtrise correspondant) relatif aux contextes issus des aptitudes"
                              hide-details
                              readonly/>
                          </v-col>
                          <v-col cols="6">
                            <v-text-field
                              :value="`${computeErhgoContextScore} (${computeContextMasteryLevel(computeErhgoContextScore)})`"
                              label="Score (et niveau de maîtrise correspondant) relatif aux contextes issus de la qualification du métier"
                              hide-details
                              readonly/>
                          </v-col>
                          <v-col cols="6">
                            <v-text-field
                              :value="`${computeTotalContextScore} (${computeContextMasteryLevel(computeTotalContextScore)})`"
                              label="Score total (et niveau de maîtrise correspondant) relatif à tous les contextes du métier"
                              hide-details
                              readonly/>
                          </v-col>
                        </v-row>
                        <v-row v-if="!hasAnySkillToQualify">
                          <ul class="mt-2">
                            <li>Activités
                              <ul>
                                <li v-for="activity in activities" :key="activity.id">{{ activity.title }}</li>
                              </ul>
                            </li>
                            <li>Contextes
                              <ul>
                                <li v-for="context in contexts" :key="context.id">{{ context.title }}</li>
                              </ul>
                            </li>
                            <li id="behaviorList">Comportements
                              <ul>
                                <li v-for="behavior in behaviors" :key="behavior.id">{{ behavior.title }}</li>
                              </ul>
                            </li>
                          </ul>
                        </v-row>
                      </v-expansion-panel-content>
                    </v-expansion-panel>
                  </v-expansion-panels>
                </v-col>
              </v-row>

              <v-row>
                <v-col cols="12">
                  <v-expansion-panels>
                    <v-expansion-panel>
                      <v-expansion-panel-header>
                        <h2 id="skillTitle" class="d-flex align-center">
                          <v-icon class="mr-2"
                                  color="primary">
                            list
                          </v-icon>
                          {{
                            `Aptitudes (${occupation.skills ? occupation.skills.filter(skill =>
                              isSkillToQualify(skill)).length : '-'} à qualifier)`
                          }}
                        </h2>
                      </v-expansion-panel-header>
                      <v-expansion-panel-content>
                        <v-list id="skills" three-line v-if="occupation.skills.length > 0">
                          <template v-for="(skill, index) in occupation.skills">
                            <v-list-item :key="skill.uri">
                              <v-list-item-content>
                                <v-list-item-title class="d-flex align-center">
                                  <v-icon class="mr-2" color="primary">arrow_forward</v-icon>
                                  <span class="font-weight-bold">{{ skill.title }}</span>
                                </v-list-item-title>
                                <v-list-item-subtitle>
                                  <div v-if="isSkillToQualify(skill)">
                                    <v-chip class="ma-1" color="info">A qualifier</v-chip>
                                    <v-chip class="ma-1" color="warning"
                                            v-if="!skill.activities.length && !skill.noActivity">
                                      Activité à qualifier
                                    </v-chip>
                                  </div>
                                  <div v-else>
                                    Activité&nbsp;: {{ skill.activities.length }} |
                                    Contextes&nbsp;: {{ skill.contexts.length }} |
                                    Comportements&nbsp;: {{ skill.behaviors.length }}
                                  </div>
                                </v-list-item-subtitle>
                                <v-list-item-subtitle
                                  v-if="!isSkillToQualify(skill) && skill.lastModifiedBy && skill.updatedDate"
                                  class="text-caption">
                                  Modifié par {{ skill.lastModifiedBy ? skill.lastModifiedBy : 'inconnu' }} le
                                  {{ skill.updatedDate | formatDateTime }}
                                </v-list-item-subtitle>
                              </v-list-item-content>
                              <v-list-item-action v-if="!isFinalState">
                                <v-btn text
                                       icon
                                       color="primary"
                                       @click="qualifySkill(skill)"
                                >
                                  <v-icon>edit</v-icon>
                                </v-btn>
                              </v-list-item-action>
                              <v-list-item-action v-if="!isFinalState">
                                <v-btn text
                                       dark
                                       icon
                                       color="error"
                                       @click="unlinkSkill(skill)"
                                >
                                  <v-icon>delete</v-icon>
                                </v-btn>
                              </v-list-item-action>
                            </v-list-item>
                            <v-divider
                              v-if="index + 1 < occupation.skills.length"
                              :key="`divider-${skill.uri}`"
                              :inset="false"
                            />
                          </template>
                        </v-list>
                        <p v-else>Ce métier ne dispose d'aucune aptitude</p>
                      </v-expansion-panel-content>
                    </v-expansion-panel>
                  </v-expansion-panels>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12">
                  <v-expansion-panels>
                    <v-expansion-panel>
                      <v-expansion-panel-header>
                        <h2 id="activitiesTitle" class="d-flex align-center">
                          <v-icon class="mr-2"
                                  color="primary">
                            stars
                          </v-icon>
                          Activités
                        </h2>
                      </v-expansion-panel-header>
                      <v-expansion-panel-content>
                        <erhgo-occupation-activities :key="componentKey" :occupation="occupation"/>
                      </v-expansion-panel-content>
                    </v-expansion-panel>
                  </v-expansion-panels>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12">
                  <v-expansion-panels>
                    <v-expansion-panel>
                      <v-expansion-panel-header>
                        <h2 id="contextsTitle" class="d-flex align-center">
                          <v-icon class="mr-2"
                                  color="primary">
                            class
                          </v-icon>
                          Contextes
                        </h2>
                      </v-expansion-panel-header>
                      <v-expansion-panel-content>
                        <erhgo-occupation-contexts :key="componentKey" :occupation="occupation"/>
                      </v-expansion-panel-content>
                    </v-expansion-panel>
                  </v-expansion-panels>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12">
                  <v-expansion-panels>
                    <v-expansion-panel>
                      <v-expansion-panel-header>
                        <h2 id="behaviorsTitle" class="d-flex align-center">
                          <v-icon class="mr-2"
                                  color="primary">
                            emoji_people
                          </v-icon>
                          Comportements
                        </h2>
                      </v-expansion-panel-header>
                      <v-expansion-panel-content>
                        <erhgo-occupation-behaviors :key="componentKey" :occupation="occupation"/>
                      </v-expansion-panel-content>
                    </v-expansion-panel>
                  </v-expansion-panels>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12">
                  <v-expansion-panels>
                    <v-expansion-panel>
                      <v-expansion-panel-header>
                        <h2 id="criteriaTitle" class="d-flex align-center">
                          <v-icon class="mr-2"
                                  color="primary">
                            mdi-checkbox-marked-outline
                          </v-icon>
                          Critères
                        </h2>
                      </v-expansion-panel-header>
                      <v-expansion-panel-content>
                        <erhgo-occupation-criteria :key="componentKey" :occupation="occupation"/>
                      </v-expansion-panel-content>
                    </v-expansion-panel>
                  </v-expansion-panels>
                </v-col>
              </v-row>

              <v-btn
                  color="primary"
                  v-if="!isFinalState"
                  class="my-2"
                  @click="forceQualificationDialog = true"
              >
                <v-icon class="mr-2">done</v-icon>
                Confirmer la qualification
              </v-btn>

              <v-tooltip top v-if="!isToConfirm">
                <template v-slot:activator="{ on }">
                  <v-btn
                      v-on="on"
                      color="error"
                      class="my-2"
                      :class="isQualifiedV1 && 'mx-2'"
                      @click="unqualifyOccupationState"
                  >
                    <v-icon class="mr-2">cancel</v-icon>
                    Masquer ce métier
                  </v-btn>
                </template>
                Le métier sera masqué des résultats de recherche et aura le statut "à confirmer" "
              </v-tooltip>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
      <unlink-modal :value="shownUnlinkModal"
                    :loading="occupationService.loading"
                    @close="onUnlinkModalClose"
                    @unlinkSkill="confirmUnlinkSkill"
                    @unlinkEsco="confirmUnlinkEsco"
                    :title="getUnlinkTitle"
                    :is-unlink-skill="!!skillToUnlink"/>
      <v-dialog
          v-model="forceQualificationDialog"
          persistent
          max-width="590"
      >
        <force-qualification-modal
            @forceQualificationClose="forceQualificationDialog = false"
            @forceQualificationValidate="qualifyOccupationState"
            :occupation-id="id"
            v-if="forceQualificationDialog"
        />
      </v-dialog>
      <v-snackbar
        :timeout="5000"
        :value="showError"
        color="red accent-2">
        <v-row>
          <v-icon class="mx-2">info</v-icon>
          <h3>Une erreur est survenue. Veuillez rafraichir la page.</h3>
        </v-row>
      </v-snackbar>
    </v-row>
  </v-container>
</template>

<script>
import ClassificationDetail from './ClassificationDetail';
import ErhgoOccupationBehaviors from './behavior/ErhgoOccupationBehaviors';
import ErhgoOccupationContexts from './context/ErhgoOccupationContexts';
import ErhgoOccupationActivities from './activity/ErhgoOccupationActivities';
import ErhgoOccupationCriteria from './criteria/ErhgoOccupationCriteria';
import ErhgoOccupationLabels from './labels/ErhgoOccupationLabels';
import EscoUtils from '@/components/mixins/EscoUtils';
import ErhgoOccupationService from './ErhgoOccupationService';
import { ErhgoOccupationState } from 'erhgo-api-client';
import ProcessReview from '../../../components/mixins/ProcessReview';
import UnlinkModal from './UnlinkModal';
import _ from 'lodash';
import MasteryLevelUtils from '../../../components/mixins/MasteryLevelUtils';
import Vue from 'vue';
import ForceQualificationModal from '@/views/repository/erhgo/ForceQualificationModal';

export default {
  components: {
    ForceQualificationModal,
    ClassificationDetail,
    ErhgoOccupationActivities,
    ErhgoOccupationContexts,
    ErhgoOccupationBehaviors,
    ErhgoOccupationCriteria,
    ErhgoOccupationLabels,
    UnlinkModal,
  },
  mixins: [EscoUtils, ProcessReview, MasteryLevelUtils],
  data() {
    return {
      initialDescription: null,
      error: null,
      occupationService: null,
      loading: false,
      ErhgoOccupationState,
      skillToUnlink: null,
      escoToUnlink: null,
      shownUnlinkModal: false,
      showError: false,
      forceQualificationDialog: false,
    };
  },
  computed: {
    componentKey() {
      return this.occupationService.componentKey;
    },
    getUnlinkTitle() {
      return this.skillToUnlink?.title || this.escoToUnlink?.title;
    },
    description() {
      return this.occupation?.description;
    },
    occupation() {
      return this.occupationService?.occupation;
    },
    updateDescriptionSuccess() {
      return this.occupationService.updateDescriptionSuccess;
    },
    updateMasterySuccess() {
      return this.occupationService.updateMasterySuccess;
    },
    isQualifiedV1() {
      return this.occupation?.erhgoOccupationState === ErhgoOccupationState.QUALIFIED_V1;
    },
    isToConfirm() {
      return this.occupation?.erhgoOccupationState === ErhgoOccupationState.TO_CONFIRM;
    },
    isQualifiedV2() {
      return this.occupation?.erhgoOccupationState === ErhgoOccupationState.QUALIFIED_V2;
    },
    isQualifiedV3() {
      return this.occupation?.erhgoOccupationState === ErhgoOccupationState.QUALIFIED_V3;
    },
    isFinalState() {
      return this.occupation?.erhgoOccupationState === ErhgoOccupationState.QUALIFIED_V3_CONFIRMED;
    },
    id() {
      return this.$route.params.id;
    },
    hasAnySkillToQualify() {
      return this.isOccupationToQualify(this.occupation);
    },
    activities() {
      return this.occupation.skills.reduce((previousList, skill) => {
        skill.activities.forEach((currentActivity) => {
          if (!previousList.some(activity => activity.id === currentActivity.id)) {
            previousList.push(currentActivity);
          }
        });
        return previousList;
      }, []);
    },
    contexts() {
      return this.occupation.skills.reduce((previousList, skill) => {
        skill.contexts.forEach((currentContext) => {
          if (!previousList.some(context => context.id === currentContext.id)) {
            previousList.push(currentContext);
          }
        });
        return previousList;
      }, []);
    },
    behaviors() {
      return this.occupation.skills.reduce((previousList, skill) => {
        skill.behaviors.forEach((currentBehavior) => {
          if (!previousList.some(behavior => behavior.id === currentBehavior.id)) {
            previousList.push(currentBehavior);
          }
        });
        return previousList;
      }, []);
    },
    skillContexts() {
      return this.occupation.skills.flatMap(skill => skill.contexts);
    },
    erhgoContexts() {
      return this.occupation.contexts.map(occupationContext => occupationContext.context);
    },
    computeSkillContextScore() {
      return this.getScore(this.skillContexts);
    },
    computeErhgoContextScore() {
      return this.getScore(this.erhgoContexts);
    },
    computeTotalContextScore() {
      return this.getScore([...this.erhgoContexts, ...this.skillContexts]);
    },
    upperFirstTitleCase() {
      return _.upperFirst(this.occupation.title);
    },
  },
  watch: {
    description(newValue, oldValue) {
      if (newValue && oldValue !== newValue) {
        this.updateDescription();
      }
    },
  },
  async mounted() {
    this.occupationService = new ErhgoOccupationService(this.id);
    await this.getOccupation();
    this.initialDescription = this.occupation.description;
    if (Vue.$eventBus) {
      Vue.$eventBus.$on('error', showError => this.showError = showError);
    }
  },
  beforeDestroy() {
    Vue.$eventBus.$off('error');
  },
  methods: {
    computeContextMasteryLevel(score) {
      const computedScore = this.computeMasteryLevel(this.getLevelFromScore(score));
      const masteredLabels = Object.values(this.$t('masteryLevels'));
      return computedScore + ` (${masteredLabels[computedScore - 1].labelM})`;
    },
    updateDescription() {
      // nextTick is required to allow vueJS to update HTML content before computing RAW content length
      this.$nextTick(() => {
        this.debouncedUpdate();
      });
    },
    debouncedUpdate: _.debounce(async function update() {
      if (this.initialDescription !== this.description) {
        return this.occupationService.updateDescription();
      }
    }, 300),
    async updateMasteryLevel() {
      await this.occupationService.updateMasteryLevel();
    },
    async generateSepecificationAndMasteryLevel() {
      await this.occupationService.generateSepecificationAndMasteryLevel();
    },
    qualifySkill(skill) {
      const route = {
        name: 'skill_detail',
        params: {uri: skill.uri},
        query: {occupationId: !this.occupationService.isOccupationQualified ? this.occupation.id : undefined},
      };
      const routeData = this.$router.resolve(route);
      window.open(routeData.href, '_blank');
    },
    onUnlinkModalClose() {
      this.shownUnlinkModal = false;
      this.escoToUnlink = null;
      this.skillToUnlink = null;
    },
    unlinkEsco(esco) {
      this.escoToUnlink = esco;
      this.shownUnlinkModal = true;
    },
    unlinkSkill(skill) {
      this.skillToUnlink = skill;
      this.shownUnlinkModal = true;
    },
    async confirmUnlinkEsco() {
      await this.$refs.classificationDetail.confirmRemoveESCOOcupation(this.escoToUnlink);
      this.shownUnlinkModal = false;
      this.escoToUnlink = null;
    },
    async confirmUnlinkSkill() {
      await this.occupationService.deleteLinkSkillToErhgoOccupation(this.skillToUnlink.uri);
      this.shownUnlinkModal = false;
      this.skillToUnlink = null;
    },
    async getOccupation() {
      try {
        this.loading = true;
        this.error = null;
        await this.occupationService.fetchOccupation();
      } catch (e) {
        this.error = `Aucune occupation trouvée pour l'id ${this.id}`;
      } finally {
        this.loading = false;
      }
    },
    async qualifyOccupationState() {
      const state = (await this.occupationService.qualifyOccupationState()).data;
      if (!this.occupationService.showAPIError) {
        this.occupation.erhgoOccupationState = state;
      }
    },
    async unqualifyOccupationState() {
      const state = (await this.occupationService.unqualifyOccupationState());
      if (!this.occupationService.showAPIError) {
        this.occupation.erhgoOccupationState = state;
      }
    },
    async generateOccupationDescription() {
      await this.occupationService.generateDescription();
    },
  },
};
</script>

<style>
#occupationDescription .ql-editor {
  min-height: 100px !important;
}
</style>
