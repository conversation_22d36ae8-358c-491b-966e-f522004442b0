import SafeService from 'odas-plugins/SafeService';
import {WorkEnvironment} from 'erhgo-api-client';
import Vue from 'vue';

export default class WorkEnvironmentService extends SafeService {
  private _workEnvironments: WorkEnvironment[] = [];

  get workEnvironments() {
    return this._workEnvironments;
  }

  async fetchEnvironments() {
    await this.safeCall(async () => {
      this._workEnvironments = (await Vue.$api.listWorkEnvironments()).data;
    });
    return this;
  }
}
