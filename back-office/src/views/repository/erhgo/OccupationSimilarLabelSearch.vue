<template>
  <v-card>
    <v-card-title class="primary d-flex justify-space-between align-center">
      <span class="text-h5 white--text">
        <v-icon large left class="white--text">mdi-magnify</v-icon>
        Métiers Similaires Suggérés
      </span>
    </v-card-title>
    <v-card-text>
      <v-row no-gutters>
        <v-col cols="12" v-if="similarOccupations.length">
          {{ similarOccupations.length }} métiers présentent un libellé proche de {{ searchTitle }} (recherche Algolia)&nbsp;:
          <v-list dense>
            <v-list-item
              v-for="occupation in similarOccupations"
              :key="occupation.id"
              class="my-2">
              <v-list-item-content class="grow">
                <v-list-item-title @click="openOccupation(occupation.code)" class="text-h6 clickable">
                  {{ occupation.title }}
                </v-list-item-title>
              </v-list-item-content>
              <v-btn icon @click="openOccupation(occupation.code)">
                <v-icon color="orange">mdi-pencil</v-icon>
              </v-btn>
            </v-list-item>
          </v-list>
          <v-divider class="my-4"/>
        </v-col>
        <v-col cols="12" class="mt-4" v-else>
          Aucune occupation présentant un libellé proche trouvée (recherche Algolia)
        </v-col>
      </v-row>
      <template v-if="bestMatchingOccupation && bestMatchingOccupation.id">
        <v-row no-gutters>
          <v-col cols="12">
            <v-alert type="info" outlined icon="mdi-lightbulb-on-outline">
              OpenAI a détecté un métier similaire à
              "<strong class="px-1 rounded primary white--text">{{ searchTitle }}</strong>".
              Pourquoi ne pas enrichir notre univers en ajoutant votre précieuse idée comme
              <span class="font-weight-bold primary--text">libellé alternatif</span> à ce métier existant &nbsp;?
              Cela pourrait illuminer notre base de données &nbsp;!
            </v-alert>
          </v-col>
        </v-row>
        <v-divider class="my-4"/>
        <v-list dense>
          <v-list-item dense
            class="my-2">
            <v-list-item-content >
              <v-list-item-title @click="openOccupation(bestMatchingOccupation.id)" class="text-h6 clickable">
                {{ bestMatchingOccupation.title }}
              </v-list-item-title>
            </v-list-item-content>
            <v-btn icon @click="openOccupation(bestMatchingOccupation.id)">
              <v-icon color="orange">mdi-pencil</v-icon>
            </v-btn>
          </v-list-item>
        </v-list>
      </template>
      <template v-else-if="similarOccupations.length">
        <div class="text-body-1">
          OpenAI considère qu'il faut créer un nouveau métier.
        </div>
      </template>
    </v-card-text>
  </v-card>
</template>

<script>

export default {
  components: {},
  props: {
    similarOccupations: {default: () => []},
    bestMatchingOccupation: {type: Object, required: false},
    searchTitle: {type: String, required: true},
  },
  methods: {
    async openOccupation(id) {
      const route = this.$router.resolve({name: 'erhgo_occupation_detail', params: {id: id}});
      window.open(route.href, '_blank');
    },
  },
};
</script>

<style lang="scss" scoped>
.clickable {
  cursor: pointer;
}
</style>
