<template>
  <v-card class="py-2" v-if="occupation">
    <v-card-title class="text-h5" v-if="!isCompletelyQualified">
      <h3>
        <v-icon class="mx-2">
          warning
        </v-icon>
        Attention
      </h3>
    </v-card-title>
    <v-card-title class="text-h5" v-else>
      <h3>
        <v-icon class="mx-2" large>
          check
        </v-icon>
        Métier complet !
      </h3>
    </v-card-title>
    <ul class="no-bullet my-3">
      <li :class="occupation.description ? 'success--text':'error--text' ">
        <v-icon :color="occupation.description?'success':'error'" small>{{
            occupation.description ? 'check' : 'cancel'
          }}
        </v-icon>
        Description métier&nbsp;: {{ occupation.description ? 'Renseignée' : 'À remplir' }}
      </li>
      <li :class="isClassificationsValid ? 'success--text':'error--text' ">
        <v-icon :color="isClassificationsValid ?'success':'error'" small>{{
            isClassificationsValid ? 'check' : 'cancel'
          }}
        </v-icon>
        Catégories "ce que je veux faire"&nbsp;: {{ occupation.erhgoClassifications.length }}/4
      </li>
      <li :class="!nbDuplicatedLabels ? 'success--text': 'error--text' ">
        <v-icon :color="!nbDuplicatedLabels ?'success':'error'" small>{{
            !nbDuplicatedLabels ? 'check' : 'cancel'
          }}
        </v-icon>
        Libellés avec doublons (incluant le titre principal)&nbsp;:
        {{ !nbDuplicatedLabels ? 'Aucun' : `${nbDuplicatedLabels}/${allLabels.length}` }}
      </li>
      <li :class="isActivitiesValid ? 'success--text':'error--text' ">
        <v-icon :color="isActivitiesValid?'success':'error'" small>{{
            isActivitiesValid ? 'check' : 'cancel'
          }}
        </v-icon>
        Nombre d'activités&nbsp;: {{ occupation.activities.length }} sur 10 à 16
      </li>
      <li :class="isEssentialActivitiesValid ? 'success--text':'error--text' ">
        <v-icon :color="isEssentialActivitiesValid?'success':'error'" small>{{
            isEssentialActivitiesValid ? 'check' : 'cancel'
          }}
        </v-icon>
        Dont essentielles&nbsp;: {{ nbEssentialActivities }}/3
      </li>
      <li :class="occupation.behaviorsDescription ? 'success--text':'error--text' ">
        <v-icon :color="occupation.behaviorsDescription?'success':'error'" small>{{
            occupation.behaviorsDescription ? 'check' : 'cancel'
          }}
        </v-icon>
        Description comportement&nbsp;: {{ occupation.behaviorsDescription ? 'Renseignée' : 'À remplir' }}
      </li>
      <li :class="isBehaviorsValid  ? 'success--text':'error--text' ">
        <v-icon :color="isBehaviorsValid ?'success':'error'" small>{{
            isBehaviorsValid ? 'check' : 'cancel'
          }}
        </v-icon>
        Comportements&nbsp;: {{ allBehaviors.length }}/3
      </li>
      <li :class="isAllSkillsQualified ? 'success--text':'error--text' ">
        <v-icon :color="isAllSkillsQualified?'success':'error'" small>{{
            isAllSkillsQualified ? 'check' : 'cancel'
          }}
        </v-icon>
        Aptitudes&nbsp;: {{
          isAllSkillsQualified ? 'Toutes les aptitudes sont qualifiées' : 'Au moins une aptitude non qualifiée'
        }}
      </li>
    </ul>
    <v-card-text>
      <h3>Souhaitez-vous marquer ce métier comme qualifié&nbsp;?</h3>
    </v-card-text>
    <v-card-actions>
      <v-spacer/>
      <v-btn
        color="error"
        outlined
        text
        @click="close"
      >
        Annuler
      </v-btn>
      <v-btn
        color="primary"
        text
        @click="validate"
      >
        Valider
      </v-btn>
    </v-card-actions>
  </v-card>
  <v-progress-circular v-else indeterminate/>
</template>
<script>
import EscoUtils from '@/components/mixins/EscoUtils';
import { MandatoryState } from 'erhgo-api-client';
import LabelsService from '@/views/repository/erhgo/labels/LabelsService';
import ErhgoOccupationService from '@/views/repository/erhgo/ErhgoOccupationService';

export default {
  name: 'ForceQualificationModal',
  props: {
    occupationId: {type: String, required: true},
  },
  data() {
    return {
      isAllSkillsQualified: false,
      nbDuplicatedLabels: 0,
      occupation: null,
    };
  },
  mixins: [EscoUtils],
  async created() {
    this.occupation = (await new ErhgoOccupationService(this.occupationId).fetchOccupation()).occupation;
    const labelService = new LabelsService(this.occupation);
    this.nbDuplicatedLabels = (await Promise.all(this.allLabels.map(async l => await labelService.fetchDuplicatedOccupationsForLabel(l)))).filter(o => o.length > 0).length;
    this.isAllSkillsQualified = !this.isOccupationToQualify(this.occupation);
  },
  computed: {
    allLabels() {
      return [this.occupation.title, ...this.occupation.alternativeLabels];
    },
    isCompletelyQualified() {
      return this.isAllSkillsQualified &&
          this.occupation.description &&
          this.isActivitiesValid &&
          this.isBehaviorsValid &&
          this.occupation.behaviorsDescription &&
        this.isEssentialActivitiesValid;
    },
    nbEssentialActivities() {
      return this.occupation.activities.reduce((acc, activity) => {
        let locAcc = acc;
        if (activity.state === MandatoryState.ESSENTIAL) {
          locAcc++;
        }
        return locAcc;
      }, 0);
    },
    isActivitiesValid() {
      return this.occupation.activities.length >= 10 &&
        this.occupation.activities.length <= 16;
    },
    isEssentialActivitiesValid() {
      return this.nbEssentialActivities === 3;
    },
    allBehaviors() {
      return [this.occupation.behaviorsCategories.behaviorCategory1, this.occupation.behaviorsCategories.behaviorCategory2, this.occupation.behaviorsCategories.behaviorCategory3].filter(v => !!v);
    },
    isBehaviorsValid() {
      return this.allBehaviors.length === 3;
    },
    isClassificationsValid() {
      return this.occupation.erhgoClassifications.length === 4;
    },
  },
  methods: {
    close() {
      this.$emit('forceQualificationClose');
    },
    validate() {
      this.$emit('forceQualificationValidate');
      this.close();
    },
  },
};
</script>
