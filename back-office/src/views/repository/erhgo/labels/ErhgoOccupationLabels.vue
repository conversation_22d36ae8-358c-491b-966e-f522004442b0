<template>
  <v-row>
    <v-col cols="12">
      <v-expansion-panels>
        <v-expansion-panel>
          <v-expansion-panel-header>
            <h2 id="labelsTitle" class="d-flex align-center">
              <v-icon class="mr-2"
                      color="primary">
                title
              </v-icon>
              Libellés
            </h2>
          </v-expansion-panel-header>
          <v-expansion-panel-content>
            <v-alert
              type="error"
              outlined
              :value="error"
            >
              Impossible de modifier les libellés&nbsp;: veuillez rafraichir la page et réessayer.
            </v-alert>
            <div v-if="service">
              <v-row>
                <v-col cols="7">
                  <v-text-field id="occupationTitle"
                                v-model="service.title"
                                @blur="() => refreshOccupationsDuplicatingLabel(service.title)"
                                :label="titleLabel"
                                :rules="[v => ((!!v && !!v.trim()) || $t('form.global.required')), v => !isLabelAlreadyUsedInLabels(v) || 'Doit être différent des titres alternatifs']"
                                :loading="service.loading"
                  />
                </v-col>
                <v-col cols="5">
                  <div v-if="!otherOccupationUsingLabels[service.title]">
                    <v-progress-circular indeterminate size="20"/>
                  </div>
                  <div
                    v-else-if="otherOccupationUsingLabels[service.title].length">
                    <template class="d-inline my-2">
                      <v-icon color="warning">mdi-alert</v-icon>
                      <span class="mx-2">{{
                          `${otherOccupationUsingLabels[service.title].length} métier(s) présente(nt) le même libellé`
                        }}
                    </span>
                    </template>
                    <v-list dense>
                      <v-list-item
                        v-for="occ in otherOccupationUsingLabels[service.title]" :key="occ.id"
                        target="_blank"
                        :to="{name: 'erhgo_occupation_detail', params: {id: occ.id}}"
                        class="pa-0"
                      >
                        <v-list-item-avatar class="my-0">
                          <v-icon class="my-0" dense>launch</v-icon>
                        </v-list-item-avatar>
                        <v-list-item-content class="pa-0">
                          <v-list-item-title>{{ occ.title }}</v-list-item-title>
                        </v-list-item-content>
                      </v-list-item>
                    </v-list>
                  </div>
                  <div v-else>
                    <v-icon color="green">mdi-check</v-icon>
                    <span class="mx-2">Pas de doublon</span>
                  </div>
                </v-col>
              </v-row>
              <v-row v-for="alternativeLabel in service.alternative" v-bind:key="alternativeLabel.id">
                <v-col cols="7">
                  <v-text-field prepend-icon="delete_forever"
                                v-bind:key="alternativeLabel.id"
                                @click:prepend="service.removeAlternativeLabel(alternativeLabel)"
                                append-icon="star"
                                @click:append="service.setAsDefault(alternativeLabel)"
                                @blur="() => refreshOccupationsDuplicatingLabel(alternativeLabel.title)"
                                v-model="alternativeLabel.title"
                                hint="Saisissez un libellé alternatif"
                                :rules="[v => ((!!v && !!v.trim()) || $t('form.global.required')), v => !isLabelAlreadyUsedInTitle(v) || 'Doit être différent du titre principal']"
                                :loading="service.loading"
                  />
                </v-col>
                <v-col cols="5">
                  <div v-if="!otherOccupationUsingLabels[alternativeLabel.title]">
                    <v-progress-circular indeterminate size="20"/>
                  </div>
                  <div
                    v-else-if="otherOccupationUsingLabels[alternativeLabel.title].length">
                    <template class="d-inline my-2">
                      <v-icon color="warning">mdi-alert</v-icon>
                      <span class="mx-2">{{
                          `${otherOccupationUsingLabels[alternativeLabel.title].length} métier(s) présente(nt) le même libellé`
                        }}
                    </span>
                    </template>
                    <v-list dense>
                      <v-list-item
                        v-for="occ in otherOccupationUsingLabels[alternativeLabel.title]" :key="occ.id"
                        target="_blank"
                        :to="{name: 'erhgo_occupation_detail', params: {id: occ.id}}"
                        link
                        class="pa-0"
                      >
                        <v-list-item-avatar class="my-0">
                          <v-icon class="my-0" dense>launch</v-icon>
                        </v-list-item-avatar>
                        <v-list-item-content class="pa-0">
                          <v-list-item-title>{{ occ.title }}</v-list-item-title>
                        </v-list-item-content>
                      </v-list-item>
                    </v-list>
                  </div>
                  <div v-else>
                    <v-icon color="green">mdi-check</v-icon>
                    <span class="mx-2">Pas de doublon</span>
                  </div>
                </v-col>

              </v-row>
              <v-row>
                <v-col>
                  <v-btn
                    color="primary"
                    class="mt-2"
                    title="Ajouter un libellé alternatif"
                    @click="service.addAlternativeLabel()"
                    id="alternativeLabel_addButton"
                    :loading="service.loading"
                  >
                    <v-icon>add</v-icon>
                    Ajouter un libellé alternatif
                  </v-btn>
                </v-col>
                <v-spacer/>
                <v-col>
                  <v-btn
                    color="primary"
                    class="mt-2"
                    title="Rafraîchir"
                    @click="refreshAllLabels()"
                    id="alternativeLabel_reloadButton"
                    :loading="service.loading"
                    outlined
                  >
                    <v-icon>refresh</v-icon>
                    Rafraîchir les libellés en doublon
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </v-expansion-panel-content>
        </v-expansion-panel>
      </v-expansion-panels>
    </v-col>
  </v-row>
</template>

<script>
import {ErhgoOccupationDetail} from 'erhgo-api-client';
import LabelService from './LabelsService';

export default {
  name: 'ErhgoOccupationLabels',
  data() {
    return {
      service: new LabelService(this.initialOccupation),
      otherOccupationUsingLabels: {},
    };
  },
  props: {
    initialOccupation: {
      required: true,
      type: ErhgoOccupationDetail,
    },
  },
  async created() {
    await this.refreshAllLabels();
  },
  computed: {
    titleLabel() {
      return `Titre principal ${this.service.alternative.length ? '(cliquez sur l\'étoile pour définir un libellé alternatif comme principal)' : ''}`;
    },
    error() {
      return this.service?.showAPIError;
    },
  },
  methods: {
    isLabelAlreadyUsedInTitle(label) {
      return label && this.service?.title?.trim() === label.trim();
    },
    isLabelAlreadyUsedInLabels(label) {
      return label && this.service?.alternative?.filter(a => a.title?.trim() === label.trim()).length;
    },
    async refreshOccupationsDuplicatingLabel(l) {
      this.$set(this.otherOccupationUsingLabels, l, await this.service.fetchDuplicatedOccupationsForLabel(l));
    },
    async refreshAllLabels() {
      const allLabels = [this.initialOccupation.title, ...this.initialOccupation.alternativeLabels];
      await Promise.all(allLabels.map(async l => this.refreshOccupationsDuplicatingLabel(l)));
    },
  },
};
</script>
