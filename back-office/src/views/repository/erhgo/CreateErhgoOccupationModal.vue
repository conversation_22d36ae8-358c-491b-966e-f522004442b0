<template>
  <div>
    <v-dialog v-model="showLabelPopin" persistent shrink width="50%">
      <v-card>
          <v-card-title>
            Création d'un métier erhgo
          </v-card-title>
          <v-card-text>
            <v-alert v-if="occupationService.showAPIError" type="error" outlined>
              Une erreur est survenue, veuillez réessayer ultérieurement
            </v-alert>
            <v-text-field label="Titre du métier"
                          :rules="[rules.required, rules.max255]"
                          v-if="!labelValidated"
                          v-model="title"/>
            <occupation-similar-label-search
              v-if="labelValidated"
              :best-matching-occupation="bestMatchingOccupation"
              :similar-occupations="similarOccupations"
              :search-title="title"
            />
          </v-card-text>
          <v-divider/>
          <v-card-actions>
            <v-btn
              small
              @click="create()"
              :disabled="!title"
              :loading="occupationService.loading"
              v-if="labelValidated"
              color="primary"
              outlined
            >
              C<PERSON><PERSON> le métier vide
            </v-btn>
            <v-spacer/>
            <v-tooltip bottom v-if="labelValidated">
              <template v-slot:activator="{ on }">
                <v-btn v-on="on"
                       small
                       color="primary"
                       :loading="occupationService.loading"
                       @click="continueQualifyErhgoOccupation()"
                       class="mx-3">
                  Créer et qualifier automatiquement
                </v-btn>
              </template>
              <span class="text-center">Le métier sera automatiquement qualifié par l'IA </span>
            </v-tooltip>
            <v-btn
              :disabled="!title"
              :loading="occupationService.loading"
              color="primary"
              @click="searchAlgoliaSimilarOccupations()"
              v-else>
              Vérifier le libellé
            </v-btn>
            <v-spacer/>
        <v-btn outlined
               color="error"
               :loading="occupationService.loading" @click="close">Fermer</v-btn>
          </v-card-actions>
      </v-card>
    </v-dialog>

    <qualify-erhgo-occupation-popin
      v-if="showQualifyPopin"
      :dialog="showQualifyPopin"
      :report-data="reportData"
      :occupation-id="occupationId"
      @close="close"
    />
  </div>
</template>

<script>
import ErhgoOccupationService from './ErhgoOccupationService';
import OccupationSimilarLabelSearch from './OccupationSimilarLabelSearch.vue';
import { v4 as uuid } from 'uuid';
import QualifyErhgoOccupationPopin from './QualifyErhgoOccupationPopin';

export default {
  name: 'CreateErhgoOccupationModal',
  components: {
    QualifyErhgoOccupationPopin,
    OccupationSimilarLabelSearch,
  },
  props: {
    value: {type: Boolean, default: true},
  },
  data() {
    return {
      rules: {
        required: value => !!value || 'Requis',
        max255: value => value.length <= 255 || 'Texte trop long',
      },
      title: '',
      showQualifyPopin: false,
      labelValidated: false,
      occupationId: '',
      occupationService: null,
      showLabelPopin: false,
    };
  },
  watch: {
    value(newValue, oldValue) {
      if (newValue) {
        this.resetComponent();
      }
      if (oldValue !== newValue && !this.labelValidated) {
        this.showLabelPopin = this.value ;
      }
    },
  },
  async created() {
    this.resetComponent();
    this.showLabelPopin = this.value;
  },
  computed: {
    reportData() {
      return this.occupationService.reportData;
    },
    similarOccupations() {
      return this.occupationService.similarOccupations;
    },
    bestMatchingOccupation() {
      return this.occupationService.bestMatchingOccupation;
    },

  },
  methods: {
    resetComponent() {
      this.occupationId = uuid();
      this.occupationService = new ErhgoOccupationService(this.occupationId);
      this.title = '';
      this.showQualifyPopin = false;
      this.labelValidated = false;
    },
    close() {
      this.resetComponent();
      this.$emit('input', false);
    },
    async create() {
      await this.occupationService.createErhgoOccupation(this.title);
      this.close();
    },
    async continueQualifyErhgoOccupation() {
      await this.occupationService.qualifyErhgoOccupation(this.title);
      if (!this.occupationService.showAPIError) {
        this.showQualifyPopin = true;
        this.showLabelPopin = false;
      }
    },

    async searchAlgoliaSimilarOccupations() {
      await this.occupationService.searchOccupations(this.title);
      if(this.similarOccupations.length) {
        await this.searchBestMatchingOccupation();
      }
      this.labelValidated = true;
      return this.similarOccupations?.length > 0;
    },

    async searchBestMatchingOccupation() {
      return await this.occupationService.searchBestMatchingOccupationFromOpenAi(this.title);
    },
  },
};
</script>
