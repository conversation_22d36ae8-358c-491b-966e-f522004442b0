<template>
  <v-container fluid>
    <v-row no-gutters align="center">
      <v-col cols="6">
        <h1 class="d-flex align-center">
          <v-icon x-large color="primary" class="mr-2">merge_type</v-icon>
          Fusion de métiers erhgo
        </h1>
      </v-col>
    </v-row>
    <v-alert color="info"
             icon="info"
             prominent
             dismissible
             text
             dense>
      <ul>
        <li>
          Le titre et les titres alternatifs du métier supprimé s'ajoutent aux titres alternatifs du métier cible
        </li>
        <li>
          Le niveaux de maîtrise, l'état prioritaire et la description restent ceux du métier cible
        </li>
        <li>
          Les aptitudes, métiers ESCO, codes ROME, activités, contextes et comportements du métier supprimé s'ajoutent au métier cible, le caractère obligatoire est maintenu
        </li>
        <li>
          Vous pouvez choisir d'ignorer la fusion des activités du métier supprimé
        </li>
        <li>
          Le métier cible est dé-qualifié
        </li>
        <li>
          Les utilisateurs qui avaient choisis le métier supprimé dans des expériences auront maintenant le métier cible
        </li>
        <li>
          Pensez également à vérifier que les catégories de comportement du métier cible sont toujours cohérentes après fusion.
        </li>
      </ul>
    </v-alert>
    <v-row>
      <v-col cols="6" class="px-4">
        <erhgo-occupations-selector :filter-items-ids="filterItemsCodes"
                                    :actual-items="targetOccupation ? [targetOccupation] : null"
                                    autocomplete-id="targetOccupation"
                                    label="Métier ERHGO cible"
                                    @onRemove="targetOccupation = null"
                                    @onSelected="assignTargetOccupation" />
      </v-col>
      <v-col cols="6" class="px-4">
        <erhgo-occupations-selector :filter-items-ids="filterItemsCodes"
                                    :actual-items="mergedOccupation ? [mergedOccupation] : null"
                                    autocomplete-id="mergedOccupation"
                                    label="Métier ERHGO supprimé"
                                    @onRemove="mergedOccupation = null"
                                    @onSelected="assignMergedOccupation" />
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="12">
        <v-checkbox
          v-model="ignoreActivities"
          label="Ignorer les activités du métier supprimé"
          :disabled="!isValid"
        />
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <v-btn
            :loading="erhgoOccupationService.loading"
            color="success"
            @click="submit"
            :disabled="!isValid">
          <v-icon class="mr-2">
            call_merge
          </v-icon>
          Fusionner les métiers
        </v-btn>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import ErhgoOccupationService from './ErhgoOccupationService';
import _ from 'lodash';
import ErhgoOccupationsSelector from '@/components/common/ErhgoOccupationsSelector';

export default {
  name: 'MergeErhgoOccupations',
  components: {ErhgoOccupationsSelector},
  data() {
    return {
      erhgoOccupationService: ErhgoOccupationService,
      isLoading: false,
      targetQuery: null,
      mergedQuery: null,
      targetOccupation: null,
      mergedOccupation: null,
      dummyModel: null,
      items: [],
      ignoreActivities: true,
    };
  },
  created() {
    this.erhgoOccupationService = new ErhgoOccupationService(null);
  },
  watch: {
    targetQuery: _.debounce(async function doSearch(val) {
      await this.fetchOccupations(val);
    }, 700),
    mergedQuery: _.debounce(async function doSearch(val) {
      await this.fetchOccupations(val);
    }, 700),
  },
  computed: {
    selectedOccupations() {
      let selected = [];
      if (this.mergedOccupation) {
        selected.push(this.mergedOccupation);
      }
      if (this.targetOccupation) {
        selected.push(this.targetOccupation);
      }
      return selected;
    },
    isValid() {
      return this.mergedOccupation && this.targetOccupation;
    },
    filterItemsCodes() {
      return [this.targetOccupation, this.mergedOccupation].filter(value => !!value).map(value => value.code);
    },
  },
  methods: {
    async fetchOccupations(query) {
      if (query) {
        try {
          this.isLoading = true;
          this.items = (await this.$api.searchOccupations(query, false)).data.filter(value => !this.selectedOccupations.map(o => o.code).includes(value.code));
        } finally {
          this.isLoading = false;
        }
      }
    },
    assignTargetOccupation(targetOccupation) {
      if (targetOccupation) {
        this.targetOccupation = targetOccupation;
        this.items = [];
        this.dummyModel = null;
      }
    },
    assignMergedOccupation(mergedOccupation) {
      if (mergedOccupation) {
        this.mergedOccupation = mergedOccupation;
        this.items = [];
        this.dummyModel = null;
      }
    },
    async submit() {
      await this.erhgoOccupationService.mergeErhgoOccupations(
        this.targetOccupation.code,
        this.mergedOccupation.code,
        this.ignoreActivities,
      );
    },
  },
};
</script>
