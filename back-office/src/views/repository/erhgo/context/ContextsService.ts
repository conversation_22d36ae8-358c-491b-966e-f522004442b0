import SafeService from 'odas-plugins/SafeService';
import { ContextsPage, ErhgoSearchOrder, SortDirection } from 'erhgo-api-client';
import Vue from 'vue';

export default class ContextsService extends SafeService {
  
  private _contextPage: ContextsPage | null = null;

  async fetchContextPage(size: number, page: number, by: ErhgoSearchOrder, direction: SortDirection, query: string) {
    await this.safeCall(async () => {
      this._contextPage = (await Vue.$api.listContexts(size, page, by, direction, query)).data;
    });
  }

  get contextPage(): ContextsPage | null {
    return this._contextPage;
  }

  set contextPage(value: ContextsPage | null) {
    this._contextPage = value;
  }
}
