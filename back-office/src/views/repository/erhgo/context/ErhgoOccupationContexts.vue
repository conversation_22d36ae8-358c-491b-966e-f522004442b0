<template>
  <div id="contexts">
    <edit-referential-entity :selected-items="occupationContexts"
                             :suggested-items="contexts"
                             with-checkbox
                             :loading="erhgoOccupationContextService.loading"
                             label="Ajouter des contextes au métier erhgo"
                             :subtitle-resolver="subtitleResolver"
                             item-text="title"
                             create-item
                             @addItem="addContext"
                             @removeItem="removeContext"
                             @changeState="changeContextState"
                             @fetchEntity="fetchEntity"
                             @createItem="createContext"/>
    <errorDialog v-model="erhgoOccupationContextService.showAPIError"/>
    <context-create-modal v-model="showCreateContextModal"
                         @closed="closeContextModal"
                         @created="onContextUpdated"
                         :initialTitle="newContextTitle"/>
  </div>
</template>

<script>
import ErhgoOccupationDetail from '../ErhgoOccupationDetail';
import ErhgoOccupationContextService from './ErhgoOccupationContextService';
import EditReferentialEntity from '../common/EditReferentialEntity';
import ErrorDialog from '@/components/common/ErrorDialog.vue';
import ContextCreateModal from '../../context/ContextCreateModal';
import ContextsService from './ContextsService';
import {SortDirection} from 'erhgo-api-client';

export default {
  name: 'ErhgoOccupationContexts',
  components: { EditReferentialEntity, ErrorDialog, ContextCreateModal },
  props: {
    occupation: ErhgoOccupationDetail,
  },
  data() {
    return {
      erhgoOccupationContextService: ErhgoOccupationContextService,
      contextsService: ContextsService,
      showCreateContextModal: false,
      newContextTitle: null,
    };
  },
  computed: {
    occupationContexts() {
      return this.erhgoOccupationContextService.contexts;
    },
    contexts: {
      set(newValue) {
        this.contextsService.contextPage = newValue;
      },
      get() {
        if (this.contextsService.contextPage) {
          return this.contextsService.contextPage.content.filter(c => !this.occupationContexts.map(c2 => c2.id).includes(c.id));
        } else {
          return [];
        }
      },
    },
  },
  methods: {
    subtitleResolver(context) {
      return `${context.categoryLevel.category.title} ${context.categoryLevel && context.categoryLevel.description ? `- ${context.categoryLevel.description}` : ''}`;
    },
    async fetchEntity(query) {
      await this.contextsService.fetchContextPage(100, 0, undefined, SortDirection.ASC, query);
    },
    async addContext(context) {
      await this.erhgoOccupationContextService.addContextToOccupation(context);
      this.contexts = null;
    },
    async removeContext(contextId) {
      await this.erhgoOccupationContextService.removeContextFromOccupation(contextId);
    },
    async changeContextState({id, selected}) {
      await this.erhgoOccupationContextService.updateMandatoryState(id, selected);
    },
    createContext(title) {
      this.newContextTitle = title;
      this.showCreateContextModal = true;
    },
    closeContextModal() {
      this.showCreateContextModal = false;
    },
    async onContextUpdated(context) {
      await this.erhgoOccupationContextService.addContextToOccupation(context);
      this.closeContextModal();
    },
  },
  created() {
    this.erhgoOccupationContextService = new ErhgoOccupationContextService(this.occupation);
    this.contextsService = new ContextsService();
  },
};
</script>
