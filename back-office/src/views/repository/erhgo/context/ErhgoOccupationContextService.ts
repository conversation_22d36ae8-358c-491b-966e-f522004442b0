import SafeService from 'odas-plugins/SafeService';
import {Context, ErhgoOccupationDetail, MandatoryState, OccupationQualificationSource} from 'erhgo-api-client';
import Vue from 'vue';

export default class ErhgoOccupationContextService extends SafeService {

  constructor(private _occupation: ErhgoOccupationDetail) {
    super();
  }

  async updateMandatoryState(contextId: string, selected: boolean) {
    await this.safeCall(async () => {
      const state = selected ? MandatoryState.ESSENTIAL : MandatoryState.OPTIONAL;
      await Vue.$api.setOccupationContextMandatoryState({referentialEntityId: contextId, state, erhgoOccupationId: this._occupation.id});
    });
  }

  async addContextToOccupation(context: Context) {
    await this.safeCall(async () => {
      await Vue.$api.addContextToOccupation({referentialEntityId: context.id, erhgoOccupationId: this._occupation.id});
      this._occupation.contexts.push(this.createOccupationContext(context));
    });
  }

  async removeContextFromOccupation(contextId: string) {
    await this.safeCall(async () => {
      await Vue.$api.removeContextFromOccupation({referentialEntityId: contextId, erhgoOccupationId: this._occupation.id});
      this._occupation.contexts = this._occupation.contexts.filter(value => value.context.id != contextId);
    });
  }

  get contexts() {
    return this._occupation.contexts.map(value => (
      {
        ...value.context,
        selected: value.state === MandatoryState.ESSENTIAL,
        source: value.source,
        skillsLabels: value.skills?.map(skill => skill.title).join(', '),
        modificationDate: value.updatedDate,
        modifiedBy: value.lastModifiedBy,
      }));
  }

  private createOccupationContext(context: Context) {
    return {
      state: MandatoryState.OPTIONAL,
      source: OccupationQualificationSource.MANUAL,
      context: context,
    };
  }
}
