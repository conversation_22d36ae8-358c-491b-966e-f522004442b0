import SafeService from 'odas-plugins/SafeService';
import Vue from 'vue';
import {
  ErhgoOccupationDetail,
  ErhgoOccupationMinimumInfo,
  ErhgoOccupationSearch,
  ErhgoOccupationState,
  ErhgoOccupationSumUp,
  GenerationReportItem,
  OccupationCreationReason,
  RomeSummary,
  UpdateSpecificationsCommand,
  UpdateWorkEnvironmentsCommand,
} from 'erhgo-api-client';

export default class ErhgoOccupationService extends SafeService {
  _occupation?: ErhgoOccupationDetail;
  _occupationSummary?: ErhgoOccupationSumUp;
  private _updateMasterySuccess = false;
  private _updateDescriptionSuccess = false;

  // reportDara
  private _reportData: GenerationReportItem[] = [];
  private _similarOccupations: ErhgoOccupationSearch[] = [];
  private _bestMatchingOccupation?: ErhgoOccupationMinimumInfo;
  // for fix re-render entity component
  private _componentKey = 0;


  constructor(private _id: string) {
    super();
  }


  async searchOccupations(query: string) {
    return  await this.safeCall(async () => {
      this._similarOccupations = (await Vue.$api.searchOccupations(query, false)).data.filter((value, index) => index < 10);
    });
  }

  async searchBestMatchingOccupationFromOpenAi(newLabel: string) {
    return  await this.safeCall(async () => {
      return  this._bestMatchingOccupation = (await Vue.$api.findBestMatchingOccupationForQuery(newLabel)).data;
    });
  }

  async qualifyOccupationState() {
    return this.safeCall(async () => Vue.$api.qualifyOccupationState(this._id));
  }

  async unqualifyOccupationState() {
    return this.safeCall(async () => (await Vue.$api.unqualifyOccupationState(this._id)).data);
  }

  async fetchOccupation() {
    await this.safeCall(async () => {
      this._occupation = (await Vue.$api.getErhgoOccupation(this._id)).data;
    });
    return this;
  }

  async fetchOccupationSummary() {
    await this.safeCall(async () => {
      this._occupationSummary = (await Vue.$api.getErhgoOccupationSumUp(this._id)).data;
    });
    return this;
  }

  async linkRomeToErhgoOccupation(id: string, rome: RomeSummary) {
    await this.safeCall(async () => {
      await Vue.$api.linkRomeToErhgoOccupation({id, romeCode: rome.code});
      this._occupation!.romeOccupations = [...this._occupation!.romeOccupations || [], rome];
    });
  }

  async generateRomeOccupations() {
    await this.safeCall(async () => {
      await Vue.$api.generateOccupationClassificationRome(this._id);
      await this.fetchOccupation();
    });
  }

  async deleteLinkRomeToErhgoOccupation(id: string, romeCode: string) {
    await this.safeCall(async () => {
      await Vue.$api.unlinkRomeFromErhgoOccupation({id, romeCode});
      this._occupation!.romeOccupations = this._occupation!.romeOccupations!.filter(value => value.code !== romeCode);
    });
  }

  async deleteLinkSkillToErhgoOccupation(skillUri: string) {
    await this.safeCall(async () => {
      await Vue.$api.unlinkSkillFromErhgoOccupation({id: this._id, skillUri});
      this._occupation!.skills = this._occupation!.skills!.filter(value => value.uri !== skillUri);
    });
  }

  async updateMasteryLevel() {
    await this.safeCall(async () => {
      await Vue.$api.updateMasteryLevel({id: this._occupation!.id, level: this._occupation!.level!});
      this._updateMasterySuccess = true;
      window.setTimeout(() => {
        this._updateMasterySuccess = false;
      }, 3000);
    });
  }

  async generateSepecificationAndMasteryLevel() {
    await this.safeCall(async () => {
      await Vue.$api.generateOccupationSpecificationAndMasteryLevel(this._id);
      this._updateMasterySuccess = true;
      window.setTimeout(() => {
        this._updateMasterySuccess = false;
      }, 3000);
      await this.fetchOccupation();
    });
  }

  async updateDescription() {
    await this.safeCall(async () => {
      if (this._occupation?.description && this._occupation?.id) {
        await Vue.$api.updateDescription({id: this._occupation?.id, description: this._occupation?.description});
        this._updateDescriptionSuccess = true;
        window.setTimeout(() => {
          this._updateDescriptionSuccess = false;
        }, 3000);
      }
    });
  }
  async generateDescription() {
    await this.safeCall(async () => {
      if (this._occupation?.id) {
        const result = (await Vue.$api.generateErghoOccupationDescription(this._id)).data;
        this._occupation!.description = result.description;
        await this.updateDescription();
      }
    });
  }

  async linkEscoOccupationToErhgoOccupation(id: string, escoUri: string) {
    await this.safeCall(async () => {
      const response = (await Vue.$api.linkEscoOccupationToErhgoOccupation({id , escoUri})).data;
      this._occupation = response;
      this._componentKey++;
    });
  }

  async unlinkEscoOccupationFromErhgoOccupation(id: string, escoUri: string) {
    await this.safeCall(async () => {
      this._occupation = (await Vue.$api.unlinkEscoOccupationFromErhgoOccupation({id, escoUri})).data;
      this._componentKey++;
    });
  }

  async createErhgoOccupation(title: string) {
    await this.safeCall(async () => {
      await Vue.$api.createErhgoOccupation({id: this._id, title, occupationCreationReason: OccupationCreationReason.BO});
      await Vue.$router.push({name: 'erhgo_occupation_detail', params: {id: this._id}});
    });
  }

  async qualifyErhgoOccupation(title: string) {
    return await this.safeCall(async () => {
      return this._reportData  = (await Vue.$api.generateErhgoOccupationGlobalQualification({id: this._id, title, occupationCreationReason: OccupationCreationReason.BO})).data;
    });
  }

  get similarOccupations() {
    return this._similarOccupations;
  }

  get bestMatchingOccupation() {
    return this._bestMatchingOccupation;
  }

  get reportData(){
    return this._reportData;
  }

  get componentKey(): number {
    return this._componentKey;
  }

  async mergeErhgoOccupations(targetOccupationId: string, occupationIdToDelete: string, ignoreActivities: boolean) {
    await this.safeCall(async () => {
      await Vue.$api.mergeOccupations({targetOccupationId, occupationIdToDelete, ignoreActivities});
      await Vue.$router.push({name: 'erhgo_occupation_detail', params: {id: targetOccupationId}});
    });
  }

  get updateDescriptionSuccess(): boolean {
    return this._updateDescriptionSuccess;
  }


  get updateMasterySuccess(): boolean {
    return this._updateMasterySuccess;
  }

  get occupation() {
    return this._occupation;
  }

  get occupationSummary() {
    return this._occupationSummary;
  }

  get isOccupationQualified() {
    return this._occupation?.erhgoOccupationState === ErhgoOccupationState.QUALIFIED_V1
      || this._occupation?.erhgoOccupationState === ErhgoOccupationState.QUALIFIED_V2;
  }

  async updateWorkEnvironments(command: UpdateWorkEnvironmentsCommand) {
    await this.safeCall(async () => {
      await Vue.$api.updateWorkEnvironments(command);
    });
  }

  async updateSpecifications(command: UpdateSpecificationsCommand) {
    await this.safeCall(async () => {
      await Vue.$api.updateSpecifications(command);
    });
  }
}
