<template>
  <v-dialog v-model="dialog" persistent max-width="800px">
    <v-card>
      <v-card-title class="d-flex justify-space-between align-center">
        <span class="text-h5 deep-purple--text text--darken-4">
          <v-icon large left>mdi-chart-bubble</v-icon>
        Rapport OpenAI de qualification du métier
        </span>
        <v-tooltip bottom>
          <template v-slot:activator="{ on, attrs }">
            <v-btn icon color="red" v-bind="attrs" v-on="on" @click="close">
              <v-icon>mdi-close-circle</v-icon>
            </v-btn>
          </template>
          <span>Fermer le Rapport</span>
        </v-tooltip>
      </v-card-title>
      <v-card-text  v-if="!reportData.length">
        <v-container fluid>
          <v-row justify="space-between">
            <v-col cols="12"  class="d-flex align-center justify-center">
              <v-progress-circular
                color="primary"
                indeterminate
                :size="75"
              />
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>
      <v-card-text v-else>
        <v-container fluid>
          <v-row justify="space-between">
            <v-col cols="8"  class="d-flex align-center justify-start">
              <v-chip color="pink" text-color="white" class="mr-4">
                <v-icon left>mdi-cash</v-icon>
                Coût: {{ totalCost.toFixed(2) }}€
              </v-chip>
              <v-chip color="cyan" text-color="white" class="mr-4">
                <v-icon left>mdi-timer-sand</v-icon>
                Délai: {{ totalDuration }}
              </v-chip>
              <v-chip :color="finalStateColor" text-color="white">
                <v-icon left>mdi-check-circle</v-icon>
                État: {{ finalState }}
              </v-chip>
            </v-col>
            <v-col cols="4"  class="d-flex align-center justify-start">
              <v-btn color="primary" dark class="call-to-action-btn" @click="goToDetails">
                <v-icon left>mdi-arrow-right-bold</v-icon>
                Détails du Métier
              </v-btn>
            </v-col>
          </v-row>
          <v-divider class="my-4"/>
          <v-subheader>Qualification Générées</v-subheader>
          <v-list dense>
            <v-list-item v-for="(item, index) in reportData" :key="index">
              <v-list-item-avatar>
                <v-icon :color="item.success ? 'green' : 'red'">mdi-checkbox-marked-circle-outline</v-icon>
              </v-list-item-avatar>
              <v-list-item-content>
                <v-list-item-title><strong>{{ item.title }}</strong></v-list-item-title>
                <v-list-item-subtitle>État: {{ item.success ? 'OK' : 'Erreur' }} | Tentatives: {{ item.nbTry }}
                  <v-fade-transition>
                    <div v-if="!item.success && item.errorMessage" class="error-message">
                      <v-icon color="red">mdi-alert-circle</v-icon>
                      {{ item.errorMessage }}
                    </div>
                  </v-fade-transition>
                </v-list-item-subtitle>
              </v-list-item-content>
            </v-list-item>
          </v-list>
        </v-container>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
import Vue from 'vue';
export default {
  name: 'QualifyErhgoOccupationPopin',
  props: {
    dialog: Boolean,
    reportData: {
      required: true,
      type: [],
    },
    occupationId: String,
  },
  data() {
    return {
    };
  },
  computed: {
    totalDuration() {
      const totalSeconds = this.reportData.reduce((acc, item) => acc + item.durationInMs / 1000, 0);
      return `${ Math.floor(totalSeconds / 60)} min ${ Math.floor(totalSeconds % 60).toString().padStart(2, '0')} s`;
    },
    totalCost() {
      return this.reportData.reduce((total, op, index) => {
        let costPerInputToken, costPerOutputToken;
        if (index === 3 || index === this.reportData.length - 1) {
          // Tarifs pour gpt-4o soit $5.00 / 1M tokens pour les entrées et $15.00 / 1M tokens pour les sorties.
          costPerInputToken = 0.000005;
          costPerOutputToken = 0.000015;
        } else {
          // Tarifs pour gpt-3.5-turbo soit $8.00 / 1M tokens pour les entrées et $6.00 / 1M tokens pour les sorties.
          costPerInputToken = 0.000008;
          costPerOutputToken = 0.000006;
        }
        const costForOp = (op.promptToken * costPerInputToken) + (op.completionToken * costPerOutputToken);
        return total + costForOp;
      }, 0);    },
    finalStateColor() {
      return this.finalState === 'succès' ? 'green' : this.finalState === 'échec' ? 'red' : 'orange';
    },
    finalState() {
      const allSuccess = this.reportData.every(item => item.success);
      const allFail = this.reportData.every(activity => !activity.success);
      if (allSuccess) {
        return  'succès';
      } else if (allFail) {
        return  'échec';
      } else {
        return  'succès partiel';
      }
    },
  },
  methods: {
    async goToDetails() {
      await Vue.$router.push({name: 'erhgo_occupation_detail', params: {id: this.occupationId}});
      this.$emit('close');
    },
    close() {
      this.$emit('close');
    },
  },
};
</script>

<style scoped>
.call-to-action-btn {
  transition: transform 0.2s ease-in-out;
}

.call-to-action-btn:hover {
  transform: scale(1.1);
  background-color: #FFAB40;
}

</style>
