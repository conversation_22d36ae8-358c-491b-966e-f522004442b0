<template>
  <v-tooltip top nudge-bottom="50">
    <template v-slot:activator="{ on }">
      <v-list-item-content v-on="on">
        <v-list-item-title class="font-weight-bold" :class="isRed ? 'red--text' : ''">{{
            item[itemText]
          }}
        </v-list-item-title>
        <v-list-item-subtitle class="font-weight-bold" v-if="subtitleResolver" :class="isRed ? 'red--text' : ''">
          {{ subtitleResolver(item) | truncate(200) }}
        </v-list-item-subtitle>
        <v-list-item-subtitle v-if="item.updatedDate" :class="isRed ? 'red--text' : ''">
          Dernière modification par {{ item.lastModifiedBy ? item.lastModifiedBy : 'inconnu' }}
          ({{ item.updatedDate | formatDateTime }})
        </v-list-item-subtitle>
      </v-list-item-content>
    </template>
    <span v-if="item.source === OccupationQualificationSource.MANUAL">Ajoutée manuellement</span>
    <span v-else-if="item.skillsLabels">Ajoutée depuis les aptitudes&nbsp;: {{item.skillsLabels}}</span>
    <span v-else>Ajoutée depuis une aptitude supprimée</span>
    <br/>
    <span v-if="item.modifiedBy && item.modificationDate">Dernière modification par {{item.modifiedBy}} ({{item.modificationDate | formatDateTime}})</span>
  </v-tooltip>
</template>

<script>
import {OccupationQualificationSource} from 'erhgo-api-client';

export default {
  name: 'EntityReferentialListItem',
  data() {
    return {
      OccupationQualificationSource,
    };
  },
  props: {
    isRed: {
      type: Boolean,
    },
    subtitleResolver: Function,
    item: {
      type: Object,
      required: true,
    },
    itemText: {
      type: String,
      required: true,
    },
  },
};
</script>
