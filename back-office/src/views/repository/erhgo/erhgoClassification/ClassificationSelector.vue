<template>
  <div v-if="occupationService.occupation">
    <v-tooltip bottom>
      <template v-slot:activator="{ on }">
        <v-btn icon
               v-on="on"
               small
               color="primary"
               :loading="service.loading"
               @click="generateErhgoClassifications"
               class="ml-auto">
          <v-icon small
                  color="primary">
            fa-recycle
          </v-icon>
        </v-btn>
      </template>
      <span class="text-center">les souhaits seront genérés automatiquement par l'IA </span>
    </v-tooltip>
    <v-row no-gutters>
      <v-col cols="12" class="py-1">
        <span class="text-caption">Correspondant à "ce que je veux faire" (quatre maximum)</span>
        <v-progress-circular v-if="service.loading" size="15" indeterminate class="pl-2"/>
        <v-icon color="success" small v-if="service.success">fa-check-circle</v-icon>

      </v-col>
      <v-col cols="4"
             v-for="item in service.erhgoClassifications"
             :key="`key-${item.code}`"
             class="px-5"
             style="border-right: 1px solid black"
      >
        <v-checkbox
            dense
            hide-details
            :label="item.title"
            class="mt-0"
            :value="item.code"
            v-model="service.selectedClassifications"
            :disabled="service.loading || (service.selectedClassifications.length >=4  && !service.selectedClassifications.includes(item.code))"
        />
      </v-col>
    </v-row>
  </div>
  <v-progress-circular v-else indeterminate/>
</template>

<script>
import OccupationErhgoClassificationService from './OccupationErhgoClassificationService';
import ErhgoOccupationService from '@/views/repository/erhgo/ErhgoOccupationService';

export default {
  name: 'occupation-erhgo-classification-selector',
  props: {
    occupationService: {
      required: true,
      type: ErhgoOccupationService,
    },
  },
  data() {
    return {
      service: new OccupationErhgoClassificationService(this.occupationService.occupation.erhgoClassifications.map(c => c.code), this.occupationService.occupation.id),
      initialized: false,
    };
  },
  methods: {
    async generateErhgoClassifications() {
      await this.service.generateErhgoClassifications();
    },
  },
  async created() {
    await this.service.fetchAllErhgoClassifications();
  },
};
</script>

