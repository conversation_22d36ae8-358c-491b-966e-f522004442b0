<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            <h1>
              <v-icon large color="primary">mdi-web</v-icon>
              {{ $t('ref.erhgoOccupation.list') }}
              <v-tooltip top>
                <template v-slot:activator="{ on }">
                  <v-icon color="primary" v-on="on">mdi-information</v-icon>
                </template>
                - Non qualifié : aucune relecture de la qualification n'a été faite
                <br />- Qualif V1 : qualification faite par l'équipe, avant Claire
                <br />- Qualif V2 : qualification faite par Claire
                <br />- Qualif auto. : Métier qualifié automatiquement par OpenAI, mais dont la qualification n'a pas
                été
                confirmée
                <br />- Qualif confirmée (final) : La qualification a été confirmée manuellement
              </v-tooltip>

            </h1>
          </v-card-title>
          <v-card-text>
            <v-row class="mb-4">
              <autocomplete v-model='selectedActivities'
                            searchRestUrlApi='/api/odas/activityLabel/search'
                            label='Saisir une activité'
                            clearable
                            :size=1
                            class="sm-6"
              >
                <template slot="listItem" slot-scope="slotProps">
                  <activity-list-item :activity="slotProps.listItem"/>
                </template>
              </autocomplete>
              <v-spacer/>
              <v-text-field v-model="query"
                            append-icon="search"
                            label="Chercher par code ISCO / ESCO uri / titre principal / libellé alternatif"
                            class="sm-6"
              />
            </v-row>
            <v-data-table id="erhgoList"
                          :headers="headers"
                          :items="occupationPage ? occupationPage.content : []"
                          :loading="erhgoOccupationsService.loading"
                          multi-sort
                          class="elevation-15"
                          item-key="uri"
                          :sort-by.sync="pagination.sortBy"
                          :page.sync="pagination.page"
                          :sort-desc.sync="pagination.descending"
                          :items-per-page.sync="pagination.rowsPerPage"
                          :server-items-length="occupationPage ? occupationPage.totalNumberOfElements : null"
                          :footer-props="{
                            'items-per-page-options': rowsPerPage,
                            'items-per-page-text': 'Nb lignes par page',
                          }"
                          loading-text="Chargement en cours..."
            >
              <template v-slot:item="props">
                <tr>
                  <td class="text-left">{{ props.item.title }}</td>
                  <td class="text-left">{{ props.item.skillSize }}</td>
                  <td class="text-left">{{ props.item.qualifiedSkillNumber }}</td>
                  <td class="text-left">{{ props.item.notQualifiedSkillNumber }}</td>
                  <td class="text-center">
                    <v-chip
                      class="ma-1 white--text"
                      color="black"
                      v-if="props.item.erhgoOccupationState === erhgoOccupationState.TO_CONFIRM">
                      À confirmer
                    </v-chip>
                    <v-chip
                        class="ma-1 black--text"
                        color="warning"
                        v-else-if="props.item.erhgoOccupationState === erhgoOccupationState.QUALIFIED_V2">
                        Qualifié V2
                      </v-chip>
                      <v-chip
                        class="ma-1 black--text"
                        color="warning"
                        v-else-if="props.item.erhgoOccupationState === erhgoOccupationState.QUALIFIED_V1">
                        Qualifié V1
                      </v-chip>
                      <v-chip
                        class="ma-1 white--text"
                        color="interaction"
                        v-else-if="props.item.erhgoOccupationState === erhgoOccupationState.QUALIFIED_V3">
                        Qualif. auto
                      </v-chip>
                      <v-chip
                        class="ma-1"
                        color="primary"
                        v-else-if="props.item.erhgoOccupationState === erhgoOccupationState.QUALIFIED_V3_CONFIRMED">
                        Qualif. confirmée
                      </v-chip>
                    <v-chip class="ma-1" color="error"
                            v-else>
                      À qualifier
                    </v-chip>
                  </td>
                  <td class="d-flex align-center">
                    <v-tooltip left>
                      <template v-slot:activator="{ on }">
                        <v-btn text
                               icon
                               color="primary"
                               v-on="on"
                               class="editOccupationButton"
                               :to="{
                        name: 'erhgo_occupation_detail',
                        params: {
                          id: props.item.id,
                        }
                     }">
                          <v-icon>edit</v-icon>
                        </v-btn>
                      </template>
                      <span>Modifier</span>
                    </v-tooltip>
                    <v-tooltip left>
                      <template v-slot:activator="{ on }">
                        <v-btn text
                               icon
                               color="primary"
                               v-on="on"
                               class="showOccupationButton"
                               @click="openOccupationPopin(props.item.id)"
                        >
                          <v-icon>visibility</v-icon>
                        </v-btn>
                      </template>
                      <span>Visualiser</span>
                    </v-tooltip>
                    <pole-emploi-link v-if="props.item.romeCodes && props.item.romeCodes.length"
                                      :rome-codes="props.item.romeCodes"/>
                  </td>
                </tr>
              </template>
              <template v-slot:footer.page-text="props">
                Lignes de {{ props.pageStart }} à {{ props.pageStop }} sur {{ props.itemsLength }}
              </template>
              <template slot="no-data">
                <v-alert :value="true"
                         outlined
                         color="error"
                         icon="warning">
                  Aucun résultat disponible
                  {{ !!query ? `pour la recherche suivante: "${query}"` : '' }}
                  {{ !!query && !!selectedActivity ? ' et ' : '' }}
                  {{ !!selectedActivity ? `pour l'activité: "${selectedActivity.title}"` : '' }}
                </v-alert>
              </template>
            </v-data-table>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-tooltip color="red darken-1"
               left>
      <template v-slot:activator="{ on }">
        <v-btn fab
               bottom
               fixed
               right
               dark
               color="red darken-1"
               v-on="on"
               @click="showCreateModal = true"
               class="text-center">
          <v-icon>mdi-plus</v-icon>
        </v-btn>
      </template>
      <span class="font-weight-bold">{{ $t('action.create') }}</span>
    </v-tooltip>
    <create-erhgo-occupation-modal v-model="showCreateModal" />

    <v-dialog v-model="showPrintPopin" v-if="showPrintPopin" max-width="75%">
      <erhgo-occupation-details-popin
        :occupation-id='this.popinOccupationId'
        @onClose="closeOccupationPopin"
      />
    </v-dialog>
  </v-container>
</template>

<script>
import _ from 'lodash';
import { ErhgoOccupationState, ErhgoSearchOrder } from 'erhgo-api-client';
import ErhgoOccupationsService from './ErhgoOccupationsService';
import CreateErhgoOccupationModal from './CreateErhgoOccupationModal';
import ErhgoOccupationDetailsPopin from './ErhgoOccupationDetailPopin';
import Autocomplete from '@/components/common/crud/Autocomplete.vue';
import ActivityListItem from '@/views/repository/activity/ActivityListItem';
import PoleEmploiLink from 'odas-plugins/PoleEmploiLink';

export default {
  components: {
    CreateErhgoOccupationModal,
    ErhgoOccupationDetailsPopin,
    Autocomplete,
    ActivityListItem,
    PoleEmploiLink,
  },
  data() {
    return {
      showCreateModal: false,
      erhgoOccupationsService: ErhgoOccupationsService,
      erhgoOccupationState: ErhgoOccupationState,
      showPrintPopin: false,
      popinOccupationId: null,
      selectedActivities: [],
      headers: [
        {
          text: this.$t('ref.headers.erhgoOccupation.title'),
          sortable: true,
          value: ErhgoSearchOrder.TITLE,
        },
        {
          text: this.$t('ref.headers.erhgoOccupation.totalSkills'),
          sortable: true,
          value: ErhgoSearchOrder.SKILL_SIZE,
        },
        {
          text: this.$t('ref.headers.erhgoOccupation.totalQualifiedSkills'),
          sortable: true,
          value: ErhgoSearchOrder.QUALIFIED_SKILL_NUMBER,
        },
        {
          text: this.$t('ref.headers.erhgoOccupation.totalNotQualifiedSkills'),
          sortable: true,
          value: ErhgoSearchOrder.NOT_QUALIFIED_SKILL_NUMBER,
        },
        {
          text: this.$t('ref.headers.erhgoOccupation.state'),
          sortable: true,
          value: ErhgoSearchOrder.QUALIFICATION_STATE,
        },
        {
          text: this.$t('ref.headers.actions'),
          align: 'right',
          sortable: false,
        },
      ],
      rowsPerPage: [10, 25, 50, 100, 200],
      pagination: {
        page: 1,
        sortBy: [
          ErhgoSearchOrder.TITLE,
        ],
        descending: [false],
        rowsPerPage: 100,
      },
      query: null,
    };
  },
  watch: {
    pagination: {
      handler: _.debounce(function () {
        this.getDataFromApi();
      }, 200),
      deep: true,
    },
    query: _.debounce(function () {
      this.pagination.page = 1;
      this.getDataFromApi();
    }, 200),
    selectedActivity: _.debounce(function () {
      this.pagination.page = 1;
      this.getDataFromApi();
    }, 200),
    filters() {
      const query = this.filters.sortBy.reduce((currentQuery, header, i) => {
        const orderBy = this.filters.descending[i] ? 'desc' : 'asc';
        const key = header.toLowerCase().replaceAll('_', '-');
        currentQuery[key] = orderBy;
        return currentQuery;
      }, {});
      if (!_.isEqual(query, this.$route.query)) {
        this.$router.replace({query});
      }
    },
  },
  async created() {
    this.erhgoOccupationsService = new ErhgoOccupationsService();
    await this.getDataFromApi();
    this.checkURLQueryFilters();
  },
  methods: {
    async getDataFromApi() {
      await this.erhgoOccupationsService.fetchOccupationPage(
        this.pagination.rowsPerPage,
        this.pagination.page - 1,
        this.pagination.sortBy,
        this.pagination.descending,
        this.query?.trim(),
        this.selectedActivity ? this.selectedActivity.id : '');
    },
    openOccupationPopin(occupationId) {
      this.popinOccupationId = occupationId;
      this.showPrintPopin = true;
    },
    closeOccupationPopin() {
      this.popinOccupationId = null;
      this.showPrintPopin = false;
    },
    checkURLQueryFilters() {
      if (!_.isEmpty(this.$route.query)) {
        const sortBy = [];
        const descending = [];
        Object.keys(ErhgoSearchOrder)
          .forEach(currentOrder => {
            const urlKey = currentOrder.toLowerCase().replaceAll('_', '-');
            const urlSortBy = this.$route.query?.[urlKey];
            if (urlSortBy) {
              sortBy.push(currentOrder);
              descending.push(this.$route.query[urlKey] === 'desc');
            }
            this.pagination = {...this.pagination, sortBy, descending};
          });
      }
    },
  },
  computed: {
    occupationPage() {
      return this.erhgoOccupationsService.occupationPage;
    },
    selectedActivity() {
      return this.selectedActivities.length ? this.selectedActivities[0] : null;
    },
    filters() {
      const {sortBy, descending} = this.pagination;
      return {sortBy, descending};
    },
  },
};
</script>

<style>
.theme--light.v-data-table .v-data-table-header__sort-badge {
  background-color: rgba(255, 255, 255, 0.18) !important;
  color: rgba(255, 255, 255, 0.87) !important;
}
</style>
