import SafeService from 'odas-plugins/SafeService';
import {CriteriaValue, ErhgoOccupationDetail} from 'erhgo-api-client';
import Vue from 'vue';

export default class ErhgoOccupationCriteriaService extends SafeService {

  private _criteriaValues: CriteriaValue[] = [];

  constructor(private _occupation: ErhgoOccupationDetail) {
    super();
    this._criteriaValues = this._occupation.criteriaValues;
  }

  async updateCriteriaValues() {
    const erhgoOccupationId = this._occupation.id;
    const criteriaValueCodes = this._criteriaValues.map(c => c.code);
    await this.safeCall(async () => {
      await Vue.$api.updateErhgoOccupationCriteriaValues({
        criteriaValueCodes,
        erhgoOccupationId,
      });
    });
  }

  get criteriaValues() {
    return this._criteriaValues;
  }

  set criteriaValues(criteriaValues) {
    this._criteriaValues = criteriaValues;
    this.updateCriteriaValues();
  }
}
