<template>
  <div id="occupationCriteriaValues">
    <v-row no-gutters>
      <v-col>
        <criteria-selector v-model="erhgoOccupationCriteriaService.criteriaValues"/>
      </v-col>
    </v-row>
  </div>
</template>

<script>
import {ErhgoOccupationDetail} from 'erhgo-api-client';
import ErhgoOccupationCriteriaService from './ErhgoOccupationCriteriaService';
import CriteriaSelector from '@/components/criteria/CriteriaSelector';

export default {
  name: 'ErhgoOccupationCriteria',
  components: {CriteriaSelector},
  props: {
    occupation: {
      required: false,
      type: ErhgoOccupationDetail,
    },
  },
  data() {
    return {
      erhgoOccupationCriteriaService: new ErhgoOccupationCriteriaService(this.occupation),
    };
  },
};
</script>

<style scoped>

</style>
