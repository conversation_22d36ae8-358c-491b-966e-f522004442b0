<template>
  <div>

  <v-card>
    <v-card-title primary-title>
      <h3>
        <v-icon large color="primary">mdi-comment-plus-outline</v-icon>
        {{ $t('statistics.spontaneousCandidatures') }}
      </h3>
      <v-icon medium color="green" @click="showInfoDialog = true">mdi-information</v-icon>
    </v-card-title>

    <v-card-text>
      <v-row>
        <v-col cols="12">
          <v-data-table
            :headers="spontaneousCandidaturesHeaders"
            :items="spontaneousCandidaturesItems"
            dense
            class="elevation-1 stats"
            :loading="loading"
            hide-default-footer
          />
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
    <v-dialog v-model="showInfoDialog" max-width="60%">
      <v-card>
        <v-card-title>
          <span class="text-h5">Modalité de calcul&nbsp;:</span>
        </v-card-title>
        <v-card-text>
          <ul>
            <li>
              <strong>Nombre de candidatures spontanées par mois (sur les 12 derniers mois)&nbsp;:</strong> Pour chaque mois, on compte simplement le nombre de candidatures spontanées reçues sur le mois.
            </li>
          </ul>
        </v-card-text>
        <v-card-actions>
          <v-spacer/>
          <v-btn color="primary" @click="showInfoDialog = false">Fermer</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import StatisticsService from './StatisticsService';

export default {
  name: 'SpontaneousCandidaturesStats',
  async created() {
    this.statisticsService = new StatisticsService();
    this.loading = true;
    await this.statisticsService.fetchMonthlySpontaneousCandidaturesStats();
    this.loading = false;
  },
  data() {
    return {
      statisticsService: null,
      showInfoDialog: false,
      loading: false,
      spontaneousCandidaturesHeaders: [
        { text: 'Mois', value: 'month', width: '100px' },
        { text: 'Total Candidatures Spontanées', value: 'totalSpontaneousCandidatureCount' },
      ],
    };
  },
  computed: {
    monthlySpontaneousCandidaturesStats() {
      return this.statisticsService?.monthlySpontaneousCandidaturesStats;
    },
    spontaneousCandidaturesItems() {
      if (!this.monthlySpontaneousCandidaturesStats?.spontaneousCandidaturesStats) return [];
      return this.monthlySpontaneousCandidaturesStats.spontaneousCandidaturesStats.map(item => ({
        month: item.month,
        totalSpontaneousCandidatureCount: this.formatNumberStatValue(item.totalSpontaneousCandidatureCount),
      }));
    },
  },
  methods: {
    formatNumberStatValue(value) {
      return Number(Math.round(value ?? 0)).toLocaleString('fr');
    },
  },
};
</script>

<style lang="scss">
.spontaneous-candidatures-stats {
  .stats tr:nth-child(even) {
    background-color: #ECEFF1;
  }
}
</style>
