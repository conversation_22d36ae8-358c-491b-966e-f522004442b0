import SafeService from 'odas-plugins/SafeService';
import Vue from 'vue';
import { RecruitmentStats } from 'erhgo-api-client';

export default class RecruitmentStatsService extends SafeService {

  private _recruitmentStats: RecruitmentStats | null = null;

  async fetchRecruitmentStats() {
    this._recruitmentStats = (await Vue.$api.getRecruitmentStats()).data;
  }

  get recruitmentStats() {
    return this._recruitmentStats;
  }
}
