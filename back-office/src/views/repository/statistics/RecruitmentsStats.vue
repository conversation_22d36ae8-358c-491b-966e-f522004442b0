<template>
  <v-container>
    <v-card>
      <v-card-title primary-title>
        <h3>
          <v-icon large color="primary">mdi-chart-areaspline</v-icon>
          {{ $t('statistics.recruitmentStats') }}
          <v-icon medium color="green"  @click="dialog = true">mdi-information</v-icon>
        </h3>
      </v-card-title>

      <v-card-text>
        <v-row>
          <v-col cols="12">
            <v-text-field
              v-model="search"
              label="Rechercher une organisation | Code d'organisation | Nom d'ATS connecté"
              prepend-icon="mdi-magnify"
              clearable
            />
          </v-col>
          <v-col cols="12">
            <v-data-table
              :headers="headers"
              :items="items"
              :search="search"
              :items-per-page="pagination.rowsPerPage"
              class="elevation-1 clickable-rows"
              @click:row="onRowClick"
              item-key="organizationCode"
              :loading="!recruitmentStatsService?.recruitmentStats"
              :footer-props="{
                'items-per-page-options': rowsPerPage,
                'items-per-page-text': 'Nb lignes par page',
              }"
              :page.sync="pagination.page"
            >

              <template v-slot:item.lastRecruitmentPublicationDate="{ item }">
                {{ formatDate(item.lastRecruitmentPublicationDate) }}
              </template>
              <template v-slot:item.totalCandidatures="{ item }">
                {{ formatNumber(item.totalCandidatures) }}
              </template>
              <template v-slot:item.totalCandidaturesArchived="{ item }">
                {{formatNumber(item.totalCandidaturesArchived) }}
              </template>
              <template v-slot:item.spontaneousCandidaturesCount="{ item }">
                {{ formatNumber(item.spontaneousCandidaturesCount) }}
              </template>
              <template v-slot:item.spontaneousCandidaturesArchived="{ item }">
                {{ formatNumber(item.spontaneousCandidaturesArchived) }}
              </template>
              <template v-slot:item.selectedCandidaturesCount="{ item }">
                {{ formatNumber(item.selectedCandidaturesCount) }}
              </template>
              <template v-slot:item.contactedCandidaturesCount="{ item }">
                {{ formatNumber(item.contactedCandidaturesCount) }}
              </template>
              <template v-slot:item.refusedCandidaturesCount="{ item }">
                {{ formatNumber(item.refusedCandidaturesCount) }}
              </template>
            </v-data-table>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

   <v-dialog v-model="dialog" max-width="60%">
  <v-card>
    <v-card-title>
      <span class="text-h5">Explication des termes</span>
    </v-card-title>
    <v-card-text>
      <v-list>
        <v-list-item>
          <v-list-item-content>
            <v-list-item-title>Rec</v-list-item-title>
            <v-list-item-subtitle>Recruitement</v-list-item-subtitle>
          </v-list-item-content>
        </v-list-item>
        <v-list-item>
          <v-list-item-content>
            <v-list-item-title>Date der. publi</v-list-item-title>
            <v-list-item-subtitle>Date de dernière publication</v-list-item-subtitle>
          </v-list-item-content>
        </v-list-item>
        <v-list-item>
          <v-list-item-content>
            <v-list-item-title>Individus associés</v-list-item-title>
            <v-list-item-subtitle>Nombre total d'utilisateurs associés à l'organiation</v-list-item-subtitle>
          </v-list-item-content>
        </v-list-item>
        <v-list-item>
          <v-list-item-content>
            <v-list-item-title>Total Cand</v-list-item-title>
            <v-list-item-subtitle>Nombre total de candidatures reçues</v-list-item-subtitle>
          </v-list-item-content>
        </v-list-item>
        <v-list-item>
          <v-list-item-content>
            <v-list-item-title>Total Cand Arch</v-list-item-title>
            <v-list-item-subtitle>Nombre total de candidatures archivées</v-list-item-subtitle>
          </v-list-item-content>
        </v-list-item>
        <v-list-item>
          <v-list-item-content>
            <v-list-item-title>Cand Spont</v-list-item-title>
            <v-list-item-subtitle>Nombre de candidatures spontanées reçues</v-list-item-subtitle>
          </v-list-item-content>
        </v-list-item>
        <v-list-item>
          <v-list-item-content>
            <v-list-item-title>Dont Arch</v-list-item-title>
            <v-list-item-subtitle>Nombre de candidatures spontanées archivées</v-list-item-subtitle>
          </v-list-item-content>
        </v-list-item>
        <v-list-item>
          <v-list-item-content>
            <v-list-item-title>Cand Sélect</v-list-item-title>
            <v-list-item-subtitle>Nombre de candidatures sélectionnées</v-list-item-subtitle>
          </v-list-item-content>
        </v-list-item>
        <v-list-item>
          <v-list-item-content>
            <v-list-item-title>Rec ouverts</v-list-item-title>
            <v-list-item-subtitle>Nombre de recrutements ouverts</v-list-item-subtitle>
          </v-list-item-content>
        </v-list-item>
        <v-list-item>
          <v-list-item-content>
            <v-list-item-title>Rec fermés</v-list-item-title>
            <v-list-item-subtitle>Nombre de recrutements fermés</v-list-item-subtitle>
          </v-list-item-content>
        </v-list-item>
        <v-list-item>
          <v-list-item-content>
            <v-list-item-title>Cand transmises</v-list-item-title>
            <v-list-item-subtitle>Nombre de candidatures transmises</v-list-item-subtitle>
          </v-list-item-content>
        </v-list-item>
        <v-list-item>
          <v-list-item-content>
            <v-list-item-title>Cand contactées</v-list-item-title>
            <v-list-item-subtitle>Nombre de candidatures contactées</v-list-item-subtitle>
          </v-list-item-content>
        </v-list-item>
        <v-list-item>
          <v-list-item-content>
            <v-list-item-title>Cand refusées</v-list-item-title>
            <v-list-item-subtitle>Nombre de candidatures refusées</v-list-item-subtitle>
          </v-list-item-content>
        </v-list-item>
        <v-list-item>
          <v-list-item-content>
            <v-list-item-title>ATS connectés</v-list-item-title>
            <v-list-item-subtitle>Nombre d'ATS connectés à l'organisation</v-list-item-subtitle>
          </v-list-item-content>
        </v-list-item>
        <v-list-item>
          <v-list-item-content>
            <v-list-item-title>Projets</v-list-item-title>
            <v-list-item-subtitle>Les projets associés</v-list-item-subtitle>
          </v-list-item-content>
        </v-list-item>
      </v-list>
    </v-card-text>
    <v-card-actions>
      <v-spacer/>
      <v-btn color="primary" text @click="dialog = false">Fermer</v-btn>
    </v-card-actions>
  </v-card>
</v-dialog>
  </v-container>
</template>

<script>
import RecruitmentStatsService from './RecruitmentStatsService';

export default {
  async created() {
    this.recruitmentStatsService = new RecruitmentStatsService();
    await this.recruitmentStatsService.fetchRecruitmentStats();
  },
  data() {
    return {
      recruitmentStatsService: null,
      search: '',
      dialog: false,
      pagination: {
        page: 1,
        rowsPerPage: 100,
      },
      rowsPerPage: [10, 20, 50, 100, 200, 400, 500],
      headers: [
        { text: 'Organisation', value: 'organizationTitle', sortable: true },
        { text: 'Code', value: 'organizationCode', sortable: true, width: '7%' },
        { text: 'Rec ouverts', value: 'openRecruitmentsCount', sortable: true },
        { text: 'Rec fermés', value: 'closedRecruitmentsCount', sortable: true },
        { text: 'Date der. publi', value: 'lastRecruitmentPublicationDate', sortable: true },
        { text: 'Individus associés', value: 'totalUsersInChannel', sortable: true },
        { text: 'Total Cand', value: 'totalCandidatures', sortable: true },
        { text: 'Dont Arch', value: 'totalCandidaturesArchived', sortable: true },
        { text: 'Total Cand Spont', value: 'spontaneousCandidaturesCount', sortable: true },
        { text: 'Dont Arch', value: 'spontaneousCandidaturesArchived', sortable: true },
        { text: 'Cand Sélect', value: 'selectedCandidaturesCount', sortable: true },
        { text: 'Cand transmises', value: 'totalTransmittedCandidatures', sortable: true },
        { text: 'Cand contactées', value: 'contactedCandidaturesCount', sortable: true },
        { text: 'Cand refusées', value: 'refusedCandidaturesCount', sortable: true },
        { text: 'ATS connectés', value: 'connectedAts', sortable: true },
        { text: 'Projets', value: 'projects', sortable: true },
      ],
    };
  },
  computed: {
    items() {
      return this.recruitmentStatsService?.recruitmentStats?.organizations || [];
    },
  },
  methods: {
    async onRowClick(item) {
      let routeData = await this.$router.resolve({
        name: 'recruitments_list',
        params: { organization_code: item.organizationCode },
      });
      window.open(routeData.href, '_blank');
    },
    formatDate(dateStr) {
      if (!dateStr) return '';
      return new Date(dateStr).toLocaleDateString('fr-FR');
    },
    formatNumber(value) {
      if (value === null || value === undefined) return '0';
      return Number(value).toLocaleString('fr');
    },
  },
};
</script>

<style>
.clickable-rows tbody tr {
  cursor: pointer;
}
</style>
