import SafeService from 'odas-plugins/SafeService';
import Vue from 'vue';
import {
  MonthlyCandidaturesStats, MonthlySpontaneousCandidaturesStats, SeveralMetricsStats,
} from 'erhgo-api-client';

export default class StatisticsService extends SafeService {

  private _monthlyCandidaturesStats: MonthlyCandidaturesStats | null = null;

  private _monthlySpontaneousCandidaturesStats: MonthlySpontaneousCandidaturesStats | null = null;

  private _severalMetricsStats: SeveralMetricsStats | null = null;

  async fetchMonthlyCandidaturesStats() {
    this._monthlyCandidaturesStats = (await Vue.$api.getMonthlyCandidaturesStats()).data;
  }

  async fetchMonthlySpontaneousCandidaturesStats() {
    this._monthlySpontaneousCandidaturesStats = (await Vue.$api.getMonthlySpontaneousCandidaturesStats()).data;
  }

  async fetchSeveralMetricsStats() {
    this._severalMetricsStats = (await Vue.$api.getSeveralMetricsStats()).data;
  }

  get monthlyCandidaturesStats() {
    return this._monthlyCandidaturesStats;
  }

  get monthlySpontaneousCandidaturesStats() {
    return this._monthlySpontaneousCandidaturesStats;
  }

  get severalMetricsStats() {
    return this._severalMetricsStats;
  }
}
