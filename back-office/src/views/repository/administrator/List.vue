<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            <h1>
              <v-icon large
                      color="primary">mdi-account-multiple
              </v-icon>
              {{ $t('ref.administrator.list') }}
              <v-btn
                text
                color="primary"
                class="mr-2 text-center"
                @click.stop.native
                :to="{
                    name: 'administrators_create',
                    params: { organization_code: organizationCode}
                  }"
              >
                <v-icon>add</v-icon>
                {{ $t('action.create') }}
              </v-btn>
            </h1>
          </v-card-title>
          <v-card-text>
            <v-data-table :headers="headers"
                          :items="items.content"
                          :page.sync="pagination.page"
                          :items-per-page.sync="pagination.rowsPerPage"
                          :footer-props="{
                            'items-per-page-options': rowsPerPage,
                            'items-per-page-text': 'Nb lignes par page',
                          }"
                          :loading="loading"
                          class="elevation-15"
                          item-key="id"
                          :server-items-length="items.totalNumberOfElements">
              <v-progress-linear v-slot:progress
                                 indeterminate/>
              <template v-slot:no-data>
                <v-alert value
                         v-if="!loading"
                         outlined
                         color="error"
                         icon="warning">
                  Aucun résultat disponible
                </v-alert>
                <div v-else class="text-center">
                  <v-progress-circular indeterminate/>
                </div>
              </template>
              <template v-slot:item="props">
                <tr>
                  <td class="text-left">{{ props.item.firstName }}</td>
                  <td class="text-left">{{ props.item.lastName }}</td>
                  <td class="text-left">{{ props.item.email }}</td>
                  <td class="text-left">
                    <v-chip class="ma-1" :color="props.item.enabled ? 'green accent-1' : 'grey lighten-1'">
                      {{props.item.enabled ? 'Activé' : 'Désactivé'}}
                    </v-chip>
                  </td>
                </tr>
              </template>

              <template v-slot:footer.page-text="props">
                Lignes de {{ props.pageStart }} à {{ props.pageStop }} sur {{ props.itemsLength }}
              </template>
            </v-data-table>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import jsog from 'jsog';
import _ from 'lodash';

export default {
  data() {
    return {
      items: [],
      organizationCode: '',
      loading: true,
      pagination: {
        page: 1,
        rowsPerPage: 10,
      },
      rowsPerPage: [10, 25, 50, 100],
      headers: [
        {
          text: 'Prénom',
          align: 'left',
          sortable: false,
        },
        {
          text: 'Nom',
          align: 'left',
          sortable: false,
        },
        {
          text: 'Email',
          align: 'left',
          sortable: false,
        },
        {
          text: 'Activé',
          align: 'left',
          sortable: false,
        },
      ],
    };
  },
  mounted() {
    this.getDataFromApi();
  },
  watch: {
    pagination: {
      handler: _.debounce(function () {
        this.getDataFromApi();
      }, 200),
      deep: true,
    },
  },
  methods: {
    getDataFromApi() {
      this.loading = true;
      this.$axios.get('/api/odas/users/list/odas', {
        params: {
          page: this.pagination.page - 1,
          size: this.pagination.rowsPerPage,
        },
      })
        .then(result => {
          this.items = jsog.decode(result.data);
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style>
</style>
