import SafeService from 'odas-plugins/SafeService';
import Vue from 'vue';
import { EscoOccupationDetail } from 'erhgo-api-client';

export default class EscoOccupationService extends SafeService {

  _occupation: EscoOccupationDetail | null = null;

  constructor(private _uri: string) {
    super();
  }

  async fetchOccupation() {
    await this.safeCall(async () => {
      this._occupation = (await Vue.$api.getEscoOccupation(this._uri)).data;
    });
    return this;
  }

  get occupation() {
    return this._occupation;
  }

}
