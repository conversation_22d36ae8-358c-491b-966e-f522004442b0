<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            <h1>
              <v-icon large color="primary">mdi-web</v-icon>
              Aptitude : {{ skill.title }}
            </h1>
          </v-card-title>
          <v-card-text>
            <v-alert v-if="error"
                     :value="true"
            >
              {{ error }}
            </v-alert>
            <v-progress-circular v-if="loading" indeterminate/>
            <div v-if="!loading">
              <v-row>
                <v-col cols="12">
                  <v-text-field :value="skill.uri" label="URI" readonly/>
                </v-col>
                <v-col cols="12">
                  <v-text-field :value="skill.skillType" label="Type d'aptitude" readonly/>
                </v-col>
                <v-col cols="12">
                  <v-textarea :value="skill.descriptionEN"
                              label="Description (anglais)"
                              rows="3"
                              readonly
                  />
                </v-col>
                <v-col cols="12">
                  <v-textarea v-model="description"
                              label="Description (français)"
                              rows="3"
                              @blur="updateDescription"
                              :counter="2000"
                              :rules="[v => v.length <= 2000 || 'Texte trop long']"
                              :loading="updatingDescription"
                              :success-messages="updateDescriptionSuccessMessage"
                  />
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    :value="(skill.alternativeLabels && skill.alternativeLabels.length > 0) ? skill.alternativeLabels.join(', ') : 'Aucun'"
                    label="Labels alternatifs" readonly/>
                </v-col>
                <v-col cols="12">
                  <v-row>
                    <v-col cols="12" md="6">
                      <h2>Activités associées
                        <span>
                          <v-chip class="ma-1" v-if="skill.activities.length === 0 && !skill.noActivity" color="info">A qualifier</v-chip>
                          <span v-else>({{ skill.activities.length || 'Aucune' }})</span>
                        </span>
                        <span class="text-caption" v-if="updatingActivity"><v-progress-circular indeterminate size="16"/>
                          Enregistrement en cours...
                        </span>
                      </h2>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12">
                      <v-checkbox
                        v-model="noActivity"
                        @change="handleToggleNoActivity"
                        :disabled="updatingActivity"
                        label="Aucune activité pour cette aptitude"
                      />
                    </v-col>
                  </v-row>
                  <v-row v-if="!noActivity">
                    <v-col cols="12">
                      <autocomplete :value="skill.activities"
                                    empty
                                    searchRestUrlApi="/api/odas/activityLabel/search"
                                    label="Chercher une activité"
                                    @input="onActivityAdded"
                                    :disabled="updatingActivity"
                                    close-on-select
                                    no-filter
                      >

                        <template v-slot:listItem="slotProps">
                          <activity-list-item :activity="slotProps.listItem"/>
                        </template>
                      </autocomplete>
                    </v-col>
                    <v-col cols="12">
                      <v-btn @click.stop="showCreateActivityModal = true">
                        Créer une activité
                      </v-btn>
                    </v-col>
                  </v-row>
                  <v-list two-line>
                    <v-list-item v-for="activity in skill.activities" :key="activity.id">
                      <v-list-item-content>
                        <v-list-item-title class="font-weight-bold">{{ activity.title }}</v-list-item-title>
                      </v-list-item-content>
                      <v-list-item-action>
                        <v-btn text
                               icon
                               color="primary"
                               class="mr-2 text-center"
                               @click.native="() => unlinkActivity(activity.id)"
                               :disabled="updatingActivity"
                        >
                          <v-icon>delete</v-icon>
                        </v-btn>
                      </v-list-item-action>
                      <v-list-item-action>
                        <v-btn text
                               icon
                               color="primary"
                               class="mr-2 text-center"
                               @click="editActivity(activity.id)"
                               :disabled="updatingActivity"
                        >
                          <v-icon>edit</v-icon>
                        </v-btn>
                      </v-list-item-action>
                    </v-list-item>
                  </v-list>
                </v-col>
                <v-col cols="12">
                  <v-row class="fluid">
                    <v-col cols="12" md="6">
                      <h2>Contextes associés
                        <span>
                          <v-chip class="ma-1" v-if="skill.contexts.length === 0 && !skill.noContext" color="info">A qualifier</v-chip>
                          <span v-else>({{ skill.contexts.length || 'Aucun' }})</span>
                        </span>
                        <span class="text-caption" v-if="updatingContext"><v-progress-circular indeterminate size="16"/>
                          Enregistrement en cours...
                        </span>
                      </h2>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12">
                      <v-checkbox
                        v-model="noContext"
                        @change="handleToggleNoContext"
                        :disabled="updatingContext"
                        label="Aucun contexte pour cette aptitude (plus aucun contexte ne sera associé)"
                      />
                    </v-col>
                  </v-row>
                  <v-row v-if="!noContext">
                    <v-col cols="12" md="4">
                      <autocomplete :value="skill.contexts"
                                    empty
                                    searchRestUrlApi="/api/odas/context/list"
                                    label="Chercher un contexte"
                                    @input="onContextAdded"
                                    close-on-select
                                    :disabled="updatingContext"
                                    no-filter
                      />
                    </v-col>
                    <v-col cols="12" md="4">
                      <v-btn @click.stop="showCreateContextModal = true">Créer un contexte</v-btn>
                    </v-col>
                  </v-row>
                  <v-list two-line>
                    <template v-for="(context, index) in skill.contexts">
                      <v-list-item :key="`c-${context.id}`">
                        <v-list-item-content>
                          <v-list-item-title class="font-weight-bold">{{ context.title }}</v-list-item-title>
                          <v-list-item-subtitle class="font-weight-bold">{{ context.categoryLevel.category.title }} - {{
                            context.categoryLevel.title }}
                          </v-list-item-subtitle>
                          <v-list-item-subtitle>
                            Dernière modification par {{context.lastModifiedBy ? context.lastModifiedBy : 'inconnu'}} ({{context.updatedDate | formatDateTime}})
                          </v-list-item-subtitle>
                        </v-list-item-content>
                        <v-list-item-action>
                          <v-btn text
                                 icon
                                 color="primary"
                                 class="mr-2 text-center"
                                 @click.native="() => unlinkContext(context.id)"
                                 :disabled="updatingContext"
                          >
                            <v-icon>delete</v-icon>
                          </v-btn>
                        </v-list-item-action>
                      </v-list-item>
                      <v-divider :key="`div-ctx-${index}`"/>
                    </template>
                  </v-list>
                </v-col>
                <v-col cols="12">
                  <v-row class="fluid">
                    <v-col cols="12" md="6">
                      <h2>Comportements associées
                        <span>
                          <v-chip class="ma-1" v-if="skill.behaviors.length === 0 && !skill.noBehavior" color="info">A qualifier</v-chip>
                          <span v-else>({{ skill.behaviors.length || 'Aucun' }})</span>
                        </span>
                        <span class="text-caption" v-if="updatingBehavior"><v-progress-circular indeterminate size="16"/>
                          Enregistrement en cours...
                        </span>
                      </h2>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12">
                      <v-checkbox
                        v-model="noBehavior"
                        @change="handleToggleNoBehavior"
                        :disabled="updatingBehavior"
                        label="Aucun comportement pour cette aptitude (plus aucun comportement ne sera associé)"
                      />
                    </v-col>
                  </v-row>
                  <v-row v-if="!noBehavior">
                    <v-col cols="12" md="4">
                      <autocomplete :value="skill.behaviors"
                                    searchRestUrlApi="/api/odas/behavior/list"
                                    label="Choisir des comportements"
                                    itemSubText="description"
                                    empty
                                    size="100"
                                    autocompleteOnFocus
                                    close-on-select
                                    :disabled="updatingBehavior"
                                    @input="onBehaviorAdded"
                      >
                        <template v-slot:listItem="slotProps">
                          <v-list-item-content>
                            <v-list-item-title>{{ slotProps.listItem.title }}</v-list-item-title>
                            <v-list-item-subtitle class="font-italic pl-1">
                              {{ $t(`behaviorCategory.${slotProps.listItem.behaviorCategory}`) }}
                            </v-list-item-subtitle>
                          </v-list-item-content>
                        </template>
                      </autocomplete>
                    </v-col>
                  </v-row>
                  <v-list>
                    <template v-for="(behavior, index) in skill.behaviors">
                      <v-list-item :key="`b-${behavior.id}`">
                        <v-list-item-content>
                          <v-list-item-title class="font-weight-bold">{{ behavior.title }}</v-list-item-title>
                          <v-list-item-subtitle class="font-weight-bold pl-1">
                            {{ $t(`behaviorCategory.${behavior.behaviorCategory}`) }}
                          </v-list-item-subtitle>
                        </v-list-item-content>
                        <v-list-item-action>
                          <v-btn text
                                 icon
                                 color="primary"
                                 class="mr-2 text-center"
                                 @click.native="() => unlinkBehavior(behavior.id)"
                                 :disabled="updatingBehavior"
                          >
                            <v-icon>delete</v-icon>
                          </v-btn>
                        </v-list-item-action>
                      </v-list-item>
                      <v-divider :key="`divb-${index}`"/>
                    </template>
                  </v-list>
                </v-col>
              </v-row>
              <v-divider />
            </div>
          </v-card-text>
          <v-card-actions v-if="nextSkill">
            <i>Pour le poste <router-link :to="{ name: 'erhgo_occupation_detail', params: {id: occupation.id}}">{{occupation.title}}</router-link>, aptitude suivante à qualifier&nbsp;: <b>{{nextSkill.title}}</b>.</i>
            <v-btn
              class="ml-3"
              outlined
              color="primary"
              :to="{
                    name: 'skill_detail',
                    params: { uri: nextSkill.uri },
                    query: {occupationId: occupation.id},
                  }"
            >
              Qualifier l'aptitude suivante
              <v-icon>chevron_right</v-icon>
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>
    <activity-edit-modal
      v-model="showCreateActivityModal"
      @closed="closeActivityModal"
      @submit="onActivityUpdated"
      :title="skill.title"
      :origin="origin"
      :edit-activity-id="editActivityId"
      :key="editActivityId"
      :activity-type="ActivityType.JOB"/>
    <context-create-modal v-model="showCreateContextModal" @closed="closeContextModal" @created="onContextCreated"
                          :origin="origin"/>
  </v-container>
</template>

<script>
import {ActivityType} from 'erhgo-api-client';
import ActivityListItem from '@/views/repository/activity/ActivityListItem.vue';
import Autocomplete from '@/components/common/crud/Autocomplete.vue';
import ActivityEditModal from '@/views/repository/activity/ActivityEditModal.vue';
import ContextCreateModal from '@/views/repository/context/ContextCreateModal.vue';
import EscoUtils from '@/components/mixins/EscoUtils';
import ErhgoOccupationService from '../erhgo/ErhgoOccupationService';

export default {
  mixins: [EscoUtils],
  components: {
    ActivityEditModal,
    ActivityListItem,
    Autocomplete,
    ContextCreateModal,
  },
  data() {
    return {
      ActivityType,
      selectedActivities: [],
      editActivityId: null,
      noActivity: null,
      noBehavior: null,
      noContext: null,
      description: '',
      error: null,
      loading: false,
      origin: 'ESCO',
      updatingActivity: false,
      updatingBehavior: false,
      updatingContext: false,
      updateDescriptionSuccessMessage: [],
      updatingDescription: false,
      showCreateActivityModal: false,
      showCreateContextModal: false,
      skill: {
        activities: [],
        behaviors: [],
        contexts: [],
      },
      occupationService: null,
      nextSkill: null,
      initialized: false,
    };
  },
  async mounted() {
    await this.fetchSkill();
    await this.fetchOccupation();
    this.nextSkill = this.nextSkillToQualify(this.skill, this.occupation);
    this.initialized = true;
  },
  computed: {
    uri() {
      return this.$route.params.uri;
    },
    occupationId() {
      return this.$route.query.occupationId;
    },
    occupation() {
      return this.occupationService?.occupation;
    },
    activitiesIds() {
      return this.skill.activities.map(a => a.id);
    },
  },
  watch: {
    $route: 'fetchSkill',
  },
  methods: {
    setData(skill) {
      this.skill = skill;
      if (skill.descriptionFR) {
        this.description = skill.descriptionFR;
      }
      this.noActivity = skill.noActivity;
      this.noBehavior = skill.noBehavior;
      this.noContext = skill.noContext;
    },
    async fetchSkill() {
      try {
        this.loading = true;
        this.error = null;
        const result = await this.$api.getSkill(this.uri);
        this.setData(result.data);
      } catch (e) {
        this.error = `Aucune aptitude trouvée pour l'uri ${this.uri}`;
      } finally {
        this.loading = false;
      }
    },
    async fetchOccupation() {
      if (this.occupationId) {
        this.occupationService = await (new ErhgoOccupationService(this.occupationId).fetchOccupation());
      }
    },
    async updateDescription() {
      if (this.description !== this.skill.descriptionFR) {
        try {
          this.updatingDescription = true;
          this.error = null;
          await this.$api.updateSkillDescription({
            uri: this.skill.uri,
            description: this.description,
          });
          this.updateDescriptionSuccessMessage = ['Enregistré'];
          setTimeout(() => {
            this.updateDescriptionSuccessMessage = [];
          }, 1000);
        } catch (e) {
          this.error = 'Une erreur est survenue.';
          // put initial value back
          this.description = this.skill.descriptionFR;
          this.logError(e);
        } finally {
          this.updatingDescription = false;
        }
      }
    },
    async handleToggleNoActivity(checked) {
      try {
        this.updatingActivity = true;
        this.error = null;
        await this.$api.setSkillNoActivity({
          uri: this.skill.uri,
          value: checked,
        });
        this.skill.noActivity = checked;
        if (checked) {
          this.skill.activities = [];
        }
      } catch (e) {
        this.error = 'Une erreur est survenue.';
        // put initial value back
        this.noActivity = !checked;
        this.logError(e);
      } finally {
        this.updatingActivity = false;
      }
    },
    async handleToggleNoBehavior(checked) {
      try {
        this.updatingBehavior = true;
        this.error = null;
        await this.$api.setSkillNoBehavior({
          uri: this.skill.uri,
          value: checked,
        });
        this.skill.noBehavior = checked;
        if (checked) {
          this.skill.behaviors = [];
        }
      } catch (e) {
        this.error = 'Une erreur est survenue.';
        // put initial value back
        this.noBehavior = !checked;
        this.logError(e);
      } finally {
        this.updatingBehavior = false;
      }
    },
    async handleToggleNoContext(checked) {
      try {
        this.updatingContext = true;
        this.error = null;
        await this.$api.setSkillNoContext({
          uri: this.skill.uri,
          value: checked,
        });
        this.skill.noContext = checked;
        if (checked) {
          this.skill.contexts = [];
        }
      } catch (e) {
        this.error = 'Une erreur est survenue.';
        // put initial value back
        this.noContext = !checked;
        this.logError(e);
      } finally {
        this.updatingContext = false;
      }
    },
    editActivity(activityId) {
      this.editActivityId = activityId;
      this.showCreateActivityModal = true;
    },
    async onActivityUpdated(activity) {
      this.editActivityId = null;
      this.closeActivityModal();
      await this.createOrUpdateActivityLink(activity);
    },
    async createOrUpdateActivityLink(activity) {
      try {
        const index = this.activitiesIds.indexOf(activity.id);
        if(index >= 0) {
          this.skill.activities[index] = activity;
        } else {
          this.updatingActivity = true;
          await this.$api.linkToActivity({uri: this.skill.uri, activityId: activity.id});
          this.skill.activities.unshift(activity);
        }
      } catch (e) {
        this.error = 'Une erreur est survenue.';
        this.logError(e);
      } finally {
        this.updatingActivity = false;
      }
    },
    async onActivityAdded(activityList) {
      activityList.forEach(this.createOrUpdateActivityLink);
    },
    async onBehaviorAdded(newList) {
      const currentBehviorIds = this.skill.behaviors.map(behavior => behavior.id);
      const addedBehavior = newList.filter(behavior => !currentBehviorIds.includes(behavior.id))[0];
      try {
        this.updatingBehavior = true;
        await this.$api.linkToBehavior({uri: this.skill.uri, behaviorId: addedBehavior.id});
        this.skill.behaviors.unshift(addedBehavior);
      } catch (e) {
        this.error = 'Une erreur est survenue.';
        this.logError(e);
      } finally {
        this.updatingBehavior = false;
      }
    },
    async onContextCreated(context) {
      try {
        this.closeContextModal();
        this.updatingContext = true;
        await this.$api.linkToContext({uri: this.skill.uri, contextId: context.id});
        this.skill.contexts.unshift(context);
      } catch (e) {
        this.error = 'Une erreur est survenue.';
        this.logError(e);
      } finally {
        this.updatingContext = false;
      }
    },
    async onContextAdded(newList) {
      const currentContextIds = this.skill.contexts.map(context => context.id);
      const addedContext = newList.filter(context => !currentContextIds.includes(context.id))[0];
      try {
        this.updatingContext = true;
        await this.$api.linkToContext({uri: this.skill.uri, contextId: addedContext.id});
        this.skill.contexts.unshift(addedContext);
      } catch (e) {
        this.error = 'Une erreur est survenue.';
        this.logError(e);
      } finally {
        this.updatingContext = false;
      }
    },
    closeActivityModal() {
      this.editActivityId = null;
      this.showCreateActivityModal = false;
    },
    closeContextModal() {
      this.showCreateContextModal = false;
    },
    async unlinkActivity(activityId) {
      try {
        this.selectedActivities = [];
        this.updatingActivity = true;
        await this.$api.deleteLinkToActivity({uri: this.skill.uri, activityId});
        this.skill.activities = this.skill.activities.filter(activity => activity.id !== activityId);
      } catch (e) {
        this.error = 'Une erreur est survenue.';
        this.logError(e);
      } finally {
        this.updatingActivity = false;
      }
    },
    async unlinkBehavior(behaviorId) {
      try {
        this.updatingBehavior = true;
        await this.$api.deleteLinkToBehavior({uri: this.skill.uri, behaviorId});
        this.skill.behaviors = this.skill.behaviors.filter(behavior => behavior.id !== behaviorId);
      } catch (e) {
        this.error = 'Une erreur est survenue.';
        this.logError(e);
      } finally {
        this.updatingBehavior = false;
      }
    },
    async unlinkContext(contextId) {
      try {
        this.updatingContext = true;
        await this.$api.deleteLinkToContext({uri: this.skill.uri, contextId});
        this.skill.contexts = this.skill.contexts.filter(context => context.id !== contextId);
      } catch (e) {
        this.error = 'Une erreur est survenue.';
        this.logError(e);
      } finally {
        this.updatingContext = false;
      }
    },
  },
};
</script>
