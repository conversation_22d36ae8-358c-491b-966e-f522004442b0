<template>
  <v-container fluid>
    <v-row v-if="!occupation" justify="center">
      <v-progress-circular indeterminate/>
    </v-row>
    <v-row v-else>
      <v-col cols="12">
        <v-card>
          <v-card-title class="d-flex flex-column align-start">
            <h1 class="d-flex align-center">
              <v-icon class="mr-2" large color="primary">mdi-web</v-icon>
              Métier ESCO : {{ occupation.title }}
            </h1>
          </v-card-title>
          <v-card-text>
            <v-alert
              type="error"
              v-if="error || (occupationService && occupationService.showAPIError)"
              :value="true"
            >
              {{ error || 'Une erreur technique est survenue'}}
            </v-alert>
            <v-progress-circular v-if="loading"/>
            <div v-if="!loading && !error">
              <v-row>
                <v-col cols="12">
                  <v-expansion-panels>
                    <v-expansion-panel>
                      <v-expansion-panel-header>
                        <h2 id="detailTitle">Détails</h2>
                      </v-expansion-panel-header>
                      <v-expansion-panel-content>
                        <v-row>
                          <v-col cols="12">
                            <v-text-field id="occupationUri" :value="occupation.iscoOccupation.iscoGroup" label="Code ISCO" readonly/>
                          </v-col>
                          <v-col cols="12">
                            <v-textarea v-model="occupation.descriptionFR"
                                        label="Description (français)"
                                        rows="3"
                                        readonly
                                        id="occupationDescriptionFR"
                            />
                          </v-col>
                          <v-col cols="12">
                            <v-textarea v-model="occupation.descriptionEN"
                                        label="Description (anglais)"
                                        rows="3"
                                        readonly
                                        id="occupationDescriptionEN"
                            />
                          </v-col>
                          <v-col cols="12">
                            <v-text-field
                              :value="(occupation.alternativeLabels && occupation.alternativeLabels.length > 0) ? occupation.alternativeLabels.join(', ') : 'Aucun'"
                              label="Labels alternatifs" readonly/>
                          </v-col>
                          <v-col cols="6">
                            <v-text-field :value="`${computeContextScore} (${computeContextMasteryLevel})`"
                                          label="Score (et niveau de maîtrise correspondant) relatif aux contextes issus de la qualification"
                                          readonly/>
                          </v-col>
                        </v-row>
                        <v-row v-if="!hasAnySkillToQualify">
                          <ul>
                            <li>Activités
                              <ul>
                                <li v-for="activity in activities" :key="activity.id">{{ activity.title }}</li>
                              </ul>
                            </li>
                            <li>Contextes
                              <ul>
                                <li v-for="context in contexts" :key="context.id">{{ context.title }}</li>
                              </ul>
                            </li>
                            <li id="behaviorList">Comportements
                              <ul>
                                <li v-for="behavior in behaviors" :key="behavior.id">{{ behavior.title }}</li>
                              </ul>
                            </li>
                          </ul>
                        </v-row>
                      </v-expansion-panel-content>
                    </v-expansion-panel>
                  </v-expansion-panels>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12">
                  <v-expansion-panels :value="0">
                    <v-expansion-panel>
                      <v-expansion-panel-header>
                        <h2 id="skillTitle">Aptitudes</h2>
                      </v-expansion-panel-header>
                      <v-expansion-panel-content>
                        <v-list id="skills" three-line v-if="occupation.skills">
                          <template v-for="(skill, index) in occupation.skills">
                            <v-list-item :key="skill.uri">
                              <v-list-item-content>
                                <v-list-item-title class="d-flex align-center">
                                  <v-icon class="mr-2" color="primary">arrow_forward</v-icon>
                                  <span class="font-weight-bold">{{ skill.title }}</span>
                                </v-list-item-title>
                                <v-list-item-subtitle>
                                  <div v-if="isSkillToQualify(skill)">
                                    <v-chip class="ma-1" color="info">A qualifier</v-chip>
                                    <v-chip class="ma-1" color="warning" v-if="!skill.activities.length && !skill.noActivity">
                                      Activité à qualifier
                                    </v-chip>
                                  </div>
                                  <div v-else>
                                    Activité&nbsp;: {{ skill.activities.length }} |
                                    Contextes&nbsp;: {{ skill.contexts.length }} |
                                    Comportements&nbsp;: {{ skill.behaviors.length }}
                                  </div>
                                </v-list-item-subtitle>
                                <v-list-item-subtitle v-if="!isSkillToQualify(skill)" class="text-caption">
                                  Modifié par {{skill.lastModifiedBy ? skill.lastModifiedBy : 'inconnu'}} le
                                  {{skill.updatedDate | formatDateTime}}
                                </v-list-item-subtitle>
                              </v-list-item-content>
                            </v-list-item>
                            <v-divider
                              v-if="index + 1 < occupation.skills.length"
                              :key="index"
                              :inset="false"
                            />
                          </template>
                        </v-list>
                      </v-expansion-panel-content>
                    </v-expansion-panel>
                  </v-expansion-panels>
                </v-col>
              </v-row>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import EscoUtils from '@/components/mixins/EscoUtils';
import EscoOccupationService from './EscoOccupationService';
import ProcessReview from '../../../components/mixins/ProcessReview';
import {MasteryLevel} from 'erhgo-api-client';

export default {
  mixins: [EscoUtils, ProcessReview],
  data() {
    return {
      error: null,
      occupationService: null,
      loading: false,
      MasteryLevel,
    };
  },
  computed: {
    occupation() {
      return this.occupationService?.occupation;
    },
    uri() {
      return this.$route.params.uri;
    },
    hasAnySkillToQualify() {
      return this.isOccupationToQualify(this.occupation);
    },
    activities() {
      return this.occupation.skills.reduce((previousList, skill) => {
        skill.activities.forEach((currentActivity) => {
          if (!previousList.some(activity => activity.id === currentActivity.id)) {
            previousList.push(currentActivity);
          }
        });
        return previousList;
      }, []);
    },
    contexts() {
      return this.occupation.skills.reduce((previousList, skill) => {
        skill.contexts.forEach((currentContext) => {
          if (!previousList.some(context => context.id === currentContext.id)) {
            previousList.push(currentContext);
          }
        });
        return previousList;
      }, []);
    },
    behaviors() {
      return this.occupation.skills.reduce((previousList, skill) => {
        skill.behaviors.forEach((currentBehavior) => {
          if (!previousList.some(behavior => behavior.id === currentBehavior.id)) {
            previousList.push(currentBehavior);
          }
        });
        return previousList;
      }, []);
    },
    jobsMasteryLevels() {
      return Object.values(this.MasteryLevel);
    },
    computeContextScore() {
      return this.getScore(this.occupation.skills.flatMap(skill => skill.contexts));
    },
    computeContextMasteryLevel() {
      const computedScore = this.computeMasteryLevel(this.getLevelFromScore(this.computeContextScore));
      const masteredLabels = Object.values(this.$t('masteryLevels'));
      return computedScore + ` (${masteredLabels[computedScore - 1].labelM})`;
    },
  },
  async mounted() {
    this.occupationService = new EscoOccupationService(this.uri);
    await this.getOccupation();
  },
  methods: {
    computeMasteryLevel(jobMasteryLevel) {
      return this.jobsMasteryLevels.indexOf(jobMasteryLevel) + 1;
    },
    async getOccupation() {
      try {
        this.loading = true;
        this.error = null;
        await this.occupationService.fetchOccupation();
      } catch (e) {
        this.error = `Aucune occupation trouvée pour l'uri ${this.uri}`;
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>
