import SafeService from 'odas-plugins/SafeService';
import Vue from 'vue';
import { EscoOccupationPage } from 'erhgo-api-client';

export default class EscoOccupationsService extends SafeService {

  private _occupationPage: EscoOccupationPage | null = null;

  async fetchOccupationPage(size: number, page: number, query: string) {
    await this.safeCall(async () => {
      this._occupationPage = (await Vue.$api.escoOccupationPage(size, page, query)).data;
    });
  }

  get occupationPage(): EscoOccupationPage | null {
    return this._occupationPage;
  }
}
