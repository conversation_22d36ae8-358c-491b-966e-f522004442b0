<template>
  <v-row no-gutters>
    <v-col cols="12">
      <v-autocomplete
        id="erhgo-search"
        return-object
        hide-no-data
        :loading="isLoading"
        v-model="capacities.erhgoJob"
        :items="erhgoItems"
        small-chips
        prepend-icon="search"
        deletable-chips
        :search-input.sync="capacities.erhgoQuery"
        hide-selected
        hide-details
        item-text="title"
        label="Rechercher un métier ERHGO"
        no-filter
      />
    </v-col>
    <v-col cols="12">
      <v-autocomplete
        return-object
        hide-no-data
        :loading="isLoading"
        v-model="capacities.escoJob"
        :items="escoItems"
        small-chips
        deletable-chips
        :search-input.sync="capacities.escoQuery"
        hide-selected
        hide-details
        prepend-icon="search"
        item-text="title"
        label="Rechercher un métier ESCO"
        no-filter
      />
    </v-col>
    <v-col cols="12">
      <v-autocomplete
        id="userSearch"
        return-object
        hide-no-data
        :loading="isLoading"
        v-model="capacities.user"
        :items="userItems"
        small-chips
        deletable-chips
        :search-input.sync="capacities.userQuery"
        hide-selected
        hide-details
        prepend-icon="search"
        label="Rechercher un individu"
        :item-text="userItemText"
        no-filter
      />
    </v-col>
    <v-col cols="12">
      <v-dialog v-model="jobPicker.showDialog" width="800">
        <template v-slot:activator="{ on }">
          <v-btn
            color="primary"
            class="my-4"
            v-on="on"
            link>
            <v-icon class="mr-2">
              mdi-briefcase-outline
            </v-icon>
            Utiliser un poste ou un profil de recrutement
          </v-btn>
        </template>
        <job-picker-view :data="jobPicker"
                         @submit="(data) => capacities.capacitiesHolderChooser = data"/>
      </v-dialog>
    </v-col>
    <v-col cols="12" class="pa-0">
      <v-alert :value="(capacities.erhgoJob || capacities.escoJob) && capacities.fullyQualified === false"
               outlined
               dense
               dismissible
               color="error"
               icon="warning">
        Ce métier n'est pas totalement qualifié
      </v-alert>
    </v-col>
    <v-col cols="12" class="mb-2">
      <i class="d-block">{{capacities.holderDescriptor}}</i>
      <i class="d-block" v-if="capacities.user && userMasteryLevel">{{`Niveau de maîtrise : ${userMasteryLevel}`}}</i>
    </v-col>
    <v-col cols="12">
      <v-data-table
        :headers="capacities.headers"
        :items="capacities.capacitiesData"
        :sort-by.sync="capacities.pagination.sortBy"
        :page.sync="capacities.pagination.page"
        :sort-desc.sync="capacities.pagination.descending"
        :items-per-page.sync="capacities.pagination.rowsPerPage"
        :footer-props="{
            'items-per-page-options': [100],
          }"
        item-key="code"
        class="elevation-1"
        no-data-text="Aucune capacité associée"
        v-if="capacities.hasCapacitiesHolder()"
        :loading="capacities.loading"
      >
        <template v-slot:item="props">
          <tr class="compare-row">
            <td class="text-left lighten-2"
                :class="{'green': props.item.commonCapacity && !props.item.lowerLevel,
                           'orange':  props.item.commonCapacity && props.item.lowerLevel,
                           'red': props.item.commonCapacity === false}">
              {{ props.item.title }}
            </td>
            <td class="text-center">{{ props.item.code }}</td>
            <td class="text-center">{{ props.item.masteryLevel }}</td>
          </tr>
        </template>
        <template v-slot:footer.page-text="props">
          Lignes de {{ props.pageStart }} à {{ props.pageStop }} sur {{ props.itemsLength }}
        </template>
      </v-data-table>
    </v-col>
  </v-row>
</template>

<script>
import CompareCapacities from './CompareCapacities';
import JobPickerView from '@/components/organization/JobPickerView';
import JobPicker from '@/components/organization/JobPicker';

export default {
  components: {
    JobPickerView,
  },
  props: {
    capacities: {
      type: CompareCapacities,
      required: true,
    },
  },
  data: () => ({
    entries: [],
    isLoading: false,
    search: null,
    jobPicker: new JobPicker(),
  }),
  computed: {
    escoItems() {
      return this.capacities.escoItems;
    },
    erhgoItems() {
      return this.capacities.erhgoItems;
    },
    userItems() {
      return this.capacities.userItems;
    },
    userMasteryLevel() {
      return this.capacities.userMasteryLevel;
    },
  },
  methods: {
    userItemText: item => `${item.firstName || ''} ${item.lastName || ''}`,
  },
};
</script>
<style>
.compare-row .red.lighten-2 {
  border-color: inherit !important;
}
</style>
