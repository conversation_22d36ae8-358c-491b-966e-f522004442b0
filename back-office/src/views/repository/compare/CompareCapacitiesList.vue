<template>
  <v-container fluid>
    <v-row no-gutters align="center">
      <v-col cols="6">
        <h1 class="d-flex align-center">
          <v-icon x-large color="primary" class="mr-2">compare_arrows</v-icon>
          Comparaison de métiers
        </h1>
      </v-col>
      <v-col class="d-flex justify-end" cols="6">
        <v-tooltip color="black" left>
          <template v-slot:activator="{ on }">
            <span class="d-flex align-center text-caption" v-on="on">
              <v-icon color="primary" class="mr-2">
                info
              </v-icon>
              Code couleur des capacités
            </span>
          </template>
          <ul>
            <li><span class="red">Rouge</span>&nbsp;: Capacité absente de l'autre élément comparé</li>
            <li><span class="orange">Orange</span>&nbsp;: Capacité présente dans l'autre élément comparé, à un niveau de maîtrise supérieur</li>
            <li><span class="green">Vert</span>&nbsp;: Capacité présente dans l'autre élément comparé à un niveau de maîtrise inférieur ou égal</li>
          </ul>
        </v-tooltip>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="6" class="border-right">
        <esco-capacities id="left-compare" v-if="capacities1" :capacities="capacities1" ref="cap1"/>
      </v-col>
      <v-col cols="6">
        <esco-capacities id="right-compare" v-if="capacities2" :capacities="capacities2" ref="cap2"/>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import EscoCapacities from '@/views/repository/compare/CompareCapacitiesItem.vue';
import CompareCapacities from './CompareCapacities';

export default {
  components: {
    EscoCapacities,
  },
  data: () => {
    return {
      capacities1: null,
      capacities2: null,
    };
  },
  created() {
    this.capacities1 = new CompareCapacities();
    this.capacities2 = new CompareCapacities();
    this.capacities1.other = this.capacities2;
    this.capacities2.other = this.capacities1;

    // TODO: forceUpdate is required to view color changes on `commonCapacity` flag update
    this.capacities2.onUpdate = () => {
      this.$refs.cap1.$forceUpdate();
    };
    this.capacities1.onUpdate = () => {
      this.$refs.cap2.$forceUpdate();
    };
  },
};
</script>

<style lang="scss" scoped>

@import '@/style/colors.scss';

.border-right {
  border-right: $mdi-primary 1px solid;
}
</style>
