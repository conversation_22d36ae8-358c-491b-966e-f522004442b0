<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <list-card :headers="headers"
                   id="behaviorList"
                   :speedDial="false"
                   listRestUrlApi="/api/odas/behavior/list">
          <template slot="title">
            <h1>
              <v-icon large
                      color="primary">mdi-human-greeting
              </v-icon>
              {{ $t('ref.behavior.list') }}
            </h1>
          </template>

          <template v-slot:row="{row}">
            <td>{{ row.code }}</td>
            <td class="text-left">{{ row.title }}</td>
            <td class="text-left">{{ $t(`behaviorCategory.${row.behaviorCategory}`) }}</td>
          </template>

          <template v-slot:details="{item}">
            <v-col cols="12">
              <b>{{$t('ref.headers.context.description')}}&nbsp;:</b>
              {{ item.description }}
            </v-col>
          </template>
        </list-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import ListCard from '@/components/common/crud/List.vue';

export default {
  components: {ListCard},
  data() {
    return {
      headers: [
        {
          text: this.$t('ref.headers.behavior.code'),
          icon: '',
          align: 'left',
          sortable: true,
          value: 'code',
        },
        {
          text: this.$t('ref.headers.behavior.title'),
          icon: '',
          align: 'left',
          sortable: true,
          value: 'title',
        },
        {
          text: this.$t('ref.headers.behavior.category'),
          icon: '',
          align: 'left',
          sortable: true,
          value: 'behaviorCategory',
        },
      ],
    };
  },
};
</script>
