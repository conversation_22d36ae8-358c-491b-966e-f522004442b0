<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <list-card :headers="headers"
                   :actions="actions"
                   :userId="userId"
                   id="contextList"
                   listRestUrlApi="/api/odas/context/list"
                   countRestUrlApi="/api/odas/context/count">
          <template slot="title">
            <h1>
              <v-icon large
                      color="primary">mdi-factory
              </v-icon>
              {{ $t('ref.context.list') }}
            </h1>
          </template>

          <template slot="otherFilters">
            <v-col offset-md="8">
              <v-select :items="users"
                        clearable
                        v-model="userId"
                        placeholder="Filtrer par administrateur"
                        :menu-props="{ offsetY: true }"
                        :item-text="backOfficeUserService.getUserName"
                        item-value="id"
                        hide-details />
            </v-col>
          </template>

          <template v-slot:row="{row}">
            <td>{{ row.code }}</td>
            <td class="text-left">{{ row.title }}</td>
            <td class="text-left">{{ row.categoryLevel.category.code }}</td>
            <td class="text-left">
              <span>{{ `Modifié par ${row.lastModifiedBy ? row.lastModifiedBy : 'inconnu' }` }}</span>
            </td>
            <td class="text-left">
              <span>Modifié le {{ row.updatedDate | formatDateTime }}</span>
            </td>
            <td class="text-right">
              <v-spacer/>
              <v-btn text
                     icon
                     color="primary"
                     class="mr-2 text-center"
                     :to="{ name: 'contexts_edit', params: {contextId: row.id}}">
                <v-icon>edit</v-icon>
              </v-btn>
            </td>
          </template>

          <template v-slot:details="{item}">
            <v-col cols="6">
              <b>{{$t('ref.headers.context.description')}}:</b>
              {{ item.description }}
            </v-col>
            <v-col cols="6">
              <b>{{$t('ref.headers.context.category')}}:</b>
              {{ item.categoryLevel.category.code }} - {{ item.categoryLevel.category.title }}
            </v-col>
            <v-col cols="6">
              <b>{{$t('ref.headers.contextLevel.category')}}:</b>
              {{ item.categoryLevel.title }} - {{ item.categoryLevel.description }}
            </v-col>
          </template>
        </list-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import ListCard from '@/components/common/crud/List.vue';
import BackOfficeUserService from '../../../components/services/BackOfficeUserService';

export default {
  components: {ListCard},
  data() {
    return {
      backOfficeUserService: BackOfficeUserService,
      userId: null,
      headers: [
        {
          text: this.$t('ref.headers.context.code'),
          icon: '',
          align: 'left',
          sortable: true,
          value: 'id',
        },
        {
          text: this.$t('ref.headers.context.title'),
          icon: '',
          align: 'left',
          sortable: true,
          value: 'title',
        },
        {
          text: this.$t('ref.headers.context.categoryCode'),
          icon: '',
          align: 'left',
          sortable: true,
          value: 'categoryLevel.category.code',
        },
        {
          text: this.$t('ref.headers.who'),
          icon: '',
          align: 'left',
          sortable: false,
        },
        {
          text: this.$t('ref.headers.when'),
          icon: '',
          align: 'left',
          sortable: false,
        },
        {
          text: this.$t('ref.headers.actions'),
          align: 'right',
          icon: '',
          sortable: false,
        },
      ],
      actions: [
        {
          component: 'contexts_create',
          icon: 'mdi-plus',
          color: 'indigo',
          label: 'action.create',
          primary: true,
        },
      ],
    };
  },
  async created() {
    this.backOfficeUserService = new BackOfficeUserService();
    await this.backOfficeUserService.fetchBackOfficeUserList();
  },
  computed: {
    users() {
      return this.backOfficeUserService.userList;
    },
  },
};
</script>
<style lang="scss">
@import '../../../style/colors.scss';

.v-datatable thead th.column {
  background-color: $mdi-primary;
  color: #ffffff;
  font-weight: bolder;
}

th i::before {
  color: #ffffff;
}

th.active, th::selection, th.active i, th:hover, th i:hover {
  color: #B5995C !important;
  font-weight: bolder;
}
</style>
