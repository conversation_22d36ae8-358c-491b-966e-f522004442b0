<template>
  <v-dialog v-model="show" scrollable>
    <context-form @created="onContextCreated"
                  :on-back="close"
                  :origin="origin"
                  :initial-title="initialTitle"/>
  </v-dialog>
</template>

<script>
import Form from './Form.vue';

export default {
  components: {ContextForm: Form},
  props: {
    value: {
      type: Boolean,
      required: true,
    },
    origin: String,
    initialTitle: {
      title: String,
      required: false,
    },
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(value) {
        if (!value) {
          this.close();
        }
      },
    },
  },
  methods: {
    onContextCreated(context) {
      this.$emit('created', context);
    },
    close() {
      this.$emit('closed');
    },
  },
};
</script>

