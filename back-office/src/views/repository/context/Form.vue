<template>
  <v-container fluid class="pa-0">
    <v-form ref="form">
      <v-card>
        <v-card-title>
          <h2>
            <v-icon large
                    right>mdi-briefcase-outline
            </v-icon>
            {{ !contextId ? $t('ref.context.create') : $t('ref.context.edit') }}
          </h2>
        </v-card-title>
        <v-card-text>
          <v-row class="fill-height">
            <v-col cols="12">
              <v-text-field v-model="title"
                            :label="$t('form.context.title')"
                            :counter="80"
                            id="contextTitle_textInput"
                            :rules="[v => (!!v && v.trim().length > 0) || $t('form.context.message.title.required'), v => (!v || v.length <= 80) || this.$t('form.global.maxLength', { maxLength: 80 })]"/>
              <v-textarea v-model="description"
                          :label="$t('form.context.description')"
                          :counter="2000"
                          id="contextDescription_textArea"
                          :rules="[v => (!!v && v.trim().length > 0) || $t('form.context.message.description.required'), v => v.length <= 2000 || 'Texte trop long']"/>
              <v-select :items="categories"
                        v-model="category"
                        :item-text="formatCategoryLabel"
                        return-object
                        id="contextCategory_select"
                        menu-props="auto"
                        :label="$t('form.context.category')"
                        :rules="[v => !!v || $t('form.context.message.category.required')]"/>
              <v-select :disabled="!category"
                        :items="categoryLevels"
                        v-model="categoryLevel"
                        :item-text="formatCategoryLevelLabel"
                        return-object
                        id="contextCategoryLevel_select"
                        menu-props="auto"
                        :label="$t('form.context.categoryLevel')"
                        :rules="[v => !!v || $t('form.context.message.categoryLevel.required')]"/>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-btn id="backToContextListButton"
                 color="grey lighten-5"
                 large
                 @click.stop="onBack ? onBack() : $router.push({ name: 'contexts_repository'})">
            <v-icon small
                    left>undo
            </v-icon>
            {{$t('action.return')}}
          </v-btn>
          <v-spacer/>
          <v-btn id="saveContextButton"
                 width="50%"
                 large
                 :color="submitOk ? 'success' : 'primary'"
                 @click="submit"
                 :loading="loading">
            {{ submitButtonText }} &nbsp;
            <v-icon large
                    right>{{submitOk ? 'mdi-check-outline' : 'save'}}
            </v-icon>
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-form>
    <v-dialog v-model="showDialog" :max-width="500">
      <v-card>
        <v-card-title
          class="text-h5 grey lighten-2"
          primary-title
        >Attention
          <v-spacer/>
          <v-icon color="warning">mdi-warning</v-icon>
        </v-card-title>
        <v-card-text>
          Vous allez modifier le niveau du contexte
          <template v-if="categoryChanged"> et sa catégorie</template>
          <br/>
          Pour les recrutements actuellement ouverts disposant de ce contexte&nbsp;:
          <ul>
            <li>Le matching des candidatures existantes ne sera pas impacté (pas de recalcul)</li>
            <li>Le matching des futures candidatures s'appuiera sur la nouvelle valeur</li>
            <li v-if="categoryChanged">Concernant la cartographie de poste&nbsp;: dans les missions, les contextes
              seront redispatchés vers la nouvelle catégorie
            </li>
          </ul>
          <br/>
          <b>Cette action est irréversible.</b>
        </v-card-text>
        <v-divider/>
        <v-card-actions>
          <v-btn
            color="error"
            @click="showDialog = false"
          >
            Annuler
          </v-btn>
          <v-spacer/>
          <v-btn
            color="success"
            @click="showDialog = false;doUpdate()"
          >
            Continuer
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
export default {
  props: {
    contextId: {
      type: String,
      required: false,
      default: null,
    },
    onBack: Function,
    origin: String,
    initialTitle: {
      title: String,
      required: false,
    },
  },
  data() {
    return {
      title: '',
      description: '',
      category: null,
      categoryLevel: null,
      categories: [],
      rawContext: null,

      loading: true,
      submitOk: false,

      showDialog: false,
    };
  },
  async created() {
    try {
      this.title = this.initialTitle;
      this.loading = true;
      await Promise.all([this.fetchCategories(), this.fetchContext()]);
      if (this.rawContext) {
        this.title = this.rawContext.title;
        this.description = this.rawContext.description;
        this.category = this.categories.find(category => category.id === this.rawContext.categoryLevel.category.id);
        this.categoryLevel = this.categoryLevels.find(categoryLevel => categoryLevel.id === this.rawContext.categoryLevel.id);
      }
    } finally {
      this.loading = false;
    }
  },
  watch: {
    initialTitle() {
      this.title = this.initialTitle;
    },
    category(newValue, oldValue) {
      if (oldValue && newValue !== oldValue) {
        this.categoryLevel = null;
      }
    },
  },
  computed: {
    levelChanged() {
      return this.rawContext && this.categoryLevel && this.rawContext.categoryLevel.id !== this.categoryLevel.id;
    },
    categoryChanged() {
      return this.rawContext && this.category && this.rawContext.categoryLevel.category.id !== this.category.id;
    },
    categoryLevels() {
      if (this.category === null) {
        return [];
      }

      return this.category.levels;
    },
    submitButtonText() {
      let key = `action.${!this.contextId ? 'create' : 'update'}`;
      if (this.submitOk) {
        key += 'd';
      }
      return this.$t(key);
    },
    context() {
      const categoryLevelId = this.categoryLevel.id;
      return {
        title: this.title,
        description: this.description,
        categoryLevelId,
        origin: this.origin,
      };
    },
  },
  methods: {
    async fetchContext() {
      if (this.contextId) {
        this.rawContext = (await this.$api.getContext(this.contextId)).data;
      }
    },
    async fetchCategories() {
      this.categories = (await this.$axios.get('/api/odas/category/list', {
        params: {
          page: 0,
          size: 100,
          by: 'code',
          direction: 'ASC',
        },
      })).data.content;
    },
    async submit() {
      try {
        const result = await this.$refs.form.validate();
        if (result) {
          if (!this.contextId) {
            this.loading = true;
            try {
              const response = await this.$api.createNewContext(this.context);
              this.$emit('created', response.data);
              this.acknowledge();
            } finally {
              this.loading = false;
            }
          } else {
            if (this.levelChanged) {
              this.showDialog = true;
            } else {
              return await this.doUpdate();
            }
          }
        }
      } finally {
        this.loading = false;
      }
    },
    acknowledge() {
      this.submitOk = true;
      setTimeout(() => this.clear(), 2000);
    },
    async doUpdate() {
      this.loading = true;
      try {
        await this.$api.updateContext(this.contextId, this.context);
        this.acknowledge();
      } finally {
        this.loading = false;
      }
    },
    async clear() {
      if (!this.contextId) {
        this.$refs.form.reset();
        this.title = '';
        this.description = '';
        this.category = null;
        this.categoryLevel = null;
      }
      this.submitOk = false;
    },
    formatCategoryLabel(category) {
      return `${category.code} - ${category.title}`;
    },
    formatCategoryLevelLabel(categoryLevel) {
      return categoryLevel.title + (categoryLevel.description.trim().length ? ` : ${categoryLevel.description}` : '');
    },
  },
};
</script>
