<template>
  <v-container fluid class="pa-0">
    <v-card>
      <v-form v-if="question" ref="form" @submit.prevent="submit">
        <v-card-title>
          <h2>{{ $t(`ref.question.${!questionForContextId ? 'create' : 'edit'}`) }}</h2>
        </v-card-title>
        <v-card-text>
          <v-row>
            <v-col>
              <h3>Titre</h3>
              <v-text-field
                :rules="[rules.required, rules.max]"
                v-model="question.title"/>
            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <h3>Contextes</h3>
              <span v-if="!minContexts && submitted" class="text-caption red--text">
                Renseigner minimum un contexte
              </span>
              <v-alert color="error" outlined v-model="showContextNotDeletableWarning" dismissible>
                Le contexte ne peut être supprimé, il est utilisé dans une question portant sur les contextes dans le{{isPlural ? 's' : ''}} profil{{isPlural ? 's' : ''}} de recrutement{{isPlural ? 's' : ''}} suivant{{isPlural ? 's' : ''}}&nbsp;:
                <ul>
                  <li v-for="rp in questionService.recruitmentProfiles" :key="rp.id">
                    {{rp.title}}
                  </li>
                </ul>
              </v-alert>
              <autocomplete v-model="questionService.contexts"
                            searchRestUrlApi="/api/odas/context/list"
                            label="Chercher un contexte"
                            empty
              />
              <template v-for="c in questionService.contexts">
                <v-list-item :key="c.code">
                  <v-list-item-content>
                    <v-list-item-title>{{c.title}}</v-list-item-title>
                  </v-list-item-content>
                  <v-list-item-action>
                    <v-btn v-if="!context || c.id !== context.id"
                           icon
                           color="primary"
                           class="mr-2 text-center"
                           @click.native="removeContext(c.id)"
                    >
                      <v-icon>delete</v-icon>
                    </v-btn>
                  </v-list-item-action>
                </v-list-item>
                <v-divider :key="`div-${c.id}`"/>
              </template>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12">
              <h3>Les réponses</h3>
            </v-col>
            <v-col cols="10" offset="1">
              <v-alert
                border="left"
                color="primary"
                outlined
              >
                Vous pouvez renseigner jusqu'à 4 réponses : seules deux sont obligatoires (au moins une pour les potentiels
                "Aucun" et "Faible" et au moins une pour les potentiels "Moyen" et "Haut")
              </v-alert>
            </v-col>
          </v-row>
          <v-row v-for="(_, index) in question.suggestedAnswers" :key="index">
            <v-col offset="1" cols="9">
              <h4>{{ $t(`ref.answer.${index}`) }}</h4>
              <v-text-field
                :rules="[rules.max]"
                v-model="question.suggestedAnswers[index]"/>
            </v-col>
          </v-row>
          <v-alert icon="error" color="error" outlined :value="minimalQuestionAnswerError">
            Renseigner au moins deux réponses
          </v-alert>
        </v-card-text>
        <v-card-actions>
          <v-btn large
                 v-if="onBack"
                 color="primary"
                 @click="onBack"
                 outlined>
            Retour
          </v-btn>
          <v-spacer/>
          <v-btn large
                 :color="success ? 'success' : 'primary'"
                 type="submit"
                 id="saveActivityButton"
                 :loading="questionService.loading">
            <span class="mr-2">
               {{$t(`action.${ !questionForContextId ? 'create' : 'update'}${success ? 'd' : ''}`)}}
            </span>
            <v-icon large
                    right>
              save
            </v-icon>
          </v-btn>
        </v-card-actions>
      </v-form>
      <div v-else class="text-center">
        <v-progress-circular
          class="my-2"
          :size="50"
          color="primary"
          indeterminate
        />
      </div>
    </v-card>
    <errorDialog v-model="questionService.showAPIError"/>
  </v-container>
</template>

<script>
import QuestionForContextsEditService from './QuestionForContextsEditService';
import Autocomplete from '@/components/common/crud/Autocomplete.vue';
import ErrorDialog from '@/components/common/ErrorDialog.vue';
import {Context} from 'erhgo-api-client';

export default {
  name: 'Edit',
  props: {
    questionForContextId: String,
    onBack: Function,
    context: Context,
  },
  components: {
    ErrorDialog,
    Autocomplete,
  },
  data() {
    return {
      questionService: null,
      showContextNotDeletableWarning: false,
      submitted: false,
      success: false,
      rules: {
        required: value => !!value || 'Requis',
        max: value => !value || value.length <= 255 || '255 caractères max',
      },
    };
  },
  async created() {
    this.questionService = new QuestionForContextsEditService(this.questionForContextId);
    if (this.context) {
      this.questionService.contexts.push(this.context);
    }
    await this.questionService.fetchQuestionDetails();
  },
  methods: {
    async submit() {
      this.submitted = true;
      if (this.$refs.form.validate() &&
        this.minContexts &&
        !this.minimalQuestionAnswerError &&
        !this.success) {

        await this.questionService.saveQuestionForContexts();
        if (!this.questionService.showAPIError) {
          this.$emit('submit', this.questionService.questionDetails.id);
          if (!this.questionForContextId) {
            this.questionService.clear();
            this.$refs.form.reset();
            this.submitted = false;
          }
          this.success = true;
          setTimeout(() => this.success = false, 3000);
        }
      }
    },
    async removeContext(contextId) {
      if(this.questionForContextId) {
        await this.questionService.fetchRecruitmentProfilesByContextQuestionContextId(contextId);
        this.showContextNotDeletableWarning = this.questionService.recruitmentProfiles.length !== 0;
      }
      if (!this.showContextNotDeletableWarning) {
        this.questionService.contexts = this.questionService.contexts.filter(b => b.id !== contextId);
      }
    },
  },
  computed: {
    isPlural() {
      return this.questionService.recruitmentProfiles.length > 1;
    },
    minContexts() {
      return this.questionService.contexts.length > 0;
    },
    question() {
      return this.questionService.questionDetails;
    },
    minimalQuestionAnswerError() {
      return this.submitted && ((!this.question.suggestedAnswers.none && !this.question.suggestedAnswers.low) ||
        (!this.question.suggestedAnswers.medium && !this.question.suggestedAnswers.high));
    },
  },
};
</script>
