import {Context, QuestionForContextsDetails, RecruitmentProfileSummary} from 'erhgo-api-client';
import Vue from 'vue';
import SafeService from 'odas-plugins/SafeService';
import {v4 as uuid} from 'uuid';

export default class QuestionForContextsEditService extends SafeService {

  private _questionDetails: QuestionForContextsDetails | null = null;
  private _contexts: Context[] = [];
  private _recruitmentProfiles: RecruitmentProfileSummary[] = [];

  constructor(private _questionId: string) {
    super();
  }

  async saveQuestionForContexts() {
    await this.safeCall(async () => {
      const contexts = this._contexts ? this._contexts.map(c => c.id) : [];
      const command = {
        id: this._questionDetails!.id,
        title: this._questionDetails!.title,
        contexts,
        suggestedAnswers: this._questionDetails!.suggestedAnswers,
      };
      await Vue.$api.saveContextQuestion(command);
    });
  }

  async fetchQuestionDetails() {
    await this.safeCall(async () => {
      if (this._questionId) {
        const question = (await Vue.$api.getQuestionForContexts(this._questionId)).data;
        this._questionDetails = question;
        this._contexts = question.contexts;
      } else {
        this.clear();
      }
    });
  }

  async fetchRecruitmentProfilesByContextQuestionContextId(contextId: string) {
    await this.safeCall(async () => {
      this._recruitmentProfiles = (await Vue.$api.getRecruitmentProfilesByContextQuestionAndContextId(this._questionId, contextId)).data;
    });
  }

  get questionDetails(): QuestionForContextsDetails | null {
    return this._questionDetails;
  }

  set questionDetails(value: QuestionForContextsDetails | null) {
    this._questionDetails = value;
  }

  get contexts(): Context[] {
    return this._contexts;
  }

  set contexts(value: Context[]) {
    this._contexts = value;
  }

  get recruitmentProfiles(): RecruitmentProfileSummary[] {
    return this._recruitmentProfiles;
  }

  clear() {
    // @ts-ignore openAPI does not handle null vs. undefined
    if (this._questionDetails) {
      this._questionDetails.contexts = [];
    }
    this._questionDetails = this.emptyQuestion();
  }

  private emptyQuestion() {
    return {
      id: uuid(),
      title: '',
      contexts: [],
      suggestedAnswers: {
        none: '',
        low: '',
        medium: '',
        high: '',
      },
    };
  }
}
