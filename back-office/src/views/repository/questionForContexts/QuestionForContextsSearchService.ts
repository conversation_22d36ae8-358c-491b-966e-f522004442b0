import SafeService from 'odas-plugins/SafeService';
import {AbstractItemPage, ContextsPage, QuestionForContextsPage, SortDirection} from 'erhgo-api-client';
import Vue from 'vue';

export default class QuestionForContextsService extends SafeService {
  private _pageQuestion: QuestionForContextsPage | null = null;
  private _contexts: ContextsPage | null = null;
  private _contextCategories: CategoryPage | null = null;

  async fetchContexts(search: string) {
    await this.safeCall(async () => {
      this._contexts = (await Vue.$api.listContexts(50, 0, undefined, SortDirection.ASC, search)).data;
    });
  }

  async fetchContextCategories() {
    await this.safeCall(async () => {
      this._contextCategories = (await Vue.$axios.get('/api/odas/category/list', {
        params: {
          page: 0,
          size: 20,
        },
      })).data;
    });
  }

  async fetchPageQuestion(size: number, page: number, sortBy: string, sortDirection: SortDirection, filter: string, contextId: string, categoryLevelId: number) {
    await this.safeCall(async () => {
      this._pageQuestion = (await Vue.$api.listQuestionForContexts(size, page, sortBy, sortDirection, filter, contextId || undefined, categoryLevelId || undefined)).data;
    });
  }

  get pageQuestion(): QuestionForContextsPage | null {
    return this._pageQuestion;
  }

  get contexts(): ContextsPage | null {
    return this._contexts;
  }

  get contextCategories(): CategoryPage | null {
    return this._contextCategories;
  }
}

interface CategoryPage extends AbstractItemPage {
  content: CategorySummary[];
}

interface CategorySummary {
  id: string;
  title: string;
}
