<template>
  <v-container fluid>
    <v-row v-if="!questionService.pageQuestion">
      <v-col class="text-center">
        <v-progress-circular indeterminate/>
      </v-col>
    </v-row>
    <v-row v-else>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            <h1 class="my-1">
              <v-icon large
                      color="primary">question_answer
              </v-icon>
              {{ $t('ref.questionForContexts.list') }}
            </h1>
            <v-spacer/>
            <v-text-field v-model="titleSearch"
                          append-icon="search"
                          label="Rechercher par titre…"
                          single-line
                          hide-details/>
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col lg="6" cols="12" class="py-1">
                <v-select v-model="pagination.contextCategoryId"
                          class="px-5"
                          item-value="id"
                          item-text="title"
                          :menu-props="{ offsetY: true }"
                          :items="categories"
                          label="Recherche par categorie de contexte…"
                          chips
                          deletable-chips
                />
              </v-col>
              <v-col lg="6" cols="12" class="py-1">
                <v-autocomplete v-model="pagination.contextId"
                                class="px-5"
                                item-value="id"
                                item-text="title"
                                :search-input.sync="contextSearch"
                                :items="contexts"
                                label="Recherche par contexte…"
                                no-filter
                                chips
                                deletable-chips
                                hide-details
                                hide-no-data
                >
                  <template v-slot:item="data">
                    <v-list-item-content>
                      <v-list-item-title>{{ data.item.title }}</v-list-item-title>
                      <v-list-item-subtitle>
                        {{ data.item.description }}
                      </v-list-item-subtitle>
                    </v-list-item-content>
                  </template>
                </v-autocomplete>
              </v-col>
            </v-row>
            <v-data-table :headers="headers"
                          :items="questionService.pageQuestion.content"
                          :sort-by.sync="pagination.sortBy"
                          :page.sync="pagination.page"
                          :sort-desc.sync="pagination.descending"
                          :items-per-page.sync="pagination.rowsPerPage"
                          :footer-props="{
                            'items-per-page-options': rowsPerPage,
                            'items-per-page-text': 'Nb lignes par page',
                          }"
                          item-key="id"
                          class="elevation-15"
                          :server-items-length="questionService.pageQuestion.totalNumberOfElements">
              <template v-slot:item="props">
                <tr>
                  <td>{{ props.item.title }}</td>
                  <td class="pt-1">
                    <v-tooltip
                      :disabled="context.title.length <= 40"
                      top
                      v-for="context in props.item.contexts"
                      :key="context.id"
                    >
                      <template v-slot:activator="{ on }">
                        <v-chip
                          v-on="on"
                          small
                          class="mb-1 mr-1"
                        >
                          {{ context.title | truncate(40) }}
                        </v-chip>
                      </template>
                      <span> {{ context.title }}</span>
                    </v-tooltip>
                  </td>
                  <td>
                    <v-btn text
                           icon
                           color="primary"
                           class="mx-1"
                           :to="{ name: 'questions_for_contexts_edit', params: {questionForContextId: props.item.id}}">
                      <v-icon>edit</v-icon>
                    </v-btn>
                  </td>
                </tr>
              </template>
              <template slot="no-data">
                <v-alert :value="true"
                         outlined
                         color="error"
                         icon="warning">
                  Aucun résultat disponible
                </v-alert>
              </template>
              <template v-slot:footer.page-text="props">
                Lignes de {{ props.pageStart }} à {{ props.pageStop }} sur {{ props.itemsLength }}
              </template>
            </v-data-table>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-row class="mr-4 my-3">
      <v-spacer/>
      <v-tooltip color="red darken-1"
                 left>
        <template v-slot:activator="{ on }">
          <v-btn fab
                 dark
                 color="red darken-1"
                 v-on="on"
                 class="text-center"
                 id="createActivityButton"
                 :to="{ name: 'questions_for_contexts_create'}">
            <v-icon>mdi-plus</v-icon>
          </v-btn>
        </template>
        <span class="font-weight-bold">{{$t('action.create')}}</span>
      </v-tooltip>
    </v-row>
  </v-container>
</template>

<script>
import _ from 'lodash';
import QuestionForContextsSearchService from './QuestionForContextsSearchService';

export default {
  name: 'List',
  data() {
    return {
      questionService: QuestionForContextsSearchService,
      titleSearch: null,
      contextSearch: null,
      pagination: {
        page: 1,
        sortBy: [],
        descending: [false],
        rowsPerPage: 10,
        contextId: null,
        contextCategoryId: null,
      },
      rowsPerPage: [10, 25, 50, 100, 200],
      headers: [
        {
          text: this.$t('ref.headers.questionForContexts.title'),
          icon: '',
          sortable: true,
          value: 'title',
        },
        {
          text: this.$t('ref.headers.questionForContexts.contexts'),
          icon: '',
          sortable: false,
          value: 'contexts',
        },
        {
          text: this.$t('ref.headers.actions'),
          sortable: false,
        },
      ],
    };
  },
  methods: {
    async getDataFromApi() {
      await this.questionService.fetchPageQuestion(this.pagination.rowsPerPage,
        this.pagination.page - 1,
        this.pagination.sortBy[0] ? this.pagination.sortBy[0] : 'title',
        !this.pagination.descending[0] ? 'ASC' : 'DESC',
        this.titleSearch,
        this.pagination.contextId,
        this.pagination.contextCategoryId);
    },
  },
  async created() {
    this.questionService = new QuestionForContextsSearchService();
    await this.getDataFromApi();
    await this.questionService.fetchContextCategories();
  },
  computed: {
    contexts() {
      return this.questionService.contexts ? this.questionService.contexts.content : [];
    },
    categories() {
      return this.questionService.contextCategories ? this.questionService.contextCategories.content : [];
    },
  },
  watch: {
    titleSearch: _.debounce(async function () {
      await this.getDataFromApi();
    }, 500),
    contextSearch: _.debounce(async function () {
      await this.questionService.fetchContexts(this.contextSearch);
    }, 500),
    pagination: {
      handler: _.debounce(function () {
        this.getDataFromApi();
      }, 200),
      deep: true,
    },
  },
};
</script>
