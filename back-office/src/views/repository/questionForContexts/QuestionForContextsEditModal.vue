<template>
  <v-dialog v-model="show" scrollable>
    <question-for-contexts-edit
      @submit="onQuestionUpdated"
      :on-back="close"
      :context="context"
      :question-for-context-id="questionForContextId"
    />
  </v-dialog>
</template>

<script>
import Edit from './Edit.vue';
import { Context } from 'erhgo-api-client';

export default {
  components: {QuestionForContextsEdit: Edit},
  props: {
    value: {
      type: Boolean,
      required: true,
    },
    context: {
      type: Context,
      required: true,
    },
    questionForContextId: {
      type: String,
      default: null,
    },
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(value) {
        if (!value) {
          this.close();
        }
      },
    },
  },
  methods: {
    onQuestionUpdated(id) {
      this.$emit('submit', id);
    },
    close() {
      this.$emit('closed');
    },
  },
};
</script>
