<template>
  <v-row justify="center" no-gutters class="text-h3 pa-5">
    Bienvenue sur le backoffice #jenesuisPASunCV&nbsp;!

    <v-expansion-panels focusable class="pt-4">
      <v-expansion-panel>
        <v-expansion-panel-header class="top-stat-header">
          {{ $t('statistics.monthlyCandidatures') }}
        </v-expansion-panel-header>
        <v-expansion-panel-content>
          <candidatures-stats/>
        </v-expansion-panel-content>
      </v-expansion-panel>
      <v-expansion-panel>
        <v-expansion-panel-header class="top-stat-header">
          {{ $t('statistics.recruitmentStats') }}
        </v-expansion-panel-header>
        <v-expansion-panel-content>
          <recruitments-stats/>
        </v-expansion-panel-content>
      </v-expansion-panel>
      <v-expansion-panel>
        <v-expansion-panel-header class="top-stat-header">
          {{ $t('statistics.spontaneousCandidatures') }}
        </v-expansion-panel-header>
        <v-expansion-panel-content>
          <spontaneous-candidatures-stats/>
        </v-expansion-panel-content>
      </v-expansion-panel>
      <v-expansion-panel>
        <v-expansion-panel-header class="top-stat-header">
          {{ $t('statistics.severalMetricsStats') }}
        </v-expansion-panel-header>
        <v-expansion-panel-content>
          <several-metrics-stats/>
        </v-expansion-panel-content>
      </v-expansion-panel>
    </v-expansion-panels>
  </v-row>
</template>

<script>

import CandidaturesStats from './repository/statistics/CandidaturesStats.vue';
import RecruitmentsStats from './repository/statistics/RecruitmentsStats.vue';
import SpontaneousCandidaturesStats from './repository/statistics/SpontaneousCandidaturesStats.vue';
import SeveralMetricsStats from './repository/statistics/SeveralMetricsStats.vue';

export default {
  name: 'Dashboard',
  components: {
    CandidaturesStats,
    RecruitmentsStats,
    SpontaneousCandidaturesStats,
    SeveralMetricsStats,
  },
};
</script>

<style lang="scss">
@import '@/style/colors.scss';
.top-stat-header {
  background-color: #ECEFF1;
}
</style>
