<template>
  <v-col class="align justify">
    <div v-if="loading" class="text-center">
      <v-progress-circular indeterminate class="ma-10"/>
    </div>
    <v-row class="my-5">
      <v-btn :loading="clearCachesInProgress" @click="clearCaches" id="clearCachesButton">Vider le cache des
        utilisateurs
      </v-btn>
      <v-btn :loading="loadingUsers" color="secondary" dark @click="exportUsers" class="ml-16" small>
        <v-icon>archive</v-icon>
        Télécharger la liste des utilisateurs
      </v-btn>
    </v-row>
    <v-row>
      <v-alert outlined type="error" :value="clearCachesError">{{ this.clearCachesError }}</v-alert>
    </v-row>
    <v-row>
      <v-btn :loading="jobIndexationInProgress" @click="reindexJobs" id="reindexJobsButton">Démarrer la
        ré-indexation des métiers erhgo
      </v-btn>
    </v-row>
    <v-row>
      <v-alert outlined type="error" :value="jobIndexationError">{{ this.jobIndexationError }}</v-alert>
    </v-row>
    <v-row class="py-5">
      <v-btn :loading="userIndexationInProgress" @click="reindexAllUsers" id="reindexUsersButton">Démarrer la
        ré-indexation des utilisateurs
      </v-btn>
    </v-row>
    <v-row class="py-5">
      <v-btn :loading="userIndexationInProgress" @click="indexUsersNow" id="reindexUsersButton">
        Indexer maintenant les utilisateurs modifiés
      </v-btn>
    </v-row>
    <v-row class="py-5">
      <v-btn :loading="triggerDataHealthCheckInProgress" @click="triggerDataHealthCheck" id="triggerDataHealthCheck">
        Déclencher tests de vérifications des données
      </v-btn>
    </v-row>
    <v-row>
      <v-alert outlined type="error" :value="userIndexationError">{{ this.userIndexationError }}</v-alert>
    </v-row>
    <v-row class="py-5">
      <h4>Envoi de notification</h4>
      <v-text-field placeholder="userId" v-model="notification.userId"
                    :value="notification ? notification.userId : ''"/>
      <v-text-field placeholder="subject" v-model="notification.subject"
                    :value="notification ? notification.subject : ''"/>
      <v-text-field placeholder="content" v-model="notification.content"
                    :value="notification ? notification.content : ''"/>
      <v-btn @click="sendNotification" :loading="notificationSendingInProgress">Envoyer</v-btn>
    </v-row>

    <v-row class="py-5">
      <h4>Execution d'une tâche de fond</h4>
      <v-text-field placeholder="class name" v-model="taskInfo.className"
                    :value="taskInfo ?taskInfo.className : ''"
                    hint="Passer le nom du paquet, exemple:'com.exemple.MonService'"/>
      <v-text-field placeholder="method" v-model="taskInfo.methodName"
                    :value="taskInfo ? taskInfo.methodName : ''"/>
      <v-btn @click="executeTask" :loading="executingTaskInProgress">Exécuter</v-btn>
    </v-row>
    <v-row class="py-5">
      <h4>Sync d'un ats</h4>
      <v-text-field placeholder="ats code" v-model="syncAts.atsCode"
                    :value="syncAts ? syncAts.atsCode: ''"
      />
      <v-text-field placeholder="config code" v-model="syncAts.customCode"
                    :value="syncAts ? syncAts.customCode : ''" />
      <v-btn @click="launchSyncAts" :loading="executingAtsInProgress">Exécuter</v-btn>
    </v-row>

    <v-row>
      <v-alert outlined type="error" :value="executingAtsError">{{ this.executingAtsError }}</v-alert>
    </v-row>
    <v-row class="py-5">
      <h4>Renvoyer un recrutement à des utilisateurs</h4>
      <v-text-field placeholder="recruitmentId" v-model="sendEmailForRecruitmentId"/>
      <v-btn @click="sendEmailForRecruitment" :loading="sendEmailForRecruitmentInProgress">Envoyer</v-btn>
    </v-row>
    <v-row>
      <v-alert outlined type="error" :value="sendEmailForRecruitmentError">{{
          this.sendEmailForRecruitmentError
        }}
      </v-alert>
    </v-row>
    <v-row class="py-5">
      <v-select v-model="selectedConfigurableProperty" :items="configurableProperties" item-text="propertyKey"
                return-object/>
      <v-text-field v-model="selectedConfigurableProperty.propertyValue"
                    :value="selectedConfigurableProperty ? selectedConfigurableProperty.propertyValue : ''"/>
      <v-btn @click="updateConfigurableProperty" id="updateConfigurablePropertyValue">
        Mettre à jour
      </v-btn>
    </v-row>
    <v-row class="py-5">
      <v-btn color="error" outlined :loading="anonymizationInProgress" @click="anonymize" id="anonymizeButton">Démarrer
        l'anonymisation (DANGER
        ZONE)
      </v-btn>
    </v-row>
    <v-row>
      <v-alert outlined type="error" :value="anonymizationError">{{ this.anonymizationError }}</v-alert>
    </v-row>
  </v-col>
</template>

<script>
import moment from 'moment/moment';
import DownloadUtils from '@/components/mixins/DownloadUtils';

export default {
  name: 'IndexingDashboard',
  mixins: [DownloadUtils],
  data() {
    return {
      loading: false,
      clearCachesInProgress: false,
      clearCachesError: null,
      jobIndexationInProgress: false,
      jobIndexationError: null,
      userIndexationInProgress: false,
      loadingUsers: false,
      userIndexationError: null,
      anonymizationInProgress: false,
      anonymizationError: false,
      propertyUpdateInProgress: false,
      propertyUpdateError: null,
      configurableProperties: [],
      selectedConfigurableProperty: {
        propertyKey: null,
        propertyValue: null,
      },
      notificationSendingInProgress: false,
      notificationSendingError: null,
      notification: {
        userId: '',
        subject: '',
        content: '',
      },
      taskInfo: {
        className: '',
        methodName: '',
      },
      syncAts: {
        customCode: '',
        atsCode: '',
      },
      executingTaskError: null,
      executingAtsError: null,
      executingTaskInProgress: false,
      executingAtsInProgress: false,
      sendEmailForRecruitmentInProgress: false,
      sendEmailForRecruitmentId: null,
      sendEmailForRecruitmentError: null,
      triggerDataHealthCheckInProgress: false,
      triggerDataHealthCheckError: null,
    };
  },
  async created() {
    this.loading = true;
    try {
      const res = await this.$api.getAllConfigurableProperties();
      this.configurableProperties = res.data;
    } finally {
      this.loading = false;
    }
  },
  methods: {
    async reindexJobs() {
      this.jobIndexationInProgress = true;
      this.jobIndexationError = null;

      try {
        await this.$api.reindexErhgoOccupations();
      } catch (e) {
        this.jobIndexationError = e;
        this.logError(e);
      } finally {
        this.jobIndexationInProgress = false;
      }
    },
    async reindexAllUsers() {
      this.userIndexationInProgress = true;
      this.userIndexationError = null;

      try {
        await this.$api.reindexAllUsers();
      } catch (e) {
        this.userIndexationError = e;
        this.logError(e);
      } finally {
        this.userIndexationInProgress = false;
      }
    },
    async indexUsersNow() {
      this.userIndexationInProgress = true;
      this.userIndexationError = null;

      try {
        await this.$api.indexUsersNow();
      } catch (e) {
        this.userIndexationError = e;
        this.logError(e);
      } finally {
        this.userIndexationInProgress = false;
      }
    },
    async clearCaches() {
      this.clearCachesInProgress = true;
      this.clearCachesError = null;
      try {
        await this.$api.clearCaches();
      } catch (e) {
        this.ckearCachesError = e;
        this.logError(e);
      } finally {
        this.clearCachesInProgress = false;
      }
    },
    async anonymize() {
      this.anonymizationInProgress = true;
      this.anonymizeError = null;
      try {
        await this.$axios.get('/api/odas/anonymize');
      } catch (e) {
        this.anonymizeError = e;
        this.logError(e);
      } finally {
        this.anonymizationInProgress = false;
      }
    },
    async updateConfigurableProperty() {
      this.propertyUpdateInProgress = true;
      this.propertyUpdateError = null;
      try {
        await this.$api.editConfigurableProperty(this.selectedConfigurableProperty);
      } catch (e) {
        this.propertyUpdateError = e;
        this.logError(e);
      } finally {
        this.propertyUpdateInProgress = false;
      }
    },
    async sendNotification() {
      this.notificationSendingInProgress = true;
      this.notificationSendingError = null;
      try {
        await this.$api.sendMobileNotificationToUsers({
          usersId: this.notification.userId.split(','),
          subject: this.notification.subject,
          content: this.notification.content,
        });
      } catch (e) {
        this.notificationSendingError = e;
        this.logError(e);
      } finally {
        this.notificationSendingInProgress = false;
      }
    },
    async executeTask() {
      this.executingTaskInProgress = true;
      try {
        await this.$api.executeManuallyTask({
          className: this.taskInfo.className,
          methodName: this.taskInfo.methodName,
        });
      } catch (e) {
        this.executingTaskInProgress = e;
        this.logError(e);
      } finally {
        this.executingTaskInProgress = false;
      }
    },
    async sendEmailForRecruitment() {
      this.sendEmailForRecruitmentInProgress = true;
      const recruitmentId = this.sendEmailForRecruitmentId;
      try {
        await this.$api.inviteToRecruitment({recruitmentId, forceResend: true});
      } catch (e) {
        this.sendEmailForRecruitmentError = e;
        this.logError(e);
      } finally {
        this.sendEmailForRecruitmentInProgress = false;
      }
    },
    async triggerDataHealthCheck() {
      this.triggerDataHealthCheckInProgress = true;
      try {
        await this.$api.executeAllHealthCheckQueries();
      } catch (e) {
        this.triggerDataHealthCheckError = e;
        this.logError(e);
      } finally {
        this.triggerDataHealthCheckInProgress = false;
      }
    },
    async launchSyncAts() {
      this.executingAtsInProgress = true;
      try {
        await this.$api.syncAts(this.syncAts);
      } catch (e) {
        this.executingAtsError = e;
        this.logError(e);
      } finally {
        this.executingAtsInProgress = false;
      }
    },

    async exportUsers() {
      this.loadingUsers = true;
      try {
        await this.download(async () => (await this.$api.exportUsers({
          usersId: [],
          deanonymizedUser: true,
        })), `utilisateurs_${moment().format('MM-DD-YYYY_HH-mm-ss')}.csv`);
      } finally {
        this.loadingUsers = false;
      }
    },
  },
};
</script>

<style scoped>

</style>
