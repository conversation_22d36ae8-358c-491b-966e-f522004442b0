<template>
  <v-card class="pa-4 mb-6">
    <v-row align="center">
      <v-col cols="1">
        <v-icon large color="primary">mdi-robot</v-icon>
      </v-col>
      <v-col cols="11">
        <span class="font-weight-bold mb-0">Testez votre prompt OpenAI</span>
        <p class="mb-4">
          Utilisez cet outil pour tester vos prompts avec OpenAI. Vous pouvez également joindre un fichier PDF pour fournir du contenu supplémentaire.
        </p>
      </v-col>
    </v-row>
    <v-form ref="form" v-model="isFormValid" @submit.prevent="submitPrompt">
      <v-row>
        <v-col cols="12">
          <v-alert v-if="error" :value="error" type="error" outlined dismissible>
            {{ error }}
          </v-alert>
        </v-col>
        <v-col cols="12" sm="6">
          <v-text-field
            v-model.number="formData.temperature"
            type="number"
            step="0.1"
            min="0"
            max="1"
            label="Température"
            hint="Valeur entre 0 et 1"
            persistent-hint
            :rules="[v => (v >= 0 && v <= 1) || 'La température doit être entre 0 et 1']"
          />
        </v-col>
        <v-col cols="12" sm="6">
          <v-text-field
            v-model="formData.model"
            label="Modèle"
            hint="Par exemple : gpt-4.1-mini"
            persistent-hint
            :rules="[v => !!v || 'Le modèle est requis']"
          />
        </v-col>
        <v-col cols="12" sm="6">
          <v-text-field
            v-model.number="formData.maxTokens"
            type="number"
            label="Tokens Max"
            hint="Nombre maximum de tokens à générer"
            persistent-hint
            :rules="[v => v > 0 || 'Le nombre de tokens doit être supérieur à 0']"
          />
        </v-col>
        <v-col cols="12" sm="6">
          <v-switch
            v-model="formData.forceJson"
            label="Forcer le mode JSON"
          />
        </v-col>
        <v-col cols="12">
          <v-textarea
            v-model="formData.promptMessages"
            label="Messages du Prompt"
            hint="Entrez votre prompt ici"
            persistent-hint
            rows="5"
            :rules="[v => !!v || 'Le prompt est requis']"
          />
        </v-col>
        <v-col cols="12">
          <v-file-input
            v-model="formData.fileContent"
            label="Fichier PDF"
            accept=".pdf"
            hint="Téléchargez un fichier PDF (optionnel)"
            persistent-hint
            prepend-icon="mdi-file-pdf-box"
            :rules="[fileSizeRule]"
          />
        </v-col>
        <v-col cols="12" class="text-right">
          <v-btn
            color="primary"
            type="submit"
            :loading="loading"
            :disabled="!isFormValid"
          >
            Tester le Prompt
            <v-icon right>mdi-send</v-icon>
          </v-btn>
        </v-col>
      </v-row>
      <v-row :style=" !loading?`visibility: hidden`:''">
        <v-col cols="12">
          <v-alert type="info" outlined>
            Votre prompt est en cours de traitement. Veuillez patienter...
          </v-alert>
        </v-col>
      </v-row>
      <v-row v-if="response">
        <v-col cols="12">
          <v-card flat class="mt-4">
            <v-card-title>Réponse</v-card-title>
            <v-card-text>
              <pre>{{ JSON.stringify(response, null, 2).replaceAll("\\r", "").replaceAll("\\n", "\n").replaceAll("\\t", "  ").replaceAll("\\\"", "")
                }}</pre>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-form>
  </v-card>
</template>

<script>
export default {
  name: 'GenericPromptTester',
  data() {
    return {
      formData: {
        temperature: 0.7,
        model: 'gpt-4.1-mini',
        maxTokens: 5000,
        forceJson: false,
        promptMessages: '',
        fileContent: null,
      },
      loading: false,
      error: '',
      response: null,
      isFormValid: false,
    };
  },
  methods: {
    async submitPrompt() {
      if (!this.isFormValid) return;

      this.loading = true;
      this.error = '';
      this.response = null;

      try {
        const formData = new FormData();
        Object.keys(this.formData).forEach(key => {
          if (key === 'fileContent' && this.formData[key]) {
            formData.append(key, this.formData[key]);
          } else {
            formData.append(key, this.formData[key]);
          }
        });

        const response = await this.$axios.post('/api/odas/erhgo-occupation-generation/generic-prompt-tester', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
        this.response = response.data;
        this.resetForm();
      } catch (e) {
        this.error = e.response?.data?.message || 'Une erreur est survenue';
      } finally {
        this.loading = false;
      }
    },
    resetForm() {
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.resetValidation();
        }
      });
    },
    fileSizeRule(file) {
      const maxSizeInBytes = 10 * 1024 * 1024; // 10 Mo
      return !file || file.size <= maxSizeInBytes || 'Le fichier ne doit pas dépasser 10 Mo.';
    },
  },
};
</script>

<style scoped>
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
