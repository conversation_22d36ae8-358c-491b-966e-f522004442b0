<template>
  <v-col class="align justify">
    <h2>Simuler une offre ATS</h2>
    <v-alert v-if="error" type="error" outlined>
      Une erreur est survenue&nbsp;: vérifiez le xml et ré-essayez.
      (Note technique&nbsp;: {{ error }})
    </v-alert>
    <v-container fluid>
      <p>URL par défaut scrutée régulièrement&nbsp;: <a
        href="https://cloud.eolia-software.com/ws_martinbelaysoud_77B2C103-8A58-409A-BE5F-65239AD6A30D_fr_jobboard_entreprise.asp"
        target="_blank">https://cloud.eolia-software.com/ws_martinbelaysoud_77B2C103-8A58-409A-BE5F-65239AD6A30D_fr_jobboard_entreprise.asp</a>
      </p>
      <v-textarea label="Copier le XML pour simuler une offre ATS" variant="outlined" v-model="rawFlow" />
    </v-container>
    <v-btn @click="sendAtsXml" :loading="loading" :disabled="!rawFlow">Envoyer</v-btn>
  </v-col>
</template>

<script>
export default {
  name: 'SimulateAtsEntry',
  data() {
    return {
      rawFlow: '',
      loading: false,
      error: '',
    };
  },
  methods: {
    async sendAtsXml() {
      this.loading = true;
      this.error = '';
      try {
        await this.$api.simulateAtsOffer({ rawFlow: this.rawFlow });
      } catch (e) {
        this.error = e;
        this.logError(e);
      } finally {
        this.loading = false;
      }
    },

  },
};
</script>
