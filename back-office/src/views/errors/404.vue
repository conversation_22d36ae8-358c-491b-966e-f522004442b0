<template>
  <v-app>
    <v-row align="center" justify="center">
      <v-card>
        <v-card-title class="justify-center">
          <v-icon large color="orange">mdi-help</v-icon>
        </v-card-title>
        <v-card-text class="text-center">
          <p>Erreur 404: Page not found</p>
          <v-btn :to="{ name: 'home' }">Go back to the homepage</v-btn>
        </v-card-text>
      </v-card>
    </v-row>
  </v-app>
</template>

<script>
import {logErrorToServer} from 'odas-plugins';
import Vue from 'vue';

export default {
  props: {
    previousRoute: String,
  },
  created() {
    logErrorToServer(`Error 404: ${Vue.$router.currentRoute.fullPath} from ${this.previousRoute}`, Vue.$api);
  },
};
</script>

<style>

</style>
