<template>
  <v-row no-gutters>
    <v-progress-circular
      :size="50"
      color="primary"
      indeterminate
      v-if="loading || !candidature"
    />
    <div id="job-candidature-detail" v-else class="full-width">
      <errorDialog v-model="candidature.showAPIErrorDialog"/>
      <v-breadcrumbs class="breadcrumb" :items="breadcrumbsItems">
        <template v-slot:divider>
          <v-icon>chevron_right</v-icon>
        </template>
        <template v-slot:item="{ item }">
          <v-breadcrumbs-item
            exact
            :to="item.to"
            :disabled="item.disabled">
            {{ item.text }}
          </v-breadcrumbs-item>
        </template>
      </v-breadcrumbs>
      <v-row class="pr-4" justify="space-between" align-content="center" no-gutters>
        <h1 class="pl-2">Détails de la candidature{{
            candidature.isCandidatureSelected ? ' - ' +
              candidature.candidatureName : ''
          }}</h1>
        <a class="contact-button" @click="showDetailModal = true">
          <v-icon large class="mr-2">mdi-account-card-details</v-icon>
          Profil complet
        </a>
      </v-row>
      <v-row align-content="center" no-gutters>
        <v-subheader>Reçue le {{ candidature.submissionDate | formatDate }}
          <span v-if="candidature.refusalDate"
                class="font-weight-bold error--text">&nbsp;/ Refusée le {{
              candidature.refusalDate | formatDate
            }}</span>
          <span
            class="font-weight-bold">&nbsp;/ {{
              $t(`candidaturesRealStates.title.${candidature.candidatureState}`)
            }}</span>
        </v-subheader>
      </v-row>
      <v-row align-content="center" no-gutters>
        <v-subheader v-if="candidature.isArchived">
          <strong>Cette candidature est archivée</strong>
        </v-subheader>
      </v-row>
      <v-row v-if="candidature.recruitment.customQuestion && candidature.customAnswer" no-gutters>
        <v-col cols="12">
          <h3 class="pl-2 py-2 mb-2 primary">
            <v-icon class="mr-2">question_answer</v-icon>
            Question spécifique
          </h3>
        </v-col>
        <v-col cols="12">
          <v-textarea class="pa-3" readonly outlined :label="candidature.recruitment.customQuestion"
                      :value="candidature.customAnswer.replaceAll('\\n', '\n')" no-resize />
        </v-col>
        <v-divider class="my-4"/>
      </v-row>
      <v-row align-content="end" no-gutters
             v-if="candidature.contextsPositioning && candidature.contextsPositioning.length">
        <v-expansion-panels class="mb-3" tile flat>
          <v-expansion-panel>
            <v-expansion-panel-header class="pl-2 primary size-unset" id="candidatureContextsPositioning">
              <h3>
                <v-icon class="mr-2">not_listed_location</v-icon>
                Positionnement sur les prérequis
              </h3>
              <template v-slot:actions>
                <v-icon>$expand</v-icon>
              </template>
            </v-expansion-panel-header>
            <v-expansion-panel-content class="pt-2">
              <context-positioning-detail :context-positioning="candidature.contextsPositioning"/>
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>
      </v-row>
      <v-row align-content="end" no-gutters>
        <v-expansion-panels tile flat>
          <v-expansion-panel>
            <v-expansion-panel-header class="pl-2 primary size-unset" id="candidatureExperiences">
              <h3>
                <v-icon class="mr-2">card_travel</v-icon>
                Expériences du candidat
              </h3>
              <template v-slot:actions>
                <v-icon>$expand</v-icon>
              </template>
            </v-expansion-panel-header>
            <v-expansion-panel-content class="pt-2">
              <experiences-sumup :experiences="candidature.experiences"/>
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>
      </v-row>
      <v-row align-content="end" no-gutters>
        <v-expansion-panels class="mt-3" tile flat :value="0">
          <v-expansion-panel>
            <v-expansion-panel-header class="pl-2 primary size-unset">
              <h3>
                <v-icon class="mr-2">phonelink_ring</v-icon>
                Informations de contact
              </h3>
              <template v-slot:actions>
                <v-icon>$expand</v-icon>
              </template>
            </v-expansion-panel-header>
            <v-expansion-panel-content class="pt-2">
              <div v-if="candidature.isCandidatureSelected" id="contactInfos">
                <general-information :candidate-details="candidature"/>
              </div>
              <div v-else>
                <v-alert type="info" outlined prominent>
                  <v-row align="center">
                    <v-col cols="10" class="black--text">
                      Pour révéler le nom et afficher les informations de contact de ce candidat, vous vous engagez
                      moralement à rencontrer cette personne pour un premier entretien.
                    </v-col>
                    <v-col cols="1">
                      <v-btn id="contactCandidateButton" color="primary" :loading="candidature.loading"
                             @click="candidature.meetCandidate()">Je m'engage
                      </v-btn>
                    </v-col>
                  </v-row>
                </v-alert>
              </div>
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>
      </v-row>
      <v-row align-content="end" no-gutters>
        <v-col>
          <h3 class="pl-2 mt-3 py-2 primary">
            <v-icon class="mr-2">edit</v-icon>
            Prise de notes
          </h3>
        </v-col>
      </v-row>
      <v-row no-gutters>
        <v-textarea v-model="candidature.candidatureNote.text"
                    label="Prenez ici vos notes relatives à cette candidature"
                    outlined
                    no-resize
                    :disabled="candidature.savingCandidatureNote"
                    hide-details
                    class="pa-3"
                    rows="3"/>
      </v-row>
      <v-row no-gutters>
        <v-btn class="ma-3"
               color="primary"
               @click="candidature.saveCandidatureNote()"
               :loading="candidature.savingCandidatureNote"
               :disabled="!candidature.saveNoteButtonActive || candidature.savingCandidatureNote">
          Enregistrer
        </v-btn>
      </v-row>
    </div>
    <v-dialog v-if="userProfileDetail"
              v-model="showDetailModal"
              scrollable>
      <v-card>
        <v-card-title class="justify-center"><h3>Profil complet</h3></v-card-title>
        <v-card-text>
          <matching-detail :experiences="userProfileDetail.experiences"/>
        </v-card-text>
        <v-card-actions>
          <v-spacer/>
          <v-btn class="ml-4" color="primary" @click="showDetailModal = false">Fermer</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-row>
</template>

<script>
import ErrorDialog from '@/components/common/ErrorDialog';
import GeneralInformation from '@/components/common/GeneralInformation';
import appStore from '@/store';
import MatchingDetail from './MatchingDetail.vue';
import { Candidature } from './Candidature';
import ExperiencesSumup from '../common/ExperiencesSumupView.vue';
import ContextPositioningDetail from './ContextPositioningDetail';

export default {
  components: {
    MatchingDetail,
    ExperiencesSumup,
    ErrorDialog,
    GeneralInformation,
    ContextPositioningDetail,
  },
  props: {
    candidature_id: Number,
    organization_code: String,
  },
  data() {
    return {
      candidature: null,
      loading: false,
      showDetailModal: false,
      userProfileDetail: null,
    };
  },
  computed: {
    recruitment_id() {
      return this.candidature?.recruitment?.id;
    },
    organization() {
      return appStore.getters.organizationLoaded;
    },
    isRefused() {
      return !!this.candidature?.refusalDate;
    },
    breadcrumbsItems: {
      get() {
        return [
          {
            text: 'Recrutements',
            to: {
              name: 'recruitments_list',
              params: {organization_code: this.organization_code},
            },
          },
          {
            text: `${this.candidature.recruitment.jobTitle} - ${this.candidature.recruitment.profileTitle}`,
            to: {
              name: 'recruitment_result',
              params: {organization_code: this.organization_code, recruitment_id: this.recruitment_id},
            },
          },
          {
            text: this.candidature.candidatureName,
            disabled: true,
          },
        ];
      },
    },
  },
  async created() {
    try {
      this.loading = true;
      this.candidature = await Candidature.initialize(this.candidature_id);
      this.userProfileDetail = (await this.$api.getUserProfileDetailWithCapacities(this.candidature.userId)).data;

    } catch (e) {
      this.logError(e);
      this.candidature.showAPIErrorDialog = true;
    } finally {
      this.loading = false;
    }
  },
};
</script>

<style lang="scss">
@import '../../../../style/colors';

.full-width {
  width: 100%;
}

.contact-button {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, .54);
  font-size: larger;

  .v-icon {
    color: inherit;
  }

  &:hover {
    color: $mdi-primary;
  }
}

.white > .v-textarea > .v-input__control > .v-input__slot:before {
  border-style: none;
}

.white > .v-textarea > .v-input__control > .v-input__slot:after {
  border-style: none;
}

.white {
  background-color: white;
}

.size-unset.v-expansion-panel-header {
  font-size: unset;
}
</style>

