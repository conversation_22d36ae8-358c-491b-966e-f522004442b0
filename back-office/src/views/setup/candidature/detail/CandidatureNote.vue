<template>
  <div v-if="candidature">
    <v-textarea v-model="candidature.candidatureNote.text"
                label="Prenez ici vos notes relatives à cette candidature"
                outlined
                no-resize
                :disabled="candidature.savingCandidatureNote"
                hide-details
                class="pa-3"
                rows="3"/>
    <v-btn class="ma-3"
           color="primary"
           @click="candidature.saveCandidatureNote()"
           :loading="candidature.savingCandidatureNote"
           :disabled="!candidature.saveNoteButtonActive || candidature.savingCandidatureNote">
      Enregistrer
    </v-btn>
  </div>
  <div v-else class="text-center">
    <v-progress-circular indeterminate/>
  </div>
</template>

<script>
import {Candidature} from './Candidature';

export default {
  name: 'CandidatureNote.vue',
  props: {
    candidatureId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      candidature: null,
    };
  },
  async created() {
    try {
      this.candidature = await Candidature.initialize(this.candidatureId);
    } catch (e) {
      this.logError(e);
    }
  },
};
</script>

<style scoped>

</style>
