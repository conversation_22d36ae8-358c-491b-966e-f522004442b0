<template>
  <div>
    <h3>Expériences</h3>
    <v-expansion-panels multiple :value="experiences.map((_, index) => index)">
      <v-expansion-panel v-for="experience in experiences" :key="experience.id">
        <v-expansion-panel-header>
          <v-col cols="12">
            <h4>
              [{{ experience.durationType ? $t(`durationType.${experience.durationType}`) : '' }}
              {{ experience.duration ? $t(`duration.${experience.duration}`) : '' }}]
              [{{ experience.experienceType ? $t(`experience.${experience.experienceType}`) : '' }}]
              {{ experience.jobTitle }} {{
                experience.organizationName ? ` chez
              ${experience.organizationName}` : ''
              }}
            </h4>
          </v-col>
        </v-expansion-panel-header>
        <v-expansion-panel-content>
          <v-row>
            <v-col md="6">
              <h5>Activités</h5>
              <ul>
                <li v-for="experienceActivity in experience.activities" :key="experienceActivity.id">
                  {{ experienceActivity.title }}
                </li>
              </ul>
            </v-col>
          </v-row>
        </v-expansion-panel-content>
      </v-expansion-panel>
    </v-expansion-panels>
  </div>
</template>

<script>
export default {
  props: {
    experiences: Array,
  },
};
</script>
