<template>
  <div>
    <p><strong>{{ experiencesCount }}</strong> expérience{{ experiencesCount > 1 ? 's' : '' }} renseignée{{
        experiencesCount
        > 1 ? 's' : ''
      }}&nbsp;:</p>
    <ul style="list-style: none;">
      <transition-group name="fade">
        <li :id="`candidatureExperience${index}`" v-for="(experience, index) in experiencesDisplay" :key="experience.id"
            class="my-1">
          <v-icon small color="primary" class="mr-1">arrow_forward_ios</v-icon>
          <template v-if="experience.erhgoOccupationTitle">
            <b>{{ experience.erhgoOccupationTitle }}</b> <i>({{ experience.jobTitle }})</i>
          </template>
          <template v-else>
            <b>{{experience.jobTitle}}</b>
          </template>
        </li>
      </transition-group>
    </ul>
    <v-row v-if="experiences && experiences.length > 3">
      <v-btn outlined small color="primary" class="mt-2 mr-3" @click="toggleExperiencesDisplay">
        <v-icon left>{{!fullDisplay ? 'arrow_drop_down' : 'arrow_drop_up'}}</v-icon>
        {{!fullDisplay ? 'Voir la suite' : 'Retour'}}
      </v-btn>
    </v-row>
  </div>
</template>
<script>
export default {
  name: 'ExperiencesSumupView',
  props: {
    experiences: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      fullDisplay: false,
    };
  },
  computed: {
    experiencesCount() {
      return this.experiences.length;
    },
    experiencesDisplay() {
      if (this.experiencesCount <= 3 || this.fullDisplay) {
        return this.experiences;
      } else {
        return this.experiences.slice(0, 3);
      }
    },
  },
  methods: {
    toggleExperiencesDisplay() {
      this.fullDisplay = !this.fullDisplay;
    },
  },
};
</script>

<style scoped>
.fade-enter-active, .fade-leave-active {
  transition: opacity .5s;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}
</style>
