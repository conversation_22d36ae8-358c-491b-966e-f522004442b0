<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <h1>
          <v-icon large
                  color="primary">mdi-office-building
          </v-icon>
          Liste des codes d'activation
        </h1>
      </v-col>
      <v-col cols="12">
        <v-data-table :headers="headers"
                      :sort-desc="sortDesc"
                      :sort-by.sync="sortBy"
                      :items="invitationCodeCodes ? invitationCodeCodes : []"
                      class="invitationCodeTable">
          <template v-slot:item.creationDate="{ item }">
            <span>{{ item.creationDate | formatDate }}</span>
          </template>
        </v-data-table>
      </v-col>
      <v-col class="text-right">
        <v-btn
          class="btnAddInvitationCode"
          id="btnAddInvitationCode"
          rounded
          color="primary"
          @click="openInvitationCodePopin">
          <v-icon>add</v-icon>
        </v-btn>
      </v-col>
      <v-dialog v-model="showAddInvitationCodePopin" v-if="showAddInvitationCodePopin" max-width="75%" persistent>
        <invitation-code-popin
          @close="closeInvitationCodePopin" :codes="invitationCodeCodes"
        />
      </v-dialog>
    </v-row>
  </v-container>
</template>

<script>

import InvitationCodePopin from './InvitationCodePopin';

export default {
  components: {InvitationCodePopin},
  props: {
    organizationCode: {
      type: String,
      required: false,
    },
  },
  data() {
    return {
      showAddInvitationCodePopin: false,
      organizationCodes: [],
      invitationCodeCodes: [],
      isLoading: false,
      sortBy: ['creationDate'],
      sortDesc: [true],
    };
  },
  async created() {
    await this.refreshList();
  },
  methods: {
    async refreshList() {
      this.isLoading = true;
      try {
        this.invitationCodeCodes = (await this.$api.listInvitationCodes()).data;
      } finally {
        this.loading = false;
      }
    },
    async closeInvitationCodePopin() {
      this.showAddInvitationCodePopin = false;
      await this.refreshList();
    },
    openInvitationCodePopin() {
      this.showAddInvitationCodePopin = true;
    },
  },
  computed: {
    headers() {
      return [
        {
          text: 'Date de création',
          align: 'left',
          sortable: true,
          value: 'creationDate',
        },
        {
          text: 'Code',
          align: 'left',
          sortable: true,
          value: 'code',
        },
        {
          text: 'Organisation',
          align: 'left',
          sortable: true,
          value: 'organization',
        },
        {
          text: 'Créateur',
          align: 'left',
          sortable: true,
          value: 'createdBy',
        },
        {
          text: `Nombre d'activations maximum pour ce code`,
          align: 'right',
          sortable: true,
          value: 'maxNumberOfGuests',
        },
        {
          text: `Nombre d'activations utilisées`,
          align: 'right',
          sortable: true,
          value: 'numberOfGuests',
        },
      ];
    },
  },
};
</script>
