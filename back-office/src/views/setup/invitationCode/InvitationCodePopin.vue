<template>
  <v-card>
    <div class="px-5 pt-4">
      <v-alert type="info" v-if="isSuccess" outlined>
        Le code a correctement été enregistré
      </v-alert>
      <v-alert type="error" v-if="isOnError" outlined>
        Une erreur est survenue, veuillez réessayer après vous être assuré que ce code n'existe pas déjà
      </v-alert>
    </div>
    <v-card-title>
      <v-row class="text-center mb-7">
        <v-col>
          <h2>
            Ajouter un code invitation pour une organisation
          </h2>
        </v-col>
      </v-row>
    </v-card-title>
    <v-form ref="form" v-model="valid" id="validate-creation-code" @submit.prevent="saveInvitationCode">
      <v-card-text class="pb-0">
        <v-row no-gutters justify="center" class="pt-2">
          <v-col class="pl-sm-2">
            <div>
              <v-text-field v-model="invitationCode"
                            outlined
                            label="Saisissez votre code"
                            :rules="[value => !!value || 'Champ obligatoire']"/>
            </div>
          </v-col>
        </v-row>
        <v-row no-gutters justify="center" class="pt-2">
          <v-col class="pl-sm-2">
            <div>
              <v-select v-model="organizationSelected"
                        outlined
                        :items="organizations"
                        :rules="[value => !!value || 'Champ obligatoire']"
                        label="Sélectionnez l'organisation à laquelle rattacher ce code"/>
            </div>
          </v-col>
        </v-row>
        <v-row no-gutters justify="center" class="pt-2">
          <v-col class="pl-sm-2">
            <div>
              <v-select v-model="duration"
                        outlined
                        :items="[1,2,3,4,5,6,7,8,9,10,11,12]"
                        :rules="[value => !!value || 'Champ obligatoire']"
                        label="Saisissez la durée de validité du code (en mois)"/>
            </div>
          </v-col>
        </v-row>
        <v-row no-gutters justify="center" class="pt-2">
          <v-col class="pl-sm-2">
            <div>
              <integer-field v-model="maxGuests"
                             maxlength="4"
                             outlined
                             :rules="[value => !!value || 'Champ obligatoire']"
                             label="Saisissez le nombre d'invité(s) autorisé(s) pour ce code"/>
            </div>
          </v-col>
        </v-row>
      </v-card-text>
      <v-divider/>
      <v-card-actions>
        <v-row dense>
          <v-col>
            <v-btn
              type="submit"
              :loading="loading"
              color="primary"
              class='white--text'
              :disabled="!valid"
              form="validate-creation-code">
              Ajouter le code d'invitation
            </v-btn>
          </v-col>

          <v-spacer/>
          <v-col class="text-right">
            <v-btn
              :loading="loading"
              color="error"
              @click="closeInvitationCodePopin"
            >
              {{ isSuccess ? 'Fermer' : 'Annuler' }}
            </v-btn>
          </v-col>
        </v-row>
      </v-card-actions>
    </v-form>
  </v-card>
</template>


<script>

import IntegerField from 'odas-plugins/IntegerField.vue';

export default {
  name: 'InvitationCodePopin.vue',
  components: {
    IntegerField,
  },
  props: {
    codes: {
      required: true,
      type: Array,
    },
  },
  data() {
    return {
      valid: null,
      showInvitationCodePopin: false,
      invitationCode: '',
      duration: 0,
      maxGuests: 0,
      numberInvitationCode: 0,
      organizations: [],
      organizationSelected: '',
      isSuccess: false,
      isOnError: false,
      loading: false,
      errorMessage: 'Une erreur est survenue, veuillez réessayer',
    };
  },
  async created() {
    this.organizations = (await this.$api.findOrganizationsPaginatedAndFilteredByProperty(0, -1, 'title', 'ASC', undefined, null)).data.content.map(e => ({
      text: e.title,
      value: e.code,
    })).sort((a, b) => a.text.localeCompare(b.text));
    this.reset();
  },
  methods: {
    reset() {
      this.$refs.form.reset();
      this.$nextTick(() => {
        this.invitationCode = '';
        this.organizationSelected = this.organizations.filter(o => o.value === 'P-20988')[0]?.value;
        this.duration = 3;
        this.maxGuests = 1;
        this.numberInvitationCode = 0;
        setTimeout(() => this.isSuccess = false, 2000);
      });
    },

    closeInvitationCodePopin() {
      this.showInvitationCodePopin = false;
      this.isSuccess = false;
      this.isOnError = false;
      this.$emit('close');
    },

    async saveInvitationCode() {
      if (this.valid) {
        if (this.codes.map(c => c.code).includes(this.invitationCode)) {
          this.isOnError = true;
        } else {
          this.isOnError = false;
          const code = this.invitationCode;
          const duration = this.duration;
          const maxInvitations = this.maxGuests;
          this.loading = true;
          try {
            (await this.$api.saveInvitationCode({
              host: this.organizationSelected,
              code: code,
              maxNumberOfGuests: maxInvitations,
              duration: duration,
            }));
            this.isSuccess = true;
            this.reset();
          } catch (e) {
            this.isOnError = true;
            this.logError(e);
          } finally {
            this.loading = false;
          }
        }
      }
    },
  },
};

</script>
