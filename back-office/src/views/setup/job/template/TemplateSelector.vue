<template>
  <v-dialog v-model="showDialog" persistent width="50%">
    <v-card>
      <v-card-title primary-title class="text-h5 grey lighten-2">
        Veuillez choisir un modèle de poste
      </v-card-title>
      <v-card-text class="pt-4">
        <div v-if="waitingForJob" class="text-center">
          <v-progress-circular indeterminate/>
          <br/>
          <div class="text-center text-h6">Création du poste en cours...</div>
        </div>
        <span v-if="!waitingForJob">Saisissez une recherche et cliquez sur le poste correspondant.</span>
        <br/>
        <v-combobox
          id="selectorJob"
          solo
          class="pt-3"
          v-if="!waitingForJob"
          v-model="model"
          :items="items"
          :loading="isLoading"
          :search-input.sync="search"
          color="white"
          hide-selected
          item-text="description"
          placeholder="Rechercher un modèle de poste"
          no-filter
          @change="createJob"
        >
          <template v-slot:item="data">
            <template-list-item
              class="selectableJob"
              :template="data.item"/>
          </template>
        </v-combobox>

        <v-alert :value="true"
                 outlined
                 color="error"
                 v-if="showError"
                 icon="warning">
          Une erreur est survenue à la création du poste :&nbsp;veuillez réessayer ou contacter le support
          technique.
        </v-alert>
        <v-alert :value="true"
                 outlined
                 :class="{'hidden': !showWarning}"
                 color="info"
                 icon="warning">
          Le texte saisi ne correspond pas à un modèle de poste connu.<br/>
          Veuillez procéder à une nouvelle recherche et cliquer dans la liste sur le poste correspondant.
        </v-alert>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
import _ from 'lodash';
import TemplateListItem from './TemplateListItem.vue';

export default {
  components: {
    TemplateListItem,
  },
  props: {
    recruiterCode: {
      type: String,
      required: true,
    },
    jobType: {
      type: String,
      required: true,
    },
  },
  data: () => ({
    entries: [],
    isLoading: false,
    model: null,
    search: null,
    showDialog: true,
    waitingForJob: false,
    showError: false,
    showWarning: false,
  }),
  computed: {
    items() {
      return this.entries.map((entry) => {
        const description = entry.title;

        return Object.assign({}, entry, {description});
      });
    },
  },
  watch: {
    search: _.debounce(async function doSearch(val) {
      if (val) {
        try {
          this.showWarning = false;
          this.isLoading = true;
          this.entries = (await this.$api.searchOccupations(val, false)).data;
        } finally {
          this.isLoading = false;
          if (!this.entries.length) {
            this.showWarning = true;
          }
        }
      }
    }, 700),
  },
  methods: {
    async createJob() {
      if (this.model.code) {
        try {
          this.waitingForJob = true;
          this.showError = false;
          this.showWarning = false;
          const templateId = this.model.code;
          const {recruiterCode, jobType} = this;
          const job = (await this.$api.createJobForTemplate({templateId, recruiterCode, jobType})).data;
          this.showDialog = false;
          this.$emit('input', job);
        } catch (e) {
          this.showError = true;
          this.waitingForJob = false;
          this.logError(e);
        }
      } else {
        this.showWarning = true;
      }
    },
  },
};
</script>

<style lang="scss">

.hidden {
  visibility: hidden;
}
</style>
