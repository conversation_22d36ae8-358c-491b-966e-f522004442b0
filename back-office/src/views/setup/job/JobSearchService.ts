import SafeService from 'odas-plugins/SafeService';
import Vue from 'vue';
import {JobDetail, JobPage, JobSummary, OrganizationSummary, SortDirection, UserSummary} from 'erhgo-api-client';
import asyncDebounce from 'async-debounce';
import {logErrorToServer} from 'odas-plugins/error-handler';
import {AxiosError} from 'axios';

export default class JobSearchService extends SafeService {

  private _job: JobDetail | null = null;
  private _jobList: JobSummary[] = [];
  private _search = '';
  private _recruiterCode = '';
  private _listMembersOfGroups: UserSummary[] = [];

  private _jobPage: JobPage | null = null;
  private _deletionLoadings: string[] = [];

  private _listRecruiters: OrganizationSummary[] = [];
  private _noOrgaFilter = false;
  private _recruiterFilter: string[] = [];

  pagination = {
    page: 1,
    sortBy: ['title'],
    descending: [false],
    itemsPerPage: 10,
  };

  deletionResult: { type: string | null; message: string } = {
    type: null,
    message: '',
  };

  constructor(defaultLoading = false) {
    super();
    this.loading = defaultLoading;
  }

  private debouncedSearch = asyncDebounce(async () => {
    return this.safeCall(async () => {
      return this.doFetchJobsPage();
    });
  }, 300);

  private async doFetchJobsPage() {
    await this.safeCall(async () => {
      this._jobPage = (await Vue.$api.getJobPage(
        this._recruiterFilter.length ? this._recruiterFilter : [this._recruiterCode],
        this.pagination.page - 1,
        this.pagination.itemsPerPage,
        !this.pagination.descending[0] ? SortDirection.ASC : SortDirection.DESC,
        this.pagination.sortBy[0],
        this.search,
        !!this._recruiterFilter?.length,
      )).data;
    });
  }

  async fetchJob(id: string) {
    await this.safeCall(async () => {
      this._job = (await Vue.$api.getJob(id)).data;
    });
  }

  async fetchPublishedJobs(organizationCode: string) {
    await this.safeCall(async () => {
      this._jobList = (await Vue.$api.getPublishedJobs(organizationCode)).data;
    });
  }

  async fetchMembersOfGroups(organizationCode: string) {
    return this.safeCall(async () => {
      this._listMembersOfGroups = (await Vue.$api.getMembersOfGroups(organizationCode)).data;
    });
  }

  async deleteJob(jobId: string) {
    this._deletionLoadings.push(jobId);
    const jobName = this._jobPage!.content.filter(x => x.id === jobId)[0].title;

    try {
      await Vue.$api.deleteJob(jobId);
      this._jobPage!.content = this._jobPage!.content.filter(x => x.id !== jobId);
      this.setDeletionResult(true, `Le poste "${jobName}" a été correctement supprimé`);
    } catch (error) {
      const status = (error as AxiosError)?.response?.status;
      switch (status) {
        case 400:
          this.setDeletionResult(false, 'Erreur : Vous ne pouvez pas supprimer un poste disposant d\'au moins une candidature sur un recrutement');
          break;
        case 404:
          // The only reason a 404 can happen here is that another user deleted the job before you
          // We do nothing and re-fetch the jobs
          this.fetchJobs();
          this.hideDeletionResult();
          break;
        default:
          this.setDeletionResult(false, 'Une erreur technique est survenue');
          logErrorToServer(error, Vue.$api);
          throw error;
      }
    } finally {
      this._deletionLoadings = this._deletionLoadings.filter(x => x !== jobId);
    }
  }

  fetchJobs() {
    this.debouncedSearch.fn();
  }

  async fetchRecruitersList(organizationCode: string) {
    this._listRecruiters = (await Vue.$api.getAllRecruitersForOrganization(organizationCode)).data;
    this.recruiterFilter = this._listRecruiters.filter(r => r.code === organizationCode)
      .filter(p => !!p.defaultProject?.code)
      .map(p => p.defaultProject?.code || '');
  }

  get membersOfGroups(): UserSummary[] {
    return this._listMembersOfGroups;
  }

  get jobList(): JobSummary[] {
    return this._jobList;
  }

  get job(): JobDetail | null {
    return this._job;
  }

  set job(value: JobDetail | null) {
    this._job = value;
  }

  get jobPage(): JobPage | null {
    return this._jobPage;
  }

  get search() {
    return this._search;
  }

  set search(search) {
    this._search = search;
    this.fetchJobs();
  }

  set recruiterCode(code: string) {
    this._recruiterCode = code;
  }

  setDeletionResult(success: boolean, message: string) {
    this.deletionResult.type = success ? 'success' : 'error';
    this.deletionResult.message = message;
  }

  hideDeletionResult() {
    this.deletionResult.type = '';
    this.deletionResult.message = '';
  }

  get showDeletionResult() {
    return !!this.deletionResult?.type;
  }

  isDeletionLoading(jobId: string): boolean {
    return this._deletionLoadings.includes(jobId);
  }

  get jobDeletable() {
    return true;
  }

  get recruiters() {
    return this._listRecruiters;
  }

  set noOrgaFilter(value) {
    this._noOrgaFilter = value;
    this.fetchJobs();
  }

  get noOrgaFilter() {
    return this._noOrgaFilter;
  }

  set recruiterFilter(value: string[]) {
    this._recruiterFilter = value;
    this.fetchJobs();
  }

  get recruiterFilter(): string[] {
    return this._recruiterFilter;
  }
}
