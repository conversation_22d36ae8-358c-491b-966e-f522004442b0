<template>
  <v-container fluid>
    <v-row class="fill-height" justify="center">
      <v-col cols="12">
        <v-card>
          <template slot="progress">
            <v-overlay absolute>
              <v-progress-circular size="85" color="primary" indeterminate>
                <span>Chargement</span>
              </v-progress-circular>
            </v-overlay>
            <v-progress-linear color="primary" indeterminate/>
          </template>
          <v-card-title>
            <v-row>
              <v-col cols="6">
                <h1>
                  {{ $t('ref.job.list.title') }}
                  <v-menu offset-y>
                    <template v-slot:activator="{ on }">
                      <v-btn id="createJobBtn"
                             text
                             color="primary"
                             class="mr-2 text-center"
                             @click.stop.native
                             v-on="on">
                        <v-icon>add</v-icon>
                        {{ $t('action.create') }}
                      </v-btn>
                    </template>
                    <v-list>
                      <v-list-item id="createSimpleJobBtn"
                                   :to="{
                    name: 'organizations_jobs_edit',
                    params: { organization_code: organization.code, mode: 'createSimpleFromErhgo' }}">
                        <v-list-item-icon>
                          <v-icon color="primary">add</v-icon>
                        </v-list-item-icon>
                        <v-list-item-title>{{ $t('action.createSimpleFromErhgo') }}</v-list-item-title>
                      </v-list-item>

                      <v-list-item
                        :to="{
                        name: 'organizations_jobs_edit',
                        params: { organization_code: organization.code, mode: 'create' }
                      }">
                        <v-list-item-icon>
                          <v-icon color="primary">create</v-icon>
                        </v-list-item-icon>
                        <v-list-item-title>{{ $t('action.createFromEmptyModel') }}</v-list-item-title>
                      </v-list-item>
                      <v-list-item :to="{
                    name: 'organizations_jobs_edit',
                    params: { organization_code: organization.code, mode: 'createFromErhgo' }}">
                        <v-list-item-icon>
                          <v-icon color="primary">library_add</v-icon>
                        </v-list-item-icon>
                        <v-list-item-title>{{ $t('action.createFromErhgo') }}</v-list-item-title>
                      </v-list-item>
                      <v-dialog v-model="jobPicker.showDialog" width="800">
                        <template v-slot:activator="{ on }">
                          <v-list-item v-on="on">
                            <v-list-item-icon>
                              <v-icon color="primary">file_copy</v-icon>
                            </v-list-item-icon>
                            <v-list-item-title>{{ $t('action.createFromAnotherJob') }}</v-list-item-title>
                          </v-list-item>
                        </template>
                        <job-picker-view :data="jobPicker"
                                         for-job-create
                                         @submit="selectJobForTemplate"/>
                      </v-dialog>
                    </v-list>
                  </v-menu>
                </h1>
              </v-col>
            </v-row>
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12" sm="6">
                <v-text-field class="pt-0"
                              v-model="jobSearchService.search"
                              append-icon="search"
                              label="Rechercher"
                              single-line
                              :hint="messageForHint"
                              persistent-hint
                />
              </v-col>
              <v-col cols="12" sm="6">
                <v-select class="pt-sm-0"
                          hint="Choisissez des organisations pour filtrer les postes"
                          persistent-hint
                          multiple
                          clearable
                          :disabled="jobSearchService.noOrgaFilter"
                          v-model="jobSearchService.recruiterFilter"
                          :menu-props="{ offsetY: true }"
                          label="Filtrer par organisation"
                          :items="jobSearchService.recruiters"
                          item-text="title"
                          item-value="code"
                />
              </v-col>
            </v-row>
            <jobs-list
              :selected_job_id="selected_job_id"
              :service="jobSearchService"
              :custom-message="new_profile_title ? `Les modifications du profil de recrutement ${new_profile_title} ont bien été prises en compte.` : ''"
            />
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { OrganizationType } from 'erhgo-api-client';

import JobsList from '@/components/job/JobsList';
import appStore from '@/store';
import ProcessReview from '@/components/mixins/ProcessReview';
import JobPickerView from '@/components/organization/JobPickerView';
import JobPicker from '@/components/organization/JobPicker';
import JobSearchService from './JobSearchService';

export default {
  mixins: [ProcessReview],
  props: {
    selected_job_id: String,
    new_profile_title: String,
  },
  components: {
    JobPickerView, JobsList,
  },
  data() {
    return {
      jobSearchService: new JobSearchService(true),
      jobPicker: new JobPicker(),
      OrganizationType,
      defaultProjectId: null,
    };
  },
  computed: {
    organization() {
      return appStore.getters.organizationLoaded;
    },
    isEnterprise() {
      return this.organization?.organizationType === OrganizationType.ENTERPRISE;
    },
    messageForHint() {
      return this.isEnterprise ? 'Recherche dans le nom du poste et dans le services' : 'Recherche dans le nom du poste et dans le nom de l\'employeur';
    },
  },
  watch: {
    organization: {
      async handler() {
        await this.refreshOrganization();
      },
      deep: true,
    },
  },
  async created() {
    await this.refreshOrganization();
  },
  methods: {
    selectJobForTemplate(o) {
      this.jobPicker.showDialog = false;
      this.$router.push({
        name: 'organizations_jobs_edit',
        params: {organization_code: this.organization.code, job_id: o.job.id, mode: 'createFromJob'},
      });
    },
    async refreshOrganization() {
      this.jobSearchService.recruiterCode = this.organization.code;
      await this.jobSearchService.fetchRecruitersList(this.organization.code);
    },
  },
};
</script>
