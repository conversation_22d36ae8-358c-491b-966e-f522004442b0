<template>
  <v-card>
    <v-card-text>
      <criteria-selector v-model="job.criteriaValues"/>
    </v-card-text>
    <v-card-actions class="d-flex justify-space-between">
      <v-row class="no-gutter">
        <v-col cols="12" md="6" xl="3">
          <v-btn color="grey lighten-5"
                 small
                 @click="$emit('back')">
            <v-icon small
                    left>arrow_back_ios
            </v-icon>
            Étape précédente&nbsp;: Définir les missions
          </v-btn>
        </v-col>
        <v-col cols="12" md="6" xl="3">
          <v-btn
            small
            outlined
            :loading="loadingNext"
            @click="validate(true)">
            <v-icon class="mr-2" small>
              save
            </v-icon>
            Enregistrer et revenir aux postes
          </v-btn>
        </v-col>
        <v-col cols="12" md="6" xl="3">
          <v-dialog persistent width="50%" v-if="typeOfJobIsSimple" v-model="showDialog">
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                id="btnJobToCustomQuestion"
                color="primary"
                small
                :loading="loadingNext"
                v-bind="attrs"
                v-on="on"
                @click="openDialog"
              >
                Ajouter une question spécifique
                <v-icon
                  left>mdi-plus
                </v-icon>
              </v-btn>
            </template>
            <v-card>
              <v-card-title
                class="text-h5 grey lighten-2"
                primary-title
                left
              >
                Définir une question complémentaire
              </v-card-title>
              <v-row no-gutters>
                <v-col>
                  <v-text-field
                    class="px-2"
                    v-model="recruitmentProfileCustomQuestion"
                    label="Question complémentaire"
                    hint="Saisissez une question qui sera soumise au candidat lors de sa candidature."
                    persistent-hint
                    id="customQuestionText"
                  />
                </v-col>
              </v-row>
              <v-divider/>
              <v-card-actions>
                <v-row no-gutters class="justify-space-between">
                  <v-col>
                    <v-btn
                      id="customQuestionClose"
                      color="primary"
                      @click="close"
                      outlined
                    >
                      Fermer
                    </v-btn>
                  </v-col>
                  <v-col class="text-end">
                    <v-btn
                      id="customQuestion"
                      color="primary"
                      :disabled="!recruitmentProfileCustomQuestion.length"
                      @click="validate(false)"
                    >
                      Continuer
                    </v-btn>
                  </v-col>
                </v-row>
              </v-card-actions>
            </v-card>
          </v-dialog>
        </v-col>
        <v-col cols="12" md="6" xl="3">
          <v-btn v-if="typeOfJobIsSimple"
                 id="btnJobToRecruitment"
                 color="primary"
                 small
                 :loading="loadingNext"
                 @click="validate(false)"
          >
            Étape suivante&nbsp;: {{ messageNextStep }}
            <v-icon small
                    left>arrow_forward_ios
            </v-icon>
          </v-btn>
          <v-btn v-if="!typeOfJobIsSimple"
                 color="primary"
                 small
                 :loading="loadingNext"
                 @click="validate(false)">
            Étape suivante&nbsp;: {{ messageNextStep }}
            <v-icon right>arrow_forward_ios</v-icon>
          </v-btn>
        </v-col>
      </v-row>

    </v-card-actions>
  </v-card>
</template>

<script>
import CriteriaSelector from '@/components/criteria/CriteriaSelector';

export default {
  components: {CriteriaSelector},
  props: {
    job: {
      type: Object,
      required: true,
    },
    organization: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      loadingNext: false,
      recruitmentProfileCustomQuestion: '',
      showDialog: false,
    };
  },
  computed: {
    typeOfJobIsSimple() {
      return this.job.jobType === 'SIMPLE';
    },
    messageNextStep() {
      return this.typeOfJobIsSimple ? 'Créer un recrutement' : 'Ajouter des comportements';
    },
  },
  methods: {
    validate(redirectToJobsList) {
      this.loadingNext = true;
      this.$emit('validate', redirectToJobsList, () => {
        this.loadingNext = false;
        if (this.typeOfJobIsSimple && !redirectToJobsList) {
          this.$router.push({
            name: 'recruitment_create',
            params: {
              organization_code: this.organization.code,
              job: this.job,
            },
          });
        }
      }, this.recruitmentProfileCustomQuestion);
    },
    close() {
      this.showDialog = false;
    },
    openDialog() {
      this.showDialog = true;
    },
  },
};
</script>

<style scoped>
ul {
  list-style-type: none;
}

</style>
