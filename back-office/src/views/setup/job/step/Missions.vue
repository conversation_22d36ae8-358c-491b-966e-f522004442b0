<template>
  <v-form ref="form" lazy-validation @submit.prevent="validate" :value="true">
    <v-card>
      <v-card-text>
        <mission
          ref="missions"
          v-for="(mission, index) in missions"
          v-model="missions[index]"
          :isFirstMission="index === 0"
          :key="mission.vueKey"
          :isMultiple="missions.length > 1"
          :last="index === (missions.length - 1)"
          :submitted="submitted"
          :disabled="disabled"
          :all-contexts="allContexts"
          @error="$emit('error')"
          @missionInitialized="newMission(index)"
          @remove="missions.splice(index, 1)"
          @valid="addEmptyMissionIfRequired"
          :id="`mission${index}`"
          :simple-mode="simpleMode"
        />

        <v-alert :value="showError"
                 outlined
                 color="error"
                 icon="warning">
          {{ $t('form.mission.message.global') }}
        </v-alert>
      </v-card-text>
      <v-card-actions class="d-flex justify-space-between">
        <v-btn color="grey lighten-5"
               small
               @click.stop="back">
          <v-icon small
                  left>arrow_back_ios
          </v-icon>
          Étape précédente : Informations
        </v-btn>
        <v-btn
          small
          outlined
          :loading="loadingNext"
          @click="validate(true)">
          <v-icon class="mr-2" small>
            save
          </v-icon>
          Enregistrer et revenir aux postes
        </v-btn>
        <v-btn id="nextStepToCriteria"
               color="primary"
               small
               :loading="loadingNext"
               @click.stop="validate(false)">
          Étape suivante : Donnez vos critères
          <v-icon right>arrow_forward_ios</v-icon>
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-form>
</template>

<script>
import Mission from './mission/Mission.vue';
import _ from 'lodash';

export default {
  components: {
    Mission,
  },
  props: {
    job: {
      type: Object,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      vueKey: 0,
      missions: [],
      loadingNext: false,
      submitted: false,
      showError: false,
    };
  },
  computed: {
    allContexts() {
      return _.uniqBy(this.missions.flatMap(m => m.contextsForCategory || []).flatMap(cfc => cfc.contexts || []), 'id');
    },
    jobId() {
      return this.job.id;
    },
    simpleMode() {
      return this.job.jobType === 'SIMPLE';
    },
  },
  created() {
    this.updateJobId(this.job.id);
  },
  watch: {
    jobId(newJobId) {
      this.updateJobId(newJobId);
    },
  },
  methods: {
    updateJobId(newJobId) {
      if (newJobId) {
        this.missions = this.job.missions.map(m => ({
          ...m,
          jobId: newJobId,
          vueKey: this.vueKey++,
          activitiesIds: m.activities.map(a => a.id),
        }));
      }
      this.addEmptyMissionIfRequired();
    },
    back() {
      this.$emit('back');
    },
    newMission(index) {
      this.$refs.missions.filter((m, i) => i !== index).forEach(m => m.collapse());
    },
    async validate(redirect) {
      this.showError = false;
      this.submitted = true;
      if (this.$refs.form.validate() && this.$refs.missions.reduce((o, m) => o && !!(m.isValid || m.isEmpty), true)) {
        this.loadingNext = true;
        try {
          await this.missions.filter(a => a.title)
            .reduce(async (promise, mission) => {
              // This line will wait for the last async function to finish.
              // The first iteration uses an already resolved Promise
              // so, it will immediately continue.
              await promise;
              let result;
              // eslint-disable-next-line @typescript-eslint/no-unused-vars
              const {vueKey, ...rest} = mission;
              rest.contextsForCategory = rest.contextsForCategory.map(cfc => {
                const noContextForCategory = cfc.noContextForCategory;
                const contextsIds = cfc.contexts.map(c => c.id);
                const categoryId = cfc.category.id;
                return {noContextForCategory, contextsIds, categoryId};
              });
              if (mission.id) {
                result = await this.$api.updateMission(mission.id, rest);
              } else {
                result = await this.$api.createNewMission(rest);
                this.$set(mission, 'id', result.data.id);
              }
            }, Promise.resolve());
          if (this.job.modifiable) {
            this.job.state = 'MISSIONS_PROVIDED';
          }
          this.$emit('validate', redirect);
          this.$emit('input', this.missions);
        } catch (e) {
          this.logError(e);
          this.$emit('error');
        } finally {
          this.loadingNext = false;
        }
      } else {
        this.showError = true;
        setTimeout(() => {
          this.showError = false;
        }, 3000);
      }
    },
    addEmptyMissionIfRequired() {
      if (!this.simpleMode && !this.disabled && !this.missions.find(m => !m.title)) {
        this.missions.push({jobId: this.jobId, vueKey: this.vueKey++});
      }
    },
  },
};
</script>
