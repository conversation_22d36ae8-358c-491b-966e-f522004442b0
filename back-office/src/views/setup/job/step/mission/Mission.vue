<template>
  <v-card flat>
    <v-card-text>
      <v-expansion-panels v-model="panelState">
        <v-expansion-panel :flat="panelState !== 0" :key="1">
          <v-expansion-panel-header>
            <v-row justify="start">
              <v-col cols="10">
                <!-- click.self.stop => avoid collapse/expand on textfield click -->
                <v-text-field
                  @click.stop
                  @keyup.space.prevent
                  class="h1 missionTitle"
                  v-model="mission.title"
                  :rules="[v => !!v || isTitleOptional ||
                                    $t('form.mission.message.title.required'),
                                    v => v.length <= 100 || $t('form.mission.message.title.toolong')]"
                  :counter="100"
                  :label="$t('form.mission.title.label')"
                  @input="titleSelected"
                  :prepend-icon="(!disabled && !deletingMission && !simpleMode)?'cancel':undefined"
                  @click:prepend="remove"
                  shrink
                  required>
                  <v-progress-circular
                    :size="10"
                    :width="1"
                    indeterminate
                    slot="prepend"
                    v-if="deletingMission"
                  />
                </v-text-field>
              </v-col>
              <v-col v-if="score !== null" class="text-right">
                Mission <span class="text-h6">{{$t(`masteryLevels.${getLevelFromScore(score)}.label`)}}</span>
              </v-col>
            </v-row>
          </v-expansion-panel-header>
          <v-expansion-panel-content>
            <v-icon slot="actions" v-if="missionInitialized" color="primary" x-large>$vuetify.icons.expand</v-icon>
            <template slot="actions" v-else>&nbsp;</template>
            <v-col id="activitySelector">
              <v-card v-if="missionInitialized">
                <v-card-text>
                  <autocomplete
                    v-if="!disabled"
                    v-model="selectedActivities"
                    empty
                    searchRestUrlApi="/api/odas/activityLabel/search"
                    :label="$t('form.mission.activity')"
                    close-on-select
                    item-value="id"
                    :hide-details="false"
                    :rules="[v => !!mission.activitiesIds.length || $t('form.mission.message.activity.required')]"
                    :createItem="createActivity()"
                  >
                    <template v-slot:listItem="slotProps">
                      <activity-list-item :activity="slotProps.listItem"/>
                    </template>
                  </autocomplete>
                  <v-list two-line>
                    <template v-for="(activity, index) in reversedSelectedActivities">
                      <v-list-item :key="activity.id">
                        <activity-list-item :activity="activity"/>
                        <v-list-item-action v-if="!disabled">
                          <v-icon @click="removeSelectedActivity(activity.id)">cancel</v-icon>
                        </v-list-item-action>
                        <v-list-item-action v-if="!disabled">
                          <v-icon @click="editActivity(activity.id)">edit</v-icon>
                        </v-list-item-action>
                      </v-list-item>
                      <v-divider :key="`${activity.id}-div`" v-if="index !== reversedSelectedActivities.length - 1"/>
                    </template>
                  </v-list>
                </v-card-text>
              </v-card>
            </v-col>
            <v-col>
              <contexts-selector
                v-model="mission.contextsForCategory"
                v-if="missionInitialized && !simpleMode"
                :is-multiple="isMultiple"
                :disabled="disabled"
                :all-contexts="allContexts"
                @score="newVal => score = newVal"
                @missingCategories="newVal => missingCategories = newVal"
                @missing-categories="newVal => missingCategories = newVal"
              />
            </v-col>
          </v-expansion-panel-content>
        </v-expansion-panel>
      </v-expansion-panels>

      <v-alert :value="submitted && (missingCategories.length > 0)"
               outlined
               color="error"
               icon="warning">
        {{`${$t('form.mission.message.context.missing')} ${missingCategories.map(c => c.category.title).join(', ')}`}}
      </v-alert>
      <activity-edit-modal v-model="showCreateActivityModal"
                           @closed="closeActivityModal"
                           @submit="onActivityUpdated"
                           :title="newActivityTitle"
                           :edit-activity-id="editActivityId"
                           :key="editActivityId"
                           :activity-type="ActivityType.JOB"/>
    </v-card-text>
  </v-card>
</template>

<script>
import { ActivityType } from 'erhgo-api-client';

import appStore from '@/store';

import Autocomplete from '@/components/common/crud/Autocomplete.vue';
import ActivityListItem from '@/views/repository/activity/ActivityListItem.vue';
import ProcessReview from '@/components/mixins/ProcessReview';
import ActivityEditModal from '@/views/repository/activity/ActivityEditModal.vue';
import ContextsSelector from './ContextsSelector.vue';

export default {
  mixins: [ProcessReview],
  components: {
    Autocomplete,
    ActivityListItem,
    ContextsSelector,
    ActivityEditModal,
  },
  props: {
    value: {
      type: Object,
      required: true,
    },
    last: {
      type: Boolean,
      default: false,
    },
    isFirstMission: {
      type: Boolean,
      required: true,
    },
    isMultiple: {
      type: Boolean,
      required: true,
    },
    submitted: {
      type: Boolean,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    allContexts: {
      type: Array,
    },
    simpleMode: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      ActivityType,
      editActivityId: null,
      panelState: 0,
      alert: '',
      missionInitialized: false,
      mission: {
        vueKey: null,
        title: '',
        activitiesIds: [],
        contextsForCategory: [],
      },
      selectedActivities: [],
      deletingMission: false,
      missingCategories: [],
      score: null,
      showCreateActivityModal: false,
      newActivityTitle: null,
    };
  },
  created() {
    this.mission = Object.assign(this.mission, this.value);
    this.missionInitialized = !!this.value.title;
    this.updateSelectedActivities();
  },
  computed: {
    isTitleOptional() {
      // At least one mission must be provided, and a title must be provided to missions for which user set activities
      return !this.isFirstMission && !this.selectedActivities.length;
    },
    isValid() {
      return !!(this.mission.title && this.mission.activitiesIds.length && (this.simpleMode || (this.mission.contextsForCategory.length && !this.missingCategories.length)));
    },
    isEmpty() {
      return !(this.mission.title || this.mission.activitiesIds.length || this.mission.contextsForCategory.length);
    },
    reversedSelectedActivities() {
      return [...this.selectedActivities].reverse();
    },
  },
  watch: {
    missionInitialized() {
      this.$emit('missionInitialized');
    },
    mission: {
      handler() {
        this.$emit('input', this.mission);
        if (this.isValid) {
          this.$emit('valid');
        }
      },
      deep: true,
    },
    selectedActivities() {
      this.mission.activitiesIds = this.selectedActivities.map(a => a.id);
      appStore.commit('addObservedJobActivities', this.selectedActivities);
    },
  },
  methods: {
    onActivityUpdated(activity) {
      this.editActivityId = null;
      const activities = this.selectedActivities.filter(value => value.id !== activity.id);
      this.selectedActivities = [...activities, activity];
      this.closeActivityModal();
    },
    editActivity(activityId) {
      this.editActivityId = activityId;
      this.showCreateActivityModal = true;
    },
    closeActivityModal() {
      this.editActivityId = null;
      this.showCreateActivityModal = false;
    },
    async remove() {
      if (this.disabled) {
        return;
      }
      try {
        if (!this.last) {
          this.deletingMission = true;
          if (this.mission.id) {
            await this.$api.deleteMission(this.mission.id);
          }
          this.$emit('remove');
        } else {
          this.missionInitialized = false;
          this.mission.title = '';
          this.mission.activitiesIds = [];
          this.mission.contextsForCategory = [];
          this.selectedActivities = [];
          this.score = null;
        }
      } catch (e) {
        this.logError(e);
        this.$emit('error');
      } finally {
        this.deletingMission = false;
      }
    },
    collapse() {
      this.panelState = null;
    },
    titleSelected() {
      this.missionInitialized = true;
      this.panelState = 0;
    },
    updateSelectedActivities() {
      if (this.value && this.value.activities && this.value.activities.length) {
        this.selectedActivities = [...this.value.activities];
      }
    },
    removeSelectedActivity(id) {
      this.selectedActivities = this.selectedActivities.filter(activity => activity.id !== id);
    },
    createActivity() {
      return searchValue => {
        this.newActivityTitle = searchValue;
        this.showCreateActivityModal = true;
      };
    },
  },
};
</script>

<style scoped>

.v-input.h1 {
  font-size: 2.1em !important;
}

</style>
