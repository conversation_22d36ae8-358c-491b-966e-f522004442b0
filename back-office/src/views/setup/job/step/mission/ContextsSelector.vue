<template>
  <div id="contextSelector">
    <v-card>
      <v-card-text>
        <v-row>
          <v-col md="6" cols="12">
            <autocomplete
              v-if="!disabled"
              v-model="selectedContexts"
              empty
              searchRestUrlApi="/api/odas/context/list"
              :label="$t('form.mission.context')"
              :itemSubTextFunction="contextSubText"
              :hide-details="false"
              close-on-select
              :rules="[v => !!v.length || $t('form.global.required')]"
              :createItem="createContext()"
            />
          </v-col>
          <v-col md="6" cols="12">
            <v-select
              no-data-text="Aucun autre contexte"
              hide-details
              v-if="!disabled && isMultiple"
              v-model="reusedContexts"
              :items="otherMissionContexts"
              item-text="title"
              multiple
              return-object
              :menu-props="{ offsetY: true }"
              :label="$t('form.mission.contextFromJob')">
              <template v-slot:item="data">
                <v-list-item-content>
                  <v-list-item-title>{{ data.item.title }}</v-list-item-title>
                  <v-list-item-subtitle>
                    {{ contextSubText(data.item) }}
                  </v-list-item-subtitle>
                </v-list-item-content>
              </template>
            </v-select>
          </v-col>
        </v-row>
        <v-list three-line v-show="selectedContexts.length || categoriesWithoutContext.length">
          <template v-for="( {category, contexts, noContextForCategory}, index) in contextsForCategory">
            <v-list-item :key="category.id">
              <v-list-item-content>
                <v-list-item-title>{{ category.code }} - {{ category.title }} {{labelsForMaxContext[category.id]}}
                </v-list-item-title>
                <v-list-item-subtitle class="font-italic pl-1">
                  <v-btn
                    class="no-context"
                    :disabled="disabled"
                    v-if="!contexts || !contexts.length"
                    @click="switchNoContext(category)"
                    :color="noContextForCategory?'primary':undefined"
                  >
                    {{$t('form.mission.no-context')}}
                  </v-btn>
                  <v-tooltip top color="black"  v-for="context in contexts" :key="context.id">
                    <template v-slot:activator="{ on }">
                      <v-chip class="ma-1"
                              v-on="on"
                              :close="!disabled"
                              color="primary"
                              text-color="white"
                              @click:close="selectedContexts = selectedContexts.filter(c => c !== context)">
                            <span  class="v-chips__label">{{context.categoryLevel.title}} - {{context.title}}</span>
                      </v-chip>
                      </template>
                    {{context.categoryLevel.title}} - {{context.title}}
                  </v-tooltip>
                </v-list-item-subtitle>
              </v-list-item-content>
            </v-list-item>
            <v-divider :key="`${category.id}-div`" v-if="index !== categories.length - 1"/>
          </template>
        </v-list>
      </v-card-text>
    </v-card>
    <context-create-modal v-model="showCreateContextModal" @closed="closeContextModal" @created="onContextCreated"
                          :initial-title="newContextTitle"/>
  </div>
</template>

<script>
import _ from 'lodash';
import Autocomplete from '@/components/common/crud/Autocomplete.vue';
import appStore from '@/store';
import ProcessReview from '@/components/mixins/ProcessReview';
import ContextCreateModal from '@/views/repository/context/ContextCreateModal.vue';

export default {
  components: {Autocomplete, ContextCreateModal},
  mixins: [ProcessReview],
  props: {
    value: {
      type: Array,
      required: true,
    },
    isMultiple: {
      type: Boolean,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    allContexts: {
      type: Array,
    },
  },
  data() {
    return {
      selectedContexts: [],
      categories: [],
      // touched flag is used to avoid sending 'input' event just after creation, with empty contexts (=> reset contexts in case of update)
      touched: false,
      showCreateContextModal: false,
      newContextTitle: null,
      noContextForCategories: [],
      reusedContexts: [],
    };
  },
  async created() {
    this.updateSelectedContexts();
    await this.fetchCategories();
  },
  computed: {
    otherMissionContexts() {
      return this.allContexts ? this.allContexts.filter(context => !this.selectedContexts.some(value => value.id === context.id)) : [];
    },
    contextsForCategory() {
      return this.categories.map(
        category => {
          const contexts = this.selectedContexts.filter(context => context.categoryLevel.category.id === category.id);
          const noContextForCategory = this.noContextForCategories.includes(category.id);
          return {category, contexts, noContextForCategory};
        });
    },

    categoriesWithoutContext() {
      return this.categories.filter(c => this.noContextForCategories.includes(c.id));
    },
    labelsForMaxContext() {
      const maxScoreContextForCategory = this.selectedContexts
        .reduce((labels, nextContext) => {
          const categoryId = nextContext.categoryLevel.category.id;
          const previousContext = labels[categoryId];
          labels[categoryId] = (!previousContext || (previousContext.categoryLevel.score < nextContext.categoryLevel.score)) ? nextContext : previousContext;
          return labels;
        }, {});
      return _.mapValues(maxScoreContextForCategory, c => `${c.title ? ` : ${c.title}` : ''}`);
    },

  },
  watch: {
    selectedContexts(newValue, oldValue) {
      if (oldValue.length !== this.selectedContexts.length) {
        this.touched = true;
      }
      // Remove from noContextForCategories list all categories having at least one context set
      this.noContextForCategories = this.noContextForCategories.filter(id => !newValue.filter(context => context.categoryLevel.category.id === id).length);
    },

    contextsForCategory(newVal) {
      if (this.selectedContexts.length) {
        this.$emit('score', this.getScore(this.selectedContexts));
      }
      if (this.touched) {
        const missingCategories = this.contextsForCategory.filter(cat => !cat.noContextForCategory && !cat.contexts.length);
        this.$emit('input', newVal);
        this.$emit('missingCategories', missingCategories);
      }
    },

    reusedContexts(newValue) {
      if (newValue && newValue.length) {
        this.selectedContexts = _.uniqBy([...this.selectedContexts, ...newValue], 'id');
        this.reusedContexts = [];
      }
    },
  },
  methods: {
    contextSubText(item) {
      return `${item.categoryLevel.category.title} ${item.categoryLevel && item.categoryLevel.description ? `- ${item.categoryLevel.description}` : ''}`;
    },
    closeContextModal() {
      this.showCreateContextModal = false;
    },
    onContextCreated(context) {
      this.selectedContexts.push(context);
      this.closeContextModal();
    },
    switchNoContext(category) {
      this.touched = true;
      if(this.noContextForCategories.includes(category.id)) {
        this.noContextForCategories = this.noContextForCategories.filter(cId => cId !== category.id);
      } else {
        this.noContextForCategories = [...this.noContextForCategories, category.id];
      }
    },
    async fetchCategories() {
      if (!appStore.state.categories.length) {
        this.categories = (await this.$axios.get('/api/odas/category/list', {
          params: {
            page: 0,
            size: 100,
            by: 'code',
            direction: 'ASC',
          },
        })).data.content;
        appStore.commit('setCategories', this.categories);
      }
      this.categories = appStore.state.categories;
    },
    updateSelectedContexts() {
      if (this.value) {
        this.selectedContexts = this.value.flatMap(c => c.contexts);
        this.noContextForCategories = this.value.filter(c => c.noContextForCategory).map(c => c.category.id);
      }
    },
    createContext() {
      return (searchValue) => {
        this.newContextTitle = searchValue;
        this.showCreateContextModal = true;
      };
    },
  },
};
</script>

<style lang="scss">
.v-list-item__subtitle {
  max-height: 80px;
  -webkit-line-clamp: unset !important;
  overflow-y: auto;
  white-space: inherit;
}

.v-list-item__title {
  min-height: 24px;
}

.v-chips__label {
  overflow: hidden;
  text-overflow: ellipsis;
}

.v-chips {
  display: inline-block;
}
</style>
