<template>
  <v-form @submit="validate" ref="form">
    <v-card id="jobDetails" class="px-2">
      <v-card-text>
        <v-row>
          <v-col cols="6">
            <v-card flat
                    color="secondary">
              <v-card-title>
                <div>
                  <h3 class="white--text text-h5 mb-0">{{ $t('form.job.create.title') }}</h3>
                </div>
              </v-card-title>
              <v-card-text class="white black--text">
                <v-text-field v-model="job.title"
                              :rules="requiredMax(150)"
                              :label="$t('form.job.create.organization.title')"
                              :counter="150"
                              :loading="loadingJob"
                              id="jobTitle"
                              required/>
                <v-text-field v-model="job.service"
                              :rules="max(50)"
                              :label="$t('form.job.create.organization.service')"
                              :counter="50"
                              :loading="loadingJob"
                              id="serviceTitle"
                />
                <vuetify-geo-places
                  id="searchCity"
                  label="Localisation du poste"
                  persistent-hint
                  @input="updateCity"
                  no-data-text="Aucune correspondance"
                  :value="job.location"
                  :show-radius="true"
                  outlined
                  :rules="[v => !!v || $t('form.global.required')]"
                />
                <v-select
                  :items="recruiters"
                  clearable
                  v-model="job.recruiterCode"
                  label="Organisation recrutant pour ce poste"
                  persistent-hint
                  :disabled="disabled"
                  v-if="organization.organizationType !== OrganizationType.ENTERPRISE && organization.organizationType !== OrganizationType.SOURCING"
                  :rules="[v => !!v || 'Veuillez préciser l\'organisme recruteur']"
                />
                <v-select
                  :items="employers"
                  clearable
                  v-model="job.employerCode"
                  label="Employeur final du poste"
                  hint="Seuls les employeurs associés à l'organisation recrutant sont accessibles"
                  persistent-hint
                  v-if="organization.organizationType !== OrganizationType.ENTERPRISE"
                />
                <v-textarea v-model="job.description"
                            :loading="loadingJob"
                            :label="$t('form.job.create.organization.description')"/>
              </v-card-text>
            </v-card>
          </v-col>
          <v-col cols="6">
            <v-card flat
                    color="primary">
              <v-card-title>
                <div>
                  <h3 class="text-h5 mb-0">Espace erhgo</h3>
                </div>
              </v-card-title>
              <v-card-text class="white black--text">
                <v-text-field v-if="job.id !== -1"
                              :value="job.createdBy"
                              :loading="loadingJob"
                              :label="$t('form.job.create.odas.owner')"
                              disabled/>
                <v-combobox v-model="job.observators"
                            :items="observatorsItems"
                            :loading="loadingJob"
                            :label="$t('form.job.create.odas.observators')"
                            chips
                            clearable
                            multiple
                            v-if="!isSimple"
                >
                  <template v-slot:selection="data">
                    <v-chip class="ma-1" :input="data.selected"
                            close
                            @click:close="removeObservator(data.item)">
                      <strong>{{ data.item }}</strong>&nbsp;
                    </v-chip>
                  </template>
                </v-combobox>
                <v-menu ref="menu"
                        :close-on-content-click="false"
                        v-model="menu"
                        :nudge-right="40"
                        transition="scale-transition"
                        offset-y
                        v-if="!isSimple"
                        max-width="290px"
                        min-width="290px">
                  <template v-slot:activator="{ on }">
                    <v-text-field v-on="on"
                                  :value="job.observationDate | formatDate"
                                  label="Date d'observation"
                                  prepend-icon="event"
                                  :loading="loadingJob"
                                  readonly/>
                  </template>
                  <v-date-picker v-model="isoDate"
                                 locale="fr"
                                 first-day-of-week="1"
                                 no-title
                                 @input="menu = false"/>
                </v-menu>
                <v-text-field :label="$t('form.job.create.erhgoOccupation.title')"
                              v-if="job.erhgoOccupation"
                              disabled
                              :value="job.erhgoOccupation.title"
                              id="jobErhgoOccupation"/>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>
      <v-card-actions class="d-flex justify-space-between">
        <v-btn
          small
          outlined
          :loading="loadingJob || loadingNext"
          @click="validate(true)">
          <v-icon class="mr-2" small>
            save
          </v-icon>
          Enregistrer et revenir aux postes
        </v-btn>
        <v-btn
          v-if="isFinished"
          small
          outlined
          :loading="loadingNext"
          @click="unvalidate()">
          <v-icon class="mr-2" small>
            next_week
          </v-icon>
          Mettre en brouillon
        </v-btn>
        <v-btn
          small
          id="nextStep"
          color="primary"
          :loading="loadingJob || loadingNext"
          @click="validate(false)">
          <v-icon class="mr-2" small>
            navigate_next
          </v-icon>
          Étape suivante&nbsp;: Définir les missions
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-form>
</template>

<script>
import { JobType, OrganizationType } from 'erhgo-api-client';
import VuetifyGeoPlaces from 'odas-plugins/VuetifyGeoPlaces';

export default {
  components: {VuetifyGeoPlaces},
  props: {
    organization: {
      type: Object,
      required: true,
    },
    job: {
      type: Object,
      required: true,
    },
    recruitmentProfileCount: {
      type: Number,
      required: true,
    },
    loadingJob: {
      type: Boolean,
      required: false,
      default: false,
    },
    disabled: Boolean,
  },
  data() {
    return {
      observatorsItems: [],
      menu: false,
      loadingNext: false,
      employers: [],
      recruiters: [],
      OrganizationType,
      pagination: {
        page: 1,
        sortBy: [],
        descending: [false],
        rowsPerPage: 10,
      },
      rowsPerPage: [10, 25, 50, 100],
    };
  },
  computed: {
    isoDate: {
      get() {
        if (!this.job.observationDate) {
          return null;
        }
        return (new Date(this.job.observationDate)).toISOString();
      },
      set(isoDate) {
        if (!isoDate) {
          this.$set(this.job, 'observationDate', null);
        }
        this.$set(this.job, 'observationDate', (new Date(isoDate)).toISOString());
      },
    },
    recruiterCode() {
      return this.job.recruiterCode;
    },
    isFinished() {
      return (this.job.state === 'PUBLISHED'
        || this.job.state === 'REEVALUATION_NOT_FINISHED'
        || this.job.state === 'FINISHED') && this.recruitmentProfileCount === 0;
    },
    isSimple() {
      return this.job.jobType === JobType.SIMPLE;
    },
  },
  async created() {
    await this.refreshRecruiters();
    await this.refreshEmployers();
  },
  watch: {
    async recruiterCode(newVal, oldVal) {
      if (newVal && newVal !== oldVal) {
        await this.refreshEmployers();
      }
    },
  },
  methods: {
    updateCity(place) {
      if (!!place) {
        const {city, citycode, postcode, departmentCode, regionName, latitude, longitude, radiusInKm} = place;
        this.job.location = {city, citycode, postcode, departmentCode, regionName, latitude, longitude, radiusInKm};
      } else {
        this.job.location = null;
      }
    },
    removeObservator(item) {
      this.job.observators.splice(this.job.observators.indexOf(item), 1);
      this.job.observators = [...this.job.observators];
    },
    max(maxLength) {
      return [v => (!v || v.length <= maxLength) || this.$t('form.global.maxLength', {maxLength})];
    },
    requiredMax(maxLength) {
      return [v => !!v || this.$t('form.global.required'), ...this.max(maxLength)];
    },
    validate(redirect) {
      if (this.$refs.form.validate()) {
        this.loadingNext = true;
        if (this.job.modifiable) {
          this.job.state = 'INFOS_PROVIDED';
        }
        this.$emit('validate', redirect, this.job, () => {
          this.loadingNext = false;
        });
      }
    },
    unvalidate() {
      this.job.state = 'LISTED';
      this.$emit('unvalidate', this.job);
    },
    async refreshEmployers() {
      if (this.recruiterCode) {
        this.employers = (await this.$api.findOrganizationsPaginatedAndFilteredByProperty(this.pagination.page - 1, this.pagination.rowsPerPage,
          this.pagination.sortBy[0], !this.pagination.descending[0] ? 'ASC' : 'DESC', undefined, Array.of(this.recruiterCode))).data.content
          .map(e => ({
            text: e.title,
            value: e.code,
          })).sort((a, b) => a.text.localeCompare(b.text));
      }
    },
    async refreshRecruiters() {
      this.recruiters = (await this.$api.getAllRecruiters())
        .data
        .map(e => ({
          text: e.title,
          value: e.code,
        }))
        .sort((a, b) => a.text.localeCompare(b.text));
    },
  },
};
</script>
