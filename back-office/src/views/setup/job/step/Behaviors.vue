<template>
  <v-card>
    <v-card-text>

      <autocomplete v-model="selection"
                    searchRestUrlApi="/api/odas/behavior/list?by=title"
                    :label="$t('form.job.create.behavior.add')"
                    itemSubText="description"
                    close-on-select
                    :size="200"
                    autocompleteOnFocus
                    empty>
        <template v-slot:listItem="slotProps">
          <v-list-item-content>
            <v-list-item-title>{{ slotProps.listItem.title }}</v-list-item-title>
            <v-list-item-subtitle class="font-italic pl-1">
              {{ $t(`behaviorCategory.${slotProps.listItem.behaviorCategory}`) }}
            </v-list-item-subtitle>
          </v-list-item-content>
        </template>
      </autocomplete>
      <template v-for="(behavior, index) in selection">
        <v-list-item :key="behavior.id">
          <v-list-item-content>
            <v-list-item-title>{{ behavior.title }}</v-list-item-title>
          </v-list-item-content>
          <v-list-item-action>
            <v-btn text
                   icon
                   color="primary"
                   class="mr-2 text-center"
                   @click.native="selection = selection.filter(b => b.id !== behavior.id)"
            >
              <v-icon>delete</v-icon>
            </v-btn>
          </v-list-item-action>
        </v-list-item>
        <v-divider :key="index"/>
      </template>
    </v-card-text>
    <v-card-actions class="d-flex justify-space-between">
      <v-btn color="grey lighten-5"
             small
             @click="$emit('back')">
        <v-icon small
                left>arrow_back_ios
        </v-icon>
        Étape précédente&nbsp;: Définir les critères
      </v-btn>
      <v-btn
        small
        outlined
        :loading="loadingNext"
        @click="validate(true)">
        <v-icon class="mr-2" small>
          save
        </v-icon>
        Enregistrer et revenir aux postes
      </v-btn>
      <v-btn color="primary"
             small
             :loading="loadingNext"
             @click="validate(false)">
        Étape suivante&nbsp;: Donner vos préconisations
        <v-icon right>arrow_forward_ios</v-icon>
      </v-btn>
    </v-card-actions>
  </v-card>
</template>

<script>
import Autocomplete from '@/components/common/crud/Autocomplete.vue';

export default {
  components: {Autocomplete},
  props: {
    job: {
      type: Object,
      required: true,
    },
    behaviors: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      selection: [],
      loadingNext: false,
    };
  },
  watch: {
    behaviors() {
      if (this.selection.length === 0) {
        Array.from(this.behaviors).forEach((item) => {
          this.selection.push(item);
        });
      }
    },
  },
  methods: {
    validate(redirect) {
      this.loadingNext = true;
      this.job.behaviors = this.selection;
      if (this.job.modifiable) {
        this.job.state = 'BEHAVIORS_PROVIDED';
      }
      this.$emit('validate', redirect, () => {
        this.loadingNext = false;
      });
    },
  },
};
</script>

<style>
</style>
