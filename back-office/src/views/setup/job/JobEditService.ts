import SafeService from 'odas-plugins/SafeService';
import {Behavior, CriteriaValue, JobDetail, SaveJobCommand, WorkingTime} from 'erhgo-api-client';
import Vue from 'vue';
import {v4 as uuid} from 'uuid';

export default class JobEditService extends SafeService {

  async saveJob(command: SaveJobCommand) {
    await this.safeCall(async () => {
      await Vue.$api.saveJob(command);
    });
  }

  async saveBehavior(jobId: string, behaviors: Behavior[]) {
    await this.safeCall(async () => {
      await Vue.$api.saveBehavior(jobId, behaviors.map(value => value.id));
    });
  }

  async publish(jobId: string, redirectToJobsList: boolean, recruitmentProfileCustomQuestion: string) {
    await this.safeCall(async () => {
      await Vue.$api.publish(jobId, {recruitmentProfileCustomQuestion}, redirectToJobsList);
    });
  }

  getJobCommand(job: JobDetail) {
    return {...job, id: job.id ? job.id : uuid()};
  }

  async saveCriteria(jobId: string, criteriaValues: CriteriaValue[]) {
    let saved = false;
    await this.safeCall(async () => {
      await Vue.$api.updateCriteriaForJob(jobId, criteriaValues.filter(c => !!c).map(c => c.code));
      saved = true;
    });
    return saved;
  }

  async addWorkingTime(jobId: string, workingTimeValues: WorkingTime[]) {
    await this.safeCall(async () => {
      await Vue.$api.addWorkingTimeTypeForJob(jobId, workingTimeValues);
    });
  }
}
