<template>
  <v-container class="pa-0 mx-2 my-3">
    <v-row no-gutters class="flex-nowrap">
      <v-col class="pr-2" cols="4" align-self="center">
        <label class="v-label">{{initial.title}}</label>
      </v-col>
      <v-col align-self="center" cols="3" class="text-center">
        <v-btn-toggle v-model="isOptional" mandatory>
          <v-btn small elevation="12" text :value="false" :class="{ active: !isOptional }" :disabled="disabled">
            Obligatoire
          </v-btn>
          <v-btn small elevation="12" text :value="true" :class="{ active: isOptional }" :disabled="disabled">
            Optionnel
          </v-btn>
        </v-btn-toggle>
      </v-col>
      <v-col align-self="stretch" cols="5">
        <v-select
          v-if="isOptional"
          persistent-hint
          dense
          hint="Sélectionner la modalité d'apprentissage"
          :label="optionalModalityValue ? null : 'Sélectionner une modalité d\'acquisition'"
          :items="modalities"
          v-model="optionalModalityValue"
          :error-messages="(showError && !optionalModalityValue) ? 'Veuillez préciser une modalité' : null"
          :disabled="disabled"
          class="pt-0 mt-0"
        />
        <v-select
          v-if="isContext && !isOptional"
          persistent-hint
          dense
          hint="Sélectionner la question à soumettre au candidat"
          :items="questionsList"
          :disabled="disabled"
          item-text="title"
          item-value="id"
          return-object
          no-data-text="Aucune question configurée"
          v-model="question">
          <template v-slot:item="data">
            <slot name="listItem" :listItem="data.item">
              <v-list-item-content class="py-1">
                <v-list-item-title>{{ data.item.title }}</v-list-item-title>
                <v-list-item-subtitle
                  class="text-caption pl-4"
                  v-for="(answer, index) in Object.values(data.item.suggestedAnswers).filter(a => !!a)"
                  :key="index">
                    {{ answer}}
                </v-list-item-subtitle>
              </v-list-item-content>
            </slot>
          </template>
          <template v-slot:append-outer v-if="!disabled">
            <v-btn icon @click="createQuestionForContexts">
              <v-icon color="primary">add_box</v-icon>
            </v-btn>
            <v-btn icon v-if="question" @click="editQuestionForContexts">
              <v-icon color="primary">edit</v-icon>
            </v-btn>
          </template>
        </v-select>
      </v-col>
      <v-col class="align-self-center">
        <v-icon color="green" :class="{'hidden': !saved}">mdi-check</v-icon>
      </v-col>
    </v-row>
    <question-for-contexts-edit-modal
      :context="initial"
      :question-for-context-id="questionForContextId"
      v-model="showEditQuestionForContextsModal"
      @closed="showEditQuestionForContextsModal = false"
      @submit="refreshAndSelect"
      :key="questionForContextId"
    />
  </v-container>
</template>

<script>
import _ from 'lodash';
import { AcquisitionModality } from 'erhgo-api-client';
import QuestionForContextsEditModal from '../../repository/questionForContexts/QuestionForContextsEditModal';

export default {
  components: {QuestionForContextsEditModal},
  props: {
    optionalModality: {
      type: String,
    },
    initial: {
      type: Object,
      required: true,
    },
    profileId: {
      type: String,
      required: true,
    },
    jobId: {
      type: String,
      required: true,
    },
    optionalType: {
      type: String,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    questionProp: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      questionForContextId: null,
      showEditQuestionForContextsModal: false,
      optionalModalityValue: null,
      modalities: Object.values(AcquisitionModality)
        .map(m => ({value: m, text: this.$t(`ref.capacity.modalities.${m}`)})),
      initializing: false,
      showError: false,
      saved: false,
      question: null,
      isOptional: false,
      questionsList: [],
    };
  },
  async created() {
    await this.resetOptional();
  },
  watch: {
    async isOptional(newValue, previousValue) {
      if (!this.initializing) {
        const optionalType = this.optionalType.toUpperCase();
        const optionalId = this.initial.id;
        // delete if not optional anymore
        // Do not delete if no modality is set (ie. server is not aware of that optional)
        if (this.optionalModalityValue && !newValue && previousValue) {
          this.optionalModalityValue = null;
          await this.$api.deleteOptionals(this.jobId, this.profileId, [{optionalId, optionalType}]);
          this.setAsSaved();
          this.$emit('optional-deleted', optionalId);
        } else if (!this.optionalModalityValue && newValue) {
          // Set a default value when switching to optional
          this.optionalModalityValue = AcquisitionModality.SELF_LEARNING;
        }
        if (previousValue && !newValue) {
          this.resetQuestionsList();
        }
        this.$emit('touch');
      }
    },
    optionalModalityValue(newModality) {
      if (newModality) {
        this.updateModality(this.initial.id, newModality);
      }
    },
    question() {
      if (!this.initializing) {
        this.updateContextQuestion();
      }
    },
  },
  computed: {
    isContext() {
      return this.optionalType === 'context';
    },
  },
  methods: {
    createQuestionForContexts() {
      this.questionForContextId = null;
      this.showEditQuestionForContextsModal = true;
    },
    editQuestionForContexts() {
      this.questionForContextId = this.question.id;
      this.showEditQuestionForContextsModal = true;
    },
    async refreshAndSelect(id) {
      this.questionForContextId = id;
      this.showEditQuestionForContextsModal = false;
      await this.resetQuestionsList(true);
      this.question = this.questionsList.filter(value => value.id === id)[0];
    },
    async resetQuestionsList(force) {
      if (force || (this.isContext && !this.isOptional && !this.questionsList.length)) {
        this.questionsList = (await this.$api.listContextsQuestionsByContextId(this.initial.id)).data;
      }
    },
    async resetOptional() {
      this.initializing = true;
      this.question = this.questionProp;
      this.optionalModalityValue = this.optionalModality;
      this.isOptional = !!this.optionalModality;
      // nextTick to ensure watcher were called; then we can consider this component as initialized
      this.$nextTick(() => {
        this.initializing = false;
      });
      await this.resetQuestionsList();
    },
    async updateModality(optionalId, acquisitionModality) {
      const optionalType = this.optionalType.toUpperCase();
      this.optionalModalityValue = acquisitionModality;

      if (!this.initializing) {
        const newOptional = {acquisitionModality, optionalId, optionalType};
        await this.$api.addOptionals(this.jobId, this.profileId, [newOptional]);
        this.$emit('touch');
        this.setAsSaved();
        newOptional[this.optionalType] = {id: optionalId};
        this.$emit('optional-added', newOptional);
      }
    },

    validate() {
      this.showError = this.isOptional && !this.optionalModalityValue;
      return !this.showError;
    },
    toggle(mandatory) {
      this.isOptional = !mandatory;
    },
    setAsSaved() {
      this.saved = true;
      setTimeout(() => {
        this.saved = false;
      }, 5000);
    },
    updateContextQuestion: _.debounce(async function __() {
      const updateQuestionCommand = {
        contextId: this.initial.id,
        questionId: this.question.id,
      };
      await this.$api.setContextQuestionForProfile(this.jobId, this.profileId, updateQuestionCommand);
      this.setAsSaved();
    }, 500),
  },
};
</script>

<style scoped lang="scss">
@import '../../../style/colors.scss';

.active.v-btn.v-btn--disabled {
  color: #e0e0e0 !important;
}

.active {
  color: white !important;
  background-color: $mdi-primary !important;
}

.hidden {
  visibility: hidden;
}
</style>

