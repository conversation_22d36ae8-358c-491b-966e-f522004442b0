<template>
  <v-container fluid>
    <v-row no-gutters v-if="isEmployer">
      <v-breadcrumbs>
        <v-breadcrumbs-item
          exact
          :to="{
              name: 'employer_repository',
              params: {organization_code: organizationCode},
            }">
          Employeurs
          <v-icon>chevron_right</v-icon>
        </v-breadcrumbs-item>
        <v-breadcrumbs-item disabled>{{ employerTitle }}
        </v-breadcrumbs-item>
      </v-breadcrumbs>
    </v-row>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            <h1>
              <v-icon large color="primary">mdi-account-multiple
              </v-icon>
              {{ $t('ref.employee.list') }}
              <!-- FIXME 812 -->
            </h1>
          </v-card-title>
          <v-card-text>
            <v-data-table :headers="isSourcing ? sourcingHeaders : headers"
                          :items="isSourcing ? items : items.content"
                          :sort-by.sync="pagination.sortBy"
                          :page.sync="pagination.page"
                          :sort-desc.sync="pagination.descending"
                          :items-per-page.sync="pagination.rowsPerPage"
                          :footer-props="{
                            'items-per-page-options': rowsPerPage,
                            'items-per-page-text': 'Nb lignes par page',
                          }"
                          :loading="loading"
                          class="elevation-15"
                          item-key="id"
                          :server-items-length="items.totalNumberOfElements"
            >
              <v-progress-linear slot="progress"
                                 indeterminate/>
              <template slot="no-data">
                <v-alert value
                         v-if="!loading"
                         outlined
                         color="error"
                         icon="warning">
                  Aucun résultat disponible
                </v-alert>
                <div v-else class="text-center">
                  <v-progress-circular indeterminate/>
                </div>
              </template>
              <template v-slot:item="props" v-if="isSourcing">
                <tr>
                  <td class="text-left">{{ props.item.fullname }}</td>
                  <td class="text-left">{{ props.item.email }}</td>
                  <td class="text-left">
                    <v-chip class="ma-1" :color="props.item.enabled ? 'green accent-1' : 'grey lighten-1'">
                      {{ props.item.enabled ? 'Activé' : 'Désactivé' }}
                    </v-chip>
                  </td>
                  <td class="text-left">
                    <v-btn
                      v-if="props.item.enabled"
                      color="red"
                      @click="openDeactivateDialog(props.item)"
                    >
                      Désactiver
                    </v-btn>
                    <v-btn
                      v-else
                      color="success"
                      @click="activateUser(props.item.id)"
                    >
                      Réactiver
                    </v-btn>
                  </td>
                </tr>
              </template>
              <template v-slot:item="props" v-else>
                <tr>
                  <td class="text-left">{{ props.item.firstName }}</td>
                  <td class="text-left">{{ props.item.lastName }}</td>
                  <td class="text-left">{{ props.item.email }}</td>
                  <td class="text-left">
                    <v-chip class="ma-1" :color="props.item.enabled ? 'green accent-1' : 'grey lighten-1'">
                      {{ props.item.enabled ? 'Activé' : 'Désactivé' }}
                    </v-chip>
                  </td>
                </tr>
              </template>

              <template v-slot:footer.page-text="props">
                Lignes de {{ props.pageStart }} à {{ props.pageStop }} sur {{ props.itemsLength }}
              </template>
            </v-data-table>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-dialog v-model="deactivateDialog" persistent max-width="500px">
      <v-card>
        <v-card-title class="text-h5">
          Désactiver l'utilisateur {{ userToDeactivateFullName}}
        </v-card-title>
        <v-card-text>
          <p>Veuillez choisir l'utilisateur qui remplacera <b>{{ userToDeactivateFullName}}</b> :</p>
          <v-select
            v-if="availableReplacements.length > 0"
            v-model="replacementUser"
            :items="availableReplacements"
            :item-text="item => `${item.fullname} (${item.email})`"
            item-value="id"
            label="Utilisateur remplaçant"
            outlined
            :loading="loadingReplacements"
          />
          <v-alert
            v-else
            outlined
            type="error"
          >
            Si aucun remplaçant n'est sélectionné et que cet utilisateur est manager sur un recrutement actif, il n'y aura plus de manager désigné pour ce recrutement et personne ne sera notifié.
          </v-alert>
        </v-card-text>
        <v-divider />
        <v-card-actions>
          <v-spacer/>
          <v-btn color="grey" text @click="closeDeactivateDialog">Annuler</v-btn>
          <v-btn color="red" :loading="deactivateLoading" @click="confirmDeactivateUser">Confirmer</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import appStore from '@/store';
import jsog from 'jsog';
import _ from 'lodash';
import { OrganizationType } from 'erhgo-api-client';

export default {
  data() {
    return {
      items: [],
      organizationCode: '',
      employerCode: '',
      employerTitle: '',
      loading: true,
      loadingReplacements: false,
      deactivateLoading: false,
      pagination: {
        page: 1,
        sortBy: [], // By default, do not apply sorting. This allows search ranking to do its job.
        descending: [false],
        rowsPerPage: 10,
      },
      rowsPerPage: [10, 25, 50, 100, 200],
      headers: [
        {
          text: 'Prénom',
          align: 'left',
          sortable: false,
        },
        {
          text: 'Nom',
          align: 'left',
          sortable: false,
        },
        {
          text: 'Email',
          align: 'left',
          sortable: false,
        },
        {
          text: 'Activé',
          align: 'left',
          sortable: false,
        },
      ],
      sourcingHeaders: [
        {
          text: 'Nom',
          align: 'left',
          sortable: false,
        },
        {
          text: 'Email',
          align: 'left',
          sortable: false,
        },
        { text: 'Statut', align: 'left', sortable: false },
        { text: 'Actions', align: 'left', sortable: false },
      ],
      deactivateDialog: false,
      userToDeactivate: null,
      availableReplacements: [],
      replacementUser: null,
    };
  },
  async mounted() {
    if (this.isSourcing) {
      await this.getSourcingDataFromApi();
    } else {
      this.getDataFromApi();
    }
  },
  watch: {
    pagination: {
      handler: _.debounce(async function () {
        if (this.isSourcing) {
          await this.getSourcingDataFromApi();
        } else {
          this.getDataFromApi();
        }
      }, 200),
      deep: true,
    },
  },
  computed: {
    isEmployer() {
      return !!this.employerCode;
    },
    isSourcing() {
      return appStore.state.organizationLoaded?.organizationType === OrganizationType.SOURCING;
    },
    userToDeactivateFullName() {
      return `${this.userToDeactivate?.fullname} (${this.userToDeactivate?.email})`;
    },
  },
  async created() {
    this.organizationCode = appStore.state.organizationLoaded.code;
    this.employerCode = this.$route.params.employer_code;
    if (this.employerCode) {
      this.employerTitle = (await this.$api.getOrganizationByCode(this.employerCode)).data.title;
    }
  },
  methods: {
    getDataFromApi() {
      this.loading = true;
      this.$axios.get(`/api/odas/users/list/${this.employerCode || this.organizationCode}`, {
        params: {
          page: this.pagination.page - 1,
          size: this.pagination.rowsPerPage,
        },
      })
        .then(result => {
          this.items = jsog.decode(result.data);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    async getSourcingDataFromApi() {
      this.loading = true;
      await this.$api.getSourcingUsers(this.employerCode || this.organizationCode)
        .then(result => this.items = result.data)
        .finally(() => this.loading = false);
    },
    openDeactivateDialog(user) {
      this.userToDeactivate = user;
      this.replacementUser = null;
      this.availableReplacements = [];
      this.deactivateDialog = true;
      if (this.isSourcing) {
        this.loadAvailableReplacements();
      }
    },
    closeDeactivateDialog() {
      this.deactivateDialog = false;
      this.userToDeactivate = null;
      this.replacementUser = null;
      this.availableReplacements = [];
    },
    async loadAvailableReplacements() {
      this.loadingReplacements = true;
      try {
        const response = await this.$api.getSourcingUsers(this.organizationCode);
        this.availableReplacements = response.data.filter(user => user.id !== this.userToDeactivate.id && user.enabled);
      }  finally {
        this.loadingReplacements = false;
      }
    },
    async confirmDeactivateUser() {
      this.deactivateLoading = true;
      const command = {
        userId: this.userToDeactivate.id,
        replacementUserId: this.replacementUser,
      };
      try {
        await this.$api.deactivateUserSourcing(command);
        this.closeDeactivateDialog();
        this.fetchData();
      } finally {
        this.deactivateLoading = false;
      }
    },
    async activateUser(userId) {
      await this.$api.activateUserSourcing(userId);
      this.fetchData();
    },

    async fetchData() {
      this.isSourcing ? await this.getSourcingDataFromApi() : this.getDataFromApi();
    },
  },
};
</script>

<style scoped>

</style>
