<template>
  <v-col>
    <v-row v-if="confirmed">
      <v-alert type="info" outlined icon="mdi-lightbulb-on-outline">
        <strong>Le fichier a été correctement envoyé.</strong><br /> <a
        href="https://app.slack.com/client/T014Y0P3BN0/C08C32QJEF5"
        target="_blank">Un compte rendu sera produit sur slack, canal
        #suivi_import_csv.</a>
      </v-alert>
    </v-row>
    <template v-else>
      <v-row v-if="service.showAPIError">
        <v-alert outlined type="error" dense>
          Une erreur est survenue, merci de contacter le support technique.
        </v-alert>
      </v-row>
      <v-row v-if="service.showImportFormatError">
        <v-alert outlined type="error" dense>
          Votre fichier est mal formaté, merci de vous reférer au template.
        </v-alert>
      </v-row>
      <v-row>
        <v-file-input v-model="file" label="Choisissez un fichier... (Types supportés : .csv)" persistent-hint
                      hint="Import de recrutements via .csv (RESPECTANT LE FORMAT DÉFINI)" />
        <v-btn
          color="primary"
          class="mt-3"
          @click="submitFile"
          :loading="service.loading"
          :disabled="!file"
        >Importer les recrutements
        </v-btn>
      </v-row>
    </template>
  </v-col>
</template>

<script>
import { RecruitmentListService } from '@/views/setup/recruitment/list/RecruitmentListService';

export default {
  name: 'offers-importer',
  data() {
    return {
      file: null,
      confirmed: false,
    };
  },
  props: {
    service: {
      required: true,
      type: RecruitmentListService,
    },
  },
  methods: {
    async submitFile() {
      await this.service.importOffers(this.file);
      this.confirmed = !this.service.showImportFormatError;

    },
  },
};
</script>
