import SafeService from 'odas-plugins/SafeService';
import Vue from 'vue';
import {Recruitment} from 'erhgo-api-client';

export default class RecruitmentJobService extends SafeService {
  private _recruitmentList: Recruitment[] = [];

  constructor(private _jobId: string) {
    super();
  }

  async fetchRecruitmentList() {
    await this.safeCall(async () => {
      this._recruitmentList = (await Vue.$api.listRecruitmentsForJob(this._jobId)).data;
    });
  }

  get recruitmentList() {
    return this._recruitmentList;
  }
}
