import Vue from 'vue';
import SafeService from 'odas-plugins/SafeService';
import { SaveRecruitmentCommand } from 'erhgo-api-client';
import { Route } from 'vue-router';

export abstract class RecruitmentService extends SafeService {

  protected constructor(
    protected readonly organizationCode: string | undefined,
    protected _selectedOrganizationCodes: string[],
    private _withNewCandidaturesOnly: boolean,
    private _withOpenRecruitmentOnly: boolean,
    private _internal: boolean,
    private _publishedAfterDate: Date | null,
    private _publishedBeforeDate: Date | null,
    private _organizationTypeFilter?: string,
  ) {
    super();
  }

  public goToCreateRecruitmentPage(): Promise<Route | undefined> {
    return new Promise((resolve: ((route?: Route) => void), reject) => {
      Vue.$router.push({
        name: 'recruitment_create',
        params: {
          organization_code: this.organizationCode!,
        },
      }, resolve, reject);
    });
  }

  get publishedAfterDate(): Date | null {
    return this._publishedAfterDate;
  }

  set publishedAfterDate(value: Date | null) {
    this._publishedAfterDate = value;
    this.refreshUrl();
    this.debouncedRefresh.fn();
  }

  get publishedAfterISODate(): string | null {
    return this.publishedAfterDate ? this.publishedAfterDate.toISOString() : null;
  }

  set publishedAfterISODate(value: string | null) {
    this.publishedAfterDate = value ? new Date(value) : null;
  }

  get publishedBeforeDate(): Date | null {
    return this._publishedBeforeDate;
  }

  set publishedBeforeDate(value: Date | null) {
    this._publishedBeforeDate = value;
    this.refreshUrl();
    this.debouncedRefresh.fn();
  }

  get publishedBeforeISODate(): string | null {
    return this.publishedBeforeDate ? this.publishedBeforeDate.toISOString() : null;
  }

  set publishedBeforeISODate(value: string | null) {
    this.publishedBeforeDate = value ? new Date(value) : null;
  }

  set internal(value: boolean) {
    this._internal = value;
    this.refreshUrl();
    this.debouncedRefresh.fn();
  }

  get internal(): boolean {
    return this._internal;
  }

  set withNewCandidaturesOnly(value: boolean) {
    this._withNewCandidaturesOnly = value;
    this.refreshUrl();
    this.debouncedRefresh.fn();
  }

  get withNewCandidaturesOnly(): boolean {
    return this._withNewCandidaturesOnly;
  }

  set withOpenRecruitmentOnly(value: boolean) {
    this._withOpenRecruitmentOnly = value;
    this.refreshUrl();
    this.debouncedRefresh.fn();
  }

  get withOpenRecruitmentOnly(): boolean {
    return this._withOpenRecruitmentOnly;
  }

  get selectedOrganizationCodes(): string[] {
    return this._selectedOrganizationCodes;
  }

  set selectedOrganizationCodes(value: string[]) {
    this._selectedOrganizationCodes = value;
    this.debouncedRefresh.fn();
  }

  get organizationTypeFilter(): string | undefined {
    return this._organizationTypeFilter;
  }

  set organizationTypeFilter(value: string | undefined) {
    this._organizationTypeFilter = value;
    this.refreshUrl();
    this.debouncedRefresh.fn();
  }

  refreshUrl(): void {
    Vue.$router.push({
      name: Vue.$router.currentRoute.name || undefined,
      params: Vue.$router.currentRoute.params,
      hash: Vue.$router.currentRoute.hash,
      query: {
        ...Vue.$router.currentRoute.query,
        newOnly: this._withNewCandidaturesOnly ? 'true' : 'false',
        openOnly: this._withOpenRecruitmentOnly ? 'true' : 'false',
        internal: this._internal ? 'true' : 'false',
        publishedAfterDate: this._publishedAfterDate ? this._publishedAfterDate.toISOString().split('T')[0] : undefined,
        publishedBeforeDate: this._publishedBeforeDate ? this._publishedBeforeDate.toISOString().split('T')[0] : undefined,
        organizationTypeFilter: this._organizationTypeFilter !== null ? 'SOURCING' : undefined,
      },
    }).catch(e => {
      if (e?.name !== 'NavigationDuplicated') {
        throw e;
      }
    });
  }

  async createRecruitment(createRecruitmentCommand: SaveRecruitmentCommand) {
    return (await this.safeCall(async () => Vue.$api.createRecruitment(createRecruitmentCommand))).data;
  }

  async updateRecruitment(recruitmentId: number, createRecruitmentCommand: SaveRecruitmentCommand) {
    await this.safeCall(async () => Vue.$api.updateRecruitment(recruitmentId, createRecruitmentCommand));
  }

  // @ts-ignore
  abstract get debouncedRefresh();
}
