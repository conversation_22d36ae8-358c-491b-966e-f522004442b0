<template>
  <v-form ref="form">
    <v-card flat>
      <v-card-text class="py-0">
        <v-row>
          <v-col cols="8" class="py-0">
            <span v-if="typeContractCategoriesLabel" class="text-body-2">
              Critères relatifs à la durée de temps de travail associés au poste&nbsp;:
              <strong>{{ typeContractCategoriesLabel }}</strong>
            </span>
            <v-autocomplete
              label="Choisissez le type de contrat"
              :items="contractTypeOptions"
              v-model="announcement.typeContract"
              @change="selectWorkContractDurationUnit"
              cache-items
              clearable
              flat
              hide-no-data
              :rules="[v => (v && !!v.length) || $t('form.global.required')]"
            />
          </v-col>
        </v-row>
        <v-row v-if="announcement.typeContract === 'CDD'">
          <v-col cols="4">
            <v-text-field
              v-model.number="announcement.workContractDuration"
              label="Durée du CDD"
              required
              type="number"
            />
          </v-col>
          <v-col cols="3" offset="1">
            <v-select
              :items="workContractDurationUnitOptions"
              v-model="announcement.workContractDurationUnit"
              cache-items
              flat
              hide-no-data
              hide-details
              required/>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <v-card flat>
      <v-card-text class="py-0">
        <v-row>
          <v-col cols="12">
            <v-row>
              <v-col cols="8">
                <span v-if="workingTimeLabel" class="text-body-2">
                  Critères relatifs au temps de travail portés par le poste&nbsp;: <strong>{{
                    workingTimeLabel
                  }}</strong>
                </span>
                <v-select
                  v-else
                  label="Choisissez le type de temps de travail"
                  :items="workingTimeValuesList"
                  v-model="workingTimeValues"
                  @change="updateWorkingTime"
                  multiple
                  clearable
                  flat
                  hide-no-data
                  :rules="[v => (v && !!v.length) || $t('form.global.required')]"
                />
                <v-subheader class="pl-0">Nombre d'heures de travail par semaine :</v-subheader>
                <v-slider
                  v-model="announcement.workingWeeklyTime"
                  min="1"
                  max="48"
                  :thumb-size="48"
                  thumb-color="primary"
                  thumb-label="always"
                  class="ml-3"
                />
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <v-card flat>
      <v-card-text class="py-0">
        <v-row>
          <v-col cols="8">
            <v-menu :close-on-content-click="false"
                    v-model="displayDatePicker"
                    :nudge-right="40"
                    transition="scale-transition"
                    offset-y
                    max-width="290px"
                    min-width="290px">
              <template v-slot:activator="{ on }">
                <v-text-field v-on="on"
                              :value="announcement.startingDate | formatDate"
                              label="Date d'embauche souhaitée"
                              prepend-icon="event"
                              readonly/>
              </template>
              <v-date-picker v-model="isoDate"
                             locale="fr"
                             first-day-of-week="1"
                             no-title
                             @input="displayDatePicker = false"/>
            </v-menu>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <v-card flat>
      <v-card-text class="py-0">
        <v-row no-gutters>
          <v-col cols="8">
            <vuetify-geo-places
              :label="'Localisation du recrutement'"
              persistent-hint
              :hint="distanceHint"
              @input="updateCity"
              no-data-text="Aucune correspondance"
              :value="announcement.location"
              :showRadius="true"
              outlined
              :rules="[v => !!v || $t('form.global.required')]"
            />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    <v-card flat>
      <v-card-text class="py-0">
        <v-row>
          <v-col cols="8">
            <v-text-field
              v-model.number="announcement.baseSalary"
              append-outer-icon="mdi-currency-eur"
              label="salaire annuel brut minimum"
              required
              type="number"
            />
            <v-text-field
              v-model.number="announcement.maxSalary"
              append-outer-icon="mdi-currency-eur"
              label="salaire annuel brut maximum"
              type="number"
            />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <v-card flat>
      <v-card-text class="py-0">
        <v-row>
          <v-col cols="8">
            <v-checkbox
              v-model="announcement.hideSalary"
              color="primary"
              label="Cacher le salaire dans l'annonce"/>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <v-card flat>
      <v-card-text class="py-0">
        <v-row>
          <v-col cols="8">
            <v-subheader class="pl-0">Descriptif du poste :</v-subheader>
            <vue-editor class="mt-2"
                        v-model="announcement.description"/>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <v-card flat>
      <v-card-text class="py-0">
        <v-row>
          <v-col cols="8">
            <v-text-field v-model.trim="announcement.externalUrl" label="Saisissez une URL externe"/>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <v-card flat>
      <v-card-text class="py-0">
        <v-row>
          <v-col cols="8">
            <v-subheader class="pl-0">Descriptif de l'organisation&nbsp;:</v-subheader>
            <vue-editor class="mt-2"
                        v-model="announcement.organizationDescription"/>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <v-btn color="primary" @click="next">Continuer</v-btn>
  </v-form>
</template>

<script>

import VuetifyGeoPlaces from 'odas-plugins/VuetifyGeoPlaces';
import GeoLocationService from 'odas-plugins/GeoLocationService';
import JobEditService from '../../job/JobEditService';

export default {
  components: {VuetifyGeoPlaces},
  props: {
    announcement: {
      type: Object,
      required: true,
    },
    job: {
      type: Object,
      required: true,
    },
    contractTypeOptions: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      displayDatePicker: false,
      jobEditService: JobEditService,
      workingTimeValues: [],
    };
  },
  async created() {
    this.jobEditService = new JobEditService();
  },
  computed: {
    distanceHint() {
      const distanceLabel = this.isDistanceKnown ? `La distance entre le poste (${this.jobLocation.city})
            et la ville sélectionnée est de ${this.distance} km.` : '';
      const radiusLabel = this.jobRadius ? `(Le rayon sur le poste est de
            ${this.jobRadius}km).` : '';
      return `${distanceLabel} ${radiusLabel}`;
    },
    workingTimeValuesList() {
      return [
        {text: 'Temps complet', value: 'FULL_TIME'},
        {text: 'Temps partiel', value: 'PART_TIME'},
      ];
    },
    typeContractCategoriesLabel() {
      const labels = {PERMANENT: 'CDI', PRO: 'Contrat pro', TEMPORARY: 'Contrat précaire'};
      return this.job.typeContractCategories.map(c => labels[c]).join(', ');
    },
    workingTimeLabel() {
      const labels = {FULL_TIME: 'Temps complet', PART_TIME: 'Temps partiel'};
      return this.job.workingTimes.map(c => labels[c]).join(', ');
    },
    jobLocation() {
      return this.job.location;
    },
    jobRadius() {
      return this.jobLocation?.radiusInKm;
    },
    distance() {
      return GeoLocationService.getDistanceBetweenPointsInKm(this.jobLocation, this.announcement.location);
    },
    isDistanceKnown() {
      return !!this.distance || this.distance === 0;
    },
    workContractDurationUnitOptions() {
      return [
        {value: 'MONTH', text: 'mois'},
        {value: 'WEEK', text: 'semaines'},
      ];
    },
    isoDate: {
      get() {
        if (!this.announcement.startingDate) {
          return null;
        }
        return (new Date(this.announcement.startingDate)).toISOString();
      },
      set(isoDate) {
        if (!isoDate) {
          this.$set(this.announcement, 'startingDate', null);
        }
        this.$set(this.announcement, 'startingDate', (new Date(isoDate)).toISOString());
      },
    },
  },
  methods: {
    next() {
      if (this.$refs.form.validate()) {
        this.$emit('next');
      }
    },
    selectWorkContractDurationUnit() {
      if (this.announcement.typeContract === 'CDD') {
        this.announcement.workContractDurationUnit = 'MONTH';
      } else {
        delete this.announcement.workContractDurationUnit;
        delete this.announcement.workContractDuration;
      }
    },
    updateCity(place) {
      if (!!place) {
        const {city, citycode, postcode, departmentCode, regionName, latitude, longitude, radiusInKm} = place;
        this.announcement.location = {
          city,
          citycode,
          postcode,
          departmentCode,
          regionName,
          latitude,
          longitude,
          radiusInKm,
        };
      } else {
        this.announcement.location = null;
      }
    },
    updateWorkingTime() {
      this.jobEditService.addWorkingTime(this.job.id, this.workingTimeValues);
    },
  },
};
</script>

