<template>
  <v-card class="mb-1" flat>
    <v-card-text>
      <v-form ref="form">
        <v-autocomplete :items="jobsList"
                        :label="label"
                        item-text="title"
                        @change="handleInput"
                        v-model="job"
                        cache-items
                        clearable
                        flat
                        hide-no-data
                        hide-details
                        return-object
                        :rules="[v => !!(job && job.id) || $t('form.global.required')]"/>
      </v-form>
    </v-card-text>
  </v-card>
</template>

<script>
export default {
  props: {
    value: {
      type: Object,
    },
    jobsList: {
      type: Array,
      required: true,
    },
    label: {
      type: String,
    },
  },
  data() {
    return {
      job: null,
    };
  },
  created() {
    this.job = {...this.value};
  },
  watch: {
    value(newValue) {
      this.job = {...newValue};
    },
  },
  methods: {
    handleInput() {
      this.$emit('input', this.job);
    },
  },
};
</script>
