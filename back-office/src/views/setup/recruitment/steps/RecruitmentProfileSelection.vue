<template>
  <v-card class="mb-1" flat>
    <v-card-text>
      <v-form ref="form">
        <v-autocomplete :items="profilesList"
                        :label="label"
                        item-text="title"
                        @change="handleInput"
                        v-model="profile"
                        clearable
                        flat
                        hide-no-data
                        hide-details
                        return-object
                        :rules="[v => !!(profile && profile.id) || $t('form.global.required')]"/>
      </v-form>
    </v-card-text>
    <v-btn color="primary"
           :disabled="value == null"
           @click="next">
      <v-icon class="mr-1">arrow_downward</v-icon>
      Continuer
    </v-btn>
  </v-card>
</template>

<script>
export default {
  props: {
    value: {
      type: Object,
    },
    profilesList: {
      type: Array,
      required: true,
    },
    label: {
      type: String,
    },
  },
  data() {
    return {
      profile: null,
    };
  },
  watch: {
    profilesList(newValue) {
      if (newValue.length === 1) {
        [this.profile] = newValue;
        this.$emit('input', this.profile);
      }
    },
    value() {
      this.refreshProfile();
    },
  },
  created() {
    this.refreshProfile();
  },
  methods: {
    handleInput() {
      this.$emit('input', this.profile);
    },
    next() {
      if (this.$refs.form.validate()) {
        this.$emit('next');
      }
    },
    refreshProfile() {
      this.profile = {...this.value};
    },
  },
};
</script>
