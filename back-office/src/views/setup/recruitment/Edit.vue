<template>
  <v-container fluid>
    <v-col class="fill-height">
      <v-card>
        <v-card-title>
          <v-col cols="12">
            <h2>
              <v-icon large
                      color="primary">mdi-receip
              </v-icon>
              {{
                announcement.id != null ? $t('ref.recruitment.edit') :
                  $t('ref.recruitment.create')
              }}
              : {{ job ? job.title : '' }}
            </h2>
          </v-col>
        </v-card-title>
        <v-card-text>
          <v-col cols="12">
            <v-stepper v-model="currentStep"
                       vertical>
              <v-stepper-step :editable="jobEditable"
                              :complete="currentStep > 1"
                              step="1">
                Sélection du poste à ouvrir au recrutement
              </v-stepper-step>

              <v-stepper-content step="1">
                <job-selection v-model="job"
                               label="Pour commencer, sélectionnez le poste qui fait l'objet de ce recrutement"
                               :jobsList="jobsList"
                               @next="currentStep++"/>
                <v-row>
                  <v-col>
                    <v-btn color="primary"
                           :disabled="job == null"
                           @click="currentStep++">
                      <v-icon class="mr-1">arrow_downward</v-icon>
                      Continuer
                    </v-btn>
                  </v-col>
                </v-row>
              </v-stepper-content>

              <v-stepper-step :editable="job != null && jobEditable"
                              :complete="currentStep > 2"
                              step="2">
                Sélection du profil de recrutement à utiliser
              </v-stepper-step>

              <v-stepper-content step="2">
                <profile-selection id="profileSelection"
                                   v-model="announcement.recruitmentProfile"
                                   :profiles-list="profilesList"
                                   label="Ensuite selectionnez le profile de recrutement qui fait l'objet de ce recrutement."
                                   @next="currentStep++"/>
              </v-stepper-content>

              <v-stepper-step :editable="job != null"
                              :complete="currentStep > 3"
                              step="3">
                Compléter les informations de l'annonce
              </v-stepper-step>

              <v-stepper-content step="3">
                <definition
                  v-if="currentStep > 2"
                  :job="job"
                  :announcement="announcement"
                  :contract-type-options="contractTypeOptions"
                  @next="currentStep++"/>
              </v-stepper-content>

              <v-stepper-step :complete="currentStep > 4"
                              step="4"
              >
                Notifications sur recrutement
              </v-stepper-step>

              <v-stepper-content step="4">
                <v-card class="mb-5">
                  <v-card-title primary-title>
                    Sélectionnez les utilisateurs de l'organisation qui seront informés par mail de chaque candidature.
                  </v-card-title>
                  <v-row>
                    <v-col cols="12">
                      <v-autocomplete :items="adminItems" item-text="name" item-value="id"
                                      multiple deletable-chips small-chips chips
                                      v-model="announcement.usersIdToNotify"
                                      label="Choisissez les personnes à prévenir"/>
                    </v-col>
                    <v-col>
                      <v-btn color="primary"
                             @click="currentStep++">
                        Continuer
                      </v-btn>
                    </v-col>
                  </v-row>
                </v-card>
              </v-stepper-content>

              <v-stepper-step :complete="currentStep > 5"
                              :step="5">
                Prévisualisation de l’annonce
              </v-stepper-step>

              <v-stepper-content :step="5">
                <v-card class="mb-5">
                  <v-card-title primary-title>
                    <div>
                      <h2>{{ this.job != null ? this.job.title : '' }}</h2>
                      <span class="subheading">À {{
                          this.organization != null ?
                            this.organization.title
                            : ''
                        }}</span>
                    </div>
                  </v-card-title>
                  <v-card-text>
                    <p><b>Type de contrat&nbsp;:</b> {{ contractType }}</p>
                    <p><b>Temps de travail&nbsp;:</b>{{ announcement.workingWeeklyTime }}h/semaine</p>
                    <p><b>Fourchette de rémunération&nbsp;:</b> {{ this.displayedSalary }} </p>
                    <p><b>Date d'embauche souhaitée&nbsp;: </b>{{ announcement.startingDate | formatDate }}</p>
                    <p><b>Lieu de travail&nbsp;: </b>{{ announcementCity }}, {{ announcementPostcode }}</p>
                    <p><b>Rayon autour du lieu de travail&nbsp;: </b>{{ announcementRadius }}km</p>
                    <p><b>Cacher le salaire dans l'annonce&nbsp;: </b>{{ announcement.hideSalary ? 'Oui' : 'Non' }}</p>
                    <p><b>Le poste&nbsp;:</b></p>
                    <div class="box"
                         v-html="announcement.description"/>
                    <p class="mt-3"><b>URL externe associée&nbsp;: </b> <a
                      :href="announcement.externalUrl">{{ announcement.externalUrl }}</a>
                    </p>
                    <p class="mt-3"><b>L'entreprise&nbsp;:</b></p>
                    <div class="box"
                         v-html="announcement.organizationDescription"/>
                  </v-card-text>
                </v-card>
                <v-btn color="grey"
                       class="mr-2"
                       @click="saveAsDraft">Enregistrer en tant que brouillon
                </v-btn>
                <v-btn color="primary"
                       @click="publish">Publier
                </v-btn>
              </v-stepper-content>

              <v-stepper-step :complete="currentStep > 6"
                              :step="6">
                Information de publication
              </v-stepper-step>

              <v-stepper-content :step="6">
                <template v-if="announcement.state === 'PUBLISHED'">
                  <v-card class="pa-3 mb-5 primary lighten-3 white--text elevation-0">
                    <p>L'annonce est maintenant visible sur la plateforme erhgo et des personnes
                      peuvent d'ors et déjà candidater.</p>
                    <p>Pour suivre et traiter les réponses à l'annonce et gérer votre campagne,
                      rendez-vous dans la rubrique Recrutement du menu.</p>
                  </v-card>

                  <h2>Lien de candidature</h2>
                  <v-card class="pa-3 mb-5 elevation-0 grey lighten-5">
                    <p>Voici le lien de l'annonce sur la plateforme erhgo,
                      vous pouvez le copier et le coller dans l'annonce créée
                      sur une autre plate-forme (Indeed, pôle emploi, etc.)
                      afin d'inciter les candidats à postuler via erhgo.</p>
                    <p>
                    <span class="text-right link pr-1 shrink align-self"
                          @click="copy">
                      <v-icon>mdi-content-duplicate</v-icon>
                      <strong>Cliquer ici pour copier le lien de candidature à diffuser.</strong>
                    </span>
                    <div :class="{'pb-3': true, 'invisible': !copyHrefLinkStatus}">
                      <v-icon :color="copyHrefLinkStatus && copyHrefLinkStatus.success ? 'green':'red'">
                        {{copyHrefLinkStatus && copyHrefLinkStatus.success ? 'check':'close'}}
                      </v-icon>&nbsp;{{copyHrefLinkStatus && copyHrefLinkStatus.message}}&nbsp;
                    </div>
                  </v-card>
                </template>

                <v-card v-if="announcement.state === 'DRAFT'"
                        class="mb-5"
                        height="200px">
                  <p>L'annonce est enregistrée en tant que brouillon. </p>

                </v-card>
                <v-btn color="primary"
                       :to="{
                        name: 'recruitments_list',
                        params: {organization_code: this.organization.code},
                      }">Revenir
                  à la liste des recrutements
                </v-btn>
              </v-stepper-content>
            </v-stepper>
          </v-col>
        </v-card-text>
      </v-card>
    </v-col>
    <errorDialog v-model="showAPIError"/>
  </v-container>
</template>

<script>
import appStore from '@/store';
import ErrorDialog from '@/components/common/ErrorDialog.vue';
import {baseUrl} from 'odas-plugins/base-url';

import JobSelection from './steps/JobSelection.vue';
import Definition from './steps/Definition.vue';
import ProfileSelection from './steps/RecruitmentProfileSelection.vue';
import JobSearchService from '../job/JobSearchService';
import {RecruitmentListService} from './list/RecruitmentListService';
import {logErrorToServer} from 'odas-plugins';
import Vue from 'vue';
import jsog from 'jsog';

export default {
  components: {
    JobSelection, Definition, ProfileSelection, ErrorDialog,
  },
  data() {
    return {
      jobSearchService: JobSearchService,
      recruitmentService: RecruitmentListService,
      recruitment: null,
      organization: {
        title: '',
        code: '',
        description: '',
      },
      announcement: {
        id: null,
        code: '',
        recruitmentProfile: null,
        workingWeeklyTime: 35,
        hideSalary: false,
        organizationDescription: '',
        location: null,
        usersIdToNotify: [],
      },
      currentStep: 1,
      copyHrefLinkStatus: null,
      workContractDurationUnitOptions: [
        {value: 'MONTH', text: 'mois'},
        {value: 'WEEK', text: 'semaines'},
      ],

      // step 1
      job: null,

      // step 2
      profilesList: [],

      // step 4
      adminItems: [],

      // validation
      loading: false,
      submitColor: 'primary',
      submitIcon: 'save',
      showAPIError: false,
    };
  },
  async created() {
    this.organization = appStore.state.organizationLoaded;
    this.jobSearchService = new JobSearchService(true);
    this.recruitmentService = new RecruitmentListService(this.organization.code, [], false, false, false);

    await this.getDataFromApi();
    await this.jobSearchService.fetchMembersOfGroups(this.organization.code);
    this.fetchAdminUsers();

    if (this.$route.params.job) {
      this.job = this.jobsList.find(job => job.id === this.$route.params.job.id);
      this.currentStep++;
      if (this.$route.params.profile) {
        this.announcement.recruitmentProfile = this.$route.params.profile;
        this.currentStep++;
      }
    }

    if (this.$route.params.recruitmentCode) {
      this.$axios
        .get(`/api/odas/recruitment/${this.$route.params.recruitmentCode}`)
        .then((response) => {
          const entity = response.data;
          this.job = entity.job;
          this.organizationCode = entity.organizationCode;
          this.announcement = entity;
          this.currentStep = 3;
        })
        .finally(() => {
          this.loading = false;
        });
    }
  },
  computed: {
    contractTypeOptions() {
      return [
        {text: 'CDD - Contrat à Durée Déterminée', value: 'CDD', contractType: 'TEMPORARY'},
        {text: 'CDI - Contrat à Durée Indéterminée', value: 'CDI', contractType: 'PERMANENT'},
        {text: 'CTT - Contrat de Travail Temporaire', value: 'CTT', contractType: 'TEMPORARY'},
        {text: 'CUI – Contrat unique d’insertion', value: 'CUI', contractType: 'TEMPORARY'},
        {text: 'Contrat d’apprentissage', value: 'CA', contractType: 'PRO'},
        {text: 'Contrat de professionnalisation', value: 'CP', contractType: 'PRO'},
        {text: 'Contrat saisonnier', value: 'SEASONAL', contractType: 'PRO'},
        {text: 'Contrat freelance / indépendant', value: 'FREELANCE', contractType: 'PRO'},
      ].filter(c => !this.job?.typeContractCategories?.length || this.job.typeContractCategories.includes(c.contractType));
    },
    jobsList() {
      return this.jobSearchService.jobList;
    },
    displayedSalary() {
      if ((this.announcement.baseSalary == null && this.announcement.maxSalary == null) ||
        (this.announcement.baseSalary === '' && this.announcement.maxSalary === '')) {
        return 'Non renseignée';
      }
      return ''.concat(
        `${this.announcement.baseSalary}`,
        this.announcement.maxSalary != null ? ` - ${this.announcement.maxSalary}` : '',
        ' €',
      );
    },
    jobEditable() {
      return !(this.announcement.code !== null && this.announcement.code !== '');
    },
    contractType() {
      const contractTypeOption = this.contractTypeOptions.find(option => option.value === this.announcement.typeContract);
      if (contractTypeOption) {
        if (this.announcement.typeContract === 'CDD' && this.announcement.workContractDurationUnit) {
          const unit = this.workContractDurationUnitOptions.find(option => option.value === this.announcement.workContractDurationUnit);
          return `${contractTypeOption.text} pour une durée de ${this.announcement.workContractDuration} ${unit ? unit.text : ''}`;
        }
        if (this.announcement.typeContract) {
          return contractTypeOption.text;
        }
      }
      return '';
    },
    announcementCity() {
      return this.announcement.location?.city;
    },
    announcementPostcode() {
      return this.announcement.location?.postcode;
    },
    announcementRadius() {
      return this.announcement.location?.radiusInKm;
    },
  },
  watch: {
    async job() {
      if (this.job) {
        this.profilesList = (await this.$api.listRecruitmentProfiles(this.job.id, true)).data;
        if (!this.announcement.organizationDescription) {
          const organizationCode = this.job.employerCode || this.job.recruiterCode;
          this.announcement.organizationDescription = (await this.$api.getOrganizationByCode(organizationCode)).data.description;
        }
      }
    },
  },
  methods: {
    async getDataFromApi() {
      this.loading = true;
      await this.jobSearchService.fetchPublishedJobs(this.organization.code);

      this.loading = false;
    },

    saveAsDraft() {
      this.submit('DRAFT');
    },
    publish() {
      this.submit('PUBLISHED');
    },
    async submit(state) {
      this.loading = true;
      try {
        this.announcement.state = state;
        this.announcement.title = `Recrutement pour le poste ${this.job.title}`;
        this.announcement.organizationCode = this.job.recruiterCode;
        this.announcement.organizationName = this.organization.title;

        // FIXME Quickfix to serialize recruitment profile id as server expects (DTO becomes DO...)
        this.announcement.recruitmentProfileUuid = this.announcement.recruitmentProfile.id || this.announcement.recruitmentProfile.uuid;
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const {recruitmentProfile, ...announcement} = this.announcement;
        if (announcement.code !== null && announcement.code !== '') {
          await this.recruitmentService.updateRecruitment(announcement.id, announcement);
        } else {
          this.announcement = await this.recruitmentService.createRecruitment(announcement);
        }
        this.currentStep = 6;
      } catch (e) {
        this.showAPIError = true;
        this.logError(e);
      } finally {
        this.loading = false;
      }
    },
    fetchAdminUsers() {
      this.loading = true;
      // TODO: refactor to use openapi service instead
      this.$axios.get('/api/odas/users/list/odas', {
        params: {
          page: 0,
          size: 50,
        },
      })
        .then(result => {
          let data = jsog.decode(result.data);
          const items = data.content || data;
          this.adminItems = items.filter(x => x.enabled).map(x => ({
            id: x.id,
            name: `${x.firstName || ''} ${x.lastName || ''} (${x.email})`,
          }));
        })
        .finally(() => {
          this.jobSearchService.membersOfGroups.forEach(x => this.adminItems.push(({
            id: x.id,
            name: `${x.firstName || ''} ${x.lastName || ''} (${x.email})`,
          })));
          this.loading = false;
        });
    },
    getAnnouncementUrl() {
      const baseFrontUrl = baseUrl('fo');
      if (!this.announcement.code) {
        logErrorToServer('Generated job page url is empty', Vue.$api);
      }
      return `${baseFrontUrl}/jobs/${this.announcement.code}`;
    },
    copy() {
      try {
        navigator.clipboard.writeText(this.getAnnouncementUrl());
        this.copyHrefLinkStatus = {
          success: true,
          message: 'L\'url a été copiée dans le presse papier',
        };
      } catch (e) {
        this.onCopyError();
      }
    },
    onCopyError() {
      this.copyHrefLinkStatus = {
        success: false,
        message: `L'url n'a pas été copiée dans le presse papier, veuillez copier/coller l'adresse manuellement : ${this.getAnnouncementUrl()}`,
      };
    },
  },
};
</script>

<style lang="scss">
.box {
  border: 1px solid lightgrey;
  padding: 10px;
  border-radius: 8px;
}

.link {
  cursor: pointer;

  :not(.v-icon) {
    text-decoration: underline;
  }
}

</style>
