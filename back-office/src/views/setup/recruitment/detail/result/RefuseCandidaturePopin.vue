<template>
  <v-dialog v-model="showDialog" max-width="50%">
    <v-card>
      <v-card-title>
        <div class="text-center capitalize-first-letter d-flex align-center">
        <span class="capitalize-first-letter">
          Refuser la candidature
        </span>
        </div>
      </v-card-title>
      <v-card-text class="pb-0">
        <v-row no-gutters justify="center" class="pt-2">
          <v-col cols="12">
            Voulez-vous vraiment refuser la candidature de
            {{ candidatureContactInfo.firstName }}
            {{ candidatureContactInfo.lastName }}&nbsp;?
          </v-col>
          <v-col cols="12" v-if="isFailure">
            <v-alert type="error" outlined dense>
              Une erreur interne est survenue, veuillez réessayez.
            </v-alert>
          </v-col>
          <v-col cols="12">
            <v-form v-model="validForm" ref="form">
              <v-switch label="Notifier le candidat par email ?"
                        v-model="notifyUserPerEmail"
                        :disabled="candidatureContactInfo.isBlacklisted"/>
              <template v-if="notifyUserPerEmail">
                <v-text-field v-model="emailTemplate.subject" label="Objet de l'email" required :counter="100"
                              :rules="[rules.required, v => v.length <= 100 || 'Texte trop long']"/>
                <v-text-field v-model="emailTemplate.emailFrom" label="Email de l'émetteur" required type="email"
                              :counter="40"
                              :rules="[rules.required, v => v.length <= 64 || 'Texte trop long']"
                              :suffix="defaultEmailDomain"/>
                <v-text-field v-model="emailTemplate.authorAlias" label="Alias de l'émetteur" :counter="40"
                              :rules="[v => v.length <= 40 || 'Texte trop long']"/>
                <v-textarea v-model="emailTemplate.content"
                            :counter="10000"
                            :rules="[rules.required, v => v.length <= 10000 || 'Texte trop long']"
                            label="Contenu de l'email"
                            required/>
              </template>
            </v-form>
          </v-col>
          <v-col cols="12" v-if="!notifyUserPerEmail">
            <v-alert :value="true"
                     outlined
                     color="orange"
                     icon="warning">
              {{
                candidatureContactInfo.isBlacklisted
                  ? 'Cet utilisateur n’a pas souhaité recevoir de mail de notre part - nous vous invitons à le contacter par un autre moyen pour lui notifier le refus de sa candidature'
                  : 'N\'oubliez pas de signifier le candidat par un autre moyen.'
              }}
            </v-alert>
          </v-col>
          <v-col cols="12">
            <v-switch label="Ajouter une note à ce candidat ?" v-model="addUserNote" />
          </v-col>
          <template v-if="addUserNote">
            <v-col cols="12">
              <template v-for="note in userDetailService.notes">
                <user-note
                  :user-detail-service="userDetailService"
                  :note="defaultNote(note)"
                  :key="note.id"
                />
              </template>
            </v-col>
            <v-col cols="12">
              <v-btn class="ma-3"
                     color="primary"
                     outlined
                     @click="userDetailService.addNote()"
                     :disabled="!userDetailService.mayAddNote"
              >
                <v-icon>add</v-icon>
                Ajouter une note
              </v-btn>
            </v-col>
          </template>
          <v-col class="pl-sm-2">
            <div class="my-4 d-flex justify-space-around">
              <v-btn
                id="confirmRefusalPopin"
                color="primary"
                @click="refuseCandidature"
                :loading="isLoading"
                :disabled="!canConfirm()"
              >
                Oui, confirmer
              </v-btn>
              <v-btn
                id="closeRefusalPopin"
                color="error"
                @click="showDialog = false"
              >
                Non, fermer
              </v-btn>
            </div>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
import UserDetailService from '@/views/repository/user/detail/UserDetailService';
import UserNote from '@/views/repository/user/detail/UserNote';
import appStore from '@/store';

import ResultService from './ResultService';
import { ContactForCandidature } from 'erhgo-api-client';

export default {
  name: 'RefuseCandidaturePopin',
  components: {
    UserNote,
  },
  data() {
    return {
      addUserNote: false,
      userDetailService: null,
      notifyUserPerEmail: true,
      defaultEmailDomain: '@jenesuispasuncv.fr',
      emailTemplate: {
        subject: '',
        emailFrom: 'recrutement',
        authorAlias: 'Iris',
        content: '',
      },
      validForm: false,
      rules: {
        required: v => !!v || 'Ce champ est obligatoire',
        email: v => /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(v) || `L'email doit être valide`,
      },
      showDialog: true,
    };
  },
  props: {
    candidatureContactInfo: {
      type: ContactForCandidature,
      required: true,
    },
    resultService: {
      type: ResultService,
      required: true,
    },
    recruitmentTitle: {
      type: String,
      required: true,
    },
    jobTitle: {
      type: String,
      required: true,
    },
  },
  async created() {
    this.notifyUserPerEmail = !this.candidatureContactInfo.isBlacklisted;
    this.userDetailService = new UserDetailService(this.candidatureContactInfo.userId, this.organization.code);
    await this.userDetailService.fetchNotes();
    await this.userDetailService.fetchUserDetailWithCapacities();
    await this.userDetailService.fetchRecruiters();
    if (this.defaultRefusalEmailTemplate) {
      this.emailTemplate = {
        ...this.defaultRefusalEmailTemplate,
        emailFrom: this.defaultRefusalEmailTemplate.emailFrom.split('@')[0],
      };
    } else {
      this.emailTemplate.content = `Bonjour,\n\nVotre candidature au poste ${this.jobTitle} vient d'être refusée par ${this.organization.title}.\nVotre profil #jenesuisPASunCV reste accessible anonymement à l'ensemble des recruteurs présents sur le site.\n\nBelle journée à vous`;
    }
  },
  computed: {
    isFailure() {
      return this.resultService.isFailure;
    },
    isLoading() {
      return this.resultService.isAnyLoading;
    },
    organization() {
      return appStore.getters.organizationLoaded;
    },
    defaultRefusalEmailTemplate() {
      return this.organization.refusalEmailTemplate;
    },
  },
  watch: {
    showDialog(newVal, oldVal) {
      if (!newVal && oldVal) {
        this.$emit('onClose');
      }
    },
  },
  methods: {
    async refuseCandidature() {
      this.emailTemplate.emailFrom = `${this.emailTemplate.emailFrom}${this.defaultEmailDomain}`;
      await this.resultService.refuseCandidature(this.candidatureContactInfo.candidatureId, this.notifyUserPerEmail ? {...this.emailTemplate} : null);
      if (!this.isFailure) {
        this.showDialog = false;
        this.$emit('refresh');
      }
    },
    canConfirm() {
      return !this.notifyUserPerEmail || this.validForm;
    },
    defaultNote(note) {
      if (!note.text) {
        note.text = `Refus candidature ${this.organization.title}/${this.jobTitle}`;
      }

      return note;
    },
  },
};
</script>
