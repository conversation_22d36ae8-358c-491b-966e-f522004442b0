import SafeService from 'odas-plugins/SafeService';
import {CandidatureContactInfoPage, CustomEmailTemplate} from 'erhgo-api-client';
import Vue from 'vue';
import {logErrorToServer} from 'odas-plugins';
import {AxiosError} from 'axios';

export default class ResultService extends SafeService {
  private _result!: CandidatureContactInfoPage;
  private _actionLoadings: number[] = [];

  actionResult: { type: string | null; message: string } = {
    type: null,
    message: '',
  };

  constructor() {
    super();
    this.loading = true;
  }

  async fetchCandidatureContactInfoPage(size: number, page: number, recruitmentId: number, isMatched: boolean) {
    await this.safeCall(async () => {
      this._result = (await Vue.$api.getCandidaturesForConsultant(size,
        page,
        recruitmentId,
        isMatched)).data;
    });
  }

  async confirmCandidature(candidatureId: number) {
    this._actionLoadings.push(candidatureId);
    const candidatureEmail = this._result.content.filter(x => x.candidatureId === candidatureId)[0].email;

    try {
      await Vue.$api.publishCandidature({candidatureId});
      this._result.content = this._result.content.filter(x => x.candidatureId !== candidatureId);
      this.setResult(true, `La candidature "${candidatureEmail}" a été correctement finalisée`);
    } catch (error) {
      const status = (error as AxiosError)?.response?.status;
      switch (status) {
        case 404:
          // The only reason a 404 can happen here is that another user confirmed the candidature before you, no big deal
          this.hideResult();
          break;
        default:
          this.setResult(false, 'Une erreur technique est survenue');
          logErrorToServer(error, Vue.$api);
          throw error;
      }
    } finally {
      this._actionLoadings = this._actionLoadings.filter(x => x !== candidatureId);
    }
  }

  async refuseCandidature(candidatureId: number, emailTemplate: CustomEmailTemplate) {
    this._actionLoadings.push(candidatureId);

    try {
      await Vue.$api.markCandidatureAsRefused({candidatureId, emailTemplate});
      this.setResult(true, `La candidature a été correctement refusée`);

      if (this._result) {
        const elementId = this._result.content.findIndex(x => x.candidatureId === candidatureId);
        this._result.content[elementId].refusalDate = new Date(Date.now());
      }
    } catch (error) {
      this.setResult(false, 'Une erreur technique est survenue');
      logErrorToServer(error, Vue.$api);
      throw error;
    } finally {
      this._actionLoadings = this._actionLoadings.filter(x => x !== candidatureId);
    }
  }

  get result(): CandidatureContactInfoPage {
    return this._result;
  }

  getCandidature(candidatureId: number) {
    return this.result.content.filter(x => x.candidatureId === candidatureId);
  }

  setResult(success: boolean, message: string) {
    this.actionResult.type = success ? 'success' : 'error';
    this.actionResult.message = message;
    setTimeout(() => this.hideResult(), 10000);
  }

  hideResult() {
    this.actionResult.type = '';
    this.actionResult.message = '';
  }

  get showResult() {
    return !!this.actionResult?.type;
  }

  get isFailure() {
    return this.actionResult?.type === 'error';
  }

  get isAnyLoading() {
    return this._actionLoadings.length > 0;
  }

  isLoading(candidatureId: number): boolean {
    return this._actionLoadings.includes(candidatureId);
  }
}
