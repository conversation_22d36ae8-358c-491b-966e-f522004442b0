<template>
  <v-container fluid>
    <v-dialog persistent :value="true" v-if="displayPopup" max-width="600">
      <v-card>
        <v-card-title class="text-h5">
          Envoi de mail de proposition de recrutement
        </v-card-title>
        <v-card-text>
          Vous allez envoyer un email de proposition de recrutement à {{ selectedUsersCount }} individu(s) du canal
          {{ organizationTitle }}. Veuillez sélectionner un recrutement en cours et confirmer.
          <v-select
            v-model="selectedRecruitment"
            :items="recruitmentService.recruitmentList"
            :item-text="correctRecruitmentTitle"
            item-value="id"
            return-object
            no-data-text="Aucun recrutement pour ce poste"
          />
          <v-alert type="error" outlined v-if="userManagementService.showAPIError">
            Une erreur est survenue à l'envoi de mail&nbsp;: merci de remonter l'incident au
            support
            erhgo.
          </v-alert>
        </v-card-text>
        <v-card-actions>
          <v-btn
            color="primary"
            outlined
            @click="displayPopup = false"
          >
            Non, annuler
          </v-btn>
          <v-spacer/>
          <v-btn
            color="primary"
            :loading="userManagementService.loading"
            :disabled="!selectedRecruitment"
            @click="sendCandidatureProposal()"
          >
            Oui
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-breadcrumbs class="breadcrumb" :items="breadcrumbsItems">
      <template v-slot:divider>
        <v-icon>chevron_right</v-icon>
      </template>
      <template v-slot:item="{ item }">
        <v-breadcrumbs-item
          exact
          :to="item.to"
          :disabled="item.disabled">
          {{ item.text }}
        </v-breadcrumbs-item>
      </template>
    </v-breadcrumbs>
    <v-row>
      <v-col>
        <h1 class="d-flex align-center">
          <v-icon large
                  class="mr-2"
                  color="primary">mdi-account-multiple
          </v-icon>
          Liste des utilisateurs compatibles
          <v-btn @click="exportUsersMatchingJob" class="ml-3" small :loading="loadingExport">
            <v-icon>archive</v-icon>
            Télécharger
          </v-btn>
          <v-spacer/>
          <v-btn outlined
                 :to="{name: `user-index-for-job${organization_code?'-for-orga':''}`, params: {organization_code: organization_code, jobId: job_id}}"
                 class="ml-3"
                 id="matching-users-for-job-index-view"
                 small>
            Vue "sourcing"
          </v-btn>
        </h1>
      </v-col>
    </v-row>
    <v-expansion-panels focusable accordion
                        :disabled="!userAlreadyAppliedToRecruitmentLength"
                        class="small-panel">
      <v-expansion-panel>
        <v-expansion-panel-header class="text-body-2 font-italic">
          <div
            :class="{'text-decoration-underline': !!userAlreadyAppliedToRecruitmentLength}">
            {{ userAlreadyAppliedToRecruitmentLength || 'Aucun' }}
            candidat{{ userAlreadyAppliedToRecruitmentLength > 1 ? 's' : '' }}
            déjà positionné{{ userAlreadyAppliedToRecruitmentLength > 1 ? 's' : '' }} sur ce poste
          </div>
        </v-expansion-panel-header>
        <v-expansion-panel-content class="pt-3">
          <already-applied-to-recruitment-list :loading="userSearchService.loading"
                                               :sortBy="userSearchService.sortBy"
                                               :sortDesc="userSearchService.sortDesc"
                                               :items="usersAlreadyAppliedToRecruitment"
                                               :organizationCode="this.organization_code"/>
        </v-expansion-panel-content>
      </v-expansion-panel>
    </v-expansion-panels>

    <v-row align="center" class="pt-5">
      <v-col id="capacityThreshold" cols="12" lg="6">
        <v-row no-gutters>
          <v-col cols="12" class="mt-5">
            Proportion de capacités correspondant chez les candidats&nbsp;:
            <capacity-threshold v-model="userSearchService.capacityThreshold"
                                :disable-none="userSearchService.noOrgaFilter"/>
          </v-col>
          <v-col cols="12">
            <v-select hint="Choisissez les critères à prendre en considération"
                      persistent-hint
                      small-chips
                      clearable
                      item-value="code"
                      item-text="titleForBO"
                      multiple
                      v-model="userSearchService.criteriaFilter"
                      :menu-props="{ offsetY: true }"
                      label="Trier par critères"
                      no-data-text="Aucun critère pour ce poste"
                      :items="jobCriteria"/>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="6" lg="3">
        <div class="d-lg-flex">
          <v-checkbox
            persistent-hint
            v-model="userSearchService.applyMasteryLevelRange"
            label="Niveau de maîtrise proche"
            hint="Si cette case est cochée, le seuil de tolérance du niveau de maitrise est abaissé."
          />
        </div>
        <div class="d-lg-flex">
          <v-checkbox
            v-if="job && !job.isPrivate"
            persistent-hint
            hint="Si cette case est cochée, l'ensemble des utilisateurs du système sont considérés, pas uniquement ceux associés à l'organisation sélectionnée."
            v-model="userSearchService.noOrgaFilter"
            label="Considérer tous les utilisateurs"
          />
        </div>
      </v-col>
      <v-col cols="6" lg="3">
        <v-row no-gutters>
          <v-col cols="12">
            <v-alert v-if="job && job.isPrivate" outlined dense class="ma-0">
              <span class="font-italic text-body-2">
                <v-icon class="pr-1"
                        small>info</v-icon>Ce poste n’est ouvert qu’aux candidats associés à l’organisation {{
                  jobRecruiterTitle
                }}.
              </span>
            </v-alert>
            <v-autocomplete hint="Choisissez des organisations pour filtrer les candidats"
                      persistent-hint
                      clearable
                      item-value="code"
                            :item-text="o => `${o.title}${o.organizationType === 'SOURCING' ? ' (sourcing)' : ''}`"
                      multiple
                      v-model="userSearchService.recruiterFilter"
                      :menu-props="{ offsetY: true }"
                      label="Filtrer par organisation"
                      :items="recruiters"
                      v-else
            />
          </v-col>
          <v-col cols="12">
            <v-text-field hide-details
                          clearable
                          v-model="userSearchService.postcode"
                          label="Code postal commençant par..."/>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <v-tooltip top :disabled="hasRecruitment">
          <template v-slot:activator="{ on }">
            <div v-on="on" class="d-inline-block">
              <v-btn
                color="primary"
                :disabled="(selectedUsersCount < 1 || !hasRecruitment)"
                @click="displayPopup = true">
                Proposer le poste
              </v-btn>
            </div>
          </template>
          Veuillez ouvrir un recrutement pour proposer le poste.
        </v-tooltip>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <users-list
          :service="userSearchService"
          :job="job"/>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import appStore from '@/store';
import UserSearchService from '@/components/services/UserSearchService';
import UserManagementService from '@/components/services/UserManagementService';
import CapacityThreshold from '@/components/capacity/CapacityThreshold';
import UsersList from './List';
import AlreadyAppliedToRecruitmentList from './AlreadyAppliedToRecruitmentList';
import RecruitmentJobService from '../recruitment/list/RecruitmentJobService';
import DownloadUtils from '@/components/mixins/DownloadUtils';

export default {
  name: 'FrontUserListByOrganization',
  components: {UsersList, CapacityThreshold, AlreadyAppliedToRecruitmentList},
  mixins: [DownloadUtils],
  props: {
    organization_code: {
      type: String,
      required: true,
    },
    job_id: {
      type: String,
      required: false,
    },
  },
  data() {
    return {
      userSearchService: null,
      recruitmentService: null,
      userManagementService: new UserManagementService(),
      job: null,
      displayPopup: false,
      selectedRecruitment: null,
      loadingExport: false,
    };
  },
  async created() {
    this.userSearchService = new UserSearchService(this.organization_code, this.job_id);
    // First load recruiters & init default project filter
    await this.userSearchService.fetchRecruitersList(this.organization_code);
    this.job = (await this.$api.getJob(this.job_id)).data;
    if (this.job.isPrivate) {
      // Override default project filter with filter on job's recruiter code
      this.userSearchService.recruiterFilter = [this.job.recruiterCode];
    }
    this.recruitmentService = new RecruitmentJobService(this.job_id);


    await this.recruitmentService.fetchRecruitmentList();
    this.userSearchService.jobCriteria = this.job.criteriaValues;
    await this.userSearchService.fetchUserList();
  },
  computed: {
    hasRecruitment() {
      return !!this.recruitmentService?.recruitmentList?.length;
    },
    jobTitle() {
      return this.job?.title;
    },
    jobRecruiterTitle() {
      return this.recruiters?.filter(r => r.code === this.job?.recruiterCode)[0]?.title;
    },
    breadcrumbsItems: {
      get() {
        return [
          {
            text: 'Postes',
            to: {
              name: 'organizations_jobs_repository',
              params: {organization_code: this.organization_code},
            },
          },
          {
            text: `${this.jobTitle}`,
            disabled: true,
          },
        ];
      },
    },
    usersAlreadyAppliedToRecruitment() {
      return this.userSearchService.usersAlreadyAppliedToRecruitment;
    },
    userAlreadyAppliedToRecruitmentLength() {
      return this.usersAlreadyAppliedToRecruitment.length;
    },
    organizationTitle() {
      return appStore.state.organizationLoaded.title;
    },
    selectedUsersCount() {
      return this.userSearchService.selectedUsers?.length;
    },
    selectedUsersId() {
      return this.userSearchService.selectedUsers?.map(u => u.id);
    },
    recruiters() {
      let noOrga = {
        title: '[Aucune organisation]',
        code: 'NO_ORGA',
      };

      let recruiters = this.userSearchService.recruiters;

      if (this.userSearchService.noOrgaFilter) {
        recruiters = [
          noOrga,
          ...this.userSearchService.recruiters,
        ];
      }

      return recruiters;
    },
    jobCriteria() {
      return this.userSearchService.jobCriteria;
    },
  },
  methods: {
    async sendCandidatureProposal() {
      await this.userManagementService.sendCandidatureProposal(this.selectedRecruitment.id, this.selectedUsersId);
      if (!this.userManagementService.showAPIError) {
        this.displayPopup = false;
        await this.userSearchService.fetchUserList();
        this.userSearchService.selectedUsers = [];
      }
    },
    async exportUsersMatchingJob() {
      this.loadingExport = true;
      try {
        await this.displayPopup(async () => await this.userSearchService.getUsersMatchingJobExport(), 'utilisateurs_correspondant.csv');
      } finally {
        this.loadingExport = false;
      }
    },
    correctRecruitmentTitle(item) {
      return item.recruitmentProfile.title + (item.city ? ` (${item.city})` : '');
    },
  },
};
</script>

