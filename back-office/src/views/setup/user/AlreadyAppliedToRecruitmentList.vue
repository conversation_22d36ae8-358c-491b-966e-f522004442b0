<template>
  <v-data-table :headers="headers"
                dense
                :items="items"
                hide-default-footer
                :loading="loading"
                :sort-by.sync="sortBy"
                :sort-desc.sync="sortDesc"
                no-data-text="Aucun résultat"
                disable-pagination
                loading-text="Chargement en cours..."
                class="font-italic"
                id="userList">
    <template v-slot:item="props">
      <tr :id="`user-${items.indexOf(props.item)}`">
        <td class="text-left">
          <v-tooltip top v-if="props.item.refusedCandidature">
            Candidature refusée
            <template v-slot:activator="{ on }">
              <v-icon color="orange" v-on="on">warning</v-icon>
            </template>
          </v-tooltip>
        </td>
        <td class="text-left">
          {{ props.item.firstName }}
        </td>
        <td class="text-left">
          {{ (props.item.lastName || '?') }}
        </td>
        <td class="text-left">
          {{ props.item.channels ? props.item.channels.join(', ') : '(direct)' }}
        </td>
        <td class="text-left">
          {{
            `${getLocationLabel(props.item)}`
          }}
        </td>
        <td> {{
            $t(`candidaturesRealStates.title.${props.item.candidatureState}`)
          }}
        </td>
        <td class="text-left">
          <v-tooltip left>
            <template v-slot:activator="{ on }">
              <v-btn text
                     :id="`candidatureDetail${props.index}`"
                     icon
                     color="primary"
                     v-on="on"
                     @click="goToCandidatureDetail(organizationCode, props.item.candidatureId)"
              >
                <v-icon>visibility</v-icon>
              </v-btn>
            </template>
            <span>Détail</span>
          </v-tooltip>
        </td>
      </tr>
    </template>
  </v-data-table>
</template>

<script>
import Vue from 'vue';

export default {
  name: 'AlreadyAppliedToRecruitmentList',
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    items: {
      type: Array,
      required: true,
    },
    sortBy: {
      type: Array,
      required: true,
    },
    sortDesc: {
      type: Array,
      required: true,
    },
    organizationCode: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      headers: [
        {
          sortable: false,
          value: 'state',
        },
        {
          text: this.$t('ref.headers.user.firstName'),
          sortable: false,
          value: 'firstName',
        },
        {
          text: this.$t('ref.headers.user.lastName'),
          sortable: false,
          value: 'lastName',
        },
        {
          text: 'Organisation',
          sortable: false,
          value: 'channels',
        },
        {
          text: this.$t('ref.headers.user.location'),
          sortable: false,
          value: 'location.city',
        },
        {
          text: 'Etat de la candidature',
          sortable: false,
          value: 'candidature.state',
        },
        {
          text: 'Détail',
          icon: '',
          align: 'left',
        },
      ],
    };
  },
  methods: {
    getLocationLabel(user) {
      let result;
      if (!!user.location) {
        result = Array.of(user.location.postcode, user.location.city)
          .filter(x => !!x)
          .join(' - ');
      }
      return result ? result : 'Non renseignée';
    },
    async goToCandidatureDetail(organizationCode, candidatureId) {
      let routeData = await Vue.$router.resolve({
        name: 'candidature_details',
        params: {organization_code: organizationCode, candidature_id: candidatureId},
      });
      window.open(routeData.href, '_blank');
    },
  },
};
</script>
