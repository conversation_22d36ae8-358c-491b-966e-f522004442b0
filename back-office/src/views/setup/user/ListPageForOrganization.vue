<template>
  <v-container fluid>
    <v-row>
      <v-col>
        <h1 class="d-flex align-center">
          <v-icon large
                  class="mr-2"
                  color="primary">recent_actors
          </v-icon>
          {{ $t(`ref.user.list`) }} {{ `(${userSearchService.totalNumberOfElements})` }}
          <v-btn :loading="loadingUsers" outlined @click="exportUsers" class="ml-3" small>
            <v-icon>archive</v-icon>
            Télécharger
          </v-btn>
        </h1>
      </v-col>
    </v-row>
    <v-row>
      <v-col md="4">
        <v-text-field v-model="userSearchService.query"
                      append-icon="search"
                      label="Chercher par nom, prénom ou email"
        />
      </v-col>
      <v-col md="4">
        <v-text-field hide-details
                      clearable
                      v-model="userSearchService.postcode"
                      label="Code postal commençant par..."/>
      </v-col>
      <v-col md="4">
        <v-autocomplete hint="Choisissez des organisations pour filtrer les candidats"
                  persistent-hint
                  clearable
                        item-value="code"
                        :item-text="o => `${o.title}${o.organizationType === 'SOURCING' ? ' (sourcing)' : ''}`"
                  v-model="userSearchService.recruiterFilter"
                  multiple
                  :menu-props="{ offsetY: true }"
                  label="Filtrer par organisation"
                  :items="recruiters"/>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <user-channel-selector-btn
          :selected-users="userSearchService.selectedUsers"
          :user-management-service="userManagementService"
          :organizations-to-add="recruiters"
          :organizations-to-remove="recruiters"
          @finish="listKey++"
        />
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <users-list :service="userSearchService" :key="listKey" :unpaginated="!!userSearchService.query"/>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import appStore from '@/store';
import UserSearchService from '@/components/services/UserSearchService';
import UserManagementService from '@/components/services/UserManagementService';
import UsersList from './List';
import moment from 'moment';
import UserChannelSelectorBtn from './UserChannelSelectorBtn';
import DownloadUtils from '@/components/mixins/DownloadUtils';

export default {
  name: 'FrontUserListPageByOrganization',
  components: {UserChannelSelectorBtn, UsersList},
  props: {
    organization_code: {
      type: String,
      required: true,
    },
  },
  mixins: [DownloadUtils],
  data() {
    return {
      listKey: 1,
      userSearchService: null,
      userManagementService: new UserManagementService(),
      displayPopup: false,
      loadingUsers: false,
    };
  },
  async created() {
    this.userSearchService = new UserSearchService(this.organization_code);
    await this.userSearchService.fetchRecruitersList(this.organization_code);
    await this.userSearchService.fetchUserList();
  },
  computed: {
    locations() {
      return this.userSearchService.locations;
    },
    recruiters() {
      return this.userSearchService?.recruiters;
    },
    organizationTitle() {
      return appStore.state.organizationLoaded.title;
    },
    selectedUsersId() {
      return this.userSearchService.selectedUsers?.map(u => u.id);
    },
  },
  methods: {
    async exportUsers() {
      this.loadingUsers = true;
      try {
        await this.download(async () => (await this.$api.getFrontOfficeUserByGroupExport(this.organization_code)), `utilisateurs_${moment().format('MM-DD-YYYY_HH-mm-ss')}.csv`);
      } finally {
        this.loadingUsers = false;
      }
    },
  },
};
</script>

