<template>
  <div>
    <v-btn color="primary"
           x-small
           outlined
           :disabled="selectedUsersCount < 1"
           @click="displayPopup = true">
      Modifier les organisations rattachées
    </v-btn>

    <v-dialog v-model="displayPopup" v-if="displayPopup" max-width="600">
      <v-card>
        <v-card-title class="text-h5">
          Modification des organisations rattachées à l'utilisateur
        </v-card-title>
        <v-card-text>
          <strong>Veuillez sélectionner les organisations auxquelles ajouter {{ updateLabel }}&nbsp;:</strong>
          <v-autocomplete
            :items="organizationsToAdd.filter(o => includesSourcing || o.organizationType !== 'SOURCING')"
            :item-text="o => `${o.title}${o.organizationType === OrganizationType.SOURCING ? ' (sourcing)' : ''}`"
            item-value="code"
            v-model="channelsToAdd"
            multiple
            small-chips
            deletable-chips
            no-data-text="Aucune organisation à ajouter"
            class="pb-10 pt-0"
          />
          <strong>Veuillez sélectionner les organisations desquelles supprimer {{ updateLabel }}&nbsp;:</strong>
          <v-autocomplete
            :items="organizationsToRemove.filter(o => includesSourcing || o.organizationType !== 'SOURCING')"
            :item-text="o => `${o.title}${o.organizationType === OrganizationType.SOURCING ? ' (sourcing)' : ''}`"
            item-value="code"
            v-model="channelsToRemove"
            multiple
            small-chips
            deletable-chips
            no-data-text="Aucune organisation à enlever"
            class=" pt-0"
          />
          <div class="font-italic">
            <v-icon small>info</v-icon>
            Cette action est sans conséquence pour les candidats déjà associés aux organisations ajoutées ou
            n'appartenant pas aux organisations supprimées.
          </div>
          <v-alert type="error" outlined v-if="userManagementService.showAPIError">
            Une erreur est survenue à la modification des organisations pour ces utilisateurs&nbsp;: merci de remonter
            l'incident au support erhgo.
          </v-alert>
          <v-checkbox v-model="includesSourcing"
                      dense
                      label="Inclure les organisations sourcing aux listes"
                      hide-details />

        </v-card-text>
        <v-card-actions>
          <card-btn
            color="primary"
            @click="displayPopup = false"
          >
            <v-icon color="error">cancel</v-icon>
            <strong>Annuler l'opération</strong>
          </card-btn>
          <v-spacer/>
          <card-btn
            :color="hasNoChange?undefined:'primary'"
            :dark="hasNoChange?undefined:true"
            :loading="userManagementService.loading"
            @click="updateChannels()"
            :disabled="hasNoChange"
          >
            <v-icon>check</v-icon>
            <strong>OK, modifier l'affectation {{ updateLabel }}</strong>
          </card-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
import UserManagementService from '@/components/services/UserManagementService';
import CardBtn from 'odas-plugins/CardBtn';
import { OrganizationType } from 'erhgo-api-client';

export default {
  name: 'user-channel-selector-dialog',
  components: {CardBtn},
  props: {
    selectedUsers: {
      required: true,
      type: Array,
    },
    organizationsToAdd: {
      required: true,
      type: Array,
    },
    organizationsToRemove: {
      required: true,
      type: Array,
    },
    userManagementService: {
      required: true,
      type: UserManagementService,
    },
  },
  data() {
    return {
      displayPopup: false,
      channelsToAdd: [],
      channelsToRemove: [],
      includesSourcing: false,
    };
  },
  methods: {
    async updateChannels() {
      await this.userManagementService.updateUsersChannels(this.selectedUsersId, this.channelsToAdd, this.channelsToRemove);
      if (!this.userManagementService.showAPIError) {
        this.displayPopup = false;
        this.$emit('finish', this.channelsToAdd, this.channelsToRemove);
      }
    },
  },
  computed: {
    OrganizationType() {
      return OrganizationType;
    },
    updateLabel() {
      let label = `l'individu sélectionné`;
      if (this.selectedUsersCount > 1) {
        label = `Les ${this.selectedUsersCount} individus sélectionnés`;
      }
      return label;
    },

    selectedUsersCount() {
      return this.selectedUsers.length;
    },
    selectedUsersId() {
      return this.selectedUsers.map(u => u.id);
    },
    hasNoChange() {
      return !this.channelsToAdd.length && !this.channelsToRemove.length;
    },
    hasAnyChannel() {
      return !this.organizations.filter(c => this.channelsToRemove.includes(c.code)).length;
    },
  },
  watch: {
    displayPopup(newVal, oldVal) {
      if (!newVal && oldVal) {
        this.channelsToRemove = [];
        this.channelsToAdd = [];
      }
    },
  },
};
</script>
