<template>
  <v-data-table :headers="headers"
                :items="listUser"
                :loading="service.loading"
                class="elevation-15"
                no-data-text="Aucun résultat"
                :footer-props="{
                        'items-per-page-options': rowsPerPage,
                        'items-per-page-text': 'Nb lignes par page',
                      }"
                :sort-by.sync="service.sortBy"
                :sort-desc.sync="service.sortDesc"
                :page.sync="service.page"
                :items-per-page.sync="service.rowsPerPage"
                item-key="id"
                :server-items-length="service.totalNumberOfElements"
                loading-text="Chargement en cours..."
                @pagination="cancelSelection()"
                :show-select="true"
                v-model="service.selectedUsers"
                id="userList"
                :hide-default-footer="unpaginated"
  >
    <template v-slot:item="props">
      <tr :id="`user-${listUser.indexOf(props.item)}`">
        <td class="text-left">
          <v-tooltip top v-if="!!props.item.unselectableIcon">
            {{ props.item.unselectableReason }}
            <template v-slot:activator="{ on }">
              <v-icon v-on="on">{{ props.item.unselectableIcon }}</v-icon>
            </template>
          </v-tooltip>
          <v-checkbox v-else :input-value="props.isSelected" @change="props.select($event)"/>
        </td>
        <td class="text-left" v-if="service.matchingMode">
          {{ props.item.matchingRateLabel }}&nbsp;
        </td>
        <td class="text-left" v-if="service.matchingMode">
          <v-tooltip bottom>
            <template v-slot:activator="{ on }">
              <div class="font-weight-bold" v-on="on" v-html="props.item.criteriaSummary"/>
            </template>
            <div v-html="props.item.criteriaDescription"/>
          </v-tooltip>
        </td>
        <td class="text-left">{{
            props.item.contactInformation.lastConnectionDate ? moment(props.item.contactInformation.lastConnectionDate).format('DD/MM/YYYY') : moment(props.item.contactInformation.creationDate).format('DD/MM/YYYY')
          }}
        </td>
        <td class="text-left">{{ props.item.contactInformation.firstName }}</td>
        <td class="text-left">{{ (props.item.contactInformation.lastName || '?').charAt(0) }}.</td>
        <td class="text-left">
          {{ props.item.contactInformation.channels ? props.item.contactInformation.channels.join(', ') : '(direct)' }}
        </td>
        <td class="text-left">
          {{ formattedLocation(props.item.contactInformation.location) }}
        </td>
        <td class="text-left">
          {{
            !!props.item.userProfileProgress ? props.item.userProfileProgress.candidaturesCount : 0
          }}
        </td>
        <td style="width: 5%;" class="text-left">
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <span v-bind="attrs" v-on="on">
                <v-progress-linear :color=props.item.completionRate.color
                                   rounded
                                   height=20
                                   :value=props.item.completionRate.progress
                />
              </span>
            </template>
            <span>{{ props.item.completionRate.title }}</span>
          </v-tooltip>
        </td>
        <td>
          <div :class="{invisible: emailWithDetails !== props.item.contactInformation}">
            <strong>Nom&nbsp;</strong>: {{ props.item.contactInformation.lastName }}<br/>
            <user-email-wrapper :transactional-blacklisted="props.item.contactInformation.transactionalBlacklisted">
              <strong>Email&nbsp;</strong>: <a
              :href="`mailto:${props.item.contactInformation.email}`">{{
                props.item.contactInformation.email
              }}</a>
            </user-email-wrapper>
            <br/>
            <user-sms-wrapper :is-sms-blacklisted="!!props.item.contactInformation.smsBlacklisted">
              <strong>Téléphone&nbsp;</strong>:
              <a :href="`tel:${props.item.contactInformation.phoneNumber}`">{{
                  props.item.contactInformation.phoneNumber
                }}</a>
            </user-sms-wrapper>
          </div>
        </td>
        <td v-if="service.matchingMode">
          <generate-candidature-btn :user-id="props.item.id"
                                    :job="job"
                                    @onGenerateCandidature="getDataFromApi()"
                                    :has-already-applied-to-recruitment="!!props.item.candidatureId"
                                    :is-refused="!!props.item.anyRefusedCandidature"
                                    :organizationCode="organizationCode"
                                    v-if="!!job"
          />
        </td>
        <td>
          <v-tooltip top>
            <template v-slot:activator="{ on }">
              <v-btn
                v-on="on"
                icon
                @click="emailWithDetails = emailWithDetails === props.item.contactInformation ? null : props.item.contactInformation"
              >
                <v-icon>{{
                    emailWithDetails === props.item.contactInformation ? 'mdi-minus' : 'mdi-information'
                  }}
                </v-icon>
              </v-btn>
            </template>
            <span
              class="font-weight-bold">{{
                emailWithDetails === props.item.contactInformation ? 'Masquer' : 'Voir'
              }} les coordonnées</span>
          </v-tooltip>
          <v-tooltip top>
            <template v-slot:activator="{ on }">
              <v-btn icon target="_blank"
                     :disabled="!props.item.id"
                     v-on="on"
                     id="userDetailBtn"
                     :to="props.item.id ? {
                       name: 'front_user_detail_for_organization',
                       params: {
                         userId: props.item.id,
                         organization_code: organizationCode
                       }
                     }:undefined"
                     color="primary">
                <v-icon>visibility</v-icon>
              </v-btn>
            </template>
            <span>Informations détaillées</span>
          </v-tooltip>
          <v-tooltip top>
            <template v-slot:activator="{ on }">
              <v-btn icon
                     small
                     :disabled="!props.item.id"
                     color="primary"
                     v-on="on"
                     :to="{
                   name: 'user-edit',
                   params: {
                     userId: props.item.id
                   },
                }"
                     target="_blank"
              >
                <v-icon small>fa-pencil</v-icon>
              </v-btn>
            </template>
            <span>Modifier l'utilisateur</span>
          </v-tooltip>
        </td>
      </tr>
    </template>
    <template v-slot:footer.page-text="props">
      Lignes de {{ props.pageStart }} à {{ props.pageStop }} {{ props.itemsLength ? `sur ${props.itemsLength}` : '' }}
    </template>
  </v-data-table>
</template>

<script>
import moment from 'moment';

import UserSearchService from '@/components/services/UserSearchService';
import store from '@/store';
import GenerateCandidatureBtn from '@/components/job/GenerateCandidatureBtn';
import UserEmailWrapper from '@/components/user/UserEmailWrapper';
import UserSmsWrapper from '@/components/user/UserSmsWrapper';

export default {
  name: 'FrontUserListCommon',
  components: {UserEmailWrapper, GenerateCandidatureBtn, UserSmsWrapper},
  props: {
    service: {
      type: UserSearchService,
      required: true,
    },
    job: {
      type: Object,
      required: false,
    },
    unpaginated: {
      default: false,
    },
  },
  data() {
    return {
      moment,
      emailWithDetails: null,
      rowsPerPage: [10, 25, 50, 100, 200],
    };
  },
  methods: {
    async getDataFromApi() {
      await this.service.fetchUserList();
    },
    cancelSelection() {
      this.service.selectedUsers = [];
    },
    formattedLocation(location) {
      if (!location || (!location.city && !location.postcode)) return 'Non précisée';
      return [location.postcode, location.city].filter(x => !!x).join(' - ');
    },
  },
  computed: {
    pagination() {
      return this.service.pagination;
    },
    organizationCode() {
      return store.getters.organizationLoaded?.code;
    },
    listUser() {
      return this.service.listUser;
    },
    headers() {
      let headers = [
        {
          text: 'Date de dernière connexion',
          sortable: false,
          value: 'contactInformation.creationDate',
        },
        {
          text: this.$t('ref.headers.user.firstName'),
          sortable: false,
          value: 'contactInformation.firstName',
        },
        {
          text: this.$t('ref.headers.user.lastName'),
          sortable: false,
          value: 'contactInformation.lastName',
        },
        {
          text: 'Organisation',
          sortable: false,
          value: 'contactInformation.channels',
        },
        {
          text: this.$t('ref.headers.user.location'),
          sortable: false,
          value: 'contactInformation.location.postcode',
        },
        {
          text: this.$t('ref.headers.user.candidaturesCount'),
          sortable: false,
          value: 'candidaturesCount',
        },
        {
          text: 'Taux complétude',
          sortable: false,
        },
        {
          text: '',
          sortable: false,
        },
        {
          text: '',
          sortable: false,
        },
      ];
      if (this.service.matchingMode) {
        headers = [
          {
            text: 'Correspondance capacitaire',
            sortable: false,
            value: 'matchingRateInPercent',
          },
          {
            text: 'Critères (OK / KO / inconnu)',
            sortable: false,
          },
          ...headers,
          {
            text: '',
            sortable: false,
          },
        ];
      }

      return headers;
    },
  },
};
</script>

