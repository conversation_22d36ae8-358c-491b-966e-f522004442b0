<template>
  <v-data-table :headers="headers"
                :items="service.candidatures"
                dense
                hide-default-footer
                :loading="service.loading"
                no-data-text="Aucun résultat"
                disable-pagination
                item-key="candidatureId"
                loading-text="Chargement en cours..."
                id="candidaturesList">
    <template v-slot:item="{ item, isExpanded, expand, index }">
      <tr :id="`candidature-${service.candidatures.indexOf(item)}`" @click="expand(!isExpanded)">
        <td class="text-left">
          <v-tooltip top v-if="item.refusedCandidature">
            Candidature refusée
            <template v-slot:activator="{ on }">
              <v-icon color="orange" v-on="on">warning</v-icon>
            </template>
          </v-tooltip>
        </td>
        <td class="text-left">{{ item.jobTitle }}</td>
        <td class="text-left" :class="{'font-italic': !item.employerTitle}">
          {{ item.employerTitle || '(Non précisé)' }}
        </td>
        <td class="text-left">{{ item.recruiterTitle }}</td>
        <td> {{ `${$t(`candidaturesRealStates.title.${item.candidatureState}`)}` }}
          <v-tooltip top v-if="!item.visibleForUser">
            <template v-slot:activator="{ on }"><div v-on="on">
              <span class="font-weight-bold">Non visible du candidat</span>
            </div></template>
            Cette candidature a été générée automatiquement (sourcing)
          </v-tooltip>
        </td>
        <td>{{ item.lastCandidatureNoteDate | formatDate }}</td>
        <td class="text-left">
          <v-tooltip left>
            <template v-slot:activator="{ on }">
              <v-btn text
                     :id="`candidatureDetail${index}`"
                     icon
                     color="primary"
                     v-on="on"
                     @click="goToCandidatureDetail(item.organizationCode, item.candidatureId)"
              >
                <v-icon>visibility</v-icon>
              </v-btn>
            </template>
            <span>Détail</span>
          </v-tooltip>
        </td>
      </tr>
    </template>
    <template v-slot:expanded-item="props">
      <tr>
        <td :colspan="props.headers.length" v-if="props.item.lastCandidatureNoteDate">
          <v-icon>$expand</v-icon>
          <candidature-note :candidature-id="props.item.candidatureId"/>
        </td>
      </tr>
    </template>
  </v-data-table>
</template>

<script>
import Vue from 'vue';
import CandidaturesService from './CandidaturesService';
import CandidatureNote from '../candidature/detail/CandidatureNote';

export default {
  name: 'UserCandidatures',
  components: {CandidatureNote},
  props: {
    service: {
      type: CandidaturesService,
      required: true,
    },
  },
  data() {
    return {
      headers: [
        {
          sortable: false,
          value: 'state',
        },

        {
          text: this.$t('ref.headers.job.title'),
          icon: '',
          align: 'left',
          sortable: false,
          value: 'title',
        },
        {
          text: 'Employeur',
          icon: '',
          align: 'left',
          sortable: false,
          value: 'employerCode',
        },
        {
          text: 'Organisation',
          icon: '',
          align: 'left',
          sortable: false,
          value: 'recruiterCode',
        },
        {
          text: 'État de la candidature',
          icon: '',
          align: 'left',
          sortable: false,
          value: 'state',
        },
        {
          text: 'Date de la dernière note sur la candidature',
          icon: '',
          align: 'left',
          sortable: false,
          value: 'state',
        },
        {
          text: 'Détail',
          icon: '',
          align: 'left',
        },
      ],
    };
  },
  methods: {
    async goToCandidatureDetail(organizationCode, candidatureId) {
      let routeData = await Vue.$router.resolve({
        name: 'candidature_details',
        params: {organization_code: organizationCode, candidature_id: candidatureId},
      });
      window.open(routeData.href, '_blank');
    },
  },
};
</script>
