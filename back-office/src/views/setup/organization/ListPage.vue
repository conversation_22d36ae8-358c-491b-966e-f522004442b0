<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <list :headers="headers"
              :referer-recruiters="organizationCodes"
              :actions="actions">
          <template slot="title">
            <h1>
              <v-icon large
                      color="primary">mdi-office-building
              </v-icon>
              {{ $t(`ref.${isEmployer ? 'employer' : 'organization'}.list`) }}
            </h1>
          </template>

          <template v-slot:row="{row}">
            <td>
              <v-tooltip bottom v-if="!isEmployer">
                <template v-slot:activator="{ on }">
                  <v-btn text
                         icon
                         color="primary"
                         class="mr-2"
                         v-on="on"
                         v-on:click="goToJobOfSpecificOrganization(row)">
                    <v-icon>
                      launch
                    </v-icon>
                  </v-btn>
                </template>
                <span>Accéder aux postes de l'organisation {{ row.title }}</span>
              </v-tooltip>
            </td>
            <td>{{ row.code }}</td>
            <td class="text-left">{{ row.title }}</td>
            <td class="text-left" v-if="row.refererRecruiter">{{ row.refererRecruiter.title }}</td>
            <td class="text-left" v-if="!isEmployer">{{ $t(`organizationType.${row.organizationType}`) }}</td>
            <td class="text-right">
              <v-btn text
                     color="primary"
                     icon
                     class="mr-2 text-center"
                     :to="{
                       name: 'organizations_employees_repository',
                       params: {
                         organization_code: organizationCode,
                         employer_code: row.code
                       },
                     }"
                     v-if="isEmployer"
              >
                <v-icon>
                  people
                </v-icon>
              </v-btn>
              <v-btn text
                     color="primary"
                     icon
                     class="mr-2 text-center"
                     :to="isEmployer ?
                        { name: 'employer_edit', params: { id: row.id, organization_code: organizationCode }} :
                        { name: 'organizations_edit', params: { id: row.id }}
                     ">
                <v-icon>
                  edit
                </v-icon>
              </v-btn>
            </td>
          </template>

          <template v-slot:details="{item}">
            <v-col cols="12">
              <v-col cols="12">
                <b>{{ $t('ref.headers.organization.description') }}:</b>
                <div v-html="item.description"/>
              </v-col>
            </v-col>
          </template>
        </list>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>

import List from '@/views/setup/organization/List.vue';

export default {
  components: {List},
  props: {
    organizationCode: {
      type: String,
      required: false,
    },
  },
  data() {
    return {
      actions: [],
      organizationCodes: [],
    };
  },
  created() {
    if (this.isEmployer) {
      this.organizationCodes = this.organizationCode;
    }
    let action = {
      icon: 'mdi-plus',
      color: 'primary',
      label: 'action.create',
      primary: true,
    };
    const organization_code = this.organizationCode;
    if (organization_code) {
      action = {
        ...action,
        to: {
          name: 'employer_create',
          params: {organization_code},
        },
      };
    } else {
      action = {
        ...action,
        component: 'organizations_create',
      };
    }
    this.actions = [action];
  },
  methods: {
    goToJobOfSpecificOrganization(selectedOrganization) {
      this.$router.push({
        name: 'organizations_jobs_repository',
        params: {organization_code: selectedOrganization.code},
      });
    },
  },
  computed: {
    isEmployer() {
      return !!this.organizationCode;
    },
    headers() {
      return [
        {
          text: '',
          align: 'right',
          sortable: false,
          width: '1em',
        },
        {
          text: this.$t('ref.headers.organization.code'),
          align: 'left',
          sortable: true,
          value: 'code',
        },
        {
          text: this.$t('ref.headers.organization.title'),
          align: 'left',
          sortable: true,
          value: 'title',
        },
        {
          text: 'Organisation',
          align: 'left',
          sortable: true,
          value: 'refererRecruiter.title',
          hidden: !this.isEmployer,
        },
        {
          text: 'Type',
          align: 'left',
          sortable: true,
          value: 'organizationType',
          hidden: this.isEmployer,
        },
        {
          text: this.$t('ref.headers.actions'),
          align: 'right',
          sortable: false,
        },
      ];
    },
  },
};
</script>
