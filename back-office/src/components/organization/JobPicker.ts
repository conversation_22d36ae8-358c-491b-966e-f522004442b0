import Vue from 'vue';
import { OrganizationSummary, OrganizationType, SortDirection } from 'erhgo-api-client';

export interface Autocompleteable {
  title: string;
  code?: string;
  recruitmentProfiles?: Autocompleteable[];
  id: number | string;
  uuid?: string;
}

export default class JobPicker {

  private _organization: Autocompleteable | null = null;
  private _job: Autocompleteable | null = null;
  private _profile: Autocompleteable | null = null;

  private _loading = false;
  private _showDialog = false;

  organizationsList?: Autocompleteable[];
  jobsList?: Autocompleteable[];
  profilesList?: Autocompleteable[];

  jobSelectorStep = 1;

  async refreshJobs() {
    if (!!this.organization) {
      this._loading = true;
      try {
        this.jobsList = (await Vue.$api.getJobPage([this.organization!.code!], 0, 100, SortDirection.ASC, 'title')).data.content;
      } finally {
        this._loading = false;
      }
    }
  }

  refreshProfiles() {
    if (!!this.job) {
      this.profilesList = this.job.recruitmentProfiles;
    }
  }

  async refreshOrganizations() {
    this._loading = true;
    try {
      this.organizationsList = (await Vue.$api.getAllRecruiters()).data.filter((o: OrganizationSummary) => o.organizationType !== OrganizationType.SOURCING);
    } finally {
      this._loading = false;
    }
  }

  get organization() {
    return this._organization;
  }

  set organization(organization: Autocompleteable | null) {
    this._organization = organization;
    if (!!organization) {
      this.jobSelectorStep = 2;
      this.refreshJobs();
    } else {
      this.jobSelectorStep = 1;
      this.job = null;
      this.jobsList = undefined;
    }
  }

  get job() {
    return this._job;
  }

  set job(job: Autocompleteable | null) {
    this._job = job;
    if (!!job) {
      this.refreshProfiles();
    } else {
      this.profile = null;
      this.profilesList = undefined;
    }
  }

  get profile() {
    return this._profile;
  }

  set profile(profile: Autocompleteable | null) {
    this._profile = profile;
  }

  get loading() {
    return this._loading;
  }

  get showDialog() {
    return this._showDialog;
  }

  set showDialog(showDialog) {
    this._showDialog = showDialog;
    if (showDialog && !this.organizationsList) {
      this.refreshOrganizations();
    }
  }

}

