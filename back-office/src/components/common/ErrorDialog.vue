<template>
  <v-dialog v-model="showDialog" persistent shrink width="50%">
    <v-card>
      <v-card-title
        class="text-h5 grey lighten-2"
        primary-title
      >
        Erreur
        <v-spacer/>
        <v-icon color="error">error</v-icon>
      </v-card-title>
      <v-card-text>
        Une erreur technique s'est produite lors de la sauvegarde des données.
        <PERSON><PERSON><PERSON> r<PERSON>er, ou de contacter le support si l'erreur persiste.
      </v-card-text>
      <v-divider/>
      <v-card-actions>
        <v-spacer/>
        <v-btn
          color="primary"
          @click="close"
        >
          Continuer
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  props: {
    value: Boolean,
  },
  data() {
    return {
      showDialog: false,
    };
  },
  created() {
    this.showDialog = this.value;
  },
  watch: {
    value(newValue) {
      this.showDialog = newValue;
    },
  },
  methods: {
    close() {
      this.$emit('input', false);
    },
  },
};
</script>

<style>
</style>
