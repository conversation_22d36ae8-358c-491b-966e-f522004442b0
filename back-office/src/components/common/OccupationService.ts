import SafeService from 'odas-plugins/SafeService';
import Vue from 'vue';
import {ErhgoOccupationSearch} from 'erhgo-api-client';
import asyncDebounce from 'async-debounce';

export default class OccupationService extends SafeService {

  private _query = '';

  occupation: ErhgoOccupationSearch | null = null;
  private _occupations: ErhgoOccupationSearch[] = [];

  get query() {
    return this._query;
  }

  set query(query) {
    this._query = query;
    this.debouncedSearchOccupations.fn();
  }

  private async searchOccupations() {
    if (this._query?.trim()) {
      await this.safeCall(async () => this._occupations = (await Vue.$api.searchOccupations(this.query, false)).data);
    }
  }

  private debouncedSearchOccupations = asyncDebounce(async () => {
    await this.searchOccupations();
  }, 300);

  get occupations() {
    return this._occupations;
  }

}
