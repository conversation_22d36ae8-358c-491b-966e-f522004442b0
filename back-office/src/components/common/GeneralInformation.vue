<template>
  <v-row no-gutters>
    <v-col cols="12">
      <ul class="text-body-2" style="list-style: none;">
        <li class="my-2">
          <v-icon small color="primary">arrow_forward_ios</v-icon>
          Prénom&nbsp;: <strong>{{ candidateDetails.firstName }}</strong>
        </li>
        <li class="my-2">
          <v-icon small color="primary">arrow_forward_ios</v-icon>
          Nom&nbsp;: <strong>{{ candidateDetails.lastName }}</strong>
        </li>
        <li class="my-2">
          <v-icon small color="primary">arrow_forward_ios</v-icon>
          Adresse mail&nbsp;:
          <a :href='`mailto:${candidateDetails.email}`' target="_blank">{{ candidateDetails.email }}</a>
        </li>
        <li class="my-2">
          <v-icon small color="primary">arrow_forward_ios</v-icon>
          Téléphone&nbsp;:
          <template v-if="candidateDetails.phoneNumber"><a :href='`tel:${candidateDetails.phoneNumber}`'
                                                           target="_blank">{{ candidateDetails.phoneNumber }}</a><i
            v-if="candidateDetails.contactTime"> - {{ $t(`contactTimes.${candidateDetails.contactTime}`) }}</i>
          </template>
          <i v-else>Non renseigné</i>
        </li>
      </ul>
    </v-col>
  </v-row>
</template>

<script>
import {GeneralInformation} from 'erhgo-api-client';

export default {
  name: 'GeneralInformation',
  props: {
    candidateDetails: {
      required: true,
      type: GeneralInformation,
    },
  },
};
</script>
