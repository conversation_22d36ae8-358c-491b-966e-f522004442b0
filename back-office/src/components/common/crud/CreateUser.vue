<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-form ref="form" @submit.prevent="validate">
          <v-card flat>
            <v-card-title>
              <h2 class="d-flex align-center">
                <v-icon large
                        class="mr-2"
                        color="primary"
                        right>person_add
                </v-icon>
                {{ $t('ref.employee.create') }}
              </h2>
            </v-card-title>
            <v-card-text>
              <v-alert :value="globalError"
                       type="error"
                       transition="scale-transition"
                       dismissible>
                {{ $t('form.global.error') }}
              </v-alert>
              <v-text-field v-model="email"
                            :label="$t('form.employee.email')"
                            :rules="[v => (!!v || $t('form.employee.message.email.required'))]"
                            :error-messages="conflictError === 'email' ? $t('form.employee.message.email.conflict') :  null"
                            required
                            type="email"/>
              <v-text-field v-model="firstName"
                            :rules="[v => (!!v || $t('form.employee.message.firstName.required')),
                                     v => ((!v || v.length <= 30) || $t('form.employee.message.firstName.maxlength'))]"
                            :label="$t('form.employee.firstName')"
                            :counter="30"
                            required/>
              <v-text-field v-model="lastName"
                            :rules="[v => (!!v || $t('form.employee.message.lastName.required')),
                                     v => (!v || v.length <= 30 || $t('form.employee.message.lastName.maxlength'))]"
                            :label="$t('form.employee.lastName')"
                            :counter="30"
                            required/>
              <password-field
                @passwordFieldChanged="passwordFieldChanged"
              />
            </v-card-text>
            <v-card-actions>
              <v-btn color="grey lighten-5"
                     @click="redirectList"
                     small>
                <v-icon small
                        left>undo
                </v-icon>
                {{$t('action.return')}}
              </v-btn>
              <v-spacer/>
              <v-btn width="50%"
                     large
                     :color="submitColor"
                     @click="submit"
                     :loading="loading">
                {{$t('action.create')}} &nbsp;
                <v-icon large
                        right>{{submitIcon}}
                </v-icon>
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-form>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import PasswordField from '@/components/common/PasswordField';

export default {
  props: {
    createAdmin: {
      type: Boolean,
      default: false,
    },
  },
  components: {PasswordField},
  data() {
    return {
      showPassword: false,
      email: '',
      firstName: '',
      lastName: '',
      password: '',

      loading: false,
      conflictError: null,
      globalError: false,
      submitColor: 'primary',
      submitIcon: 'save',
    };
  },
  methods: {
    passwordFieldChanged(value) {
      this.password = value;
    },
    async submit() {
      this.conflictError = null;
      this.globalError = false;
      if (this.$refs.form.validate()) {
        const user = {
          email: this.email,
          firstName: this.firstName,
          lastName: this.lastName,
          password: this.password,
          organizationCode: null,
          group: 'ODAS',
        };
        try {
          this.loading = true;
          await this.$axios.post('/api/odas/user/create', user);
          this.submitColor = 'success';
          this.submitIcon = 'mdi-check-outline';
          setTimeout(() => this.clear(), 2000);
        } catch (e) {
          this.submitColor = 'error';
          this.submitIcon = 'mdi-exclamation';
          if (e.response && e.response.status === 409) {
            if (e.response.data && e.response.data.source === 'email') {
              this.conflictError = 'email';
            }
          } else {
            this.globalError = true;
            this.logError(e);
          }
        } finally {
          this.loading = false;
        }
      }
    },
    clear() {
      this.email = '';
      this.firstName = '';
      this.lastName = '';
      this.password = '';
      this.submitColor = 'primary';
      this.submitIcon = 'save';
      if (this.$refs.form) {
        this.$refs.form.reset();
      }
    },
    redirectList() {
      this.$router.push({
        name: 'administrators_repository',
      });
    },
  },
};
</script>

<style>
</style>
