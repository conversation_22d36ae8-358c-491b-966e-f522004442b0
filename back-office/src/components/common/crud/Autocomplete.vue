<template>
  <v-autocomplete :loading="loading"
                  :items="items"
                  :search-input.sync="search"
                  :label="label"
                  :item-text="itemText"
                  :item-value="itemValue"
                  hide-selected
                  :rules="rules"
                  :disabled="disabled"
                  :menu-props="closeOnSelect ? 'closeOnContentClick' : undefined"
                  v-model="selection"
                  :clearable="clearable"
                  flat
                  hide-no-data
                  :hide-details="hideDetails"
                  multiple
                  required
                  :filter="filter"
                  class="mx-3"
                  @input="update()"
                  :append-outer-icon="(createItem && search) ? 'add_circle' : null"
                  @click:append-outer="createItem ? doCreateItem(search): null"
                  ref="autocomplete"
                  :no-filter="!filter"
                  return-object>
    <template v-slot:selection="data">
      <v-chip class="ma-1 chip--select-multi" :select="data.select" v-if="!empty"
              close
              @click:close="selectItem(data.parent, data.item)">
        {{ data.item[itemText] }}
      </v-chip>
    </template>
    <template v-slot:item="data">
      <slot name="listItem" :listItem="data.item">
        <v-list-item-content>
          <v-list-item-title>{{ data.item[itemText] }}</v-list-item-title>
          <v-list-item-subtitle
            v-if="typeof resolveAndGet(data.item, itemSubText, itemSubTextFunction) !== 'undefined'">
            {{resolveAndGet(data.item, itemSubText, itemSubTextFunction)}}
          </v-list-item-subtitle>
        </v-list-item-content>
      </slot>
    </template>
  </v-autocomplete>
</template>

<script>
import jsog from 'jsog';
import _ from 'lodash';

export default {
  props: {
    clearable: {
      type: Boolean,
      default: false,
    },
    closeOnSelect: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    empty: {
      type: Boolean,
      default: false,
    },
    filter: {
      type: Function,
      default: null,
    },
    hideSelected: {
      type: Boolean,
      default: false,
    },
    hideDetails: {
      type: Boolean,
      default: true,
    },
    itemText: {
      type: String,
      default: 'title',
    },
    itemSubText: {
      type: String,
      default: 'description',
    },
    itemSubTextFunction: {
      type: Function,
      default: null,
    },
    itemValue: {
      type: String,
      default: 'code',
    },
    label: {
      type: String,
      default: 'Rechercher',
    },
    autocompleteOnFocus: { // if true, the autocompletion list will open on the first focus instead of waiting for the first typed char
      type: Boolean,
      default: false,
    },
    searchRestUrlApi: {
      type: String,
      required: true,
    },
    rules: Array,
    value: Array,
    createItem: {
      type: Function,
      required: false,
    },
    size: {
      default: 20,
    },
  },
  data: () => ({
    items: [],
    selection: [],
    search: '',
    loading: true,
  }),
  created() {
    this.sync();
    this.loading = false;
    if (this.autocompleteOnFocus) {
      this.doSearch('');
    }
  },
  watch: {
    value() {
      this.sync();
    },
    search(val) {
      this.doSearch(val);
    },
  },
  methods: {
    doCreateItem(search) {
      this.createItem(search);
      this.search = '';
    },
    doSearch: _.debounce(async function (val) {
      await this.debouncedDoSearch(val);
    }, 300),
    async debouncedDoSearch(val) {
      if (val || this.autocompleteOnFocus) {
        try {
          this.loading = true;
          const response = await this.$axios.get(this.searchRestUrlApi, {
            params: {
              page: 0,
              size: this.size,
              direction: 'ASC',
              filter: val, // @TODO: cleanup the legacy list API (https://odas-project.atlassian.net/browse/ODAS3-609)
              query: val,
            },
          });
          let data = jsog.decode(response.data);
          // use content attr for paginated request
          data = data.content || data;
          if (this.filter) {
            this.items = data.filter(d => this.filter(d));
          } else {
            this.items = data;
          }
        } finally {
          this.loading = false;
        }
      }
    },
    sync() {
      if (typeof this.value !== 'undefined') {
        this.selection = [...this.value];
      }
    },
    update() {
      this.$emit('input', this.selection);
      if (this.closeOnSelect) {
        // TODO: stop using v-autocomplete...due to 'closeOnContentClick', suggestions does not appear anymore without that ugly blur/focus
        this.$refs.autocomplete.blur();
        setTimeout(() => this.$refs.autocomplete && this.$refs.autocomplete.focus(), 250);
      }
    },
    selectItem(parent, item) {
      parent.selectItem(item);
      this.update();
    },
    resolveAndGet(obj, path, resolver) {
      if (resolver) {
        return resolver(obj);
      }
      return path.split('.').reduce((prev, curr) => (prev ? prev[curr] : null), obj);
    },
  },
};
</script>

<style>
</style>
