<template>

  <v-card flat id="list">
    <v-card-title>
      <v-row>
        <v-col cols="12" class="d-flex align-center">
          <!-- Slot main title -->
          <slot name="title"/>
          <!-- END Slot main title -->

          <v-spacer/>

          <v-text-field v-model="search"
                        append-icon="search"
                        placeholder="Je cherche"
                        autofocus
                        single-line
                        hide-details
                        clearable/>
        </v-col>
        <slot name="otherFilters"/>
      </v-row>
    </v-card-title>

    <v-card-text>
      <v-col cols="12">
        <v-data-table :headers="headers.filter(h => !h.hidden)"
                      :items="items.content"
                      :sort-by.sync="pagination.sortBy"
                      :page.sync="pagination.page"
                      :sort-desc.sync="pagination.descending"
                      :items-per-page.sync="pagination.rowsPerPage"
                      :footer-props="{
                        'items-per-page-options': rowsPerPage,
                        'items-per-page-text': 'Nb lignes par page',
                      }"
                      :server-items-length="items.totalNumberOfElements"
                      :loading="loading"
                      class="elevation-15"
                      item-key="code">
          <template v-slot:item="props">
            <!-- Slot row -->
            <tr @click="props.expand(!props.isExpanded)">
              <slot name="row"
                    :row="props.item"/>
            </tr>
            <!-- END Slot row -->
          </template>

          <template v-slot:expanded-item="props">
            <tr>
              <td :colspan="props.headers.length">
                <v-row>
                  <!-- Slot row expand -->
                  <slot name="details"
                        :item="props.item"/>
                  <!-- END Slot row expand -->
                </v-row>
              </td>
            </tr>
          </template>

          <template slot="no-data">
            <v-alert :value="true"
                     v-if="!loading"
                     outlined
                     color="error"
                     icon="warning">
              Aucun résultat disponible {{ search !== '' ? `pour la recherche: "${search}"` : '' }}
            </v-alert>
            <div v-else class="text-center">
              <v-progress-circular indeterminate/>
            </div>
          </template>

          <template v-slot:footer.page-text="props">
            Lignes de {{ props.pageStart }} à {{ props.pageStop }} sur {{ props.itemsLength }}
          </template>
        </v-data-table>
      </v-col>

      <!-- Slot footer -->
      <slot name="footer"/>

      <speed-dial :enable="speedDial"
                  :actions="actions"/>

    </v-card-text>
  </v-card>
</template>

<script>
import SpeedDial from '@/components/common/crud/SpeedDial.vue';
import _ from 'lodash';
import jsog from 'jsog';

export default {
  components: {SpeedDial},
  props: {
    headers: {
      type: Array,
    },
    actions: {
      type: Array,
    },
    listRestUrlApi: {
      type: String,
      default: '',
    },
    speedDial: {
      type: Boolean,
      default: true,
    },
    userId: {
      type: String,
      default: null,
    },
  },
  data: () => ({
    items: [],
    total: 0,
    search: '',
    loading: true,
    pagination: {
      page: 1,
      sortBy: [], // By default, do not apply sorting. This allows search ranking to do its job.
      descending: [false],
      rowsPerPage: 10,
    },
    rowsPerPage: [10, 25, 50, 100],
  }),
  mounted() {
    this.getDataFromApi();
  },
  watch: {
    search() {
      this.pagination.page = 1;
      this.getDataFromApi();
    },
    pagination: {
      handler: function () {
        this.getDataFromApi();
      },
      deep: true,
    },
    userId() {
      this.getDataFromApi();
    },
  },
  methods: {
    getDataFromApiDebounced() {
      this.loading = true;
      this.$axios.get(this.listRestUrlApi, {
        params: {
          page: this.pagination.page - 1, // @FIXME : offset or page, one must choose
          size: this.pagination.rowsPerPage,
          by: this.pagination.sortBy[0],
          direction: !this.pagination.descending[0] ? 'ASC' : 'DESC',
          filter: this.search,
          userFilter: this.userId,
        },
      })
        .then(value => {
          this.items = jsog.decode(value.data);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    getDataFromApi: _.debounce(function () {
      this.getDataFromApiDebounced();
    }, 300),
  },
};
</script>
