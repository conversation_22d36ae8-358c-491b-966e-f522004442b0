<template>
  <v-card class="mb-1" flat>
    <v-card-text>
      <v-autocomplete
        class="mb-2"
        :id="autocompleteId || 'occupationSelector'"
        return-object
        hide-no-data
        :loading="isLoading"
        v-model="dummyModel"
        :items="items"
        prepend-icon="search"
        @change="emitSelectedOccupation"
        :search-input.sync="query"
        hide-details
        item-text="title"
        item-value="code"
        :label="label || 'Associer un métier ERHGO'"
        no-filter
      />
      <v-list v-if="actualItems">
        <v-list-item
          v-for="occupation in actualItems"
          :key="occupation.id">
          <v-list-item-content>
            <v-list-item-title>{{ occupation.title }}</v-list-item-title>
          </v-list-item-content>

          <v-list-item-action>
            <v-icon @click="emitRemoveOccupation(occupation)">cancel</v-icon>
          </v-list-item-action>
        </v-list-item>
      </v-list>
    </v-card-text>
  </v-card>
</template>

<script>
import _ from 'lodash';

export default {
  name: 'ErhgoOccupationsSelector',
  props: {
    actualItems: {
      type: Array,
      required: false,
    },
    filterItemsIds: {
      type: Array,
      required: false,
    },
    label: {
      type: String,
      required: false,
    },
    autocompleteId: {
      type: String,
      required: false,
    },
  },
  data() {
    return {
      isLoading: false,
      query: null,
      dummyModel: null,
      items: [],
    };
  },
  watch: {
    query: _.debounce(async function doSearch(val) {
      await this.fetchOccupations(val);
    }, 700),
  },
  methods: {
    async fetchOccupations(query) {
      if (query) {
        try {
          this.isLoading = true;
          const data = (await this.$api.searchOccupations(query, false)).data;
          if (this.filterItemsIds) {
            this.items = data.filter(value => !this.filterItemsIds.includes(value.code));
          } else {
            this.items = data;
          }
        } finally {
          this.isLoading = false;
        }
      }
    },
    emitSelectedOccupation(targetOccupation) {
      if (targetOccupation) {
        this.$emit('onSelected', targetOccupation);
        this.items = [];
        this.dummyModel = null;
      }
    },
    emitRemoveOccupation(targetOccupation) {
      this.$emit('onRemove', targetOccupation);
    },
  },
};
</script>
