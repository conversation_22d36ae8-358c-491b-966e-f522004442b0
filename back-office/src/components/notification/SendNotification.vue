<template>
  <v-card v-if="!countNotificationService.loading">
    <v-card-title>Envoi de notifications</v-card-title>
    <v-card-text>
      <v-alert outlined type="success" dense :class="{'invisible': !sendNotificationService.success}">
        Le notification a été correctement envoyée
      </v-alert>
      <v-alert outlined type="error" dense
               v-if="countNotificationService.showAPIError || sendNotificationService.showAPIError">
        Envoi de la notification impossible, contactez le support.
      </v-alert>
      <template v-if="result">
        {{ result.totalUsers }} {{ result.totalUsers > 1 ? 'personnes verront' : 'personne verra' }} la notification
        dans leur centre de notification,
        dont {{ result.notifiableUsers }} {{ result.notifiableUsers > 1 ? 'recevront' : 'recevra' }} une notification
        sur smartphone.
        <v-text-field
            v-model="subject"
            label="Titre de la notification *"
            outlined
            dense
            class="pt-5"
            clearable
            :counter="100"
            hide-details="auto"
            maxlength="100"
        />
        <v-textarea
            v-model="content"
            label="Contenu de la notification *"
            outlined
            dense
            clearable
            :counter="500"
            maxlength="500"
            hide-details="auto"
        />
        <v-text-field
            v-model="link"
            label="Lien externe de la notification"
            outlined
            dense
            class="pt-5"
            clearable
            :counter="100"
            hide-details="auto"
            maxlength="100"
        />
      </template>
    </v-card-text>
    <v-card-actions>
      <v-btn
          outlined
          color="primary"
          @click="sendNotification()"
          :loading="sendNotificationService.loading"
          :disabled="!content || !subject"
      >
        Envoyer la notification
      </v-btn>
      <v-spacer/>
      <v-btn outlined color="error" @click="$emit('cancel')" :loading="sendNotificationService.loading">
        Fermer
      </v-btn>
    </v-card-actions>
  </v-card>
  <v-progress-circular indeterminate v-else/>
</template>
<script>
import NotificationService from '@/components/notification/SendNotificationService';

export default {
  name: 'send-notification',
  props: {
    service: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      result: null,
      subject: '',
      content: '',
      link: '',
      countNotificationService: new NotificationService(),
      sendNotificationService: new NotificationService(),
    };
  },
  async created() {
    this.result = await this.countNotificationService.countNotifiableUsers(this.service.computeAlgoliaQuery());
  },
  methods: {
    async sendNotification() {
      await this.sendNotificationService.sendNotification(this.service.computeAlgoliaQuery(), this.subject, this.content, this.link);
      this.subject = '';
      this.content = '';
      this.link = '';
    },
  },
};
</script>
