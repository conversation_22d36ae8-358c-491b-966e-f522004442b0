<template>
  <v-tooltip bottom v-if="transactionalBlacklisted">
    <template v-slot:activator="{ on, attrs }">
      <div
        v-bind="attrs"
        v-on="on"
      >
        <slot/>
        <v-icon color="red">mdi-minus-circle</v-icon>
        <em>(Blacklisté)</em>
      </div>
    </template>
    <span>Cet utilisateur a demandé à ne pas recevoir d'alerte par mail</span>
  </v-tooltip>
  <div v-else>
    <slot/>
  </div>
</template>
<script>

export default {
  name: 'user-email-wrapper',
  props: {
    transactionalBlacklisted: {
      type: Boolean,
      default: false,
    },
  },
};
</script>
