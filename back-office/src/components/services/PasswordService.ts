import Vue from 'vue';
import SafeService from 'odas-plugins/SafeService';

export default class PasswordService extends SafeService {

  static containsLowerCase(value: string | undefined) {
    return value?.toUpperCase() !== value;
  }

  static containsUpperCase(value: string | undefined) {
    return value?.toLowerCase() !== value;
  }

  static containsDigit(value: string | undefined) {
    return value && /\d/.test(value);
  }

  static isValidPassword(password: string | undefined) {
    return !!password
      && PasswordService.containsLowerCase(password)
      && PasswordService.containsUpperCase(password)
      && PasswordService.containsDigit(password);
  }

  async resetPassword(userId: string, newPassword: string) {
    return this.safeCall(async () => Vue.$api.setFrontOfficeUserPassword({userId: userId, password: newPassword}));
  }

  async resendInitialMail(userId: string) {
    return this.safeCall(async () => Vue.$api.resendInitialMail({userId: userId}));
  }

}
