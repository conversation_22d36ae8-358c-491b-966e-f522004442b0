import SafeService from 'odas-plugins/SafeService';
import Vue from 'vue';
import {
  AlreadyAppliedUser,
  CriteriaValue,
  MatchingUserSummary,
  OrganizationSummary,
  UserContactInfo,
  UserPage,
  UserProfileProgress,
  UserSummary,
} from 'erhgo-api-client';
//@ts-ignore
import store from '@/store';
import asyncDebounce from 'async-debounce';

export default class UserSearchService extends SafeService {

  private _userPage: UserPage | null = null;
  private _listUser: UserContactInfo[] = [];
  private _listRecruiters: OrganizationSummary[] = [];
  private _query: string | null = null;
  private _criteriaFilter: string[] = [];
  private _recruiterFilter: string[] = [];
  private _noOrgaFilter = false;
  private _page = 1;
  private _size = 50;
  private _totalNumberOfElements = 0;
  private _usersAlreadyAppliedToRecruitment: AlreadyAppliedUser[] = [];
  private _jobCriteria: CriteriaValue[] = [];

  sortBy = ['contactInformation.creationDate'];
  sortDesc = [true];

  selectedUsers: UserSummary[] = [];

  private _capacityThreshold = 0.8;
  private _applyMasteryLevelRange = false;
  private _postcode: (string | null) = null;

  constructor(private _group: string | null = null, private _jobId: string | null = null) {
    super();
    this.loading = true;
  }

  async fetchUserPage(size: number, page: number, search: string) {
    await this.safeCall(async () => {
      const userPage = (await Vue.$api.searchFrontOfficeUser(size, page, search)).data;

      const content = userPage.content.map(u => ({
        ...u,
        completionRate: this.getCompletionRate(u.userProfileProgress),
      }));
      this._userPage = {...userPage, content};
    });
  }

  private debouncedSearch = asyncDebounce(async () => {
    return this.safeCall(async () => {
      return this.doFetchUserList();
    });
  }, 500);

  private debouncedAllRecruiters = asyncDebounce(async () => {
    return this.safeCall(async () => {
      return this.fetchRecruitersList();
    });
  }, 500);

  fetchUserList() {
    this.debouncedSearch.fn();
  }

  async doFetchUserList() {
    await this.safeCall(async () => {
      let listUser;

      if (this._jobId) {
        this.sortBy = ['matchingRateInPercent', 'contactInformation.creationDate'];
        this.sortDesc = [true, true];
        listUser = (await Vue.$api.getUsersMatchingJob(
          this._jobId,
          this._page - 1,
          this._size,
          this._capacityThreshold || undefined,
          this.getRecruiterFilter(),
          this._applyMasteryLevelRange ? 0.5 : undefined,
          !!this._recruiterFilter.length,
          !!this._recruiterFilter.find(x => x === 'NO_ORGA'),
          this._postcode || undefined,
          this._criteriaFilter,
        )).data
        ;
        listUser.content = listUser.content.map(u => this.buildUserViewObject(u));
        this._usersAlreadyAppliedToRecruitment = (await Vue.$api.getJobCandidates(this._jobId)).data;
      } else {
        listUser = (await Vue.$api.searchFrontOfficeUserByGroups(
          this._recruiterFilter?.length ? this._recruiterFilter : [this._group!],
          this._page - 1,
          this._size,
          this._postcode || undefined,
          this._query || undefined,
          !!this._recruiterFilter?.length,
        ))
          .data
        ;
      }

      this._listUser = listUser.content.map(u => ({
        ...u,
        completionRate: this.getCompletionRate(u.userProfileProgress),
      }));
      this._totalNumberOfElements = listUser.totalNumberOfElements;
    });
  }

  async getUsersMatchingJobExport() {
    return Vue.$api.getUsersMatchingJobExport(
      this._jobId!,
      this._capacityThreshold || undefined,
      this.getRecruiterFilter(),
      this._applyMasteryLevelRange ? 0.5 : undefined,
      !!this._recruiterFilter.length,
      !!this._recruiterFilter.find(x => x === 'NO_ORGA'),
      this._postcode || undefined,
      this._criteriaFilter,
    );

  }

  private buildUserViewObject(user: MatchingUserSummary) {
    let unselectableIcon = '';
    let unselectableReason = '';
    if (!!user.candidatureId) {
      unselectableIcon = 'textsms';
      unselectableReason = `L'utilisateur a déjà envoyé sa candidature`;
    } else if (user.alreadyReceivedProposalEmail) {
      unselectableIcon = 'mdi-timer-sand';
      unselectableReason = `Une proposition a déjà été envoyée par mail`;
    } else if (user.contactInformation?.transactionalBlacklisted) {
      unselectableIcon = 'mdi-minus-circle';
      unselectableReason = `Cet utilisateur a demandé à ne pas recevoir d'alerte par mail`;
    }

    const criteriaValues = this.jobCriteria.filter(x => this._criteriaFilter.some(c => c === x.code));
    const missingCriteria = user.missingCriteria || [];
    const unknownCriteria = user.unknownCriteria || [];

    const criteriaSummary = `
        <span class="green--text">${(criteriaValues?.length || 0) - missingCriteria.length - unknownCriteria.length}</span> / <span class="red--text">${missingCriteria.length}</span> / <span class="orange--text">${unknownCriteria.length}</span>
    `;
    const criteriaDescription = `
        <ul>
        <li>Critères non validés&nbsp;: ${!missingCriteria.length ? 'Aucun' : criteriaValues.filter(cv => missingCriteria.includes(cv.code)).map(cv => cv.titleForBO).join(', ')}</li>
        <li>Critères inconnus&nbsp;: ${!unknownCriteria.length ? 'Aucun' : criteriaValues.filter(cv => unknownCriteria.includes(cv.code)).map(cv => cv.titleForBO).join(', ')}</li>
        </ul>
    `;
    return {
      ...user,
      matchingRateLabel: UserSearchService.getLabelForRate(user.matchingRateInPercent),
      isSelectable: !unselectableIcon,
      criteriaSummary,
      criteriaDescription,
      unselectableIcon,
      unselectableReason,
    };
  }

  async fetchRecruitersList() {
    if (this._noOrgaFilter) {
      this._listRecruiters = (await Vue.$api.getAllRecruiters()).data;
    } else {
      this._listRecruiters = (await Vue.$api.getAllRecruitersForOrganization(store.getters.organizationLoaded?.code)).data;
      this._recruiterFilter = this._listRecruiters
        .filter(r => r.code === store.getters.organizationLoaded?.code)
        .filter(o => !!(o.defaultProject && o.defaultProject.code))
        .map(o => o.defaultProject?.code || '' /* dummy ts type check*/);
    }
  }

  get userPage(): UserPage | null {
    return this._userPage;
  }

  get query(): string | null {
    return this._query;
  }

  set query(value: string | null) {
    this._query = value;
    this.debouncedSearch.fn();
  }

  set capacityThreshold(value: number) {
    this._capacityThreshold = value / 100;
    this.debouncedSearch.fn();
  }

  get capacityThreshold() {
    return this._capacityThreshold * 100;
  }

  set noOrgaFilter(value) {
    if (value !== this._noOrgaFilter) {
      this.recruiterFilter = [];
    }
    this._noOrgaFilter = value;

    this.debouncedAllRecruiters.fn();
    this.debouncedSearch.fn();
  }

  get noOrgaFilter() {
    return this._noOrgaFilter;
  }

  get applyMasteryLevelRange() {
    return this._applyMasteryLevelRange;
  }

  set applyMasteryLevelRange(value) {
    this._applyMasteryLevelRange = value;
    this.debouncedSearch.fn();
  }

  get recruiters() {
    return this._listRecruiters;
  }

  set recruiterFilter(value) {
    this._recruiterFilter = value;
    this.debouncedSearch.fn();
  }

  get recruiterFilter() {
    return this._recruiterFilter;
  }

  get matchingMode() {
    return !!this._jobId;
  }

  private getRecruiterFilter() {
    if (!!this._recruiterFilter.length) {
      return this._recruiterFilter;
    } else if (this._noOrgaFilter) {
      return undefined;
    } else {
      return [store.getters.organizationLoaded?.code];
    }
  }

  private getCompletionRate(value: UserProfileProgress | undefined): { color: string; title: string; progress: number } {
    let color = 'red';
    let title = 'Profil inconnu';
    let progress = 1;

    if (value) {
      if (value.experiencesCount === 0) {
        if (value.capacitiesCount < 10) {
          title = 'Profil non renseigné';
          progress = 10;
        } else {
          color = 'orange';
          title = "Pas d'expérience renseignée";
          progress = 25;
        }
      } else {
        if (value.capacitiesCount < 10) {
          title = "Peu d'expériences renseignées";
          color = 'orange';
          progress = 50;
        } else if (value.hasBehaviors) {
          title = 'Partie affinitaire incomplète';
          color = 'orange';
          progress = 50;
        } else {
          color = 'green';
          title = 'Profil initialisé';
          progress = 100;
        }
      }
    }
    return {color, title, progress};
  }

  static getLabelForRate(matchingRateInPercent: number | undefined) {
    if (matchingRateInPercent === undefined) return 'Inconnue';
    if (matchingRateInPercent >= 90) return 'Très forte';
    if (matchingRateInPercent >= 85) return 'Forte';
    if (matchingRateInPercent >= 80) return 'Normale';
    if (matchingRateInPercent >= 70) return 'Faible';
    if (matchingRateInPercent >= 50) return 'Très faible';
    return 'Aucune';
  }

  get page() {
    return this._page;
  }

  set page(page) {
    this._page = page;
    this.debouncedSearch.fn();
  }

  get rowsPerPage() {
    return this._size;
  }

  set rowsPerPage(pageSize) {
    this._size = pageSize;
    this.debouncedSearch.fn();
  }

  get postcode() {
    return this._postcode;
  }

  set postcode(postcode) {
    this._postcode = postcode;
    this.debouncedSearch.fn();
  }

  get totalNumberOfElements() {
    return this._totalNumberOfElements;
  }

  get usersAlreadyAppliedToRecruitment() {
    return this._usersAlreadyAppliedToRecruitment;
  }

  get listUser() {
    return this._listUser;
  }

  get jobCriteria() {
    return this._jobCriteria;
  }

  set jobCriteria(criteria) {
    this._jobCriteria = criteria;
    this.criteriaFilter = criteria.map(x => x.code);
  }

  set criteriaFilter(value) {
    this._criteriaFilter = value;
    this.debouncedSearch.fn();
  }

  get criteriaFilter() {
    return this._criteriaFilter;
  }
}

