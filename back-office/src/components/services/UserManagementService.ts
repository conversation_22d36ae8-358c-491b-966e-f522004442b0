import SafeService from 'odas-plugins/SafeService';
import Vue from 'vue';

export default class UserManagementService extends SafeService {
  async updateUsersChannels(usersId: string[], channelsToAdd: string[], channelsToRemove: string[]) {
    return this.safeCall(async () => Vue.$api.updateUsersChannels({
      channelsToAdd,
      channelsToRemove,
      usersId,
    }));
  }

  async sendCandidatureProposal(recruitmentId: number, usersId: string[]) {
    return this.safeCall(async () => Vue.$api.sendCandidatureProposal(
      recruitmentId,
      usersId,
    ));
  }
}
