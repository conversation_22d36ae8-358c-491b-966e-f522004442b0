import SafeService from 'odas-plugins/SafeService';
import Vue from 'vue';
import { UserSummary } from 'erhgo-api-client';

export default class ActivitySearchService extends SafeService {

  private _userList: UserSummary[] = [];

  async fetchBackOfficeUserList() {
    await this.safeCall(async () => {
      this._userList = (await Vue.$api.getBackOfficeUsers()).data;
    });
  }

  getUserName(userSummary: UserSummary) {
    return `${userSummary.firstName} ${userSummary.lastName ? userSummary.lastName : ''}`;
  }

  get userList(): UserSummary[] {
    return this._userList;
  }
}
