<template>
  <v-slider
    thumb-label
    thumb-size="40"
    v-model="capacityThresholdIndex"
    :tick-labels="capacityThresholdLabels.map(value => value.label)"
    :max="maxIndex"
    step="1"
    ticks="always"
    tick-size="5"
  >
    <template v-slot:thumb-label="{ value }">
      {{ `${capacityThresholdLabels[value].value} %` }}
    </template>
  </v-slider>
</template>
<script>
export default {
  name: 'CapacityThreshold',
  props: {
    value: {
      type: Number,
      required: false,
      default: 80,
    },
    disableNone: {
      default: false,
    },
  },
  data() {
    return {
      capacityThresholdIndex: 3,
      capacityThresholdLabelsRaw: [
        {label: 'Aucune', value: 0},
        {label: 'Très faible', value: 50},
        {label: 'Faible', value: 70},
        {label: 'Normale', value: 80},
        {label: 'Forte', value: 85},
        {label: 'Très forte', value: 90},
      ],
    };
  },
  watch: {
    thresholdValue() {
      this.$emit('input', this.thresholdValue);
    },
    value() {
      if (this.value !== this.thresholdValue) {
        const index = this.capacityThresholdLabels.findIndex(c => c.value === this.value);
        this.capacityThresholdIndex = index >= 0 ? index : 3;
      }
    },
    disableNone(isDisabled, wasDisabled) {
      if (isDisabled && !wasDisabled) {
        this.capacityThresholdIndex = this.maxIndex;
      }
      if (!isDisabled && wasDisabled) {
        this.capacityThresholdIndex++;
      }
    },
  },
  computed: {
    maxIndex() {
      return this.disableNone ? 4 : 5;
    },
    thresholdValue() {
      return this.capacityThresholdLabels[this.capacityThresholdIndex].value;
    },
    capacityThresholdLabels() {
      // Caution: capacityThresholdIndex needs to be re-calculated through disableNone before recomputing capacityThresholdLabels
      // to avoid having capacityThresholdIndex > capacityThresholdLabels length
      return (this.disableNone && this.capacityThresholdIndex <= this.maxIndex) ? this.capacityThresholdLabelsRaw.slice(1) : this.capacityThresholdLabelsRaw;
    },
  },
};
</script>
