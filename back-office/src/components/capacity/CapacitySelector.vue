<template>
  <v-col cols>
    <h3>Capacités</h3>
    <autocomplete v-model='capacities'
                  searchRestUrlApi='/api/odas/capacity/list'
                  label='Chercher une capacité'
                  :itemSubTextFunction='capacitySubText'
                  empty
    />
    <template v-for='capacity in capacities'>
      <v-list-item :key='`capa-${capacity.id}`'>
        <v-list-item-content>
          <v-list-item-title>{{ capacity.code }} - {{ capacity.title }}</v-list-item-title>
          <v-list-item-subtitle v-if='inducedCapacityText(capacity)'>{{ inducedCapacityText(capacity) }}
          </v-list-item-subtitle>
        </v-list-item-content>
        <v-list-item-action>
          <v-btn text
                 icon
                 color='primary'
                 class='mr-2 text-center'
                 @click.native='capacities = capacities.filter(b => b.id !== capacity.id)'
          >
            <v-icon>delete</v-icon>
          </v-btn>
        </v-list-item-action>
      </v-list-item>
      <v-divider :key='`div-${capacity.id}`'/>
    </template>
  </v-col>
</template>
<script>
import Autocomplete from '@/components/common/crud/Autocomplete.vue';

export default {
  name: 'capacity-selector',
  components: {Autocomplete},
  props: {
    value: {type:Array, default:() => []},
  },
  data() {
    return {
      capacities: [],
    };
  },
  methods: {
    inducedCapacityText(capacity) {
      if (capacity.inducedCapacities && capacity.inducedCapacities.length) {
        const joined = capacity.inducedCapacities.map(c => `${c.code} - ${c.title}`).join(', ');
        return `Induit ${joined}`;
      }
      return '';
    },

    capacitySubText(capacity) {
      const inducedText = this.inducedCapacityText(capacity);
      return `${capacity.code} ${inducedText ? ` - ${inducedText}` : ''}`;
    },
  },
  created() {
    this.capacities = [...this.value];
  },
  watch: {
    capacities: {
      handler(newCapacities) {
        this.$emit('input', newCapacities);
      },
      deep: true,
    },
    value: {
      handler(capacities) {
        this.value = capacities;
      },
      deep: true,
    },
  },
};
</script>
