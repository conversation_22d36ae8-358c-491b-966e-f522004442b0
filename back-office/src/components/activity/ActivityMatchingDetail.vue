<template>
  <v-card flat>
    <v-card-title class="justify-center">
      <h2 class="mr-4 d-flex align-center">
        <v-icon color="primary" class="mr-2">
          star
        </v-icon>
        {{activity.title}}
      </h2>
      <capacity-chip v-for="capacity in activity.inducedCapacities"
                     :key="capacity.id"
                     :capacity="capacity"
                     :disabled="!isPresent(capacity)"
                     :outline="isHoveredCapacity(capacity)"
                     @mouseover="setHoveredCapacity(capacity)"
                     @mouseout="unsetHoveredCapacity"
      />
    </v-card-title>
    <v-card-text>
      <h3 class="d-flex align-center mb-2">
        <v-icon color="primary" class="mr-2">
          work
        </v-icon>
        Expériences&nbsp;:
      </h3>
      <!-- thanks to value, expansion panel is fully open  -->
      <v-expansion-panels multiple :value="filteredExperiences.map((_, index) => index)">
        <v-expansion-panel v-for="experience in filteredExperiences" :key="experience.id">
          <v-expansion-panel-header>
            <v-col cols="12">
              <h4 class="d-flex align-center">
                <v-icon color="primary" class="mr-2">
                  next_week
                </v-icon>
                [{{ experience.durationType ? $t(`durationType.${experience.durationType}`) : '' }}
                {{ experience.duration ? $t(`duration.${experience.duration}`) : '' }}]
                [{{ experience.experienceType ? $t(`experience.${experience.experienceType}`) : '' }}]
                {{ experience.jobTitle }} chez {{ experience.organizationName }}
              </h4>
            </v-col>
          </v-expansion-panel-header>
          <v-expansion-panel-content>
            <ul>
              <li v-for="experienceActivity in experience.activities" :key="experienceActivity.id">
                {{ experienceActivity.title }}
                <capacity-chip v-for="capacity in experienceActivity.inducedCapacities"
                               :key="capacity.id"
                               :capacity="capacity"
                               :outline="isHoveredCapacity(capacity)"
                               @mouseover="setHoveredCapacity(capacity)"
                               @mouseout="unsetHoveredCapacity"
                />
                <v-expansion-panels v-if="hasDeepInducedCapacities(experienceActivity)"
                                    multiple>
                  <v-expansion-panel class="my-1">
                    <v-expansion-panel-header>
                      <v-row align="center">
                        <div class="pr-3">
                          <v-icon color="primary">
                            mdi-certificate
                          </v-icon>
                          Capacités induites
                        </div>
                        <capacity-chip
                          v-for="capacity in experienceActivity.allInducedCapacitiesGroupedByPresence.present"
                          :key="capacity.id"
                          :capacity="capacity"
                          :outline="isHoveredCapacity(capacity)"
                          @mouseover="setHoveredCapacity(capacity)"
                          @mouseout="unsetHoveredCapacity"
                        />
                      </v-row>
                    </v-expansion-panel-header>
                    <v-expansion-panel-content>
                      <v-col cols="12">
                        <v-row>
                          <capacity-chip
                            v-for="capacity in experienceActivity.allInducedCapacitiesGroupedByPresence.absent"
                            :key="capacity.id"
                            :capacity="capacity"
                          />
                        </v-row>
                      </v-col>
                    </v-expansion-panel-content>
                  </v-expansion-panel>
                </v-expansion-panels>
              </li>
            </ul>
          </v-expansion-panel-content>
        </v-expansion-panel>
      </v-expansion-panels>
    </v-card-text>
  </v-card>
</template>

<script>

import CapacityChip from '@/components/capacity/CapacityChip.vue';
import CapacityUtils from '@/components/mixins/CapacityUtils';

export default {
  mixins: [CapacityUtils],
  components: {
    CapacityChip,
  },
  props: {
    activity: Object,
    experiences: Array,
  },
  data() {
    return {
      hoveredCapacityId: null,
    };
  },
  computed: {
    activityInducedCapacitiesId() {
      return this.activity.inducedCapacities.map(capacity => capacity.id);
    },
    filteredExperiences() {
      return this.experiences.map(experience => ({
        ...experience,
        activities: experience.activities
          .map(experienceActivity => ({
            ...experienceActivity,
            allInducedCapacitiesGroupedByPresence: this.groupActivityInducedCapacitiesByPresenceInList(
              experienceActivity,
              this.activity.inducedCapacities,
              true,
            ),
          })).filter(experienceActivity =>
            experienceActivity.inducedCapacities.some(capacity => this.activityInducedCapacitiesId.includes(capacity.id)) ||
            experienceActivity.allInducedCapacitiesGroupedByPresence.present.length > 0),
      })).filter(experience => experience.activities.length);
    },
    allInducedCapacitiesIds() {
      return this.filteredExperiences.flatMap(experience =>
        experience.activities.flatMap(experienceActivity =>
          experienceActivity.inducedCapacities
            .concat(...experienceActivity.allInducedCapacitiesGroupedByPresence.present)))
        .map(capacity => capacity.id);
    },
  },
  methods: {
    isPresent(capacity) {
      return this.allInducedCapacitiesIds.includes(capacity.id);
    },
    isHoveredCapacity(capacity) {
      return this.hoveredCapacityId === capacity.id;
    },
    setHoveredCapacity(capacity) {
      if (this.activityInducedCapacitiesId.includes(capacity.id)) {
        this.hoveredCapacityId = capacity.id;
      }
    },
    unsetHoveredCapacity() {
      this.hoveredCapacityId = null;
    },
    hasDeepInducedCapacities(activity) {
      return activity.allInducedCapacitiesGroupedByPresence.present.length > 0 ||
        activity.allInducedCapacitiesGroupedByPresence.absent.length > 0;
    },
  },

};
</script>

<style lang="scss" scoped>
.mission-list {
  list-style-type: none;
}
</style>
