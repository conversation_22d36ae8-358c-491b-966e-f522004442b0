<template>
  <div>
    <v-dialog :value="true" v-if="showError" persistent shrink width="50%">
      <v-card>
        <v-card-title
          class="text-h5 grey lighten-2"
          primary-title
        >
          Impossible de positionner le candidat
          <v-spacer/>
          <v-icon color="error">error</v-icon>
        </v-card-title>
        <v-card-text>
          {{ errorMessage }}
        </v-card-text>
        <v-divider/>
        <v-card-actions>
          <v-spacer/>
          <v-btn
            id="close-user-matching-popin"
            color="primary"
            class='float-right'
            @click="showError=false"
          >
            Fermer
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-if="showCandidaturePreview" v-model="showCandidaturePreview" shrink width="70%">
      <v-card>
        <v-card-title>
          Correspondances entre le poste et le candidat
        </v-card-title>
        <v-card-text>
          <v-row no-gutters class="pb-4">
            <v-col cols="12" sm="6">
              <experiences-sumup :experiences="candidaturePreview.experiences"/>
            </v-col>
          </v-row>
          <v-row no-gutters class="mt-0 pt-0">
            <v-col cols v-if="hasAppliedToRecruitment">
              <template>
                <p class="text-caption font-italic pa-0 ma-0">
                  <v-icon color="success" small>check</v-icon>
                  Cette personne est déjà positionnée sur au moins un recrutement
                </p>
              </template>
            </v-col>
          </v-row>
          <v-row no-gutters class="mt-0 pt-0">
            <v-col cols v-if="!showRecruitmentsList">
              <v-btn rounded
                     class="showRecruitments"
                     color="primary"
                     @click.stop.native
                     :loading="generatingCandidature"
                     id="showUserMatchingRecruitments"
                     @click="loadRecruitmentsList">
                <v-icon>mdi-plus</v-icon>
                Voir les recrutements
              </v-btn>
            </v-col>
            <v-spacer/>
            <v-col cols>
              <v-btn rounded
                     outlined
                     @click="cleanRecruitmentsList()">
                Fermer
              </v-btn>
            </v-col>
          </v-row>
          <v-row no-gutters>
            <v-col cols>
              <v-data-table
                v-if="showRecruitmentsList"
                :headers="headers"
                :items="service.recruitments"
                :loading="service.loading"
                class="elevation-15 mt-4 matching-recruitments"
              >
                <template v-slot:item="props">
                  <tr :id="`matching-rec-${props.item.id}`">
                    <td class="text-left">{{ props.item.profileTitle }}</td>
                    <td class="text-left">{{ props.item.city }}
                      <em v-if="props.item.radiusInKm">(Rayon&nbsp;: {{ props.item.radiusInKm }}km)</em>
                    </td>
                    <td class="text-left">{{
                        props.item.distance ? convertInKilometers(props.item.distance) : 'Inconnue'
                      }}
                    </td>
                    <td class="text-center">
                      <template v-if="props.item.isRefused">
                        <v-icon color="orange" small>warning</v-icon>
                        Candidat refusé
                      </template>
                      <template v-else-if="props.item.hasApplied">
                        <v-icon color="success" small>check</v-icon>
                        Candidat déjà positionné
                      </template>
                      <v-btn
                        v-else
                        small
                        rounded
                        color="primary"
                        @click="generateCandidature(props.item.id)"
                      >
                        Positionner
                      </v-btn>
                    </td>
                  </tr>
                </template>
                <template v-slot:footer.page-text="props">
                  Lignes de {{ props.pageStart }} à {{ props.pageStop }} sur {{ props.itemsLength }}
                </template>
              </v-data-table>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <div class="text-center py-2">
      <v-tooltip left>
        <template v-slot:activator="{ on }">
          <div v-on="on">
            <v-btn small
                   class="candidateCompare"
                   rounded
                   outlined
                   color="primary"
                   @click.stop.native
                   :loading="generatingCandidature"
                   :disabled="disabled"
                   @click="previewCandidature">
              Comparer
            </v-btn>
            <template v-if="isRefused">
              <p class="text-caption font-italic pa-0 ma-0">
                <v-icon color="orange" small>warning</v-icon>
                Candidat déjà&nbsp;refusé
              </p>
            </template>
            <template v-else-if="hasAppliedToRecruitment">
              <p class="text-caption font-italic pa-0 ma-0">
                <v-icon color="success" small>check</v-icon>
                Candidat déjà&nbsp;positionné
              </p>
            </template>
          </div>
        </template>
        <span v-if="disabled">Aucun profil de recrutement, impossible de positionner le candidat</span>
        <span v-else>Comparer le candidat au poste</span>
      </v-tooltip>
    </div>
  </div>
</template>
<script>
import ExperiencesSumup from '@/views/setup/candidature/common/ExperiencesSumupView';
import CandidateRecruitmentListService from '../../views/setup/recruitment/list/CandidateRecruitmentListService';

export default {
  name: 'generate-candidature-btn',
  components: {
    ExperiencesSumup,
  },
  props: {
    userId: {
      type: String,
      required: true,
    },
    job: {
      type: Object,
      required: true,
    },
    organizationCode: {
      type: String,
      required: true,
    },
    hasAlreadyAppliedToRecruitment: {
      type: Boolean,
      required: true,
    },
    isRefused: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      errorMessage: null,
      showError: false,
      generatingCandidature: false,
      generatedCandidatureId: null,
      candidaturePreview: null,
      service: CandidateRecruitmentListService,
      showRecruitmentsList: false,
      headers: [
        {
          text: 'Profil de recrutement',
          align: 'left',
          sortable: true,
          value: 'profileTitle',
        },
        {
          text: 'Ville',
          align: 'left',
          sortable: true,
          value: 'city',
        },
        {
          text: 'Distance',
          align: 'left',
          sortable: true,
          value: 'distance',
        },
        {
          text: 'Candidat positionné ?',
          align: 'center',
          value: 'hasApplied',
          sortable: true,
        },
      ],
    };
  },
  computed: {
    disabled() {
      return ((this.job.recruitmentProfiles && !this.job.recruitmentProfiles.filter(rp => rp.qualified).length)
          || this.job.recruitmentProfileCount === 0)
        && this.job.state !== 'PUBLISHED';
    },
    hasAppliedToRecruitment() {
      return this.hasAlreadyAppliedToRecruitment || this.service?.recruitments?.some(r => r.hasApplied);
    },
    showCandidaturePreview: {
      get() {
        return !!this.candidaturePreview;
      },
      set(showPreview) {
        if (!showPreview) {
          this.candidaturePreview = null;
        }
      },
    },
  },
  methods: {
    async previewCandidature() {
      try {
        const preview = (await this.$api.getCandidaturePreview(this.job.id, this.userId)).data;
        const experiences = preview.experiences;
        this.candidaturePreview = {
          experiences,
        };
      } catch (e) {
        if (e?.response?.status === 400) {
          this.errorMessage = 'Le poste cible ne dispose pas de recrutement ouvert sur un profil sans activité ou contexte obligatoire.';
        } else {
          this.errorMessage = 'Une erreur technique est survenue, veuillez ré-essayer ou contacter le support.';
        }
        this.showError = true;
      } finally {
        this.generatingCandidature = false;
      }
    },
    async loadRecruitmentsList() {
      this.service = new CandidateRecruitmentListService(this.job.id, this.userId);
      this.showRecruitmentsList = true;
    },
    cleanRecruitmentsList() {
      this.service = null;
      this.showRecruitmentsList = false;
      this.candidaturePreview = null;
    },
    async generateCandidature(recruitmentId) {
      this.generatingCandidature = true;
      this.showMessage = null;
      const userId = this.userId;
      const recruitmentsId = [recruitmentId];
      try {
        await this.$api.generateCandidaturesOnRecruitments({userId, recruitmentsId});
        this.service.initialize();
        this.$emit('onGenerateCandidature');
      } catch (e) {
        this.errorMessage = 'Une erreur technique est survenue, veuillez contacter le support.';
        this.showError = true;
      } finally {
        this.generatingCandidature = false;
      }
    },
    convertInKilometers(distance) {
      return Math.round(distance / 1000) + ' km';
    },
  },
};
</script>
