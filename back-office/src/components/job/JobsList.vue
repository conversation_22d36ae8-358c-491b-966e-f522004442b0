<template>
  <div>
    <v-alert v-if="service.jobDeletable || customMessage"
             dense
             :class="{invisible:!service.showDeletionResult && !customMessage}"
             :type="(service.deletionResult && service.deletionResult.type) || 'success'" outlined>
      {{ (service.deletionResult && service.deletionResult.message) || customMessage }}
    </v-alert>
    <v-data-table :headers="headers"
                  :items="items ? items.content : []"
                  :sort-by.sync="pagination.sortBy"
                  :page.sync="pagination.page"
                  :sort-desc.sync="pagination.descending"
                  :items-per-page.sync="pagination.itemsPerPage"
                  :footer-props="{
                  'items-per-page-options': itemsPerPage,
                  'items-per-page-text': 'Nb lignes par page',
                }"
                  :loading="loading"
                  item-key="id"
                  :server-items-length="items ? items.totalNumberOfElements : 0"
                  ref="jobsTable"
                  no-data-text="Aucun poste correspondant"
    >
      <v-progress-linear slot="progress"
                         indeterminate/>
      <template v-slot:item="props">
        <tr class="jobItem">
          <template v-if="!!userId">

            <td class="text-left">{{
                getLabelForRate(props.item.matchingRateInPercent)
              }}
            </td>
            <td class="text-left">
              <v-tooltip bottom>
                <template v-slot:activator="{ on }">
                  <div class="font-weight-bold" v-on="on" v-html="props.item.criteriaSummary"/>
                </template>
                <div v-html="props.item.criteriaDescription"/>
              </v-tooltip>
            </td>
          </template>
          <td class="text-left">{{ props.item.title }}</td>
          <td class="text-left" v-if="isEnterprise">{{ props.item.service }}</td>
          <td class="text-left" v-else :class="{'font-italic': !props.item.employerTitle}">
            {{ props.item.employerTitle || '(Non précisé)' }}
          </td>
          <td class="text-left">{{ props.item.recruiterTitle }}</td>
          <td class="text-center">
            <v-chip class="ma-1" small
                    :class="`${getColorState(props.item.state)}`">
              {{ getState(props.item.state) }}
            </v-chip>
          </td>
          <td class="text-right" v-if="!!userId">
            <generate-candidature-btn :user-id="userId"
                                      :organization-code="organization ? organization.code : props.item.recruiterCode"
                                      :job="props.item"
                                      @onGenerateCandidature="$emit('onGenerateCandidature')"
                                      :has-already-applied-to-recruitment="!!props.item.candidatureId"
                                      :is-refused="props.item.anyRefusedCandidature"
            />
          </td>
          <td>
            <v-tooltip left>
              <template v-slot:activator="{ on }">
                <v-btn text
                       icon
                       color="primary"
                       @click.stop.native
                       v-on="on"
                       :to="{
                           name: 'front_user_list_by_job',
                           params: {organization_code: organization ? organization.code : props.item.recruiterCode, job_id: props.item.id},
                    }">
                  <v-icon>mdi-account-multiple</v-icon>
                </v-btn>
              </template>
              <span>Candidats correspondants</span>
            </v-tooltip>
            <v-tooltip left>
              <template v-slot:activator="{ on }">
                <v-btn text
                       icon
                       color="primary"
                       v-on="on"
                       @click.stop.native
                       :to="{
                        name: 'organizations_jobs_edit',
                        params: {
                          organization_code: organization ? organization.code : props.item.recruiterCode,
                          job_id:props.item.id, mode: 'edit'
                        }
                     }">
                  <v-icon>edit</v-icon>
                </v-btn>
              </template>
              <span>Modifier</span>
            </v-tooltip>
            <v-tooltip left>
              <template v-slot:activator="{ on }">
                <v-btn text
                       icon
                       color="primary"
                       v-on="on"
                       @click="props.expand(!props.isExpanded)"
                       :disabled="!isViewableState(props.item)"
                >
                  <v-icon>mdi-account-card-details</v-icon>
                </v-btn>
              </template>
              <span>Profils de recrutement</span>
            </v-tooltip>
            <v-tooltip left v-if="service.jobDeletable">
              <template v-slot:activator="{ on }">
                <v-btn text
                       :id="`deleteJob${props.index}`"
                       icon
                       color="primary"
                       v-on="on"
                       :loading="isDeletionLoading(props.item.id)"
                       @click="deleteJob(props.item.id)"
                >
                  <v-icon>delete</v-icon>
                </v-btn>
              </template>
              <span>Supprimer</span>
            </v-tooltip>
          </td>
        </tr>
      </template>
      <template v-slot:expanded-item="props">
        <tr>
          <td :colspan="props.headers.length">
            <v-row justify="center">
              <v-col>
                <v-list subheader>
                  <v-subheader>
                    <h2>
                      Profils de recrutement
                      <v-btn
                        text
                        color="primary"
                        class="mr-2 text-center"
                        @click.stop.native
                        :to="{
                      name: 'recruitment_profile_create',
                      params: {
                        organization_code: props.item.recruiterCode,
                        job_id: props.item.id
                      }
                    }"
                      >
                        <v-icon>add</v-icon>
                        {{ $t('action.create') }}
                      </v-btn>
                    </h2>
                  </v-subheader>
                  <template v-for="(recruitmentProfile, index) in props.item.recruitmentProfiles">
                    <v-list-item
                      :key="recruitmentProfile.id">
                      <v-list-item-content>
                        <v-list-item-title>{{ recruitmentProfile.title }}</v-list-item-title>
                      </v-list-item-content>
                      <v-list-item-action class="ma-0">
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on }">
                            <v-btn text
                                   icon
                                   color="primary"
                                   @click.stop.native
                                   v-on="on"
                                   :to="{
                              name: 'recruitment_profile_edit',
                              params: {
                                organization_code: props.item.recruiterCode,
                                job_id: props.item.id,
                                profile_id: recruitmentProfile.id
                              }
                            }">
                              <v-icon>edit</v-icon>
                            </v-btn>
                          </template>
                          <span>Voir / Modifier</span>
                        </v-tooltip>
                      </v-list-item-action>
                      <v-list-item-action class="ma-0">
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on }">
                            <v-btn text
                                   icon
                                   color="primary"
                                   @click.stop
                                   v-on="on"
                                   :class="{invisible:  !recruitmentProfile.qualified}"
                                   :disabled="!isViewableState(props.item)"
                                   :to="{
                                name: 'recruitment_create',
                                params: {
                                  organization_code: organization ? organization.code : props.item.recruiterCode,
                                  profile: recruitmentProfile,
                                  job: props.item
                                }
                              }">
                              <v-icon>mdi-bullhorn</v-icon>
                            </v-btn>
                          </template>
                          <span>Créer un recrutement</span>
                        </v-tooltip>
                      </v-list-item-action>
                    </v-list-item>
                    <v-divider
                      v-if="index < props.item.recruitmentProfiles.length - 1"
                      :key="index"
                      :inset="false"
                    />
                  </template>
                </v-list>
              </v-col>
            </v-row>
          </td>
        </tr>
      </template>
      <template v-slot:footer.page-text="props">
        {{ getLinesMessages(props.pageStart, props.pageStop, props.itemsLength) }}
      </template>
    </v-data-table>
  </div>
</template>
<script>
import appStore from '@/store';
import GenerateCandidatureBtn from './GenerateCandidatureBtn';
import UserSearchService from '../services/UserSearchService';
import {OrganizationType} from 'erhgo-api-client';

export default {
  components: {GenerateCandidatureBtn},
  props: {
    selected_job_id: String,
    customMessage: {
      type: String,
      default: '',
    },
    service: Object,
    userId: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      showMessage: null,
      itemsPerPage: [10, 25, 50, 100, 200],
      headers: [],
    };
  },
  computed: {
    items() {
      return this.service.jobPage;
    },
    loading() {
      return this.service.loading;
    },
    pagination() {
      return this.service.pagination;
    },
    search() {
      return this.service.search;
    },
    organization() {
      return appStore.getters.organizationLoaded;
    },
    isEnterprise() {
      return this.organization?.organizationType === OrganizationType.ENTERPRISE;
    },
  },
  mounted() {
    if (this.selected_job_id) {
      // FIXME: refactor next line when https://github.com/vuetifyjs/vuetify/issues/2890 is available
      this.$set(this.$refs.jobsTable.expansion, this.selected_job_id, true);
    }

    if (!!this.userId) {
      this.headers = [
        {
          text: 'Correspondance',
          icon: '',
          align: 'left',
          sortable: true,
          value: 'matchingRateInPercent',
        }, {
          text: 'Critères',
          icon: '',
          align: 'left',
          sortable: false,
        }];
    }

    this.headers = [...this.headers,
      {
        text: this.$t('ref.headers.job.title'),
        icon: '',
        align: 'left',
        sortable: true,
        value: 'title',
      },
      (
        this.isEnterprise ?
          {
            text: this.$t('ref.headers.job.service'),
            icon: '',
            align: 'left',
            sortable: true,
            value: 'service',
          }
          :
          {
            text: 'Employeur',
            icon: '',
            align: 'left',
            sortable: true,
            value: 'employer',
          }
      ),
      {
        text: 'Organisation',
        icon: '',
        align: 'left',
        sortable: true,
        value: 'recruiter',
      },
      {
        text: 'Statut',
        align: 'center',
        icon: '',
        sortable: false,
      },
      {
        text: '',
        align: 'right',
        icon: '',
        sortable: false,
      },
    ];

    if (!!this.userId) {
      this.headers = [...this.headers, {
        text: '',
        align: 'right',
        icon: '',
        sortable: false,
      }];
    }
  },
  watch: {
    pagination: {
      handler(newVal, oldVal) {
        if (newVal && oldVal) {
          this.service.fetchJobs();
        }
      },
      deep: true,
    },
  },
  methods: {
    isViewableState(item) {
      return item.state === 'PUBLISHED'
        || item.state === 'REEVALUATION_NOT_FINISHED'
        || item.state === 'FINISHED';
    },
    getNoDataMessage(search) {
      const extraMessage = search !== '' ? ` avec: "${search}"` : '';
      return `Aucun résultat disponible ${extraMessage}`;
    }
    ,
    getLinesMessages(pageStart, pageStop, itemsLength) {
      return `Lignes de ${pageStart} à ${pageStop} sur ${itemsLength}`;
    }
    ,
    getState(state) {
      let text = '';
      switch (state) {
        case 'PUBLISHED':
        case 'REEVALUATION_NOT_FINISHED':
        case 'FINISHED':
          text = 'Finalisé';
          break;
        case 'INFOS_PROVIDED':
        case 'MISSIONS_PROVIDED':
        case 'BEHAVIORS_PROVIDED':
        default:
          text = 'Brouillon';
          break;
      }
      return text;
    },
    getColorState(state) {
      let color = '';
      switch (state) {
        case 'PUBLISHED':
        case 'REEVALUATION_NOT_FINISHED':
        case 'FINISHED':
          color = 'green accent-1';
          break;
        case 'INFOS_PROVIDED':
        case 'MISSIONS_PROVIDED':
        case 'BEHAVIORS_PROVIDED':
        default:
          color = 'orange accent-1';
          break;
      }
      return color;
    },
    getLabelForRate(matchingRateInPercent) {
      return UserSearchService.getLabelForRate(parseInt(matchingRateInPercent));
    },
    isDeletionLoading(jobId) {
      return this.service.isDeletionLoading(jobId);
    },
    async deleteJob(jobId) {
      await this.service.deleteJob(jobId);
    },
  },
};
</script>

