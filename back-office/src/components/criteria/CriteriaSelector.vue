<template>
  <v-progress-circular class="ma-5" indeterminate v-if="service.loading"/>
  <v-row no-gutters v-else>
    <v-col cols="12" :md="column?12:6" class="text-center  mt-3" :style="!column ?'border-right: 1px solid grey' : ''">
      <h1>Critères à sélection multiples</h1>
      <slot name="multipleTitle">
        <p class="text-caption">
          Vous pouvez choisir plusieurs valeurs de critères auxquelles le candidat sera confronté
        </p>
      </slot>
      <v-expansion-panels multiple
                          accordion
                          flat
                          :value="service.multipleCriteriaIndexes">
        <v-expansion-panel :key="`criterion-multiple-${index}`"
                           v-for="(criterion, index) in service.multipleCriteria">
          <v-expansion-panel-header><h4>{{ criterion.title }}</h4></v-expansion-panel-header>
          <v-expansion-panel-content>
            <ul>
              <li v-for="(value, indexValue) in criterion.criteriaValues" :key="indexValue">
                <v-checkbox class="mt-1"
                            hide-details
                            multiple
                            v-model="service.selectedMultipleCriteria"
                            :label="value[valueTitleSource]"
                            :value="value"
                />
              </li>
            </ul>
          </v-expansion-panel-content>
          <v-divider/>
        </v-expansion-panel>
      </v-expansion-panels>

    </v-col>
    <v-col cols="12" :md="column?12:6" class="text-center mt-3">
      <h1>Critères de type seuil</h1>
      <slot name="thresholdTitle">
        <p class="text-caption">
          Choisissez le niveau minimal que doit atteindre le candidat
        </p>
      </slot>
      <v-expansion-panels multiple
                          accordion
                          flat
                          :value="service.thresholdCriteriaIndexes">
        <v-expansion-panel :key="`criterion-threshold-${index}`"
                           v-for="(criterion, index) in service.thresholdCriteria">
          <v-expansion-panel-header><h4>{{ criterion.title }}</h4></v-expansion-panel-header>
          <v-expansion-panel-content>
            <v-checkbox class="mt-1"
                        v-model="service.selectedThresholdCriteria[index]"
                        hide-details
                        :label="value[valueTitleSource]"
                        :value="value"
                        v-for="(value, indexValue) in criterion.criteriaValues"
                        :key="indexValue"
            />
          </v-expansion-panel-content>
          <v-divider/>
        </v-expansion-panel>
      </v-expansion-panels>
    </v-col>
  </v-row>
</template>
<script>
import CriteriaService from './CriteriaService';

export default {
  name: 'criteria-selector',
  data() {
    return {
      service: new CriteriaService(this.value.map(c => c.code)),
    };
  },
  props: {
    value: {
      required: true,
      type: Array,
    },
    showAsFO: {
      default: false,
    },
    column: {
      default: false,
    },
  },
  computed: {
    criteria() {
      return this.service.criteria;
    },
    allValues() {
      return this.service.allValues;
    },
    valueTitleSource() {
      return this.showAsFO ? 'titleStandalone' : 'titleForBO';
    },

  },
  watch: {
    allValues: {
      deep: true,
      handler() {
        if (this.service.initialized) {
          this.$emit('input', this.allValues);
        }
      },
    },
  },
};
</script>
<style scoped>
ul {
  list-style-type: none;
}
</style>
