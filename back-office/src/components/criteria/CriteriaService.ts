import SafeService from 'odas-plugins/SafeService';
import {Criteria, CriteriaQuestionType, CriteriaValue} from 'erhgo-api-client';
import Vue from 'vue';

export default class CriteriaService extends SafeService {
  private _criteria: Criteria[] = [];
  private _selectedMultipleCriteria: CriteriaValue[] = [];
  private _selectedThresholdCriteria: CriteriaValue[] = [];
  private _initialized = false;

  constructor(private _initialSelectedCriteriaCodes: string[]) {
    super();
    this.initializeCriteria();
  }

  initializeSelectedMultipleCriteria() {
    const allReferentialCriteriaValues = this.multipleCriteria.flatMap(c => c.criteriaValues);
    this.selectedMultipleCriteria = allReferentialCriteriaValues.filter(cv => this._initialSelectedCriteriaCodes.some(code => code === cv.code));
  }

  initializeSelectedThresholdCriteria() {
    this.selectedThresholdCriteria = this.thresholdCriteria
      .map(referentialCriteria => referentialCriteria.criteriaValues.filter(referentialCriteriaValue => this._initialSelectedCriteriaCodes.includes(referentialCriteriaValue.code))[0]);

  }

  private async initializeCriteria() {
    this._criteria = (await Vue.$api.getCriteria()).data;
    this.initializeSelectedMultipleCriteria();
    this.initializeSelectedThresholdCriteria();
    this._initialized = true;
  }

  async saveEditedCriteria(criterion: Criteria) {
    const criteriaValues = criterion.criteriaValues.map(c => {
      const {code, titleStandalone, titleForBO, titleForQuestion, icon} = c;
      return {code, titleStandalone, titleForBO: titleForBO || '', titleForQuestion, icon};
    });
    const {title, questionLabel} = criterion;
    await this.safeCall(async () => Vue.$api.editCriteria(criterion.code, {
      title,
      questionLabel,
      criteriaValues,
    }));
  }

  get criteria() {
    return this._criteria;
  }

  get thresholdCriteria() {
    return this._criteria.filter(c => c.questionType === CriteriaQuestionType.THRESHOLD);
  }

  get multipleCriteria() {
    return this._criteria.filter(c => c.questionType === CriteriaQuestionType.MULTIPLE);
  }

  get multipleCriteriaIndexes() {
    return Array.from(Array(this.multipleCriteria.length).keys());
  }

  get thresholdCriteriaIndexes() {
    return Array.from(Array(this.thresholdCriteria.length).keys());
  }

  get selectedMultipleCriteria() {
    return this._selectedMultipleCriteria;
  }

  set selectedMultipleCriteria(value) {
    this._selectedMultipleCriteria = value;
  }

  get selectedThresholdCriteria() {
    return this._selectedThresholdCriteria;
  }

  set selectedThresholdCriteria(value) {
    this._selectedThresholdCriteria = value;
  }

  get allValues() {
    return [...this.selectedMultipleCriteria, ...this.selectedThresholdCriteria].filter(a => !!a);
  }

  get initialized() {
    return this._initialized;
  }
}
