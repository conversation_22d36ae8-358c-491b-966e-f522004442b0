import SafeService from 'odas-plugins/SafeService';
import {Criteria, CriteriaQuestionType, UserCriteriaValue} from 'erhgo-api-client';
import Vue from 'vue';

export default class UserCriteriaService extends SafeService {

  private _userCriteria: UserCriteriaValue[] = [];
  private _selectedValues: UserCriteriaValue[] = [];
  private _allCriteria: Criteria[] = [];

  constructor(private _userId: string, private _onTheFly?: boolean) {
    super();
  }

  async fetchUserCriteria() {
    await this.safeCall(async () => {
      this.userCriteria = (await Vue.$api.getUserCriterias(this._userId)).data;
    });
  }

  async fetchAllCriteria() {
    await this.safeCall(async () => {
      this._allCriteria = (await Vue.$api.getCriteria()).data;
    });
  }

  async submitCriteria() {
    return this.safeCall(async () => {
      const {_userId: userId, selectedValueCodes, unselectedValueCodes} = this;
      await Vue.$api.setUserCriterias({
        userId,
        selectedValueCodes,
        unselectedValueCodes,
      });
      this._userCriteria = [...this.selectedValues.map(s => ({
        ...s,
        selected: true,
      })), ...this.unselectedValues.map(s => ({...s, selected: false}))];
      return true;
    });
  }

  set userCriteria(criteria: UserCriteriaValue[]) {
    this._userCriteria = criteria;
    this._selectedValues = [...this._userCriteria.filter(c => c.selected)];
  }

  get userCriteria() {
    return this._userCriteria;
  }

  get allCriteria() {
    return this._allCriteria;
  }

  set allCriteria(criteria: Criteria[]) {
    this._allCriteria = criteria;
  }

  get selectedValueCodes() {
    return this._selectedValues.map(cv => cv.code);
  }

  get selectedValues() {
    return this._selectedValues;
  }

  set selectedValues(values) {
    const mustSubmit = this._onTheFly && this.isModifiedBy(values.map(c => c.code));
    this._selectedValues = values;
    if (mustSubmit) {
      this.submitCriteria();
    }
  }

  get unselectedValues() {
    return this._allCriteria.flatMap(c => c.questionType === CriteriaQuestionType.THRESHOLD ? [] : c.criteriaValues.filter(cv => !this.selectedValueCodes.includes(cv.code)));
  }

  get unselectedValueCodes() {
    return this.unselectedValues.map(cv => cv.code);
  }

  private isModifiedBy(values: string[]) {
    return !values.every(c => this.selectedValueCodes.includes(c)) || values.length !== this.selectedValueCodes.length;
  }
}
