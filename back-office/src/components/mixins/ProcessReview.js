export default {
  methods: {
    getScore(contexts) {
      return Object.values(contexts.reduce((maxPerCategory, c) => {
        const prevScore = maxPerCategory[c.categoryLevel.category.id];
        const currentScore = c.categoryLevel.score;
        const localMax = prevScore ? Math.max(prevScore, currentScore) : currentScore;
        maxPerCategory[c.categoryLevel.category.id] = localMax;
        return maxPerCategory;
      }, {})).reduce((a, b) => a + b, 0);
    },
    getLevelFromScore(score) {
      let level = 'PROFESSIONAL';
      if (score > 30 && score <= 55) {
        level = 'TECHNICAL';
      } else if (score > 55 && score <= 75) {
        level = 'COMPLEX';
      } else if (score > 75 && score <= 90) {
        level = 'EXPERT';
      } else if (score > 90) {
        level = 'STRATEGIC';
      }
      return level;
    },
  },

};
