const sortByCode = (capacity1, capacity2) => {
  const code1 = capacity1.code;
  const code2 = capacity2.code;
  if (code1 < code2) {
    return -1;
  } else if (code1 > code2) {
    return 1;
  }
  return 0;
};

const groupByLevel = capacities => capacities.reduce((grouped, capacity) => {
  const level = capacity.code.substring(0, 3);
  grouped[level].push(capacity);
  return grouped;
}, {
  CA3: [],
  CA2: [],
  CA1: [],
});

const filterUniqueCapacities = (capacity, index, allCapacities) => allCapacities.findIndex(c => c.id === capacity.id) === index;

const getDeepInducedCapacities = capacity =>
  capacity.inducedCapacities.flatMap(inducedCapacity =>
    [inducedCapacity].concat(...getDeepInducedCapacities(inducedCapacity)));

export default {
  methods: {
    getAllUniqueInducedCapacities(capacities, withThemselves) {
      return capacities.flatMap((capacity) => {
        const allDeepInducedCapacities = getDeepInducedCapacities(capacity);
        return withThemselves ?
          [capacity].concat(...allDeepInducedCapacities) :
          allDeepInducedCapacities;
      }).filter(filterUniqueCapacities);
    },
    groupActivityInducedCapacitiesByPresenceInList(activity, list, deepInducedCapacitiesOnly) {
      const toMatchCapacitiesId = list.map(capacity => capacity.id);
      return this.groupAndSort(this.getAllUniqueInducedCapacities(activity.inducedCapacities, !deepInducedCapacitiesOnly))
        .reduce((groupedByPresence, capacity) => {
          const targetList = toMatchCapacitiesId.includes(capacity.id) ? 'present' : 'absent';
          groupedByPresence[targetList].push(capacity);
          return groupedByPresence;
        }, {
          present: [],
          absent: [],
        });
    },
    groupAndSort(capacities) {
      const grouped = groupByLevel(capacities);
      Object.keys(grouped).forEach((key) => {
        grouped[key] = grouped[key].sort(sortByCode);
      });
      return grouped.CA3.concat(grouped.CA2, grouped.CA1);
    },
    getInducedCapacities(capacities) {
      return this.groupAndSort(this.getAllUniqueInducedCapacities(capacities));
    },
    getTopLevelCapacities(inducedCapacity, selectedCapacities) {
      return selectedCapacities.filter(c => {
        return getDeepInducedCapacities(c).map(c => c.id).includes(inducedCapacity.id);
      });
    },

  },
};
