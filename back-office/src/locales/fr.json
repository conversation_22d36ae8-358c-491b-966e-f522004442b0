{"menu": {"profile": "Votre profil", "logout": "Se déconnecter", "organizations": "Organisations", "setup": "Setup", "jobs": "<PERSON><PERSON>", "repositories": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "activity": "Activités", "capacity_related_question": "Activités Extra Pro", "capacity": "Capacités", "context": "Contextes", "behavior": "Comportements", "category": "Categories de contexte", "erhgoOccupations": "Métiers erhgo", "compare": "Comparer des métiers", "locations": "Localisations", "candidates": "Candidatures", "employee": "Utilisateurs", "employee_repository": "Gestion des utilisateurs", "administrator": "Administration", "administrator_repository": "Gestion des administrateurs", "questions": "Questionnaire", "users_repository": "Suivi des individus", "scraped_users": "Candidats scrapés", "users_index": "Sourcing candidat", "sourcing_simulated_result": "Simulation de recherche", "landing_page": "Pages d'accueil", "list": "Recrutements", "recruitment_list": "Suivi des recrutements", "criterias": "Critères", "invitation_code": "Invitations je recrute"}, "action": {"create": "<PERSON><PERSON><PERSON>", "createFromErhgo": "<PERSON><PERSON><PERSON> depuis un métier ERHGO", "createSimpleFromErhgo": "Créer un poste simplifié depuis un métier ERHGO", "createFromEmptyModel": "Créer un nouveau poste vierge", "createFromAnotherJob": "<PERSON><PERSON><PERSON> depuis un poste déjà cartographié", "created": "<PERSON><PERSON><PERSON>", "update": "Mettre à jour", "updated": "Mis à jour", "save": "<PERSON><PERSON><PERSON><PERSON>", "reset": "Réinitialiser", "import": "Importer", "export": "Exporter", "return": "Retourner à la liste"}, "form": {"global": {"error": "Une erreur est survenue : veuil<PERSON>z réessayer l'opération ou contacter le support", "required": "Ce champ est obligatoire", "maxLength": "Ce champ peut contenir {maxLength} caractères au maximum"}, "activity": {"title": "<PERSON><PERSON> un titre", "alternativeLabel": "Saisir un libellé alternatif", "defaultLabel": "Libellé par défaut (cliquer sur l'étoile en fin de ligne pour en changer)", "description": "<PERSON><PERSON> une description", "inducedCapacities": "Choisir des capacités gigognes", "preview": "Prévisualisation", "message": {"title": {"required": "Le titre est obligatoire", "maxlength": "Ce champ ne peut contenir que 30 caractères maximum"}, "inducedCapacities": {"minLength": "Au moins une capacités doit être sélectionnée"}, "delete": {"title": "Supprimer cette activité ?", "content": "Vous êtes sur le point de supprimer l'activité <b>{code} - {title}</b>.<br/> Etes-vous sûr de vouloir supprimer cette activité ?"}}}, "context": {"title": "<PERSON><PERSON> un titre", "description": "<PERSON><PERSON> une description", "category": "Sélectionner une catégorie de contexte", "categoryLevel": "Sélectionner un niveau de catégorie", "acquisitionModality": "Modalité d'acquisition", "message": {"title": {"required": "Ce champ ne peut être vide", "maxlength": "Ce champ ne peut contenir que 30 caractères maximun"}, "description": {"required": "Ce champ ne peut être vide"}, "category": {"required": "Ce champ ne peut être vide"}, "categoryLevel": {"required": "Ce champ ne peut être vide"}}}, "behavior": {"title": "<PERSON><PERSON> un titre", "description": "<PERSON><PERSON> une description", "message": {"title": {"required": "Ce champ ne peut être vide", "maxlength": "Ce champ ne peut contenir que 30 caractères maximun"}}}, "organization": {"title": "<PERSON><PERSON> un titre", "siret": "<PERSON><PERSON> le siret", "address": "<PERSON><PERSON> une adresse", "description": "Décrire l'organisation", "message": {"title": {"required": "Ce champ ne peut être vide", "maxlength": "Ce champ ne peut contenir que 30 caractères maximun"}, "description": {"maxlength": "Ce champ peut contenir 200 caractères maximun"}}, "selected": "Organisation sélectionnée", "search": "Je cherche l'organisation"}, "job": {"create": {"title": "Espace entreprise", "organization": {"title": "Titre du poste", "service": "Nom du service", "contexts": "Localisation du poste", "description": "Commentaire"}, "erhgoOccupation": {"title": "<PERSON><PERSON><PERSON> Erhgo associé"}, "odas": {"owner": "C<PERSON><PERSON> par", "observators": "Observé par", "observationDate": "Date d'observation"}, "activity": {"add": "Ajouter une ou plusieurs activités"}, "context": {"add": "Ajouter un ou plusieurs contextes"}, "recommendation": "<PERSON><PERSON><PERSON> vos préconisations pour ce poste", "behavior": {"add": "Ajouter un ou plusieurs comportements"}}}, "employee": {"userName": "<PERSON>sir un nom d'utilisateur", "email": "<PERSON><PERSON> une adresse email valide", "firstName": "<PERSON><PERSON> le prénom", "lastName": "<PERSON><PERSON> le nom", "password": "Saisir un password temporaire", "message": {"userName": {"required": "Ce champ ne peut être vide", "conflict": "Ce nom d'utilisateur est déjà utilisé."}, "email": {"required": "Ce champ ne peut être vide", "conflict": "Cette adresse mail est déjà utilisée."}, "firstName": {"required": "Ce champ ne peut être vide", "maxlength": "Ce champ ne peut contenir que 30 caractères maximun"}, "lastName": {"required": "Ce champ ne peut être vide", "maxlength": "Ce champ ne peut contenir que 30 caractères maximun"}, "password": {"required": "Ce champ ne peut être vide", "maxlength": "Ce champ ne peut contenir que 30 caractères maximun", "policies": "Ce champ doit contenir minimum 8 caractères, dont une majuscule, un chiffre et un caractère spécial. Il ne doit pas contenir le nom, le prénom ou l'adresse mail."}, "group": {"required": "Ce champ ne peut être vide"}}}, "administrator": {"userName": "<PERSON>sir un nom d'utilisateur", "email": "<PERSON><PERSON> une adresse email valide", "firstName": "<PERSON><PERSON> le prénom", "lastName": "<PERSON><PERSON> le nom", "password": "Saisir un password temporaire", "message": {"userName": {"required": "Ce champ ne peut être vide"}, "email": {"required": "Ce champ ne peut être vide"}, "firstName": {"required": "Ce champ ne peut être vide", "maxlength": "Ce champ ne peut contenir que 30 caractères maximun"}, "lastName": {"required": "Ce champ ne peut être vide", "maxlength": "Ce champ ne peut contenir que 30 caractères maximun"}, "password": {"required": "Ce champ ne peut être vide", "maxlength": "Ce champ ne peut contenir que 30 caractères maximun"}}}, "mission": {"no-context": "Aucun contexte significatif observé", "title": {"label": "Nom de la mission (commencer par un verbe à l'infinitif)"}, "message": {"global": "Passage à l'étape suivante impossible : au moins une mission comporte des erreurs", "title": {"required": "Ce champ ne peut être vide", "toolong": "Ce champ peut contenir 100 caractères au maximum"}, "activity": {"required": "Veuillez renseigner au moins une activité"}, "context": {"missing": "Veuillez indiquer un contexte par catégorie, ou cliquer sur 'Aucun contexte significatif observé', pour les catégories: "}}, "activity": "Saisissez une nouvelle activité de la mission", "context": "Saisissez un nouveau contexte pour la mission", "contextFromJob": "Ajouter un contexte d'une autre mission"}, "recruitment_profile": {"title": {"label": "Titre du profil de recrutement"}, "custom_question": {"label": "Saisissez une question spécifique que vous pouvez poser au candidat"}}}, "ref": {"headers": {"actions": "Actions", "who": "<PERSON>ui", "when": "Quand", "capacity": {"code": "Code", "title": "Titre", "description": "Description", "capacities": "Sous-capacités", "inducedCapacities": "Capacités induites"}, "activity": {"title": "Titre", "description": "Description", "capacities": "Capacités", "inducedCapacities": "Capacités induites"}, "category": {"code": "Code", "title": "Nom", "description": "Description"}, "behavior": {"code": "Code", "title": "Nom", "category": "<PERSON><PERSON><PERSON><PERSON>", "description": "Description"}, "context": {"code": "Code", "title": "Nom", "description": "Description", "category": "Catégorie du <PERSON>e", "categoryCode": "Catégorie du <PERSON>e"}, "questionForContexts": {"title": "Nom", "contexts": "Contextes concernés"}, "contextLevel": {"code": "Code", "title": "Nom", "description": "Description", "category": "Sous catégorie du contexte", "categoryCode": "Sous catégorie du contexte"}, "erhgoOccupation": {"state": "État", "title": "Titre", "totalSkills": "Nombre d'aptitudes", "totalQualifiedSkills": "Aptitudes qualifiées", "totalNotQualifiedSkills": "Aptitudes non qualifiées"}, "organization": {"code": "Code", "title": "Nom", "description": "Description"}, "job": {"code": "Code", "title": "Nom", "description": "Description", "service": "Service", "state": "État", "owner": "C<PERSON><PERSON> par", "observators": "Observés par", "observationDate": "Date d'observation", "publicationDate": "Date de finalisation", "behaviors": "Comportements", "contexts": "Contextes", "organization": "Entreprise", "activities": "Activités"}, "user": {"firstName": "Prénom", "lastName": "Nom", "enabled": "Activé", "email": "Email", "phoneNumber": "Téléphone", "location": "Localisation", "candidaturesCount": "Nombre de candidatures", "isFromHandicap": "Jnspuhandicap origine", "handicapModeEnabled": "Jnspuhandicap actif", "createdAt": "Date de création"}}, "dashboard": {"title": "Tableau de bord"}, "category": {"list": "Liste des catégories"}, "capacity": {"list": "Liste des capacités", "modalities": {"SELF_LEARNING": "Auto-apprentissage", "INTEGRATION_PROCESS": "Process d'intégration", "TRAINING": "Formation"}}, "activity": {"create": {"JOB": "Créer une activité"}, "edit": {"JOB": "Éditer l'activité"}, "list": "Liste des activités"}, "user": {"list": "Suivi des individus"}, "location": {"create": "Création d'une localisation", "edit": "Edition d'une localisation", "list": "Liste des localisations"}, "question": {"create": "<PERSON><PERSON>er une question", "edit": "Éditer une question", "list": "Liste des questions"}, "questionForContexts": {"create": "<PERSON><PERSON>er une question", "edit": "Éditer une question", "list": "Liste des questions relatives aux contextes"}, "answer": {"none": "Aucun potentiel", "low": "Faible potentiel", "medium": "<PERSON><PERSON><PERSON><PERSON> moyen", "high": "Potentiel élé<PERSON>"}, "behavior": {"create": "Créer un comportement", "edit": "Éditer le comportement", "list": "Liste des comportements"}, "context": {"create": "<PERSON><PERSON><PERSON> un contexte", "edit": "<PERSON><PERSON><PERSON> le contexte", "list": "Liste des contextes"}, "erhgoOccupation": {"list": "Métiers erhgo"}, "organization": {"create": "Créer une organisation", "edit": "Éditer une organisation", "list": "Liste des organisations", "goto": "Sélectionner cette organisation"}, "employer": {"create": "<PERSON><PERSON><PERSON> un employeur", "edit": "É<PERSON>er un employeur", "list": "Liste des employeurs"}, "job": {"create": "<PERSON><PERSON>er un poste", "edit": "Éditer un poste", "list": {"title": "<PERSON><PERSON>"}, "breadcrumb": {"setup": "Set-up", "organizationSelected": "Entreprise {name}", "createJob": "Nouveau poste", "editJob": "Edition du poste", "job": "Poste"}, "steps": {"definition": {"title": "Informations", "subtitle": "Saisie des informations", "nextButton": ""}, "missions": {"title": "Missions", "subtitle": "Définition des activités et contextes", "nextButton": ""}, "behaviors": {"title": "Comportements", "subtitle": "Ajout des comportements", "nextButton": ""}, "criteria": {"title": "Critères", "subtitle": "Définir des critères", "nextButton": ""}, "recommendation": {"title": "Préconisations", "subtitle": "Ajout de commentaires", "nextButton": ""}, "publishing": {"title": "Finalisation", "subtitle": "", "nextButton": ""}, "cancelButton": "Annuler et revenir la liste"}}, "recruitment": {"create": "Création d'un recrutement pour le poste", "edit": "Edition d'un recrutement pour le poste"}, "employee": {"create": "Création d'un accés utilisateur", "list": "Utilisateurs"}, "administrator": {"create": "Création d'un accés administrateur", "list": "Administrateurs"}}, "matchingLevel": {"MASTERED": "Activités maîtrisées", "INSUFFICIENT": "Activités à un niveau de maîtrise inférieur", "UNMASTERED": "Activités non maîtrisées"}, "acquisitionModalities": {"SELF_LEARNING": "Auto-apprentissage", "INTEGRATION_PROCESS": "Processus d’intégration", "TRAINING": "Formation"}, "masteryLevels": {"PROFESSIONAL": {"label": "Professionnelle", "labelM": "Professionnel", "description": "Mobilise des capacités pour réaliser le travail attendu, à la lettre, sans écart avec la consigne ou la règle"}, "TECHNICAL": {"label": "Technique", "labelM": "Technique", "description": "Mobilise des capacités avec succès avec des variations de contexte légères"}, "COMPLEX": {"label": "Complexe", "labelM": "Complexe", "description": "Mobilise des capacités dans le contexte spécialisé d’un cadre professionnel précis"}, "EXPERT": {"label": "Experte", "labelM": "Expert", "description": "Mobilise des capacités dans un contexte très exigeant et technique, avec une obligation de crédibilité face à ses interlocuteurs"}, "STRATEGIC": {"label": "Stratégique", "labelM": "Stratégique", "description": "Mobilise des capacités pour réaliser quelque chose de rare ou d’inédit dans son contexte (idée, service, produit, oeuvre…)"}}, "recruitmentStates": {"DRAFT": "en brouillons", "PUBLISHED": "ouverts", "SELECTION": "fermés", "CLOSED": "terminés"}, "recruitmentState": {"DRAFT": "en brouillon", "PUBLISHED": "ouvert", "SELECTION": "fermé", "CLOSED": "terminé"}, "processingType": {"COMMENT": "Ajout note", "PUBLISH": "Publication", "CLOSE": "Cl<PERSON><PERSON>", "REPUBLISH": "Republication", "SUSPEND": "Suspension", "REFUSE_CANDIDATURE": "<PERSON><PERSON><PERSON> candidature", "FAV_CANDIDATURE": "Candidature mise en fav.", "MEET_CANDIDATURE": "Candidature contactée", "MEET_LATER_CANDIDATURE": "Candidature à contacter"}, "recruitmentEmailState": {"WAITING": "Envoi prévu après", "DONE": "<PERSON><PERSON><PERSON>", "CANCEL": "<PERSON><PERSON><PERSON>", "FORCED": "Envoi forcé", "ERROR": "En erreur"}, "candidaturesRealStates": {"title": {"NOT_FINALIZED": "Non finalisée par le candidat", "NOT_TREATED_BY_ERHGO": "Non traitée par Erhgo", "MISSING_PREREQUISITE": "<PERSON><PERSON><PERSON><PERSON> - Prére<PERSON>", "REFUSED_ON_CALL": "Refusée en préqualification", "INTERNAL_POSITION": "Positionnée en interne", "INTRODUCE_TO_CLIENT": "À présenter au client", "SUMMARY_SHEET_SENT": "Fiche de synthèse envoyée", "REFUSED_BY_CLIENT_WITH_SHEETS": "Refusée par le client en rapport avec la fiche de synthèse", "ON_RECRUITMENT_CLIENT": "Recrutement en cours chez le client", "REFUSED_MEETING_CLIENT": "Refusée par le client après entretien", "RECRUITMENT_VALIDATED": "Recrutement validé", "STAND_BY": "Stand-by"}}, "candidatureStates": {"title": {"waiting": "En attente", "contacted": "Contactées", "refused": "<PERSON><PERSON><PERSON><PERSON>"}, "no-result": {"waiting": "candidature en attente", "contacted": "candidature contactée"}, "result": {"matched": "Candidatures sélectionnées", "unmatched": "Candidatures non sélectionnées", "invalid": "Candidatures non finalisées"}, "show-detail": "Voir la candidature", "refuse": "Refuser la candidature", "show-profile": "Voir le profil"}, "contactTimes": {"ALL_DAY": "A tout moment de la journée", "MORNING": "<PERSON><PERSON><PERSON><PERSON> le matin", "NOON": "<PERSON>lut<PERSON><PERSON> le midi", "AFTERNOON": "Plutôt l'après-midi", "EVENING": "Plutôt en fin de journée", "null": "Non précisé"}, "behaviorCategory": {"SOCIABILITY": "Sociabilité", "CONSTANCY": "Constance", "HONESTY": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RIGOR": "<PERSON><PERSON><PERSON><PERSON>", "TENACITY": "Engagement", "PRAGMATISM": "Pragmatisme", "AUTONOMY": "Autonomie", "CONFIDENTIALITY": "Confidentialité", "CURIOSITY": "Curiosité", "PERSEVERANCE": "Persévérance", "CRITICAL_THINKING": "Pensée critique", "PUNCTUALITY": "Ponctualité", "REACTIVITY": "Réactivité", "SENSE_OF_SERVICE": "Sens du <PERSON>", "VIGILANCE": "Vigilance"}, "behaviorCategoriesMatchingType": {"VERY_STRONG": "Affinité forte", "STRONG": "Affinité moyenne", "MEDIUM": "Affinité souple", "IGNORE": "Aucun filtre sur les catégories"}, "occupationCreationReason": {"ESCO": "Lié à un métier ESCO", "CV": "Issu d'un CV", "XP": "<PERSON><PERSON> d'un récit d'expérience", "SOURCING": "Créé via une offre de sourcing", "ATS": "Créé via un recrutement ATS", "CSV": "Importé depuis un CSV", "BO": "<PERSON><PERSON><PERSON> de<PERSON> le back-office"}, "behaviorQuestionPhase": {"FIRST": "Première", "SECOND": "Seconde"}, "organizationType": {"ENTERPRISE": "Entreprise", "TERRITORIAL": "Organisation territoriale", "CONSORTIUM": "Groupement d'employeurs", "PROJECT": "Projet", "SOURCING": "Sourcing"}, "userGroupType": {"HUMAN_RESOURCES": "<PERSON><PERSON><PERSON><PERSON> humaines", "EXTERNAL_CONSULTANTS": "Consultant externe", "TERRITORIAL_ORGANIZATION": "Conseiller territorial", "TALENT_MANAGER": "Gestionnaire vivier", "MASTER_TERRITORIAL_ORGANIZATION": "Administrateur territorial"}, "statistics": {"totalCount": "Nb total d'utilisateurs FO", "monthlyCandidatures": "Statistiques mensuelles des candidatures par offre", "spontaneousCandidatures": "Statistiques des candidatures spontanées", "recruitmentStats": "Statistiques des recrutements par organisation", "severalMetricsStats": "Statistiques de diverses métriques"}, "experience": {"JOB": "Emploi", "MISSION": "Mission", "INTERNSHIP": "Stage", "VOLUNTEERING": "Bénévolat", "PERSONAL": "Autre expérience"}, "situation": {"EMPLOYEE": "En poste", "RESEARCHING": "En recherche", "STANDBY": "<PERSON> veille"}, "salary": {"MIN_1000": "1000€", "MIN_1500": "1500€", "MIN_2000": "2000€", "MIN_2500": "2500€", "MIN_3000": "3000€", "MIN_3500": "3500€", "MIN_4000": "4000€", "MORE": "Plus"}, "duration": {"DURATION_1": "moins de 6 mois", "DURATION_2": "6 mois à 1 an", "DURATION_3": "1 an à 5 ans", "DURATION_4": "plus de 5 ans"}, "durationType": {"SINCE": "<PERSON><PERSON><PERSON>", "DURING": "Pendant"}}