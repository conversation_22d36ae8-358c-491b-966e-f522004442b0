module.exports = {
  css: {
    // see https://github.com/vuetifyjs/vuetify/issues/5271#issuecomment-542624461
    extract: {ignoreOrder: true},
  },
  // NB.:  see https://stackoverflow.com/questions/62598443/how-to-increase-performance-when-building-vue-webapp to increase memory limits
  chainWebpack: config => {
    config.resolve.symlinks(false);

    // keycloak mjs files support
    config.module
      .rule('esm')
      .test(/\.m?jsx?$/)
      .type('javascript/auto');
  },
  devServer: {
    compress: true,
    port: 8070,
    proxy: {
      '/api': {
        target: 'http://localhost:8080/',
        changeOrigin: true,
      },
    },
  },
  configureWebpack: {
    cache: {
      'type': 'filesystem',
    },
  },
  pluginOptions: {
    i18n: {
      locale: 'fr',
      fallbackLocale: 'en',
      localeDir: 'locales',
      enableInSFC: false,
    },
  },
  transpileDependencies: [
    'vuetify',
  ],
};
