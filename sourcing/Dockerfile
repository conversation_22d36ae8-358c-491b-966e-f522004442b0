FROM openapitools/openapi-generator-cli:v6.6.0 as client-builder
# Generate the JS client from OpenAPI spec
COPY ./common/generated /local/common/generated
RUN /usr/local/bin/docker-entrypoint.sh generate -i /local/common/generated/api.yaml -g typescript-axios -o /local/common/generated/client --additional-properties=npmName=erhgo-api-client,supportsES6=true,enumPropertyNaming=UPPERCASE,sortModelPropertiesByRequiredFlag=false --type-mappings=set=Array  --type-mappings=DateTime=Date --type-mappings=Date=Date

FROM node:24 as build
WORKDIR /usr/src/app
# Copy all package.json files and install dependencies. This is a separate step for caching purposes.
COPY ./sourcing/eslint.config.mjs ./sourcing/package.json ./sourcing/yarn.lock ./sourcing/.npmrc ./sourcing/
COPY --from=client-builder /local/common/generated/client ./common/generated/client
# Build the generated client. This is a separate step for caching purposes.
RUN cd ./common/generated/client && yarn install --frozen-lockfile --no-progress --non-interactive && yarn build
# https://gitlab.com/gitlab-org/gitlab-runner/-/issues/3161
RUN cd ./sourcing && yarn install --frozen-lockfile --no-progress --non-interactive --network-timeout 1000000
# Copy the rest of the code.
COPY ./sourcing ./sourcing
# Run the linter and unit tests
RUN cd ./sourcing && \
    yarn lint --no-fix
# Build the application.
ARG VERSION=dev
RUN cd ./sourcing && echo "${VERSION}" > ./public/version && NODE_ENV=production yarn quasar build
# Final image
FROM nginx:1.28.0
COPY --from=build /usr/src/app/sourcing/dist/spa/ /var/www
COPY ./common/*_security_headers.conf /etc/nginx/snippets/
COPY ./common/static_nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 8080
