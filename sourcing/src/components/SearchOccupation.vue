<template>
  <div class="column">
    <label for="select-occupation" class="text-label"><PERSON><PERSON><PERSON>, mot-clé, activité,...</label>
    <q-dialog v-model="showOccupationInfos" v-if="selectedOccupation?.code">
      <occupation-detail v-if="showOccupationInfos"
                         :occupation-id="selectedOccupation?.code"
                         :forced-title="wantPersonalizedTitle ? undefined : selectedOccupation?.title"
                         @close="showOccupationInfos=false" />
    </q-dialog>
    <q-select
      v-if="!wantPersonalizedTitle"
      :loading="loading"
      :options="options"
      input-class="ellipsis"
      :clearable="!disabled"
      hide-dropdown-icon
      :input-debounce="650"
      option-label="title"
      option-value="code"
      placeholder="Choisissez un métier"
      :use-input="!disabledInput && !selectedOccupation"
      v-model="selectedOccupation"
      @filter="filterFn"
      @clear="clearOccupation"
      @update:modelValue="setOccupation"
      ref="qSelectRef"
      persistent-hint
      hint="Vous pourrez changer l'intitulé de votre offre plus loin"
      @keyup.enter="validateCustomOccupation"
      :disable="disabled"
    >
      <template v-slot:prepend>
        <q-icon
          v-if="selectedOccupation?.code"
          name="fal fa-info-circle"
          class="text-erhgo-violet1 cursor-pointer"
          @click.stop.prevent="showOccupationInfos = true"
        />
      </template>
      <template v-slot:append>
        <q-btn
          v-if="queryValue?.trim() && !selectedOccupation"
          :loading="isLoading"
          flat
          dense
          round
          icon="fas fa-paper-plane-top"
          color="green"
          @click="validateCustomOccupation"
        >
          <q-tooltip>Je valide mon nouveau métier</q-tooltip>
        </q-btn>
      </template>
      <template v-slot:selected v-if="!!selectedOccupation">
        <div class="no-emphase" v-html="selectedOccupation.title"/>
      </template>
      <template v-slot:option="scope">
        <q-item class="q-py-none" v-bind="scope.itemProps">
          <q-item-section class="matching-pattern-container">
            <div class="text-black row items-center justify-between">
              <div class="row items-center">
                <span v-html="scope.opt.title"/>
              </div>
            </div>
            <span class="text-caption text-italic q-pl-md" v-html="scope.opt.snippet" v-if="scope.opt.snippet"/>
          </q-item-section>
        </q-item>
      </template>
      <div v-if="!!selectedOccupation">
        <q-btn class="q-ml-md q-mt-sm" flat rounded @click="wantPersonalizedTitle=true">
          <q-icon class="fa-light fa-pen" size="xs"/>
          <q-tooltip>Vous pouvez personnaliser le libellé affiché aux candidats</q-tooltip>
        </q-btn>
      </div>
    </q-select>
    <div v-if="wantPersonalizedTitle">
      <q-input
        v-model="personalizedTitle"
        hint="Libellé personnalisé de l'offre"
        :clearable="!isLoading && !disabled"
        :disable="disabled"
        :rules="[val => val && val.length <= 230 || 'Le Libellé personnalisé ne doit pas dépasser 230 caractères.']"
        @clear="clearOccupation">
        <template v-slot:prepend>
          <q-icon name="fal fa-info-circle"
                  v-if="selectedOccupation?.code"
                  class="text-erhgo-violet1"
                  style="cursor: pointer!important"
                  @click="showOccupationInfos = true" />
        </template>
      </q-input>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { nextTick, ref, watch } from 'vue';
import type { ErhgoOccupationSearch } from 'erhgo-api-client';
import { useApi } from 'src/config/api';
import { QSelect } from 'quasar';
import OccupationDetail from 'components/OccupationDetail.vue';

const {service: {api, isLoading}} = useApi();

const props = defineProps({
  modelValue: {type: Object as PropType<ErhgoOccupationSearch | null>, required: false, default: null},
  query: {type: String, required: false, default: ''},
  disabled: { type: Boolean, required: false, default: false },
});

const disabledInput = ref<boolean>(false);
const options = ref<Array<ErhgoOccupationSearch>>([]);
const loading = ref<boolean>(false);
const selectedOccupation = ref<ErhgoOccupationSearch | null>(props.modelValue);
const wantPersonalizedTitle = ref<boolean>(false);
const personalizedTitle = ref<string | undefined>();
const queryValue = ref<string>(props.query);
const showOccupationInfos = ref(false);
const qSelectRef = ref<QSelect | null>();
const MAX_LENGTH = 230;

const fetchOccupations = async (query: string) => {
  return query.length < MAX_LENGTH ? (await api.searchOccupations(query, true)).data : [];
};

const filterFn = async (query: string, update: (fn: () => void) => void) => {
  update(() => loading.value = true);
  try {
    queryValue.value = query || props.query;
    if (queryValue.value) {
      const datas = await fetchOccupations(queryValue.value);
      update(() => options.value = datas);
    }
  } finally {
    update(() => loading.value = false);
  }
};

const validateCustomOccupation = () => {
    const newOccupation = {code: '', title: queryValue.value};
    setOccupation(newOccupation);
    wantPersonalizedTitle.value = true;
};

watch(() => props.query, async (newQuery) => {
  if (newQuery && qSelectRef.value) {
    setOccupation(undefined);
    await nextTick(() => {
      qSelectRef.value?.updateInputValue(newQuery);
      qSelectRef.value?.showPopup();
    });
  }
}, {immediate: true});

const emit = defineEmits<{
  (e: 'update:modelValue', value: ErhgoOccupationSearch | null): void
}>();

const setOccupation = (o?: ErhgoOccupationSearch) => {
  if (o) {
    selectedOccupation.value = o;
    personalizedTitle.value = `${o.title.replace(/(<([^>]+)>)/ig, '')} (F / H / NB)`;
  } else {
    wantPersonalizedTitle.value = false;
    selectedOccupation.value = null;
    personalizedTitle.value = undefined;
  }
  disabledInput.value = !!o;
  emit('update:modelValue', o ? { ...o, title: personalizedTitle.value } : null);
};

const clearOccupation = () => {
  setOccupation(undefined);
  queryValue.value = '';
  if (qSelectRef.value) {
    qSelectRef.value.updateInputValue('');
  }
};

const updateTitle = () => {
  if (!!selectedOccupation.value && !!personalizedTitle.value && personalizedTitle.value.length <= MAX_LENGTH) {
    selectedOccupation.value.title = personalizedTitle.value;
    emit('update:modelValue', selectedOccupation.value ? {
      ...selectedOccupation.value,
      title: selectedOccupation.value.title,
    } : null);
  }
};

watch(() => personalizedTitle.value, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    void nextTick(() => updateTitle());
  }
});

personalizedTitle.value = selectedOccupation.value?.title;


</script>
