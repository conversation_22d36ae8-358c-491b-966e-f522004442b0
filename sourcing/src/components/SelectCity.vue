<template>
  <div class="column">
    <label for="select-location" class="text-label">Lieu de travail</label>
    <q-select
      id="select-location"
      :loading="loading"
      :options="options"
      :clearable="!disabled"
      for="select-location"
      hide-dropdown-icon
      input-debounce="650"
      :option-label="a => `${a.city} (${a.postcode})`"
      option-value="postcode"
      placeholder="Ex : Villeurbanne"
      :use-input="!disabledInput && !selectedLocation"
      v-model="selectedLocation"
      @filter="filterFn"
      @clear="() => setLocation(null)"
      @update:modelValue="setLocation"
      hint="Veuillez saisir au moins 3 caractères"
      :hide-hint="!showHint"
      error-message="Aucun résultat correspondant à votre recherche"
      :error="!options.length && !showHint && !loading && !selectedLocation"
      ref="qSelectRef"
      :disable="disabled"
    >
      <template v-slot:selected v-if="!!selectedLocation">
        <div class="no-emphase">{{ selectedLocation.city }} ({{ selectedLocation.postcode }})</div>
      </template>

    </q-select>
  </div>
</template>
<script setup lang="ts">
import type { PropType} from 'vue';
import { nextTick, ref, watch } from 'vue';
import type { Location } from 'erhgo-api-client';
import type { FeatureCollection, Point } from 'geojson';
import { useApi } from 'src/config/api';
import { QSelect } from 'quasar';

const {service: {axios}} = useApi(undefined, false);

type Emits = (e: 'update:modelValue', input: Location | null) => void;

const props = defineProps({
  modelValue: {type: Object as PropType<Location | null>, required: false, default: null},
  query: {type: String, required: false, default: ''},
  disabled: { type: Boolean, required: false, default: false },
});

const disabledInput = ref<boolean>(false);
const options = ref<Array<Location>>([]);
const loading = ref<boolean>(false);
const showHint = ref<boolean>(!props.modelValue?.citycode);
const selectedLocation = ref<Location | null>(props.modelValue);
const qSelectRef = ref<QSelect | null>();

const filterFn = async (query: string, update: (fn: () => void) => void) => {
  update(() => loading.value = true);
  try {
    let datas: FeatureCollection | null = null;
    if (query && query.length > 2) {
      const uri = `https://api-adresse.data.gouv.fr/search/?q=${query}&type=municipality&autocomplete=1`;
      datas = (await axios.get(encodeURI(uri))).data;
      showHint.value = false;
    } else {
      showHint.value = true;
    }
    update(() => options.value = datas?.features.map(x => {
      const locationContext = x.properties?.context.split(', ');
      return {
        city: x.properties?.city,
        citycode: x.properties?.citycode,
        postcode: x.properties?.postcode,
        departmentCode: locationContext[0],
        regionName: locationContext[locationContext.length - 1],
        longitude: (x.geometry as Point)?.coordinates[0],
        latitude: (x.geometry as Point)?.coordinates[1],
      };
    }) || []);
  } finally {
    update(() => loading.value = false);
  }
};

watch(() => props.query, async (newQuery) => {
  if (newQuery && qSelectRef.value) {
    setLocation(null);
    await nextTick(() => {
      qSelectRef.value?.updateInputValue(newQuery);
      qSelectRef.value?.showPopup();
    });
  }
}, {immediate: true});

const emit = defineEmits<Emits>();

const setLocation = (o: Location | null) => {
  disabledInput.value = !!o;
  if (!o) {
    selectedLocation.value = null;
  }
  emit('update:modelValue', o);
};
</script>
