<template>

  <div class="q-ml-md">
    <q-breadcrumbs align="left" class="cursor-pointer text-italic text-secondary"
                   style="text-decoration: underline">
      <div>
        <q-icon
          size="1em"
          name="fa-solid fa-bullhorn"
          color="green"
        />
      </div>
      <q-breadcrumbs-el label="Mes offres" class="text-black" :to="{name: 'recruitments-list'}"/>
    </q-breadcrumbs>
  </div>

  <close-recruitment-dialog :recruitment="recruitmentService"
                            v-model="recruitmentService.showClosedDialog"
  />

  <users-notified-dialog :selected-users-ids="recruitment.usersToNotify?.map(u => u.id)"
                         v-model="showUserNotifyDialog"
                         :recruitment-id="recruitment.recruitmentId"
                         :manager-user-id="recruitment.manager?.id"
  />

  <republished-dialog :recruitment="recruitmentService"
                      :job-title="recruitment.title"
                      v-model="recruitmentService.showRepublishDialog" />

  <div class="row chimney--fluid bordered q-pa-sm items-center justify-evenly">
    <div class="col q-pt-none bordered-right text-caption">
      <div class="text-weight-bold text-subtitle3 q-mb-xs">
        {{ recruitment.title }}
      </div>
      <template v-if="recruitment.baseSalary && recruitment.maxSalary">
        <strong>Salaire brut annuel&nbsp;</strong>:
        <template v-if="recruitment.baseSalary !== recruitment.maxSalary">
          entre {{ $filters.formatNumber(recruitment.baseSalary) }}&nbsp;€ et
          {{ $filters.formatNumber(recruitment.maxSalary) }}&nbsp;€
        </template>
        <template v-else>
          {{ $filters.formatNumber(recruitment.baseSalary) }}&nbsp;€
        </template>
        <template v-if="recruitment.location">
          &nbsp;//&nbsp;
        </template>
      </template>
      <template v-if="recruitment.location">
        <strong>Localisation&nbsp;</strong>: {{ recruitment.location.city }}
      </template>
    </div>
    <div class="col q-pt-none">
      <div class="q-mb-none">
        <div class="q-mb-lg row justify-evenly">
          <q-chip class="user-text text-caption"
                  :label="$t(`recruitmentState.${recruitmentService.recruitmentState}`)"
          />
          <template v-if="recruitmentService.recruitmentState === RecruitmentState.PUBLISHED">
            <q-btn class="text-weight-bold green-text q-py-none"
                   icon="fa fa-bell"
                   label="Configuration"
                   outline
                   size="xs"
                   @click="showUserNotifyDialog = true"
            >
              <q-tooltip content-class="bg-secondary text-white shadow-4" anchor="bottom middle" self="top middle">
                Modifier les personnes à notifier pour ce recrutement
              </q-tooltip>

            </q-btn>

            <q-chip class="user-text text-caption"
                    :label="daysRemaining"
                    icon="fa-regular fa-clock"
            />
          </template>
        </div>
        <div class=" row justify-evenly">

          <template v-if="recruitmentService.recruitmentState !== RecruitmentState.CLOSED">
            <q-btn class="text-weight-bold green-text"
                   icon="fa fa-eye"
                   label="Voir l'offre"
                   outline
                   target="_blank"
                   v-if="recruitmentService.recruitmentState === RecruitmentState.PUBLISHED"
                   :href="$filters.announceUrl(recruitment.recruitmentId)"
                   size="xs"
            />
            <q-btn
              :class=" recruitmentService.recruitmentState === RecruitmentState.PUBLISHED ? 'invisible':  'text-weight-bold green-text'"
              icon="fa fa-stop-circle"
              label="Clôturer"
              color="red"
              size="xs"
              @click="recruitmentService.showClosedDialog=true"
            >
              <q-tooltip>
                Clôturez l'offre quand vous avez terminé le recrutement correspondant.
              </q-tooltip>
            </q-btn>

            <q-btn class="text-weight-bold green-text"
                   icon="fa fa-pause-circle"
                   label="Suspendre"
                   color="red"
                   @click="recruitmentService.changeState(RecruitmentState.SELECTION)"
                   v-if="recruitmentService.recruitmentState === RecruitmentState.PUBLISHED"
                   size="xs"
            />
            <q-btn class="text-weight-bold green-text"
                   icon="fa fa-circle-play"
                   label="Reprendre"
                   color="green"
                   @click="recruitmentService.republish()"
                   v-if="recruitmentService.recruitmentState === RecruitmentState.SELECTION"
                   size="xs"
            >

              <q-tooltip>
                Une fois la diffusion reprise, vous pourrez recevoir de nouvelles candidatures.
              </q-tooltip>
            </q-btn>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import type { PropType} from 'vue';
import { computed, ref } from 'vue';
import type { SourcingJobAndRecruitment } from 'erhgo-api-client';
import { RecruitmentState } from 'erhgo-api-client';
import moment from 'moment/moment';
import useRecruitment from 'src/hooks/useRecruitement';
import CloseRecruitmentDialog from 'components/recruitment/CloseRecruitmentDialog.vue';
import UsersNotifiedDialog from 'components/recruitment/UsersNotifiedDialog.vue';
import RepublishedDialog from 'components/recruitment/RepublishedDialog.vue';

const showUserNotifyDialog = ref(false);

const props = defineProps({
  isLoading: Boolean,
  recruitment: {type: Object as PropType<SourcingJobAndRecruitment>, required: true},
});

const recruitmentService = ref(useRecruitment(props.recruitment));
const daysRemaining = computed(() => {
  if (!recruitmentService.value.publicationEndDate) return '';
  const remainingDays = moment(recruitmentService.value.publicationEndDate).diff(moment(), 'days');
  if (remainingDays <= 0) return '0 jours';
  if (remainingDays === 1) return '1 jour';
  return `${remainingDays} jours`;
});

</script>
