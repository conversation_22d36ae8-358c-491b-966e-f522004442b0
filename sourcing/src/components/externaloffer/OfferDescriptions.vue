<template>
  <q-card>
    <h5 class="bg-secondary q-pl-sm q-mt-none">Description de l'offre</h5>
    <q-card-section>
      <div v-html="modelValue.description" />
    </q-card-section>
    <h5 class="bg-secondary q-pl-sm ">Description de l'organisation</h5>
    <q-card-section>
      <div v-html="modelValue.organizationDescription" />
    </q-card-section>
  </q-card>
</template>
<script setup lang="ts">
import type { ExternalOfferSummary } from 'erhgo-api-client';
import type { PropType } from 'vue';

defineProps({
  modelValue: { type: Object as PropType<ExternalOfferSummary>, required: true },
});

</script>
