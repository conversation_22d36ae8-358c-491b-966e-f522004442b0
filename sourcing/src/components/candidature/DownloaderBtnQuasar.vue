<template>
  <q-btn v-if="!anonymizable"
         flat
         outline
         v-bind="{...$attrs}"
         @click="processDownload()"
         :loading="loading"
         no-caps
         class="green-text"
  >
    <slot name="icon">
      <q-icon :name="icon" class="q-mr-sm" :size="iconSize" />
    </slot>
    <span class="text-caption">{{ label }}</span>
    <q-tooltip>
      {{ tooltip }}
    </q-tooltip>
  </q-btn>
  <q-btn-dropdown v-else
                  flat
                  :loading="loading"
                  :label="label"
                  push
                  v-bind="{...$attrs}"
                  class="green-text"
                  :icon="`fa-regular fa-file-${props.extension}`"
  >
    <q-tooltip>{{ props.tooltip }}</q-tooltip>

    <q-list>
      <q-item clickable v-close-popup @click="processDownload(false)">
        <q-item-section avatar>
          <q-icon name="fa-regular fa-file-check" />
        </q-item-section>
        <q-item-section>
          <q-item-label>Profil complet</q-item-label>
        </q-item-section>
      </q-item>
      <q-item clickable v-close-popup @click="processDownload(true)">
        <q-item-section avatar>
          <q-icon name="fa-solid fa-file-exclamation" />
        </q-item-section>
        <q-item-section>
          <q-item-label>Profil anonyme</q-item-label>
        </q-item-section>
      </q-item>
    </q-list>
  </q-btn-dropdown>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

const props = defineProps({
  icon: { required: false, default: 'fa-regular fa-file-pdf' },
  label: { required: false, default: 'Exporter' },
  download: { required: true, type: Function },
  title: { required: true, type: String },
  titleAnonymous: { required: false, type: String },
  extension: { required: true, type: String },
  tooltip: { required: false, default: 'Télécharger le profil du candidat' },
  iconSize: { required: false, default: 'xs' },
});
const anonymizable = computed(() => !!props.titleAnonymous);
const loading = ref<boolean>(false);

const processDownload = async (forcedAnonymous?: boolean) => {
  loading.value = true;
  try {
    const result = await props.download(forcedAnonymous);
    const hiddenElement = document.createElement('a');
    hiddenElement.href = window.URL.createObjectURL(new Blob([result], { type: mediaType }));
    const title = forcedAnonymous ? props.titleAnonymous : props.title;
    hiddenElement.download = title;
    hiddenElement.setAttribute('download', `${title}.${props.extension}`);
    document.body.appendChild(hiddenElement);
    hiddenElement.click();
  } finally {
    loading.value = false;
  }
};

const mediaType = props.extension === 'csv' ? 'text/csv;charset=utf-8;' : `application/${props.extension}`;
</script>
