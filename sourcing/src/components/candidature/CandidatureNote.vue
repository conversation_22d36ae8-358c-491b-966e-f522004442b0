<template>
  <section class="q-py-lg candidate-notes full-width">
    <h4>Notes</h4>
    <div class="text-caption text-italic">
      Ces notes sont réservées à votre entreprise. Le candidat peut y avoir accès dans le cadre d'une demande
      légale relative au RGPD.
    </div>
    <span class="text-caption">
      <strong>
        Attention à ne pas saisir de commentaires injurieux ou discriminants.<q-btn
        icon="fa-regular fa-circle-plus violet-text"
        href="https://www.defenseurdesdroits.fr/fr/institution/competences/lutte-contre-discriminations"
        target="_blank"
        size="xs"
        flat
        round>
          <q-tooltip>Plus d'infos sur le site defenseurdesdroits.fr</q-tooltip>
        </q-btn>
      </strong>
    </span>
    <div>
      <q-input
        v-model="note"
        type="textarea"
        label="Écrire une note sur le candidat..."
        class="q-mt-md q-pa-sm text-black"
      />
      <div class=" row float-right">
        <q-btn size="sm"
               flat
               rounded
               label="ajouter la note"
               class="bg-violet text-white q-mt-md"
               @click="saveCandidatureNote"
               :disable="!note"
               :loading="candidatureService.loading.value">
        </q-btn>
      </div>
      <div v-for="(previousNote, index) in candidatureService.notes.value" :key="index" class="q-mt-md">
        <p class="q-mb-none">
          <span class="text-weight-bold">{{
            currentUser.fullname === previousNote.userFullname ? "Vous" : previousNote.userFullname || "inconnu"
          }}</span>
          <span class="text-grey"> — {{ moment(previousNote.noteDateTime).format("DD/MM/YYYY HH:mm") }}</span>
        </p>
        <p>{{ previousNote.content }}</p>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import moment from 'moment';
import { useApi } from 'src/config/api';
import type { SourcingUserDetails } from 'erhgo-api-client';

const {service: {api}} = useApi();

const currentUser = ref<SourcingUserDetails>((await api.getSourcingUser()).data);

const note = ref<string>('');

const props = defineProps({
  candidatureService: {required: true, type: Object},
});

const saveCandidatureNote = async () => {
  await props.candidatureService.saveNote(note.value);
  note.value = '';
};
</script>
