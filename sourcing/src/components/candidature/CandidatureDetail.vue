<template>
  <q-circular-progress v-if="!candidature"
                       indeterminate
                       size="30px"
                       class="q-mx-lg"
                       color="secondary" />
  <div class="q-mx-lg container candidature-detail" v-else>
    <div class="row q-mb-lg flex justify-between">
      <custom-chip>{{ recruitment?.title }}</custom-chip>
      <custom-chip>{{ recruitment?.location?.city }} ({{ recruitment?.location?.postcode }})</custom-chip>
      <custom-chip>
        Salaire&nbsp;: {{ $filters.formatSalaries(recruitment?.baseSalary, recruitment?.maxSalary) }}
      </custom-chip>
      <q-btn
        flat
        rounded
        class="bg-violet text-white"
        target="_blank"
        :href="$filters.announceUrl(recruitment?.recruitmentId)"
        v-if="recruitment?.recruitmentId"
      >
        Voir l'offre
      </q-btn>
    </div>
    <div class="row">
      <div class="col-6 q-pr-sm">
        <user-info-card :candidature-service="candidatureService" />
        <candidature-note :candidature-service="candidatureService" />
        <previous-candidatures :previous-candidatures="candidature.previousCandidatures" v-if="candidature.email" />
      </div>
      <div class="col-6 q-pl-sm">
        <user-description :user-id="candidature.userId!"
                          :not-enough-experiences="(candidature.numberOfExperiences ?? 0 ) < 2" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">

import type { Ref } from 'vue';
import { computed } from 'vue';
import type { SourcingCandidatureDetail } from 'erhgo-api-client';
import { useApi } from 'src/config/api';
import type { CandidatureModel } from 'src/hooks/useCandidature';
import useCandidature from 'src/hooks/useCandidature';
import CustomChip from 'components/candidature/CustomChip.vue';
import UserInfoCard from 'components/candidature/UserInfoCard.vue';
import CandidatureNote from 'components/candidature/CandidatureNote.vue';
import PreviousCandidatures from 'components/candidature/PreviousCandidatures.vue';
import UserDescription from 'components/candidature/UserDescription.vue';

const { service: { api } } = useApi();

const props = defineProps({
  candidatureId: { required: true, type: Number },
});

const candidatureService: CandidatureModel = useCandidature((await api.getSourcingCandidatureDetail(props.candidatureId)).data);
const candidature: Ref<SourcingCandidatureDetail> = candidatureService.candidature;
const recruitment = computed(() => {
  return candidature.value.recruitment;
});

</script>
<style lang="scss">
.candidature-detail {
  color: $erhgo-title;
  font-size: 15px;
}

.candidature-detail h4 {
  font-weight: bold;
  margin-bottom: 0;
  margin-top: 0;
  font-size: 21px;
}

.candidature-detail h5, h6 {
  font-style: italic;
  margin-bottom: 0;
  margin-top: 0;
  font-size: 19px;
}
</style>
