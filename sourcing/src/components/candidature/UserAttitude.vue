<template>
  <h4 v-if="attitude">Découvrez-moi en quelques mots</h4>
  <q-circular-progress v-if="isLoading"
                       indeterminate
                       size="30px"
                       class="q-mx-lg"
                       color="secondary" />

  <template v-else>{{ attitude }}</template>
</template>

<script setup lang="ts">

import { useApi } from 'src/config/api';
import { ref } from 'vue';

const props = defineProps({
  userId: { required: true, type: String },
});
const { service: { api, isLoading } } = useApi();
const attitude = ref<string>();
api.getUserBehaviorDescription(props.userId).then(response => attitude.value = response.data.description);
</script>
