<template>
  <custom-card>
    <div class="text-center">
      <h4>{{ candidature.firstName }} {{ candidature.lastName }}</h4>
      <h5 v-if="candidature.situation">Disponibilité&nbsp;{{ $t(`availability.${candidature.situation}`) }}</h5>
      <h6 v-if="lastConnectionDate">Dernière connexion le {{ lastConnectionDate }}</h6>
    </div>
    <deanonymize-dialog v-model="showAnonymousDialog"
                        :candidature-service="candidatureService"
    />
    <div class="row items-center infos-persos">
      <div class="col-12 q-mt-xs">
        <q-icon name="fa-light fa-envelope" class="q-mr-sm" :disabled="isAnonymous ? true : undefined" />
        <span class="caption" v-if="!isAnonymous">
            <a class="custom-lien" :href="`mailto:${candidature.email}`">{{ candidature.email }}</a>
        </span>
      </div>
      <div class="col-12 q-mt-xs">
        <q-icon name="fa-light fa-phone" class="q-mr-sm" :disabled="isAnonymous ? true : undefined" />
        <i class="fa-kit fa-regular-heart-pulse-circle-arrow-down"></i>
        <span class="caption" v-if="!isAnonymous">
            <a class="custom-lien" :href="`tel:${candidature.phone}`" v-if="candidature.phone">
              {{ $filters.formatPhone(candidature.phone) }}
            </a>
            <em v-else>Non précisé</em></span>
      </div>
      <div class="col-12 q-mt-xs q-mb-md">
        <q-icon name="fa-light fa-location-dot" class="q-mr-sm" />
        <span class="caption">
            {{ location }}
        </span>
        <span style="white-space: nowrap;" class="text-italic"> {{ mobility }}</span>
      </div>
      <div class="col-2 col-md-1 q-pb-sm q-pb-md-none">
        <q-icon name="fa-light fa-file-signature" size="sm" class="q-mr-sm" />
      </div>
      <div
        class="col-10 col-md-3 caption q-pb-sm q-pb-md-none">
        {{ candidature.contracts?.map(w => $t(`typeContractCategory.${w}`)).join(", ") || "Non précisé"
        }}
      </div>
      <div class="col-2 col-md-1 q-pb-sm q-pb-md-none">
        <q-icon name="fa-light fa-clock-rotate-left" size="sm" class="q-mr-sm" />
      </div>
      <div
        class="col-10 col-md-3 caption q-pb-sm q-pb-md-none">
        {{ candidature.workingTimes?.map(w => $t(`workingTimes.${w}`)).join(", ") || "Non précisé"
        }}
      </div>

      <template v-if="candidature.hasDrivingLicense">
        <div class="col-2 col-md-1 q-pb-sm q-pb-md-none">
          <q-icon name="fa-light fa-car-side" class="q-mr-sm" size="sm" />
        </div>
        <div class="col-10 col-md-3 q-pb-sm q-pb-md-none">
          <span class="caption">Permis B</span>
        </div>
      </template>

    </div>
    <div class="row q-py-md">
      <div v-if="isRecruitmentOpen" class="col-6">
        <div class="row justify-start">
          <q-btn
            v-if="candidature.state !== SourcingCandidatureState.DISMISS && candidature.state !== SourcingCandidatureState.NEW"
            size="md"
            class="green-text"
            flat
            dense
            round
            :loading="candidatureService.loading.value"
            :icon="`fa-${candidature.state === SourcingCandidatureState.FAVORITE ? 'solid' : 'light'} fa-star`"
            @click.prevent="candidatureService.changeState(candidature.state === SourcingCandidatureState.FAVORITE ?  SourcingCandidatureState.CONTACTED : SourcingCandidatureState.FAVORITE)">
            <q-tooltip>
              <template v-if="candidature.state === SourcingCandidatureState.FAVORITE">
                Retirer des candidatures favorites
              </template>
              <template v-else>
                Ajouter aux candidatures favorites
              </template>
            </q-tooltip>
          </q-btn>
          <div v-if="candidature.state === SourcingCandidatureState.DISMISS || isAnonymous">
            <q-btn size="md"
                   class="text-positive"
                   flat
                   dense
                   round
                   @click="showAnonymousDialog = true"
                   :loading="candidatureService.loading.value"
                   icon="fa-light  fa-envelope" />
            <q-tooltip>
              Accéder aux coordonnées de cette personne
              <template v-if="candidature.state === SourcingCandidatureState.DISMISS">(réintègre la candidature)
              </template>
            </q-tooltip>
          </div>
          <div v-else-if="candidature.state === SourcingCandidatureState.TO_CONTACT">
            <q-btn size="md"
                   class="text-positive"
                   flat
                   dense
                   round
                   @click="candidatureService.changeState(SourcingCandidatureState.CONTACTED)"
                   :loading="candidatureService.loading.value"
                   icon="fa-light  fa-envelope-circle-check" />
            <q-tooltip>
              Cliquer pour indiquer que vous avez contacté cette personne
            </q-tooltip>
          </div>
          <div>
            <q-btn size="md"
                   class="text-negative"
                   flat
                   dense
                   round
                   :loading="candidatureService.loading.value"
                   :disable="candidature.state === SourcingCandidatureState.DISMISS"
                   :icon="`fa-${candidature.state === SourcingCandidatureState.DISMISS ? 'solid' : 'light'} fa-circle-xmark`"
                   @click="refuseCandidatureAndSearchPotentialNewCandidates" />
            <q-tooltip>
              <template v-if="candidature.state === SourcingCandidatureState.DISMISS">
                Cette candidature a été écartée.
              </template>
              <template v-else>
                Écarter cette candidature
              </template>
            </q-tooltip>
          </div>
        </div>
        <div class="row justify-start">
          Candidature&nbsp;<strong>{{ $t(`candidatureState.${candidature.state}`) }}</strong>
        </div>
      </div>
      <div class="col-6 flex justify-end">
        <downloader-btn-quasar
          v-if="candidature.hasSoftSkillPdf"
          class="q-px-none"
          icon-size="lg"
          extension="pdf"
          :title="`SoftSkills_jenesuisPASunCV_${filenameSuffix}`"
          :download="() => softSkillsPDF()"
          icon="fa-regular fa-heart-pulse"
          label=" "
          tooltip="Télécharger les soft skills du candidat"
        >
          <template #icon>
            <download-skills-icon size="lg" />
          </template>
        </downloader-btn-quasar>
        <downloader-btn-quasar
          icon-size="lg"
          class="q-px-none"
          outlined
          extension="pdf"
          :title="`Profil_jenesuisPASunCV_${filenameSuffix}`"
          :title-anonymous="!isAnonymous ? `competences_${candidature.anonymousCode}_anonyme`:undefined"
          label=" "
          :download="(forceAnonymous?: boolean) => userProfileFO(forceAnonymous)"
        >
          <template #icon>
            <download-profile-competence-icon size="lg" />
          </template>

        </downloader-btn-quasar>
      </div>
    </div>
  </custom-card>
</template>
<script setup lang="ts">
import type { CandidatureModel } from 'src/hooks/useCandidature';
import type { ComputedRef, PropType} from 'vue';
import { computed, ref } from 'vue';
import type { SourcingCandidatureDetail} from 'erhgo-api-client';
import { RecruitmentState, SourcingCandidatureState } from 'erhgo-api-client';
import moment from 'moment/moment';
import DownloaderBtnQuasar from 'components/candidature/DownloaderBtnQuasar.vue';
import DownloadSkillsIcon from 'components/customicons/DownloadSkillsIcon.vue';
import DownloadProfileCompetenceIcon from 'components/customicons/DownloadProfileCompetenceIcon.vue';
import { useApi } from 'src/config/api';
import departmentForCode from 'src/models/Departments';
import DeanonymizeDialog from 'components/DeanonymizeDialog.vue';
import CustomCard from 'components/candidature/CustomCard.vue';

const { service: { api } } = useApi();

const props = defineProps({
  candidatureService: { required: true, type: Object as PropType<CandidatureModel> },
});
const candidature: ComputedRef<SourcingCandidatureDetail> = computed(() => props.candidatureService.candidature.value);

const recruitment = computed(() => {
  return candidature.value.recruitment;
});
const lastConnectionDate = computed(() => candidature.value.lastConnectionDate ? moment(candidature.value.lastConnectionDate).format('DD/MM/YY') : null);
const isRecruitmentOpen = computed(() => recruitment.value?.recruitmentState !== RecruitmentState.CLOSED);

const refuseCandidatureAndSearchPotentialNewCandidates = async () => {
  await props.candidatureService.changeState(SourcingCandidatureState.DISMISS);
};
const showAnonymousDialog = ref<boolean>(false);
const isAnonymous = computed(() => !candidature.value.email);
const softSkillsPDF = async () => (await (api.getCandidatureSoftSkillsPdfResult(candidature.value.candidatureId, { responseType: 'blob' }))).data;
const userProfileFO = async (forceAnonymous?: boolean) => candidature.value.userId ? (await api.getCandidatureUserProfileCompetences(candidature.value.candidatureId, forceAnonymous, { responseType: 'blob' })).data : null;
const filenameSuffix = computed(() => isAnonymous.value ? candidature.value.anonymousCode : `${candidature.value.firstName ?? ''}_${candidature.value.lastName ?? ''}`);
const location = computed(() => {
    if (!candidature.value.location) {
      return 'Non précisé';
    }
    const departmentCode = candidature.value.location.departmentCode;
    const postcode = candidature.value.location.postcode;
    return isAnonymous.value ? departmentForCode(departmentCode) : `${candidature.value.location.city} (${postcode})`;
  },
);
const mobility = computed(() => {
  if (isAnonymous.value || !candidature.value.location?.radiusInKm) {
    return '';
  }
  const radiusInKm = candidature.value.location?.radiusInKm;
  return radiusInKm >= 200 ? ' - Mobilité nationale' : ` +/- ${radiusInKm} km`;
});
</script>
