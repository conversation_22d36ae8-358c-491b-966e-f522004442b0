<template>
  <custom-card class="full-width">
    <q-banner class="alert-text full-width" v-if="notEnoughExperiences">
      Attention&nbsp;: cette personne n'a pas encore renseigné intégralement son profil, nous vous encourageons à la
      contacter pour mieux la connaître.
    </q-banner>
    <user-attitude :user-id="userId" />
    <div class="q-py-lg">
      <user-hashtags :user-id="userId" />
    </div>
  </custom-card>
</template>

<script setup lang="ts">
import CustomCard from 'components/candidature/CustomCard.vue';
import UserAttitude from 'components/candidature/UserAttitude.vue';
import UserHashtags from 'components/candidature/UserHashtags.vue';

defineProps({
  userId: { required: true, type: String },
  notEnoughExperiences: { default: false },
});


</script>
