<template>
  <q-chip
    :style="`background-color: ${$t(`sectors.${sector.code}.bg`)};color: ${$t(`sectors.${sector.code}.fg`)};`"
    dense><span
    class="text-caption"><q-tooltip>
            {{ sector.label }}
          </q-tooltip>
            {{ sector.abbreviation }}
          </span>
  </q-chip>
</template>
<script setup lang="ts">

defineProps({
  sector: {
    type: Object,
    required: true,
  },
});
</script>
