<template>
  <section class="q-py-lg candidate-notes">
    <h4>Précédentes candidatures</h4>
    <template v-if="previousCandidatures.length">
      <p class="text-italic q-pt-sm">
        Cette personne a déjà candidaté à {{ $tc("previousCandidature", previousCandidatures.length) }}.
      </p>
      <q-table
        dense
        flat
        class="previous-candidatures"
        :wrap-cells="true"
        separator="none"
        :rows="previousCandidatures"
        :columns="previousCandidaturesColumns"
        @row-click="previousCandidatureClicked"
        :pagination="{
              sortBy: 'submissionDate',
              descending: true
            }"
      />
    </template>
    <p class="text-italic q-pt-sm" v-else>
      Cette personne n'a candidaté à aucune autre offre.
    </p>

  </section>
</template>

<script setup lang="ts">
import moment from 'moment/moment';
import type { QTableProps } from 'quasar';
import type { SourcingCandidatureSumUp } from 'erhgo-api-client';
import { useRouter } from 'vue-router';
import type { PropType } from 'vue';
import { getCurrentInstance } from 'vue';

const router = useRouter();
const t: (a: string) => string = getCurrentInstance().appContext.app.config.globalProperties.$t;
defineProps({
  previousCandidatures: {
    required: false,
    default: () => [],
    type: Array as PropType<Array<SourcingCandidatureSumUp>>,
  },
});
const previousCandidaturesColumns: QTableProps['columns'] = [
  {
    name: 'jobTitle',
    align: 'center',
    label: 'Offre',
    field: 'jobTitle',
    sortable: true,
  },
  {
    name: 'submissionDate',
    label: 'Date',
    align: 'center',
    field: 'submissionDate',
    sortable: true,
    format: val => val ? moment(val).format('DD/MM/YY') : 'Inconnue',
  },
  {
    name: 'state',
    align: 'center',
    label: 'État',
    field: 'state',
    sortable: true,
    format: val => t(`candidatureState.${val}`),
  },
];

const previousCandidatureClicked = (_: unknown, row: SourcingCandidatureSumUp) => {
  const redirect = {
    name: 'candidature-detail',
    params: {
      candidatureId: row.id,
    },
  };
  const url = router.resolve(redirect).href;
  window.open(url, '_blank');
};


</script>
<style lang="scss">
.previous-candidatures th {
  font-weight: bold;
}

.previous-candidatures th, td {
  color: $erhgo-title;
}

.previous-candidatures td {
  background-color: $erhgo-grey4;
  border-bottom: solid 5px white;

}

.previous-candidatures tr td:nth-child(2) {
  border-left: dotted 3px white;
  border-right: dotted 3px white;
}

.previous-candidatures tr td:nth-child(1) {
  border-top-left-radius: 1rem;
  border-bottom-left-radius: 1rem;
}

.previous-candidatures tr td:nth-child(3) {
  border-top-right-radius: 1rem;
  border-bottom-right-radius: 1rem;
}
</style>
