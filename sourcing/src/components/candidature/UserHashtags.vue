<template>
  <h4 v-if="isLoading || hashtags?.length">Mon parcours demain</h4>
  <q-circular-progress v-if="isLoading"
                       indeterminate
                       size="30px"
                       class="q-mx-lg"
                       color="secondary" />
  <template v-else-if="hashtags?.length">
    <div class="row">
      <div :class="`col-12 col-sm-6 col-md-${colMd}`" v-for="hashtag in hashtags" :key="hashtag"
           style="word-wrap: anywhere">
        {{ hashtag }}
      </div>
    </div>
  </template>
</template>

<script setup lang="ts">

import { useApi } from 'src/config/api';
import { computed, ref } from 'vue';

const props = defineProps({
  userId: { required: true, type: String },
});
const { service: { api, isLoading } } = useApi();
const hashtags = ref<Array<string>>();
api.getUserHashtags(props.userId).then(response => hashtags.value = response.data.sort());
const colMd = computed(() => hashtags.value && hashtags.value.some(h => h.length > 15) ? 6 : 4);
</script>
