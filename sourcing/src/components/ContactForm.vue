<template>
  <template v-if="!sent">
    <div id="contact-form" class="row justify-between text-body1">
      <span class="col-12 q-pb-sm">
        Remplissez le formulaire ci-dessous et nous vous recontacterons&nbsp;!
      </span>
      <span class="col-12 q-py-sm">
          <q-icon name="fa-regular fa-calendar-days q-pr-sm"/>
          <a class="text-black" href="https://calendly.com/entreprisejenesuispasuncv" target="_blank">
            Prenez un rendez-vous téléphonique
          </a>
      </span>
      Ou envoyez-nous un message&nbsp;:
      <q-form @submit="submit" ref="form" class="col-12">
        <q-input
            v-model="command.fullname"
            v-if="!authentication.authenticated"
            label="Votre prénom et votre nom *"
            class="q-mt-md text-black text-body1"
            :rules="[v => !!v || 'Veuillez préciser votre nom']"
        />
        <q-input
            v-model="command.userEmail"
            v-if="!authentication.authenticated"
            label="Votre adresse mail *"
            class="q-mt-md text-black text-body1"
            :rules="[v => !!v || 'Veuillez préciser votre adresse mail']"
        />
        <q-input
            name="content"
            v-model="command.content"
            type="textarea"
            label="Votre message *"
            class="q-mt-md q-pa-sm text-black text-body1"
            :rules="[v => !!v || 'Veuillez remplir votre message']"
            hint="Donnez-nous quelques détails sur les profils que vous voulez sourcer (service qui recrute, métier, localisation, nombre d'offres ouvertes…)"
        />
        <div class="q-py-md">
          <q-btn type="submit" label="Envoyer" outline class="q-mt-xl text-black" size="md" :loading="isLoading"/>
        </div>
      </q-form>
    </div>
  </template>
  <div v-else>
    <q-banner class="valid-text">
      <div class="q-pa-sm">
        <p class="text-h6">Merci&nbsp;! Nous revenons vers vous dans les plus brefs délais.</p>
        <slot name="message"/>
      </div>
    </q-banner>
  </div>
</template>
<script lang="ts" setup>

import { ref } from 'vue';
import type { SendContactFormCommand } from 'erhgo-api-client';
import { QForm } from 'quasar';
import { useApi } from 'src/config/api';
import authentication from 'src/config/authentication';

const form = ref<QForm>();
const sent = ref<boolean>(false);
const {service: {api, isLoading}} = useApi();
const props = defineProps({context: {type: String, required: true}});
const command = ref<SendContactFormCommand>({content: '', context: props.context});

interface Emits {
  (e: 'sending'): void

  (e: 'done'): void
}

const emits = defineEmits<Emits>();

const submit = async () => {
  emits('sending');
  if (await form?.value?.validate?.()) {
    await api.sendContactForm(command.value);
    sent.value = true;
    emits('done');
  }
};

</script>
