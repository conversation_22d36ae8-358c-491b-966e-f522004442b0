<template>
  <p class="custom-h-rappel-metier text-weight-light q-ma-none q-px-sm">
    {{ title }}
  </p>
  <p v-if="nbUsers !== undefined"
     class="user-text custom-banner-inline text-body1 text-weight-bold custom-text-md"
     :class="{invisible: nbUsers === null}"
  >
    <q-icon name="fa-solid fa-user q-mr-sm" size="sm"/>
    {{ $tc('foundPeople', nbUsers, {count: nbUsers >= 1000 ? 'Plus de 1000' : nbUsers}) }}
  </p>
</template>
<script setup lang="ts">

defineProps({
  title: {type: String, required: true},
  nbUsers: { type: Number, required: false },
});
</script>
