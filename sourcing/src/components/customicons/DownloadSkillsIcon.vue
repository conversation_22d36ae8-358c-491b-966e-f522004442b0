<template>
  <q-icon v-bind="attrs">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512">
      <!--https://fontawesome.com/kits/0c3b7a12d7/icons/regular-heart-pulse-circle-arrow-down-->
      <path
        d="M0 189.5C0 119.1 50 58.6 119.2 45.4C158.6 37.9 198.9 47 231 69.6c9.1 6.4 17.5 13.9 25 22.3c4.2-4.8 8.8-9.2 13.5-13.3c3.7-3.2 7.5-6.2 11.5-9C313.1 47 353.4 37.9 392.8 45.5C462 58.7 512 119.1 512 189.5l0 3.3c-5.3-.6-10.6-.8-16-.8c-11 0-21.7 1-32.1 2.9c0-.7 0-1.5 0-2.2l0-3.3c0-47.4-33.6-88-80.1-96.9c-34-6.5-68.9 5.3-92 31.2l-.1 .1-.1 .1-17.8 20c-.3 .4-.6 .8-1 1.1c-4.5 4.5-10.6 7-16.9 7s-12.4-2.5-16.9-7c-.3-.4-.7-.7-1-1.1l-17.8-20-.1-.1c-23-25.8-58-37.7-92-31.2C81.6 101.5 48 142.2 48 189.5l0 3.3c0 23.1 7.8 45.3 21.8 63.1l52.9 0c6.5 0 12.3-3.8 14.8-9.8l31.8-76.3c2.4-5.8 8.1-9.7 14.5-9.8s12.2 3.6 14.8 9.4l58.2 129.4 48.9-97.9c2.7-5.4 8.2-8.8 14.3-8.8s11.6 3.4 14.3 8.8l23.2 46.3c1.2 2.4 3 4.5 5.1 5.9c-7 8.1-13.2 16.8-18.6 26.1c-6.3-4.5-11.6-10.5-15.2-17.7L320 243.8l-49.8 99.4c-2.7 5.5-8.4 8.9-14.6 8.8s-11.8-3.8-14.3-9.4L184.5 216.3l-17.6 42.2C159.5 276.3 142 288 122.6 288l-20.3 0L56 288l-22.1 0C12.1 261.3 0 227.7 0 192.8l0-3.3zM66.3 320l70.5 0L256 430.7l64-59.5c.4 19.6 3.9 38.5 10.2 56l-41.6 38.6-2.5 2.3c-8.2 7.7-19 11.9-30.2 11.9s-22-4.3-30.2-11.9c-.8-.8-1.7-1.5-2.5-2.3L66.3 320zM352 368c0-51.4 27.4-99 72-124.7s99.4-25.7 144 0s72 73.3 72 124.7s-27.4 99-72 124.7s-99.4 25.7-144 0S352 419.4 352 368zm76.7-3.3c-6.2 6.2-6.2 16.4 0 22.6l56 56c6.2 6.3 16.4 6.3 22.6 0l56-56c6.2-6.2 6.2-16.4 0-22.6s-16.4-6.2-22.6 0L512 393.4l0-89.4c0-8.8-7.2-16-16-16s-16 7.2-16 16l0 89.4-28.7-28.7c-6.2-6.2-16.4-6.2-22.6 0z" />
    </svg>
  </q-icon>
</template>
<script setup lang="ts">
import { useAttrs } from 'vue';

const attrs = useAttrs();
</script>
