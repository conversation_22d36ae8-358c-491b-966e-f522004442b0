<template>
  <q-icon v-bind="attrs">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512">
      <!--https://fontawesome.com/kits/bb15ff7ae0/icons/sharp-light-circle-check-circle-xmark/sharp-light-circle-check-circle-xmark-->
      <path
        d="M34.3 128C80 48.8 164.5 0 256 0s176 48.8 221.7 128c11.8 20.4 20.5 42 26.2 64.2c-2.6-.1-5.3-.2-7.9-.2c-8.4 0-16.7 .6-24.8 1.7c-5-17.2-12-33.9-21.2-49.7C410 74.7 336 32 256 32S102 74.7 62 144s-40 154.7 0 224s114 112 194 112c31.7 0 62.5-6.7 90.5-19.1c5.8 9.3 12.5 18 19.8 26.1c-33.9 16.2-71.5 25-110.4 25c-91.5 0-176-48.8-221.7-128s-45.7-176.8 0-256zM137.4 256L160 233.4l11.3 11.3L224 297.4 340.7 180.7 352 169.4 374.6 192l-11.3 11.3c-42.7 42.7-85.3 85.3-128 128L224 342.6l-11.3-11.3-64-64L137.4 256zm233.9 40c25.7-44.6 73.3-72 124.7-72s99 27.4 124.7 72s25.7 99.4 0 144S547.4 512 496 512s-99-27.4-124.7-72s-25.7-99.4 0-144zM399 312c-20 34.7-20 77.3 0 112s57 56 97 56s77-21.3 97-56s20-77.3 0-112s-57-56-97-56s-77 21.3-97 56zm26.4 8L448 297.4l11.3 11.3L496 345.4l36.7-36.7L544 297.4 566.6 320l-11.3 11.3L518.6 368l36.7 36.7L566.6 416 544 438.6l-11.3-11.3L496 390.6l-36.7 36.7L448 438.6 425.4 416l11.3-11.3L473.4 368l-36.7-36.7L425.4 320z" />
    </svg>
  </q-icon>
</template>
<script setup lang="ts">
import { useAttrs } from 'vue';

const attrs = useAttrs();
</script>
