<template>
  <q-icon v-bind="attrs">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512">
      <!--https://fontawesome.com/kits/bb15ff7ae0/icons/vlatest/light-clipboard-list-check-pen-->
      <path
        d="M0 144c0-35.3 28.7-64 64-64l2.7 0C73.3 61.4 91.1 48 112 48l6.7 0C131 19.8 159.2 0 192 0s61 19.8 73.3 48l6.7 0c20.9 0 38.7 13.4 45.3 32l2.7 0c35.3 0 64 28.7 64 64l0 140.2-32 32L352 144c0-17.7-14.3-32-32-32l0 16c0 17.7-14.3 32-32 32l-96 0-96 0c-17.7 0-32-14.3-32-32l0-16c-17.7 0-32 14.3-32 32l0 304c0 17.7 14.3 32 32 32l194.4 0-1 3.8c-2.3 9.5-1.8 19.3 1.4 28.2L64 512c-35.3 0-64-28.7-64-64L0 144zm76.7 92.7c6.2-6.2 16.4-6.2 22.6 0L112 249.4l36.7-36.7c6.2-6.2 16.4-6.2 22.6 0s6.2 16.4 0 22.6c-16 16-32 32-48 48c-6.2 6.2-16.4 6.2-22.6 0l-24-24c-6.2-6.2-6.2-16.4 0-22.6zM88 368c0-13.3 10.7-24 24-24s24 10.7 24 24s-10.7 24-24 24s-24-10.7-24-24zM96 96l0 32 96 0 96 0 0-32c0-8.8-7.2-16-16-16l-18 0c-7.3 0-13.7-4.9-15.5-12c-5.3-20.7-24.1-36-46.5-36s-41.2 15.3-46.5 36c-1.8 7.1-8.2 12-15.5 12l-18 0c-8.8 0-16 7.2-16 16zm64 272c0-8.8 7.2-16 16-16l96 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-96 0c-8.8 0-16-7.2-16-16zM176 80c0-8.8 7.2-16 16-16s16 7.2 16 16s-7.2 16-16 16s-16-7.2-16-16zm16 192c0-8.8 7.2-16 16-16l64 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-64 0c-8.8 0-16-7.2-16-16zM292.5 492.1l18.7-74.9c2.1-8.4 6.4-16.1 12.6-22.3L477.5 241.2c18.8-18.7 49.2-18.7 67.9 0l17.4 17.4c18.7 18.8 18.7 49.2 0 67.9L409.1 480.2c-6.2 6.1-13.9 10.5-22.3 12.6l-74.9 18.7c-5.5 1.4-11.2-.2-15.2-4.2s-5.6-9.7-4.2-15.2zM330 474l49-12.2c2.8-.7 5.3-2.1 7.4-4.2L492.7 351.3l-40-40c-35.3 35.4-70.8 70.9-106.2 106.3c-2 2-3.5 4.6-4.2 7.4L330 474zM475.3 288.6l40 40 24.8-24.8c6.2-6.2 6.2-16.4 0-22.6l-17.3-17.4c-6.2-6.2-16.4-6.2-22.6 0l-24.9 24.8z" />
    </svg>
  </q-icon>
</template>
<script lang="ts" setup>
import { useAttrs } from 'vue';

const attrs = useAttrs();
</script>
