DecisionIcon.vue
<template>
  <q-icon v-bind="attrs">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512">
      <!--https://fontawesome.com/kits/bb15ff7ae0/icons/light-thought-bubble-user/light-thought-bubble-user-->
      <path
        d="M0 208C0 132.9 60.9 72 136 72c1.6 0 3.2 0 4.8 .1C161.6 29.4 205.3 0 256 0s94.4 29.4 115.2 72.1c1.6-.1 3.2-.1 4.8-.1c58.2 0 107.9 36.6 127.3 88c-2.4-.2-4.8-.2-7.3-.2c-9.1 0-17.9 1.1-26.3 3.1C452.9 128.1 417.2 104 376 104c-4.4 0-8.7 .3-13 .8c-7.4 .9-14.4-3.3-17-10.3C332.4 58 297.2 32 256 32s-76.4 26-90 62.5c-2.6 7-9.6 11.2-17 10.3c-4.2-.5-8.6-.8-13-.8C78.6 104 32 150.6 32 208s46.6 104 104 103.9c11.5 0 22.6-1.9 32.9-5.3c7.1-2.3 14.8 .5 18.7 6.9C201.7 336.6 227.1 352 256 352s54.3-15.4 68.4-38.4c3.9-6.4 11.6-9.3 18.7-6.9c10.3 3.4 21.4 5.3 32.9 5.3c5.1 0 10.1-.4 15-1.1c4 10.7 9.5 20.6 16.4 29.4c-10.1 2.4-20.6 3.6-31.4 3.6c-10.8 0-21.2-1.2-31.3-3.6C324.2 366.9 292.1 384 256 384s-68.2-17.1-88.7-43.6c-10.1 2.3-20.5 3.6-31.3 3.6C60.9 344 0 283.1 0 208zM0 480c0-17.7 14.3-32 32-32s32 14.3 32 32s-14.3 32-32 32s-32-14.3-32-32zm102.4-72c8.6-14.9 24.4-24 41.6-24s33 9.1 41.6 24s8.6 33.1 0 48s-24.4 24-41.6 24s-33-9.1-41.6-24s-8.6-33.1 0-48zM128 432c0 8.8 7.2 16 16 16s16-7.2 16-16s-7.2-16-16-16s-16 7.2-16 16zm224 45.1c0-51.4 41.7-93.1 93.1-93.1l50.9 0 50.9 0c51.4 0 93.1 41.7 93.1 93.1c0 19.3-15.6 34.9-34.9 34.9l-218.2 0c-19.3 0-34.9-15.6-34.9-34.9zm32 0c0 1.6 1.3 2.9 2.9 2.9l218.2 0c1.6 0 2.9-1.3 2.9-2.9c0-33.7-27.4-61.1-61.1-61.1l-101.8 0c-33.7 0-61.1 27.4-61.1 61.1zM426.7 232c14.3-24.8 40.7-40 69.3-40s55 15.2 69.3 40s14.3 55.2 0 80s-40.7 40-69.3 40s-55-15.2-69.3-40s-14.3-55.2 0-80zm27.7 16c-8.6 14.9-8.6 33.1 0 48s24.4 24 41.6 24s33-9.1 41.6-24s8.6-33.1 0-48s-24.4-24-41.6-24s-33 9.1-41.6 24z" />
    </svg>
  </q-icon>
</template>
<script setup lang="ts">
import { useAttrs } from 'vue';

const attrs = useAttrs();
</script>
