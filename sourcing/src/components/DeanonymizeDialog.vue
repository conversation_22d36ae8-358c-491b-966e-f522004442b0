<template>
  <q-dialog v-model="showDialog" :maximized="$q.screen.lt.sm">
    <q-card class="text-center q-pa-sm-lg">
      <div class="text-h5 text-weight-bold q-mt-none row items-center">
        Contacter cette personne
        <q-space/>
        <q-icon name="fa-light fa-close cursor-pointer" size="md" @click="showDialog = false"/>
      </div>
      <div class="text-erhgo-grey q-py-md text-weight-bold text-body1">
        Vous vous apprêtez à contacter cette personne afin d'échanger avec elle et de la rencontrer.
        Pour cela, son identité va vous être révélée.
      </div>
      <div class="alert-text">
        Rappel&nbsp;: en vous inscrivant et en utilisant #jenesuisPASunCV, vous
        vous engagez moralement à contacter toutes les personnes que
        vous avez désanonymisées.
      </div>
      <q-btn outline class="q-mt-xs" no-caps @click="deanonymize" v-if="!contact"
             :loading="props.candidatureService.loading.value">
        <div>
          <h5 class="text-weight-bold q-pa-none q-ma-none ">OUI</h5>
          <div class="text-weight-bold text-body1">Voir les coordonnées <br/>pour contacter cette personne</div>
        </div>
      </q-btn>
      <div class="row items-center q-pt-md" @click="showDialog = false" v-if="!contact">
        <q-icon name="fa-light fa-arrow-left" class="q-ma-none" size="xs"/>
        <div class="text-left q-pl-sm cursor-pointer text-caption">
          Non, je ne la contacterai peut-être pas<br/>
          Revenir à la liste
        </div>
      </div>
      <template v-if="contact">
        <div class="col-12 text-left q-pt-sm">
          Voici les coordonnées de <strong>{{ contact.firstName }} {{ contact.lastName?.toUpperCase() }}</strong>&nbsp;:
        </div>
        <div class="row justify-between text-body1 q-py-sm">
          <div class="col text-left">
            <q-icon name="fa-light fa-envelope q-pr-sm"/>
            <span>Adresse email&nbsp;: </span>
            <a :href="`mailto:${contact.email}`">{{ contact.email }}</a>
          </div>
          <div class="col text-right">
            <q-icon name="fa-light fa-phone-rotary  q-pr-sm"/>
            <span>Téléphone&nbsp;: </span>
            <a :href="`tel:${contact.phone}`" v-if="contact.phone">{{ $filters.formatPhone(contact.phone) }}</a>
            <em v-else>Non précisé</em>
          </div>
        </div>
        <div class="row justify-between q-pt-sm q-col-gutter-x-md">
          <div class="col-6">
            <q-btn @click="contactNow"
                   :loading="props.candidatureService.loading.value"
                   outline
                   class="full-width bg-secondary q-pa-xs col-6"
                   no-caps
            >
              <div class="q-mr-md-lg">
                <q-icon name="fa-light fa-check" size="xs"/>
              </div>
              J'ai contacté<br/>cette personne
            </q-btn>
          </div>
          <div class="col-6">
            <q-btn outline no-caps @click="showDialog = false" class="q-pa-xs col-6 full-width">
              <div class="q-mr-md-lg">
                <q-icon name="fa-light fa-alarm-clock" size="xs" class="q-mr-sm"/>
              </div>
              Je la contacterai<br/>ultérieurement
            </q-btn>
          </div>
        </div>
      </template>
    </q-card>
  </q-dialog>
</template>
<script setup lang="ts">

import {ref, watchEffect} from 'vue';
import type { SourcingContactInformation} from 'erhgo-api-client';
import {SourcingCandidatureState} from 'erhgo-api-client';

type Emits = (e: 'update:modelValue', input: boolean) => void;

const emit = defineEmits<Emits>();
const props = defineProps({
  modelValue: {type: Boolean, required: true},
  candidatureService: {required: true, type: Object},
});

const showDialog = ref<boolean>(props.modelValue);
watchEffect(() => emit('update:modelValue', showDialog.value));
watchEffect(() => showDialog.value = props.modelValue);
const contact = ref<SourcingContactInformation>();

const contactNow = async () => {
  contact.value = await props.candidatureService.changeState(SourcingCandidatureState.CONTACTED);
  showDialog.value = false;
};

const deanonymize = async () => {
  contact.value = await props.candidatureService.changeState(SourcingCandidatureState.TO_CONTACT);
};


</script>
