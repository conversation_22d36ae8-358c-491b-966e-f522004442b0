<template>
  <q-card>
    <q-circular-progress indeterminate size="50px" color="secondary" v-if="isLoading" />
    <template v-else>
      <q-card-section class="row q-pa-none">
        <div class="text-h6 q-pl-sm">{{ forcedTitle ?? occupation.title }}</div>
        <q-space />
        <q-icon name="fa-light fa-close cursor-pointer" size="md" @click="emit('close')" />
      </q-card-section>
      <q-card-section class="q-pt-none q-pl-none q-pb-md row">
        <div class="col-4 title-breaker">&nbsp;</div>
      </q-card-section>
      <q-card-section class="q-pt-none">
        <div v-html="occupation.description" />
      </q-card-section>
      <q-card-section class="q-pt-none" v-if="occupation.attitude">
        <div class="text-h6">Attitudes attendues</div>
        <pre style="white-space: pre-wrap;word-wrap: break-word;">{{ occupation.attitude }}</pre>
      </q-card-section>
      <q-card-section class="q-pt-none" v-if="otherTitles">
        <div class="text-h6">Autres libellés</div>
        <pre style="white-space: pre-wrap;word-wrap: break-word;">{{ otherTitles }}</pre>
      </q-card-section>

    </template>
  </q-card>
</template>
<script setup lang="ts">
import { useApi } from 'src/config/api';
import { computed, ref } from 'vue';
import type { ErhgoOccupationSumUp } from 'erhgo-api-client';

type Emits = (e: 'close') => void;
const emit = defineEmits<Emits>();
const props = defineProps({
  occupationId: { type: String, required: true },
  forcedTitle: { type: String, required: false },
});
const { service: { api, isLoading } } = useApi();
const occupation = ref<ErhgoOccupationSumUp>((await api.getErhgoOccupationSumUp(props.occupationId)).data);

const otherTitles = computed(() => {
  let others = occupation.value.otherTitles.filter(o => !o.includes('/'));
  others = (!others.length ? occupation.value.otherTitles : others)
    .filter(o => o !== props.forcedTitle)
    .slice(0, 5);
  if (props.forcedTitle !== occupation.value.title) {
    others.push(occupation.value.title);
  }
  return others.join(', ');
});
</script>

