<template>
  <div class="row items-center q-gutter-sm">
    <q-btn-dropdown icon="fa-solid fa-share-nodes" size="sm">
      <q-list>
        <q-item clickable v-close-popup @click="shareOnLinkedIn">
          <q-item-section avatar>
            <q-icon name="fa-brands fa-linkedin" color="indigo" />
          </q-item-section>
          <q-item-section>LinkedIn</q-item-section>
        </q-item>

        <q-item clickable v-close-popup @click="shareOnFacebook">
          <q-item-section avatar>
            <q-icon name="fa-brands fa-facebook" color="blue-9" />
          </q-item-section>
          <q-item-section>Facebook</q-item-section>
        </q-item>

        <q-item clickable v-close-popup @click="copyShareLink">
          <q-item-section avatar>
            <q-icon name="fa fa-copy" color="grey-8" />
          </q-item-section>
          <q-item-section>Le lien</q-item-section>
        </q-item>
      </q-list>
    </q-btn-dropdown>

  </div>
</template>
<script setup lang="ts">
import type { PropType } from 'vue';
import { getCurrentInstance } from 'vue';
import type { SourcingRecruitmentItem } from 'erhgo-api-client';
import type { RecruitmentService } from 'src/hooks/useRecruitement';
import { baseUrl } from 'src/config/base-url';


const props = defineProps({
  recruitment: { type: Object as PropType<RecruitmentService<SourcingRecruitmentItem>>, required: true },
});

const instance = getCurrentInstance();
const $q = instance.appContext.config.globalProperties.$q;

const getShareUrl = () => {
  return `${baseUrl('fo')}/jobs/R-${props.recruitment.recruitmentId}`;
};

const shareOnLinkedIn = () => {
  const linkedInUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(getShareUrl())}`;
  window.open(linkedInUrl, '_blank');
};

const shareOnFacebook = () => {
  const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(getShareUrl())}`;
  window.open(facebookUrl, '_blank');
};

const copyShareLink = () => {
  const url = getShareUrl();
  void navigator.clipboard.writeText(url).then(() => {
    $q.notify({
      message: 'Lien copié dans le presse-papier',
      color: 'positive',
      icon: 'fa-light fa-check',
    });
  });
};
</script>

<style scoped>
.share-buttons {
  margin: 1rem 0;
}
</style>
