<template>
  <div class="q-mt-xs users-select" style="min-width: 400px">
    <div class="user-text row  items-start text-justify q-pa-md text-caption">
      <div style="text-decoration: underline" class="text-weight-bold">Personnes notifiées</div>
      <div>
        Un mail est envoyé pour vous informer des recrutements disposant de nouvelles candidatures. Vous
        pouvez ici modifier les personnes concernées par cette notification. Par défaut, seule la personne ayant créé le
        recrutement est notifiée. <br />
        Pour ajuster la fréquence à laquelle nous vous envoyons ces notifications,
        <router-link class="text-weight-bold" :to="{ name : 'user-profile'}">veuillez vous
          rendre dans votre profil.
        </router-link>
      </div>
      <template v-if="withManager">
        <div style="text-decoration: underline" class="q-pt-xs text-weight-bold">Personne responsable</div>
        <div>
          <PERSON><PERSON> d<PERSON><PERSON><PERSON>, la personne ayant créé l'offre est marquée comme responsable. Vous pouvez modifier ici le
          responsable en cliquant sur l'icône à droite de son email.
        </div>
      </template>
    </div>

    <q-list dense>
      <q-item dense>
        <q-item-section avatar>
          <q-btn v-if="!allSelected"
                 class="q-pl-xs"
                 flat
                 size="sm"
                 :disable="allSelected"
                 @click="selectAllUsers"
                 :loading="loadingUpdate">
            <q-icon name="fa-light fa-square"
                    size="sm" color="black" />
            {{ "Sélectionner tout" }}
          </q-btn>
          <q-btn v-else
                 class="q-pl-none"
                 flat
                 size="sm"
                 @click="resetAllUsers"
                 :loading="loadingUpdate">
            <q-icon name="fa-light fa-times" size="sm" :loading="loadingUpdate"
                    color="red" />
            Réinitialiser
          </q-btn>
        </q-item-section>
        <q-item-section>&nbsp;</q-item-section>
        <q-item-section avatar v-if="withManager">
          Responsable de l'offre&nbsp;?
        </q-item-section>
      </q-item>
      <q-item v-for="option in users" :key="option.id"
              dense
              clickable
              style="cursor: default !important;">
        <q-item-section avatar>
          <q-checkbox
            v-model="updatedUsersToNotify"
            :val="option"
            color="secondary"
          >
            <q-item-label>{{ option.fullname }} - <span class="text-caption">{{ option.email }}</span></q-item-label>
          </q-checkbox>
        </q-item-section>
        <q-item-section>
          <div v-if="option?.notificationRefused" class="text-caption text-negative q-ml-sm">
            <q-icon name="fa-regular fa-warning" class="q-mr-xs" />
            Attention&nbsp;: {{ currentUserEmail === option.email ? "Vous avez" : "Cette personne a" }} choisi
            de ne pas recevoir de notification.
          </div>
        </q-item-section>
        <template v-if="withManager">
          <q-item-section avatar v-if="managerUser?.id === option.id">
            <q-icon name="fa-solid fa-user-check" class="q-mr-xs grey-1 text-secondary" />
          </q-item-section>
          <q-item-section avatar style="cursor: pointer" v-else>
            <q-icon name="fa-thin fa-user" class="q-mr-sm grey-1 text-erhgo-grey " @click="updateManager(option)" />
          </q-item-section>
        </template>
      </q-item>
    </q-list>
      <div v-if="showNoUserWarning" class="text-negative row justify-center">
        <q-icon name="fa-light fa-warning q-mr-xs" size="sm"/>
        <span>
        Attention, aucun utilisateur ne sera notifié pour ce recrutement</span>
      </div>
  </div>
</template>
<script setup lang="ts">

import { computed, ref, watch } from 'vue';
import { useApi } from 'src/config/api';
import type { SourcingUser } from 'erhgo-api-client';
import authentication from 'src/config/authentication';
import _ from 'underscore';

const currentUserEmail = authentication.authData?.email;
const organizationCode = authentication.authData?.realm_access?.roles.find(e => e.includes('S-'));
const users = ref<SourcingUser[]>([]);
const updatedUsersToNotify = ref<SourcingUser[]>([]);
const managerUser = ref<SourcingUser | undefined>();

const props = defineProps({
  selectedUsersIds: { type: Array<string>, required: false },
  managerUserId: { type: String, required: false },
  loadingUpdate: { type: Boolean, required: false },
  isSuccess: { type: Boolean, required: false },
  withManager: { type: Boolean, required: false },
});

const { service: { api } } = useApi();

interface Emits {
  (e: 'update:selected-users', value: Array<SourcingUser>): void;

  (e: 'update:manager-user', value: SourcingUser): void;
}
const emit = defineEmits<Emits>();

const showNoUserWarning = computed(() => updatedUsersToNotify.value.length === 0);

users.value = [...(await api.getSourcingUsers(organizationCode)).data].sort((a, b) => a.fullname.localeCompare(b.fullname));
updatedUsersToNotify.value = users.value.filter(u => (props.selectedUsersIds ?? []).some(id => id === u.id));
managerUser.value = users.value.filter(u => props.managerUserId === u.id)[0];
const debouncedUpdateUsersToNotify = _.debounce(() => {
  emit('update:selected-users', updatedUsersToNotify.value);
}, 500);

const selectAllUsers = () => {
  updatedUsersToNotify.value = [...users.value.map(user => user)];
};
const resetAllUsers = () => {
  const currentUser = users.value.find(user => user.email === authentication.authData?.email);
  if (currentUser) {
    updatedUsersToNotify.value = [currentUser];
  }
};

watch(() => updatedUsersToNotify.value, () => debouncedUpdateUsersToNotify());

const allSelected = computed(() => updatedUsersToNotify.value.length === users.value.length);

const updateManager = (user: SourcingUser) => {
  managerUser.value = user;
  emit('update:manager-user', managerUser.value);
};

</script>
<style lang="scss">
.users-select .q-focus-helper {
  background-color: inherit;
}
</style>
