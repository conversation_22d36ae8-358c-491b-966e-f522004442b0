<template>
  <div class="bg-white q-px-lg" style="max-width: 1000px">
    <q-banner class="q-my-md alert-text text-center" v-if="blockUser">
      <q-icon name="fa-light fa-warning q-mr-sm" size="sm"/>
      <span class=" text-body1">Votre période d'essai est arrivée à expiration&nbsp;: votre compte est suspendu</span>
    </q-banner>
    <h1 class="bg-secondary custom-h1 q-pa-md q-mt-md q-mb-lg text-center rounded-borders">
      Renseignez votre code d'activation
    </h1>
    <q-banner class="q-my-md alert-text text-center" v-if="invitationError || accountProvider.isError.value">
      <q-icon name="fa-light fa-warning q-mr-sm" size="sm"/>
      <span
          class=" text-body1">{{
          invitationError || "Erreur à l'activation de votre compte, merci de rafraîchir la page"
        }}</span>
    </q-banner>
    <q-banner class="q-my-md alert-text text-center" v-if="sessionIsExpired">
      <q-icon name="fa-light fa-warning q-mr-sm" size="sm"/>
      <span class=" text-body1">Votre session d'essai à expiré</span>
    </q-banner>
    <div class="row justify-around q-mt-lg q-py-md">
      <div class="col-sm-5 col-12 text-body1 q-pr-lg">
        <h2 class="q-ma-none q-mb-md text-center custom-h2">Code d'activation</h2>
        Merci de saisir le code ici, puis cliquez sur "Activer mon compte"
        <q-input
            v-model="invitationCode"
            hint="Saisissez votre code d'activation *"
        >
          <template v-slot:prepend>
            <q-icon name="fa-light fa-key"/>
          </template>
        </q-input>
        <div>
          <q-tooltip v-if="!invitationCode">
            Veuillez saisir un code d'activation ou nous contacter.
          </q-tooltip>
          <q-btn class="q-mt-md full-width bg-secondary outline"
                 @click="updateSubscription"
                 :disable="!invitationCode"
                 :loading="isLoading || accountProvider.isLoading.value"

          >
            Activer mon compte
          </q-btn>
        </div>
      </div>
      <q-separator vertical inset class="gt-xs" color="secondary"/>
      <div class="col-12 q-my-lg xs">
        <q-separator color="secondary"/>
      </div>
      <div class="col-sm-6 col-12 text-body1 q-pl-sm-md q-pb-xl">
        <h2 class="q-ma-none q-mb-md text-center custom-h2">Contact</h2>
        <p>Vous n'avez pas de code d'activation&nbsp;: échangeons au sujet de vos besoins.</p>
        <contact-form context="Je veux obtenir des informations" @sending="invitationError=null"/>
      </div>
    </div>
    <div>
      <q-btn class="q-my-md bg-red text-white full-width alert-text outline"
             rounded
             icon="fa-light fa-xmark"
             v-if="!blockUser"
             @click="closePopin(false)"
      >
        Fermer
      </q-btn>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ContactForm from 'components/ContactForm.vue';
import { useApi } from 'src/config/api';
import type { AxiosError } from 'axios';
import axios from 'axios';
import accountProvider from 'src/config/AccountProvider';

const invitationError = ref<string>();
const invitationCode = ref<string>('');
const sessionIsExpired = ref<boolean>(false);
const {service: {api: apiForInit, isLoading, isError}} = useApi(
    (e) => {
      if (axios.isAxiosError(e)) {
        if ((e as AxiosError).response?.status === 403) {
          invitationError.value = invitationCode.value ? `Ce code d'activation n'est pas connu` : '';
          return false;
        } else if ((e as AxiosError).response?.status === 402) {
          invitationError.value = invitationCode.value ? `Ce code d'activation n'est plus valide` : '';
          return false;
        }
      }
      invitationError.value = 'Une erreur est survenue, veuillez réessayer ou contacter le support.';
      return true;
    },
);

const updateSubscription = async () => {
  invitationError.value = null;
  await apiForInit.updateSubscription({
    invitationCode: invitationCode.value,
  });
  if (!isError.value) {
    await accountProvider.refresh();
    if (!accountProvider.isError.value) {
      closePopin(true);
    }
  }
};

type Emits = (e: 'closePopin', success: boolean) => void;

const closePopin = (success: boolean) => emits('closePopin', success);

const emits = defineEmits<Emits>();

defineProps({
  blockUser: {required: true, type: Boolean},
});

</script>

