<template>
  <h1 class="custom-h1 q-my-sm">{{ $t(`recruitment.steps.${props.step}`) }}</h1>
  <div v-for="(criterion, index) in thresholdCriteria" :key="index" class="row">
    <p class="text-label q-mb-xs q-mt-xl col-12">{{ criterion.title }}</p>
    <q-checkbox color="secondary"
                v-for="(criterionValue, index) in criterion.criteriaValues"
                v-model="selectedCriteriaCodes"
                :key="index"
                :val="criterionValue.code"
                class="text-body1 q-pr-xl col-12 col-sm-6"
                :label="criterionValue.titleForBO"
                @update:model-value="() => removeOtherThresholdCriteriaValue(criterionValue.code, criterion.criteriaValues.map(c => c.code))"
    />
  </div>
  <div v-for="(criterion, index) in multipleCriteria" :key="index" class="row">
    <p class="text-label q-mb-xs q-mt-xl col-12">{{ criterion.title }}</p>
    <q-checkbox color="secondary"
                class="text-body1 q-pr-xl col-12 col-sm-6"
                v-for="(criterionValue, index) in criterion.criteriaValues"
                v-model="selectedCriteriaCodes"
                :key="index"
                :val="criterionValue.code"
                :label="criterionValue.titleForBO"

    />
  </div>
</template>
<script setup lang="ts">
import { onActivated, ref, watch } from 'vue';
import { useCriteriaService } from 'src/models/CriteriaService';
import type { SourcingCriteriaStep } from 'erhgo-api-client';

const props = defineProps({
  step: String,
  selectedCriteriaCodes: {
    type: Array,
    required: true,
  },
});

const {fetchCriteria, thresholdCriteria, multipleCriteria} = useCriteriaService();
await fetchCriteria(props.step as SourcingCriteriaStep);
const selectedCriteriaCodes = ref<Array<string>>(props.selectedCriteriaCodes as string[] || []);
const emit = defineEmits<{
  (event: 'update:thresholdCriteria', selectedCriteriaCode: string, allCriteriaValueCodes: string[]): void;
  (event: 'update:criteriaSelection', addedCriteria: string[], removedCriteria: string[]): void;
}>();

const removeOtherThresholdCriteriaValue = (selectedCriteriaValueCode: string, allCriteriaValueCodes: string[]) => {
  selectedCriteriaCodes.value = selectedCriteriaCodes.value.filter(c => !allCriteriaValueCodes.includes(c) || selectedCriteriaValueCode === c);
};

watch(
  () => selectedCriteriaCodes.value,
  (newValue, oldValue) => {
    const addedCriteria = newValue.filter(v => !oldValue.includes(v));
    const removedCriteria = oldValue.filter(v => !newValue.includes(v));
    emit('update:criteriaSelection', addedCriteria, removedCriteria);
  });

onActivated(async () => await fetchCriteria(props.step as SourcingCriteriaStep));
</script>
