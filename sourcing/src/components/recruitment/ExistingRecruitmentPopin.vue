<template>
  <q-card style="max-width: max-content" class="no-padding">
    <q-card-section class="q-banner--dense no-padding">
      <q-banner class="text-center no-margin no-padding dense">
        <h5>
          {{ $tc('similarRecruitments', {count: recruitments.length}) }} à proximité &nbsp;:
        </h5>

      </q-banner>
    </q-card-section>
    <q-card-section class=" no-padding">
      <q-list>
          <template v-for="recruitment in recruitments" :key="recruitment.recruitmentId">
              <q-item style="min-height: 0">
                  <q-item-section class=" no-padding">
                      <router-link target="_blank"
                                   :to="{name: 'recruitment-detail', params: {recruitmentId: recruitment.recruitmentId}}">
                          <b class="text-dark"> {{ recruitment.title }} à {{ recruitment.location.city }}</b>
                      </router-link>
                  </q-item-section>
              </q-item>
          </template>
      </q-list>
    </q-card-section>
      <q-card-section>
        <span><i>
            Nous notifions les candidats dans un rayon de 50 km autour de l'offre.
            <br/>
        Vous pouvez <strong>continuer</strong> à créer cette offre ou <strong>annuler</strong> et retourner sur la liste des offres</i></span>
      </q-card-section>
      <q-card-section class="q-my-md row">
          <div class="col-9">
              <q-btn
                      v-close-popup
                      color="red"
                      text-color="black"
                      class="q-ml-lg"
                      :to="{name: 'recruitments-list'}">
                  Annuler
              </q-btn>
          </div>
          <div class="col-3">
              <q-btn
                      v-close-popup
                      color="secondary"
                      text-color="black"
                      @click="createRecruitmentAnyway"
              >
                  Continuer
              </q-btn>
          </div>
      </q-card-section>
  </q-card>
</template>


<script setup lang="ts">
import type { PropType } from 'vue';
import type { SourcingJobAndRecruitment } from 'erhgo-api-client';


defineProps({
  recruitments: {type: Object as PropType<SourcingJobAndRecruitment[]>, required: true},
});
const emit = defineEmits<Emits>();

interface Emits {
  (e: 'createRecruitmentAnyway'): void;
}

const createRecruitmentAnyway = () => {
  emit('createRecruitmentAnyway');
};


</script>
