<template>
  <h2 class="text-label q-pt-md q-my-none">Contrat de travail *</h2>
  <div class="column">
    <q-radio
      :class="{'text-negative text-weight-bold': props.submitted && !typeContractCategory}"
      v-model="typeContractCategory"
      v-for="contractType in contractTypes"
      :key="contractType"
      :label="$t(`contractType.${contractType}`)"
      :val="contractType"
      @update:modelValue="emit('update:modelValue', {...props.modelValue, typeContractCategory})"
      color="secondary"
    />
  </div>
  <div class="text-caption text-negative" v-if="props.submitted && !typeContractCategory">
    Veuillez préciser le type de contrat
  </div>
</template>
<script setup lang="ts">
import type { PropType} from 'vue';
import { defineEmits, ref } from 'vue';
import type { UpdateSourcingJobContractCommand } from 'erhgo-api-client';
import { TypeContractCategory } from 'erhgo-api-client';

const contractTypes = Object.keys(TypeContractCategory);
const props = defineProps({
  submitted: Boolean,
  modelValue: {
    type: Object as PropType<Partial<UpdateSourcingJobContractCommand>>,
    required: false,
  },
});
const typeContractCategory = ref<TypeContractCategory | undefined>(props.modelValue?.typeContractCategory);

const emit = defineEmits<(event: 'update:modelValue', value: Partial<UpdateSourcingJobContractCommand>) => void>();

</script>
