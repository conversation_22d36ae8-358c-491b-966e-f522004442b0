<template>
  <h2 class="text-label q-pt-md q-my-none">Temps de travail *</h2>
  <q-radio
    color="secondary"
    :class="{'text-negative text-weight-bold': submitted && !workingTimeType}"
    v-model="workingTimeType"
    v-for="workingTimeTypeItem in Object.keys(WorkingTime)"
    :label="$t(`workingTimeType.${workingTimeTypeItem}`)"
    @update:modelValue="update"
    :key="workingTimeTypeItem"
    :val="workingTimeTypeItem"
  />
  <div>
    <label for="workingWeeklyTime">
      Nombre {{ modularWorkingTime ? "moyen" : "" }} d'heures de travail hebdomadaire&nbsp;:
    </label>
    <div class="row q-pt-md">
      <q-slider
        class="col-12 col-sm-6 q-pt-lg"
        v-model="workingWeeklyTime"
        @update:modelValue="update"
        :min="1"
        :max="48"
        name="workingWeeklyTime"
        label-text-color="black"
        color="secondary"
        label-always
      />
    </div>
  </div>
  <q-checkbox
    v-model="modularWorkingTime"
    @update:modelValue="update"
    class="text-body1 q-pr-xl"
    label="Modulable"
    color="secondary"
  />
  <div class="text-caption text-negative" v-if="submitted && !workingTimeType">
    Veuillez préciser le temps de travail
  </div>
</template>
<script setup lang="ts">
import type { PropType} from 'vue';
import { defineEmits, ref } from 'vue';
import type { UpdateSourcingJobContractCommand} from 'erhgo-api-client';
import { WorkingTime } from 'erhgo-api-client';

const props = defineProps({
  submitted: Boolean,
  modelValue: {
    type: Object as PropType<Partial<UpdateSourcingJobContractCommand>>,
    required: false,
  },
});

const workingTimeType = ref<WorkingTime | undefined>(props.modelValue?.workingTimeType);
const modularWorkingTime = ref(props.modelValue?.modularWorkingTime || false);
const workingWeeklyTime = ref(props.modelValue?.workingWeeklyTime || 35);
const emit = defineEmits<(event: 'update:modelValue', value: Partial<UpdateSourcingJobContractCommand>) => void>();

const update = () => {
  emit('update:modelValue', {
    ...props.modelValue,
    workingTimeType: workingTimeType.value,
    modularWorkingTime: modularWorkingTime.value,
    workingWeeklyTime: workingWeeklyTime.value,
  });
};

</script>
