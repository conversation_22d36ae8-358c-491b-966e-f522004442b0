<template>
  <select-criteria
    :step="SourcingCriteriaStep.STEP1"
    :selectedCriteriaCodes="selectedCriteriaCodes"
    @update:criteriaSelection="updateCriteriaSelection"
  />
</template>

<script setup lang="ts">

import type { PropType } from 'vue';
import { computed, onActivated, ref } from 'vue';
import type { SourcingJobAndRecruitment } from 'erhgo-api-client';
import { SourcingCriteriaStep } from 'erhgo-api-client';
import { useApi } from 'src/config/api';
import { useCriteriaService } from 'src/models/CriteriaService';
import SelectCriteria from 'components/recruitment/SelectCriteria.vue';


const props = defineProps({
  recruitment: {type: Object as PropType<SourcingJobAndRecruitment>, required: true},
  step: {type: String as PropType<SourcingCriteriaStep>, required: true},
});
const {service: {isError: isSendError, api}} = useApi();

const submit = async () => {
  await api.updateCriteriaForSourcingJob(props.recruitment.jobId, props.step, selectedCriteriaCodes.value.filter(c => !!c));
  return !isError.value;
};
defineExpose({submit});

const {isError: isGetError, fetchCriteria} = useCriteriaService();

const selectedCriteriaCodes = ref<Array<string>>(props.recruitment.criteriaValues || []);


interface Emits {
  (e: 'updateCriteria', addedCriteria: string[], removedCriteria: string[]): void;
}

const emit = defineEmits<Emits>();

const isError = computed(() => {
  return isSendError.value || isGetError.value;
});

const updateCriteriaSelection = (addedCriteria: string[], removedCriteria: string[]) => {
  selectedCriteriaCodes.value = selectedCriteriaCodes.value.filter(c => !removedCriteria.includes(c));
  selectedCriteriaCodes.value = selectedCriteriaCodes.value.concat(addedCriteria);
  emit('updateCriteria', addedCriteria, removedCriteria);
};

onActivated(async () => await fetchCriteria(props.step));
</script>

<style scoped>

</style>
