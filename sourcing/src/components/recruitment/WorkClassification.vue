<template>
  <h1 class="custom-h1 q-my-sm">
    Qualification du poste (facultatif)
  </h1>
  <div class="row" v-if="erhgoClassifications">
    <h2 class="text-label q-pt-md q-my-none">Quels sont les domaines qui correspondent le mieux à votre recherche ?
      (vous pouvez en choisir jusque 4)</h2>
    <q-checkbox color="secondary"
                v-for="classification in erhgoClassifications"
                :key="classification.code"
                v-model="selectedQualifications"
                class="text-body1 q-pr-xl col-12 col-sm-6"
                :label="classification.title"
                :val="classification.code"
                :disable="selectedQualifications.length === 4 && !selectedQualifications.includes(classification.code)">
      <q-tooltip>
        {{ classification.description }}
      </q-tooltip>
    </q-checkbox>
  </div>
</template>

<script setup lang="ts">

import type {PropType} from 'vue';
import { ref} from 'vue';
import type {ErhgoClassification, SourcingJobAndRecruitment} from 'erhgo-api-client';
import {useApi} from 'src/config/api';


const props = defineProps({
  recruitment: {
    type: Object as PropType<SourcingJobAndRecruitment>,
    required: true,
  },
});
const {service: {isError, api}} = useApi();

const selectedQualifications = ref<Array<string>>(props.recruitment.erhgoClassifications.map(e => e.code) ?? []);

const submit = async () => {
  await api.updateErhgoClassificationsForSourcingRecruitment({
    recruitmentId: props.recruitment.recruitmentId,
    erhgoClassificationCodes: selectedQualifications.value,
  });
  return !isError.value;
};

defineExpose({submit});

const erhgoClassifications = ref<Array<ErhgoClassification>>((await api.listErhgoClassifications()).data);


</script>

<style scoped>

</style>
