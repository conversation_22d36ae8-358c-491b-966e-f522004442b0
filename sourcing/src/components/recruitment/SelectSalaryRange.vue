<template>
  <h2 class="text-label q-pt-md q-my-none">Salaire brut annuel</h2>
  <div class="row ">
    <label for="minSalary"
           class="text-body1 row items-center">Entre</label>
    <q-input v-model.number="baseSalary"
             @update:model-value="update"
             @blur="handleBlurOnSalariesInput"
             id="minSalary"
             placeholder="19000"
             class="q-px-md"
             type="number"
             min="1"
             step="100"
             :rules="[ val => !val || val <= 99999 || 'Veuillez préciser un salaire inférieur à 100 000 €']"
    >
      <template #append>
        <q-icon name="fa-solid  fa-euro-sign" size="xs"/>
      </template>
    </q-input>
    <label for="maxSalary" class="text-body1 row items-center">et</label>
    <q-input v-model.number="maxSalary"
             @update:model-value="update"
             @blur="handleBlurOnSalariesInput"
             id="maxSalary"
             placeholder="30000"
             class="q-pl-md"
             type="number"
             min="2"
             step="100"
             :rules="[ val => !val || (val > 0 && baseSalary && val >= baseSalary) || 'Veuillez préciser un salaire maximum, supérieur au salaire minimum', val => !val || val <= 99999 || 'Veuillez préciser un salaire inférieur à 100 000 €']"
    >
      <template #append>
        <q-icon name="fa-solid  fa-euro-sign" size="xs"/>
      </template>
    </q-input>
  </div>
</template>
<script setup lang="ts">
import type { PropType } from 'vue';
import { computed, defineEmits, ref } from 'vue';
import { Notify } from 'quasar';
import type { UpdateSourcingJobContractCommand } from 'erhgo-api-client';

const props = defineProps({
  submitted: Boolean,
  modelValue: {
    type: Object as PropType<Partial<UpdateSourcingJobContractCommand>>,
    required: false,
  },
});
const baseSalary = ref<number | undefined>(props.modelValue?.baseSalary);
const maxSalary = ref<number | undefined>(props.modelValue?.maxSalary);

const atLeastHasOneFloatingPoint = computed(() => {
  return !Number.isInteger(baseSalary.value) || !Number.isInteger(maxSalary.value);
});

const emit = defineEmits<(event: 'update:modelValue', value: Partial<UpdateSourcingJobContractCommand>) => void>();
const update = () => {
  emit('update:modelValue', { ...props.modelValue, maxSalary: maxSalary.value, baseSalary: baseSalary.value });
};
const handleBlurOnSalariesInput = () => {
  if (atLeastHasOneFloatingPoint.value || (baseSalary.value || 0) < 10000) {
    maxSalary.value = maxSalary.value ? Math.trunc(maxSalary.value) : undefined;
    baseSalary.value = baseSalary.value ? Math.trunc(baseSalary.value) : undefined;
    Notify.create({
      message: 'Souvenez-vous : nous demandons un salaire brut annuel. Nous ne tenons pas comptes des centimes',
      type: 'negative',
      position: 'bottom',
      classes: 'text-weight-bolder',
    });
  }
};

</script>
