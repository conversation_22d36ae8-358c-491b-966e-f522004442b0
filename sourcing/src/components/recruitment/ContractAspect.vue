<template>
  <q-form ref="form" greedy>
    <div>
      <h1 class="custom-h1 q-my-sm">
        Aspects contractuels (obligatoires)
      </h1>
      <select-type-contract
        :submitted="submitted"
        v-model="contract"
      />
      <select-working-time
        :submitted="submitted"
        v-model="contract"
      />
      <select-salary-range
        :submitted="submitted"
        v-model="contract"
      />
    </div>
  </q-form>
</template>

<script setup lang="ts">

import type { PropType } from 'vue';
import { ref, watchEffect } from 'vue';
import type { SourcingJobAndRecruitment, UpdateSourcingJobContractCommand } from 'erhgo-api-client';
import type { UserFilter } from 'src/models/UserFilter';
import { useApi } from 'src/config/api';
import SelectTypeContract from 'components/recruitment/SelectTypeContract.vue';
import SelectWorkingTime from 'components/recruitment/SelectWorkingTime.vue';
import SelectSalaryRange from 'components/recruitment/SelectSalaryRange.vue';

const {service: {isError, api}} = useApi();
type Emits = (e: 'updateUserFilter', userFilter: Partial<UserFilter>) => void;

const emit = defineEmits<Emits>();

const submitted = ref<boolean>(false);
const props = defineProps({
  recruitment: {type: Object as PropType<SourcingJobAndRecruitment>, required: true},
});

const contract = ref<Partial<UpdateSourcingJobContractCommand>>({
  maxSalary: props.recruitment.maxSalary,
  baseSalary: props.recruitment.baseSalary,
  workingTimeType: props.recruitment.workingTimeType,
  typeContractCategory: props.recruitment.typeContractCategory,
  workingWeeklyTime: props.recruitment.workingWeeklyTime || 35,
  modularWorkingTime: !!props.recruitment.modularWorkingTime,
});

const form = ref<{ validate: () => boolean }>();

const validate = () => form.value?.validate()
  && !!contract.value.workingTimeType
  && !!contract.value.typeContractCategory
  && !!contract.value.workingWeeklyTime
;

watchEffect(() => emit('updateUserFilter', {
  workingTimeType: contract.value.workingTimeType,
  typeContractCategory: contract.value.typeContractCategory,
  salaryMin: contract.value.baseSalary,
  salaryMax: contract.value.maxSalary,
}));

const submit = async () => {
  submitted.value = true;
  if (!validate()) {
    return false;
  }
  await api.updateSourcingJobContract(props.recruitment.jobId, contract.value as UpdateSourcingJobContractCommand);
  return !isError.value;
};

defineExpose({submit});


</script>
