<template>
  <div id="tableForm">
    <close-recruitment-dialog :recruitment="recruitmentToClose"
                              :model-value="true"
                              @update:modelValue="recruitmentToClose=null"
                              v-if="recruitmentToClose"/>
    <republished-dialog :recruitment="recruitmentToRepublish"
                        :model-value="true"
                        @update:modelValue="recruitmentToRepublish=null"
                        v-if="recruitmentToRepublish"
    />
    <div class="row q-py-md">
      <div class="col-6 col-md-4">
        <q-input
          v-model="searchQuery"
          placeholder="Rechercher une offre"
          hint="Recherche dans le titre et la localisation"
          :clearable="!!searchQuery"
          dense
        />
      </div>
      <div class="col-6 col-md-4 q-px-xl">
        <q-select
          v-model="selectedStates"
          multiple
          dense
          use-chips
          stack-label
          outlined
          :options="allStates.filter(c => !selectedStates.some(s => s.value === c.value))"
          :clearable="selectedStates.length > 0"
          @clear="selectedStates=[]"
          label="Filtrer par état"
        />
      </div>
      <div class="col-6 col-md-4">
        <q-checkbox
          color="secondary"
          v-model="onlyNewCandidatures"
          label="Uniquement avec de nouvelles candidatures"
        />
      </div>
    </div>
  </div>
  <div class="q-pa-md-sm">
    <q-table
      class="recruitments-table sticky-header-table"
      flat
      bordered
      separator="cell"
      :rows="filteredRows"
      :columns="columns"
      row-key="name"
      :loading="isLoading"
      no-data-label="Aucune offre trouvée"
      no-results-label="Aucune offre ne correspond à ce critère de recherche"
      :wrap-cells="true"
      v-model:pagination="pagination"
      :pagination-label="getPaginationLabel"
      :style="{height: `${computedTableHeight}px`}"
      :rows-per-page-options="[ 10, 20, 50, 0 ]"
    >
      <!-- Slot personnalisé pour l'en-tête du tableau -->
      <template v-slot:header="props">
        <q-tr>
          <q-th colspan="3" class="text-weight-bold">
            Dates
          </q-th>
          <q-th colspan="6" class="text-weight-bold">
            Caractéristiques
          </q-th>
          <q-th colspan="4" class="text-weight-bold">
            Nombres de candidatures
          </q-th>
          <q-th />
        </q-tr>
        <q-tr>
          <q-th v-for="col in props.cols" :key="col.name" :props="props">
            <span v-html="col.label" />
            <br />

            <q-icon v-if="col.name==='candidaturesGeneratedCount'"
                    name="fa-light fa-circle-question"
                    size="5">
              <q-tooltip>
                Nombres de candidatures correspondant particulièrement à votre offre et générées par #jenesuisPASunCV
              </q-tooltip>
            </q-icon>
            <q-icon v-if="col.name==='candidaturesFromUserCount'"
                    name="fa-light fa-circle-question"
                    size="5">
              <q-tooltip>
                Nombres de personnes ayant soumis une candidature à votre offre
              </q-tooltip>
            </q-icon>
          </q-th>
        </q-tr>
      </template>
      <template v-slot:loading>
        <q-inner-loading showing color="secondary"/>
      </template>
      <template v-slot:body-cell="props">
        <q-td :props="props"
              :class="{
               'violet-text': props.row.recruitmentState === RecruitmentState.DRAFT,
               'text-blue': props.row.recruitmentState === RecruitmentState.SELECTION,
               'text-red': props.row.recruitmentState === RecruitmentState.CLOSED,
               'cursor-pointer': props.row.recruitmentState !== RecruitmentState.DRAFT,
               'selected-cell bg-erhgo-grey': props.row.recruitmentId === selectedId,
        }"
              @click="props.row.recruitmentState !== RecruitmentState.DRAFT ? goToRecruitmentDetail(props.row) : null">
          {{ props.value }}
        </q-td>
      </template>
      <template v-slot:body-cell-actions="props">
        <q-td :props="props">
          <template v-if="props.row.recruitmentState === RecruitmentState.DRAFT">
            <q-btn
              title="Finaliser et publier cette offre"
              size="md"
              flat
              color="erhgo-violet1"
              :to="{name: 'recruitment', params: {recruitmentId: props.row.recruitmentId}}"
              icon="fa-light fa-pencil-alt"
              :disable="isActionLoading"
              class="text-dark-purple"
            />
            <q-btn
              title="Supprimer"
              size="sm"
              flat
              icon="fa-light fa-trash-can"
              outline
              color="red"
              @click="deleteRecruitment(props.row)"
              :loading="loadingDeleteId===props.row.recruitmentId"
              :disable="isActionLoading"
            />

          </template>
          <template v-else>

            <q-btn
              flat
              size="sm"
              :disable="isActionLoading"
              v-if="props.row.recruitmentState === RecruitmentState.PUBLISHED"
            >
              <share-buttons
                :recruitment="props.row"
                v-if="props.row.recruitmentState === RecruitmentState.PUBLISHED"
                :disable="isActionLoading"
              />
            </q-btn>
            <q-btn
              flat
              size="sm"
              title="Suspendre la diffusion"
              icon="fa-light fa-pause-circle"
              @click="props.row.changeState(RecruitmentState.SELECTION)"
              :loading="props.row.isLoading"
              :disable="isActionLoading"
              v-if="props.row.recruitmentState === RecruitmentState.PUBLISHED"
            >
              <q-tooltip>
                Une fois l'offre suspendue, vous ne recevez aucune nouvelle candidature.
              </q-tooltip>
            </q-btn>
            <q-btn
              flat
              size="sm"
              title="Reprendre la diffusion"
              icon="fa-light fa-circle-play"
              @click="republish(props.row)"
              :loading="props.row.isLoading"
              :disable="isActionLoading"
              v-if="props.row.recruitmentState === RecruitmentState.SELECTION"
            >
              <q-tooltip>
                Une fois la diffusion reprise, vous pourrez recevoir de nouvelles candidatures.
              </q-tooltip>
            </q-btn>
            <q-btn
              flat
              size="sm"
              title="Clôturer l'offre"
              icon="fa-light fa-stop-circle"
              @click="recruitmentToClose = props.row"
              :loading="props.row.isLoading"
              :disable="isActionLoading"
              v-if="props.row.recruitmentState === RecruitmentState.SELECTION"
            >
              <q-tooltip>
                Clôturez l'offre quand vous avez terminé le recrutement correspondant.
              </q-tooltip>
            </q-btn>
            <q-btn
              flat
              size="sm"
              title="Voir la liste des candidats"
              icon="fa-light fa-users"
              :loading="props.row.isLoading"
              :to="{name: 'recruitment-detail',
                      params: {
                        recruitmentId: props.row.recruitmentId,
                      },
                    }"
              :disable="isActionLoading"
            />
            <q-btn flat
                   size="sm"
                   title="Dupliquer l'offre"
                   :loading="isStateLoading"
                   @click="duplicateSourcingJobAndRecruitment(props.row)"
                   icon="fa-duotone fa-copy"
                   :disable="isActionLoading || isStateLoading"
            />
          </template>
        </q-td>
      </template>
    </q-table>
  </div>
</template>

<script setup lang="ts">

import type { PropType } from 'vue';
import { computed, getCurrentInstance, nextTick, onMounted, ref, watch } from 'vue';
import type {
  SourcingCandidatureCountRecruitment,
  SourcingJobAndRecruitment,
  SourcingRecruitmentItem,
} from 'erhgo-api-client';
import { RecruitmentState } from 'erhgo-api-client';
import moment from 'moment/moment';
import { useApi } from 'src/config/api';

import { useRoute, useRouter } from 'vue-router';
import type { QTableProps } from 'quasar';
import useStickyQTable from 'src/hooks/useStickyQTable';
import RepublishedDialog from 'components/recruitment/RepublishedDialog.vue';
import CloseRecruitmentDialog from 'components/recruitment/CloseRecruitmentDialog.vue';
import ShareButtons from 'components/ShareButtons.vue';
import type { RecruitmentService } from 'src/hooks/useRecruitement';

const router = useRouter();
const route = useRoute();
const instance = getCurrentInstance();
const formatSalary = instance.appContext.config.globalProperties.$filters.formatSalary;
const t: (a: string) => string = instance.appContext.app.config.globalProperties.$t;
const {service: {api, isLoading: isStateLoading}} = useApi();
const isActionLoading = ref(false);



const searchQuery = ref('');
const onlyNewCandidatures = ref(false);
const allStates = [
  RecruitmentState.DRAFT,
  RecruitmentState.PUBLISHED,
  RecruitmentState.SELECTION,
  RecruitmentState.CLOSED,
].map(value => ({label: t(`recruitmentState.${value}`), value}));
const selectedStates = ref<{
  label: string;
  value: RecruitmentState
}[]>([]);

const props = defineProps({
  recruitments: { type: Object as PropType<RecruitmentService<SourcingRecruitmentItem>[]>, required: true },
  isLoading: Boolean,
});

type Emits = (e: 'deleteRecruitment', recruitmentId: number) => void;

const emit = defineEmits<Emits>();

const formatMinMaxSalary = (row: {
  baseSalary?: number,
  maxSalary?: number
}) =>
  [row.baseSalary, row.maxSalary].filter(a => !!a).map(a => `${formatSalary(a)}`).join(' - ');

const countAllCandidatures = (candidaturesCount: SourcingCandidatureCountRecruitment) => {
  return candidaturesCount.totalCandidatureCount.fromUser + candidaturesCount.totalCandidatureCount.generated;
};

const recruitmentToClose = ref();
const recruitmentToRepublish = ref();
const loadingDeleteId = ref<number>();

const columns: QTableProps['columns'] = [
  {
    name: 'lastPublicationDate',
    align: 'left',
    label: 'Dernière Publication',
    field: (row: RecruitmentService<SourcingRecruitmentItem>) => {
      if (row.recruitmentState === 'DRAFT') return 'Non publiée';
      return row.publicationDate ? moment(row.publicationDate).format('DD/MM/YY') : 'Inconnue';
    },
    sortable: true,
    sort: (_a: unknown, _b: unknown, rowA: SourcingRecruitmentItem, rowB: SourcingRecruitmentItem) => {
      if (!rowA.publicationDate) return -1;
      if (!rowB.publicationDate) return 1;
      return moment(rowA.publicationDate).diff(moment(rowB.publicationDate));
    },
  },
  {
    name: 'lastProcessingDate',
    align: 'left',
    label: 'Dernière activité<br/>(type)',
    field: (row: RecruitmentService<SourcingRecruitmentItem>) => {
      if (row.recruitmentState === 'DRAFT') return 'Non publiée';
      const dateLabel = row.lastProcessingDate ? moment(row.lastProcessingDate).format('DD/MM/YY') : 'Inconnue';
      const typeLabel = row.lastProcessingType && row.lastProcessingDate ? `(${t(`processingType.${row.lastProcessingType}`)})` : '';
      return `${dateLabel} ${typeLabel}`;
    },
    sortable: true,
    sort: (_a: unknown, _b: unknown, rowA: SourcingRecruitmentItem, rowB: SourcingRecruitmentItem) => {
      if (!rowA.lastProcessingDate) return -1;
      if (!rowB.lastProcessingDate) return 1;
      return moment(rowA.lastProcessingDate).diff(moment(rowB.lastProcessingDate));
    },
  },
  {
    name: 'remainingTime',
    align: 'left',
    label: 'Jours restants',
    field: (row: RecruitmentService<SourcingRecruitmentItem>) => {
      if (row.recruitmentState !== 'PUBLISHED') return 'Non publiée';
      if (!row.publicationEndDate) return 'Inconnu';
      const nbDays = moment(row.publicationEndDate).diff(moment(), 'days');
      return nbDays < 0 ? 0 : nbDays;
    },
    sortable: true,
  },
  { name: 'offerManager', align: 'left', label: 'Responsable', field: row => row.manager?.fullname, sortable: true },
  { name: 'title', align: 'left', label: 'Poste', field: 'title', sortable: true },
  {
    name: 'city',
    align: 'left',
    label: 'Localisation',
    field: (row: RecruitmentService<SourcingRecruitmentItem>) => row.location?.city,
    sortable: true,
  },
  {name: 'salary', align: 'left', label: 'Salaire', field: formatMinMaxSalary, sortable: true},
  {
    name: 'recruitmentState',
    align: 'left',
    label: 'État',
    field: (row: RecruitmentService<SourcingRecruitmentItem>) => t(`recruitmentState.${row.recruitmentState}`),
    sortable: true,
  },
  {
    name: 'persNotified',
    align: 'left',
    label: 'Personnes<br/>Notifiées',
    field: row => row.notifiedUsersCount,
    sortable: true,
  },
  {
    name: 'totalCandidatureCount',
    align: 'left',
    label: 'Total',
    field: (row: RecruitmentService<SourcingRecruitmentItem>) => countAllCandidatures(row.candidaturesCount),
    sortable: true,
    sort: (_a: unknown, _b: unknown, rowA: RecruitmentService<SourcingRecruitmentItem>, rowB: RecruitmentService<SourcingRecruitmentItem>) => {
      const sumA = countAllCandidatures(rowA.candidaturesCount);
      const sumB = countAllCandidatures(rowB.candidaturesCount);
      return sumA - sumB;
    },
  },
  {
    name: 'candidaturesFromUserCount',
    align: 'left',
    label: 'Sur offre<br/>(dont nouvelles)',
    field: (row: RecruitmentService<SourcingRecruitmentItem>) => `${(row.candidaturesCount.totalCandidatureCount.fromUser)} (${row.candidaturesCount.newCandidatureCount.fromUser})`,
    sortable: true,
    sort: (_a: unknown, _b: unknown, rowA: RecruitmentService<SourcingRecruitmentItem>, rowB: RecruitmentService<SourcingRecruitmentItem>) => {
      return rowA.candidaturesCount.totalCandidatureCount.fromUser - rowB.candidaturesCount.totalCandidatureCount.fromUser;
    },
  },
  {
    name: 'candidaturesGeneratedCount',
    align: 'left',
    label: 'Proposées<br/>(dont nouvelles)',
    field: (row: RecruitmentService<SourcingRecruitmentItem>) => `${(row.candidaturesCount.totalCandidatureCount.generated)} (${row.candidaturesCount.newCandidatureCount.generated})`,
    sortable: true,
    sort: (_a: unknown, _b: unknown, rowA: RecruitmentService<SourcingRecruitmentItem>, rowB: RecruitmentService<SourcingRecruitmentItem>) => {
      return rowA.candidaturesCount.totalCandidatureCount.generated - rowB.candidaturesCount.totalCandidatureCount.generated;
    },
  },
  {
    name: 'UntreatedCandidatures',
    align: 'left',
    label: 'Non traitées',
    field: (row: RecruitmentService<SourcingRecruitmentItem>) =>
      (row.candidaturesCount.newCandidatureCount.fromUser + row.candidaturesCount.newCandidatureCount.generated) + row.candidaturesCount.toContactCandidatureCount + row.candidaturesCount.contactedCandidatureCount,
    sortable: true,
  },
  {name: 'actions', align: 'center', label: 'Actions', field: 'actions'},
];

const filteredRows = computed(() => {
  return props.recruitments.filter(recruitment => {
    if (onlyNewCandidatures.value && !recruitment.candidaturesCount.newCandidatureCount.fromUser && !recruitment.candidaturesCount.newCandidatureCount.generated) {
      return false;
    }
    let includes = true;
    if (searchQuery.value) {
      const searchLowercase = searchQuery.value.toLowerCase();
      includes = includes &&
        (recruitment.title?.toLowerCase().includes(searchLowercase) ||
          !!recruitment.location?.city?.toLowerCase().includes(searchLowercase))
      ;
    }
    return includes && (!selectedStates.value.length || selectedStates.value.some(s => s.value === recruitment.recruitmentState));
  });
});

const getPaginationLabel = (firstRowIndex: number, endRowIndex: number, totalRowsNumber: number) => {
  return `${firstRowIndex}-${endRowIndex} sur ${totalRowsNumber}`;
};

const pagination = ref({
  sortBy: 'lastPublicationDate',
  descending: true,
  page: 1,
  rowsPerPage: 20,
});
const selectedId = ref<number>();

const updateSelectedRow = async ({ recruitmentId }: RecruitmentService<SourcingRecruitmentItem>) => {
  selectedId.value = recruitmentId;
  await updateRouteQuery();
};

const updateRouteQuery = async () => {
  const { rowsPerPage, page, descending, sortBy } = pagination.value;
  await router.replace({
    query: {
      ...router.currentRoute.value.query,
      sortBy,
      page,
      rowsPerPage,
      descending: `${descending}`,
      selectedId: selectedId.value,
      selectedStates: selectedStates.value.map(s => s.value).join(','),
      searchQuery: searchQuery.value,
      onlyNewCandidatures: `${onlyNewCandidatures.value}`,
    },
  }).catch();
};

const getPaginationFromRoute = () => {
  const query = router.currentRoute.value.query;
  return {
    sortBy: query?.sortBy as string || pagination.value.sortBy,
    descending: query?.descending !== 'false',
    page: query?.page ? parseInt(query.page as string, 10) : pagination.value.page,
    rowsPerPage: query?.rowsPerPage ? parseInt(query.rowsPerPage as string, 10) : pagination.value.rowsPerPage,
  };
};

onMounted(() => {
  pagination.value = getPaginationFromRoute();
  if (route.query.selectedId) {
    selectedId.value = parseInt(route.query.selectedId as string, 10);
    void nextTick(() => {
      if (selectedId.value) {
        const selectedElement = document.querySelector(`.selected-cell`);
        if (selectedElement) {
          selectedElement.scrollIntoView({ block: 'center' });
        }
      }
    });
  }
  selectedStates.value = route.query?.selectedStates ? (route.query.selectedStates as string).split(',').map(s => allStates.find(a => a.value === s)) : [];
  searchQuery.value = route.query?.searchQuery ? route.query.searchQuery as string : '';
  onlyNewCandidatures.value = route.query?.onlyNewCandidatures ? route.query.onlyNewCandidatures as string === 'true' : false;
});

watch([pagination, searchQuery, selectedStates, onlyNewCandidatures], () => updateRouteQuery(), { deep: true });

const goToRecruitmentDetail = async (row: RecruitmentService<SourcingRecruitmentItem>) => {
  await updateSelectedRow(row);
  const redirect = {
    name: 'recruitment-detail',
    params: {
      recruitmentId: row.recruitmentId,
    },
  };
  await router.push(redirect);
};

const duplicateSourcingJobAndRecruitment = async (row: RecruitmentService<SourcingRecruitmentItem>) => {
  if (isActionLoading.value) return;
  isActionLoading.value = true;
  try {
    const recruitmentDuplicated = ref<SourcingJobAndRecruitment>((await api.duplicateSourcingJobAndRecruitment({ recruitmentId: row.recruitmentId })).data);
    const redirect = {
      name: 'recruitment',
      params: {
        recruitmentId: recruitmentDuplicated.value.recruitmentId,
      },
    };
    await router.push(redirect);
  } finally {
    isActionLoading.value = false;
  }
};

const {tableHeight: computedTableHeight} = useStickyQTable();

const deleteRecruitment = async (row: RecruitmentService<SourcingRecruitmentItem>) => {
  if (isActionLoading.value) return;
  isActionLoading.value = true;
  loadingDeleteId.value = row.recruitmentId;
  try {
    await api.deleteJob(row.jobId);
    emit('deleteRecruitment', row.recruitmentId);
  } finally {
    loadingDeleteId.value = undefined;
    isActionLoading.value = false;
  }
};

const republish = async (row: RecruitmentService<SourcingRecruitmentItem>) => {
  if (isActionLoading.value) return;
  isActionLoading.value = true;
  try {
    await row.republish();
    if (row.showRepublishDialog) {
      recruitmentToRepublish.value = row;
    }
  } finally {
    isActionLoading.value = false;
  }
};

</script>

<style lang="scss">
.recruitments-table {
  .q-table__top,
  .q-table__bottom,
  thead tr th {
    background-color: $secondary;
  }

  thead tr th, .q-td {
    padding-left: 5px !important;
    padding-right: 5px !important;
  }

  thead tr:first-child th {
    font-weight: bold;
    padding: 0 !important;
    line-height: 1 !important;
  }

  thead tr:first-child {
    height: 40px !important;
  }
}
</style>

