<template>
  <q-dialog v-model="showDialog" :persistent="recruitment.isLoading" :maximized="$q.screen.lt.sm">
    <q-card class="text-center q-pa-sm-lg">
      <div class="q-mt-none row items-justify">
        <span class="col q-mt-sm  text-weight-bold text-justify text-h6 q-mb-lg">Reprendre la diffusion pour&nbsp;: {{
            recruitment.title
          }}</span>
        <q-space/>
        <q-icon name="fa-light fa-close cursor-pointer q-ml-md q-mt-md" size="md" @click="showDialog = false"/>
      </div>
      <div class="text-justify q-mb-md">
        Une fois la diffusion reprise, vous pourrez recevoir de nouvelles candidatures.
      </div>
      <div class="user-text text-left q-mb-md q-pa-md custom-radio"
           v-if="recruitment.newUsersToNotifyCount || recruitment.oldUsersToNotifyCount">
        Vous avez
        {{ recruitment.oldUsersToNotifyCount && recruitment.newUsersToNotifyCount ? "trois" : "deux" }}
        options pour indiquer quelles personnes notifier&nbsp;:

        <q-radio v-model="notificationType" val="NONE" class="q-pt-md">
          <div class="text-weight-bold">Aucune</div>
          <span class="text-caption">Ne pas envoyer de notification</span>
        </q-radio>
        <q-radio v-model="notificationType" val="NEW" class="q-pt-md" v-if="recruitment.newUsersToNotifyCount">
          <div class="text-weight-bold">
            Nouvelles seulement
          </div>
          <span class="text-caption">
            Envoyer une notification {{ recruitment.oldUsersToNotifyCount ? "uniquement" : "" }}
            aux {{ recruitment.newUsersToNotifyCount }} nouvelles personnes inscrites sur #jenesuisPASunCV et correspondant à votre offre
          </span>
        </q-radio>
        <q-radio v-model="notificationType" val="ALL" class="q-pt-md" v-if="recruitment.oldUsersToNotifyCount">
          <div class="text-weight-bold">
            Toutes
          </div>
          <span class="text-caption">
            Informer {{ recruitment.newUsersToNotifyCount ? "aussi" : "" }}
            à nouveau les personnes déjà
            notifiées lors de la précédente publication ({{ recruitment.oldUsersToNotifyCount }} personnes au total).
            </span>
        </q-radio>
      </div>
      <div class="q-pt-sm text-left">
        Il vous est également possible de modifier l'intitulé de l'offre, visible des personnes souhaitant candidater&nbsp;:
        <q-input label="Titre de votre offre"
                 :hint="wantPersonalizedTitle ? '':'Modifiez en cliquant sur le crayon'"
                 :disable="!wantPersonalizedTitle"
                 :rules="[val => val && val.length <= 230 || 'Le Libellé personnalisé ne doit pas dépasser 230 caractères.']"
                 dense
                 class="q-pt-sm"
                 v-model="title">
          <template v-slot:after>
            <q-btn
              no-caps
              flat
              @click="wantPersonalizedTitle = !wantPersonalizedTitle"
            >
              <q-icon class="q-ml-md q-mt-sm fas fa-close" size="sm" v-if="wantPersonalizedTitle" />
              <q-icon class="q-ml-md q-mt-sm fas fa-pen" size="xs" v-else />
            </q-btn>
          </template>
        </q-input>
      </div>
      <div class="row justify-between q-pt-lg q-col-gutter-x-md">
        <div class="col-4">
          <q-btn
            :loading="recruitment.isLoading"
            outline
            class="full-width bg-secondary q-pa-xs col-6"
            :disable="!notificationType || !!title && title.length > 230"
            no-caps
            @click="recruitment.changeState(RecruitmentState.PUBLISHED, notificationType, title)"
          >
            <div class="q-mr-md-lg">
              <q-icon name="fa-light fa-check" size="xs"/>
            </div>
            Confirmer
          </q-btn>
        </div>
        <div class="col-4">
          <q-btn
            class="full-width bg-red-5 q-pa-xs col-6"
            no-caps
            @click="showDialog=false"
          >
            <div class="q-mr-md-lg">
              <q-icon name="fa-light fa-xmark" size="xs"/>
            </div>
            Annuler
          </q-btn>
        </div>
      </div>
    </q-card>
  </q-dialog>
</template>
<script setup lang="ts">

import type { PropType} from 'vue';
import { ref, watch, watchEffect } from 'vue';
import type { CommonSourcingRecruitmentInfos, UsersToNotifySelectionType } from 'erhgo-api-client';
import { RecruitmentState } from 'erhgo-api-client';
import type { RecruitmentService } from 'src/hooks/useRecruitement';

const props = defineProps({
  modelValue: {type: Boolean, required: true},
  recruitment: { type: Object as PropType<RecruitmentService<CommonSourcingRecruitmentInfos>>, required: true },
});

const showDialog = ref(props.modelValue);
watchEffect(() => showDialog.value = props.modelValue);
type Emits = (e: 'update:modelValue', input: boolean) => void;

const emit = defineEmits<Emits>();
watch(() => showDialog.value, newValue => {
  if (props.modelValue !== newValue) {
    emit('update:modelValue', newValue);
  }
});

watch(() => props.recruitment.showRepublishDialog, newValue => {
  if (props.modelValue !== newValue) {
    emit('update:modelValue', newValue);
  }
});

const title = ref(props.recruitment.title);
const notificationType = ref<UsersToNotifySelectionType>();
const wantPersonalizedTitle = ref(false);
</script>
<style>
.custom-radio .q-radio__inner {
  color: unset !important;
}
</style>
