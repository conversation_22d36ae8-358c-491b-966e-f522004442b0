<template>
    <q-table
      :rows="suggestingMap"
      :columns="columns"
      flat
      bordered
      dense
      :hide-bottom="!!suggestingMap.length"
      hide-header
      no-data-label="Aucune information"
      :rows-per-page-options="[0]"
    >
      <template v-slot:body="props">
        <q-tr
          :props="props"
          :class="nonClickable ? '' : 'clickable'"
          @click="nonClickable ? '':onRowClick(props.row)"
        >

          <q-td v-for="col in props.cols" :key="col.name" :props="props">
            <span v-html="col.value"></span>
          </q-td>
        </q-tr>
      </template>
    </q-table>
</template>
<script setup lang="ts">

import { ref } from 'vue';
import { diffWordsWithSpace } from 'diff';

const computeDiff = (originalText: string | undefined, updatedText: string | undefined) => {
  if (!updatedText || !originalText) return '';
  const diffResult = diffWordsWithSpace(originalText, updatedText);
  if (diffResult.some(p => p.added || p.removed)) {
    const diff = diffResult.map((part: {
      added: boolean,
      removed: boolean,
      value: string
    }) => {
      const color = part.added ? 'green' :
        part.removed ? 'red' : 'grey';
      const spanStyle = `color: ${color} !important;`;
      return `<strong style="${spanStyle}">${part.value}</strong>`;
    });
    return diff.join('');
  }
  return '';
};
const props = defineProps({
  suggestionMap: { type: Object, required: false },
  previousMap: { type: Object, required: false },
  nonClickable: { default: false },
  onlyDiff: { default: false },
});

const columns = [
  {
    name: 'key',
    align: 'left',
    field: 'key',
    sortable: false,
  },
  {
    name: 'value',
    align: 'left',
    field: 'value',
    sortable: false,
  },
  {
    name: 'diff',
    align: 'left',
    field: 'diff',
    sortable: false,
  },
];

interface Emits {
  (e: 'rowClick', value: string): void,

  (e: 'diff', value: string): void,
}

const emit = defineEmits<Emits>();

const onRowClick = (row) => emit('rowClick', row.value);
const suggestingMap = ref<{ key: string, value: string, diff: string }[]>([]);

suggestingMap.value = Object.keys(props.suggestionMap || {})
  .map((key) => {
    const value = props.suggestionMap[key];
    let diff;
    if (props.previousMap?.[key]) {
      diff = computeDiff(props.previousMap?.[key], value);
      if (diff) {
        emit('diff', key);
      }
    } else {
      diff = '[donnée ajoutée]';
    }
    return ({
      key: props.onlyDiff ? '' : key,
      value: props.onlyDiff ? '' : value,
      diff,
    });
  });

if (props.previousMap && props.suggestionMap) {
  Object.keys(props.previousMap).filter(key => !Object.keys(props.suggestionMap).includes(key)).forEach(key => emit('diff', `${key} [donnée supprimée]`));
}

</script>
<style>
.clickable {
  cursor: pointer;
}
</style>
