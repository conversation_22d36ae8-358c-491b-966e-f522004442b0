<template>
  <q-dialog v-model="showDialog" @hide="closeDialog" :persistent="loadingUpdate">
    <q-card class="text-center q-pa-sm-lg">
      <div class="q-mt-none row items-justify">
        <h6 class="col q-mt-none text-weight-bold text-justify  text-h6 q-mb-none">
          {{ withManager ? "Responsable et p" : "P" }}ersonnes notifiées
        </h6>
        <q-icon name="fa-light fa-close cursor-pointer q-ml-md" size="md" @click="closeDialog" />
      </div>
      <users-select
        :is-success="isSuccess"
        :loading-update="loadingUpdate"
        :selected-users-ids="localUsersIds"
        @update:selected-users="updateSelectedUsers"
        @update:manager-user="updateManagerUser"
        :manager-user-id="managerUserId"
        :with-manager="withManager"
      />
      <q-btn
        @click="closeDialog"
        :loading="loadingUpdate"
        size="sm"
        class="bg-grey-1 text-weight-bold q-mt-sm"
        outline
      >
        <q-icon name="fa-light fa-close cursor-pointer" color="black" size="xs" />
        Fermer
      </q-btn>
    </q-card>
  </q-dialog>
</template>
<script setup lang="ts">

import { computed, ref, watch } from 'vue';
import UsersSelect from 'components/UsersSelect.vue';
import { useApi } from 'src/config/api';
import type { SourcingUser } from 'erhgo-api-client';

const props = defineProps({
  modelValue: {type: Boolean, required: true},
  selectedUsersIds: { type: Array<string>, required: false },
  managerUserId: { type: String, required: false },
  recruitmentId: { type: Number, required: false },
});

const withManager = computed(() => !!props.recruitmentId);
const showDialog = ref(props.modelValue);
const localUsersIds = ref(props.selectedUsersIds);
const { service: { api: updateApi, isLoading: loadingUpdate, isSuccess } } = useApi();
const updateSelectedUsers = async (updatedUsersToNotify: SourcingUser[]) => {
  localUsersIds.value = updatedUsersToNotify.map(user => user.id);
  await updateApi.updateUsersToNotify({
    recruitmentId: props.recruitmentId,
    usersIdsToNotify: localUsersIds.value,
  });
};

const updateManagerUser = async ({ id: managerUserId }: SourcingUser) => {
  await updateApi.updateManager(
    {
      recruitmentId: props.recruitmentId,
      managerUserId,
    },
  );
};

type Emits = (e: 'update:modelValue', input: boolean) => void;
const emit = defineEmits<Emits>();

const closeDialog = () => {
  showDialog.value = false;
  emit('update:modelValue', false);
};

watch(() => props.modelValue, (newValue) => {
  showDialog.value = newValue;
});

</script>

