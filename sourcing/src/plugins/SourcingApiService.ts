import { DefaultApi } from 'erhgo-api-client';
import { baseUrl } from 'src/config/base-url';
import type { AxiosInstance, AxiosRequestConfig } from 'axios';
import axios from 'axios';
import authentication from 'src/config/authentication';
import { ref } from 'vue';
import logErrorToServer from 'src/config/ErrorHandlerService';
import useGlobalError from 'src/hooks/useGlobalError';

export type LogToServerErrorHandler = (error: unknown) => boolean;

const RESET_SUCCESS_FLAG_DELAY = 7000;
export default class SourcingApiService {
    private readonly _isHandlingLogToServer: LogToServerErrorHandler;
    private readonly _basePath: string;
    private readonly _axios: AxiosInstance;
    private readonly _api: DefaultApi;
    private readonly _isError = ref(false);
    private readonly _isLoading = ref(false);
    private readonly _isSuccess = ref(false);
    private readonly _withAuthenticationHeader: boolean;
    private static appVersion: string;
    private static device: string;

    public get isError() {
        return this._isError;
    }

    public get isLoading() {
        return this._isLoading;
    }

    public get isSuccess() {
        return this._isSuccess;
    }

    public get api() {
        return this._api;
    }

    public get axios() {
        return this._axios;
    }

    constructor(customErrorHandler?: LogToServerErrorHandler, withAuthenticationHeader = true) {
        this._withAuthenticationHeader = withAuthenticationHeader;
        this._basePath = SourcingApiService.generateBashPath();
        this._isHandlingLogToServer = customErrorHandler ?? SourcingApiService.defaultErrorHandler;
        this._axios = axios.create({baseURL: baseUrl('api')});
        this._api = new DefaultApi(undefined, this._basePath, this._axios);
        this.initializeInterceptors();
    }

    private initializeInterceptors() {
        this._axios.interceptors.request.use(async (request) => {
              this._isError.value = false;
              this._isSuccess.value = false;
              useGlobalError.globalError.value = false;
              this._isLoading.value = true;
              if (this._withAuthenticationHeader) {
                  await this.addHeaders(request);
                  await this.addAuthenticationHeader(request);
              }
              return request;
          }, async (error) => {
              await this.handleReject(error);
          },
        );

        this._axios.interceptors.response.use(response => {
            this._isLoading.value = false;
            this._isSuccess.value = true;
            setTimeout(() => this._isSuccess.value = false, RESET_SUCCESS_FLAG_DELAY);
            return response;
        }, async (error) => {
            await this.handleReject(error);
        });
    }

    private isAndroid() {
        return /Android/i.test(navigator.userAgent);
    }

    private isIOs() {
        return /iPhone|iPad|iPod/i.test(navigator.userAgent);
    }

    private async addHeaders(request: AxiosRequestConfig) {
        if (!SourcingApiService.appVersion) {
            try {
                SourcingApiService.appVersion = (await axios.get(`${window.location.origin}/version`)).data || 'unavailable';
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
            } catch (e) {
                SourcingApiService.appVersion = 'unknown';
            }
        }
        request.headers = {
            ...(request.headers || {}),
            'X-App-Version': SourcingApiService.appVersion,
            'X-Device': this.isAndroid() ? 'Android' : this.isIOs() ? 'Apple' : 'Desktop',
            'X-Resolution': `${window.innerWidth}x${window.innerHeight}`,
        };
    }

    private async addAuthenticationHeader(request: AxiosRequestConfig) {
        if (authentication.authenticated) {
            request.headers = {
                ...request.headers,
                Authorization: `Bearer ${await authentication.getToken()}`,
                'X-Realm': 'sourcing.erhgo.app',
            };
        }
    }

    private async handleReject(error: unknown) {
        this._isLoading.value = false;
        if (this._isHandlingLogToServer(error)) {
            this._isError.value = true;
            useGlobalError.globalError.value = true;
            const rawApi = SourcingApiService.createBasicDefaultApi();
            await logErrorToServer(rawApi, error instanceof Error ? error : new Error(JSON.stringify(error)));
        }
        return Promise.reject(error as Error);
    }

    private static defaultErrorHandler(this: void, error: unknown) {
        console.error('error during request', error);
        return true;
    }

    public static createBasicDefaultApi() {
        const rawAxios = axios.create({baseURL: baseUrl('api')});
        return new DefaultApi(undefined, this.generateBashPath(), rawAxios);
    }

    private static generateBashPath() {
        let basePath = baseUrl('api');
        if (basePath.endsWith('/')) {
            basePath = basePath.substring(0, basePath.length - 1);
        }
        basePath += '/api/odas';
        return basePath;
    }
}
