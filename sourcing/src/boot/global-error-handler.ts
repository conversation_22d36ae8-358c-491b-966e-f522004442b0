import { boot } from 'quasar/wrappers';
import type { AxiosError } from 'axios';
import logErrorToServer from 'src/config/ErrorHandlerService';
import SourcingApiService from 'src/plugins/SourcingApiService';

export default boot(({app}) => {
  window.onerror = async (message, url, linenumber) => {
    await logErrorToServer(SourcingApiService.createBasicDefaultApi(), new Error(`${JSON.stringify(message)} "at line ${linenumber}" (${url})`));
  };

  app.config.errorHandler = (err, vm, info) => {
    if (!(err as AxiosError).isAxiosError) {
      // Axios error are handle through SourcingApiService
      console.error(err, info);
      logErrorToServer(SourcingApiService.createBasicDefaultApi(), err instanceof Error ? err : new Error(JSON.stringify(err)));
    }
  };
});
