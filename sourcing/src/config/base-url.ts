export const getEnv = (): string => {
  const url = window.location.host.split('.');
  let env = url[0].split('-')[0];
  if (env === 'entreprise' || env === 'jerecrute') {
    env = 'master';
  }
  return env;
};

export const baseUrl = (target: string) => {
  // target : fo, api, auth
  const env = getEnv();
  const protocol = window.location.protocol;
  const isLocalhost = env.startsWith('localhost');
  const isE2e = window.location.host.includes('e2e');
  const domain = 'jenesuispasuncv.fr';

  const isApi = target === 'api';
  const isAuth = target === 'auth';
  const isFo = target === 'fo';

  let redirect;

  if (isE2e) {
    if (isAuth) {
      redirect = 'keycloak-e2e:8080';
    } else if (isApi) {
      redirect = 'api-e2e:8080';
    } else if (isFo) {
      redirect = 'front-office-e2e:8080';
    }
  } else if (env === 'master') {
    if (isAuth) {
      redirect = `auth.${domain}`;
    } else if (isApi) {
      redirect = `api.${domain}`;
    } else if (isFo) {
      redirect = `app.${domain}`;
    }
  } else if (isLocalhost) {
    if (isAuth) {
      redirect = 'auth.localhost';
    } else if (isFo) {
      redirect = 'localhost:53323';
    } else if (isApi) {
      redirect = 'localhost:8080';
    }
  } else if (isFo) {
    redirect = `${env}.${domain}`;
  } else {
    redirect = `${env}-${target}.${domain}`;
  }

  const forced = (isApi && process.env.VUE_APP_API_URL) || (isAuth && process.env.VUE_APP_AUTH_URL);
  if (forced) {
    return forced;
  }
  return `${protocol}//${redirect}`;

};
