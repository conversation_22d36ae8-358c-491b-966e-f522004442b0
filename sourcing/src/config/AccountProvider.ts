import type { DefaultApi, SourcingSubscription} from 'erhgo-api-client';
import { SourcingSubscriptionType } from 'erhgo-api-client';
import { useApi } from 'src/config/api';
import authentication from 'src/config/authentication';
import type { Ref } from 'vue';
import { ref } from 'vue';
import type {AxiosError} from 'axios';

class AccountProvider {
  private _account: Ref<SourcingSubscription | null> = ref(null);

  private readonly _accountPromise: Ref<Promise<unknown>>;

  static instance = new AccountProvider();
  private _api: DefaultApi;
  private _isLoading: Ref<boolean>;
  private _isError: Ref<boolean>;
  private _isSiretConflict = ref(false);
  constructor() {
    const {service: {api, isLoading, isError}} = useApi();
    this._api = api;
    this._isLoading = ref(isLoading);
    this._isError = ref(isError);
    this._accountPromise = ref(authentication.initialized
      .then(async () => {
        if (authentication.authenticated) {
          return await api.getSourcingSubscription()
            .then(async request => {
              if (request.data.subscriptionType === SourcingSubscriptionType.NONE) {
                try {
                  await api.initializeSourcingAccount();
                } catch (e) {
                  if ((e as AxiosError)?.response?.status === 409) {
                    this._isSiretConflict.value = true;
                    return;
                  }
                  throw e;
                }
                await this.refresh();
                await authentication.refresh();
              } else {
                this._account.value = request.data;
              }
            })
            ;
        } else {
          return Promise.resolve(null);
        }
      }));
  }

  async refresh() {
    this._account.value = (await this._api.getSourcingSubscription()).data;
  }

  get account() {
    return this._account;
  }

  get accountPromise() {
    return this._accountPromise;
  }

  get isTrial() {
    return this._account?.value?.subscriptionType === SourcingSubscriptionType.TRIAL;
  }

  get isLoading() {
    return this._isLoading;
  }

  get isError() {
    return this._isError;
  }

  get isSiretConflict() {
    return this._isSiretConflict;
  }

}

export default AccountProvider.instance;
