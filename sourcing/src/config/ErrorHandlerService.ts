import { baseUrl } from './base-url';
import type { AxiosError } from 'axios';
import type { DefaultApi } from 'erhgo-api-client';


const rawSend = (message: unknown) => {
  const url = `${baseUrl('api')}/api/odas/public/log`;
  const xhr = new XMLHttpRequest();
  xhr.open('POST', url);
  xhr.setRequestHeader('Content-Type', 'application/json');
  xhr.send(JSON.stringify(message));
};

export default async (api: DefaultApi, err: Error, errorContext?: string) => {
  try {
    let message;
    if (err) {
      console.error(err);
      message = (err.stack || err);
      if (Object.prototype.hasOwnProperty.call(err, 'config')) {
        message = (err as AxiosError).config?.method?.toUpperCase() + ' ' + (err as AxiosError).config?.url + ' => ' + message;
      }
      if (typeof message !== 'string') {
        message = JSON.stringify(message);
      }
      if (!err.stack) {
        message += ` (stack: ${new Error().stack}) `;
      }
      if (err.message) {
        message += ` --- ${err.message}`;
      }
    } else {
      message = 'Unknown error';
      console.error(message);
    }
    if (errorContext) {
      message += ` --- error context: ${errorContext}`;
    }
    const logCommand = {message: `${message}, location: ${window.location.href}`};
    if (api) {
      await api.logEvent(logCommand);
    } else {
      rawSend(logCommand);
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (e) {
    console.error('Unable to log error');
  }
};

