import type { KeycloakProfile } from 'keycloak-js';
import Keycloak from 'keycloak-js';
import { baseUrl } from 'src/config/base-url';

interface ErhgoKeycloakProfile extends KeycloakProfile {
  attributes?: { [key: string]: string[] };
}

class Authentication {

  _keycloak: Keycloak.KeycloakInstance;
  _initialized: Promise<boolean | void>;
  private static _initOptions = {
    realm: 'sourcing.erhgo.app',
    url: baseUrl('auth'),
    clientId: 'web-sourcing',
    sslRequired: false,
  };
  static instance = new Authentication();

  constructor() {
    this._keycloak = new Keycloak(Authentication._initOptions);
    this._initialized = this.bootstrap();
  }

  private async bootstrap(): Promise<boolean | void> {
    const initialized = this._keycloak.init({
      onLoad: 'check-sso',
      checkLoginIframe: false,
      silentCheckSsoRedirectUri: undefined,
      responseMode: 'query',
    }).catch(e => {
      console.error('Authentication Failed', e);
    });

    this.initialized = initialized;
    return initialized;
  }

  set initialized(a) {
    this._initialized = a;
  }

  get initialized() {
    return this._initialized;
  }

  async refresh() {
    await this._keycloak.updateToken(-1);
  }

  public async authenticate(redirectUri?: string, signup?: boolean): Promise<void> {
    if (await this.initialized) {
      if (this._keycloak.authenticated) {
        if (this._keycloak.isTokenExpired()) {
          try {
            await this._keycloak.updateToken(5);
            return;
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
          } catch (e) {
            // no op - just re-authenticate
          }
        } else {
          return;
        }
      }
    }
    await this._keycloak.login({redirectUri, action: signup ? 'register' : undefined});
  }

  public async getToken() {
    await this.initialized;
    await this.authenticate();
    return this._keycloak.token;
  }

  get authenticated() {
    return this._keycloak.authenticated;
  }

  public async signup(redirectUri?: string) {
    await this.authenticate(redirectUri, true);
  }

  public async logout() {
    await this._keycloak.logout();
  }

  get authData() {
    return this._keycloak.tokenParsed;
  }

  public async fetchUserProfile(): Promise<ErhgoKeycloakProfile> {
    return this._keycloak.loadUserProfile();
  }
}

export default Authentication.instance;
