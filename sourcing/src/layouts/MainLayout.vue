<template>
  <q-layout view="lHh LpR lff">
    <q-header class="text-black" :class="{'hidden': hideHeader}">
      <q-toolbar class="custom-toolbar q-py-sm">
        <q-btn flat
               @click="openDrawer = !openDrawer"
               round
               dense
               icon="fa-light fa-bars"
               size="md"
               v-if="authentication.authenticated"/>

        <q-toolbar-title class="custom-toolbar__title">
          <q-img alt="Logo Je ne suis pas un cv"
                 src="~assets/logo_noir.png"
                 class="erhgo-logo"
                 v-if="authentication.authenticated"/>
        </q-toolbar-title>
        <q-btn flat round dense icon="fa-light fa-circle-user" size="md" v-if="authentication.authenticated">
          <q-menu auto-close style="min-width: 200px">
            <q-list>
              <q-item clickable to="/user-profile" v-if="$route.name !== 'recruitment'">
                <q-item-section avatar>
                  <q-icon size="xs" color="black" name="fa-light fa-user"/>
                </q-item-section>
                <q-item-section>
                  Mon profil
                </q-item-section>
              </q-item>
              <q-item clickable to="/edit-organization" v-if="$route.name !== 'recruitment'">
                <q-item-section avatar>
                  <q-icon size="xs" color="black" name="fa-light fa-building"/>
                </q-item-section>
                <q-item-section>
                  Mon entreprise
                </q-item-section>
              </q-item>
             <q-item clickable @click="authentication.logout()">
               <q-item-section avatar>
                 <q-icon size="xs" color="negative" name="fa-light fa-arrow-right-from-bracket"/>
               </q-item-section>
               <q-item-section>
                 Me déconnecter
               </q-item-section>
             </q-item>
            </q-list>
          </q-menu>
        </q-btn>
        <q-btn v-else outline
               id="login-button"
               @click="authenticate"
               icon="fa-light fa-circle-user"
               size="md">
          Me connecter
        </q-btn>
      </q-toolbar>
      <div v-if="showWarningAccount || showErrorAccount" class="row q-pb-lg">
        <div class="col-12 text-center">
          <q-btn class="q-mt-md text-center justify-center"
                 :class="{'warning': showWarningAccount, 'error': showErrorAccount}"
                 @click="openContactPopin = true" outline>
            <q-icon name="fa-light fa-warning q-mr-sm" size="sm"/>
            <span class=" text-body1">Version d'essai de #jenesuisPASunCV -
              <template v-if="days">{{ $tc('daysLeft', {count: days}) }}</template>
              <template v-else>publiez votre première offre</template>
            </span>
          </q-btn>
        </div>
        <div class="col-12 text-center" v-if="showErrorAccount">
          <p class="text-caption">Votre compte va être suspendu - pensez à contacter les candidats</p>
        </div>
      </div>
      <q-dialog v-model="openContactPopin" v-if="openContactPopin" class="contact-popin" :persistent="blockUser">
        <contact-pop-in v-if="openContactPopin" @closePopin="success => closePopin(success)" :blockUser="blockUser"/>
      </q-dialog>
      <div class="row q-pb-md justify-center" v-if="showSuccessActivation">
        <q-banner class="valid-text">
          <strong>Merci&nbsp;! Vous avez activé votre compte #jenesuisPASunCV.</strong>
          <q-btn flat
                 rounded
                 icon="fa-light fa-xmark" @click="showSuccessActivation = false"/>
        </q-banner>
      </div>
    </q-header>

    <q-drawer
        v-if="authentication.authenticated"
        v-model="openDrawer"
        side="left"
        :width="200"
        class="custom-bg-green1">

      <q-scroll-area class="fit">
        <q-list>

          <template v-for="(menuItem, index) in menuList" :key="index">
            <q-item clickable v-ripple :to="menuItem.to">
              <q-item-section avatar>
                <q-icon :name="`fa-light fa-${menuItem.icon}`"/>
              </q-item-section>
              <q-item-section>
                {{ menuItem.label }}
              </q-item-section>
            </q-item>
            <q-separator :key="'sep' + index" v-if="menuItem.separator"/>
          </template>
        </q-list>
      </q-scroll-area>

    </q-drawer>

    <q-page-container>

      <q-page>
        <suspense>
          <router-view/>
          <template #fallback>
            <div class="row justify-center">
              <q-circular-progress indeterminate size="50px" color="secondary"/>
            </div>
          </template>
        </suspense>
      </q-page>

    </q-page-container>

    <q-footer reveal>
      <!-- FIXME ERHGO-1349 -->
      <div class="row flex-center custom-footer">
        <p class="text-body1 q-my-sm">
          #jenesuisPASunCV © 2022 ‒
          <a href="mailto:<EMAIL>" class="custom-lien">Conditions d'utilisation</a> ‒
          <a target="_blank" href="https://jenesuispasuncv.fr/sourcing/" class="custom-lien">Plus d'informations</a>
        </p>
      </div>
    </q-footer>

  </q-layout>
</template>
<script setup lang="ts">
import { computed, provide, ref } from 'vue';
import authentication from 'src/config/authentication';
import { useRouter } from 'vue-router';
import accountProvider from 'src/config/AccountProvider';
import moment from 'moment';
import ContactPopIn from 'components/InvitationCodePopin.vue';
import { SourcingSubscriptionType } from 'erhgo-api-client';

const openDrawer = ref<boolean>(false);
const subscriptionType = computed(() => accountProvider.account.value?.subscriptionType);
const subscriptionExpirationDate = computed(() => accountProvider.account.value?.expirationDate);
const days = computed(() => {
  const now = moment();
  const dateOffset = moment(subscriptionExpirationDate.value);
  return dateOffset.diff(now, 'days');
});
const blockUser = computed(() => subscriptionType.value === SourcingSubscriptionType.EXPIRED || (subscriptionType.value === SourcingSubscriptionType.TRIAL && days.value <= 0));
const openContactPopin = ref<boolean>(blockUser.value);
const router = useRouter();
const authenticate = () => {
  authentication.authenticate(new URL(router.resolve('/').href, window.location.origin).href);
};


provide('showContactPopin', () => openContactPopin.value = true);
const showWarningAccount = computed(() => subscriptionType.value === SourcingSubscriptionType.TRIAL && (days.value > 7 || !days.value));
const showErrorAccount = computed(() => subscriptionType.value === SourcingSubscriptionType.TRIAL && days.value <= 7);

const menuList = [
  {
    label: 'Mes offres',
    to: '/recruitments-list',
    icon: 'bullhorn',
    separator: true,
  },
  {
    label: 'Mon vivier',
    to: '/sourcing',
    icon: 'folder-user',
    separator: true,
  },
  {
    label: 'Mon équipe',
    to: '/users-list',
    icon: 'people-roof',
    separator: true,
  },
];

const showSuccessActivation = ref(false);

const closePopin = (success: boolean) => {
  openContactPopin.value = false;
  showSuccessActivation.value = success;
};

const hideHeader = computed(() => !window.menubar?.visible && !window.toolbar?.visible);
</script>
<style lang="scss">

@import '../css/quasar.variables.scss';

.erhgo-logo {
  max-width: 250px;
  min-width: 200px;
  max-height: 40px;
}

.warning.q-btn--rectangle::before {
  border-color: $warning !important;
}

.error.q-btn--rectangle::before {
  border-color: $negative !important;
}

.warning-border {
  border: solid 1px $warning !important;
}

.negative-border {
  border: solid 1px $negative !important;
}
</style>
