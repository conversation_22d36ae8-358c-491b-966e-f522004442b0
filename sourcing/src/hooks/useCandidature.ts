import type { Ref} from 'vue';
import { ref, watchEffect } from 'vue';
import type {
    SourcingCandidatureDetail,
    SourcingCandidatureNote,
    SourcingCandidatureState,
    SourcingContactInformation,
} from 'erhgo-api-client';
import { useApi } from 'src/config/api';
import { v4 as uuidv4 } from 'uuid';

export interface CandidatureModel {
    candidature: Ref<SourcingCandidatureDetail>;
    loading: Ref<boolean>,
    changeState: (nextState: SourcingCandidatureState) => Promise<SourcingContactInformation | undefined>;
}

const useCandidature = (candidatureValue: SourcingCandidatureDetail) => {

    const loading = ref<boolean>(false);
    const {service: {api, isLoading}} = useApi();
    const candidature = ref<SourcingCandidatureDetail>(candidatureValue);
    const notes = ref<SourcingCandidatureNote[]>(candidatureValue.candidatureNotes);

    watchEffect(() => {
        loading.value = isLoading.value;
    });

    function dispatchCandidatureChanged() {
        if (window.opener?.document) {
            window.opener.document.dispatchEvent(new CustomEvent('refreshCandidature', { detail: candidature.value }));
        }
    }

    const saveNote = async (note: string) => {
        await api.saveCandidatureNote(candidatureValue.candidatureId, {
            id: uuidv4(),
            text: note,
        });
        candidature.value = { ...candidature.value, lastNote: note };
        notes.value = [{ userFullname: 'Vous', noteDateTime: new Date(), content: note }, ...notes.value];
        dispatchCandidatureChanged();
    };

    const changeState = async (nextState: SourcingCandidatureState) => {
        if (candidature.value && candidature.value.state !== nextState) {
            const {candidatureId} = candidature.value;
            const data = (await api.updateSourcingCandidatureState(candidatureId, {nextState: nextState})).data;
            candidature.value = {...candidature.value, state: nextState, ...(data ?? {})};
            dispatchCandidatureChanged();
            return data;
        }
    };

    return {
        candidature,
        changeState,
        loading,
        saveNote,
        notes,
    };
};

export default useCandidature;
