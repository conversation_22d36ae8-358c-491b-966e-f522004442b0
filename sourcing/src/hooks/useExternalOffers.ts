import type { Ref } from 'vue';
import { ref, watchEffect } from 'vue';
import type {
  ExternalOffersPage,
  ExternalOfferSummary,
  ScrapeOffersCommandDTO,
} from 'erhgo-api-client';
import { useApi } from 'src/config/api';
import authentication from 'src/config/authentication';

const DEFAULT_ROWS_PER_PAGE = 10;
export type Pagination = {
  page: number;
  rowsPerPage: number;
  rowsNumber?: number;
};

export class ExternalOffersService {
  offers = ref<ExternalOfferSummary[]>([]);
  loading = ref<boolean>(false);
  pagination: Ref<Pagination> = ref({
    page: 1,
    descending: true,
    rowsPerPage: DEFAULT_ROWS_PER_PAGE,
    rowsNumber: 0,
  });
  loadings = ref<string[]>([]);

  integrate = async (item: ExternalOfferSummary) => {
    item.recruitmentCreationState = 'PROCESSING';
    try {
      const {
        service: { api },
      } = useApi();
      await api.integrateExternalOffer(item.id);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {
      item.recruitmentCreationState = 'ERROR';
    }
  };

  ignore = async (item: ExternalOfferSummary) => {
    this.loadings.value = [...this.loadings.value, item.id];
    try {
      const {
        service: { api },
      } = useApi();
      await api.ignoreExternalOffer(item.id);
      item.recruitmentCreationState = 'IGNORE';
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {
      item.recruitmentCreationState = 'ERROR';
    } finally {
      this.loadings.value = this.loadings.value.filter(
        (finished) => finished !== item.id,
      );
    }
  };

  scrapeOffers = async (command: ScrapeOffersCommandDTO) => {
    const {
      service: { api },
    } = useApi();

    this.loading.value = true;
    try {
      await api.scrapeOffers(command);
      await this.updatePagination();
    } finally {
      this.loading.value = false;
    }
  };

  updatePagination = async (paginationParam?: Pagination) => {
    const { page, rowsPerPage } = paginationParam ?? this.pagination.value;
    if (paginationParam) {
      this.pagination.value = { ...this.pagination.value, page, rowsPerPage };
    }
    const newLines = await this.fetchNewLines();
    this.pagination.value.rowsNumber = newLines.totalNumberOfElements;
    this.offers.value = [...newLines.content];
  };
  private fetchNewLines = async (): Promise<ExternalOffersPage> => {
    const {
      service: { api, isLoading },
    } = useApi();
    watchEffect(() => {
      this.loading.value = isLoading.value;
    });

    const organizationCode = authentication.authData?.realm_access?.roles.find(
      (e) => e.includes('S-'),
    );
    return (
      await api.getExternalOffers(
        (this.pagination.value.rowsPerPage ||
          this.pagination.value.rowsNumber) ??
          DEFAULT_ROWS_PER_PAGE,
        this.pagination.value.page - 1,
        organizationCode,
      )
    ).data;
  };
}
