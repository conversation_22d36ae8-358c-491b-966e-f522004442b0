import type { ComputedRef, Ref } from 'vue';
import { computed, getCurrentInstance, ref, watch, watchEffect } from 'vue';
import type {
  SourcingCandidatureItem,
  SourcingCandidaturePage} from 'erhgo-api-client';
import {
  SortDirection,
  SourcingCandidatureSort,
  SourcingCandidatureState,
} from 'erhgo-api-client';
import { useApi } from 'src/config/api';
import _ from 'underscore';

const DEFAULT_ROWS_PER_PAGE = 20;
export type Pagination = {
  page: number;
  sortBy: string;
  descending: boolean;
  rowsPerPage: number;
  rowsNumber?: number;
};
export type SelectableState = {
  value: SourcingCandidatureState;
  label: string;
};
const sortForString = new Map<string, SourcingCandidatureSort>([
  ['submissionDate', SourcingCandidatureSort.SUBMISSION_DATE],
  ['lastActionDate', SourcingCandidatureSort.LAST_ACTION_DATE],
  ['recommendation', SourcingCandidatureSort.CANDIDATURE_SOURCE],
  ['state', SourcingCandidatureSort.CANDIDATURE_STATE],
  ['alreadyCandidated', SourcingCandidatureSort.CANDIDATURES_COUNT],
]);

const allStatesEnum = [
  SourcingCandidatureState.NEW,
  SourcingCandidatureState.FAVORITE,
  SourcingCandidatureState.TO_CONTACT,
  SourcingCandidatureState.CONTACTED,
  SourcingCandidatureState.DISMISS,
];

export class CandidaturesService {

  candidatures = ref<SourcingCandidatureItem[]>([]);
  loading = ref<boolean>(false);
  pagination: Ref<Pagination> = ref({
    page: 1,
    sortBy: SourcingCandidatureSort.CANDIDATURES_COUNT,
    descending: true,
    rowsPerPage: DEFAULT_ROWS_PER_PAGE,
    rowsNumber: 0,
  });
  searchQuery = ref<string>();
  selectedStates = ref<SelectableState[]>();
  selectableStates: ComputedRef<SelectableState[]>;
  clearableStates: ComputedRef<boolean>;
  allStates: SelectableState[];
  defaultStates: SelectableState[];
  private readonly recruitmentId: number | undefined;

  constructor(recruitmentId?: number) {
    this.recruitmentId = recruitmentId;
    const t: (a: string) => string = getCurrentInstance().appContext.app.config.globalProperties.$t;
    this.allStates = allStatesEnum.map(value => ({ value, label: t(`candidatureState.${value}`) }));
    this.selectableStates = computed(() => this.allStates.filter(s => !this.selectedStates.value?.some(v => v.value === s.value)));
    this.defaultStates = [...this.allStates.filter(s => s.value !== SourcingCandidatureState.DISMISS)];
    this.clearableStates = computed(() => this.selectedStates.value?.length !== this.defaultStates.length || this.selectedStates.value.some(s => !this.defaultStates.some(d => d.value === s.value)));
    this.selectedStates.value = [...this.defaultStates];
    watch(() => [this.selectedStates.value, this.searchQuery.value], _.debounce(() => this.updatePagination(), 500));
  }

  clearStates = () => {
    this.selectedStates.value = [...this.defaultStates];
  };


  updatePagination = async (paginationParam?: Pagination) => {
    const { page, rowsPerPage, sortBy, descending } = paginationParam ?? this.pagination.value;
    if (paginationParam) {
      this.pagination.value = { ...this.pagination.value, page, rowsPerPage, descending, sortBy };
    }
    const newLines = await this.fetchNewLines();
    this.pagination.value.rowsNumber = newLines.totalNumberOfElements;
    this.candidatures.value = [...newLines.content];
  };
  private fetchNewLines = async (): Promise<SourcingCandidaturePage> => {
    const { service: { api, isLoading } } = useApi();
    watchEffect(() => {
      this.loading.value = isLoading.value;
    });

    return (await api.getSourcingCandidaturesPage(
      (this.pagination.value.rowsPerPage || this.pagination.value.rowsNumber) ?? DEFAULT_ROWS_PER_PAGE,
      this.pagination.value.page - 1,
      sortForString.get(this.pagination.value.sortBy) ?? SourcingCandidatureSort.SUBMISSION_DATE,
      this.pagination.value.descending ? SortDirection.DESC : SortDirection.ASC,
      this.selectedStates.value?.map(c => c.value) ?? [],
      this.searchQuery.value,
      this.recruitmentId,
    )).data;
  };
}
