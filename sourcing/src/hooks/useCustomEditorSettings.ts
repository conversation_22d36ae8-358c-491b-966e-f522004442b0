import { useQuasar } from 'quasar';

export function useCustomEditorSettings() {
  const $q = useQuasar();
  return {
    denseMode: $q.screen.lt.md,
    basicToolbar: [
      ['left', 'center', 'right'],
      ['bold', 'italic', 'underline'],
      ['token', 'hr', 'link', 'custom_btn'],
      ['fullscreen'],
      [
        {
          label: $q.lang.editor.fontSize,
          icon: $q.iconSet.editor.fontSize,
          fixedLabel: true,
          fixedIcon: true,
          list: 'no-icons',
          options: [
            'size-1',
            'size-2',
            'size-3',
            'size-4',
            'size-5',
            'size-6',
            'size-7',
          ],
        },
        {
          label: $q.lang.editor.defaultFont,
          icon: $q.iconSet.editor.font,
          fixedIcon: true,
          list: 'no-icons',
          options: [
            'default_font',
            'arial',
            'arial_black',
            'courier_new',
            'impact',
            'lucida_grande',
            'times_new_roman',
            'verdana',
          ],
        },

        'removeFormat',
      ],
      ['quote', 'unordered', 'ordered', 'outdent', 'indent'],

      ['undo', 'redo'],
    ],
    fonts: {
      arial: 'Arial',
      arial_black: 'Arial Black',
      courier_new: 'Courier New',
      impact: 'Impact',
      lucida_grande: 'Lucida Grande',
      times_new_roman: 'Times New Roman',
      verdana: 'Verdana',
    },
  };
}
