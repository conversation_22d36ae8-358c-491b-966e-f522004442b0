import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
    {
        path: '/',
        component: () => import('layouts/MainLayout.vue'),
        children: [
            {
                path: '/',
                redirect: {
                    name: 'recruitments-list',
                    state: {
                        // See beforeEach router guard (router/index.ts atm) too if changes required here
                        redirected: true,
                    },
                },
            },
            {
                path: '/registration',
                redirect: '/',
            },
            {
                path: 'recruitment/:recruitmentId?',
                component: () => import('pages/RecruitmentPage.vue'),
                name: 'recruitment',
                props: route => {
                    const {recruitmentId} = route.params;
                    return {
                        recruitmentId: recruitmentId ? Number.parseInt(recruitmentId as string) : undefined,
                    };
                },
            },
            {
                path: 'recruitment-detail/:recruitmentId',
                component: () => import('pages/RecruitmentDetailPage.vue'),
                name: 'recruitment-detail',
                props: (route) => {
                    const {recruitmentId} = route.params;
                    const showConfirmBanner = route.query?.showConfirmBanner === 'true';
                    return {
                        recruitmentId: Number.parseInt(recruitmentId as string),
                        showConfirmBanner,
                    };
                },
            },
          {
            path: 'candidature-detail/:candidatureId',
            component: () => import('pages/CandidatureDetailPage.vue'),
            name: 'candidature-detail',
            props: (route) => {
              return {
                candidatureId: Number.parseInt(route.params.candidatureId as string),
              };
            },
          },
            {
              path: 'recruitments-list',
              component: () => import('pages/RecruitmentsListPage.vue'),
              name: 'recruitments-list',
              props: () => {
                return {
                  // See '/' redirection stuff & beforeEach guard too if changes required here
                  redirected: history.state?.redirected,
                };
              },
            },
          {
            path: 'users-list',
            component: () => import('pages/UsersListPage.vue'),
            name: 'users-list',
          },
          {
            path: 'user-profile',
            component: () => import('pages/UserProfilePage.vue'),
            name: 'user-profile',
          },
          {
            path: 'edit-organization',
            component: () => import('pages/EditOrganizationPage.vue'),
            name: 'edit-organization',
          },
          {
            path: 'siret-error-page',
            component: () => import('pages/SiretAlreadyExistErrorPage.vue'),
            name: 'siret-error-page',
          },
          {
            path: 'sourcing',
            component: () => import('pages/SourcingPage.vue'),
            name: 'sourcing-page',
          }, {
            path: 'edit-recruitment',
            component: () => import('pages/RecruitmentEditPage.vue'),
            name: 'edit-recruitment-page',
            props: (route) => ({
              externalOfferId: route.query.externalOfferId ? route.query.externalOfferId : null,
              recruitmentId: route.query.recruitmentId ? Number.parseInt(route.query.recruitmentId, 10) : null,
            }),
          }, {
            path: 'external-offers',
            component: () => import('pages/ExternalOfferPage.vue'),
            name: 'external-offer-page',
          },

        ],
    },

    // Always leave this as last one,
    // but you can also remove it
    {
        path: '/:catchAll(.*)*',
        component: () => import('pages/ErrorNotFound.vue'),
    },
];

export default routes;
