import { route } from 'quasar/wrappers';
import { createMemoryHistory, createRouter, createWebHashHistory, createWebHistory } from 'vue-router';

import routes from './routes';
import authentication from 'src/config/authentication';
import accountProvider from 'src/config/AccountProvider';

/*
 * If not building with SSR mode, you can
 * directly export the Router instantiation;
 *
 * The function below can be async too; either use
 * async/await or return a Promise which resolves
 * with the Router instance.
 */
const SIRET_ERROR_PATH = 'siret-error-page';

export default route(function (/* { store, ssrContext } */) {
  const createHistory = process.env.SERVER
    ? createMemoryHistory
    : (process.env.VUE_ROUTER_MODE === 'history' ? createWebHistory : createWebHashHistory);

  const Router = createRouter({
    scrollBehavior: () => ({left: 0, top: 0}),
    routes,

    // Leave this as is and make changes in quasar.conf.js instead!
    // quasar.conf.js -> build -> vueRouterMode
    // quasar.conf.js -> build -> publicPath
    history: createHistory(process.env.VUE_ROUTER_BASE),
  });

  const adjust = (path: string) => {
    return path?.endsWith(SIRET_ERROR_PATH) ? '/' : path;
  };

  Router.beforeEach(async (to, from) => {
    let pathToRedirect = '';
    if (from?.path !== '/' || !to.redirectedFrom) {
      pathToRedirect = `#${adjust(to.fullPath)}`;
    }
    await authentication.authenticate(`${window.location.origin}/${pathToRedirect}`);
    return true;
  });

  Router.beforeEach(async (to) => {
    await accountProvider.accountPromise.value;
    if (
      authentication.authenticated &&
      to.name !== SIRET_ERROR_PATH &&
      accountProvider.isSiretConflict.value
    ) {
      return { name: SIRET_ERROR_PATH };
    }
    return true;
  });
  return Router;
});
