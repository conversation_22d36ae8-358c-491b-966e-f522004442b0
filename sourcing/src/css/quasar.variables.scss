// Quasar SCSS (& Sass) Variables
// --------------------------------------------------
// To customize the look and feel of this app, you can override
// the Sass/SCSS variables found in Quasar's source Sass/SCSS files.

// Check documentation for full list of Quasar variables

// Your own variables (that are declared here) and Quasar's own
// ones will be available out of the box in your .vue/.scss/.sass files

// It's highly recommended to change the default colors
// to match your app's branding.
// Tip: Use the "Theme Builder" on Quasar's documentation website.
@import url('https://fonts.googleapis.com/css?family=Ubuntu');

$typography-font-family: 'Ubuntu', sans-serif !default;
$primary: #ffffff;
$secondary: #00f0c8;
$accent: #e50354;
$purple: #7E02EC;
$green: #007A66;

$dark: #1d1d1d;
$dark-page: #121212;

$positive: #7e02ec;
$negative: #e50354;
$info: #31CCEC;
$warning: #F2C037;

$erhgo-grey1: #00000010;
$erhgo-grey2: #999999;
$erhgo-grey3: #666666;
$erhgo-grey4: #F9F9F9;
$erhgo-blue: #3096D6;
$erhgo-green1: #00f0c8;
$erhgo-green1-light: #00f0c81f;
$erhgo-green2: #00CFAD;
$erhgo-green3: #007A66;
$erhgo-violet1: #7E02EC;
$erhgo-violet1-light: #7E02EC1f;
$erhgo-violet2: #6F02CF;
$erhgo-violet3: #430099;

$erhgo-title: #006655;
$erhgo-object: #00a88c;
