<template>
  <router-view />
</template>

<script lang="ts" setup>
import {watch} from 'vue';
import useGlobalError from 'src/hooks/useGlobalError';
import {useQuasar} from 'quasar';

const {globalError} = useGlobalError;
const $q = useQuasar();

watch(() => globalError.value, (isError, wasError) => {
  if (isError && !wasError) {
    $q.notify({
      position: 'top',
      type: 'negative',
      message: 'Une erreur est survenue ; veuillez rafraîchir la page et contacter le support si le problème persiste.',
    });
  }
});
</script>
