<template>
  <div>
    <q-card class="q-mb-md">
      <q-card-section>
        <div class="text-h6">Récupération d'offres externes</div>
        <q-form class="row q-col-gutter-md" @submit="onScrapeSubmit">
          <q-input
            v-model="scrapeForm.recruiterUrl"
            :rules="[(val) => !!val || 'URL est obligatoire']"
            class="col-12 col-md-6"
            clearable
            label="URL du recruteur*"
            outlined
          />

          <q-input
            v-model.number="scrapeForm.maxOffersScrapped"
            class="col-12 col-md-2"
            hint="Optionnel"
            label="Nombre max. d'offres"
            outlined
            type="number"
          />

          <q-input
            v-model="scrapeForm.offerUrlMustContain"
            class="col-12 col-md-4"
            clearable
            hint="Optionnel"
            label="Filtrer les URLs contenant"
            outlined
          />

          <div class="col-12 flex justify-end">
            <q-btn
              :disable="!scrapeForm.recruiterUrl"
              :loading="loading"
              color="secondary"
              label="Récupérer les offres"
              type="submit"
            />
          </div>
        </q-form>
      </q-card-section>
    </q-card>

    <q-dialog v-model="showDialog" @hide="selectedItem = null">
      <offer-descriptions :model-value="selectedItem" v-if="selectedItem" />
    </q-dialog>
    <q-table
      class="recruitments-table sticky-header-table"
      sticky
      flat
      bordered
      separator="cell"
      :rows="offers"
      :columns="columns"
      row-key="candidatureId"
      no-data-label="Aucune candidature trouvée"
      no-results-label="Aucune candidature ne correspond à ce critère de recherche"
      wrap-cells
      @request="fetchData"
      :rows-per-page-options="[10, 20, 50, 0]"
      v-model:pagination="pagination"
      ref="candidatureTable"
    >
      <template v-slot:body-cell-title="props">
        <q-td :props="props">
          <a
            class="custom-lien cursor-pointer"
            @click="selectedItem = props.row"
            >{{ props.row.title }}</a
          >
        </q-td>
      </template>
      <template v-slot:body-cell-recruitmentState="props">
        <q-td :props="props">
          <q-chip
            :color="
              recruitmentStateColor[props.row.recruitmentCreationState || '']
            "
            :label="
              recruitmentStateLabel[props.row.recruitmentCreationState || '']
            "
          />
        </q-td>
      </template>
      <template v-slot:body-cell-actions="props">
        <q-td :props="props">
          <q-btn
            flat
            size="sm"
            @click="goToRecruitmentDetail(props.row)"
            title="Voir le recrutement"
            icon="fa-light fa-eye"
            class="q-px-xs"
            v-if="props.row.recruitmentId"
          />
          <q-btn
            @click="integrate(props.row)"
            :label="
              props.row.recruitmentCreationState === 'IGNORE'
                ? 'Réintégrer'
                : 'Intégrer'
            "
            color="secondary"
            text-color="black"
            class="q-ma-sm"
            size="sm"
            rounded
            v-if="
              props.row.recruitmentCreationState !== 'PROCESSING' &&
              !props.row.recruitmentId
            "
            :loading="loadings.includes(props.row.id)"
          ></q-btn>
          <q-btn
            @click="ignore(props.row)"
            label="Ignorer"
            color="negative"
            text-color="white"
            size="sm"
            rounded
            v-if="
              props.row.recruitmentCreationState !== 'PROCESSING' &&
              props.row.recruitmentCreationState !== 'IGNORE' &&
              !props.row.recruitmentId
            "
            :loading="loadings.includes(props.row.id)"
          ></q-btn>
        </q-td>
      </template>
    </q-table>
  </div>
</template>
<script setup lang="ts">
import { ExternalOffersService } from 'src/hooks/useExternalOffers';
import type { Pagination } from 'src/hooks/useCandidatures';
import moment from 'moment/moment';
import type { QTableProps } from 'quasar';
import type {
  ExternalOfferSummary,
  ScrapeOffersCommandDTO,
} from 'erhgo-api-client';
import { useRouter } from 'vue-router';
import { ref, watchEffect } from 'vue';
import OfferDescriptions from 'components/externaloffer/OfferDescriptions.vue';
import authentication from 'src/config/authentication';

const router = useRouter();
const recruitmentStateLabel = {
  DONE: 'Finalisée',
  ERROR: 'En erreur',
  IGNORE: 'Ignorée',
  MANUAL: 'Manuelle',
  PROCESSING: 'En cours...',
  '': 'Inconnu',
};

const recruitmentStateColor = {
  DONE: 'secondary',
  ERROR: 'negative',
  IGNORE: 'erhgo-grey2',
  MANUAL: 'info',
  PROCESSING: 'warning',
  '': null,
};

const typeContractCategoryLabel = {
  TEMPORARY: 'Temporaire',
  PERMANENT: 'CDI',
  PRO: 'Contrat pro.',
  '': 'Inconnu',
};

const columns: QTableProps['columns'] = [
  {
    name: 'remoteId',
    label: 'Identifiant externe',
    field: 'remoteId',
  },
  {
    name: 'title',
    label: 'Intitulé',
    field: 'title',
  },
  {
    name: 'lastModificationDate',
    label: 'Dernier traitement',
    field: (row: ExternalOfferSummary) =>
      row.lastModificationDate
        ? moment(row.lastModificationDate).format('DD/MM/YY')
        : 'Inconnu',
  },
  {
    name: 'location',
    label: 'Localisation',
    field: 'location',
  },
  {
    name: 'typeContractCategory',
    label: 'Contrat',
    field: (row: ExternalOfferSummary) =>
      typeContractCategoryLabel[row.typeContractCategory ?? ''],
  },
  {
    name: 'salary',
    label: 'Salaires',
    field: (row: ExternalOfferSummary) => (row.salaries ?? []).join(','),
    sortable: true,
  },
  {
    name: 'recruitmentState',
    label: 'Création recrutement',
    field: 'recruitmentState',
  },
  { name: 'actions', align: 'center', label: 'Actions', field: 'actions' },
];

const {
  updatePagination,
  offers,
  pagination,
  integrate,
  ignore,
  loadings,
  scrapeOffers,
  loading,
} = new ExternalOffersService();

const scrapeForm = ref<ScrapeOffersCommandDTO>({
  recruiterUrl: '',
  maxOffersScrapped: 5,
  offerUrlMustContain: '',
  organizationCode: authentication.authData?.realm_access?.roles.find(
    (e: string) => e.includes('S-'),
  ),
});

const onScrapeSubmit = async () => {
  await scrapeOffers(scrapeForm.value);
};
const fetchData: (props: { pagination: Pagination }) => Promise<void> = async ({
  pagination,
}) => await updatePagination(pagination);

const goToRecruitmentDetail = async (row: ExternalOfferSummary) => {
  const redirect = {
    name: 'recruitment-detail',
    params: {
      recruitmentId: row.recruitmentId,
    },
  };
  if (
    !window.open(
      router.resolve(redirect).href,
      'newwindow',
      "width=1200,height=800,menubar=no,toolbar=no,locationbar=no,status=no,resizable=yes,scrollbars=yes'",
    )
  ) {
    await router.push(redirect);
  }
};

const selectedItem = ref<ExternalOfferSummary | null>(null);
const showDialog = ref<boolean>(false);

watchEffect(() => {
  showDialog.value = !!selectedItem.value;
});

updatePagination();
</script>
<style lang="scss">
.recruitments-table {
  .q-table__top,
  .q-table__bottom,
  thead tr:first-child th {
    background-color: $secondary;
  }
}
</style>
