<template>
  <div class="chimney--small">
    <div class="row justify-end">
      <q-btn round flat size="sm" @click="$router.go(-1)">
        <q-icon name="fa-solid fa-close" size="sm" color="secondary"/>
      </q-btn>
    </div>
    <q-banner dense class="q-ma-md text-white bg-negative" v-if="isError">
      <q-icon name="fa-light fa-warning q-mr-md" size="md"/>
      Une erreur est survenue - veuillez réessayer ou contacter notre support.
    </q-banner>
    <q-form ref="form" greedy @submit="submit">
      <q-input hint="Saisissez votre nom"
               label="Votre nom"
               dense
               hide-hint
               v-model="command.fullname"
               :rules="[v => !!v || 'Veuillez préciser votre nom']"/>
      <q-input hint="Saisissez votre adresse mail"
               label="Votre adresse mail"
               dense
               hide-hint
               v-model="command.email"
               :rules="[v => !!v || 'Veuillez préciser votre adresse mail']"/>
      <q-input hint="Saisissez votre numéro de téléphone"
               label="Votre téléphone"
               dense
               hide-hint
               v-model="command.phone"
               :rules="[v => !!v || 'Veuillez préciser votre numéro de téléphone']"/>
      <div id="recurrence-mail-form" class="row justify-between text-body1 q-mb-sm">
        <label class="user-text  items-start text-justify q-pa-md text-caption">
          <q-icon name="fa-regular fa-circle-info" size="xs" color="secondary"/>
          <span> Nous vous envoyons régulièrement des mails vous informant de l’avancement de vos offres. Vous pouvez ici choisir la fréquence à laquelle nous vous envoyons ces mails.
          </span>
        </label>
        <div class="col-12 text-caption q-mt-sm">
          <q-list>
            <q-item v-for="option in options" :key="option.value" tag="label" v-ripple>
              <q-item-section avatar>
                <q-radio v-model="selectedOption" :val="option.value" :value="selectedOption" color="secondary"/>
              </q-item-section>
              <q-item-section>
                <q-item-label>
                  <template v-if="option.value === 'WEEKLY'">
                    Toutes les semaines le
                    <q-select
                      v-model="selectedDay"
                      color="secondary"
                      :options="days"
                      dense
                      outlined
                      class="inline q-mx-xs col-auto text-caption outli"
                      :value="selectedDay"
                    />
                    vers 8h
                  </template>
                  <template v-else>
                    {{ option.label }}
                  </template>

                </q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
        <label  v-if="selectedOption === 'NEVER'" class="user-text items-start text-justify q-pa-md text-caption">
          <q-icon name="fa-solid fa-triangle-exclamation" size="sm" color="secondary"/>
          <span class="text-red">Attention, vous ne recevrez aucune notification en cas de nouvelle candidature à vos recrutements.
          </span>
        </label>
      </div>
      <q-btn type="submit" outline class="bg-secondary" :loading="isLoading">Valider</q-btn>
    </q-form>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import type { SourcingUserDetails, UpdateSourcingUserCommand } from 'erhgo-api-client';
import { useApi } from 'src/config/api';
import { QForm, useQuasar } from 'quasar';

const {service: {api, isError, isLoading}} = useApi();

const options = [
  {label: 'Toutes les semaines vers 8h', value: 'WEEKLY'},
  {label: 'Tous les matins vers 8h', value: 'DAILY'},
  {label: 'Dès qu’une personne candidate', value: 'IMMEDIATELY'},
  {label: 'Jamais', value: 'NEVER'},
];

const days = [
  { label: 'Lundi', value: 1 },
  { label: 'Mardi', value: 2 },
  { label: 'Mercredi', value: 3 },
  { label: 'Jeudi', value: 4 },
  { label: 'Vendredi', value: 5 },
  { label: 'Samedi', value: 6 },
  { label: 'Dimanche', value: 7 },
];

const $q = useQuasar();
const form = ref<{ validate: () => Promise<boolean> }>();
const user = ref<SourcingUserDetails>((await api.getSourcingUser()).data);

const selectedOption = ref(user.value.preferences?.mailFrequency ?? 'IMMEDIATELY');
const selectedDay = ref(user.value.preferences?.isoWeekDay ? days[user.value.preferences?.isoWeekDay - 1] : undefined);

const command = ref<UpdateSourcingUserCommand>({
  phone: user.value.phone,
  fullname: user.value.fullname,
  email: user.value.email,
  preferences: user.value.preferences,
});

const submit = async () => {
  if (await form.value?.validate()) {
    command.value.preferences = {
      mailFrequency: selectedOption.value,
      isoWeekDay: selectedOption.value === 'WEEKLY' ? selectedDay.value?.value : undefined,
    };
    const success = await api.updateSourcingUser(command.value);
    if (success) {
      $q.notify({
        position: 'top',
        type: 'positive',
        textColor: 'black',
        color: 'secondary',
        message: 'Vos changements ont bien été pris en compte',
      });
    }
  }
};
</script>

