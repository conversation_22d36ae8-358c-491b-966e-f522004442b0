<template>
  <div class="chimney--fluid">
    <q-circular-progress v-if="!recruiters"/>
    <div v-if="!recruiters" class="text-center">
      <div class="row justify-center q-my-md">
        <q-spinner-dots color="secondary" size="xl"/>
      </div>
    </div>
    <div class="q-px-lg">
      <h5 class="d-flex justify-between row">
        <span>Mon équipe</span>
      </h5>
    </div>
    <div class="q-pa-lg">
      <q-btn rounded
             icon="fa-light fa-add"
             class="bg-green text-white"
             @click="showDialog = true"
             label="Inviter une personne"/>
      <div class="q-pb-md">
        <q-dialog v-model="showDialog">
          <invite-person v-if="showDialog" @close="showDialog = false"
                         @updateRecruiters="fetchRecruiters"/>
        </q-dialog>
      </div>
    </div>
    <div class="q-px-lg">
      <q-table
          v-if="!isError && !isLoading && recruiters"
          :pagination="pagination"
          :columns="columns"
          :rows="recruiters"
          hide-pagination
      >
        <template v-slot:body-cell-lastConnectionDate="props">
          <q-td :props="props">
            <template v-if="props.row.lastConnectionDate">
              {{ moment(props.row.lastConnectionDate).format("DD/MM/YYYY HH:mm") }}
            </template>
            <template v-else>
              <q-icon size="xs" color="red" name="fa-regular fa-circle-xmark"/>
              jamais connecté
            </template>
          </q-td>
        </template>
        <template v-slot:body-cell-enabled="props">
          <q-td :props="props">
            <q-chip
                :color="props.row.enabled ? 'secondary' : 'red'"
                :label="props.row.enabled ? 'Activé' : 'Désactivé'"
            />
          </q-td>
        </template>
      </q-table>
    </div>
    <div v-if="!isError && !isLoading && !recruiters.length">
      <h4>Personne dans mon équipe</h4>
    </div>
    <q-banner dense class="q-ma-md text-white bg-negative" v-if="isError">
      <q-icon name="fa-light fa-warning q-mr-md" size="md"/>
      Une erreur est survenue - veuillez réessayer ou contacter notre support.
    </q-banner>
  </div>

</template>

<script setup lang="ts">
import {useApi} from 'src/config/api';
import authentication from 'src/config/authentication';
import {onMounted, ref} from 'vue';
import type {SourcingUser} from 'erhgo-api-client';
import InvitePerson from 'components/InvitePerson.vue';
import moment from 'moment';

const {service: {api, isError, isLoading}} = useApi();
const organizationCode = authentication.authData?.realm_access?.roles.find(e => e.includes('S-'));
const recruiters = ref<SourcingUser[]>([]);
const showDialog = ref<boolean>(false);

const pagination = {
  page: 1,
  rowsPerPage: 0,
};
const columns = [
  {name: 'fullname', align: 'left', label: 'Nom', field: 'fullname', sortable: true},
  {name: 'email', align: 'left', label: 'Email', field: 'email', sortable: true},
  {name: 'enabled', align: 'center', label: 'Statut', field: 'enabled', sortable: false},
  {
    name: 'lastConnectionDate',
    align: 'left',
    label: 'Dernière Connexion',
    field: 'lastConnectionDate',
    sortable: true,
  },

];

const fetchRecruiters = async () => {
  if (organizationCode) {
    recruiters.value = (await api.getSourcingUsers(organizationCode)).data;
  }
};

onMounted(() => fetchRecruiters());

</script>
