<template>
  <div class="chimney--fluid">
    <div class="row justify-end">
      <q-btn round flat size="sm" @click="$router.go(-1)">
        <q-icon name="fa-solid fa-close" size="sm" color="secondary"/>
      </q-btn>
    </div>
    <organization-description :organization="organization" @submit="submit" ref="form"/>
    <div class="row">
      <q-btn @click="submit" outline class="bg-secondary q-mt-lg" :loading="isLoading">Valider</q-btn>
      <q-space/>
      <q-btn class="bg-negative q-mt-lg" :loading="isLoading" @click="$router.go(-1)">Annuler</q-btn>
    </div>
  </div>
</template>
<script setup lang="ts">
import {ref} from 'vue';
import type {SourcingOrganization} from 'erhgo-api-client';
import {useApi} from 'src/config/api';
import OrganizationDescription from 'components/recruitment/OrganizationDescription.vue';
import {useQuasar} from 'quasar';

const form = ref<{ submit: () => Promise<boolean> }>();
const {service: {api, isLoading}} = useApi();
const organization = ref<SourcingOrganization>((await api.getSourcingOrganization()).data);

const $q = useQuasar();
const notifySuccess = () => {
  $q.notify({
    position: 'top',
    type: 'positive',
    textColor: 'black',
    color: 'secondary',
    message: 'Vos changements ont bien été pris en compte',
  });
};
const submit = async () => {
  try {
    isLoading.value = true;
    const success = await form.value?.submit();
    if (success) {
      notifySuccess();
    }
  } finally {
    isLoading.value = false;
  }

};

</script>
