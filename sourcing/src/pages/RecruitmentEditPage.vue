<template>
  <div class="q-mx-lg">
    <div class="justify-center">
      <q-form ref="form" greedy>
        <h3 class="text-weight-bold q-my-xl">
          {{ isEditingRecruitment ? "Modification" : "Création" }} d'un recrutement
        </h3>
        <span class="text-caption"
              v-if="allDiffKeys.length"><strong>Modifications identifiées</strong>&nbsp;: {{ allDiffKeys.join(", ")
          }}</span>
        <p class="text-caption" v-if="allDiffKeys.length">Pour les données modifiées, sont indiqués : En rouge les
          suppressions, en vert les ajouts, en gris les
          parties non modifiées.</p>
        <div class="row q-py-lg" v-if="isEditingExternalOffer">
          <div class="col">
            <label for="select-occupation" class="text-label">Informations de l'offre externe</label>
            <external-offer-suggesting-list
              @diff="addDiffKey"
              class="q-my-md"
              :suggestion-map="suggestionFromAts?.otherInformations"
              :previous-map="previousAtsVersion?.otherInformations"
              :non-clickable="true"
            />
          </div>
        </div>

        <div class="row q-py-lg">
          <search-occupation
            :class="isEditingExternalOffer ? 'col-5' : 'col-12'"
            class="q-pa-md"
            v-model="selectedJobOccupation"
            :query="occupationQuery"
            :disabled="isLoading"
          />
          <external-offer-suggesting-list
            @diff="addDiffKey"
            class="col-7"
            v-if="isEditingExternalOffer"
            :suggestion-map="suggestionFromAts?.occupationTitles"
            :previous-map="previousAtsVersion?.occupationTitles"
            @rowClick="updateOccupationQuery"
          />
        </div>
        <div class="row q-py-lg">
          <select-city
            :class="isEditingExternalOffer ? 'col-5' : 'col-12'"
            class="q-pa-md"
            v-model="selectedLocation"
            :query="locationSearchQuery"
            :disabled="isLoading"
          />
          <external-offer-suggesting-list
            @diff="addDiffKey"
            class="col-7" v-if="isEditingExternalOffer"
            :suggestion-map="suggestionFromAts?.localisationInformations"
            :previous-map="previousAtsVersion?.localisationInformations"
            @rowClick="updateLocationQuery"
          />
        </div>

        <div class="q-py-lg">
          <h2 class="text-label q-pt-md q-my-xs">Description du poste *
            <q-btn v-if="allDiffKeys.includes(descriptionKey)"
                   size="sm"
                   outline icon="fa-solid fa-eye"
                   @click="openDescriptionDiff=true" />
          </h2>
          <q-editor
            v-model="description"
            :dense="editorSettings.denseMode"
            :toolbar="editorSettings.basicToolbar"
            :fonts="editorSettings.fonts"
          />
          <q-dialog v-model="openDescriptionDiff">
            <external-offer-suggesting-list
              @diff="addDiffKey"
              class="col-7" v-if="isEditingExternalOffer"
              :suggestion-map="{description: Object.values(suggestionFromAts?.descriptionParts || {}).join('/n')}"
              :previous-map="{description: Object.values(previousAtsVersion?.descriptionParts || {}).join('/n')}"
              @rowClick="updateLocationQuery"
              :non-clickable="true"
              :only-diff="true"
            />
          </q-dialog>
        </div>

        <div class="q-py-lg">
          <h2 class="text-label q-pt-md q-my-xs">Description de l'organisation *
            <q-btn v-if="allDiffKeys.includes(organizationDescriptionKey)"
                   size="sm"
                   outline icon="fa-solid fa-eye"
                   @click="openOrganizationDescriptionDiff=true" />

          </h2>
          <q-editor
            v-model="organizationDescription"
            :dense="editorSettings.denseMode"
            :toolbar="editorSettings.basicToolbar"
            :fonts="editorSettings.fonts"
          />
          <q-dialog v-model="openOrganizationDescriptionDiff">
            <external-offer-suggesting-list
              @diff="addDiffKey"
              class="col-7" v-if="isEditingExternalOffer"
              :suggestion-map="{'description (entreprise)': Object.values(suggestionFromAts?.organizationDescriptionParts || {}).join('/n')}"
              :previous-map="{'description (entreprise)': Object.values(previousAtsVersion?.organizationDescriptionParts || {}).join('/n')}"
              @rowClick="updateLocationQuery"
              :non-clickable="true"
              :only-diff="true"
            />
          </q-dialog>

        </div>

        <div class="row q-py-lg">
          <div :class="isEditingExternalOffer ? 'col-6' : 'col-12'">
            <select-type-contract
              :submitted="submitted"
              v-model="contract"
            />
          </div>
          <div class="col-6 q-pa-lg" v-if="isEditingExternalOffer">
            <external-offer-suggesting-list
              @diff="addDiffKey"
              :suggestion-map="suggestionFromAts?.typeContractInformations"
              :previous-map="previousAtsVersion?.typeContractInformations"
              :non-clickable="true"/>
          </div>
        </div>
        <select-working-time
          :submitted="submitted"
          v-model="contract"
        />
        <div class="row q-py-lg">
          <div :class="isEditingExternalOffer ? 'col-6' : 'col-12'">
            <select-salary-range
              :submitted="submitted"
              v-model="contract"
            />
          </div>
          <div class="col-6 q-py-lg" v-if="isEditingExternalOffer">
            <external-offer-suggesting-list
              @diff="addDiffKey"
              :suggestion-map="suggestionFromAts?.salaryRelatedData"
              :previous-map="previousAtsVersion?.salaryRelatedData"
              :non-clickable="true"
            />
          </div>
        </div>
        <div class="row q-py-lg">
          <div
            :class="isEditingExternalOffer ? 'col-6' : 'col-12'">
            <select-criteria
              :step="SourcingCriteriaStep.STEP1"
              :selectedCriteriaCodes="selectedCriteriaCodes"
              @update:criteriaSelection="updateCriteriaSelection"
            />
          </div>
          <div class="col-6 q-py-lg">
            <external-offer-suggesting-list
              @diff="addDiffKey"
              v-if="isEditingExternalOffer"
              :suggestion-map="suggestionFromAts?.criteriaRelatedData"
              :previous-map="previousAtsVersion?.criteriaRelatedData"
              :non-clickable="true"
            />
          </div>
        </div>
      </q-form>
    </div>
    <div class="row">
      <q-space />
      <q-banner class="user-text text-center"
                v-if="selectedJobOccupation && !selectedJobOccupation.code && isLoading">
        <h2 class="text-body1 q-my-none custom-text-md text-bold">
          Vous avez précisé un nouveau métier.</h2>
        <span>
          la création de votre offre peut prendre une petite
          minute...
        </span>
        <br />
        <span>
            Merci pour votre patience&nbsp;!
        </span>
      </q-banner>
    </div>
    <div class="row">
      <q-space/>
      <q-tooltip v-if="errors.length" anchor="top right">
        <pre>Préciser {{ errors.join(",\n") }}</pre>
      </q-tooltip>
      <q-btn @click="submit" outline class="bg-secondary q-mt-lg"
             :disable="!!errors.length"
             :loading="isLoading">
        Valider
      </q-btn>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useApi } from 'src/config/api';
import SearchOccupation from 'components/SearchOccupation.vue';
import SelectCity from 'components/SelectCity.vue';
import { computed, nextTick, ref } from 'vue';
import SelectTypeContract from 'components/recruitment/SelectTypeContract.vue';
import SelectWorkingTime from 'components/recruitment/SelectWorkingTime.vue';
import SelectSalaryRange from 'components/recruitment/SelectSalaryRange.vue';
import SelectCriteria from 'components/recruitment/SelectCriteria.vue';
import type {
  CreateOrUpdateFullRecruitmentCommand,
  ErhgoOccupationSearch,
  ExtractedAtsData,
  Location,
  SourcingJobAndRecruitment,
  UpdateSourcingJobContractCommand,
} from 'erhgo-api-client';
import { SourcingCriteriaStep } from 'erhgo-api-client';
import ExternalOfferSuggestingList from 'components/recruitment/ExternalOfferSuggestingList.vue';
import { useCustomEditorSettings } from 'src/hooks/useCustomEditorSettings';
import { useRouter } from 'vue-router';

const props = defineProps({
  externalOfferId: {required: false, type: String},
  recruitmentId: { required: false, type: Number },
});
const organizationDescriptionKey = "Description de l'organisation";
const descriptionKey = "Description de l'offre";

const router = useRouter();
const {service: {api, isLoading}} = useApi();
const editorSettings = useCustomEditorSettings();
const selectedJobOccupation = ref<ErhgoOccupationSearch>();
const selectedLocation = ref<Location>();
const contract = ref<Partial<UpdateSourcingJobContractCommand>>();
const selectedCriteriaCodes = ref<Array<string>>([]);
const description = ref<string>('');
const organizationDescription = ref<string>('');
const suggestionFromAts = ref<ExtractedAtsData>();
const previousAtsVersion = ref<ExtractedAtsData>();
const recruitment = ref<SourcingJobAndRecruitment>();
const occupationQuery = ref<string>();
const locationSearchQuery = ref<string>();
const isEditingExternalOffer = computed(() => !!props.externalOfferId && !props.recruitmentId);
const openDescriptionDiff = ref(false);
const openOrganizationDescriptionDiff = ref(false);
const allDiffKeys = ref<string[]>([]);

const submitted = ref<boolean>(false);

function buildDescription(descriptionParts: { [p: string]: string } | undefined) {
  if (!descriptionParts) return '';
  return Object.values(descriptionParts).join('\n');
}

if (isEditingExternalOffer.value) {
  const { data } = (await api.initializeAndExtractDataFromExternalOffer(props.externalOfferId));
  suggestionFromAts.value = data.latestVersion;
  previousAtsVersion.value = data.previousVersion;
  recruitment.value = data.existingRecruitment;
  description.value = buildDescription(suggestionFromAts.value?.descriptionParts);
  organizationDescription.value = buildDescription(suggestionFromAts.value?.organizationDescriptionParts);
} else if (props.recruitmentId) {
  recruitment.value = (await api.getSourcingJobAndRecruitment(props.recruitmentId)).data;
}
if (recruitment.value) {
  const title = recruitment.value?.title ?? '';
  const code = recruitment.value?.occupationId ?? '';
  selectedJobOccupation.value = code ? {title, code} : undefined;
  selectedLocation.value = recruitment.value?.location;
  description.value = recruitment.value?.description ?? '';
  organizationDescription.value = recruitment.value?.organizationDescription ?? '';
  contract.value = {
    typeContractCategory: recruitment.value?.typeContractCategory,
    workingTimeType: recruitment.value?.workingTimeType,
    modularWorkingTime: recruitment.value?.modularWorkingTime ?? false,
    workingWeeklyTime: recruitment.value?.workingWeeklyTime ?? 35,
    baseSalary: recruitment.value?.baseSalary ?? 0,
    maxSalary: recruitment.value?.maxSalary ?? 0,
  };
  selectedCriteriaCodes.value = recruitment.value?.criteriaValues ?? [];
}


function hasDifferences(a?: { [p: string]: string }, b?: {
  [p: string]: string
}) {
  return Object.keys(a || []).some(k => b?.[k] !== a?.[k]) ||
    Object.keys(b || []).some(k => b?.[k] !== a?.[k]);
}

if (hasDifferences(previousAtsVersion.value?.organizationDescriptionParts, suggestionFromAts.value?.organizationDescriptionParts)) {
  allDiffKeys.value = [...allDiffKeys.value, organizationDescriptionKey];
}
if (hasDifferences(previousAtsVersion.value?.descriptionParts, suggestionFromAts.value?.descriptionParts)) {
  allDiffKeys.value = [...allDiffKeys.value, descriptionKey];
}

const isEditingRecruitment = computed(() => {
  return !!recruitment.value;
});

/* eslint-disable */
const errors = computed(() => {
  const errors = [];
  (!!selectedLocation.value || errors.push('la localisation'));
  (!!selectedJobOccupation.value || errors.push('le métier'));
  (!!description.value || errors.push('la description'));
  (!!organizationDescription.value || errors.push("la description de l'entreprise"));
  (!!contract.value?.typeContractCategory || errors.push('le type de contrat'));
  (!!contract.value?.workingTimeType || errors.push("le type d'horaire"));
  (!!contract.value?.workingWeeklyTime || errors.push('la durée de travail'));
  (!!contract.value?.baseSalary || errors.push('le salaire min'));
  (!!contract.value?.maxSalary || errors.push('le salaire max'));
  return errors;
});
/* eslint-enable */

const updateCriteriaSelection = (addedCriteria: string[], removedCriteria: string[]) => {
  selectedCriteriaCodes.value = selectedCriteriaCodes.value.filter(c => !removedCriteria.includes(c));
  selectedCriteriaCodes.value = selectedCriteriaCodes.value.concat(addedCriteria);
};
const updateOccupationQuery = (value: string) => {
  if (occupationQuery.value === value) {
    occupationQuery.value = '';
  }
  void nextTick(() => {
    occupationQuery.value = value;
  });
};

const updateLocationQuery = (value: string) => {
  if (locationSearchQuery.value === value) {
    locationSearchQuery.value = '';
  }
  void nextTick(() => {
    locationSearchQuery.value = value;
  });
};

const submit = async () => {
  submitted.value = true;
  if (!errors.value.length) {
    const command = {
      recruitmentId: isEditingRecruitment.value ? recruitment.value.recruitmentId : undefined,
      jobOccupationId: selectedJobOccupation.value?.code,
      title: selectedJobOccupation.value?.title,
      jobLocation: selectedLocation.value,
      description: description.value,
      organizationDescription: organizationDescription.value,
      typeContractCategory: contract.value.typeContractCategory,
      workingTimeType: contract.value.workingTimeType,
      modularWorkingTime: contract.value.modularWorkingTime,
      workingWeeklyTime: contract.value.workingWeeklyTime,
      baseSalary: contract.value.baseSalary,
      maxSalary: contract.value.maxSalary,
      criteriaValues: selectedCriteriaCodes.value,
      externalOfferId: props.externalOfferId,
    } as CreateOrUpdateFullRecruitmentCommand;

    const recruitmentId = (await api.createOrUpdateRecruitmentByFormSubmission(command)).data;
    const redirect = {
      name: isEditingRecruitment.value ? 'recruitment-detail' : 'recruitment',
      params: {
        recruitmentId,
      },
    };
    await router.push(redirect);
  }
};
const addDiffKey = (key: string) => {
  allDiffKeys.value = [...new Set([...allDiffKeys.value, key])].sort();
};

</script>
